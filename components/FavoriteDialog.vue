<template>
  <div class="favorite-dialog">
    <!-- 添加到资料库弹窗 -->
    <el-dialog
      :visible="dialogVisible"
      width="375px"
      custom-class="favorite-select-dialog"
      :show-close="false"
      @close="handleDialogClose"
    >
      <div class="dialog-header">
        <span>加入资料库</span>
        <span class="add-new" @click="showCreateDialog">+新建</span>
      </div>
      <div class="dialog-content">
        <div
          v-for="item in favoriteList"
          :key="item.id"
          class="favorite-item"
          @click="handleSelect(item.id)"
        >
          <div class="item-info">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-meta">
              <span>{{ item.count }}个内容</span>
              <span>· {{ item.isPublic ? '公开' : '私密' }}</span>
            </div>
          </div>
          <div
            class="checkbox"
            :class="{ 'is-checked': selectedId === item.id }"
          ></div>
        </div>
      </div>
      <div class="dialog-footer">
        <el-button class="cancel-btn" @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          class="confirm-btn"
          :class="{ disabled: !selectedId }"
          :disabled="!selectedId"
          @click="handleConfirm"
          >确认</el-button
        >
      </div>
    </el-dialog>

    <!-- 新建资料库弹窗 -->
    <el-dialog
      title="新建资料库"
      :visible.sync="createDialogVisible"
      width="375px"
      custom-class="resource-dialog"
      :show-close="false"
    >
      <div class="dialog-content">
        <div class="form-item">
          <div class="label">名称</div>
          <el-input
            v-model="form.name"
            placeholder="为你的资料库添加一个名称"
            maxlength="12"
          ></el-input>
        </div>
        <div class="form-item switch-item">
          <div class="switch-header">
            <span class="label">设置为公开</span>
            <el-switch v-model="form.isPublic"></el-switch>
          </div>
          <div class="tip">公开后将展示在个人主页，有利会被推荐给其他人</div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handleCancelCreate"
          >取消</el-button
        >
        <el-button
          type="primary"
          class="confirm-btn"
          :class="{ disabled: !form.name }"
          :loading="loading"
          :disabled="!form.name"
          @click="handleCreate"
          >确认</el-button
        >
      </div>
    </el-dialog>
    <!-- 成功吐司 -->
    <transition name="toast">
      <div v-if="showToast" class="success_toast">
        <svg-icon icon-class="fav_suc" class-name="toast_icon" />
        <div class="text">已加入资料库</div>
        <div class="btn" @click="handleGoView">
          <div class="btn_text">去看看</div>
          <svg-icon icon-class="fav_suc" class-name="btn_icon" />
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { Switch, Radio } from 'element-ui'

export default {
  name: 'FavoriteDialog',
  components: {
    'el-switch': Switch,
    'el-radio': Radio,
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible: {
      handler(val) {
        val && (this.dialogVisible = val)
      },
      immediate: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedId: null,
      createDialogVisible: false,
      loading: false,
      form: {
        name: '',
        isPublic: true,
      },
      favoriteList: [
        {
          id: 1,
          name: '手术方案',
          count: 1,
          isPublic: false,
        },
        {
          id: 2,
          name: '私人学习小儿神经资料私人学习小儿神经资料',
          count: 3,
          isPublic: false,
        },
        {
          id: 3,
          name: '医学知识论述',
          count: 3,
          isPublic: true,
        },
        {
          id: 4,
          name: '神经外科',
          count: 3,
          isPublic: true,
        },
      ],
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      immediate: true
    }
  },
  methods: {
    handleDialogClose() {
      this.$emit('update:visible', false)
    },
    handleSelect(id) {
      this.selectedId = id
    },
    handleCancel() {
      this.selectedId = null
      this.dialogVisible = false
      this.$emit('update:visible', false)
    },
    async handleConfirm() {
      if (!this.selectedId) return
      // TODO: 处理添加到资料库逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      this.dialogVisible = false
      this.selectedId = null
      this.showToast = true
      setTimeout(() => {
        this.showToast = false
        this.$emit('update:visible', false)
      }, 2500)
    },
    handleGoView() {
      // TODO: 处理跳转逻辑
      this.showToast = false
    },
    showCreateDialog() {
      this.dialogVisible = false
      this.createDialogVisible = true
    },
    async handleCreate() {
      if (!this.form.name) {
        this.$message.warning('请输入资料库名称')
        return
      }
      this.loading = true
      try {
        // TODO: 处理创建资料库逻辑
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.createDialogVisible = false
        // 更新列表
        this.favoriteList.unshift({
          id: 1,
          name: this.form.name,
          count: 0,
          isPublic: this.form.isPublic
        })
        // 重置表单
        this.form = {
          name: '',
          isPublic: true
        }
        this.dialogVisible = true

      } finally {
        this.loading = false
      }
    },
    handleCancelCreate () {
      this.createDialogVisible = false
      this.dialogVisible = true
      this.form = {
        name: '',
        isPublic: true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.favorite-dialog {
  /deep/ .favorite-select-dialog {
    border-radius: 16px;
    overflow: hidden;

    .el-dialog__header {
      display: none;
    }

    .el-dialog__body {
      padding: 0;
    }

    .dialog-header {
      height: 55px;
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eeeeee;
      font-size: 16px;
      color: #666666;
      font-weight: 500;

      .add-new {
        color: #0581ce;
        cursor: pointer;
        border-radius: 16px;
        border: 1px solid #0581ce;
        padding: 4px 14px;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
      }
    }

    .dialog-content {
      padding: 2px 15px;
      max-height: 400px;
      box-sizing: border-box;
      overflow-y: auto;

      .favorite-item {
        padding: 14px 2px 14px 0;
        cursor: pointer;
        border-bottom: 1px solid #f8f8f8;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8f8f8;
        }

        .checkbox {
          width: 16px;
          height: 16px;
          border: 1px solid #999;
          flex-shrink: 0;
          box-sizing: border-box;
          background-color: #fff;
          border-radius: 50%;

          &.is-checked {
            background: url('~assets/images/user-center/selected.png') no-repeat
              center;
            background-size: 100%;
            border: none;
          }
        }

        .item-info {
          flex: 1;
          margin-right: 12px;
          width: 100%; // 添加这行
          min-width: 0; // 添加这行
          overflow: hidden;

          .item-title {
            width: 100%; // 添加这行
            max-width: 100%;
            font-size: 15px;
            color: #333333;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 21px;
          }

          .item-meta {
            font-size: 12px;
            color: #888;
            line-height: 17px;
          }
        }
      }
    }

    .dialog-footer {
      padding: 12px 0;
      text-align: center;
      border-top: 1px solid #eeeeee;

      .el-button {
        width: 88px;
        height: 32px;
        border-radius: 16px;
        padding: 0;

        & + .el-button {
          margin-left: 14px;
        }
      }

      .cancel-btn {
        background: #f4f4f4;
        border: 1px solid #ccc;
        color: #333333;
      }

      .confirm-btn {
        background: #0581ce;
        border: none;
        color: #ffffff;

        &.disabled {
          background: #f4f4f4;
          border: none;
          color: #999999;
        }
      }
    }
  }

  /deep/ .resource-dialog {
    border-radius: 16px;
    overflow: hidden;

    .el-dialog__header {
      padding: 16px 0;
      border-bottom: 1px solid #eaeaea;
      margin-right: 0;

      .el-dialog__title {
        font-size: 16px;
        color: #666;
        font-weight: 500;
      }
    }

    .el-dialog__body {
      padding: 15px 15px 20px;
    }

    .dialog-content {
      .form-item {
        margin-bottom: 15px;

        .el-input__inner,
        .el-textarea__inner {
          background-color: #fafafa;
          border: none;
          border-radius: 4px;
          resize: none;
          color: #333333;
          font-size: 15px;
          padding: 12px 16px;

          &::placeholder {
            color: #a0a0a0;
            font-size: 14px;
          }
        }

        .label {
          margin-bottom: 8px;
          color: #333333;
          font-size: 14px;
          font-weight: 400;
        }
      }

      .switch-item {
        margin-bottom: 0;
        .el-switch__core {
          background-color: #eee;
          border-color: #c0c0c0;
          width: 32px !important;
          height: 18px !important;
          &::after {
            top: 0px;
          }
        }
        .el-switch.is-checked .el-switch__core {
          background-color: #0581ce;
          border-color: #0581ce;
          width: 32px !important;
          height: 18px !important;
          &::after {
            top: 0px;
          }
        }

        .switch-header {
          margin-bottom: 4px;

          .label {
            margin-bottom: 0;
          }
        }

        .tip {
          margin-top: 4px;
          color: #999999;
          font-size: 12px;
          line-height: 17px;
        }
      }
    }
    .dialog-footer {
      .el-button {
        width: 88px;
        height: 32px;
        border-radius: 16px;
        font-size: 14px;
        padding: 0;
        font-weight: 400;

        & + .el-button {
          margin-left: 14px;
        }
      }

      .cancel-btn {
        background: #f4f4f4;
        border: 1px solid #ccc;
        color: #333333;
      }

      .confirm-btn {
        background: #0581ce;
        border: none;
        color: #ffffff;
        &.disabled {
          background: #f4f4f4;
          border: none;
          color: #999;
        }
      }
    }
  }
  .success_toast {
    width: 392px;
    height: 48px;
    border-radius: 8px;
    background: rgba(51, 51, 51, 0.95);
    padding: 8px 16px;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .toast_icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      flex-shrink: 0;
    }
    .text {
      color: #fff;
      font-family: 'Source Han Sans CN';
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      flex: 1;
      margin-right: 12px;
    }
    .btn {
      display: flex;
      align-items: center;
      cursor: pointer;
      .btn_text {
        color: #fff;
        font-size: 15px;
      }
      .btn_icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }
    }
  }
}
</style>
