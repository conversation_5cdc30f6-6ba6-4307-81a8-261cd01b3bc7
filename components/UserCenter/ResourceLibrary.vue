<template>
  <div class="resource_library">
    <div class="header" @click="toggleExpand">
      <div class="left">
        <img src="@/assets/images/user-center/resource.png" alt="" />
        <span>我的资料库</span>
      </div>
      <img
        :class="['arrow', { 'arrow-down': !isExpanded }]"
        src="@/assets/images/user-center/arrow-right.png"
        alt=""
      />
    </div>
    <div v-show="isExpanded" class="content">
      <!-- 只有自己的视角才显示新建按钮 -->
      <div v-if="isOwner" class="add-new" @click="showDialog">
        <img src="@/assets/images/user-center/add.png" alt="" />
        <span>新建资料库</span>
      </div>

      <div class="list-container" :style="{ maxHeight: maxListHeight + 'px' }" @scroll="handleScroll">
        <div v-loading="listLoading" class="list">
          <div v-if="list.length === 0 && !listLoading" class="empty-state">
            <p>{{ isOwner ? '暂无资料库，快去创建一个吧' : '暂无公开的资料库' }}</p>
          </div>
          <div
            v-else
            class="item"
            v-for="item in list"
            :key="item.id"
            @click="handleItemClick(item)"
          >
            <img :src="item.icon" alt="" />
            <span class="name">{{ item.name }}</span>
            <span class="count">{{ item.count }}</span>
            <!-- 私有标识 -->
            <img
              v-if="!item.isPublic && isOwner"
              class="private-icon"
              src="@/assets/images/user-center/private.png"
              alt="私有"
              title="私有资料库"
            />
          </div>

          <!-- 加载更多提示 -->
          <div v-if="loadingMore" class="loading-more">
            <span>加载中...</span>
          </div>
          <div v-else-if="!hasMore && list.length > 0" class="no-more">
            <span>没有更多了</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加弹窗 -->
    <el-dialog
      title="新建资料库"
      :visible.sync="dialogVisible"
      width="375px"
      custom-class="resource-dialog"
      :show-close="false"
    >
      <div class="dialog-content">
        <div class="form-item">
          <div class="label">名称</div>
          <el-input
            v-model="form.name"
            placeholder="为你的资料库添加一个名称"
            maxlength="12"
          ></el-input>
        </div>
        <div class="form-item">
          <div class="label">简介</div>
          <el-input
            type="textarea"
            v-model="form.description"
            :rows="4"
            placeholder="请输入简介"
            maxlength="30"
          ></el-input>
        </div>
        <div class="form-item upload-item">
          <div class="label">封面</div>
          <div class="upload-cover" @click="handleUpload">
            <template v-if="!form.cover">
              <div class="upload-btn">
                <img src="@/assets/images/user-center/new_add.png" alt="" />
                <span>添加封面</span>
              </div>
            </template>
            <img v-else :src="form.cover" class="preview-img" />
          </div>
        </div>
        <div class="form-item switch-item">
            <div class="switch-header">
            <span class="label">设置为公开</span>
            <el-switch v-model="form.isPublic"></el-switch>
          </div>
          <div class="tip">
            公开后将展示在个人主页，有利会被推荐给其他人
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          class="confirm-btn"
          :class="{ 'disabled': !isFormValid }"
          :loading="loading"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Switch } from 'element-ui'
import { getFavoritesByUserId, saveFavorite } from '../../api/favorite'

export default {
  name: 'ResourceLibrary',
  components: {
    'el-switch': Switch
  },
  props: {
    // 查看的用户ID，如果不传则为当前登录用户
    userId: {
      type: [Number, String],
      default: null
    },
    // 是否为自己的视角
    isOwner: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isExpanded: false,
      dialogVisible: false,
      loading: false, // 创建资料库的loading状态
      listLoading: false, // 列表加载状态
      form: {
        name: '',
        description: '',
        cover: '',
        isPublic: true,  // 默认为公开
      },
      list: [], // 资料库列表
      // 分页信息
      pagination: {
        pageNo: 1,
        pageSize: 20, // 每页20条，但UI显示最多8.5个元素高度
        totalCount: 0,
        totalPage: 0
      },
      // 是否还有更多数据
      hasMore: true,
      // 是否正在加载更多
      loadingMore: false
    }
  },
  computed: {
    isFormValid() {
      const { name, description, cover } = this.form;
      return !!(name || description || cover);
    },
    // 获取查看的用户ID
    targetUserId() {
      return this.userId || this.$store.state.auth.user.id
    },
    // 列表最大高度（8.5个元素）
    maxListHeight() {
      return 56 * 8.5 // 每个元素56px高度
    }
  },
  mounted() {
    this.loadFavoriteList()
  },
  methods: {
    // 获取资料库列表
    async loadFavoriteList(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.listLoading = true
          this.pagination.pageNo = 1
        } else {
          this.loadingMore = true
        }

        const response = await this.$axios.$request(getFavoritesByUserId({
          userId: this.targetUserId,
          loginId: this.$store.state.auth.user.id,
          pageNo: this.pagination.pageNo,
          pageSize: this.pagination.pageSize
        }))

        if (response.code === 1) {
          const newList = response.list || []

          // 处理列表数据
          const processedList = newList.map(item => ({
            id: item.id,
            name: item.name || '未命名资料库',
            icon: this.getResourceIcon(item),
            count: item.contentCount || 0,
            isPublic: item.isPublic,
            cover: item.cover
          }))

          if (isLoadMore) {
            this.list = [...this.list, ...processedList]
          } else {
            this.list = processedList
          }

          // 更新分页信息
          if (response.page) {
            this.pagination = {
              ...this.pagination,
              totalCount: response.page.totalCount || 0,
              totalPage: response.page.totalPage || 0
            }
          }

          // 判断是否还有更多数据
          this.hasMore = this.pagination.pageNo < this.pagination.totalPage
        }
      } catch (error) {
        console.error('获取资料库列表失败:', error)
        this.$message.error('获取资料库列表失败')
      } finally {
        this.listLoading = false
        this.loadingMore = false
      }
    },

    // 加载更多
    async loadMore() {
      if (!this.hasMore || this.loadingMore) return

      this.pagination.pageNo++
      await this.loadFavoriteList(true)
    },

    // 获取资源图标
    getResourceIcon(item) {
      // 如果有封面图，使用封面图
      if (item.cover) {
        return item.cover
      }

      // 根据是否公开选择默认图标
      if (item.isPublic) {
        return require('@/assets/images/user-center/case.png')
      } else {
        return require('@/assets/images/user-center/private.png')
      }
    },

    toggleExpand () {
      this.isExpanded = !this.isExpanded
      // 展开时如果列表为空，加载数据
      if (this.isExpanded && this.list.length === 0) {
        this.loadFavoriteList()
      }
    },
    showDialog() {
      // 重置表单
      this.form = {
        name: '',
        description: '',
        cover: '',
        isPublic: true
      }
      this.dialogVisible = true
    },

    async handleSubmit() {
      if (!this.form.name) {
        this.$message.warning('请输入资料库名称');
        return;
      }

      this.loading = true;
      try {
        const response = await this.$axios.$request(saveFavorite({
          name: this.form.name,
          description: this.form.description,
          cover: this.form.cover,
          isPublic: this.form.isPublic
        }))

        if (response.code === 1) {
          this.$message.success('创建成功')
          this.dialogVisible = false
          // 重新加载列表
          this.loadFavoriteList()
        } else {
          this.$message.error(response.msg || '创建失败')
        }
      } catch (error) {
        console.error('创建资料库失败:', error)
        this.$message.error('创建失败')
      } finally {
        this.loading = false;
      }
    },

    // 点击资料库项目
    handleItemClick(item) {
      // 跳转到资料库详情页
      this.$router.push(`/favorite/detail/${item.id}`)
    },

    // 滚动加载
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target

      // 滚动到底部时加载更多
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        this.loadMore()
      }
    },

    handleUpload () {
      // TODO: 实现上传封面功能
    }
  },

  watch: {
    // 监听用户ID变化，重新加载数据
    targetUserId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.loadFavoriteList()
      }
    }
  }
}
</script>

<style scoped lang="less">
.resource_library {
  background: #ffffff;
  border-radius: 12px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 17px 14px;

    .left {
      display: flex;
      align-items: center;
      gap: 8px;

      img {
        width: 20px;
        height: 20px;
      }

      span {
        color: #333;
        font-family: 'Source Han Sans CN';
        font-size: 20px;
      }
    }

    .arrow {
      width: 16px;
      height: 16px;
      transition: transform 0.3s;

      &.arrow-down {
        transform: rotate(180deg);
      }
    }
  }

  .content {
    border-top: 1px solid #eee;

    .add-new {
      height: 56px;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 14px;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }

      span {
        color: #333;
        font-family: 'Source Han Sans CN';
        font-size: 16px;
      }

      &:hover {
        background: #f7f7f7;
        border-radius: 4px;
      }
    }

    .list-container {
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .list {
      .empty-state {
        text-align: center;
        padding: 40px 14px;
        color: #999;
        font-size: 14px;
      }

      .item {
        border-top: 1px solid #eee;
        height: 56px;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 14px;
        cursor: pointer;
        position: relative;

        & + .item {
          margin-top: 4px;
        }

        img {
          width: 20px;
          height: 20px;
          border-radius: 2px;
          object-fit: cover;
        }

        .name {
          color: #333;
          font-family: 'Source Han Sans CN';
          font-size: 16px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .count {
          font-size: 12px;
          color: #666;
          margin-right: 8px;
        }

        .private-icon {
          width: 16px;
          height: 16px;
          opacity: 0.6;
        }

        &:hover {
          background: #f7f7f7;

          .name {
            color: #0581ce;
          }
        }
      }

      .loading-more,
      .no-more {
        text-align: center;
        padding: 12px;
        font-size: 12px;
        color: #999;
      }
    }
  }

  /deep/ .resource-dialog {
    border-radius: 16px;
    overflow: hidden;
    .el-dialog__header {
      padding: 17px 0 14px;
      border-bottom: 1px solid #eaeaea;
      margin-right: 0;
      display: flex;
      justify-content: center;

      .el-dialog__title {
        font-size: 16px;
        color: #666;
        font-weight: normal;
        width: 100%;
        text-align: center;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .dialog-content {
      .form-item {
        margin-bottom: 15px;
        .el-input__inner,
        .el-textarea__inner {
            background-color: #fafafa;
            border: none;
            resize: none;
            color: #333333;
            font-size: 15px;
            &::placeholder {
                color: #A0A0A0;
                font-size: 14px;
                }

        }
        .el-textarea__inner {
            font-size: 14px;
        }

        .label {
          margin-bottom: 8px;
          color: #333;
          font-size: 14px;

        }

        .upload-cover {
          width: 220px;
          height: 124px;  // 修改为 16:9 比例
          background: #fafafa;
          border: none;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          overflow: hidden;

            // border-color: #0581ce;
            .upload-btn span {
              color: #708aa2;
              font-size: 14px;
            }

          .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }

      .switch-item {
        .el-switch__core {
          background-color: #eee;
          border-color: #C0C0C0;
          width: 32px!important;
          height: 18px!important;
          &::after {
            top: 0px;
          }
        }
        .el-switch.is-checked .el-switch__core {
          background-color: #0581CE;
          border-color: #0581CE;
          width: 32px!important;
          height: 18px!important;
          &::after {
            top: 0px;
          }
        }
        .switch-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .tip {
        //   margin-top: 6px;
          color: #999;
          font-size: 12px;
          line-height: 1.5;
        }
      }
    }

    .el-dialog__footer {
      padding: 12px 0;
      text-align: center;
      border-top: 1px solid #eaeaea;

      .dialog-footer {
        .el-button {
          width: 88px;
          height: 32px;
          border-radius: 16px;
          font-size: 14px;
          padding: 0;
          font-weight: 400;

          & + .el-button {
            margin-left: 14px;
          }
        }

        .cancel-btn {
          background: #F4F4F4;
          border: 1px solid #CCC;
          color: #333333;
        }

        .confirm-btn {
          background: #0581CE;
          border: none;
          color: #FFFFFF;
          &.disabled {
            background: #F4F4F4;
            border: none;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
