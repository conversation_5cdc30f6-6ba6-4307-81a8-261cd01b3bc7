<template>
  <div :style='!isFlag ? "background: rgba(0, 0, 0, 0.6);" : "background: rgba(0, 0, 0, 0.2);"' class='followButton'
       @click.stop='followHandler'>
    <p v-if='!isFlag'>
      <svg-icon className='tab_icon' iconClass='subscribe'></svg-icon>
      关注
    </p>
    <p v-else>
      已关注
    </p>
  </div>
</template>

<script>
export default {
  name: 'FollowButton',
  props: {
    id: {
      type: Number,
      default: true
    },
    isFollow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFlag: this.isFollow
    }
  },
  watch: {
    isFollow(newData) {
      if (newData) {
        this.isFlag = newData
      }
    }
  },
  methods: {
    followHandler() {
      this.isFlag = !this.isFlag
      this.$emit('followHandler', this.id)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
