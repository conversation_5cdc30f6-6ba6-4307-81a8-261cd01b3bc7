.post-item-container {
  display: grid;
  grid-template-columns: 220px calc(100% - 220px - 10px);
  grid-gap: 0 10px;
  padding: 10px;
  border-radius: 6px;
  overflow: hidden;
  transition: all .3s;

  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }

  .left-image {
    width: 100%;
    height: 124px;
    border-radius: 6px;
    overflow: hidden;
  }

  .right-content {
    display: flex;
    flex-flow: column;
    justify-content: space-between;

    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;
      color: #202020;
      margin-bottom: 14px;
    }

    .company {
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #708AA2;

    }

    .bottom-tips {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      margin-bottom: 6px;

      .sub-content {
        max-width: 65%;
      }

      .sub-container {
        li {
          display: inline-block;
          flex-shrink: 0;
          background: rgba(5, 129, 206, 0.1);
          border-radius: 6px;
          padding: 3px 6px;
          font-size: 12px;
          line-height: 17px;
          color: #0581CE;
          margin-right: 10px;
        }
      }

      .time-and-city {
        font-size: 12px;
        line-height: 12px;
        color: #708AA2;
        margin-right: 10px;

        .time {
          margin-right: 10px;
        }
      }
    }
  }
}
