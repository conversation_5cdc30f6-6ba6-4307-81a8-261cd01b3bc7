<template>
  <li class='cursor'>
    <nuxt-link class='post-item-container' :to='{path:`/info/detail?id=${infoId}`}'
               target='_blank'>
      <div class='left-image'>
        <img v-if='infoImg' :src='$tool.compressImg(infoImg,220,124)' alt='招聘与培训' class='img_cover'>
        <img v-else alt='文章默认图' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='right-content'>
        <div>
          <p class='title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1"  icon-class="auth-new" style="width: 20px;height: 20px;vertical-align:revert-layer"/>
            <span>{{ infoTitle }}</span>
          </p>
          <p class='company'>
            {{ hospitalName }}
          </p>
        </div>
        <div class='bottom-tips'>
          <div class='text-limit-1 sub-content'>
            <ul class='sub-container'>
              <li v-for='item in trainRecruitLabels' :key='item.id'>{{ item.labelName }}</li>
            </ul>
          </div>
          <p class='time-and-city'>
            <span class='time'>{{ publishDate }}</span>
            <span class='city'>{{ province }}</span>
          </p>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
export default {
  name: 'RecruitmentTrainingPostsItem',
  props: {
    infoId: {
      type: Number
    },
    infoImg: {
      type: String,
      default: ''
    },
    infoTitle: {
      type: String,
      default: ''
    },
    province: {
      type: String,
      default: ''
    },
    hospitalName: {
      type: String,
      default: ''
    },
    trainRecruitLabels: {
      type: Array,
      default: () => []
    },
    publishDate: {},
    bmsAuth:{}
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
