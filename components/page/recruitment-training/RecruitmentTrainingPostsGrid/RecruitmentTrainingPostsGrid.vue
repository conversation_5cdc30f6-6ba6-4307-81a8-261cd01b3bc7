<template>
  <ol class='post-grid-container'>
    <RecruitmentTrainingPostsItem
      v-for='item in recruitmentTrainingList'
      :key='item.infoId'
      :info-id='item.infoId'
      :info-img='item.infoImg'
      :info-title='item.infoTitle'
      :hospital-name='item.department.hospitalGroup.name'
      :province='item.department.province'
      :train-recruit-labels='item.trainRecruitLabels'
      :publish-date='timeStamp.timestampFormat((item.publishDate / 1000))'
      :bmsAuth="item.bmsAuth"
    />
  </ol>
</template>

<script>
import RecruitmentTrainingPostsItem
  from '@/components/page/recruitment-training/RecruitmentTrainingPostsGrid/RecruitmentTrainingPostsItem/RecruitmentTrainingPostsItem'

export default {
  name: 'RecruitmentTrainingPostsGrid',
  components: {
    RecruitmentTrainingPostsItem
  },
  props: {
    recruitmentTrainingList: {
      type: Array,
      default: () => []
    }
  },
  mounted() {

  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
