<template>
  <transition name='el-zoom-in-top' @click.stop>
    <section v-show='showHidden' class='filter-popup-container' @click.stop>
      <div class='filter-title-container'>
        <p class='filter-title'>
          {{ filterTitle }} {{ Object.keys(seletedMap).length > 0 ? `（${Object.keys(seletedMap).length}）` : '' }}
        </p>
      </div>

      <section class='option-container'>
        <ol class='option-type-ol'>
          <li v-for='(item,index) in dataSet' :key='index' class='option-type-li'>
            <p class='option-title flex_start'>
              <span :style='item.name === "热门选项" ? {color:"var(--theme-color)"} : {}'>{{ item.name }}</span>
              <span
                v-if='isShowSelectedQuantity && selectNum[index]'
                style='color:#888888;font-weight: 400'>
                （{{ selectNum[index] }}）
              </span>
            </p>
            <div
              :class='openFilterMap[item.name] ? "flex_align_end" : "flex_align_center"'
              class='flex_between flex_align_center option-big-container'>
              <div
                ref='option-refs'
                :class='!isShrink || openFilterMap[item.name] ? "openFilter" : "closeFilter"'
                class='option-container-children-content'>
                <ul
                  ref='options-content-refs'
                  class='option-type-children-ul'>
                  <li
                    v-if='isShowWhole'
                    flag='false'
                    class='option-type-children-li whole-li'
                    :ref='`whole-${index}`'
                    @click='checkWholeHandler(item.list,index)'>
                    全部
                  </li>
                  <li
                    v-for='(itemChildren,childrenIndex) in item.list' :key='childrenIndex'
                    :class='(seletedMap[itemChildren.labelName] || (Object.keys(seletedMap).length===0 && itemChildren.labelName === "全国")) ? "is_active" : ""'
                    class='option-type-children-li'
                    @click='changeSeletedHandler(itemChildren.labelName,index)'>
                    {{ itemChildren.labelName }}
                  </li>
                </ul>
              </div>
              <div
                :flag='openFilterMap[item.name] ? "true" : "false"'
                ref='openHide'
                style='display: block'
                class='flex_center'>
                <svg-icon
                  v-if='isShrink'
                  :class-name='openFilterMap[item.name] ? "close-iconse cursor" : "open-icons cursor"'
                  icon-class='open-zhankai'
                  @click='openFilterMap[item.name] ? $delete(openFilterMap,item.name) : $set(openFilterMap,item.name,1)'/>
              </div>
            </div>
          </li>
        </ol>
      </section>

      <transition name='el-fade-in-linear'>
        <section
          v-show='Object.keys(seletedMap).length>0'
          class='selected-container'>
          <p class='tips'>
            已选
          </p>
          <div class='seleced-option-container'>
            <ul ref='selectedUl' class='selected-ul-container'>
              <transition-group ref='selected-ul-ul' class='selected-ul' name='el-zoom-in-center' tag='ul'>
                <li
                  v-for='(item,index) in seletedMap'
                  :id='index' ref='selected-li' :key='index' class='selected-li cursor'
                  @click='deleteSeletedHandler(item,index,"close")'>
                  <span>{{ index }}</span>
                  <svg-icon class-name='deleted-icons' icon-class='deleted'/>
                </li>
              </transition-group>
            </ul>

          </div>
          <div class='switch-button'>
            <svg-icon class-name='switch-icons' icon-class='sanjiao-left' @click='slideSeletedHandler("left")'/>
            <svg-icon class-name='switch-icons' icon-class='sanjiao-right' @click='slideSeletedHandler("right")'/>
          </div>
        </section>
      </transition>

      <section class='function-bar'>
        <div class='themeButton button clearbutton' @click='clearSeletedHandler'>清除</div>
        <div class='themeButton button okbutton' @click='submitFilterHandler'>确认</div>
      </section>
    </section>
  </transition>

</template>

<script>
export default {
  name: 'FilterPopup',
  props: {
    resultMap: {
      type: Object,
      default: () => {
      }
    },
    // 默认选中全国
    defaultCheckLabel: {
      type: Boolean,
      default: false
    },
    defaultCheckArr: {
      type: Array,
      default: () => []
    },
    // 是否收缩
    isShrink: {
      type: Boolean,
      default: true
    },
    // 是否显示全部
    isShowWhole: {
      type: Boolean,
      default: false
    },
    // 是否显示已经选中
    isShowSelectedQuantity: {
      type: Boolean,
      default: false
    },
    dataSet: {
      type: Array,
      default: () => []
    },
    filterTitle: {
      type: String,
      default: '搜素标题'
    },
    showHidden: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      monitorSilde: null,
      slideW: 0,
      selectNum: {},
      seletedMap: {},
      openFilterMap: {},
      slideNum: 0 // 滑动的页数
    }
  },
  watch: {
    defaultCheckArr(newValue) {
      if (newValue.length > 0) {
        newValue.forEach(item => {
          this.changeSeletedHandler(item.labelName, "0")
        })
      }
    },
    seletedMap(newValue) {
      if (newValue) {
        /**
         * <AUTHOR> (Rick)
         * @date 2022-12-13 17:53
         * @Description: 靠右
         */
        const slideListItme = this.$refs['selected-li']
        const selectedUlW = this.$refs.selectedUl?.offsetWidth
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          if (slideListItme && selectedUlW && slideListItme.length > 0) {
            let slideListWidth = 0
            for (let i = 0; i < slideListItme.length; i++) {
              slideListWidth += Number(slideListItme[i].offsetWidth + 8)
            }
            const pageNum = (slideListWidth / selectedUlW).toFixed(1)
            const slideLastW = slideListItme[slideListItme.length - 1].offsetWidth
            const slideWNum = ((this.slideW + selectedUlW + slideLastW) / selectedUlW).toFixed(1)

            if (slideListWidth > selectedUlW) {
              // this.slideW = slideListWidth - selectedUlW + slideLastW
              if (this.monitorSilde) {
                this.slideW = this.slideW - this.monitorSilde
                this.monitorSilde = null
              } else {
                this.slideW = slideListWidth - selectedUlW
              }
              this.$refs.selectedUl.style.transform = `translateX(-${this.slideW}px)`

            } else {
              this.$refs.selectedUl.style.transform = `translateX(0px)`
            }
          }
        }, 0)


        const map = newValue
        this.selectNum = {}
        Object.keys(map).forEach((key) => {
          if (!this.selectNum[map[key]]) {
            this.selectNum[map[key]] = 1
          } else {
            this.selectNum[map[key]] = this.selectNum[map[key]] + 1
          }
        })


        if (this.isShowWhole) {
          // 如果全部选中 那么那一行的全部 高亮
          const ulDom = this.$refs['options-content-refs']
          for (let i = 0; i < ulDom.length; i++) {
            ulDom[i].firstChild.innerHTML ? ulDom[i].firstChild.classList.remove('is_active') : null
            ulDom[i].firstChild.innerHTML ? ulDom[i].firstChild.setAttribute('flag', 'false') : null
          }

          Object.keys(this.selectNum).forEach((item) => {
            if (ulDom[item].children.length - 1 === this.selectNum[item]) {
              const wholDOm = this.$refs[`whole-${item}`][0]
              wholDOm.classList.add('is_active')
              wholDOm.setAttribute('flag', 'true')
            }
          })
        }
      }
    },
    showHidden(newValue) {
      if (newValue) {
        if (this.dataSet.length > 0 && this.isShrink) {
          setTimeout(() => {
            this.$nextTick(() => {
              const container = this.$refs['option-refs'] ? this.$refs['option-refs'] : []
              const content = this.$refs['options-content-refs']
              const openHide = this.$refs.openHide
              for (let i = 0; i < container.length; i++) {
                if (
                  (content[i].offsetHeight > container[i].offsetHeight)
                  ||
                  openHide[i].getAttribute('flag') === 'true'
                ) {
                  openHide[i].style.display = 'block'
                } else {
                  openHide[i].style.display = 'none'
                }
              }
            })
          }, 0)
        }
        document.body.addEventListener('click', this.addHandler)
      } else {
        if (Object.keys(this.resultMap).length === 0) {
          this.monitorSilde = null
          this.slideW = 0
          this.selectNum = {}
          this.seletedMap = {}
          this.openFilterMap = {}
          this.slideNum = 0
        }

        document.body.removeEventListener('click', this.addHandler)
      }
    }
  },
  mounted() {
  },
  methods: {
    /**
     * 点击全部
     */
    checkWholeHandler(data, id) {
      const wholDOm = this.$refs[`whole-${id}`][0]
      if (wholDOm.getAttribute('flag') === 'false') {
        wholDOm.setAttribute('flag', 'true')
        wholDOm.classList.add('is_active')
        data.forEach((item) => {
          this.$set(this.seletedMap, item.labelName, id + '')
        })
      } else {
        wholDOm.setAttribute('flag', 'false')
        wholDOm.classList.remove('is_active')
        data.forEach((item) => {
          this.$delete(this.seletedMap, item.labelName)
        })
      }
    },
    /**
     * 清除选中
     */
    clearSeletedHandler() {
      this.slideNum = 0
      const selectedUlW = this.$refs.selectedUl.offsetWidth
      this.$refs.selectedUl.style.transform = `translateX(-${selectedUlW * this.slideNum}px)`
      this.seletedMap = {}
    },
    /**
     * 监听点击时间
     */
    addHandler(e) {
      if (e.target.className !== 'filter-popup-container') {
        if (Object.keys(this.resultMap).length === 0 || (Object.keys(this.resultMap).length === Object.keys(this.seletedMap).length)) {
          this.$emit('showHideFlagHandler', false)
        } else {
          this.submitFilterHandler()
        }
        console.log('筛选框关闭,监听移出')
      }
    },
    /**
     * 选中选项
     */
    changeSeletedHandler(id, data) {
      if (id === '全国') {
        this.seletedMap = {}
      } else {
        this.seletedMap[id] ? this.$delete(this.seletedMap, id) : this.$set(this.seletedMap, id, data + '')
      }
    },
    /**
     * 删除选项
     */
    deleteSeletedHandler(data, id, type) {
      const delDom = document.getElementById(id)
      if (type === 'close') {
        this.monitorSilde = delDom.offsetWidth
      }
      this.$delete(this.seletedMap, id)
    },
    /**
     * 左右滑动已选中内容
     */
    slideSeletedHandler(type) {
      const slideListItme = this.$refs['selected-li']
      let slideListWidth = 0
      for (let i = 0; i < slideListItme.length; i++) {
        slideListWidth += Number(slideListItme[i].offsetWidth)
      }

      const selectedUlW = this.$refs.selectedUl.offsetWidth
      switch (type) {
        case 'left': {
          // this.slideNum > 0 ? this.slideNum -= 1 : this.slideNum = 0
          // this.$refs.selectedUl.style.transform = `translateX(-${selectedUlW * this.slideNum}px)`

          this.slideW = (this.slideW - selectedUlW) > 0 ? (this.slideW - selectedUlW) : 0

          this.$refs.selectedUl.style.transform = `translateX(-${this.slideW}px)`
          break
        }
        case 'right': {
          // if ((selectedUlW * (this.slideNum + 1)) > slideListWidth) {
          //   break
          // } else {
          //   this.slideNum += 1
          // }
          //
          //


          this.slideW = (this.slideW + selectedUlW) < slideListWidth ? (this.slideW + selectedUlW) : this.slideW

          this.$refs.selectedUl.style.transform = `translateX(-${this.slideW}px)`
          break
        }
      }
    },
    /**
     * 确认筛选按钮
     */
    submitFilterHandler() {
      this.$emit('submitFilterHandler', this.seletedMap)
      this.$emit('showHideFlagHandler', false)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
