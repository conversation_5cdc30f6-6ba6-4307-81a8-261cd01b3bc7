.filter-popup-container {
  user-select: none;
  width: 1200px;
  padding: 0 20px;
  box-sizing: border-box;
  overflow: hidden;
  background: #FFFFFF;
  position: absolute;
  left: 0;
  top: 66px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  transition: all .3s;
  z-index: 11;


  .mixin_desktop({
    width: 950px;
  });

  .filter-title-container {
    padding: 20px 0 16px;
    border-bottom: 1px solid #D9E1E9;
    margin-bottom: 20px;

    .filter-title {
      font-weight: 400;
      font-size: 18px;
      line-height: 24px;
      color: #666666;
    }
  }

  .option-container {
    max-height: 18vh;
    overflow: auto;
    padding-bottom: 50px;

    &::-webkit-scrollbar {
      width: 2px;
    }

    .option-type-ol {
      display: grid;
      grid-gap: 20px 0;

      .option-type-li {
        .option-title {
          font-weight: 700;
          font-size: 16px;
          line-height: 21px;
          color: #333333;
          margin-bottom: 16px;
        }

        .option-big-container {
          display: grid;
          grid-gap: 0 20px;
          grid-template-columns: calc(100% - 45px) 25px;
        }

        .option-container-children-content {

        }

        .open-icons {
          width: 20px;
          height: 20px;
          color: #99999999;

          &:hover {
            color: rgba(5, 129, 206, 0.6) !important;
          }
        }

        .close-iconse {
          width: 20px;
          height: 20px;
          color: rgba(5, 129, 206, 0.6) !important;
          transform: rotateZ(180deg);

          &:hover {
            color: rgba(5, 129, 206, 0.6) !important;
          }
        }

        .option-type-children-ul {
          max-width: 100%;
          display: flex;
          grid-gap: 14px 14px;
          justify-content: flex-start;
          flex-wrap: wrap;

          .option-type-children-li {
            min-width: 64px;
            text-align: center;
            background: #F2F2F2;
            border-radius: 6px;
            padding: 0 12px;
            height: 28px;
            line-height: 28px;
            font-size: 15px;
            color: #666666;

            &:hover {
              cursor: pointer;
              //background: rgba(5, 129, 206, 0.15);
              //color: var(--theme-color);
            }
          }
        }

        .openFilter {
          height: auto;
        }

        .closeFilter {
          max-height: 28px;

          overflow: hidden;
        }

        .is_active {
          background: rgba(5, 129, 206, 0.15) !important;
          color: var(--theme-color) !important;
        }
      }
    }
  }

  .selected-container {
    margin-top: 50px;
    background: #F9F9F9;
    height: 46px;
    display: grid;
    align-items: center;
    margin: 0 -20px;
    grid-template-columns: 5.9% calc(100% - 5.9% - 5.9%) 5.9%;

    .tips {
      font-weight: 400;
      font-size: 16px;
      line-height: 21px;
      color: #666666;
      text-align: center;
    }

    .seleced-option-container {
      max-width: 100%;
      overflow: hidden;
    }

    .selected-ul-container {
      transition: all .3s;
    }

    .selected-ul {
      transition: all 1s;
      display: flex;
      justify-content: flex-start;
      grid-gap: 0 8px;


      .selected-li {
        flex-shrink: 0;
        height: 26px;
        line-height: 26px;
        border: 1px solid rgba(5, 129, 206, 0.25);
        border-radius: 6px;
        color: #0581CE;
        font-weight: 400;
        font-size: 15px;
        padding: 0 12px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        grid-gap: 0 6px;
        background: #FFFFFF;

        &:hover .deleted-icons {
          color: var(--theme-color);
        }

        .deleted-icons {
          width: 16px;
          height: 16px;
          color: #B1B1B1;
        }
      }
    }

    .switch-button {
      display: flex;
      justify-content: center;
      grid-gap: 0 7px;

      .switch-icons {
        color: #C0C0C0;
        cursor: pointer;

        &:hover {
          color: #0581CE
        }
      }
    }
  }

  .function-bar {
    padding: 20px 0 30px;
    display: flex;
    justify-content: center;
    grid-gap: 0 60px;

    .button {
      width: 220px;
      height: 48px;
      text-align: center;
      line-height: 48px;
    }

    .clearbutton {
      color: #0581CE;
      background: #E8F1F6;

      &:hover {
        color: #FFFFFF;
      }

      &:active {
        color: #0581CE;
        background: #E8F1F6 !important;
      }
    }

    .okbutton {
      color: #FFFFFF;
    }
  }

}
