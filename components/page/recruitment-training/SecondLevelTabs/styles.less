.secondLevel-tabs-container {
  user-select: none;
  position: relative;
}

.second-tabs-content {
  display: grid;
  justify-content: space-between;
  grid-auto-flow: column;
  align-items: center;
  padding: 0 0 11px 20px;
  border-bottom: 1px solid #D9E1E9;
  margin-bottom: 20px;

  .type-ul {
    display: grid;
    grid-auto-flow: column;
    justify-content: flex-start;
    grid-gap: 0 30px;

    .type-li {
      font-weight: 400;
      font-size: 16px;
      line-height: 18px;
      color: #666666;

      &:hover {
        color: #202020;
      }
    }

    .is_active {
      font-weight: 700;
      color: #202020;
    }
  }

  .filter-container {
    .filter-ol {
      display: grid;
      grid-auto-flow: column;
      justify-content: flex-start;
      grid-gap: 0 15px;

      .filter-li {
        max-width: 160px;
        background: #FBFBFB;
        border-radius: 6px;
        height: 35px;
        line-height: 35px;
        padding: 0 16px;
        font-size: 14px;
        color: #666666;
        cursor: pointer;
        position: relative;

        &:hover {
          color: var(--theme-color);
          outline: 1px solid rgba(5, 129, 206, 0.15);
        }

        .icons {
          width: 6px;
          height: 6px;
          position: absolute;
          right: 6px;
          bottom: 6px;
        }
      }

      .is_active {
        color: var(--theme-color);
        outline: 1px solid rgba(5, 129, 206, 0.15);
      }
    }
  }
}
