<template>
  <section class='secondLevel-tabs-container'>
    <div class='second-tabs-content'>
      <ul class='type-ul'>
        <li
          v-for='item in typeList'
          :key='item.id'
          class='type-li cursor '
          :class='activeOnlineStatus === item.code ? "is_active" : ""'
          @click='changeOnlineStatusHandler(item.code)'
        >
          {{ item.typeName }}
        </li>
      </ul>
      <div class='filter-container'>
        <ol class='filter-ol'>
          <li
            :class='showCityFilterFlag || Object.keys(selectCityMap).length>0 ? "is_active" : ""'
            class='filter-li'
            @click.stop='showHiddenHandler("showCityFilterFlag")'>
            <span class='text-limit-1'>
              {{ filterCityMapName }}
            </span>
            <svg-icon class-name='icons' icon-class='zhankai' />
          </li>
          <li
            :class='showKeywordFilterFlag || Object.keys(selectKeywordMap).length>0 ? "is_active" : ""'
            class='filter-li'
            @click.stop='showHiddenHandler("showKeywordFilterFlag")'>
            <span class='text-limit-1'>
              {{ filterKeywordMapName }}
            </span>
            <svg-icon class-name='icons' icon-class='zhankai' />
          </li>
        </ol>
      </div>
    </div>
    <FilterPopup
      :result-map='selectCityMap'
      :is-shrink='false'
      filter-title='请选择省份或直辖市'
      :show-hidden='showCityFilterFlag'
      :data-set='cityData'
      :default-check-label='true'
      @showHideFlagHandler='showHideCityFlagHandler'
      @submitFilterHandler='submitCityFilterHandler'
    />
    <FilterPopup
      :result-map='selectKeywordMap'
      :is-show-whole='true'
      :is-show-selected-quantity='true'
      filter-title='请选择关键词'
      :show-hidden='showKeywordFilterFlag'
      :data-set='departmentList'
      @showHideFlagHandler='showHideKeywordFlagHandler'
      @submitFilterHandler='submitKeywordFilterHandler' />
  </section>
</template>

<script>
import { getByParentLabelIsNullAndIsUseTrue, getDepartmentHotCity } from '../../../../api/recruitment-training'
import FilterPopup from '@/components/page/recruitment-training/FilterPopup/FilterPopup'

export default {
  name: 'SecondLevelTabs',
  components: {
    FilterPopup
  },
  data() {
    return {
      typeList: [
        { id: 0, typeName: '全部', code: 'A' },
        { id: 1, typeName: '线上', code: 'O' },
        { id: 2, typeName: '线下', code: 'D' }
      ],
      showCityFilterFlag: false, // 展开城市筛选
      showKeywordFilterFlag: false, // 展开关键词筛选
      selectCityMap: {},
      selectKeywordMap: {},
      activeOnlineStatus: 'A',
      cityData: [],
      departmentList: []
    }
  },
  mounted() {
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 11:28
     * @Description: 获取默认数据
     */
    this.getDefaultData()
  },
  computed: {
    filterCityMapName() {
      if (Object.keys(this.selectCityMap).length > 0) {
        let name = ''
        Object.keys(this.selectCityMap).forEach((item, index) => {
          name += item + '、'
        })

        return name.substring(0, name.length - 1)

      } else {
        return '全国'
      }
    },
    filterKeywordMapName() {
      if (Object.keys(this.selectKeywordMap).length > 0) {
        let name = ''
        Object.keys(this.selectKeywordMap).forEach(item => {
          name += item + '、'
        })

        return name.substring(0, name.length - 1)

      } else {
        return '关键词'
      }
    }
  },
  methods: {
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 11:21
     * @Description: 获取默认数据
     */
    async getDefaultData() {
      /**
       * <AUTHOR> (Rick)
       * @date 2022-12-08 11:22
       * @Description:
       *              1.cityData 获取城市
       *              2.labelData 获取标签
       */
      const [cityData, labelData] = await Promise.all([
        this.$axios.$request(getDepartmentHotCity()),
        this.$axios.$request(getByParentLabelIsNullAndIsUseTrue())
      ])
      if (cityData && cityData.code === 1) {
        this.cityData = [
          {
            name: '热门选项', list: cityData.result.departmentHotCityList.map((item) => {
              return { labelName: item.cityName }
            })
          },
          {
            name: '其他省份', list: cityData.result.departmentList.map((item) => {
              return { labelName: item.city }
            })
          }
        ]
      }

      if (labelData && labelData.code === 1) {
        this.departmentList = labelData.list.map(item => {
          return {
            name: item.labelName,
            list: item.children.map(itemChildren => {
              return {
                labelName: itemChildren.labelName
              }
            })
          }
        })
      }
    },

    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 13:42
     * @Description: 切换线上线下
     */
    changeOnlineStatusHandler(code) {
      this.activeOnlineStatus = code
      this.$emit('changeOnlineStatusHandler', code)
    },
    /**
     * 关键词展开收回
     */
    showHideKeywordFlagHandler(flag) {
      this.showKeywordFilterFlag = flag
    },
    /**
     * 城市展开收回
     */
    showHideCityFlagHandler(flag) {
      this.showCityFilterFlag = flag
    },
    /**
     * 筛选框展开收回
     */
    showHiddenHandler(name) {
      switch (name) {
        case 'showCityFilterFlag': {
          this.showCityFilterFlag = !this.showCityFilterFlag
          this.showKeywordFilterFlag = false
          break
        }
        case 'showKeywordFilterFlag': {
          this.showKeywordFilterFlag = !this.showKeywordFilterFlag
          this.showCityFilterFlag = false
          break
        }
      }
    },
    /**
     * 筛选城市函数
     */
    submitCityFilterHandler(map) {
      this.selectCityMap = JSON.parse(JSON.stringify(map))
      let name = ''
      Object.keys(this.selectCityMap).forEach(item => {
        name += item + ','
      })
      name = name.substring(0, name.length - 1)
      this.$emit('filterCitiesHandler', name)
    },
    /**
     * 筛选关键词
     */
    submitKeywordFilterHandler(map) {
      this.selectKeywordMap = JSON.parse(JSON.stringify(map))
      let name = ''
      Object.keys(this.selectKeywordMap).forEach(item => {
        name += item + ','
      })
      name = name.substring(0, name.length - 1)
      this.$emit('filterKeywordsHandler', name)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
