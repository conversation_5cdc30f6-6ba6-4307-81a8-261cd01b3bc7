<template>
  <section class='tabs-container'>
    <ol class='tabse_ol'>
      <li v-for='item in tabsList' :id='item.id' :key='item.id' class='tabs_li cursor'
          @click='changeTabHandler(item.id)'>
        <span :id='activeTab === item.id ? "is_active" : ""'>{{ item.tabName }}</span>
      </li>
      <div id='line' />
    </ol>
  </section>
</template>

<script>
export default {
  name: 'RecruitmentTrainingTabs',
  data() {
    return {
      tabsList: [
        { id: 'A', tabName: '近期发布', code: '' },
        { id: 'T', tabName: '培训班', code: '' },
        { id: 'R', tabName: '招聘', code: '' }
      ],
      activeTab: 'A'
    }
  },
  mounted() {
    this.lineHandler('is_active')
  },
  methods: {
    /**
     * 底部选中跳移动位置
     */
    lineHandler(id) {
      const idDom = document.getElementById(id)
      const lineDom = document.getElementById('line')
      const idDomLeft = idDom ? idDom.offsetLeft : 0
      lineDom.style.opacity = 1
      lineDom.style.transform = `translateX(${(idDom.offsetWidth / 2) - (lineDom.offsetWidth / 2) + idDomLeft}px)`
    },
    /**
     * 切换tab
     */
    changeTabHandler(id) {
      this.activeTab = id
      this.lineHandler(id)
      this.$emit('changeTabHandler', id)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
