.sub-majorTab-box {
  width: 100%;
  border-radius: var(--theme-ridius);
  background-color: var(--theme-bg-color);
  padding: 0 22px;
  box-sizing: border-box;
  min-height: 54px;
  display: flex;
  align-items: center;

  .sub-list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .sub-item {
      font-size: 16px;
      color: var(--font-color-666);
      margin-right: 28px;
      line-height: 35px;
      transition: all .3s;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: var(--theme-color);
      }
    }

    .is_active {
      font-weight: bold;
      color: var(--theme-color);
    }
  }
}
