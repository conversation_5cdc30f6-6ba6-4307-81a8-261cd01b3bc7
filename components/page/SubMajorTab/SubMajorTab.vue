<!--
/**
 *  @author:<PERSON>  @date:2022/10/11 11:35
 *  亚专业选项栏
 */
-->
<template>
  <div class='sub-majorTab-box'>
    <ul class='sub-list'>
      <li :class='activeId === 0 ? "is_active" : ""' class='sub-item cursor' @click='changeChannel<PERSON>andler({
         channelId:0
      })'>全部
      </li>
      <li
        v-for='item in channelList' :key='item.channelId'
        :class='activeId === item.channelId ? "is_active" : ""'
        class='sub-item cursor'
        @click='changeChannelHandler(item)'>
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'SubMajorTab',
  props: {
    channelList: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      activeId: 0
    }
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/14 15:37
     *  切换频道
     */
    change<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(channel) {
      this.activeId = channel.channelId
      this.$emit('changeChannelHandler', channel)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
