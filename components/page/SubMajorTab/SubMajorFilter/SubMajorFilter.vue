<template>
  <div class='sub-majorfilter'>
    <ul class='filter-list'>
      <li
        v-for='(item,index) in filterType'
        :key='index'
        class='filter-item cursor'>
        <span>{{ item.filterName }}</span>
        <i
          class='el-icon-caret-bottom'
          style='font-size: 12px; color: #0581CE; margin-left: 3px'
        ></i>
        <div class='filter-dataset-box'>
          <ul class='filter-dataset-list'>
            <li
              :class='
              item.code === "type" ?
              typeActiveId === 0 ? "is_active" : "" :
              item.code === "time" ?
              timeActiveId === 0 ? "is_active" : ""
              :null'
              class='filter-dataset-item'
              @click='changeChannelTypeOrTimeHandler({id:0},item.code)'>
              全部
            </li>
            <li
              v-for='itemChildren in item.filterDataSet'
              :key='itemChildren.id'
              :class='
              item.code === "type" ?
              typeActiveId === itemChildren.id ? "is_active" : "" :
              item.code === "time" ?
              timeActiveId === itemChildren.id ? "is_active" : ""
              : null'
              class='filter-dataset-item'
              @click='changeChannelTypeOrTimeHandler(itemChildren,item.code)'>
              {{ itemChildren.name }}
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { getGuideTypeList } from '@/api/guidance'

export default {
  name: 'SubMajorFilter',
  data() {
    return {
      guideTypeListSet: [],
      typeActiveId: 0,
      timeActiveId: 0,
      filterType: [
        { code: 'type', filterName: '类型', filterDataSet: [] },
        {
          code: 'time', filterName: '发布时间', filterDataSet: []
        }
      ]
    }
  },
  mounted() {
    this.getGuideTypeListHandler()
    this.getYearHandler(2016)
  },
  methods: {
    /**
     * 获取2016年之前的所有年份
     */
    getYearHandler(yearParams = 2016) {
      const year = new Date().getFullYear()
      for (let i = year; i >= yearParams; i--) {
        this.filterType[1].filterDataSet.push({
          id: i,
          name: i + '年'
        })
      }

      this.filterType[1].filterDataSet.push({
        id: 'isBefore',
        name: '更早'
      })
    },
    /**
     *  @author:Rick  @date:2022/10/14 15:24
     *  获取指南共识的切换类型数据
     */
    getGuideTypeListHandler() {
      this.$axios.$request(getGuideTypeList()).then(response => {
        if (response && response.code === 1) {
          this.filterType.forEach((item, index) => {
            if (item.code === 'type') {
              this.filterType[index].filterDataSet = response.list
            }
          })
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/14 16:25
     *  切换频道类型
     */
    changeChannelTypeOrTimeHandler(item, type) {
      switch (type) {
        case 'type': {
          // this.filterType[0].filterName = item.name
          this.typeActiveId = item.id
          this.$emit('changeChannelTypeHandler', item)
          break
        }
        case 'time': {
          // this.filterType[1].filterName = item.name
          this.timeActiveId = item.id
          this.$emit('changeChannelTimeHandler', item)
          break
        }
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
