.side-hotcontent {
  padding: 18px 14px;
  background: #FBFBFB;
  border-radius: 6px;
  box-sizing: border-box;

  .title {
    margin-bottom: 19px;

    .shareStyleIcon {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }

    span {
      font-size: 18px;
    }
  }

  .hot-content-list {
    display: grid;
    grid-gap: 16px 0;
    font-size: 14px;
    color: var(--font-color-333);

    .item-li:hover .info-content {
      color: var(--theme-color);
    }

    .item {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .num {
        width: 20px;
        height: 20px;
        line-height: 18px;
        text-align: center;
        border-radius: 6px;
        margin-right: 8px;
        font-size: 10px;
        background: #C0CFDD;
        color: #FFFFFF;
        flex-shrink: 0;
      }
    }

    .content-item {
      display: grid;
      grid-template-columns: 106px calc(100% - 106px - 14px);
      grid-gap: 0 10px;

      .img-box {
        height: 59.63px;
        border-radius: 6px;
        overflow: hidden;
        position: relative;

        .first-num {
          position: absolute;
          left: 0;
          top: 0;
          width: 20px;
          height: 20px;
          line-height: 18px;
          text-align: center;
          background: #EE4A22;
          color: #FFFFFF;
          font-size: 10px;
        }
      }

      .info-content {
        line-height: 18px;
        transition: all .3s;
      }
    }
  }
}
