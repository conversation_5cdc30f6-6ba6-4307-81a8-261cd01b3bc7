<template>
  <section class='side-hotcontent'>
    <!--   标题   -->
    <p class='title  flex_start flex_align_center'>
      <svg-icon :icon-class='icon' class-name='shareStyleIcon'></svg-icon>
      <span class='fontWeight'>{{ title }}</span>
    </p>

    <ul class='hot-content-list'>
      <li v-for='(item,index) in dataSet' :key='index' @click='jumpDetailPageHandler(item)'>
        <!--   有图片   -->
        <div
          v-if='index===0 &&
          (
            item.type === "info" ?
            item.info.infoImg :
            item.type === "mp_article" ?
            item.mp_article.cover :
            item.type === "meeting" ?
            item.meeting.appMainPic :
            item.type === "interpreting_video" ?
            item.interpreting_video.cover
            :
            null
          )'
          class='content-item item-li cursor'>
          <div class='img-box'>
            <img :src='
            item.type === "info" ?
            item.info.infoImg :
            item.type === "mp_article" ?
            item.mp_article.cover :
            item.type === "meeting" ?
            item.meeting.appMainPic :
            item.type === "interpreting_video" ?
            item.interpreting_video.cover
            :
            null' alt='侧边内容' class='img_cover'>
            <div class='first-num'>1</div>
          </div>
          <div class='info-content'>
            <span class='text-limit-3'>
                {{
                item.type === 'info' ?
                  item.info.infoTitle :
                  item.type === 'mp_article' ?
                    item.mp_article.title :
                    item.type === 'meeting' ?
                      item.meeting.meetingName :
                      item.type === 'interpreting_video' ?
                        item.interpreting_video.videoName
                        :
                        null
              }}
            </span>
          </div>
        </div>
        <!--   没有图片   -->
        <div v-if='index===0 &&
          (
            item.type === "info" ?
            !item.info.infoImg :
            item.type === "mp_article" ?
            !item.mp_article.cover :
            item.type === "meeting" ?
            !item.meeting.appMainPic :
            item.type === "interpreting_video" ?
            !item.interpreting_video.cover
            :
            null
          )' class='item item-li cursor'>
          <div :style='{background : "#EE4A22"}'
               class='num'>1
          </div>
          <div class='info-content text-limit-1'>
            {{
              item.type === 'info' ?
                item.info.infoTitle :
                item.type === 'mp_article' ?
                  item.mp_article.title :
                  item.type === 'meeting' ?
                    item.meeting.meetingName :
                    item.type === 'interpreting_video' ?
                      item.interpreting_video.videoName
                      :
                      null
            }}
          </div>
        </div>

        <!--   从2开始   -->
        <div v-if='index>0' class='item item-li cursor'>

          <div :style='{
            background :
            index === 1 ? "#EE7E22"
             :
             index === 2 ? "#EEDA22" : ""
          }'
               class='num'>
            {{ index + 1 }}
          </div>
          <div class='info-content text-limit-1'>
            {{
              item.type === 'info' ?
                item.info.infoTitle :
                item.type === 'mp_article' ?
                  item.mp_article.title :
                  item.type === 'meeting' ?
                    item.meeting.meetingName :
                    item.type === 'interpreting_video' ?
                      item.interpreting_video.videoName
                      :
                      null
            }}
          </div>
        </div>
      </li>
    </ul>

  </section>
</template>

<script>
export default {
  name: 'SideHotContent',
  props: {
    title: {
      type: String,
      default: '请输入标题',
      require: false
    },
    icon: {
      type: String,
      default: 'hotsearch',
      require: false
    },
    dataSet: {
      type: Array,
      required: true
    }
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/18 13:42
     *  跳转详情页
     */
    jumpDetailPageHandler(item) {
      let url = ''
      switch (item.type) {
        case 'info': {
          url = `/guidance/detail/article/${item.info.infoId}`
          break
        }
        case 'mp_article': {
          url = `/case/detail-ugc?id=${item.mp_article.id}`
          break
        }
        case 'meeting': {
          url = `/meeting/detail?id=${item.meeting.id}`
          break
        }
        case 'interpreting_video': {
          url = `/guidance/detail/video/${item.interpreting_video.id}`
          break
        }
      }
      const { href } = this.$router.resolve({ path: url })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
