<template>
  <div class='speaker-list-container'>
    <div class="title">
      <div class="title_content">
        <svg-icon icon-class="speak_author" class-name="icons"/>
        <span>讲者</span>
      </div>
      <div v-if="todayFormatData.length>1" class="slider_wrapper">
        <div
          class="slider_btn slider_left"
          :class="index > 1 ? 'slider_btn_active' : ''"
          @click="sliderHandler('left')">
          <svg-icon
            :icon-class="index > 1 ?   'speaker_active' : 'speaker'"
            :style="index > 1  ? 'transform: rotateY(180deg)' : ''"
            class-name="slider_icons"/>
        </div>

        <div
          class="slider_btn slider_right"
          :class="index < todayFormatData.length ? 'slider_btn_active' : ''"
          @click="sliderHandler('right')">
          <svg-icon
            :icon-class="index < todayFormatData.length ? 'speaker_active' : 'speaker'"
            :style="index < todayFormatData.length  ? '' : 'transform: rotateY(180deg)'"
            class-name="slider_icons"/>
        </div>
      </div>
    </div>
    <div style="overflow: hidden">
      <div ref="list_wrapper" class="list_wrapper">
        <ul v-for="(itemF,indexF) in todayFormatData" :key="indexF" class='speaker-list'>
          <li
            v-for='item in itemF'
            :key='item.id'
            :class='item.systemUserId ? "is_active":""'
            class='speaker-item'
            @click='jumpUserCenterHandler(item.systemUserId)'>
            <div class='img-box'>
              <img
                v-if='item.headImage'
                :src='$tool.compressImg(item.headImage,58,58)'
                alt='讲者头像'
                class='img_cover'>
              <svg-icon
                v-else
                class-name='img_cover'
                icon-class='signinavatar'
              />
            </div>

            <div class='speaker-info'>
              <p class='name'>{{ item.authorName }}</p>
              <p class='company'>{{ item.company }}</p>
            </div>

            <!--          <FollowButton-->
            <!--            v-if='item.systemUserId' :id='item.systemUserId' :is-follow='item.followStatus !== "NF"'-->
            <!--            @followHandler='followHandler' />-->
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpeakerList',
  props: {
    authorList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      index: 1
    }
  },
  computed: {
    todayFormatData() {
      if (this.authorList && this.authorList.length > 0) {
        const array = this.authorList
        const groupSize = 2;
        const result = [];

        for (let i = 0; i < array.length; i += groupSize) {
          const group = array.slice(i, i + groupSize);
          result.push(group);
        }

        return result
      } else {
        return []
      }
    }
  },
  methods: {
    followHandler(id) {
      this.$store.dispatch('follow', id)
    },
    jumpUserCenterHandler(id) {
      if (id) {
        const routeUrl = this.$router.resolve({
          path: `/user-center?profileUserId=${id}`
        })
        window.open(routeUrl.href, '_blank')
      }
    },
    sliderHandler(type) {
      const wrapperDom = this.$refs.list_wrapper
      if (type === 'left' && this.index > 1) {
        this.index -= 1;
        wrapperDom.style.transform = `translateX(-${this.index - 1}00%)`

      } else if (type === 'right' && (this.index < this.todayFormatData.length)) {
        wrapperDom.style.transform = `translateX(-${this.index}00%)`
        this.index += 1;
      }

    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
