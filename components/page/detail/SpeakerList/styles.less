
.speaker-list-container {
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;

    .icons {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      margin-right: 6px;
    }

    font-size: 16px;
    color: #333333;
    line-height: 24px;

    .title_content {
      display: flex;
      align-items: center;
      font-weight: 700;
    }

    .slider_wrapper {
      display: flex;
      align-items: center;

      .slider_btn {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;

        .slider_icons {
          width: 20px;
          height: 20px;
          color: #C7C7C7;
          cursor: pointer;
        }
      }

      .slider_btn_active {

        .slider_icons {
          color: #708AA2;
          path{
            color: white;
          }
        }
      }

      .slider_left {
        color: #708AA2;
        fill: #FFFFFF !important;
        margin-right: 8px;
        transform: none;
      }
    }
  }

  .list_wrapper {
    display: flex;
    transition: all .2s ease;
  }

  .speaker-list {
    width: 100%;
    flex-shrink: 0;

    .speaker-item {
      padding: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;
      border-radius: 8px;
      background: #F8F8F8;

      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .img-box {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 16px;

      }

      .speaker-info {
        flex: 1;

        .name {
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          transition: all .3s;

          /* identical to box height, or 100% */

          color: #333333;
          margin-bottom: 4px;
        }

        .company {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;

          /* identical to box height, or 100% */

          color: #666666;
        }
      }
    }

    .is_active {
      &:hover .name {
        color: var(--theme-color);
      }
    }
  }
}
