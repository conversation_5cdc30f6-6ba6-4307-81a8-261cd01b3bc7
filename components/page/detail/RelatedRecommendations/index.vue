<template>
  <!--Related Start-->
  <div v-show='relatedArticles && relatedArticles.length>0' class='Related_box'>
    <div class='title'>{{ title }}</div>
    <ul class='Related-item'>
      <template v-for='item in relatedArticles'>
        <li
          v-if='item.type === "info"'
          :key='item.info.infoId'
          @click='jumpDetailsPageFun(item.info.infoId,"info")'
        >
          <div class='left'>
            <img
              v-if='item.info.infoImg'
              :src='$tool.compressImg(item.info.infoImg,142,80)'
              alt=''
              class='img_cover' />
            <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
          </div>
          <div class='right flex_column flex_column_justify'>
            <p class='item-title text-limit-2'>
              {{ item.info.infoTitle }}
            </p>
            <p class='con'>
              {{ timeStamp.timestamp_13(item.info.publishDate, 'y-m-d') }}
            </p>
          </div>
        </li>
        <li
          v-if='item.type === "meeting"'
          :key='item.meeting.id'
          @click='jumpDetailsPageFun(item.meeting.id,"meeting")'
        >
          <div class='left'>
            <img
              v-if='item.meeting.appMainPic || item.meeting.titlePic'
              :src='$tool.compressImg(item.meeting.appMainPic || item.meeting.titlePic,142,80)'
              alt=''
              class='img_cover' />
            <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
            <LiveState :state='item.meeting.meetingLiveStatus' />
          </div>
          <div class='right flex_column flex_column_justify'>
            <p class='item-title text-limit-2'>
              {{ item.meeting.meetingName }}
            </p>
            <p class='con'>
              {{ item.meeting.meetingDateStr }}
            </p>
          </div>
        </li>
        <li
          v-if='item.type === "ocs_course"'
          :key='item.ocs_course.id'
          @click='jumpDetailsPageFun(item.ocs_course.id,"ocs_course")'
        >
          <div class='left'>
            <img
              v-if='item.ocs_course.cover'
              :src='$tool.compressImg(item.ocs_course.cover,142,80)'
              alt=''
              class='img_cover' />
            <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
            <div class='video-icon'></div>
          </div>
          <div class='right flex_column flex_column_justify'>
            <p class='item-title text-limit-2'>
              {{ item.ocs_course.name }}
            </p>
            <p class='con'>
              {{ timeStamp.timestamp_13(item.ocs_course.onlineTime, 'y-m-d') }}
            </p>
          </div>
        </li>
      </template>

    </ul>
  </div>
  <!--End-->
</template>

<script>
import LiveState from '../../../LiveState/LiveState.vue'

export default {
  name: 'RelatedRecommendations',
  components: { LiveState },
  props: {
    title: {
      type: String,
      default: '相关推荐'
    },
    relatedArticles: {
      type: Array,
      default: () => []
    }
  },
  mounted() {

  },
  methods: {
    /**
     * 跳转文章详情
     * @param id
     * @param type
     */
    jumpDetailsPageFun(id, type) {
      if (type === 'info') {
        const { href } = this.$router.resolve({ name: 'index-info-detail', query: { id } })
        window.open(href, '_blank')
      } else if (type === 'meeting') {
        const { href } = this.$router.resolve({ name: 'index-meeting-detail', query: { id } })
        window.open(href, '_blank')
      } else if (type === 'ocs_course') {
        this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: id } })
      }

    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
