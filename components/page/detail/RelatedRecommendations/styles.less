.Related_box {
  border-radius: 6px;
  border: 1px solid #f0f0f0ff;
  padding: 17px 14px 0;
  overflow: hidden;

  .title {
    font-size: 18px;
    line-height: 20px;
    position: relative;
    padding-left: 14px;
    margin-bottom: 19px;
  }

  .title::before {
    content: "";
    display: block;
    width: 3px;
    height: 15px;
    background: #0581ceff;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -7.5px;
  }

  .Related-item {
    overflow: hidden;

    li {
      display: flex;
      justify-items: flex-start;
      margin-bottom: 16px;
      cursor: pointer;

      .left {
        min-width: 142px;
        max-width: 142px;
        height: 80px;
        margin-right: 10px;
        overflow: hidden;
        border-radius: 6px;
        position: relative;

        .video-icon {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 34px;
          height: 34px;
          background: url("assets/images/guidance/video-icon.png");
        }
      }

      .right {
        .item-title {
          font-size: 14px;
          color: #000000ff;
          margin-bottom: 9px;
          line-height: 24px;
        }

        .con {
          color: #708aa2ff;
          font-size: 12px;
          line-height: 24px;
        }
      }
    }
  }
}
