<template>
  <transition name='el-fade-in-linear'>
    <li class='comments-item'>
      <nuxt-link :to='{path:`/user-center?profileUserId=${systemUserId}`}' class='left-userImg' target='_blank'>
        <img v-if='commenterAvatar' :src='$tool.compressImg(commenterAvatar,36,36)' alt='头像' class='img_cover'>
        <svg-icon
          v-else
          class-name='svg_zhanwei'
          icon-class='signinavatar'
        />
      </nuxt-link>
      <div class='right-content'>
        <p class='username'>
          <span class='name'>{{ reviewer }}</span>
          <span class='time'>{{ commentTime }}</span>
        </p>
        <p class='text'>
          {{ text }}
        </p>

        <transition name='el-fade-in-linear'>
          <ol v-if='subComments.length>0' class='reply-box'>
            <SubComments
              v-for='(item,index) in subComments'
              v-show='subComments.length>2 ? index < opList : true'
              :id='item.id'
              :key='item.id'
              :child='item.child'
              :child-system-user-id='item.childId'
              :comment-time='timeStamp.timestampFormat(item.commentTime / 1000)'
              :is-expand-index-of='isExpandIndexOf'
              :parent='item.parent'
              :reply-content='item.text'
              :reviewer='reviewer'
              @openCollectionHandler='openChildrenCollectionHandler'
            />
            <li v-if='subComments.length>2' class='open-list' @click='opList === 2 ? opList = 999999 : opList = 2'>
              {{ opList === 2 ? '展开>' : '收回' }}
            </li>
          </ol>
        </transition>

        <div class='comment-function-bar'>
          <div class='fabulous-box flex_start flex_align_center' @click='$emit("commentLikeHandler",id)'>
            <svg-icon
              :icon-class='isFabulous ? "comment_active" : "comment_zan"'
              class-name='imgbox_amll cursor'
            ></svg-icon>
            <span>
                赞 {{ commentLiked }}
              </span>
          </div>
          <div
            class='collection-box flex_start flex_align_center'
            @click='openCollectionHandler(isExpandIndexOf,reviewer,id)'>
            <svg-icon
              class-name='imgbox_amll cursor'
              icon-class='comment-reply'
            ></svg-icon>
            <span>
                回复
              </span>
          </div>
        </div>

        <CollapseTransition>
          <div v-if='isExpandIndex === isExpandIndexOf' class='reply-input-container'>
            <el-input
              id='commentInput'
              v-model.trim='replyInfo'
              :placeholder='placeholderAi'
              :rows='2'
              resize='none'
              type='textarea'
              @keyup.enter.native='submitReplyCommentsHandler(replyInfo)'>
            </el-input>
            <div class='bar-box flex_between flex_align_center'>
              <span class='tips'>
                Enter 发布
              </span>
              <div class='buttonlist-box flex_start flex_align_center'>
                <div class='themeButton fabuButton' @click='submitReplyCommentsHandler(replyInfo)'>发布</div>
                <div class='cancelButton' @click='isExpandIndex = null'>取消</div>
              </div>
            </div>
          </div>
        </CollapseTransition>
      </div>
    </li>
  </transition>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import SubComments from '@/components/page/detail/Comment/CommentList/CommentItem/SubComments/SubComments'

export default {
  name: 'CommentItem',
  components: {
    CollapseTransition,
    SubComments
  },
  props: {
    id: {
      type: Number,
      required: true
    },
    systemUserId: {
      type: Number,
      required: true
    },
    isExpandIndexOf: {
      type: Number,
      required: true
    },
    text: {
      type: String,
      default: '评论文本',
      required: false
    },
    reviewer: {
      type: String,
      default: '评论人',
      required: false
    },
    commenterAvatar: {
      type: String,
      default: '',
      required: false
    },
    commentTime: {
      type: String,
      default: '评论时间',
      required: false
    },
    commentLiked: {
      type: Number,
      default: 0,
      required: false
    },
    isFabulous: {
      type: Boolean,
      default: false,
      required: false
    },
    subComments: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    id(newData) {
      if (newData) {
        this.replyId = newData
      }
    }
  },
  data() {
    return {
      opList: 2,                   // 是否展开
      replyId: null,              // 回复ID
      replyInfo: '',             // 回复评论
      isExpandIndex: null,       // 展开回复评论下标
      placeholderAi: ''          // 回复提示语
    }
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/19 16:43
     *  展开回复框
     */
    openCollectionHandler(index, name, id) {
      this.isExpandIndex = index
      this.placeholderAi = `回复 : @${name}`
      this.replyId = id
    },
    /**
     *  @author:Rick  @date:2022/10/20 13:21
     *  子评论展开回复框
     */
    openChildrenCollectionHandler(params) {
      this.placeholderAi = `回复 : @${params.name}`
      this.isExpandIndex = params.isExpandIndexOf
      this.replyId = params.id
    },
    /**
     *  @author:Rick  @date:2022/10/20 10:10
     *  提交回复输入框
     */
    submitReplyCommentsHandler(info) {
      if (info === '' || !info) {
        this.$toast('请输入回复内容')
        return
      }
      this.$toast.loading({ message: '提交中...', forbidClick: true })

      this.$emit('submitReplyCommentsHandler', { info, parentId: this.replyId }, (val) => {
        if (val) {
          // eslint-disable-next-line no-undef
          setTimeout(() => {
            this.replyInfo = ''
            this.isExpandIndex = null
            this.$toast('评论成功!')
          }, 150)
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
