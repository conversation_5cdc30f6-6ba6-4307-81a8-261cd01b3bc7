.comments-item {
  display: grid;
  grid-template-columns:36px calc(100% - 36px - 8px);
  grid-gap: 0 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #F0F0F0;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  .left-userImg {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;

    .svg_zhanwei {
      width: 100%;
      height: 100%;
    }
  }

  .right-content {
    .username {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: end;
      margin-bottom: 10px;

      .name {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #333333;
      }

      .time {
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        color: #708AA2;
        margin-left: 12px;
      }
    }

    .text {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #333333;
      margin-bottom: 15px;
    }

    .reply-box {
      padding: 10px 10px 10px;
      background: #FBFBFB;
      border-radius: 6px;
      margin-bottom: 15px;
      position: relative;

      .open-list {
        position: absolute;
        right: 20px;
        bottom: 10px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #0581CE;
        cursor: pointer;
      }
    }

    .comment-function-bar {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      span {
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: #708AA2;
        margin-left: 6px;
        transition: all .3s;
      }

      .fabulous-box {
        cursor: pointer;
        margin-right: 23px;

        .imgbox_amll {
          width: 16px;
          height: 16px;
        }
      }

      .collection-box {
        cursor: pointer;


        .imgbox_amll {
          width: 16px;
          height: 16px;
        }

      }
    }

    .reply-input-container {
      background: rgb(245, 245, 245);
      border-radius: 6px;
      padding: 10px;
      margin: 13px 0 0;

      .bar-box {
        margin: 10px 0 5px;

        .tips {
          font-size: 12px;
          color: #708aa2;
        }

        .buttonlist-box {
          .fabuButton {
            color: #fff;
            margin-right: 15px;
            border-radius: 10px;
            padding: 0 15px;
            cursor: pointer;
            height: 30px;
            font-size: 12px;
            line-height: 30px;
          }

          .cancelButton {
            border: 1px solid #999;
            border-radius: 10px;
            padding: 0 14px;
            height: 28px;
            line-height: 28px;
            cursor: pointer;
            font-size: 12px;
            color: #708aa2;
            -webkit-transition: all .3s;
            transition: all .3s
          }
        }
      }
    }
  }
}
