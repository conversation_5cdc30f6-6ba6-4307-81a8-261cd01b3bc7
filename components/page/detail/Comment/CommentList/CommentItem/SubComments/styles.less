.reply-item {
  margin-bottom: 4px;
  line-height: 12px;
  transition: all .3s;
  display: flex;
  justify-content: flex-start;
  align-items: start;

  &:hover .replyOrTime {
    opacity: 1 !important;
  }

  &:last-child {
    margin-bottom: 0;
  }

  span {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: #708AA2;
  }

  .right {
    flex-shrink: 0;
    margin-right: 8px;
    line-height: 20px;
  }

  .reply-txt {
    font-weight: 400;
    font-size: 12px;
    color: #708AA2;
    line-height: 20px;

    .textx {
      .text-span {
        margin-right: 15px;
      }

      .replyOrTime {
        transition: all .3s;
        opacity: 0;
      }

      .time {
        margin-left: 5px;
      }
    }
  }

  .reply-item-username {
    color: #0581CE;
    margin-right: 8px;
    flex-shrink: 0;
    line-height: 20px;
  }

  .activeUser {
    &:hover {
      text-decoration: underline;
    }
  }
}
