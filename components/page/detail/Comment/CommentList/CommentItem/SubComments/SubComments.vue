<template>
  <transition name='el-fade-in-linear'>
    <li class='reply-item'>
    <span style='flex-shrink: 0'>
      <nuxt-link :to='{path:`/user-center?profileUserId=${childSystemUserId}`}' class='reply-item-username activeUser'
                 target='_blank'>
        {{ child }}
      </nuxt-link>
    </span>
      <span class='right'>回复</span>
      <span class='reply-item-username'>{{ parent ? parent : reviewer }}</span>
      <span class='right'>:</span>
      <div class='reply-txt flex_start flex_warp'>
        <span class='textx'>
          <span class='text-span'>
             {{ replyContent }}
          </span>
           <span class='replyOrTime'>
            <span class='reply themeFontColor cursor'
                  @click='$emit("openCollectionHandler",{name:child,id,isExpandIndexOf})'>回复</span>
            <span class='time'>{{ commentTime }}</span>
          </span>
        </span>
      </div>
    </li>
  </transition>
</template>

<script>
export default {
  name: 'SubComments',
  props: {
    id: {
      type: Number,
      required: true
    },
    isExpandIndexOf: {
      type: Number,
      required: true
    },
    childSystemUserId: {
      type: Number,
      required: false
    },
    child: {
      type: String,
      default: '回复人名字'
    },
    parent: {
      type: String,
      default: '被回复人名字'
    },
    replyContent: {
      type: String,
      default: '回复内容'
    },
    commentTime: {
      required: false
    },
    reviewer: {
      type: String,
      default: '',
      required: false
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
