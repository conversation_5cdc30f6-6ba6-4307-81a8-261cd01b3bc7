<template>
  <!--  评论列表  -->
  <section class='comments-list-container'>
    <ul class='comments-list'>
      <CommentItem
        v-for='(item,index) in commentList'
        :id='item.id'
        :key='item.id'
        :comment-liked='item.diggs'
        :comment-time='timeStamp.timestampFormat(item.commentTime / 1000)'
        :commenter-avatar='item.commentator.avatarAddress'
        :is-expand-index-of='index'
        :is-fabulous='item.diggStatus === "T"'
        :reviewer='item.commentator.realName'
        :sub-comments='item.childComments'
        :system-user-id='item.commentator.id'
        :text='item.text'
        v-on='$listeners'
      />
    </ul>
  </section>
</template>

<script>

import CommentItem from '@/components/page/detail/Comment/CommentList/CommentItem/CommentItem'

export default {
  name: 'CommentList',
  components: {
    CommentItem
  },
  props: {
    commentList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      replyInfo: ''
    }
  },
  methods: {}
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
