<template>
  <section class='comment-container'>
    <div class='comment-tips'>
      <span class='title'>
        参与评论
      </span>
      <span class='tips'>
        文明上网理性发言，请遵守新闻评论服务协议
      </span>
    </div>
    <!--  评论输入框  -->
    <section class='input-box-container'>
      <el-input
        id='commentInput'
        v-model.trim='comments'
        :rows='2'
        placeholder='添加评论...'
        resize='none'
        trim
        type='textarea'
        @keyup.enter.native='submitCommentsHandler'>
      </el-input>
      <div class='input-bar-container'>
        <div class='left-tips'>
          <div v-if='!$store.state.auth.isLogged'>
            <span class='fontSize16 themeFontColor cursor' @click='jumpSigninFun'>登录</span>
            <span>后参与讨论</span>
          </div>
        </div>
        <div class='right-container'>
          <div
            v-if='$store.state.auth.isLogged'
            class='sumbit-button themeButton'
            @click='submitCommentsHandler'>
            提交评论
          </div>
        </div>
      </div>
    </section>

    <!--  评论列表标题  -->
    <p class='comments-title'>
      全部评论
    </p>

    <!--    <i v-if='isLoading' class='el-icon-loading'></i>-->


    <p v-if='!commentList || (commentList && commentList.length===0)' class='no-list-tips'>
      还没有评论哦~
    </p>

    <!--  评论列表-->
    <CommentList
      v-else
      :comment-list='commentList'
      v-on='$listeners' />

    <!--  查看更多评论 -->
    <section v-if='seeMoreFlag!==null' class='more-comments-container'>
      <div class='more-comments-button themeButton' @click='$emit("viewMoreDataSetHandler")'>
        <span v-if='!seeMoreFlag'>
          查看更多评论
        </span>
        <i v-else class='el-icon-loading'></i>
      </div>
    </section>

  </section>
</template>

<script>
import CommentList from '@/components/page/detail/Comment/CommentList/CommentList'

export default {
  name: 'ArticleComment',
  components: {
    CommentList
  },
  props: {
    commentList: {
      type: Array,
      default: () => []
    },
    seeMoreFlag: {
      default: null,
      required: false
    }
  },
  data() {
    return {
      isLoading: true,  // 加载动画
      comments: '',    // 评论内容
      placeholderAi: ''// 提示语
    }
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/20 10:48
     *  跳转登录
     */
    jumpSigninFun() {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
    },
    /**
     *  @author:Rick  @date:2022/10/20 10:32
     *  提交评论
     */
    submitCommentsHandler() {
      this.$emit('submitCommentsHandler', { commentInfo: this.comments, parentId: null }, (val) => {
        if (val) {
          this.comments = ''
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles.less";
</style>
