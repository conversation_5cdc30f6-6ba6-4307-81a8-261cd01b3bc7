.comment-container {
  .comment-tips {
    margin-bottom: 14px;
    line-height: 22px;

    .title {
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      color: #333333;
    }

    .tips {
      display: inline-block;
      font-weight: 400;
      font-size: 12px;
      line-height: 22px;
      color: #C7C7C7;
      margin-left: 14px;
    }
  }

  .input-box-container {
    //border: 1px solid #F0F0F0;
    border-radius: 6px;
    overflow: hidden;

    /deep/ .el-textarea__inner {
      height: calc(190px - 56px);
      border: none;
      padding: 12px 20px;
      background: #F4F6F8;
    }

    .input-bar-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #E7EBEF;
      height: 60px;
      padding: 0 14px;

      .left-tips {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #7D7D7D;
      }

      .right-container {
        .sumbit-button {
          background: #0581CE;
          border-radius: 4px;
          text-align: center;
          width: 98px;
          height: 38px;
          line-height: 38px;
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
        }
      }
    }
  }

  .no-list-tips {
    text-align: center;
    color: #6d8696;
    padding: 30px;
    font-size: 22px;
  }

  .comments-title {
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: #333;
    margin: 20px 0;
  }

  .more-comments-container {
    .more-comments-button {
      background: #E8F1F6;
      border-radius: 6px;
      line-height: 48px;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #0581CE;
      margin-top: 20px;

      &:hover {
        color: white;
      }
    }
  }
}
