<template>
  <div id='only-guidance-label' class='label-container'>
    <div class='label-list'>
      <a class='label-item' href="/clinical/release" target="_blank" @click="handler">
        <div class='left-info'>
          <div class='img-box'>
            <div class='bg-img'/>
          </div>
          <div class='label-desc'>
            <p class='title'>
              临床招募
            </p>
            <p class='desc'>
              招募代发布 快速找患者
            </p>
          </div>
        </div>
        <div class='right-img'>
          <div class='arrow'/>
        </div>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: "ClinicalDetailLabel",
  methods: {
    handler() {
      this.$analysys.new_btn_click({
        userId: this.$store.state.auth.user.id,
        btnName: "临床招募",
        pageName: document.title
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
