<template>
  <div class='article-content'>
    <GuidanceArticleTitle
      v-if='$route.name === "index-guidance-detail-article-id"'
      :author-list='infoData.info.authorList'
      :publish-date='infoData.info.publishDate'
      :recommend='infoData.recommend'
      :title='infoData.title'/>

    <GuidanceLabelEntrance v-if='$route.name === "index-guidance-detail-article-id"'/>

    <InfoArticleTitle
      v-if='$route.name !== "index-guidance-detail-article-id"'
      :attrs='infoData.attrs'
      :author-names='infoData.authorNames'
      :publish-date='infoData.info.publishDate'
      :recommend='infoData.recommend'
      :subspecialtys='infoData.info.subspecialtys'
      :title='infoData.title'
      :train-recruit-labels='infoData.trainRecruitLabels'
    />

    <InfoColumn
      v-if='$route.name !== "index-guidance-detail-article-id"'
      :is-clinical-recruitment="infoData.isClinicalRecruitment"/>

    <RecruitmentTrainingEntrance
      v-if='infoData.isInfoTrainRecruit === "A" || infoData.isInfoTrainRecruit === "T" || infoData.isInfoTrainRecruit === "R"'
    />

    <article
      ref='article'
      :class='(!$store.state.auth.isLogged && articleHideNum > 3) ? "hide-article-content" :""'
      class='article-guide'
      @click="previewHandler"
      v-html='infoData.text'
    />

    <HideArticle v-if='(!$store.state.auth.isLogged && articleHideNum > 3)'/>
    <el-image
      ref="preview"
      style="width: 100px; height: 100px;display: none"
      :preview-src-list="srcList">
    </el-image>
  </div>
</template>

<script>
import {restrictViewingDetails} from '../../../../assets/helpers/restrict-viewing-details'
import {removeIframeVideo} from '../../../../assets/helpers/remove-iframe-video'
import RecruitmentTrainingEntrance from './RecruitmentTrainingEntrance/RecruitmentTrainingEntrance.vue'
import ClinicalDetailLabel from "./ClinicalDetailLabel/index.vue";
import HideArticle from '@/components/page/detail/HideArticle/HideArticle'
import GuidanceArticleTitle from '@/components/page/detail/ArticleContent/GuidanceArticleTitle/GuidanceArticleTitle'
import GuidanceLabelEntrance from '@/components/page/detail/ArticleContent/GuidanceLabelEntrance/GuidanceLabelEntrance'
import InfoArticleTitle from '@/components/page/detail/ArticleContent/InfoArticleTitle/InfoArticleTitle'
import InfoColumn from '@/components/ArticleColumn/ColumnBox/index'

export default {
  name: 'ArticleContent',
  components: {
    ClinicalDetailLabel,
    HideArticle,
    GuidanceArticleTitle,
    GuidanceLabelEntrance,
    InfoArticleTitle,
    InfoColumn,
    RecruitmentTrainingEntrance
  },
  props: {
    infoData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      articleHideNum: 1,
      catalogueList: [],
      srcList: []
    }
  },
  mounted() {
    restrictViewingDetails(this, 'restrict_article', 'id')

    const iframeArr = document.querySelectorAll('iframe')
    for (let i = 0; i < iframeArr.length; i++) {
      const srcName = iframeArr[i].getAttribute('src')
      const video = document.createElement('video')
      video.style.width = '100%'
      video.style.background = 'black'
      video.setAttribute('controls', 'controls')
      video.style.height = iframeArr[i].offsetWidth * (9 / 16) + 'px'
      video.src = srcName

      const parentElement = iframeArr[i].parentElement
      parentElement.insertAdjacentElement('afterbegin', video)

      iframeArr[i].remove()


    }


    removeIframeVideo()
  },
  methods: {
    previewHandler(event) {
      const mateStr = "patient.medtion.com"
      const tagParentName = event?.target?.parentNode?.tagName
      const tagParentHref = event?.target?.parentNode?.href
      const tagName = event?.target?.tagName
      const tagHref = event?.target?.href


      if ((tagName === 'A' && tagHref && tagHref.includes(mateStr)) || (tagParentName === "A" && tagParentHref && tagParentHref.includes(mateStr))) {
        this.$analysys.new_btn_click({
          userId: this.$store.state.auth.user.id,
          btnName: "跳转患者端",
          pageName: document.title,
          tourl: tagHref || tagParentHref
        })
      }

      if (event?.target?.tagName === 'IMG' && tagParentName !== "A" && event?.target.src) {
        this.srcList = [event?.target.src]
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.$refs.preview.clickHandler()
        }, 0)
      }
    }
  }
}
</script>

<style>

.article-guide img {
  max-width: 100%;
  width: 100%;
  height: auto;
  cursor: zoom-in;
}

.article-guide a > img {
  cursor: pointer;
}

.article-guide section {
  max-width: 100%;
}

.article-guide iframe {
  width: 100%;
  min-height: 45vh;
}
</style>
<style lang='less' scoped>
@import "./styles";
</style>
