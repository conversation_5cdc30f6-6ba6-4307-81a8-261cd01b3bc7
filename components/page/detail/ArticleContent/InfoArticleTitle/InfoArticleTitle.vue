<template>
  <section class='article-topTitle'>
    <div class='article-title'>
      <svg-icon
        v-if='recommend==="T"'
        class-name='poneIcon'
        icon-class='jinghua'
      />
      <span>
          {{ title }}
        </span>
    </div>
    <div class='article-author'>
      <span class='article-time'>
        {{ timeStamp.timestamp_13(publishDate, 'yyy-mm-d-h-m-s') }}
      </span>
      <span v-if='authorNames' class='authorName'>
          {{ authorNames }}
      </span>

      <InfoLabelSet :attrs='attrs' :subspecialtys='subspecialtys' />
      <LabelSet v-for='item in trainRecruitLabels' :key='item.id' :label-name='item.labelName' />
    </div>

  </section>
</template>

<script>
import LabelSet from './LabelSet/LabelSet.vue'
import InfoLabelSet from './InfoLabelSet/InfoLabelSet.vue'

export default {
  name: 'InfoArticleTitle',
  components: {
    LabelSet,
    InfoLabelSet
  },
  props: {
    title: {
      type: String,
      default: '文章标题'
    },
    recommend: {
      type: String,
      default: ''
    },
    authorNames: {
      type: String,
      default: ''
    },
    publishDate: {
      type: Number,
      default: 0
    },
    subspecialtys: {
      type: Array,
      required: false,
      default: () => []
    },
    attrs: {
      type: Array,
      required: false,
      default: () => []
    },
    trainRecruitLabels: {
      type: Array,
      required: false,
      default: () => []
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
