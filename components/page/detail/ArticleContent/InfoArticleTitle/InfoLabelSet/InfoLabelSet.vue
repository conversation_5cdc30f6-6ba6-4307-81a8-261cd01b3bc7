<template>
  <span>
     <span v-if='$route.name !== "index-compile-detail" && subspecialtys.length>0'>
        <span
          v-for='item in subspecialtys'
          :key='item.id'
          class='con_lable_sub'
        >
          <span
            v-for='itemChildren in item.children'
            :key='itemChildren.id'
            class='con_lable_sub_item cursor'
          >
            {{ item.name + '-' + itemChildren.name }}
          </span>
        </span>
      </span>

    <span v-else>
        <span
          v-for='item in attrs'
          :key='item.id'
          class='con_lable_sub'
        >
          <span
            class='con_lable_sub_item'
          >
            {{ item.name }}
          </span>
        </span>
      </span>
  </span>
</template>

<script>
export default {
  name: 'InfoLabelSet',
  props: {
    subspecialtys: {
      type: Array,
      default: () => []
    },
    attrs: {
      type: Array,
      default: () => []
    }
  },
  mounted() {

  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
