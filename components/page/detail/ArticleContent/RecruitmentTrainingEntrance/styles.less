.label-container {
  width: 48.5815%;
  margin: 16px 0;
  cursor: pointer;

  .label-list {
    padding: 12.5px 16.5px;
    box-sizing: border-box;
    background: #FBFBFB;
    border-radius: 6px;
    transition: all .3s;

    .label-item {
      display: flex;
      justify-content: space-between;

      align-items: center;


      .left-info {
        display: flex;
        justify-content: flex-start;
        grid-gap: 0 14px;
        align-items: center;

        .img-box {
          width: 38px;
          height: 35px;

          .bg-img {
            width: 100%;
            height: 100%;
            background: url('assets/images/recruitment-training/entrance.png') -2px -3px;
          }
        }

        .label-desc {
          .title {
            font-weight: 500;
            font-size: 14px;
            line-height: 14px;
            color: var(--font-color-333);
            margin-bottom: 8px;
          }

          .desc {
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: #999999;
          }
        }
      }

      .right-img {
        .arrow {
          width: 14px;
          height: 14px;
          background: url('assets/images/guidance/icon-set.png') -168px -106px;
        }
      }

    }
  }
}
