<template>
  <section class='article-topTitle'>
    <p class='article-title'>
      <svg-icon
        v-if='recommend==="T"'
        class-name='poneIcon'
        icon-class='jinghua'
      />
      <span>
          {{ title }}
        </span>
    </p>
    <p class='article-author'>
        <span v-for='author in authorList' :key='author.id' class='authorName'>
          <span v-if='!author.webApiAuthorUserId'>{{ author.authorName }}</span>
          <nuxt-link v-else :to='{path:`/user-center?profileUserId=${author.webApiAuthorUserId}`}' target='_blank'>
            <span class='cursor' style='color: var(--theme-color)'>{{ author.authorName }}</span>
          </nuxt-link>
        </span>
    </p>
    <p class='article-time'>
      {{ timeStamp.timestampFormat(publishDate / 1000) }}
    </p>
  </section>
</template>

<script>
export default {
  name: 'ArticleTitle',
  props: {
    title: {
      type: String,
      default: '文章标题'
    },
    recommend: {
      type: String,
      default: ''
    },
    authorList: {
      type: Array,
      default: () => []
    },
    publishDate: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
