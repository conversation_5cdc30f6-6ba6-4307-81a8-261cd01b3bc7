.label-container {
  text-align: right;
  width: 48.5815%;
  margin-bottom: 20px;

  .label-list {
    padding: 12px 16.5px;
    box-sizing: border-box;
    background: #FBFBFB;
    border-radius: 6px;
    transition: all .3s;

    .label-item {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      align-items: center;


      .left-info {
        display: grid;
        grid-template-columns: 35px calc(100% - 35px - 14px);
        grid-gap: 0 14px;
        align-items: center;

        .img-box {
          width: 38px;
          height: 36px;

          .bg-img {
            width: 38px;
            height: 36px;
            background: url('assets/images/guidance/icon-set.png')  -109px -109px;
          }
        }

        .label-desc {
          .title {
            font-weight: 500;
            font-size: 14px;
            line-height: 14px;
            color: var(--font-color-333);
            margin-bottom: 8px;
            text-align: left;
          }

          .desc {
            font-weight: 400;
            font-size: 12px;
            line-height: 12px;
            color: #999999;
          }
        }
      }

      .right-img {
        .arrow {
          width: 14px;
          height: 14px;
          background: url('assets/images/guidance/icon-set.png') -168px -106px;
        }
      }

    }
  }
}
