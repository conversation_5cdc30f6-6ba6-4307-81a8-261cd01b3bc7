<template>
  <div class='author-column-container'>
    <ul
      :style='authorList.length===1 ? "justify-content: center;" : authorList.length===2 ? "justify-content: space-around;" : ""'
      class='author-list-ul'
      @mouseleave='userInfoNum = -1'>
      <li
        v-for='(user,index) in authorList'
        :key='index'
        class='author-item'>
        <div
          :style='{
          width:authorList.length===1 ? "78px" : "60px",
          height:authorList.length===1 ? "78px" : "60px",
        }'
          class='image-container'
          :class='user.authorUserId ? "is_active_author" : ""'
          @click='jumpUserCenterPageHandler(user.authorUserId)'
          @mouseover='userInfoNum = user.authorUserId'
        >
          <img
            v-if='user.avatarAddress || user.headImage'
            :alt='user.authorName'
            :src='$tool.compressImg(user.avatarAddress || user.headImage,60,60)'
            class='img_cover'>
          <svg-icon
            v-else
            class-name='imgbox_amll'
            icon-class='signinavatar'
          ></svg-icon>
        </div>
        <div
          v-show='userInfoNum && userInfoNum === user.authorUserId'
          :class='authorList.length > 4 ? "userListFixed" : ""'>
          <InformationPopUp
            :id='user.authorUserId'
            :author-name='user.authorName'
            :fans='user.fans' :follow='user.followStatus'
            :follows='user.follows'
            :head-image='user.avatarAddress || user.headImage'
            :is-auth='user.isAuth'
            :post-num='user.postNum' :title='user.title'
            :user-diggs='user.userDiggs'
            :user-length='authorList.length'
            @followFn='followFun(user.authorUserId)' />
        </div>
        <p class='user-name text-limit-1'>{{ user.authorName }}</p>
        <div
          v-show='parseInt(user.authorUserId) !== parseInt($store.state.auth.user.id) && user.authorUserId'
          :class='user.followStatus !== "FD" ? "" : "follow-ok"'
          :style='authorList.length===1 ? "width: 82px;height: 30px;font-size: 16px;" : ""'
          class='follow-button flex_center flex_align_center'
          @click.stop='followFun(user.authorUserId)'
        >
          <svg-icon
            v-if='user.followStatus !== "FD"'
            :class-name='authorList.length===1 ? "tab_icon_one flex_shirk" : "tab_icon flex_shirk"'
            icon-class='subscribe'></svg-icon>
          {{ user.followStatus !== 'FD' ? '关注' : '已关注' }}
        </div>
      </li>
    </ul>
    <div v-if='newsInfo.length>0' class='latest-release-container'>
      <p class='title'>
        最新发布
      </p>
      <ul class='latest-release-ul'>
        <li
          v-for='vn in newsInfo'
          :key='vn.infoId'
          style='cursor: pointer'
          @click='jumpDetailsPageFun(vn.infoId)'
        >
          <p class='item-title text-limit-2'>
            {{ vn.infoTitle }}
          </p>
          <p class='time'>{{ vn.publishDate }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import InformationPopUp from '@/components/PageComponents/InfoPopup/InfoPopup'

export default {
  name: 'AuthorColumn',
  components: {
    InformationPopUp
  },
  props: {
    authorList: {
      type: Array,
      default: () => []
    },
    newsInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      userInfoNum: -1   // 移入显示
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-03-28 11:08
     * 跳转个人中心
     * ------------------------------------------------------------------------------
     */
    jumpUserCenterPageHandler(userId){
      if(userId){
        this.$store.dispatch("jumpUserCenterPageHandler",userId)
      }
    },
    /**
     * 跳转文章详情
     * @param infoid
     */
    jumpDetailsPageFun(infoid) {
      const { href } = this.$router.resolve({ name: 'index-info-detail', query: { id: infoid } })
      window.open(href, '_blank')
    },
    followFun(id) {
      /**
       *@author:Rick  @date:2022/6/28 15:49  @method:followFun
       *@desc:
       *     - 关注作者
       */
      this.$store.dispatch('follow', id).then(res => {
        this.authorList.forEach((item, index) => {
          if (item.authorUserId === id) {
            if (res && res.result.followStatus === 'T') {
              this.authorList[index].followStatus = 'FD'
            } else {
              this.authorList[index].followStatus = 'NO'
            }
          }
        })

      })

    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
