.author-column-container {
  border-radius: 6px;
  background: #fbfbfb;
  border: 1px solid #f0f0f0;
  padding-top: 40px;
  margin-bottom: 20px;

  .author-list-ul {
    display: grid;
    grid-auto-flow: column;
    grid-gap: 0 20px;
    justify-content: space-between;
    margin: 0 38px;
    overflow-x: auto;
    padding-top: 2px;
    padding-bottom: 36px;
    .mixin_desktop({
      margin: 0 34px;
    });

    .author-item {
      text-align: center;
      position: relative;

      &:hover .is_active_author {
        cursor: pointer;
        outline: 2px solid #0581ce;
      }

      .userListFixed {
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        width: 1200px;
        z-index: 3000;
      }

      .image-container {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto;

        .imgbox_amll {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          display: inline-block;
          overflow: hidden;
        }
      }

      .user-name {
        font-size: 20px;
        color: #0581ce;
        margin: 15px 0;
        line-height: 26px;
        text-align: center;
      }

      .follow-ok {
        background: #c9c9c9 !important;
      }

      .follow-button {
        cursor: pointer;
        border-radius: 18px;
        background: rgba(0, 0, 0, .6);
        color: #fff;
        font-size: 12px;
        line-height: 24px;
        width: 62px;
        margin: 0 auto;

        &:hover {
          background-color: rgba(0, 0, 0, .4);
        }

        .tab_icon_one {
          width: 11px;
          height: 11px;
          margin-right: 4px;
        }

        .tab_icon {
          width: 9px;
          height: 9px;
          margin-right: 4px;
        }
      }
    }
  }

  .latest-release-container {
    padding: 0 14px 0;

    .title {
      font-size: 18px;
      line-height: 20px;
      position: relative;
      padding-left: 14px;

      &::after {
        content: "";
        display: block;
        width: 3px;
        height: 14px;
        background: #0581ce;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -7.5px;
        border-radius: 6px;
      }
    }
  }

  .latest-release-ul {
    margin-top: 20px;

    li {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 20px;
      }

      .item-title {
        font-size: 14px;
        margin-bottom: 6px;
      }

      .time {
        font-size: 12px;
        color: #708aa2ff;
      }
    }
  }
}
