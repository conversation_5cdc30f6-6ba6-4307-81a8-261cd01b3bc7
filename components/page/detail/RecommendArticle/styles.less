.recommend-article-container {
  border-radius: 6px;
  padding: 24px 0;


  .wrapper-title {
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .icons {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }
  }

  .article-list {
    display: grid;
    grid-gap: 16px 0;

    .article-item {
      display: flex;
      align-items: center;
      padding: 8px;
      border-radius: 8px;
      background: #F8F8F8;

      &:hover .title {
        color: var(--theme-color) !important;
      }

      .article-info {
        flex: 1;
        display: flex;
        flex-flow: column;
        justify-content: space-between;

        .title {
          font-weight: 400;
          font-size: 14px;
          line-height: 18px;
          color: #333333;
          transition: all .3s;
          margin-bottom: 4px;
        }

        .time {
          font-weight: 400;
          font-size: 12px;
          line-height: 10px;
          color: #999EA4;
        }
      }

      .img-box {
        flex-shrink: 0;
        width: 90px;
        height: 52px;
        border-radius: 6px;
        overflow: hidden;
        position: relative;
        margin-right: 16px;

        .video-icon {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}
