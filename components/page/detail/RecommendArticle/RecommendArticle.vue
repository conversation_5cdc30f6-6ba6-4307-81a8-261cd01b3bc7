<template>
  <aside class='recommend-article-container'>
    <div class="wrapper-title">
      <svg-icon icon-class="xgtj" class-name="icons"/>
      <span>相关推荐</span>
    </div>


    <ul class='article-list'>
      <li v-for='(item,index) in dataSet' :key='index' class='cursor'>
        <nuxt-link v-if='item.type === "info"' :to='{path:`/guidance/detail/article/${item.info.infoId}`}'
                   class='article-item'
                   target='_blank'>
          <div class='img-box'>
            <img
              v-if='item.info.infoImg'
              :src='$tool.compressImg(item.info.infoImg,90,52)'
              alt='相关推荐文章'
              class='img_cover'>
            <img v-else alt='相关推荐文章' class='img_cover' src='~assets/images/default3.png'>
          </div>
          <div class='article-info'>
            <p class='title text-limit-2'>
              {{ item.info.infoTitle }}
            </p>
            <p class='time'>
              {{ timeStamp.timestampFormat(item.info.publishDate / 1000) }}
            </p>
          </div>
        </nuxt-link>

        <nuxt-link v-else-if='item.type === "meeting"' :to='{path:`/meeting/detail?id=${item.meeting.id}`}'
                   class='article-item'
                   target='_blank'>
          <div class='img-box'>
            <LiveState :state='"LE"' />
            <img v-if='item.meeting.appMainPic || item.meeting.playerCover || item.meeting.titlePic'
                 :src='item.meeting.appMainPic?$tool.compressImg(item.meeting.appMainPic,142,80):item.meeting.playerCover?$tool.compressImg(item.meeting.playerCover,142,80):item.meeting.titlePic?$tool.compressImg(item.meeting.titlePic,142,80):null'
                 alt='相关推荐文章'
                 class='img_cover'>
            <img alt='相关推荐文章' class='img_cover' src='~assets/images/default3.png'>
          </div>
          <div class='article-info'>
            <p class='title text-limit-2'>
              {{ item.meeting.meetingName }}
            </p>
            <p class='time'>
              {{ timeStamp.timestampFormat(item.meeting.publishDate / 1000) }}
            </p>
          </div>
        </nuxt-link>

        <nuxt-link v-else-if='item.type === "interpreting_video"'
                   :to='{path:`/guidance/detail/video/${item.interpreting_video.id}`}' class='article-item'
                   target='_blank'>
          <div class='img-box'>
            <img alt='播放按钮' class='video-icon' src='~assets/images/guidance/video-icon.png'>
            <img v-if='item.interpreting_video.cover' :src='$tool.compressImg(item.interpreting_video.cover,142,80)'
                 alt='播放按钮'
                 class='img_cover'>
            <img alt='相关推荐文章' class='img_cover' src='~assets/images/default3.png'>
          </div>
          <div class='article-info'>
            <p class='title text-limit-2'>
              {{ item.interpreting_video.videoName }}
            </p>
            <p class='time'>
              {{ timeStamp.timestampFormat(item.interpreting_video.publishTime / 1000) }}
            </p>
          </div>
        </nuxt-link>


      </li>
    </ul>
  </aside>
</template>

<script>
import LiveState from '@/components/LiveState/LiveState'

export default {
  name: 'RecommendArticle',
  components: {
    LiveState
  },
  props: {
    title: {
      type: String,
      default: '相关推荐'
    },
    dataSet: {
      type: Array,
      default: () => []
    }
  },
  mounted() {

    // document.addEventListener('keydown', function(event) {
    //   return event.keyCode !== 123 || (event.returnValue = false)
    // })
    //
    // document.addEventListener('contextmenu', function(event) {
    //   // eslint-disable-next-line no-return-assign
    //   return event.returnValue = false
    // })
  }
}
</script>

<style lang='less'>
@import "./styles";
</style>
