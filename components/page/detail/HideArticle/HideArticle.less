.hide-article{
  position: relative;
  height: 194px;
  background: #F8F8F8;
  border-radius: 6px 6px 6px 6px;
  text-align: center;
  padding-top: 36px;
  box-sizing: border-box;
  margin-top: -30px;
  box-shadow:0px -55px 60px #ffffff;
  //&::after{
  //  display: block;
  //  content: "";
  //  position: absolute;
  //  left: 0;
  //  top: -23px;
  //  width: 100%;
  //  height: 23px;
  //  background-color: rgba(255,255,255,0.2);
  //  backdrop-filter: blur(2px);
  //}

  .tips{
    font-size: 18px;
    margin: 0 0 16px;
    color: #333333;
    line-height: 21px;
  }
  button{
    width: 198px;
    height: 46px;
    border-radius: 6px 6px 6px 6px;
    border: 0;
    color: #FFFFFF;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
  }
  .tips-bottom{
    margin-top: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #92A3AE;
    line-height: 16px;
  }
}
