<template>
  <div class='hide-article'>
    <p class='tips'>
      如需继续阅读文章，请先登录/注册
    </p>
    <button class='themeButton'  @click='jumpSigninFun'>登录</button>
    <p class='tips-bottom'>
      没有账号？
      <nuxt-link to='/register'>
      <span class='themeFontColor'>
          请点击注册
      </span>
      </nuxt-link>
    </p>
  </div>
</template>

<script>
export default {
  name: 'HideArticlePage',
  methods:{
    // 跳转登录
    jumpSigninFun() {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
    },
  }
}
</script>

<style scoped lang='less'>
@import "./HideArticle";
</style>
