<template>
  <PageContainer>
    <div slot='page-top' class='pageNav'>
      <luckArticleTips />
      <!--Navigation Start-->
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          :to='{ path: navPath}'>
          {{ navName }}
        </el-breadcrumb-item>
        <el-breadcrumb-item :to='{ path: navPath }'>
          正文
        </el-breadcrumb-item>
      </bm-breadcrumb>
      <!--Navigation End-->
    </div>

    <section slot='page-left' class='left-page'>
      <div class='article-container'>
        <SideNavigation
          :info-data='infoData'
          @articleCollectionHandler='articleCollectionHandler'
          @articleFabulousHandler='articleFabulousHandler'
        />
        <section>
          <ArticleContent :info-data='infoData' />
          <Comment
            :comment-list='commentListSet'
            :see-more-flag='seeMoreFlag'
            @commentLikeHandler='commentLikeHandler'
            @submitCommentsHandler='submitCommentsHandler'
            @submitReplyCommentsHandler='submitReplyCommentsHandler'
            @viewMoreDataSetHandler='viewMoreDataSetHandler'
          />
        </section>
      </div>
    </section>

    <section ref='SideBar' slot='page-right' class='right-page'>
      <AuthorColumn
        v-if='infoData.authorList && infoData.authorList.length>0'
        :author-list='infoData.authorList'
        :news-info='newsInfo' />
      <div v-if='relatedArticlesIsShow' style='margin-bottom: 20px'>
        <RelatedArticles :related-articles='relatedArticles' />
      </div>
      <RelatedRecommendations :related-articles='recommendList' />
    </section>
  </PageContainer>
</template>

<script>
import RelatedRecommendations
  from '../RelatedRecommendations/index.vue'
import PageContainer from '@/components/page/PageContainer/PageContainer'
import RelatedArticles from '@/components/page/detail/RelatedArticles/RelatedArticles'
import ArticleContent from '@/components/page/detail/ArticleContent/ArticleContent'
import Comment from '@/components/page/detail/Comment/Comment'
import SideNavigation from '@/components/page/detail/SideNavigation/SideNavigation'
import AuthorColumn from '@/components/page/detail/AuthorColumn/AuthorColumn'
import { comment, commentDiggs, diggs, getInfoCommentsPage } from '@/api/article'
import { infoCollect } from '@/api/user'
import { saveBrowsingHistory } from '@/api/browsing-history'
// 幸运中奖弹窗
import luckArticleTips from '@/components/LuckyDrawPop/luckArticleTips'

export default {
  name: 'DetailIndexPage',
  components: {
    RelatedRecommendations,
    PageContainer,
    RelatedArticles,
    Comment,
    ArticleContent,
    SideNavigation,
    AuthorColumn,
    luckArticleTips
  },
  props: {
    relatedArticlesIsShow:{
      type:Boolean,
      default:true
    },
    navName: {
      type: String,
      default: '资讯'
    },
    navPath: {
      type: String,
      default: '/info'
    },
    relatedArticles: {
      type: Array,
      default: () => []
    },
    recommendList: {
      type: Array,
      default: () => []
    },
    infoData: {
      type: Object,
      default: () => {
      }
    },
    newsInfo: {
      type: Array,
      default: () => []
    },
    isCollect: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      commentListSet: [],   // 评论列表
      seeMoreFlag: null,              // 查看更多loading开关
      commentData: {},      // 评论所有数据
      pageSize: 5              // 评论数量
    }
  },
  mounted() {
    // document.addEventListener('keydown', function(event) {
    //   return event.keyCode !== 123 || (event.returnValue = false)
    // })

    // document.addEventListener('contextmenu', function(event) {
    //   // eslint-disable-next-line no-return-assign
    //   return event.returnValue = false
    // })

    /**
     *采集浏览记录
     */
    this.$store.state.auth.user.id ? this.$axios.$request(saveBrowsingHistory({
      loginUserId: this.$store.state.auth.user.id,
      contentSource: 1,
      contentId: this.$route.query.id,
      courseId: null,
      playDuration: null
    })) : null

    /**
     * 作者公司
     * @type {string}
     */
    let company = ''
    let name = '' // 作者名字
    this.infoData.info.authorList.forEach(item => {
      company += item.company + ','
      name += item.authorName + ','
    })
    this.$analysys.view_info_page(this.infoData.title, company, [], '', '图文', [], String(this.infoData.info.id), name, 0)

    this.getCommentsListHandler()
  },
  methods: {
    /**
     * 文章点赞
     */
    articleFabulousHandler(item, callback) {
      this.$analysys.like('', '', '', '点赞原文', '', '', '', '', [], [], '资讯', '', '', String(this.$route.query.id))

      this.$axios.$request(diggs({
        articleId: this.$route.query.id,
        userId: this.$store.state.auth.user.id
      })).then((res) => {
        if (res.code === 1) {
          if (!res.result) {
            callback(true)
          } else {
            callback(false)
          }
        }
      })
    },
    /**
     * 文章收藏
     */
    articleCollectionHandler(item, callback) {
      this.$analysys.like('', '', '', '收藏原文', '', '', '', '', [], [], '资讯', '', '', String(this.$route.query.id))
      this.$axios.$request(infoCollect({
        articleId: this.$route.query.id
      })).then((res) => {
        if (res && res.code === 1) {
          callback(true)
        }
      })
    },
    /**
     * 获取评论列表
     */
    getCommentsListHandler(params = {
      pageSize: 5
    }) {
      this.$axios.$request(getInfoCommentsPage({
        infoId: this.$route.query.id,
        userId: this.$store.state.auth.user.id,
        order: 0,
        pageNo: 1,
        pageSize: params.pageSize
      })).then(response => {
        this.commentListSet = response.result.list
        const newArray = []
        this.commentListSet.forEach(item => {
          item.parent.childComments.forEach(itemChildren => {
            itemChildren.commentTime = itemChildren.creationDate
          })
          newArray.push({
            childComments: item.parent.childComments,
            commentTime: item.parent.creationDate,
            commentator: item.parent.creator,
            diggs: item.parent.diggs,
            id: item.parent.id,
            diggStatus: item.parent.isDigg === 'true' ? 'T' : 'F',
            status: item.parent.status,
            text: item.parent.text
          })
        })
        this.commentListSet = newArray
        this.commentData = response
        params.callback && params.callback(true)

        const count = this.commentData.result.page.totalCount

        if (count <= this.pageSize) {
          this.seeMoreFlag = null
        } else {
          this.seeMoreFlag = false
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/20 13:45
     *  查看更多
     */
    viewMoreDataSetHandler() {
      const count = this.commentData.result.page.totalCount
      this.seeMoreFlag = true
      if (this.pageSize + 5 < (count + 5)) {
        this.pageSize = this.pageSize + 5
        this.getCommentsListHandler({ pageSize: this.pageSize })
      }
    },
    /**
     * 评论点赞
     */
    commentLikeHandler(id) {
      this.$analysys.like('', '', '', '点赞留言', '', '', '', '', [], [], '咨询', '', '', String(id))
      this.$axios.$request(commentDiggs({
        commentId: id,
        userId: this.$store.state.auth.user.id
      })).then((response) => {
        if (!response.result) {
          this.commentListSet.forEach((item, index) => {
            if (item.id === id) {
              this.commentListSet[index].diggStatus = 'T'
              this.commentListSet[index].diggs = this.commentListSet[index].diggs + 1
            }
          })
        } else {
          this.commentListSet.forEach((item, index) => {
            if (item.id === id) {
              this.commentListSet[index].diggStatus = 'F'
              this.commentListSet[index].diggs = this.commentListSet[index].diggs - 1
            }
          })
        }
      })
    },
    /**
     * 评论回复
     */
    /**
     *  @author:Rick  @date:2022/10/20 10:02
     *  提交回复评论
     */
    submitReplyCommentsHandler({ info, parentId }, callback) {
      this.$analysys.comment('评论留言', '', '', '', [], '', '', '', '', String(this.replyId), '', '评论留言', [], String(document.title), '')
      this.$toast.loading({ message: '提交中...', forbidClick: true })

      this.$axios.$request(comment({
        text: info,
        userId: this.$store.state.auth.user.id,
        parentId,
        articleId: this.$route.query.id
      })).then((response) => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getCommentsListHandler({ callback, pageSize: this.pageSize })
          // eslint-disable-next-line node/no-callback-literal
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/20 9:43
     *  提交评论
     */
    submitCommentsHandler({ commentInfo, parentId = null }, callback) {

      if (commentInfo === '' || !commentInfo) {
        this.$toast('请输入评论内容')
        return
      }

      this.$axios.$request(comment({
        text: commentInfo,
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.query.id,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getCommentsListHandler({ callback, pageSize: this.pageSize })
        }
      })

    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
