.catalogue-container {
  background: #FBFBFB;
  border-radius: 6px;
  margin-bottom: 20px;
  max-height: 55vh;
  overflow-y: auto;

  .catalogue-button {
    height: 56px;
    background: #FBFBFB;
    padding: 0 24px 0 27px;
    box-sizing: border-box;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    cursor: pointer;

    &::after {
      display: block;
      content: "";
      position: absolute;
      width: 3px;
      height: 14px;
      background: #0581CE;
      border-radius: 6px;
      left: 14px;
      top: 50%;
      transform: translateY(-50%);
    }

    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 20px;
      color: #202020;
    }

    .arrow {
      width: 16px;
      height: 9px;
      background: url('assets/images/guidance/icon-set.png') -52px -208px;
      transition: all .3s;
    }
  }

  .catalogue-content {
    margin: 0 14px 0;
    padding-bottom: 18px;

    .line {
      height: 1px;
      padding: 0 14px;
      margin-bottom: 14px;
      background: #E2E2E2;
    }

    .titleFirst {
      //margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      h1 {
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        color: #333333;
        cursor: pointer;
        transition: all .3s;
        margin-top: 20px;
        margin-bottom: 14px;

        &:first-child {
          margin-top: 0;
        }

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .second {
      h2 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #404040;
        margin-left: 5px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .three {
      h3 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #505050;
        margin-left: 20px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .four {
      h4 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
        margin-left: 30px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .is_active {
      h1 {
        color: #0582CE;
      }

      h2 {
        color: #0582CE;
      }

      h3 {
        color: #0582CE;
      }

      h4 {
        color: #0582CE;
      }
    }
  }
}
