<template>
  <section class='downloadpdf-box'>
    <a :href='pdfUrl' class='container cursor ' target='_blank' @click='analysysFun(articleTitle)'>
      <section class='flex_start flex_align_center'>
        <div class='pdf-img' />
        <p class='word'>下载PDF</p>
      </section>
      <div class='download-pdf' />
    </a>
  </section>
</template>

<script>
export default {
  name: 'DownloadPDF',
  props: {
    pdfUrl: {
      type: String,
      required: false
    },
    articleTitle: {
      type: String,
      required: false
    }
  },
  methods: {
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
