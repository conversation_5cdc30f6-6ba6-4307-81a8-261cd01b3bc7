<template>
  <aside class='side-article-container'>
    <div class='side-article-title'>
      <p class='title'>{{ dataSet.title }}</p>
    </div>

    <div class='cover'>
      <div ref='articleSideUl' class='article-side-ul'>
        <ul v-for='(list,index) in set' :key='index' class='article-list'>
          <li
            v-for='item in list' :key='item.id'
            class='article-item cursor'>
            <nuxt-link
              :to='{path:(item.style || item.guideTypeId) ? `/guidance/detail/article/${item.id}` : `/info/detail?id=${item.id}`}'
              target='_blank'>
              <span class='text-limit-3'>
                 {{ item.title }}
              </span>
            </nuxt-link>
          </li>
        </ul>
      </div>
    </div>

    <div v-if='set.length>1' class='side-button'>
      <svg-icon
        :class-name='slideNum > 0 ? "btn_active next_btn":"next_btn"'
        icon-class='left_active' @click='slideArticleHandler("left")' />
      <svg-icon
        :class-name='slideNum < set.length-1 ? "btn_active next_btn":"next_btn"'
        icon-class='right_active'
        @click='slideArticleHandler("right")' />
    </div>

  </aside>
</template>

<script>
export default {
  name: 'SideArticle',
  props: {
    dataSet: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      slideNum: 0,     // 滑动位置
      set: []          // 文章列表
    }
  },
  fetch() {
    for (let i = 0; i < this.dataSet.infos.length; i += 5) {
      this.set.push(this.dataSet.infos.slice(i, i + 5))
    }
  },
  mounted() {
    
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/19 10:31
     *  滑动滑块
     */
    slideArticleHandler(type) {
      const box = this.$refs.articleSideUl
      switch (type) {
        case 'left': {
          if (this.slideNum > 0) {
            this.slideNum -= 1
            box.style.transform = `translateX(-${this.slideNum}00%)`
          }
          break
        }
        case 'right': {
          if (this.slideNum < this.set.length - 1) {
            this.slideNum += 1
            box.style.transform = `translateX(-${this.slideNum}00%)`
          } else {
            this.$toast('没有喽 ~')
          }
          break
        }

      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
