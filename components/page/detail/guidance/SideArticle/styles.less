.side-article-container {
  background: #FBFBFB;
  border-radius: 6px;
  margin-bottom: 20px;
  padding: 18px 0;

  .side-article-title {
    padding-left: 27px;
    position: relative;

    &::after {
      display: block;
      content: "";
      position: absolute;
      width: 3px;
      height: 14px;
      background: #0581CE;
      border-radius: 6px;
      left: 14px;
      top: 50%;
      transform: translateY(-50%);
    }

    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 20px;
      color: #202020;
    }
  }

  .cover {
    overflow: hidden;
  }

  .article-side-ul {
    display: flex;
    padding: 18px 0 0;
    transition: all 1s;
  }

  .article-list {
    width: calc(100% - 28px);
    flex-shrink: 0;
    padding: 0 14px;

    .article-item {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      padding-bottom: 8px;
      border-bottom: 1px solid #E2E2E2;
      margin-bottom: 8px;

      &:active {
        color: var(--theme-color);
      }

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
        padding-bottom: 0;
      }
    }
  }

  .side-button {
    margin-top: 12px;
    display: grid;
    grid-auto-flow: column;
    grid-gap: 0 10px;
    justify-content: center;
    align-items: center;

    .btn_active {
      cursor: pointer;
      color: #0582CE;
    }

    .next_btn {
      width: 20px;
      height: 20px;

      &:hover {
        cursor: pointer;
        color: #0582CE;
      }
    }
  }
}
