<template>
  <!--Related Start-->
  <div v-show='relatedArticles && relatedArticles.length>0' class='Related_box'>
    <div class='title'>{{ title }}</div>
    <ul class='Related-item'>
      <li
        v-for='item in relatedArticles'
        :key='item.id'
        @click='jumpDetailsPageFun(item.id)'
      >
        <div class='left'>
          <img v-if='item.smallImage' :src='$tool.compressImg(item.smallImage,142,80)' alt=''
               class='img_cover' />
          <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
        </div>
        <div class='right flex_column flex_column_justify'>
          <p class='item-title text-limit-2'>
            {{ item.title }}
          </p>
          <p class='con'>
            {{ timeStamp.timestamp_13(item.publishDate, 'y-m-d') }}
          </p>
        </div>
      </li>
    </ul>
  </div>
  <!--End-->
</template>

<script>
export default {
  name: 'RelatedArticles',
  props: {
    title: {
      type: String,
      default: '相关文章'
    },
    relatedArticles: {
      type: Array,
      default: () => []
    }
  },
  mounted() {

  },
  methods: {
    /**
     * 跳转文章详情
     * @param infoid
     */
    jumpDetailsPageFun(infoid) {
      const { href } = this.$router.resolve({ name: 'index-info-detail', query: { id: infoid } })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
