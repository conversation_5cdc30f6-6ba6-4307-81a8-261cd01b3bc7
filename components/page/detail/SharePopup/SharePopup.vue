<template>
  <div class='meeting_share_box'>
    <el-dialog
      :before-close='handleClose'
      :show-close='false'
      :visible.sync='shareFlagNew'
    >
      <svg-icon class-name='closeButton cursor' icon-class='close_button' @click='handleClose(false)' />
      <div class='share_content_box flex_between flex_align_center'>
        <div class='share_content_left flex_center flex_warp flex_align_center'>
          <p class='share_content_left_title'>微信扫一扫</p>
          <div class='shart_weixin_box'>
            <img :src='weixinUrl' alt='' />
          </div>
        </div>
        <div class='share_content_right'>
          <p class='share_content_right_title'>分享至</p>
          <div class='share_buttonList'>
            <div class='share_box flex_start'>
              <img
                alt=''
                class='cursor'
                src='~assets/images/qqkongjian.png'
                style='min-width: 38px'
                @click="shareFun('qq')"
              />
              <img
                alt=''
                class='cursor'
                src='~assets/images/weibo.png'
                style='min-width: 38px'
                @click="shareFun('weibo')"
              />
              <tooltip :content='urlcontent' class='item' effect='dark' placement='top-start'>
                <div v-clipboard:copy='urlcontent' v-clipboard:error='onError' v-clipboard:success='onCopy'
                     class='share_button flex_center flex_align_center cursor'>
                  <svg-icon class-name='lianjie_share' icon-class='lianjie' />
                  <span>复制链接</span>
                </div>
              </tooltip>
            </div>
          </div>
          <p class='share_content_right_info'>您可以直接复制短链接，分享给朋友， 也可以直接点击社交平台图标，指定分享～</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import qrcade from 'qrcode'
import { tooltip } from 'element-ui'
import { userInfo } from '@/api/user'

export default {
  name: 'SharePopup',
  components: {
    tooltip
  },
  props: {
    /**
     * shareFlag 分享弹框
     * meetingDetails 会议详情
     */
    shareFlag: {
      type: Boolean,
      default: false
    },
    shareData: {}
  },
  data() {
    return {
      shareFlagNew: this.shareFlag,
      weixinUrl: '',// 微信分享地址
      urlcontent: window.location.href// 分享地址
    }
  },
  watch: {
    shareFlag(val) {
      this.shareFlagNew = val
    }
  },
  mounted() {
    this.sharweixin()
  },
  methods: {
    onCopy(e) {
      this.$axios.$request(userInfo()).then(res => {
        if (res.code === 1) {
          this.$analysys.share(res.result.realName, '链接分享', '', '', res.result.company, document.title, '', [], [], '用户中心', '', '用户中心', res.result.title, document.title, '', '')
        }

      })
      this.$toast('内容已复制到剪切板！')
    },
    // 复制失败时的回调函数
    onError(e) {
      this.$toast.fail('抱歉，复制失败！')
    },
    // 关闭对话框
    handleClose(data) {
      this.$emit('editFlag', false)
    },
    // 分享事件
    shareFun(data) {
      this.$axios.$request(userInfo()).then(res => {
        if (res.code === 1) {
          this.$analysys.share(res.result.realName, data === 'weibo' ? '微博' : 'QQ', '', '', res.result.company, document.title, '', [], [], '用户中心', '', '用户中心', res.result.title, document.title, '', '')
        }

      })
      switch (data) {
        case 'weibo': {
          this.shareweibo()
          break
        }
        case 'qq': {
          this.shareqq()
          break
        }
      }
    },
    shareweibo() {
      //  event.preventDefault();防止链接打开 URL：
      var _shareUrl = '//service.weibo.com/share/share.php?'
      _shareUrl += 'url=' + encodeURIComponent(document.location) // 分享地址
      _shareUrl += '&title=' + encodeURIComponent(this.shareData.realName) // 分享标题
      window.open(_shareUrl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    },
    shareqq() {
      const share = {
        title: this.shareData.realName,
        desc: this.shareData.title,
        image_url: [this.shareData.avatarAddress],
        share_url: document.location // 注意 localhost 生成失败
      }
      const image_urls = share.image_url.map(function(image) {
        return encodeURIComponent(image)
      })
      // 跳转地址
      var qqurl = 'https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=' + encodeURIComponent(share.share_url) + '&title=' + share.title + '&pics=' + image_urls.join('|') + '&summary=' + share.desc
      window.open(qqurl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    },
    // 微信分享
    sharweixin() {
      const url = location.href
      qrcade.toDataURL(url).then((img) => {
        this.weixinUrl = img
      }).catch((err) => {
        console.log(err)
      })
    }

  }
}
</script>

<style lang='less' scoped>
@import "~@/components/Share/Share.less";
</style>
