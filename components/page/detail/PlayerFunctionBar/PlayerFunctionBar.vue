<template>
  <div class='player-function'>
    <div class='player-function-bar-box'>

      <div class='right-bar'>
        <div
          :class="isDiggStatus ? 'themeFontColor' : ''"
          class='fabulous flex_start cursor flex_align_center'
          @click='diggsHandler'>
          <svg-icon
            class-name='iconCollection'
            icon-class='fabulous_3'
          ></svg-icon>
          <span class='fontSize14'>{{ diggsNum > 0 ? '点赞' : '点赞' }}</span>
        </div>

        <div
          :class='isCollectionFlag?"themeFontColor" : ""'
          class='collection flex_start flex_align_center cursor'
          @click='collectionHandler'>
          <svg-icon
            class-name='yishoucang'
            icon-class='yishoucang'
          ></svg-icon>
          <span class='fontSize14'>
          收藏
        </span>
        </div>

        <div class='share flex_start flex_align_center cursor' @click='shareFlag = !shareFlag'>
          <svg-icon
            class-name='share'
            icon-class='share'
          ></svg-icon>

          <span class='fontSize14'>
          分享
        </span>
        </div>

      </div>

      <div class='pv flex_start flex_align_center'>
        <svg-icon
          class-name='watch'
          icon-class='meetingwatch_2'
        ></svg-icon>
        <span class='fontSize14'>
        {{ pv }}
      </span>
      </div>
    </div>
    <client-only>
      <SharePopup :share-data='shareData' :share-flag='shareFlag' @editFlag='closeSharePopupHandler'/>
    </client-only>
  </div>
</template>

<script>
import SharePopup from '@/components/page/detail/SharePopup/SharePopup'

export default {
  name: 'PlayerFunctionBar',
  components: {
    SharePopup
  },
  props: {
    shareData: {},
    pv: {
      type: Number,
      default: 0
    },
    diggs: {
      type: Number,
      default: 0
    },
    diggStatus: {
      type: Boolean,
      default: false
    },
    isCollection: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isCollectionFlag: this.isCollection,// 是否收藏
      isDiggStatus: this.diggStatus,
      diggsNum: this.diggs,                // 点赞数
      shareFlag: false  //  分享弹框
    }
  },
  watch: {
    isCollection(newData) {
      this.isCollectionFlag = Boolean(newData)
    },
    diggs(newData) {
      if (newData) {
        this.diggsNum = newData
      }
    }
  },
  methods: {
    closeSharePopupHandler(flag) {
      this.shareFlag = flag
    },
    collectionHandler() {
      this.$emit('collectionHandler', '', (val) => {
        if (val) {
          this.$toast('收藏成功')
          this.isCollectionFlag = true
        } else {
          this.isCollectionFlag = false
        }
      })
    },
    diggsHandler() {
      this.$emit('diggsHandler', '', (val) => {
        if (val) {
          this.isDiggStatus = true;
          this.diggsNum = this.diggsNum + 1
        } else {
          this.isDiggStatus = false;
          this.diggsNum = this.diggsNum - 1
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
