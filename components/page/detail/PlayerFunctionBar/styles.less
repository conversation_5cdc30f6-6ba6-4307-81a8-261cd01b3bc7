.player-function-bar-box {
  background: #F4F6F8;
  height: 46px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px 0 16px;
  color: #50789C;

  span {
    transition: all .3s;
    color: #50789C;
  }

  .pv {
    .watch {
      width: 14px;
      height: 14px;
      margin-right: 6px;
      color: #C7C7C7;
    }

    span {
      color: #C7C7C7;
    }
  }

  .right-bar {
    display: flex;
    justify-content: flex-end;

    .fabulous {
      width: 100px;

      .iconCollection {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .collection {
      width: 100px;

      &:hover .yishoucang {
        color: var(--theme-color);
      }

      &:hover span {
        color: var(--theme-color);
      }

      .yishoucang {
        width: 20px;
        height: 20px;
        margin-right: 7px;
        transition: all .3s;
      }
    }

    .share {
      transition: all .3s;

      &:hover .share {
        color: var(--theme-color);
      }

      &:hover span {
        color: var(--theme-color);
      }

      .share {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
  }
}
