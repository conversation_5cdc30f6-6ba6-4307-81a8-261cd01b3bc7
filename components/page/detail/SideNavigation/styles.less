.popper-box {
  text-align: center !important;
}

.side-navigation {
  background: #FBFBFB;
  border-radius: 47px;
  padding: 20px 0;
  display: flex;
  flex-flow: column;
  max-height: 492px;
  box-sizing: border-box;

  .fenxiang {
    .info {
      margin-bottom: 10px;
    }

    .con {
      display: flex;
      flex-flow: column;

      .item {
        width: 38px;
        height: 38px;
        margin-bottom: 10px;
        cursor: pointer;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .container {
    display: flex;
    flex-flow: column;
    align-items: center;
    position: relative;
    padding-bottom: 18px;
    margin-bottom: 18px;

    &::after {
      display: block;
      content: "";
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      width: 44.11%;
      height: 1px;
      background: #E5E3E3;
    }

    &:last-child::after {
      display: none;
    }

    &:last-child {
      padding-bottom: 0;
      margin-bottom: 0;
    }

    .aside-img {
      width: 28px;
      height: 28px;
      margin-bottom: 10px;
      cursor: pointer;
    }

    .comment-container-img {
      background: url('assets/images/guidance/icon-set.png') -202px -10px;
    }

    .zan-container-img {
      width: 27px;
      height: 28px;
      background: url('assets/images/guidance/icon-set.png') -202px -105px;
    }

    .shoucang-container-img {
      width: 29px;
      height: 27px;
      background: url('assets/images/guidance/icon-set.png') -202px -58px;
    }

    .info {
      font-weight: 400;
      font-size: 12px;
      color: #708AA2;

      .num {
        font-size: 16px;
        line-height: 22px;
        color: #000000;
      }
    }
  }
}
