<template>
  <aside>
    <div style='height: 115px'></div>
    <Affix offset-top='30'>
      <aside class='side-navigation'>
        <div class='container'>
          <div class='comment-container-img aside-img' @click='discussJumpFun' />
          <p class='info text-limit-1'>
            <span class='num'>{{ infoDataSet.info.comments }}</span>
            评论
          </p>
        </div>

        <div class='container'>
          <!--          <div class='zan-container-img aside-img' />-->
          <svg-icon
            :icon-class='infoDataSet.isArticleDigg?"dianzan":"placedianzan"'
            class-name='aside-img cursor'
            @click='fabulousHandler'
          ></svg-icon>
          <p class='info text-limit-1'>
            <span class='num'>{{ infoDataSet.info.showDiggs }}</span>
            点赞
          </p>
        </div>

        <div class='container'>
          <!--          <div class='shoucang-container-img aside-img' />-->
          <svg-icon
            :icon-class='infoDataSet.isArticleCollect?"collectionok":"collectiono"'
            class-name='aside-img cursor'
            @click='collectionHandler'
          ></svg-icon>
          <p class='info text-limit-1'>
            <span class='num'>{{ infoDataSet.info.showCollects }}</span>
            收藏
          </p>
        </div>

        <div class='container fenxiang'>
          <p class='info'>
            分享至
          </p>
          <ul class='con'>
            <li class='item' @click='shareWechatHandler'>
              <el-popover
                placement='right-start'
                popper-class='popper-box'
                title='扫码分享'
                trigger='click'
                width='50'
              >
                <img
                  :src='weixinUrl'
                  alt=''
                  style='width:100%; text-align: center'
                />
                <img slot='reference' alt='微信' class='img_cover' src='~assets/images/weixin.png'>
              </el-popover>
            </li>
            <li class='item' @click='shareWeiboHandler'>
              <img alt='微博' class='img_cover' src='~assets/images/weibo.png'>
            </li>
            <li class='item' @click='shareQQHandler'>
              <img alt='qq空间' class='img_cover' src='~assets/images/qqkongjian.png'>
            </li>
          </ul>
        </div>
      </aside>
    </Affix>
  </aside>

</template>

<script>
import qrcade from 'qrcode'
import Affix from '@/components/UI/Affix/Affix'

export default {
  name: 'SideNavigation',
  components: {
    Affix
  },
  props: {
    infoData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      timer: null,                  //  计时器盒子
      infoDataSet: this.infoData,   //  文章内容
      weixinUrl: ''
    }
  },
  watch: {
    infoData(newData) {
      if (newData) {
        this.infoDataSet = newData
      }
    }
  },
  methods: {
    //  跳转评论
    discussJumpFun() {
      //  document.querySelector('.participate').scrollIntoView({ behavior: 'smooth' }) //  跳到指定元素位置
      document.querySelector('#commentInput').focus() //  高亮
    },
    fabulousHandler() {
      this.$emit('articleFabulousHandler', '', (val) => {
        if (val) {
          this.infoDataSet.isArticleDigg = true
          this.infoDataSet.info.showDiggs = parseInt(this.infoDataSet.info.showDiggs) + 1
        } else {
          this.infoDataSet.isArticleDigg = false
          this.infoDataSet.info.showDiggs = parseInt(this.infoDataSet.info.showDiggs) - 1
        }
      })
    },
    collectionHandler() {
      this.$emit('articleCollectionHandler', '', (val) => {
        if (val) {
          this.infoDataSet.isArticleCollect = !this.infoDataSet.isArticleCollect
          this.infoDataSet.info.showCollects = !this.infoDataSet.isArticleCollect ?
            parseInt(this.infoDataSet.info.showCollects) - 1
            :
            parseInt(this.infoDataSet.info.showCollects) + 1
        }
      })
    },
    shareWechatHandler() {
      // eslint-disable-next-line no-undef
      const url = location.href
      qrcade.toDataURL(url).then((img) => {
        this.weixinUrl = img
      }).catch((err) => {
        console.log(err)
      })
    }
    ,
    shareWeiboHandler() {
      //  event.preventDefault();防止链接打开 URL：
      let _shareUrl = '//service.weibo.com/share/share.php?'
      _shareUrl += 'url=' + encodeURIComponent(document.location) // 分享地址
      _shareUrl += '&title=' + encodeURIComponent(document.title) // 分享标题
      window.open(_shareUrl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes')
    }
    ,
    shareQQHandler() {
      const share = {
        title: document.title,
        desc: '文章描述',
        image_url: [''],
        share_url: document.location // 注意 localhost 生成失败
      }
      // eslint-disable-next-line camelcase
      const image_urls = share.image_url.map(function(image) {
        return encodeURIComponent(image)
      })
      // 跳转地址
      const qqurl = 'https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=' + encodeURIComponent(share.share_url) + '&title=' + share.title + '&pics=' + image_urls.join('|') + '&summary=' + share.desc
      window.open(qqurl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    }
  }
}
</script>

<style lang='less'>
@import "./styles";
</style>
