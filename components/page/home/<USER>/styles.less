.secondary-navigation-box {
  display: grid;
  grid-template-columns: 40% 40% 15.5%;
  justify-content: space-between;
  margin-bottom: 16px;

  .secondary-navigation-container{
    &:first-child{
      .sub-nav-list-ol{
        &::after {
          display: none;
        }
      }
    }
  }
  .title {
    width: 100%;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 16px;
    line-height: 150%;
    color: #222222;
    padding-bottom: 8px;
    margin-bottom: 12px;
    position: relative;

    .icons {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background: #F6F6F6;
    }
  }

  .sub-nav-list-ol {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(84px, 1fr));
    grid-template-rows: repeat(auto-fill, minmax(34px, 1fr));
    grid-gap: 14px 14px;
    position: relative;


    .sub-nav-list {
      flex-shrink: 0;
      display: inline-block;
      width: 82px;
      height: 34px;
      text-align: center;
      line-height: 36px;
      background: #F9F9F9;
      border: 1px solid #F4F4F4;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;
      //margin-right: 18px;
      //margin-bottom: 14px;

      &:first-child {
        margin-left: 0;
      }

      a {
        width: 100%;
        height: 100%;
        display: block;
        color: #333333;
        font-size: 14px;
        line-height: 36px;
      }

      &:hover {
        background: #0681CE !important;
        color: #fff !important;
      }

      &:hover a {
        color: #fff !important;
      }
    }
  }
}
