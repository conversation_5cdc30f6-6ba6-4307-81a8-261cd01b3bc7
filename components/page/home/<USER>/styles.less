.selected-cases-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 0 20px;
}

.case-box {
  box-sizing: border-box;
  border-radius: 6px;
  background: #ffffffff;
  border: 1px solid #e5edf1ff;
  overflow: hidden;
  margin-bottom: 20px;
  position: relative;
  height: calc(100% - 20px);
  transition: all .3s;

  .case_box_first {
    padding: 13px;
    margin-bottom: auto;
  }

  &:hover {
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
  }

  .mixin_desktop({

  });

  .top-left {
    flex-shrink: 0;
    width: 151px;
    height: 84px;
    float: left;
    border-radius: 6px;
    overflow: hidden;
  }

  .top-right {
    float: left;
    padding-left: 10px;
    box-sizing: border-box;

    p {
      font-size: 18px;
      line-height: 26px;
      font-weight: bold;
    }
  }

  .label-box {
    margin: 14px 0 2px;
    overflow: hidden;

    li {
      display: inline-block;
      border-radius: 11px;
      background: #f0f9ffff;
      color: #0a83ceff;
      font-size: 12px;
      padding: 3px 8px;
      margin: 0px 6px 0px 0;
    }

    .lable-item-product {
      background: #f6f6f6;
      color: #0ca92e;
    }
  }

  .author-box {
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    padding: 13px;

    &::after {
      display: block;
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 1px;
      background: #e5edf1ff;
    }

    .author-left {
      .headName {
        font-size: 14px;
        color: #202020;
        margin-left: 4px;
      }

      .article_headimage {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        overflow: hidden;

        &:first-child {
          margin-left: 0;
        }

        margin-left: -11px;
      }

      .author-imgbox {
        width: 22px;
        height: 22px;
        overflow: hidden;
        border-radius: 50%;
        margin-right: 8px;
      }

      .follow {
        width: 38px;
        height: 12px;
        text-align: center;
        margin-left: 6px;
        background: #0581CE;

        &:active {
          background: #3a8ee6;
        }

        .follow-span {
          display: block;
          color: #fff;
          font-size: 10px;
          line-height: 12px;
          transform: scale(0.6);
        }
      }

      span {
        color: #333333ff;
        font-size: 14px;
        .mixin_desktop({
          font-size: 12px;
        });
      }
    }

    .author-right {
      font-size: 12px;
      color: #708aa2ff;
    }
  }
}
