.hot-meeting-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 0 20px;
}

.meeting-box {
  max-width: 100%;
  height: calc(100% - 20px);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 20px;
  background: #fbfbfbff;
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
  }

  .column-box {
    display: flex;
    flex-direction: column;
    flex: 1;

    .meet-pone {
      font-size: 18px;
      line-height: 24px;
      margin: 10px 0;
      font-weight: bold;
      padding: 0 10px;
      flex: 0 0 auto;
    }

    .meet-ptwo {
      font-size: 12px;
      color: #708aa2ff;
      margin-bottom: 14px;
      padding: 0 10px;
      margin-top: auto;

      span {
        color: #708aa2;
      }
    }
  }

  .meeting-imagxbox {
    // width: 252px;
    height: 141px;
  }
}
