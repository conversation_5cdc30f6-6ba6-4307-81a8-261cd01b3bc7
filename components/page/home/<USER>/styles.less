.second-curriculum {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 20px 20px;
  margin-top: 20px;

  .curriculum-con {
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    background: #fbfbfbff;
    height: 100%;
    display: flex;
    flex-direction: column;

    &:hover .curriculum-con-mack {
      opacity: 1;
    }

    &:hover img {
      filter: blur(2px);
    }

    &:hover .curriculum-title {
      filter: blur(2px);
    }

    &:hover .curriculum-info {
      filter: blur(2px);
    }

    &:hover .pay {
      filter: blur(2px);
    }

    .curriculum-con-mack {
      opacity: 0;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      transition: all 0.3s;
      z-index: 999;
      padding: 10px;
      color: #fff;
      box-sizing: border-box;

      h5 {
        font-size: 16px;
        margin-bottom: 6px;
      }

      .speakers-p {
        font-size: 14px;
        margin-bottom: 20px;
      }

      dl dd {
        margin-right: 6px;
        margin-bottom: 3px;
        padding: 3px 8px;
        color: #62c3ff;
        border-radius: 6px;
        background: rgba(0, 0, 0, 0.6);
        font-size: 12px;
        max-width: 100%;
      }
    }

    .curriculum-imagebox {
      width: 100%;
      height: 141px;
      overflow: hidden;
    }

    .try_watch {
      position: absolute;
      left: 5px;
      top: 5px;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      padding: 2px 7px;
      border-radius: 6px 6px 6px 6px;

      .trywatchIcon {
        width: 12px;
        height: 12px;
        margin-right: 3px;
      }
    }

    img {
      max-width: 100%;
    }

    .curriculum-title {
      font-size: 18px;
      line-height: 24px;
      margin: 10px 10px 15px;
      font-weight: bold;
    }

    .curriculum-info-box {
      padding: 0 10px 15px;
      line-height: 18px;
      margin-top: auto;
    }

    .curriculum-info {
      float: left;
      font-size: 12px;
      color: #708aa2ff;
      flex-shrink: 0;
    }

    .free {
      margin-right: 7px;
    }

    .pay {
      margin-right: 7px;
    }

    .team_box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin: 0 15px 19px;

      img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .right_info {
        .title {
          color: #0581ce;
          font-size: 13px;
          margin-bottom: 4px;
        }

        .info {
          color: #708aa2;
          font-size: 12px;
        }
      }
    }
  }
}
