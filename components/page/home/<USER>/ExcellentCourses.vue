<template>
  <ul class='second-curriculum'>
    <li
      v-for='item in curriculum'
      :key='item.id'
    >
      <div class='curriculum-con cursor' @click='jumpCurriculumFun(item.id)'>
        <div class='curriculum-con-mack'>
          <h5 class='text-limit-3'>{{ item.name }}</h5>
          <p
            v-for='speakers in item.speakers'
            :key='speakers.id'
            class='speakers-p'
          >
            讲者:{{ speakers.name }}
          </p>
          <dl class='flex_start flex_warp'>
            <dd
              v-for='subspecialties in item.subspecialties'
              :key='subspecialties.id'
            >
              {{ subspecialties.parentName
              }}{{ subspecialties.parentName ? '-' : ''
              }}{{ subspecialties.name }}
            </dd>
          </dl>
        </div>
        <div v-if='item.money' class='try_watch flex_start flex_align_center cursor'>
          <svg-icon
            class-name='trywatchIcon cursor img_radius'
            icon-class='trywatch'
          ></svg-icon>
          <span class='fontSize12'>试学</span>
        </div>
        <div class='curriculum-imagebox'>
          <img :src='$tool.compressImg(item.cover,252,141)' alt='云课堂' class='img_cover' />
        </div>
        <p class='curriculum-title text-limit-2'>{{ item.name }}</p>
        <div v-if='false' class='team_box'>
          <img :src='item.speakers.avatar' alt='云课堂' class='img_cover' />
          <div class='right_info'>
            <p class='title'>朱XX教授团队</p>
            <p class='info'>item.speakers.unit</p>
          </div>
        </div>
        <div class='flex_between curriculum-info-box'>
          <p class='curriculum-info'>
                      <span style='margin-right: 10px'>{{ item.showViews }}次播放</span
                      ><span>{{ item.score }}分</span>
          </p>
          <p v-if='false' class='free'>
                      <span
                        class='fontWeight'
                        style='text-align: right; font-size: 16px; color: #0581ceff'
                      >免费</span
                      >
          </p>
          <p class='pay' style='color: #f35234'>
                      <span style='font-size: 14px'>
                        ￥
                        <span
                          style='text-align: right; font-size: 18px; font-weight: 400'
                        >{{ item.money }}</span
                        >
                      </span>
          </p>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'ExcellentCourses',
  props: {
    curriculum: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 跳转云课堂
    jumpCurriculumFun(id) {
      this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: id } })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
