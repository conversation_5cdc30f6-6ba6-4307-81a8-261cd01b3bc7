<template>
  <ul class='selected-cases-container'>
    <li
      v-for='(item, index) in caseInfoDataListNew'
      :key='index'
    >
      <!--              UGC-->
      <div
        v-if="item.type === 'U'" class='case-box cursor flex_column'
        @click='jumpArticleFun(item.article.id,item.type,"UGC")'>
        <div class='case_box_first'>
          <div class='flex_start' style='overflow: hidden'>
            <div class='top-left'>
              <img
                v-if='item.article.cover'
                :alt='item.article.title'
                :src='$tool.compressImg(item.article.cover,151,84)'
                class='img_cover'
              />
              <img
                v-else
                :alt='item.article.title'
                class='img_cover'
                src='~assets/images/default16.png'
              />
            </div>
            <div class='top-right'>
              <p class='fontWeight text-limit-3'>
                {{ item.article.title }}
              </p>
            </div>
          </div>
          <ul class='label-box text-limit-1'>
            <li
              v-for='itemCase in item.article.subspecialtys.length > 0
                          ? item.article.subspecialtys[0].children
                          : item.article.subspecialtys'
              :key='itemCase.id'
            >
              {{ item.article.subspecialtys[0].name + '-' + itemCase.name }}
            </li>
            <!--                 ugC产品标签 -->
            <li
              v-for='product in item.article.productList'
              id='productItem'
              :key='product.id'
              class='lable-item-product lable-item flex_start flex_align_center flex_warp'
            >
              <svg-icon class-name='productIcon' icon-class='product2'></svg-icon>
              {{ product.name }}
            </li>
          </ul>
        </div>
        <div class='author-box flex_between flex_align_center'>
          <div class='author-left flex_start flex_align_center'>
            <div class='author-imgbox'>
              <img
                v-if='
                            item.article.creator
                              ? item.article.creator.avatarAddress
                              : null
                          '
                :src='
                            item.article.creator
                              ? $tool.compressImg(item.article.creator.avatarAddress,22,22)
                              : null
                          '
                alt='ugc'
                class='img_cover'
              />
              <svg-icon
                v-else
                class-name='img_cover'
                icon-class='signinavatar'
              />
            </div>
            <span class='fontWeight'>{{
                item.article.creator ? item.article.creator.realName : null
              }}</span>
            <div
              v-if='item.article.creator.id !== $store.state.auth.user.id'
              :class='{"themeFollowed":item.article.creator.isFollow}'
              class='follow themeBorderRadius'
              @click.stop='followFun(item.article.creator.id)'>
              <span class='follow-span'>{{ item.article.creator.isFollow ? '已关注' : '+ 关注' }}</span>
            </div>
          </div>
          <div class='author-right'>
                      <span style='margin-right: 10px'
                      >{{ item.article.showViews }}阅读</span
                      >
            <span>{{
                timeStamp.timestamp_13(item.article.publishTime, 'yyyy-mm-d')
              }}</span>
          </div>
        </div>
      </div>
      <!--              PGC-->
      <div
        v-if="item.type === 'P'" class='case-box cursor flex_column'
        @click='jumpArticleFun(item.info.id,item.type,"PGC")'>
        <div class='case_box_first'>
          <div class='flex_start' style='overflow: hidden'>
            <div class='top-left'>
              <img
                v-if='item.info.infoImg'
                :alt='item.info.title'
                :src='$tool.compressImg(item.info.infoImg,151,84)'
                class='img_cover'
              />
              <img
                v-else
                :alt='item.info.title'
                class='img_cover'
                src='~assets/images/default16.png'
              />
            </div>
            <div class='top-right'>
              <p class='fontWeight text-limit-3'>
                {{ item.info.title }}
              </p>
            </div>
          </div>
          <ul class='label-box text-limit-1'>
            <li
              v-for='itemCase in item.info.subspecialtys.length > 0
                          ? item.info.subspecialtys[0].children
                          : item.info.subspecialtys'
              :key='itemCase.id'
            >
              {{ item.info.subspecialtys[0].name + '-' + itemCase.name }}
            </li>
            <!--                 ugC产品标签 -->
            <li
              v-for='product in item.info.productList'
              id='productItem'
              :key='product.id'
              class='lable-item-product flex_start flex_align_center flex_warp'
            >
              <svg-icon class-name='productIcon' icon-class='product2'></svg-icon>
              {{ product.name }}
            </li>
          </ul>
        </div>
        <div class='author-box'>
          <div class='flex_between flex_align_center'>
            <div class='author-left flex_start flex_align_center'>
              <div
                v-for='(authorList, index) in item.info.authorList'
                v-show='index < 3'
                :key='index'
                class='article_headimage flex_start'
              >
                <img
                  v-if='authorList.headImage'
                  :key='authorList.id'
                  :src='$tool.compressImg(authorList.headImage,22,22)'
                  alt='ugc'
                  class='img_cover img_radius flex_shrink'
                />
                <svg-icon
                  v-else
                  class-name='img_cover'
                  icon-class='signinavatar'
                />
              </div>
              <div class='headName text-limit-1 fontWeight'>
                {{
                  item.info.authorList.length > 1
                    ? `${item.info.authorList[0].authorName}等${item.info.authorList.length}位作者`
                    : `${
                      item.info.authorList.length === 1
                        ? item.info.authorList[0].authorName
                        : ''
                    }`
                }}
              </div>
            </div>
            <div class='author-right flex_shrink'>
                        <span style='margin-right: 10px'
                        >{{ item.info.showViews }}阅读</span
                        >
              <span>{{
                  timeStamp.timestamp_13(item.info.publishTime, 'yyyy-mm-d')
                }}</span>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
import { selectedArticleList } from '@/api/home'

export default {
  name: 'SelectedCases',
  props: {
    caseInfoDataList: {
      type: Array,
      default: () => []
    },
    subSpecialtyDataListCurrent: {}
  },
  data() {
    return {
      caseInfoDataListNew: this.caseInfoDataList
    }
  },
  watch: {
    caseInfoDataList(newValue) {
      if (newValue) {
        this.caseInfoDataListNew = newValue
      }
    }
  },
  methods: {
    /**
     * 跳转文章
     * @param url
     */
    jumpArticleFun(id, type, articleType) {
      switch (articleType) {
        case 'PGC' : {
          const routeUrl = this.$router.resolve({
            name: 'index-case-detail-pgc',
            query: { id }
          })
          window.open(routeUrl.href, '_blank')
          break
        }
        case 'UGC': {
          const routeUrl = this.$router.resolve({
            name: 'index-case-detail-ugc',
            query: { id }
          })
          window.open(routeUrl.href, '_blank')
          break
        }
      }
    },
    /**

     **@desc: 关注作者

     **@date:2022/6/22 17:30

     **@author:Rick

     **@method:

     **@params:

     */
    async followFun(id) {
      await this.$store.dispatch('follow', id)
      await this.$axios.$request(selectedArticleList({
        specilityId: this.subSpecialtyDataListCurrent,
        pageNo: 1,
        pageSize: this.$store.state.case_count,
        userId: this.$store.state.auth.user.id
      })).then((res) => {
        if (res && res.code === 1) {
          this.caseInfoDataListNew = res.list
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
