<template>
  <section class='secondary-navigation-box'>
    <template v-for='(itemNav,index) in subNavListSet'>
      <div
        v-if='itemNav.isEnable'
        :key='index'
        class='secondary-navigation-container'>
        <div class='title'>
          <svg-icon :icon-class='itemNav.titleIcon' class-name='icons' />
          <span>{{ itemNav.title }}</span>
        </div>
        <div v-show='itemNav.isEnable' class='sub-nav-list-ol'>
          <div
            v-for='(itemList,indexList) in itemNav.list'
            v-show='itemList.isEnable'
            :key='indexList'
            class='sub-nav-list'
            @click='analysysFun(itemList.subName)'>
            <nuxt-link :to='itemList.subUrl' target='_self'>
              {{ itemList.subName }}
            </nuxt-link>
          </div>
        </div>
      </div>
    </template>
  </section>
</template>

<script>
export default {
  name: 'SecondaryNavigation',
  data() {
    return {
      subNavListSet: [
        {
          isEnable: true,
          title: '亚专业',
          titleIcon: 'sec_one',
          list: this.$store.state.channelList
        },
        {
          isEnable: true,
          title: '学术资源',
          titleIcon: 'sec_two',
          list: [
            { subName: '文章专栏', subUrl: '/column', isEnable: true },
            { subName: '指南共识', subUrl: '/guidance', isEnable: true },
            { subName: '精选编译 ', subUrl: '/compile', isEnable: true },
            { subName: '科室主页', subUrl: '/department/home', isEnable: true },
            { subName: '招聘培训', subUrl: '/', isEnable: false },
            { subName: '评分工具', subUrl: '/score', isEnable: true },
            { subName: '文献速览', subUrl: '/literature/home', isEnable: true },
            { subName: '百家关注', subUrl: '/', isEnable: false },
            { subName: '招聘培训', subUrl: '/recruitment-training', isEnable: true },
            { subName: '藏书阁', subUrl: '/mall', isEnable: true },
            { subName: '话题圈子', subUrl: '/topic-circle', isEnable: true },
            { subName: '临床招募', subUrl: '/clinical', isEnable: true },
          ]
        },
        {
          isEnable: true,
          title: '医药器械',
          titleIcon: 'sec_three',
          list: []
        }
      ]
    }
  },
  methods: {
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
