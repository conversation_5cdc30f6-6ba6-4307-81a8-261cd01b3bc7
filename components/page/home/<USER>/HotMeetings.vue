<template>
  <div
    class='hot-meeting-container'
  >
    <div
      v-for='meet in hotMeetingDataList'
      :key='meet.id'
      class='hot-meeting-box'
    >
      <nuxt-link
        :to='{ path: `/meeting/detail`,query:{id:meet.id} }'
        target='_blank'
      >
        <div class='meeting-box'>
          <div class='meeting-imagxbox overflow_hidden'>
            <img
              v-if='meet.appMainPic || meet.playerCover || meet.titlePic'
              :alt='meet.meetingName'
              :src='
                        meet.appMainPic
                          ? $tool.compressImg(meet.appMainPic,254,141)
                          : meet.playerCover
                          ? $tool.compressImg(meet.playerCover,254,141)
                          : meet.titlePic
                          ? $tool.compressImg(meet.titlePic,254,141)
                          : null
                      '
              class='img_cover'
            />
            <img v-else :alt='meet.meetingName' class='img_cover' src='~assets/images/default16.png' />
          </div>
          <div class='column-box'>
            <p class='meet-pone fontWeight text-limit-2'>
              {{ meet.meetingName }}
            </p>
            <p class='meet-ptwo text-limit-1'>
              <a class='flex_between'>
                        <span class='time fontSize12 flex_shrink'>{{
                            meet.meetingDateStr
                          }}</span>
                <span class='userName fontSize12 text-limit-1'
                >{{ meet.province }}-{{ meet.city }}</span
                >
              </a>
            </p>
          </div>
          <live-state :state='meet.meetingLiveStatus'></live-state>
        </div>
      </nuxt-link>
    </div>
  </div>
</template>

<script>
import LiveState from '@/components/LiveState/LiveState'

export default {
  name: 'HotMeetings',
  components: {
    LiveState
  },
  props: {
    hotMeetingDataList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
