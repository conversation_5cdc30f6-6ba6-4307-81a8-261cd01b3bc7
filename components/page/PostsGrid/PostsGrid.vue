<template>
  <section class='posts-box'>
    <Empty :loading='loading' :no-more='!loading && guideDataSet.length===0' height='150px' />
    <ul class='posts-list'>
      <li v-for='(item,index) in guideDataSet' :key='index'>
        <PostArticleItem
          v-if='item.type === "info"'
          :id='item.info.infoId'
          :author-names='item.info.authorNames'
          :description='item.info.metaDescription'
          :guide-type-list='item.info.guideTypeList'
          :info-img='item.info.infoImg'
          :publish-date='item.info.publishDate+""'
          :title='item.info.infoTitle'
        />
        <PostMpArticleItem
          v-if='item.type === "mp_article"'
          :id='item.mp_article.id'
          :creator='item.mp_article.creator'
          :description='item.mp_article.description'
          :guide-type-list='item.mp_article.guideTypeList'
          :info-img='item.mp_article.cover'
          :publish-date='item.mp_article.publishTime+""'
          :title='item.mp_article.title'
        />

        <PostMeetingItem
          v-if='item.type === "meeting"'
          :id='item.meeting.id'
          :city='item.meeting.city'
          :guide-type-list='item.meeting.guideTypeList'
          :img-url='item.meeting.appMainPic || item.meeting.playerCover || item.meeting.titlePic'
          :meeting-date-str='item.meeting.meetingDateStr+""'
          :meeting-live-status='item.meeting.meetingLiveStatus'
          :province='item.meeting.province'
          :title='item.meeting.meetingName'
        />


        <PostMeetingFieldsItem
          v-if='item.type === "meetingFields"'
          :id='item.meetingFields.id'
          :guide-type-list='item.meetingFields.guideTypeList'
          :img-url='item.meetingFields.fieldsCover'
          :meeting-date-str='item.meetingFields.startTime+""'
          :meeting-i-d='item.meetingFields.meetingId'
          :meeting-live-status='item.meetingFields.fieldsStatus'
          :title='item.meetingFields.subject'


        />

        <PostVideoItem
          v-if='item.type === "interpreting_video"'
          :id='item.interpreting_video.id'
          :author-names='item.interpreting_video.authors'
          :guide-type-list='item.interpreting_video.guideTypeList'
          :publish-date='item.interpreting_video.publishTime'
          :title='item.interpreting_video.videoName'
          :video-img='item.interpreting_video.cover'
        />
      </li>
    </ul>
  </section>
</template>

<script>
import PostArticleItem from '@/components/page/PostsGrid/PostArticleItem/PostArticleItem'
import PostVideoItem from '@/components/page/PostsGrid/PostVideoItem/PostVideoItem'
import PostMeetingItem from '@/components/page/PostsGrid/PostMeetingItem/PostMeetingItem'
import PostMpArticleItem from '@/components/page/PostsGrid/PostMpArticleItem/PostMpArticleItem'
import PostMeetingFieldsItem from '@/components/page/PostsGrid/PostMeetingFieldsItem/PostMeetingFieldsItem'
import Empty from '@/components/UI/Empty/Empty'

export default {
  name: 'PostsGrid',
  components: {
    PostArticleItem,
    PostVideoItem,
    PostMeetingItem,
    PostMpArticleItem,
    PostMeetingFieldsItem,
    Empty
  },
  props: {
    loading: {
      type: Boolean,
      required: false
    },
    guideDataSet: {
      type: Array,
      required: true
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
