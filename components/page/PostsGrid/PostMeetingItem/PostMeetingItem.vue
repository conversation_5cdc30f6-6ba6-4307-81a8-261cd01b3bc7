<template>
  <div class='post-item-box'>
    <nuxt-link :to='{path:`/meeting/detail?id=${id}`}' class='post-item' target='_blank'>
      <div class='img-box'>
        <live-state :state='meetingLiveStatus'></live-state>
        <!--        <div v-if='meetingLiveStatus === "LE"' class='video-icon' />-->
        <img v-if='imgUrl' :src='$tool.compressImg(imgUrl,220,124)' alt='会议图' class='img_cover'>
        <img v-else alt='文章默认图' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='info-content'>
        <p class='title text-limit-2'>
          {{ title }}
        </p>
        <p class='tow_title'>
          <span class='address'>
          {{ province }} {{ province && city ? '-' : '' }} {{ city }}
          </span>
          <span class='time'>
          {{ meetingDateStr }}
        </span>
        </p>
        <div class='guideTypeList-box'>
          <ul class='type-list'>
            <li v-for='item in guideTypeList' :key='item.id' class='type-item flex_start flex_align_center'>
              <SvgIcon class-name='type_icon' icon-class='guide_type' />
              <span> {{ item.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </nuxt-link>
  </div>

</template>

<script>
import LiveState from '@/components/LiveState/LiveState'

export default {
  name: 'PostMeetingItem',
  components: {
    LiveState
  },
  props: {
    id: {
      type: Number,
      required: true
    },
    guideTypeList: {
      type: Array,
      required: true
    },
    imgUrl: {
      type: String,
      default: '',
      required: false
    },
    title: {
      type: String,
      default: '文章标题',
      required: true
    },
    meetingDateStr: {
      type: String,
      default: '时间',
      required: false
    },
    province: {
      type: String,
      default: '省份',
      required: false
    },
    city: {
      type: String,
      default: '城市',
      required: false
    },
    meetingLiveStatus: {
      type: String,
      default: 'NL',
      required: false
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
