.post-item {
  display: grid;
  grid-template-columns:220px calc(100% - 220px);
  grid-gap: 0 10px;
  cursor: pointer;
  transition: all .3s;
  padding: 10px;
  border-radius: 6px;

  &:hover {
    box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
  }

  .img-box {
    border-radius: 6px;
    overflow: hidden;
    height: 124.3px;
  }

  .info-content {
    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 22px;
      margin-bottom: 15px;
      color: #202020;
    }

    .time-or-author {
      font-weight: 400;
      font-size: 12px;
      line-height: 12px;
      color: #708AA2;
      margin-bottom: 8px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .time {
        margin-right: 10px;

        i {
          font-size: 12px;
        }
      }

      .author {
        .svg {
          width: 12px;
          height: 12px;
        }
      }
    }

    .guideTypeList-box {
      margin-bottom: 12px;

      .type-list {
        margin-top: 8px;

        .type-item {
          font-size: 12px;
          line-height: 12px;
          color: #708AA2;

          .type_icon {
            width: 12px;
            height: 12px;
            margin-right: 3.5px;
          }
        }
      }
    }

    .description {
      font-size: 12px;
      line-height: 20px;
      color: #708AA2;
    }
  }
}
