<template>
  <div class='post-item-box'>
    <nuxt-link :to='{path:`/info/detail?id=${id}`}' class='post-item' target='_blank'>
      <div class='img-box'>
        <img v-if='infoImg' :src='$tool.compressImg(infoImg,220,124)' alt='指南文章图' class='img_cover'>
        <img v-else alt='文章默认图' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='info-content'>
        <p class='title text-limit-1'>
          {{ title }}
        </p>
        <p class='time-or-author'>
        <span class='time'>
          {{ timeStamp.timestampFormat(publishDate / 1000) }}
        </span>
          <span class='author'>
          {{ authorNames }}
        </span>
        </p>
        <div class='guideTypeList-box'>
          <ul class='type-list'>
            <li v-for='item in guideTypeList' :key='item.id' class='type-item flex_start flex_align_center'>
              <SvgIcon class-name='type_icon' icon-class='guide_type'/>
              <span> {{ item.name }}</span>
            </li>
          </ul>
        </div>
        <p class='description text-limit-2'>
          {{ description }}
        </p>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
export default {
  name: 'PostArticleItem',
  props: {
    id: {
      type: Number,
      required: true
    },
    infoImg: {
      type: String,
      default: '',
      required: false
    },
    guideTypeList: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: '文章标题',
      required: true
    },
    publishDate: {
      type: String,
      default: '时间',
      required: false
    },
    authorNames: {
      type: String,
      default: '作者名字'
    },
    description: {
      type: String,
      default: '文章描述'
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
