<template>
  <div class='post-item-box'>
    <nuxt-link :to='{path:`/case/detail-ugc?id=${id}`}' class='post-item' target='_blank'>
      <div class='img-box'>
        <img v-if='infoImg' :src='$tool.compressImg(infoImg,220,124)' alt='指南文章图' class='img_cover'>
        <img v-else alt='文章默认图' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='info-content'>
        <div class='top'>
          <p class='title text-limit-2'>
            {{ title }}
          </p>
          <div class='guideTypeList-box'>
            <ul class='type-list'>
              <li v-for='item in guideTypeList' :key='item.id' class='type-item flex_start flex_align_center'>
                <SvgIcon class-name='type_icon' icon-class='guide_type' />
                <span> {{ item.name }}</span>
              </li>
            </ul>
          </div>
        </div>

        <div class='time-or-author'>
          <div class='author flex_start flex_align_center'>
            <div class='img-box'>
              <img v-if='creator.avatarAddress' :src='$tool.compressImg(creator.avatarAddress,16,16)' alt='author头像'
                   class='img_cover'>
              <svg-icon
                v-else
                class-name='img-box'
                icon-class='signinavatar'
              />
            </div>
            <span>
               {{ creator.realName }}
            </span>
          </div>
          <span class='time'>
             {{ timeStamp.timestampFormat(publishDate / 1000) }}
          </span>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
export default {
  name: 'PostMpArticleItem',
  props: {
    id: {
      type: Number,
      required: true
    },
    infoImg: {
      type: String,
      default: '',
      required: false
    },
    creator: {},
    guideTypeList: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: '文章标题',
      required: true
    },
    publishDate: {
      type: String,
      default: '时间',
      required: false
    },
    description: {
      type: String,
      default: '文章描述'
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
