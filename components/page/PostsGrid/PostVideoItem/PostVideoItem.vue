<template>
  <div class='post-item-box'>
    <nuxt-link :to='{path:`/guidance/detail/video/${id}`}' class='post-item' target='_blank'>
      <div class='img-box'>
        <div class='video-icon' />
        <img v-if='videoImg' :src='$tool.compressImg(videoImg,220,124)' alt='指南文章图' class='img_cover'>
        <img v-else alt='文章默认图' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='info-content'>
        <p class='title text-limit-2'>
          {{ title }}
        </p>
        <p class='tow_title'>
          <span class='time'>
            {{ timeStamp.timestampFormat(publishDate / 1000) }}
          </span>
          <span class='address'>
            {{ authorNames }}
          </span>
        </p>
        <div class='guideTypeList-box'>
          <ul class='type-list'>
            <li v-for='item in guideTypeList' :key='item.id' class='type-item'>
              <SvgIcon class-name='type_icon' icon-class='guide_type' />
              <span> {{ item.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
export default {
  name: 'PostVideoItem',
  props: {
    id: {
      type: Number,
      required: false
    },
    videoImg: {
      type: String,
      default: '',
      required: false
    },
    guideTypeList: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: '文章标题',
      required: true
    },
    publishDate: {
      type: Number,
      required: false
    },
    authorNames: {
      type: String,
      default: '作者名字'
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
