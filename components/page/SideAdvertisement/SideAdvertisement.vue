<template>
  <div :class="(data.url || data.extras) ? 'cursor' : ''" class='advertisement-box' @click='jumpBannerFun(data)'>
    <img v-if='data.image' :src='$tool.compressImg(data.image,387,132)' alt='侧边导航' class='img_cover'>
    <img v-else alt='侧边导航' class='img_cover' src='~assets/images/live_z.png'>
  </div>
</template>

<script>
import brandAdJump from "../../../assets/helpers/brandAdJump";

export default {
  name: 'SideAdvertisement',
  props: {
    data: {
      default: {}
    },
  },
  methods: {
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId : item.adId,
        adUrl : item.extras,
        adModule : item.module,
        unionid : this.$store.state.auth.unionid,
        adClickLocation : item.clickLocation,
        adCode : item.code,
        adExtras : item.extras,
        adName : item.name
      })

      brandAdJump(this, item.module, item.extras)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
