.comment_item_wrapper {
  display: flex;

  .user_image {
    flex-shrink: 0;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 7px;

    .author__img {
      width: 42px;
      height: 42px;
    }
  }

  .comment_wrapper {
    flex: auto;
    //flex-shrink: 0;

    .user_name {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      margin: 4px 0 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .top {
        flex-shrink: 0;
        display: inline-block;
        background: #F23C17;
        border-radius: 2px;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #FFFFFF;
        padding: 2px 5px 3px;
      }
    }

    .user_comment_content {
      font-size: 14px;
      line-height: 20px;
      color: #FFFFFF;
      margin-bottom: 13px;
    }

    .user_comment_edit {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 400;
      font-size: 12px;
      line-height: 12px;
      color: #999999;

      .right {
        display: flex;
        align-items: center;

        .praise {
          color: #666666;

          .icons {
            cursor: pointer;
            margin-right: 1px;
            transition: all .3s;
          }
        }

        .comment {
          cursor: pointer;
          margin-left: 16px;
        }
      }
    }

    .sub_item_wrapper {
      margin-top: 16px;
      display: flex;
      flex-flow: column;
      grid-gap: 16px 0;

      .isShow_tips {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #FFFFFF;
        padding-left: calc(26px + 9px);
        cursor: pointer;

        .icons {
          width: 8px;
          height: 8px;
          margin-left: 5px;
          transition: all .3s;
        }
      }
    }
  }
}
