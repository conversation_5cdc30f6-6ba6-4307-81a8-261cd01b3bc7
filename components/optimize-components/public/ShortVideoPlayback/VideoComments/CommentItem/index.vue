<template>
  <div class='comment_item_wrapper'>
    <div class='user_image'>
      <img v-if='avatarAddress' class='img_cover' :src='$tool.compressImg(avatar<PERSON>dd<PERSON>,42,42)' alt=''>
      <svg-icon v-else class-name='author__img' icon-class='signinavatar' />
    </div>
    <div class='comment_wrapper'>
      <div class='user_name'>
        <span class='text-limit-1'>{{ realName }}</span>
        <span v-if='stick === "T"' class='top'>
          <span>置顶</span>
        </span>
      </div>
      <p class='user_comment_content'>{{ content }}</p>
      <div class='user_comment_edit'>
        <div class='time'>{{ timeStamp.timestamp_13(commentTime, 'y-m-d-/') }}</div>
        <div class='right'>
          <div class='praise' @click='diggHandler'>
            <svg-icon v-if='isDiggFlag === "T"' class-name='icons' icon-class='praise__' />
            <svg-icon v-else class-name='icons' icon-class='praise_' />
            <span ref='diggsNum'>{{ diggs }}</span>
          </div>
          <div class='comment' @click='replyHandler'>
            <svg-icon class-name='icons' icon-class='vide_comment' />
          </div>
        </div>
      </div>
      <div v-if='isShowInput' style='margin-top: 12px'>
        <CommentInput
          :id='id'
          :is-reply='true'
          @cancel='replyHandler'
          @submitHandler='submitHandler'
        />
      </div>
      <div class='sub_item_wrapper'>
        <SubItem
          v-for='(item,index) in childCommentsList'
          v-show='index < isShowComment'
          :id='item.id'
          :key='item.id'
          :content='item.text'
          :comment-time='item.commentTime'
          :diggs='item.diggs'
          :real-name='item.child'
          :parent-name='item.parent'
          :avatar-address='item.avatarAddress'
          :is-digg='item.isDigg'
          v-on='$listeners'
          @submitHandler='submitChildrenHandler'
        />
        <div
          v-if='childCommentsList.length > 3'
          class='isShow_tips'
          @click='isShowComment === 3 ? isShowComment = 999999 : isShowComment = 3'>
          <span v-if='isShowComment === 3'>展开{{ childCommentsList.length - 3 }}条回复</span>
          <span v-else>收回</span>
          <svg-icon
            :style='isShowComment === 999999 ? {transform:"rotateZ(180deg)"} : {transform:"rotateZ(0deg)"}'
            class-name='icons' icon-class='zhankai_' />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CommentInput from '../CommentInput/index.vue'
import SubItem from './SubItem/index.vue'

export default {
  name: 'CommentItem',
  components: { SubItem, CommentInput },
  props: {
    id: {},
    realName: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    commentTime: {},
    diggs: {},
    avatarAddress: {},
    childComments: {
      type: Array,
      default: () => []
    },
    stick: {},
    isDigg: {}

  },
  watch: {
    isDigg(val) {
      this.isDiggFlag = val
    },
    childComments(val) {
      this.childCommentsList = val
    }
  },
  data() {
    return {
      childCommentsList: this.childComments,
      isShowComment: 3,
      isShowInput: false,
      isDiggFlag: this.isDigg
    }
  },
  methods: {
    submitHandler(msg, backFn) {
      this.$emit('saveVideoComment', msg, val => {
        const newArr = {
          id: val.id,
          child: val.creator.realName,
          diggs: val.diggs,
          commentTime: val.createTime,
          avatarAddress: val.creator.avatarAddress,
          childUserId: val.creator.id,
          parentCommentId: val.parent.id,
          parentUserId: val.parent.creator.id,
          parent: val.parent.creator.realName,
          text: val.content,
          isDigg: 'F'
        }
        this.childCommentsList.unshift(newArr)
        backFn(true)
      })
    },
    submitChildrenHandler(val, backFn) {
      const newArr = {
        id: val.id,
        child: val.creator.realName,
        diggs: val.diggs,
        commentTime: val.createTime,
        avatarAddress: val.creator.avatarAddress,
        childUserId: val.creator.id,
        parentCommentId: val.parent.id,
        parentUserId: val.parent.creator.id,
        parent: val.parent.creator.realName,
        text: val.content,
        isDigg: 'F'
      }
      this.childCommentsList.unshift(newArr)
      backFn(true)
    },
    replyHandler() {
      this.isShowInput = !this.isShowInput
    },
    diggHandler() {
      if (this.isDiggFlag !== 'T') {
        this.isDiggFlag = 'T'
        this.$refs.diggsNum.innerText = Number(this.$refs.diggsNum.innerText) + 1
      } else {
        this.isDiggFlag = 'F'
        this.$refs.diggsNum.innerText = this.diggs > 0 ? this.diggs - 1 : this.diggs
      }

      this.$emit('commentDigg', { commentId: this.id }, val => {
      })
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
