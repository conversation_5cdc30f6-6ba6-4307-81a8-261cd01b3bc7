<template>
  <div class='comment_item_wrapper'>
    <div class='user_image'>
      <img v-if='avatarAddress' class='img_cover' :src='$tool.compressImg(avatarAddress,26,26)' alt=''>
      <svg-icon v-else class-name='author__img' icon-class='signinavatar' />
    </div>
    <div class='comment_wrapper'>
      <div class='user_name'>{{ realName }}</div>
      <p class='user_comment_content'>
        <span style='margin-right: 4px'>回复</span>
        <span style='margin-right: 4px'>{{ parentName }}：</span>
        <span class='content'>
          {{ content }}
        </span>
      </p>
      <div class='user_comment_edit'>
        <div class='time'>{{ timeStamp.timestamp_13(commentTime, 'y-m-d-/') }}</div>
        <div class='right'>
          <div @click='diggHandler' class='praise'>
            <svg-icon v-if='isDiggFlag === "T"' class-name='icons' icon-class='praise__' />
            <svg-icon v-else class-name='icons' icon-class='praise_' />
            <span ref='diggsNum'>{{ diggs }}</span>
          </div>
          <div class='comment' @click='replyHandler'>
            <svg-icon class-name='icons' icon-class='vide_comment' />
          </div>
        </div>
      </div>
      <div v-if='isShowInput' style='margin-top: 12px'>
        <CommentInput
          :id='id'
          :is-reply='true'
          @cancel='replyHandler'
          @submitHandler='submitHandler'
        />
      </div>
    </div>
  </div>
</template>

<script>
import CommentInput from '../../CommentInput/index.vue'

export default {
  name: 'SubItem',
  props: {
    id: {},
    realName: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    commentTime: {},
    diggs: {},
    avatarAddress: {},
    parentName: {},
    isDigg: {}
  },
  components: { CommentInput },
  data() {
    return {
      isDiggFlag: this.isDigg,
      isShowInput: false
    }
  },
  methods: {
    submitHandler(msg, backFn) {
      this.$emit('saveVideoComment', msg, val => {
        if (val) {
          this.$emit('submitHandler', val, backFnC => {
            if (backFnC) {
              backFn(true)
            }
          })
        }
      })
    },
    replyHandler() {
      this.isShowInput = !this.isShowInput
    },
    diggHandler() {
      if (this.isDiggFlag !== 'T') {
        this.isDiggFlag = 'T'
        this.$refs.diggsNum.innerText = Number(this.$refs.diggsNum.innerText) + 1
      } else {
        this.isDiggFlag = 'F'
        this.$refs.diggsNum.innerText = this.diggs > 0 ? this.diggs - 1 : this.diggs
      }

      this.$emit('commentDigg', { commentId: this.id }, val => {
      })
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
