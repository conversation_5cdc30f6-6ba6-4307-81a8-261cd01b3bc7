<template>
  <div class='input_wrapper' :class='isReply ? "reply_wrapper" : ""'>
    <el-input
      v-model.trim='content'
      type='textarea'
      :rows='2'
      resize='none'
      placeholder='说点什么...'>
    </el-input>
    <div class='button_wrapper'>
      <div v-if='isCancel' class='cannel_button' @click='$emit("cancel",false)'>取消</div>
      <div ref='sub_button' class='sub_button' @click='submitHandler'>提交</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentInput',
  props: {
    id: {
      default: null
    },
    isReply: {
      type: Boolean,
      default: false
    },
    isCancel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      content: ''
    }
  },
  methods: {
    submitHandler() {
      if (this.content) {
        this.$refs.sub_button.innerText = '提交中..'
        this.$emit('submitHandler', { content: this.content, parentId: this.id }, val => {
          if (val) {
            this.content = ''
            this.$refs.sub_button.innerText = '提交'
            this.$emit('cancel', false)
          }
        })
      } else {
        this.$toast('请输入内容')
      }
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
