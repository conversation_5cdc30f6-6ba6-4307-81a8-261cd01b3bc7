.input_wrapper {
  height: 128px;
  background: #131313;
  border-radius: 4px;
  position: relative;

  /deep/ .el-textarea__inner {
    min-height: 80px !important;
    background: unset;
    border: none;
    padding: 12px 12px 0;
    margin-bottom: 12px;
    line-height: 1;
    color: #FFFFFF;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      color: #999999;
    }

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .button_wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 12px;

    .cannel_button {
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      color: #999999;
      margin-right: 24px;
    }

    .sub_button {
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      color: #0581CE;
      padding: 5px 11px;
      border: 1px solid #0581CE;
      border-radius: 100px;
    }
  }
}

.reply_wrapper {
  height: 100px !important;

  /deep/ .el-textarea__inner {
    min-height: 52px !important;
  }
}
