.footer {
  background: #f6f6f6;
  .footer-content {
    padding: 53px 0 56px;

    .about-medtion {
      margin-top: 40px;

      .about-item {
        .item, a {
          font-size: 16px;
          line-height: 16px;
          color: #333333;
          margin-right: 54px;
          font-weight: 500;

          .phone-svg {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            line-height: 16px;
            color: #333333;
          }

          &:hover {
            color: #0582CE;
          }

          &:hover {
            .phone-svg {
              color: #0582CE;
            }

          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .logo-list {
      .logo {
        width: 80px;
        height: 80px;
        position: relative;
        margin-right: 77px;


        .logo_imgbox {
          max-width: 100%;
        }

        .shenwai {
          max-width: 77.42px;
        }


        &:hover {
          .hover-img {
            display: block;
          }
        }

        .hover-img {
          position: absolute;
          left: 50%;
          bottom: 84px;
          transform: translateX(-50%);
          width: 132px;
          height: 159px;
          background: #FFFFFF;
          box-shadow: 2px 3px 8px rgba(0, 0, 0, 0.15);
          text-align: center;
          display: none;

          .hover-img-item {
            width: 120px;
            height: 120px;
            margin: 6px auto 0;
          }

          .hover-img-tips {
            font-size: 14px;
            line-height: 14px;
            color: #333333;
            margin-top: 7px;
          }

          .shenwai-info-hover {
            background: url("assets/images/footer/footer-logo.png") 0 0;
          }

          .shenjie-info-hover {
            background: url("assets/images/footer/footer-logo.png") -130px 0;
          }

          .naoyi-consulting-hover {
            background: url("assets/images/footer/footer-logo.png") -260px 0;
          }

          .shennei-info-hover {
            background: url("assets/images/footer/footer-logo.png") -390px 0;
          }

          .arbrain-info-hover {
            background: url("assets/images/footer/footer-logo.png") -520px 0;
          }

          .footer-logo-hover {
            background: url("assets/images/footer/footer-logo.png") -650px 0;
          }
        }

        &:last-child {
          margin-right: 0;

          &::after {
            display: none;
          }
        }

        &::after {
          display: block;
          content: "";
          clear: both;
          width: 1px;
          height: 46px;
          background-color: #E5E5E5;
          position: absolute;
          right: -37px;
          top: 50%;
          transform: translateY(-50%);
        }

        .shenwai-info {
          width: 78px !important;
          height: 78px !important;
          //background: url("assets/images/footer/footer-logo.png") 0 80px;
        }

        .shenjie-info {
          width: 80px !important;
          height: 80px !important;
          //background: url("assets/images/footer/footer-logo.png") -90px 80px;
        }

        .naoyi-consulting {
          width: 74px !important;
          height: 74px !important;
          //background: url("assets/images/footer/footer-logo.png") -180px 80px;
        }

        .shennei-info {
          width: 70px !important;
          height: 70px !important;
          //background: url("assets/images/footer/footer-logo.png") -270px 80px;
        }

        .arbrain-info {
          width: 64px !important;
          height: 64px !important;
          //background: url("assets/images/footer/footer-logo.png") -360px 80px;
        }

        .footer-logo {
          width: 70px !important;
          //background: url("assets/images/footer/footer-logo.png") -450px 80px;
        }
      }
    }
  }

  .footer-tail {
    background: #666666;
    color: #FFFFFF;
    text-align: center;
    font-size: 12px;
    line-height: 50px;

    span {
      margin-right: 24px;

      &:last-child {
        margin-right: 0;
      }

      a {
        color: #FFFFFF;
      }
    }
  }
}
