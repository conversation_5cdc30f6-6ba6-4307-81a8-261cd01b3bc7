<template>
  <div class='footer'>
    <div class='footer-content'>
      <div class='logo-list flex_center flex_align_center'>
        <div v-for='(item,index) in logoList' :key='index' class='logo flex_align_center flex_center'>
          <div class='hover-img'>
            <div :class='item.urlHoverTitle' class='hover-img-item'></div>
            <p class='hover-img-tips'>{{ item.hoverTitle }}</p>
          </div>
          <img :class='item.urlTitle' :src='item.imgSrc' alt='底部LOGO' class='logo_imgbox'>
          <!--          <svg-icon :class-name='`logo-img ${item.urlTitle} img_cover`' :icon-class='item.urlTitle' />-->
        </div>
      </div>
      <div class='about-medtion'>
        <ul v-if='$store.state.navigation.NavList[$store.state.navigation.NavList.length-1]'
            class='about-item flex_center'>
          <li
            v-for='(medtion,index) in $store.state.navigation.NavList[$store.state.navigation.NavList.length-1].children'
            v-if='medtion.isEnable'
            :key='index'
            :class="$route.query.optionActive === medtion.activeUrlName? 'themeFontColor':''" class='item cursor'
            @click='jumpOptionActive(medtion.activeUrlName)'
          >
            {{ $t(medtion.name) }}
          </li>
          <li class='item'>
            <a class='flex_start' href='tel:************'>
              <svg-icon class-name='phone-svg' icon-class='phone'></svg-icon>
              <span>热线电话: ************</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <div class='footer-tail'>
      <span>
        {{ footerTail.info }}
      </span>
      <span>
         <a href='http://beian.miit.gov.cn/' target='_blank'>
             {{ footerTail.icp }}
         </a>
        {{ footerTail.copyright }}
      </span>
    </div>
    <div style='width:300px;margin:0 auto; padding:20px 0;'>
      <a target='_blank' href='http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011202014644'
         style='display:inline-block;text-decoration:none;height:20px;line-height:20px;'><img
        src='~assets/images/put_on_record.png' style='float:left;' />
        <p style='float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px; color:#939393;'>沪公网安备
          31011202014644号</p></a>
    </div>
  </div>
</template>

<script>
import shenwai from '@/assets/images/footer/shenwai.png'
import shenjie from '@/assets/images/footer/shenjie.png'
import naoyi from '@/assets/images/footer/naoyi.png'
import shennei from '@/assets/images/footer/shennei.png'
import arbrain from '@/assets/images/footer/aibrainn.png'
import logo from '@/assets/images/footer/logo22.png'

export default {
  name: 'FooterNav',
  mounted() {

  },
  data() {
    return {
      footerTail: {
        info: 'Copyright © 2014-2025 Brainmed All Rights Reserved.',
        icp: '沪ICP备14035871号-3 | ',
        copyright: '沪网审 [2014-08-26] 版权归属上海脑医汇数字科技有限公司'
      },
      logoList: [
        {
          urlTitle: 'shenwai',
          urlHoverTitle: 'shenwai-info-hover',
          hoverTitle: '神外资讯公众号',
          imgSrc: shenwai
        },
        {
          urlTitle: 'shenjie-info',
          urlHoverTitle: 'shenjie-info-hover',
          hoverTitle: '神介资讯公众号',
          imgSrc: shenjie
        },
        {
          urlTitle: 'naoyi-consulting',
          urlHoverTitle: 'naoyi-consulting-hover',
          hoverTitle: '脑医咨询公众号',
          imgSrc: naoyi
        },
        {
          urlTitle: 'shennei-info',
          urlHoverTitle: 'shennei-info-hover',
          hoverTitle: '神内资讯公众号',
          imgSrc: shennei
        },
        { urlTitle: 'arbrain-info', urlHoverTitle: 'arbrain-info-hover', hoverTitle: 'AiBrain公众号', imgSrc: arbrain },
        { urlTitle: 'footer-logo', urlHoverTitle: 'footer-logo-hover', hoverTitle: '脑医汇服务号', imgSrc: logo }
      ]
    }
  },
  methods: {
    // 跳转底部链接
    jumpOptionActive(code) {
      this.$router.push({ path: '/introduce', query: { optionActive: code } })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
