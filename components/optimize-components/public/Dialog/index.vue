<template>
  <NewBackdrop>
    <div class="dialog_content">
      <div class="dialog_close" @click="$emit('close',false)">
        <svg-icon icon-class="close_user" class-name="dialog_content_icons"/>
      </div>
      <slot></slot>
    </div>
  </NewBackdrop>
</template>

<script>
import NewBackdrop from "../../UI/NewBackdrop/index.vue";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Dialog",
  components: {NewBackdrop},
  mounted() {
    document.body.style.overflow = "hidden"
  },
  beforeDestroy() {
    document.body.style.overflow = ""
  },
  destroyed() {
    document.body.style.overflow = ""
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
