/deep/.el-autocomplete-suggestion{
  background: #202020;
  border-radius: 12px;
  border: none;
}
/deep/.popper__arrow{
  display: none;
}

/deep/.el-autocomplete-suggestion li{
  padding: 0;
}
/deep/.el-autocomplete-suggestion li:hover{
  background: unset;
  .value_item{
    .default_label{
      background: #333333;
      color: #FFFFFF;
    }
  }
}
/deep/.el-autocomplete-suggestion__wrap{
  max-height: 466px;
}
/deep/.el-autocomplete-suggestion.is-loading li:hover{
  background: unset;
}
.value_item{
  margin-bottom: 2px;
  .default_label{
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: #EEEEEE;
    padding: 6px 16px;
  }
  .is_lable{
    font-size: 12px;
    line-height: 150%;
    color: #999999;
    cursor: not-allowed;
    padding: 6px 16px;
  }
}
.mall_nav_header {
  height: 60px;
  .nav_wrapper {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 2000;
    background: rgba(0, 0, 0, 0.3);
    /* 背景模糊 */
    backdrop-filter: blur(10px);

    .nav_container {
      height: 60px;
      line-height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .logo_name {
        display: flex;
        align-items: center;

        .logo_image {
          flex-shrink: 0;
          width: 56px;
          height: 56px;

          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }

        .name {
          padding-left: 40px;
          font-weight: 400;
          font-size: 18px;
          line-height: 1.5;
          color: #FFFFFF;
          user-select: none;
        }
      }

      .search_login {
        user-select: none;
        display: flex;
        align-items: center;

        .search_content {
          margin-right: 40px;
          position: relative;

          .clear_button{
            position: absolute;
            right: -30px;
            top: calc(50% + 2px);
            transform: translateY(-50%);
            .icons{
              width: 20px;
              height: 20px;
              cursor: pointer;
              color: #333333;
              &:hover{
                color: #444444;
              }
            }

          }
          .search_logo {
            width: 17px;
            height: 16px;
            padding-right: 16px;
            color: #999999;
          }

          /deep/ .el-input__inner {
            width: 348px;
            height: 36px;
            line-height: 36px;
            background: rgba(51, 51, 51, 0.8);
            border: 1px solid #666666;
            border-radius: 18px;
            padding: 0 16px;
            color: #FFFFFF;

            &::placeholder {
              font-weight: 400;
              font-size: 14px;
              line-height: 150%;
              color: #999999;
            }
          }
        }

        .back_home{
          height: 36px;
          width: 84px;
          border-radius: 18px;
          background: rgba(51, 51, 51, 0.40);
          font-size: 14px;
          color: #FFF;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          margin-right: 20px;
          &:hover{
            background: rgba(51, 51, 51, 0.80);
          }
          &:active{
            background: rgba(51, 51, 51, 0.40);
          }
        }
        .login_content {
          text-align: right;
          width: 130px;
          flex-shrink: 0;

          .login_tips {
            display: inline;
            background: #0581CE;
            border-radius: 24px;
            padding: 6px 18px;
            position: relative;

            .shu {
              content: "";
              width: 1px;
              height: 11px;
              background: rgba(255, 255, 255, 0.6);
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }

            .tips {
              font-weight: 400;
              font-size: 16px;
              line-height: 1.5;
              color: #FFFFFF;
              margin-right: 20px;
              cursor: pointer;

              &:last-child {
                margin-right: 0;
                color: rgba(255, 255, 255, 0.8);

                &::before {
                  display: none;
                }
              }
            }

            .register {

            }
          }

          .user_cart{
            display: flex;
            justify-content: end;
            .cart_content{
              flex-shrink: 0;
              margin-right: 20px;
              width: 36px;
              height: 36px;
              background: #333333;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              cursor: pointer;
              .mall_cart_icons{
                width: 20px;
                height: 20px;
                color: #FFFFFF;
              }
              .image{
                width: 20px;
                height: 20px;
              }
            }

            .user{
              .user_image{
                width: 36px;
                height: 36px;
                background: #999999;
                border-radius: 50%;
                overflow: hidden;

                img{
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}

.user_list_wrapper{
  width: 116px;
  .item{
    padding: 6px 16px;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: #999999;
    cursor: pointer;
    margin-bottom: 4px;
    &:last-child{
      margin-bottom: 0;
    }
    &:hover{
      background: #333333;
      color: #FFFFFF;
    }
  }
  .is_active{
    background: #333333;
    color: #FFFFFF;
  }
}

