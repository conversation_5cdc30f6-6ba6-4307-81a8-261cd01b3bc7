<template>
  <header class='mall_nav_header' :class='`mall_nav_header_${theme}`'>
    <div class='nav_wrapper'>
      <div class='container-box'>
        <div class='nav_container'>
          <div class='logo_name'>
            <div class='logo_image'>
              <nuxt-link to='/mall'>
                <img v-if='theme === "default"' src='~assets/images/mall/mall_logo.png' alt=''>
                <img v-else src='~assets/images/mall/mall_logo_default.png' alt=''>
              </nuxt-link>
            </div>
            <span class='name' @click='$router.push({path:`/mall`})'>脑医汇·脑医藏书阁</span>
          </div>
          <div class='search_login'>
            <div class='search_content'>
              <autocomplete
                v-model.trim='searchInfo'
                :fetch-suggestions='querySearch'
                :popper-append-to-body='false'
                placeholder='请输入关键字'
                popper-class='my-autocomplete'
                @select='handleSelect'
                @keyup.enter.native='searchHandler'
                @focus='getHotMerchandiseHandler()'
              >
                <svg-icon
                  slot='suffix'
                  class='cursor'
                  class-name='search_logo'
                  icon-class='search'
                  :style='searchInfo ? {color:"#0581CE"} : {}'
                  @click='searchHandler'
                >
                </svg-icon>
                <template slot-scope='{ item }'>
                  <div class='value_item'>
                    <p class='text-limit-1' :class='item.isLabel ? "is_lable" : "default_label"'>{{ item.value }}</p>
                  </div>
                </template>
              </autocomplete>
              <div v-if='searchInfo' class='clear_button' @click='searchInfo = ""'>
                <svg-icon icon-class='mall_clear' class-name='icons'/>
              </div>
            </div>
            <div v-if="!$store.state.cs.TerminalType" class="back_home" @click="jumpBackHandler">返回官网</div>
            <div class='login_content'>
              <div v-if='!$store.state.auth.isLogged' class='login_tips'>
                <span class='tips' @click='SigninFun'>登录</span>
                <span class='shu'></span>
                <span class='tips register' @click='registerFun'>注册</span>
              </div>
              <div v-else class='user_cart'>
                <div class='cart_content' @click='jumpCartPage'>
                  <MallPriceLabel top='-7px' :price='$store.state.mall.totalShoppingCartPrice'/>
                  <svg-icon icon-class='mall_cart' class-name='mall_cart_icons'/>
                </div>
                <div class='user'>
                  <Popover
                    placement='bottom-end'
                    width='116'
                    trigger='hover'
                    :popper-class='`mall_nav_popover_user mall_nav_popover_user_${theme}`'
                  >
                    <div class='user_list_wrapper' :class='`user_list_wrapper_${theme}`'>
                      <div
                        v-for='(item,index) in userList'
                        :key='index'
                        :class='$route.path === item.url ? "is_active" : ""'
                        class='item'
                        @click='jumpPageHandler(item)'>
                        {{ item.name }}
                      </div>
                    </div>
                    <div slot='reference' class='user_image'>
                      <img
                        :src='userAvatarAddress ? $tool.compressImg(userAvatarAddress,20,20) : require("/assets/images/user.png")'
                        alt=''>
                    </div>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import {Autocomplete, Popover} from 'element-ui'
import MallPriceLabel from '../../page-components/mall/MallPriceLabel/index.vue'
import {getHotMerchandiseList} from '../../../../api/mall'

export default {
  name: 'MallNav',
  components: {
    MallPriceLabel,
    Autocomplete,
    Popover
  },
  props: {
    theme: {
      type: String,
      default: 'default'
    }
  },
  data() {
    return {
      hotMerchandiseList: [],
      searchInfo: '',
      userList: [
        {name: '我的订单', url: '/mall/orders'},
        {name: '我的优惠券', url: '/mall/coupons'},
        {name: '我的收货地址', url: '/mall/addresses'},
        {name: '我的发票', url: '/mall/invoices'}
      ],
      timeout: null
    }
  },
  computed: {
    userAvatarAddress() {
      return this.$store.state.auth.user.avatarAddress
    }
  },
  mounted() {
    if (this.$route.query.keywords) {
      this.searchInfo = this.$route.query.keywords
    }
    this.getHotMerchandiseHandler()
  },
  methods: {
    jumpBackHandler() {
      window.location.href = '/'
    },
    jumpPageHandler(item) {
      this.$router.push({path: item.url})
      this.$analysys.btn_click(item.name, document.title)
    },
    // 获取热门搜索
    getHotMerchandiseHandler() {
      this.$axios.$request(getHotMerchandiseList({
        limit: 5
      })).then(response => {
        if (response && response.code === 1) {
          this.hotMerchandiseList = [
            {
              value: '热门书籍',
              isLabel: true
            }
          ]
          response.list.map(item => {
            this.hotMerchandiseList.push({
              value: item.name,
              isLabel: false
            })
            return this.hotMerchandiseList
          })

          const historyArr = JSON.parse(window.localStorage.getItem('mall_history_arr'))
          if (historyArr && historyArr.length > 0) {
            this.hotMerchandiseList.push({
              value: '历史搜索',
              isLabel: true
            })

            this.hotMerchandiseList = this.hotMerchandiseList.concat(historyArr ? historyArr.slice(0, 5) : [])
          }
        }
      })
    },
    // 登录跳转
    SigninFun() {
      /**
       * 如果已经登录了 就刷新页面
       */
      if (this.$cookies.get('medtion_token_only_sign')) {
        window.location.reload()
      } else {
        this.$analysys.btn_click('登录', document.title)
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({
          name: 'signin',
          query: {fallbackUrl: this.$route.fullPath}
        })
      }
    },
    // 注册跳转
    registerFun() {
      this.$analysys.btn_click('注册', document.title)
      this.$router.push({name: 'register'})
      this.$store.commit('editIdentityInformationFun', 'Register') // 到身份选择页面
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-05-22 16:50
     * 跳转购物车
     * ------------------------------------------------------------------------------
     */
    jumpCartPage() {
      window.location.href = '/mall/cart'
      // this.$router.push({
      //   path: `/mall/cart`
      // })
    },
    querySearch(queryString, cb) {
      const restaurants = this.hotMerchandiseList
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      if (queryString && results.length > 0) {
        results.unshift({
          value: '相关搜索',
          isLabel: true
        })
        const map = new Map()
        for (const item of results) {
          if (!map.has(item.value)) {
            map.set(item.value, item)
          }
        }
        results = [...map.values()]
      }
      cb(results)

    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleSelect(item) {
      if (item.isLabel) {
        this.searchInfo = ''
      } else {
        this.searchHandler()
      }
    },
    searchHandler() {
      if (this.searchInfo) {
        this.$analysys.resource_click({resource_title: this.searchInfo, resource_type: '脑医藏书阁搜索'})
        this.$analysys.search(
          null,
          null,
          false,
          this.searchInfo,
          false,
          '脑医藏书阁搜索'
        )
        if (this.$route.path === '/mall/search') {
          this.$router.push({path: `/mall/search`, query: {keywords: this.searchInfo}})
        } else {
          const {href} = this.$router.resolve({path: `/mall/search`, query: {keywords: this.searchInfo}})
          window.open(href, '_blank')
        }
        const historyArr = window.localStorage.getItem('mall_history_arr')
        if (!historyArr) {
          window.localStorage.setItem('mall_history_arr', JSON.stringify([{
            value: this.searchInfo,
            isLabel: false
          }]))
        } else {
          let newArr = JSON.parse(historyArr)
          newArr.unshift({
            value: this.searchInfo,
            isLabel: false
          })
          const map = new Map()
          for (const item of newArr) {
            if (!map.has(item.value)) {
              map.set(item.value, item)
            }
          }
          newArr = [...map.values()]
          window.localStorage.setItem('mall_history_arr', JSON.stringify(newArr))
        }
      } else {
        this.$toast('请输入关键字!')
      }
    }
  }
}
</script>

<style>
.mall_nav_popover_user {
  min-width: 116px;
  background: #202020;
  border-radius: 6px;
  border: none;
  padding: 10px 0;
}

.mall_nav_popover_user .popper__arrow {
  display: none !important;
}

.mall_nav_popover_user_white {
  background: #FCFCFC !important;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
}
</style>
<style scoped lang='less'>
@import "./styles";
@import "./white_styles";
</style>
