.mall_nav_header_white {
  /deep/ .el-autocomplete-suggestion {
    background: #FCFCFC !important;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    border: none;
  }

  /deep/ .el-autocomplete-suggestion li:hover {
    background: unset;

    .value_item {
      .default_label {
        background: #F2F9FD;
        color: #333333;
      }
    }
  }

  .value_item {
    .default_label {
      color: #666666;
    }

    .is_lable {
      color: #999999;
    }
  }

  .nav_wrapper {
    background: #FFFFFF;
    border-bottom: 1px solid #f1f1f1;

    .logo_name {
      .name {
        color: #303030 !important;
      }
    }

    /deep/ .el-input__inner {
      background: rgba(248, 248, 248, 0.8) !important;
      border: 1px solid #EEEEEE !important;
      color: #333333 !important;

      &::placeholder {
        color: #999999 !important;
      }
    }


    .cart_content {
      background: #F8F8F8 !important;

      .mall_cart_icons {
        color: #333333 !important;
      }
    }
  }

  .clear_button {
    .icons {
      color: #EEEEEE !important;

      &:hover {
        color: #e8e7e7 !important;
      }
    }

  }

  .back_home{
    background: rgba(248, 248, 248, 0.8) !important;
    color: #333!important;
  }
}

.user_list_wrapper_white {
  .item {
    color: #999999 !important;

    &:hover {
      background: #F2F9FD !important;
      color: #333333 !important;
    }
  }

  .is_active {
    background: #F2F9FD !important;
    color: #333333 !important;
  }
}

