<template>
  <section>
    <slot></slot>
    <div v-if='loading' class='empty-tips'>
      <p
        v-loading='loading'
        :style='{height:loadingHeight}'
        class='loading-tips'
        element-loading-spinner='el-icon-loading'
        element-loading-background='rgba(255,255,255,0.6)'
        element-loading-text='加载中'
      >
      </p>
    </div>
    <div v-else-if='!loading && noData' :style='{height:loadingHeight}' class='tips'>
      <img src='~assets/images/null_content.png' alt='' :style='{width:"30vh"}'>
      <span>没有更多内容 ~</span>
    </div>

    <p v-else-if='!loading && !noData && noMore' class='no_more' :style='{height:loadingHeight}'>
      <span>到底了 ~</span>
    </p>
  </section>
</template>

<script>
export default {
  name: 'BottomtingLoad',
  props: {
    noData: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    noMore: {
      type: Boolean,
      default: false
    },
    loadingHeight: {
      type: String,
      default: '30vh'
    }
  }
}
</script>

<style lang='less' scoped>
@import "styles";
</style>
