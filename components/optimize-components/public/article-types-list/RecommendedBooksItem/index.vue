<template>
  <div class='wrapper'>
    <nuxt-link
      class='book_item_wrapper'
      :to='{path:`/mall/product/${id}`}'
    >
      <div class='image'>
        <img class='img_cover' :src='cover ? $tool.compressImg(cover,86,86) : require("/assets/images/default1.png")' alt=''>
      </div>
      <div class='info'>
        <p class='title text-limit-2'>
          {{title}}
        </p>
        <MallPrice :price='price' font-size='16px' decimal-font-size='12px'/>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import MallPrice from '../../../page-components/mall/MallPrice/index.vue'

export default {
  name: 'RecommendedBooksItem',
  components: { MallPrice },
  props:["title","cover","price","id"]
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
