.book_item_wrapper{
  display: flex;
  &:hover{
    .info{
      .title{
        color: var(--theme-color)!important;
      }
    }
  }

  .image{
    flex-shrink: 0;
    width: 86px;
    height: 86px;
    margin-right: 10px;
    border-radius: 6px;
    overflow: hidden;
  }
  .info{
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    padding: 2px 0;
    .title{
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      color: #333333;
      margin-bottom: 10px;
    }
    .price{
      justify-content: start!important;
    }
  }
}
