<template>
  <li class='pgc_wrapper'>
    <div class='pgc_link' @click='shortVideoPlay'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title'/>
        <ArticleType v-if='isLabelShow' type='短视频'/>
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 20px;height: 20px"/>
            <span v-html="title"></span>
          </p>
          <p class='ugc__label__wrapper'>
            <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
              <svg-icon class-name='productIcon' icon-class='product2'/>
              <span>{{ item.name }}</span>
          </span>
          </p>
        </div>
        <div class='author_wrapper'>
          <div class='author_content'>
            <div
              v-for='item in authorList.slice(0,3)'
              :key='item.id'
              class='image'>
              <img
                v-if='item.userAvatar'
                class='img_cover'
                :src='item.userAvatar'
                :alt='item.authorName'>
              <svg-icon
                v-else
                class-name='author__img'
                icon-class='signinavatar'
              />
            </div>
            <span class='author_name'>{{
                authorList.length > 1 ?
                  `${authorList[0].authorName}等${authorList.length}位作者`
                  :
                  authorList.length === 1 ?
                    `${authorList[0].authorName}`
                    :
                    ''
              }}</span>
          </div>
          <div class='article_info'>
            <span class='article_date'>{{ articleDate }}</span>
            <span class='article_read'>{{ articleRead }}阅读</span>
            <span class='article_comment'>{{ articleComment }}评论</span>
            <span class='article_fabulous'>{{ articleFabulous }}点赞</span>
          </div>
        </div>
      </div>
    </div>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'ShortVideoItem',
  methods: {
    shortVideoPlay() {
      this.$store.commit('bms/setBmsHomeShortVideoHandler', this.id)
    }
  },
  components: {ArticleType},
  props: {
    id: {},
    categoryId: {},
    productList: {},
    articleDate: {},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {},
    authorList: {},
    articleRead: {},
    articleComment: {},
    articleFabulous: {},
    bmsAuth:{}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
