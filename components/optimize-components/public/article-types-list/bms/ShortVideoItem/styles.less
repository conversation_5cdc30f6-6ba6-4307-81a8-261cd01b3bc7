.pgc_wrapper {
  border-radius: 6px;
  padding: 10px;
  transition: all .3s;

  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }

  .pgc_link {
    display: flex;
    grid-gap: 0 10px;
    cursor: pointer;

    .image {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }

    .pgc_info {
      width: calc(100% - (220px + 10px));
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .pgc_title {
        font-weight: 700;
        font-size: 18px;
        line-height: 20px;
        color: #202020;
        margin-bottom: 12px;
      }

      .ugc__label__wrapper {
        max-height: calc(22px + 10px + 22px);
        overflow: hidden;

        .ugc__label__product {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          padding: 0 9px;
          background: #F6F6F6;
          border-radius: 11px;
          font-size: 12px;
          color: #0CA92F;
          margin-right: 10px;
          margin-bottom: 10px;

          .productIcon {
            width: 12px;
            height: 12px;
            margin-right: 2px;
          }

        }
      }

      .author_wrapper {
        display: flex;
        justify-content: space-between;

        .author_content {
          display: flex;
          align-items: center;

          .image {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 4px;

            .author__img {
              width: 16px;
              height: 16px;
            }

            &:first-child {
              margin-left: 0;
            }

            margin-left: -8px;
          }

          .author_name {
            font-size: 12px;
            line-height: 12px;
            color: #708AA2;
          }
        }

        .article_info {
          display: flex;
          grid-gap: 0 10px;
          padding-right: 5px;

          span {
            font-size: 12px;
            line-height: 12px;
            color: #708AA2;
          }
        }
      }
    }
  }

}
