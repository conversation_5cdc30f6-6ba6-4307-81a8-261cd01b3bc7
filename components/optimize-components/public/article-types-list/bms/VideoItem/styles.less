.video_item_wrapper {
  cursor: pointer;
  display: flex;
  flex-direction: column;

  .thumbnail {
    height: 332px;
    background: #222222;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    img {
      width: 100%;
      object-fit: cover !important;
      max-height: 100%;
    }
  }

  .title {
    font-weight: 700;
    font-size: 18px;
    line-height: 20px;
    color: #202020;
    margin-bottom: 12px;
  }

  .author {
    margin-top: auto;
    display: flex;
    align-items: center;

    &:hover {
      .author_name {
        color: var(--theme-color) !important;
      }
    }

    .image {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      overflow: hidden;

      .author__img {
        width: 24px;
        height: 24px;
      }
    }

    .author_name {
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
      color: #666666;
      margin-left: 12px;
    }
  }
}
