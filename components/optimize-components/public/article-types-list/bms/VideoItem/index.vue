<template>
  <li class='video_item_wrapper' @click='videoDialogHandler'>
    <div class='thumbnail'>
      <img v-show='videoImg' :src='$tool.compressImg(videoImg,187,332)' :alt='videoTitle'>
      <svg-icon v-if="bmsAuth === 1" icon-class="auth-new"
                style="width: 20px;height: 20px;position: absolute;left: 10px;top:10px;"/>
    </div>
    <p class='title text-limit-2'>
      {{ videoTitle }}
    </p>
    <div class='author' @click.stop='jumpAuthorPage'>
      <div class='image'>
        <img v-if='avatarAddress' class='img_cover' :src='$tool.compressImg(avatarAddress,24,24)' alt=''>
        <svg-icon v-else class-name='author__img' icon-class='signinavatar' />
      </div>
      <div class='author_name text-limit-1'>{{ authorName }}</div>
    </div>
  </li>
</template>

<script>

export default {
  name: 'VideoItem',
  components: {},
  props: {
    videoTitle: {
      type: String,
      default: ''
    },
    videoId: {},
    videoImg: {},
    authorName: {
      type: String,
      default: ''
    },
    authorId: {},
    avatarAddress: {},
    bmsAuth:{}
  },
  data() {
    return {}
  },
  methods: {
    videoDialogHandler() {
      this.$emit('videoFrame', this.videoId)
    },
    jumpAuthorPage() {
      const routeUrl = this.$router.resolve({
        path: `/user-center?profileUserId=${this.authorId}`
      })
      window.open(routeUrl.href, '_blank')
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles.less";
</style>
