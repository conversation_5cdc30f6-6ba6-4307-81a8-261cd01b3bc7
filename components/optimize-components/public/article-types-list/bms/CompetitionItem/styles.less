.pgc_wrapper {
  border-radius: 6px;
  padding: 10px;
  transition: all .3s;

  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }

  .pgc_link {
    display: flex;
    grid-gap: 0 10px;

    .image {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }

    .pgc_info {
      width: calc(100% - (220px + 10px));
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .pgc_title {
        font-weight: 700;
        font-size: 18px;
        line-height: 20px;
        color: #202020;
        margin-bottom: 10px;

        .poneIcon {
          width: 16px;
          height: 16px;
          margin-right: 2px;
        }
      }
    }
  }

}
