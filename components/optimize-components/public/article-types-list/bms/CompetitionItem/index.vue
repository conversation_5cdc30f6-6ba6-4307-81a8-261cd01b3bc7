<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{ path :`/shortvideo/competition?competitionId=${id}`}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title' />
        <ArticleType v-if='isLabelShow' type='比赛' />
      </div>
      <div class='pgc_info'>
        <p class='pgc_title text-limit-2'>
          <span class='vertical_align_middle'>{{ title }}</span>
        </p>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'CompetitionItem',
  components: { ArticleType },
  props: {
    id: {},
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {
      type: String,
      default: ''
    },
    isLabelShow: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
