<template>
  <li>
    <div class="list_strip_box" @click="navGetTo(id)">
      <div class="list_img" :style="`background-image: url(${$tool.compressImg(image, 220, 124)})`">
        <div v-if="myType === 1">
          <div v-if="elabSurgicalClassification.code === '全息手术'" class="subscript">
            <img src="~assets/images/elabweb/loadding.png"/>
            <span>全景手术</span>
          </div>
        </div>
        <ArticleType v-else type='手术复盘'/>
      </div>
      <div class="list_txt_box">
        <div class="">
          <h3 v-html="caseName"></h3>
          <div v-if="elabSurgicalClassification.code === '全息手术'" class="authorList">
            <div class="author" v-html="hospital ? hospital.name : ''"></div>
          </div>
          <div v-else>
            <div v-if="authorList.length > 0" class="authorList">
              <div v-for="(sitem) in authorList" :key="sitem.id" class="author curBox"
                   @click.stop="navGetToCenter(sitem.id)" v-html="sitem.realName"></div>
            </div>
          </div>
        </div>
        <div class="">
          <div class="tagList">
            <div v-for="sitem in classificationList" :key="sitem.id" class="tag">#<span v-html="sitem.name"></span>
            </div>
          </div>
          <div class="introduce">{{ shareDesc }}</div>
        </div>
      </div>
    </div>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'ElabItemTui',
  components: {ArticleType},
  props: {
    id: {
      type: Number,
      default: null
    },
    caseName: {
      type: String,
      default: ''
    },
    image: {
      type: String,
      default: ''
    },
    elabSurgicalClassification: {
      type: Object,
      default: null
    },
    hospital: {
      type: Object,
      default: null
    },
    authorList: {
      type: Array,
      default: () => ([])
    },
    shareDesc: {
      type: String,
      default: ''
    },
    classificationList: {
      type: Array,
      default: () => ([])
    },
    myType: {
      type: Number,
      default: 0
    },
    isLabelShow: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    navGetTo(pid) {
      const {href} = this.$router.resolve({
        path: '/elabweb/detail',
        query: {id: pid}
      })
      window.open(href, '_blank')
    },

    // 前往术者中心
    navGetToCenter(id) {
      this.$router.push({
        path: '/user-center',
        query: {
          profileUserId: id
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
.list_strip_box {
  display: flex;
  margin-top: 32px;
  cursor: pointer;

  .list_img {
    width: 220px;
    height: 124px;
    flex-shrink: 0;
    border-radius: 8px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-right: 16px;
    position: relative;

    .subscript {
      border-radius: 3px;
      position: absolute;
      left: 8px;
      top: 8px;
      display: flex;
      align-items: center;
      height: auto;
      width: 66px;
      padding: 3px 2px;
      box-sizing: border-box;
      height: 23px;
      background: rgba(0, 0, 0, 0.20);
      backdrop-filter: blur(36px);

      span {
        color: #FFF;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17;
      }

      img {
        display: block;
        width: 11px;
        height: 11px;
        flex-shrink: 0;
        margin-right: 2px;
      }
    }
  }

  .list_txt_box {
    width: calc(100% - 256px);
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    h3 {
      color: #333;
      text-overflow: ellipsis;
      font-size: 14px;
      font-weight: 500;
      line-height: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      margin-bottom: 16px;
    }

    .authorList {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .author {
        color: #708AA2;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17px;
        padding: 3.104px 3.104px 3.104px 6.208px;
        margin-right: 12px;
        cursor: pointer;
      }

      .curBox {
        background: rgba(112, 138, 162, 0.08);
        color: #0581CE !important;
      }
    }

    .tagList {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 4px;

      .tag {
        color: #708AA2;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17px;
        margin-right: 16px;
        margin-bottom: 8px;
      }
    }

    .introduce {
      color: #708AA2;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 17px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
  }
}
</style>
