.products_item {
  background: #FBFBFB;
  border: 1px solid #F4F4F4;
  border-radius: 6px;
  overflow: hidden;
  transition: all .3s;
  cursor: pointer;

  .item_container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

  }

  &:hover {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }

  .image {
    height: 141px;
    margin-bottom: 12px;
    position: relative;
  }

  .content {
    flex: 1;
    padding: 0 12px 16px;
    display: flex;
    flex-direction: column;

    .title {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;
      color: #202020;
      margin-bottom: 12px;

    }

    .bottom {
      margin-top: auto;
    }

    .company_wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      cursor: pointer;

      &:hover {
        .name {
          color: var(--theme-color) !important;
        }
      }

      .logo {
        width: 26px;
        height: 26px;
        border-radius: 6px;
        overflow: hidden;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .name {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #000000;
        transition: all .3s;
      }
    }

    .label_wrapper {

      .label {
        line-height: 22px;
        background: #F6F6F6;
        border-radius: 11px;
        padding: 0 8px;
        color: #0CA92E;
        font-size: 12px;

        .productIcon {
          width: 12px;
          height: 12px;
          margin-right: 4px;
        }
      }
    }
  }


}
