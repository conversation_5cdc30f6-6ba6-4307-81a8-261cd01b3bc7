<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{path:`/bms/classify/${categoryId}/brand-video-details/${id}`}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title'/>
        <ArticleType v-if='isLabelShow' type='视频'/>
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <span class='vertical_align_middle' v-html="title"></span>
          </p>
          <p class='ugc__label__wrapper'>
            <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
              <svg-icon class-name='productIcon' icon-class='product2'/>
              <span>{{ item.name }}</span>
          </span>
          </p>
        </div>
        <div class='time'>
          {{ createTime }}
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'BmsVideoItem',
  components: {ArticleType},
  props: {
    id: {},
    categoryId: {},
    productList: {},
    createTime: {},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
