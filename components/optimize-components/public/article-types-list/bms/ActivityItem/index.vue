<template>
  <li class='products_item'>
    <div class='item_container' @click='jumpPageHandler'>
      <div class='image' :style='{marginBottom:imageBottom,height:imgHeight}'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,224,126) : require("/assets/images/default16.png")'
          :alt='title' />
      </div>
      <div class='content'>
        <p class='title text-limit-2'>
          {{ title }}
        </p>
        <div class='bottom'>
          <div v-if='brandIsShow' class='company_wrapper' @click.stop='jumpBrandPageHandler'>
            <div class='logo'>
              <img
                class='img_cover'
                :src='logo ? $tool.compressImg(logo,26,26) : require("/assets/images/default1.png")'
                :alt='title'
              />
            </div>
            <span class='name text-limit-2'>{{ brandName }}</span>
          </div>
          <div v-if='productIsShow' class='label_wrapper'>
          <span class='label'>
            <svg-icon class-name='productIcon' icon-class='product2' />
            <span>{{ categoryName }}</span>
          </span>
          </div>
        </div>
      </div>
    </div>
  </li>
</template>

<script>
export default {
  name: 'NewProductsItem',
  props: {
    imgHeight: {
      type: String,
      default: '141px'
    },
    img: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    categoryName: {
      type: String,
      default: ''
    },
    brandName: {
      type: String,
      default: ''
    },
    logo: {
      type: String,
      default: ''
    },
    imageBottom: {
      type: String,
      default: ''
    },
    productIsShow: {
      type: Boolean,
      default: true
    },
    brandIsShow: {
      type: Boolean,
      default: true
    },
    id: {},
    brandId: {},
    categoryId: {},
    productLineId: {}
  },
  methods: {
    jumpPageHandler() {
      const { href } = this.$router.resolve({ path: `/bms/classify/${this.categoryId}/activity-details/${this.id}` })
      window.open(href, '_blank')
    },
    jumpBrandPageHandler() {
      if (this.productLineId) {
        const { href } = this.$router.resolve({ path: `/bms/classify/${this.categoryId}/product-line-details/${this.productLineId}` })
        window.open(href, '_blank')
      } else {
        const { href } = this.$router.resolve({ path: `/bms/classify/${this.categoryId}/brand-details/${this.brandId}` })
        window.open(href, '_blank')
      }

    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
