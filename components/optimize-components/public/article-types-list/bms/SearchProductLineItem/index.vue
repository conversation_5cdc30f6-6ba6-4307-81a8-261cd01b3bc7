<template>
  <div class='product_line_head_wrapper'>
    <div class='product_line_head_content'>
      <div class='image'>
        <nuxt-link
          class='button_brand'
          target='_blank'
          :to='`/bms/classify/${productLineDetails.categoryId}/product-line-details/${productLineDetails.id}`'>
          <img
            class='img_cover'
            :src='productLineDetails.shareImage ? $tool.compressImg(productLineDetails.shareImage,128,128): require("/assets/images/default1.png")'
            alt=''>
        </nuxt-link>
      </div>
      <div class='info'>
        <p class='title text-limit-2'>{{ productLineDetails.brand ? productLineDetails.brand.name + "-" : '' }}{{ productLineDetails.name }}</p>
        <div class='bottom'>
          <p class='product_num'>
            <span>{{ productLineDetails.products }}产品</span>
            <span>｜</span>
            <span>{{ productLineDetails.articles }}病例</span>
          </p>
          <p class='look'>{{ $tool.formatterNumUnit(productLineDetails.views) }}浏览</p>
        </div>
      </div>
    </div>
    <div class='product_line_head_operate'>
      <div class='wrapper_button'>
        <FollowButton
          width='79px'
          height='30px'
          background='#0581CE'
          font-size='14px'
          gap='0 6px'
          icon-name='follow___'
          :is-follow='productLineDetails.subscribeStatus === "T"'
          @follow='followHandler'
        />
        <nuxt-link
          class='button_brand'
          target='_blank'
          :to='`/bms/classify/${productLineDetails.categoryId || "-"}/product-line-details/${productLineDetails.id}`'>
          进入品牌
        </nuxt-link>

      </div>
    </div>
  </div>
</template>

<script>
import FollowButton from '../../../../public/FollowButton/index.vue'

export default {
  name: 'SearchProductLineItem',
  components: { FollowButton },
  props: ['productLineDetails'],
  methods: {
    followHandler() {
      this.$emit('followProductLineFun', {
        brandId: this.productLineDetails.brand.id,
        productLineId: this.productLineDetails.id
      }, val => {
        if (val === 'T') {
          this.$toast('订阅成功')
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
