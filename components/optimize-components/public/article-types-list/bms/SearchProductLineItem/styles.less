.product_line_head_wrapper {
  //padding: 16px 24px 16px 16px;
  background: #FBFBFB;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  //margin-bottom: 20px;

  .product_line_head_content {
    display: flex;

    .image {
      flex-shrink: 0;
      margin-right: 16px;
      width: 128px;
      height: 128px;
      border-radius: 6px;
      overflow: hidden;
    }

    .info {
      flex: auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px 0 8px;

      .title {
        font-weight: 700;
        font-size: 18px;
        line-height: 1.2;
        color: #333333;
      }

      .product_num {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #999999;
        margin-bottom: 16px;
      }

      .look {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #999999;
      }
    }
  }

  .product_line_head_operate {
    display: flex;
    align-items: end;

    .wrapper_button {
      display: flex;
      grid-gap: 0 20px;
      margin-bottom: 8px;

      .button_brand {
        width: 78px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #0581CE;
        border-radius: 100px;
        font-weight: 400;
        font-size: 14px;
        color: #0581CE;
      }
    }
  }
}
