//  咨询
.consulting-item:hover {
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
}

.consulting-item {
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  justify-content: flex-start;
  padding: 10px 0 10px 10px;
  box-sizing: border-box;

  .item-left {
    margin-right: 10px;
    min-width: 220px;
    max-width: 220px;
    text-align: center;
    min-height: 100%;
    border-radius: 6px;
    height: 124px;
    position: relative;
    overflow: hidden;

    .position_top {
      position: absolute;
      right: 6px;
      top: 6px;
      padding: 2px 4px;
      font-size: 12px;
      color: white;
      background: rgb(87, 137, 199);
      border-radius: 2px;
    }

    img {
      display: block;
      width: 100%;
      height: auto;
      // max-height: 126px;
      vertical-align: middle;
      margin: 0 auto;
    }
  }

  .item-right {
    padding-right: 20px;

    .pone {
      font-size: 18px;
      line-height: 22px;

      .poneIcon {
        min-width: 16px;
        min-height: 16px;
      }
    }

    .ptwo {
      display: block;
      font-size: 12px;
      line-height: 22px;
      color: #708AA2FF;
    }

    .pthree {
      font-size: 12px;
      color: #708AA2FF;
      margin: 12px 0 16px;

      .pthreeIcon {
        width: 12px;
        height: 12px;
      }
    }
  }
}
