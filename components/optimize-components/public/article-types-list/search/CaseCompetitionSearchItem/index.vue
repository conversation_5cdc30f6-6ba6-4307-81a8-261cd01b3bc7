<template>

  <!-- 病例大赛 -->
  <div>
    <div
      class='consulting-item'
      @click='jumpDetailsPageFun(caseCompetition.mcs_competition.competitionUrl,caseCompetition.mcs_competition.code)'
    >
      <div class='item-left'>
        <competition-state :leader='1' :state='caseCompetition.mcs_competition.status'></competition-state>
        <img v-if='caseCompetition.mcs_competition.coverImage' id='articleImg'
             :src='$tool.compressImg(caseCompetition.mcs_competition.coverImage,220,124)'
             class='img_cover'/>
        <img v-else class='img_cover' src='~assets/images/default16.png'/>
        <!--              <div v-show='item.infoImg !== null' class='position_top'>文章</div>-->
      </div>
      <div class='item-right'>
        <p class='pone fontWeight flex_start flex_align_center'>
          <span class='text-limit-2' v-html='caseCompetition.mcs_competition.name'></span>
        </p>
        <p class='pthree'>
          <span
            style='margin-right: 22px'>{{
              timeStamp.timestampFormat(Date.parse(caseCompetition.mcs_competition.addTime.replace(/-/g, '/')) / 1000)
            }}</span>
          <span>
<!--            <svg-icon class-name='pthreeIcon cursor' icon-class='yuanchan'></svg-icon>-->
            <!--            <span v-html='consultingData.info.authorNames'></span>-->
          </span>
          <!--          <span v-if='consultingData.info.authorList.length<=0'><i class='el-icon-video-play'></i>脑医咨询助手</span>-->
        </p>
        <p class='ptwo text-limit-2' v-html='caseCompetition.mcs_competition.description'></p>
      </div>
    </div>
  </div>
  <!-- 咨询结束 -->
</template>

<script>
import CompetitionState from '@/components/CompetitionState/CompetitionState'
import env from '/env-module'

export default {
  name: 'CaseCompetitionSearchItem',
  components: {
    CompetitionState
  },
  props: {
    caseCompetition: {
      required: true
    }
  },
  mounted() {

  },
  methods: {
    // 跳转文章详情
    jumpDetailsPageFun(competitionUrl,code) {
      if (competitionUrl) {
        window.open(competitionUrl, '_blank')
      } else {
        window.open(env[process.env.NODE_ENV].ENV_API + code + '/', '_blank')
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
