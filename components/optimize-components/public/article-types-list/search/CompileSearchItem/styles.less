// 编译
.consulting-item:hover {
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
}

.consulting-item {
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  justify-content: flex-start;
  padding: 10px 0 10px 10px;
  box-sizing: border-box;

  .item-left {
    margin-right: 10px;
    min-width: 220px;
    max-width: 220px;
    text-align: center;
    min-height: 100%;
    border-radius: 6px;
    height: 124px;
    overflow: hidden;
  }

  .item-right {
    padding-right: 20px;

    .pone {
      font-size: 18px;
      line-height: 22px;
      margin-bottom: 15px;
      font-weight: bold;

      .poneIcon {
        min-width: 16px;
        min-height: 16px;
        max-width: 16px;
        max-height: 16px;
        vertical-align: middle;
      }
    }

    .ptwo {
      display: block;
      font-size: 12px;
      line-height: 22px;
      color: #708AA2FF;
    }

    .pthree {
      font-size: 12px;
      color: #708AA2FF;

      i {
        margin-right: 2px;
      }
    }

    .lable-box {
      margin-top: 10px;

      .lable-item {
        display: inline-block;
        border-radius: 11px;
        background: #F0F9FF;
        font-size: 12px;
        color: #0A83CE;
        line-height: 22px;
        padding: 0 11px;
        margin: 0 10px 10px 0;
      }
    }
  }
}

.pthreeIcon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}
