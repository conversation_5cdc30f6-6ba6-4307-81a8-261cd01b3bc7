<template>
  <div>
    <div v-for='v in 1' :key='v' class='consulting-item cursor' @click='jumpDetailsPageFun(consultingData.info.infoId)'>
      <div class='item-left'>
        <img v-if='consultingData.info.infoImg' :src='$tool.compressImg(consultingData.info.infoImg,220,124)'
             class='img_cover'>
        <img v-else class='img_cover' src='~assets/images/default16.png'/>
      </div>
      <div class='item-right'>
        <p class='pone text-limit-1 flex_align_center flex_start'>
          <svg-icon
            v-if='consultingData.info.essences==="T"'
            class-name='poneIcon cursor'
            icon-class='jinghua'
          ></svg-icon>
          <svg-icon v-if="consultingData.info.bmsAuth === 1" icon-class="auth-new"
                    style="width: 20px;height: 20px;vertical-align:middle"/>
          <span class='vertical_align_middle' v-html='consultingData.info.infoTitle'></span>
        </p>
        <p class='pthree'>
          <span
            style='margin-right: 22px'>{{
              timeStamp.timestampFormat(Date.parse(consultingData.info.publishDate.replace(/-/g, '/')) / 1000)
            }}</span>
          <svg-icon class-name='pthreeIcon cursor' icon-class='yuanchan'></svg-icon>
          <span v-html='consultingData.info.authorNames?consultingData.info.authorNames:"神外资讯原创"'></span>
        </p>
        <ul class='lable-box text-limit-1'>
          <li v-for='attrs in consultingData.info.attrs' :key='attrs.id' class='lable-item' v-html='attrs.name'></li>
        </ul>
        <p class='ptwo text-limit-1' v-html='consultingData.info.searchDescription'></p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CompileSearchItem',
  props: {
    consultingData: {
      required: true
    }
  },
  methods: {
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({name: 'index-info-detail', query: {id: infoid}})
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";

.pthreeIcon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}
</style>
