<template>

  <!-- 咨询开始 -->
  <div>
    <div
      class='consulting-item'
      @click='jumpDetailsPageFun(infoId)'
    >
      <div class='item-left'>
        <img
          v-if='infoImg'
          id='articleImg'
          :src='$tool.compressImg(infoImg,220,124)' class='img_cover' alt=""/>
        <img v-else class='img_cover' src='~assets/images/default16.png' alt=""/>
        <svg-icon icon-class="icon_play_n" class-name="play_icon"/>
      </div>
      <div class='item-right'>
        <p class='pone fontWeight text-limit-2'>
          <span class='vertical_align_middle' v-html='infoTitle'></span>
        </p>
        <p class='pthree'>
          <i class='el-icon-time'></i>
          <span
            style='margin-right: 22px'>{{
              timeStamp.timestampFormat(Date.parse(publishDate.replace(/-/g, '/')) / 1000)
            }}</span>
          <span>
            <svg-icon class-name='pthreeIcon cursor' icon-class='yuanchan'></svg-icon>
            <span v-html='authorName'></span>
          </span>
        </p>
        <p class='ptwo text-limit-2' v-html='searchDescription'></p>
      </div>
    </div>
  </div>
  <!-- 咨询结束 -->
</template>

<script>
export default {
  name: 'InterpretingVideoSearchItem',
  props: {
    infoId: {
      required: true,
      type: Number
    },
    infoImg: {
      type: String,
      default: ""
    },
    infoTitle: {
      type: String,
      default: ""
    },
    publishDate: {},
    authorList: {
      type: Array,
      default: () => []
    },
    authorName: {
      type: String,
      default: ""
    },
    searchDescription: {
      type: String,
      default: ""
    }
  },
  mounted() {

  },
  methods: {
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({path:`/guidance/detail/video/${infoid}`})
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
