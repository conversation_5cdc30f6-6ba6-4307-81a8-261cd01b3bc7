<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{path:`/bms/classify/${categoryId}/product-details/${id}`}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title'/>
        <ArticleType v-if='isLabelShow' type='产品'/>
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <span class='vertical_align_middle' v-html="title"></span>
          </p>
          <p class='category' v-html="categoryName"></p>
        </div>
        <div class='brand'>
          <div class='image'>
            <img class='img_cover'
                 :src='$tool.compressImg(brandImg,26,26) || require("/assets/images/default1.png")' alt=''>
          </div>
          <div class='brand_name' v-html="brandName"></div>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'ProductSearchItem',
  components: {ArticleType},
  props: {
    id: {},
    categoryId: {},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {},
    categoryName: {},
    brandImg: {},
    brandName: {}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
