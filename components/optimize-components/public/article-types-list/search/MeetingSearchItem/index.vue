<template>
  <ul class='recording_list_box'>
    <li class='recording_list flex_start'>
      <div class='recording_list_left cursor' @click='jumpMeetingDetailsFun(meetingData.meeting.id)'>
        <img v-if="meetingData.meeting.publishStatus === '0' && meetingData.meeting.forthcoming === 'T'"
             class='img_cover'
             src='~assets/images/meeting/herald.png' alt=''/>
        <img v-else-if='meetingData.meeting.appMainPic || meetingData.meeting.titlePic'
             :src='$tool.compressImg(meetingData.meeting.appMainPic || meetingData.meeting.titlePic,220,124)' alt=''
             class='img_cover'/>
        <img v-else class='img_cover' src='~assets/images/default16.png'/>
        <live-state :state='meetingData.meeting.meetingLiveStatus'></live-state>
      </div>
      <div class='recording_list_right'>
        <p class='recording_list_right_title fontSize18 fontWeight cursor'
           @click='jumpMeetingDetailsFun(meetingData.meeting.id)'
           v-html='meetingData.meeting.meetingName'></p>
        <p class='recording_list_right_time fontSize12'>
          <span class='time' v-html='meetingData.meeting.meetingDateStr'></span>
          <span class='userName'
                v-html='meetingData.meeting.province + (meetingData.meeting.province && meetingData.meeting.city?"-":"") + meetingData.meeting.city'></span>

        </p>
        <ul class='live_list_box'>
          <li
            v-for='(item, index) in meetingData.meeting.searchMeetingAgendas'
            :key='item.id'
            v-show='index<maxDisplay'
            class='live_list flex_between flex_align_center'
            @click='jumpMeetingDetailsFun(meetingData.meeting.id,null,item.id)'
          >
            <div class='live_left'>
              <p class='live_title fontSize16' v-html='item.theme'></p>
              <p class='live_author fontSize12' v-html='item.authorNames'></p>
            </div>
            <div class='live_right'>
              <SummaryPopover
                v-if="item.summaryShow === 1"
                placement="right"
                :meeting-name="meetingData.meeting.meetingName"
                :meeting-id="meetingData.meeting.id"
                :agenda-name="item.theme"
                :agenda-id="item.id"
              >
                <div class="flex_between flex_align_center cursor" :style="{height:'20px',marginRight:'21px'}">
                  <svg-icon
                    className='summary_svg cursor'
                    iconClass='summary'
                  ></svg-icon>
                  <span
                    style="background: linear-gradient(180deg,#48a7ff,#532ee4);background-clip: text;-webkit-background-clip: text;-webkit-text-fill-color: transparent;font-size: 14px;margin-left: 4.5px;line-height: 21px">摘要</span>
                </div>
              </SummaryPopover>
              <svg-icon icon-class='play' icon-name='play-name'></svg-icon>
            </div>
          </li>
          <div
            v-if='meetingData.meeting.searchMeetingAgendas.length>3 && maxDisplay<meetingData.meeting.searchMeetingAgendas.length'
            class='open_more fontSize12 themeFontColor'
            @click='maxDisplay += 3'>展开更多
          </div>
        </ul>
      </div>
    </li>
  </ul>
</template>

<script>
import LiveState from '@/components/LiveState/LiveState'
import SummaryPopover from "../../../../../../opt-components/popover/SummaryPopover.vue";

export default {
  name: 'MeetingSearchItem',
  components: {
    SummaryPopover,
    LiveState
  },
  props: {
    meetingData: {
      required: true
    }
  },
  data() {
    return {
      maxDisplay: 3// 最多展示
    }
  },
  methods: {
    // 跳转到会议详情
    jumpMeetingDetailsFun(meetingId, fieldsId, agendaId) {
      if (event.target.id === "summary_popover_btn") {
        return;
      }

      if (this.meetingData.meeting.publishStatus === "0" && this.meetingData.meeting.forthcoming === "T") {
        this.$toast('敬请期待～')
      } else {
        if (fieldsId) {
          const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: meetingId, fieldsId: fieldsId}})
          window.open(href, '_blank')
        } else if (agendaId) {
          const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: meetingId, agendaId: agendaId}})
          window.open(href, '_blank')
        } else {
          const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: meetingId}})
          window.open(href, '_blank')
        }

      }

    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
