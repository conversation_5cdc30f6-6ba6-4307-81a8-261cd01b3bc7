// 云课堂
.recording_list_box {
  .recording_list {
    border-radius: 6px;
    padding: 15px 0 15px 14px;

    .recording_list_left {
      min-width: 220px;
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }

    .recording_list_right {
      margin-left: 14px;
      width: 100%;

      .recording_list_right_title {
        margin-bottom: 12px;
      }

      .recording_list_right_time {
        color: #708aa2;
        margin-bottom: 12px;

        .userName {
          margin-left: 10px;
        }

        .class-span-right {
          margin-right: 15px;
        }

        .class-item-price {
          color: #DD5A42;
          font-size: 14px;
        }
      }

      .class-lable-ul {
        margin-bottom: 15px;

        .class-lable-item {
          padding: 4px 11px;
          background: #F0F9FF;
          border-radius: 11px 11px 11px 11px;
        }
      }

      .live_list_box {
        .live_list {
          background: #f1f5f9;
          padding: 10px 18px 10px 20px;

          &:nth-child(even) {
            background: #fbfbfb;
          }

          &:hover {
            background: #fbfbfb;
            cursor: pointer;

            .live_left {
              .live_title,
              .live_author {
                color: #0581ce;
              }
            }
          }

          .live_left {
            .live_title {
              color: #404040;
              margin-bottom: 5px;
              line-height: 24px;
            }

            .live_author {
              color: #708aa2;
            }
          }

          .live_right {
            display: flex;
            align-items: center;
            flex-shrink: 0;

            .summary_svg {
              width: 19px;
              height: 19px;
            }

            .play-name, .svg-icon {
              width: 21px;
              height: 21px;
            }
          }
        }

        .open_more {
          text-align: center;
          margin-top: 15px;
          cursor: pointer;
        }
      }
    }
  }
}
