<template>
  <div class="ad_search_item" @click='jumpBannerFun(ad)'>
    <ZipImg
      fill
      :src="ad.image"
      :width="800"
      :height="160"
      :alt="ad.name"
    />
  </div>
</template>

<script>
import ZipImg from "../../../../../../opt-components/component/ZipImg/index.vue";
import brandAdJump from "../../../../../../assets/helpers/brandAdJump.js";

export default {
  name: "AdSearchItem",
  components: {ZipImg},
  props: ["ad"],
  methods: {
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
  },
  mounted() {
    this.$analysys.ad_exposure({
      adExtras: this.ad.extras,
      adModule: this.ad.module,
      exposureLocation: this.ad.clickLocation,
      adCode: this.ad.code,
      adId: this.ad.adId,
      adName: this.ad.name,
      unionid: this.$store.state.auth.unionid,
      adUrl: this.ad.extras,
    })
  }
}
</script>

<style scoped lang="less">
.ad_search_item {
  width: 100%;
  padding-bottom: 20%;
  background: #E4E4E4;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: default;
}
</style>
