<template>
  <div
    class='follow-list-li flex_between flex_align_center themeBorderRadius'>
    <div class='follow-list-li-left flex_start flex_align_center cursor'
         @click='$store.dispatch("jumpUserCenterPageHandler",userId)'>
      <div class='image flex_shrink img_radius overflow_hidden'>
        <img
          v-if='avatarAddress'
          :src='$tool.compressImg(avatarAddress,74,74)'
          alt=''
          class='img_cover cursor'>
        <svg-icon
          v-else
          class-name='img_cover cursor'
          icon-class='signinavatar'
        />
      </div>
      <div class='user-info'>
        <p class='info-name fontWeight fontSize16' v-html='realName'></p>
        <p class='info-title fontSize14'>
          <span v-html='company'></span>
          <span class='title' v-html='department'></span>
        </p>
        <p class='info-desc fontSize14'>
          <!--                    创作<span class='themeBlackColor'>12</span> | -->
          粉丝<span class='themeBlackColor' v-html='fans'></span>
        </p>
      </div>
    </div>
    <div v-if='userId !== $store.state.auth.user.id' class='follow-list-li-right'
         @click.stop='followFun(userId)'>
      <div v-if='!isFollowNew' class='follow_btn flex_shrink flex_center flex_align_center themeButton'>
        <svg-icon class-name='tab_icon' icon-class='subscribe'></svg-icon>
        <span class='fontSize12'>关注</span>
      </div>
      <div
        v-else
        class='follow_btn followed_btn'
        @mouseleave='collapse(userId)'
        @mouseover='expand(userId)'>
        <span class='fontSize12'>
          {{ hoverShow[userId] ? '取消关注' : '已关注' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserList',
  props: {
    userId: {
      type: Number
    },
    avatarAddress: {
      type: String,
      default: ""
    },
    realName: {
      type: String,
      default: ""
    },
    company: {
      type: String,
      default: ""
    },
    department: {
      type: String,
      default: ""
    },
    fans: {
      type: Number
    },
    isFollow:{
      type:Boolean
    }
  },
  data() {
    return {
      isFollowNew:this.isFollow,
      hoverShow: {}    //  是否移入
    }
  },
  methods: {
    expand(data) {
      this.$set(this.hoverShow, data, 1)
    },
    collapse(data) {
      this.$delete(this.hoverShow, data)
    },
    followFun(id) {
      this.isFollowNew = !this.isFollowNew
      this.$emit('followFun', id)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
