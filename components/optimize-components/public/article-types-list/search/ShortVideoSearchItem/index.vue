<template>
  <div class="short_video_wrapper cursor" @click='shortVideoPlay'>
    <div class="image">
      <svg-icon v-if="bmsAuth === 1"  icon-class="auth-new"
                style="width: 20px;height: 20px;vertical-align:middle;position: absolute;right: 10px;top: 10px"/>
      <ImageCDN :src="cover" width="220" height="294"/>
    </div>
    <div class="content">
      <p class="text-limit-2 title" v-html="title"></p>
      <p class="tips text-limit-5" v-html="description"></p>
      <div class="detail">
        <div class="time">
          <svg-icon icon-class="short_video_time" class-name="icons"/>
          <span>{{ onlineTime }}</span>
        </div>
        <div class="user_name">{{ realName }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageCDN from "../../../../ImageCDN/index.vue";

export default {
  name: "ShortVideoSearchItem",
  components: {ImageCDN},
  props: {
    id: {
      type: Number,
    },
    cover: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    description: {
      type: String,
      default: ""
    },
    realName: {
      type: String,
      default: ""
    },
    onlineTime: {
      type: String,
      default: ""
    },
    bmsAuth:{}
  },
  methods: {
    shortVideoPlay() {
      this.$store.commit('bms/setBmsHomeShortVideoHandler', this.id)
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
