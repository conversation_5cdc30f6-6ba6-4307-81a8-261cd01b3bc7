<template>
  <div class="books_wrapper">
    <div class="books_title">
      <div class="title_left">
        <svg-icon icon-class='book_title' class-name='icons'/>
        <span>书籍</span>
      </div>
      <div class="title_right" @click="changeTab">
        <span>更多</span>
        <svg-icon icon-class="home-more" class-name="more_icon"/>
      </div>
    </div>

    <div class="books_list">
      <BooksSearchItem
        v-for="item in booksList"
        :key="item.id"
        :book-id="item.id"
        :title="item.name"
        :cover="item.cover"
        :price="item.price"
        :press-name="item.osmPress.name"
        :introduction="item.introduction"
        :discount-price="item.discountPrice"
        :author-list="item.authorList"
      />
    </div>
  </div>
</template>

<script>
import BooksSearchItem from "./BooksSearchItem";

export default {
  name: "BooksWrapper",
  props: {
    booksList: {
      type: Array,
      default: () => []
    }
  },
  components: {BooksSearchItem},
  methods:{
    changeTab(){
      this.$emit('changeTab','15')
      this.$tool.scrollIntoTop()
    }
  }
}
</script>

<style scoped lang="less">
.books_wrapper {
  //border-radius: 8px;
  background: #FFF;
  //background: beige;
  padding: 24px;
  margin: 0 -24px;
  border-top: 12px solid #EEF2F3;
  border-bottom: 12px solid #EEF2F3;

  .books_title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title_left {
      display: flex;
      align-items: center;
      gap: 2px;
      color: #333;
      font-size: 18px;

      .icons {
        width: 28px;
        height: 28px;
      }
    }

    .title_right {
      display: flex;
      align-items: center;
      gap: 2px;
      color: #708AA2;
      font-size: 12px;
      cursor: pointer;

      .more_icon {
        width: 10px;
        height: 10px;
      }
    }
  }

  .books_list {
    display: grid;
    grid-template-columns: 1fr;
    margin-top: 18px;
    gap: 24px;
  }
}
</style>
