<template>
  <div class="books_search_item cursor" @click="jumpDetailsPageHandler">
    <div class="image">
      <zip-img
        :width="220"
        :height="220"
        :src="cover"
        fill
      />
    </div>
    <div class="item_content">
      <div class="content_title text-limit-1" v-html="title"></div>
      <div class="books_info">
        <div class="info_left">
          <p v-if="authorName" class="author_tips">作者：<span v-html="authorName"></span></p>
          <p v-if="translateAuthorName" class="author_tips">译者：<span v-html="translateAuthorName"></span></p>
          <p v-if="pressName" class="author_tips">出版社：<span v-html="pressName"></span></p>
        </div>
        <div class="info_right">
          <div class="price">
            <p>¥</p>
            <p class="number">{{ discountPrice || price }}</p>
          </div>
<!--          <div v-if="discountPrice && price" class="old_price">-->
<!--            <p>¥</p>-->
<!--            <p class="number">{{ price }}</p>-->
<!--          </div>-->
        </div>
      </div>

      <div v-if="introduction" class="book_introduction">
        <div class="introduction_content text-limit-5" v-html="introduction"></div>
        <div class="book_label">
          <svg-icon icon-class="book_label" class-name="label_icon"/>
          <span>图书简介</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ZipImg from "../../../../../../../opt-components/component/ZipImg/index.vue";

export default {
  name: "BooksSearchItem",
  components: {ZipImg},
  props: ["cover", "title", "price", "pressName", "bookId","introduction","authorList","discountPrice"],
  methods: {
    jumpDetailsPageHandler() {
      const {href} = this.$router.resolve({
        path: `/mall/product/${this.bookId}`
      })
      window.open(href, '_blank')
    },
  },
  computed:{
    authorName(){
      if(this.authorList && this.authorList.length>0){
        return this.authorList.filter(item => item.type === 'A').map(item => item.authorName).join()
      }else{
        return ''
      }
    },
    translateAuthorName(){
      if(this.authorList && this.authorList.length>0){
        return this.authorList.filter(item => item.type === 'T').map(item => item.authorName).join()
      }else{
        return ''
      }
    }
  }
}
</script>

<style scoped lang="less">
.books_search_item {
  display: flex;
  align-items: start;

  .image {
    border-radius: 8px;
    background: #F4F6F8;
    overflow: hidden;
    position: relative;
    width: 220px;
    height: 220px;
    flex-shrink: 0;
  }

  .item_content {
    margin-left: 16px;
    flex: 1;

    .content_title {
      color: #333;
      font-size: 16px;
      line-height: 21px;
      margin-bottom: 12px;
      font-weight: 600;
    }

    .books_info {
      min-height: 54px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .info_left {
        .author_tips {
          color: #999EA4;
          font-size: 12px;
          line-height: 18px;
        }
      }

      .info_right {
        display: flex;
        align-items: center;

        .price {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #0581CE;
          font-weight: 500;
          gap: 2px;

          .number {
            font-size: 18px;
            font-weight: 600;
          }
        }
        .old_price{
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 12px;
          color:#999999;
          font-weight: 500;
          gap: 2px;
          margin-left: 10px;
          text-decoration-line:line-through;

          .number {
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
    }

    .book_introduction {
      height: 121px;
      border-radius: 8px;
      background: #F4F6F8;
      padding: 8px 8px 26px;
      position: relative;
      box-sizing: border-box;

      .introduction_content {
        color: #708AA2;
        font-size: 12px;
        line-height: 18px;
      }

      .book_label {
        position: absolute;
        right: 10px;
        bottom: 6px;
        color: #B3C2D0;
        font-size: 12px;

        .label_icon {
          width: 12px;
          height: 12px;
        }
      }
    }

  }
}
</style>
