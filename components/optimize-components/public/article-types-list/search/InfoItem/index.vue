<template>

  <!-- 咨询开始 -->
  <div>
    <div
      class='consulting-item'
      @click='jumpDetailsPageFun(infoId)'
    >
      <div class='item-left'>
        <img
          v-if='infoImg'
          id='articleImg'
          :src='$tool.compressImg(infoImg,220,124)' class='img_cover' alt=""/>
        <img v-else class='img_cover' src='~assets/images/default16.png' alt=""/>
      </div>
      <div class='item-right'>
        <p class='pone fontWeight text-limit-2'>
          <svg-icon
            v-if='essences==="T"'
            class-name='poneIcon cursor'
            icon-class='jinghua'
          ></svg-icon>
          <svg-icon v-if="bmsAuth === 1"  icon-class="auth-new"
                    style="width: 20px;height: 20px;vertical-align:middle"/>
          <span class='vertical_align_middle' v-html='infoTitle'></span>
        </p>
        <p class='pthree'>
          <i class='el-icon-time'></i>
          <span
            style='margin-right: 22px'>{{
              timeStamp.timestampFormat(Date.parse(publishDate.replace(/-/g, '/')) / 1000)
            }}</span>
          <span v-if='authorList.length>0'>
            <svg-icon class-name='pthreeIcon cursor' icon-class='yuanchan'></svg-icon>
            <span v-html='authorNames'></span>
          </span>
          <span v-else>
             <svg-icon class-name='pthreeIcon cursor' icon-class='yuanchan'></svg-icon>
          <span>神外资讯原创</span>
          </span>
        </p>
        <p class='ptwo text-limit-2' v-html='searchDescription'></p>
      </div>
    </div>
  </div>
  <!-- 咨询结束 -->
</template>

<script>
export default {
  name: 'InfoItem',
  props: {
    infoId: {
      required: true,
      type: Number
    },
    infoImg: {
      type: String,
      default: ""
    },
    essences: {
      type: String,
      default: "F"
    },
    infoTitle: {
      type: String,
      default: ""
    },
    publishDate: {},
    authorList: {
      type: Array,
      default: () => []
    },
    authorNames: {
      type: String,
      default: ""
    },
    searchDescription: {
      type: String,
      default: ""
    },
    bmsAuth:{}
  },
  mounted() {

  },
  methods: {
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({name: 'index-info-detail', query: {id: infoid}})
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
