<template>
  <div
    class='follow-list-li flex_between flex_align_center themeBorderRadius cursor' @click='jumpPage'>
    <div class='follow-list-li-left flex_start flex_align_center cursor'>
      <div class='image flex_shrink overflow_hidden'>
        <img
          v-if='iconUrl'
          :src='$tool.compressImg(iconUrl,74,74)'
          alt=''
          class='img_cover cursor'>
        <svg-icon
          v-else
          class-name='img_cover cursor'
          icon-class='signinavatar'
        />
      </div>
      <div class='user-info'>
        <p class='info-name fontWeight fontSize16' v-html='hospitalGroupName'></p>
        <p class='info-title fontSize14'>
          <span v-html='department'></span>
        </p>
        <p class='info-desc fontSize14'>
          {{ memberTotal }}位已认证成员
        </p>
      </div>
    </div>
    <div
      class='follow-list-li-right'
      @click.stop='followFun()'>
      <div v-if='!isFollowNew' class='follow_btn flex_shrink flex_center flex_align_center themeButton'>
        <svg-icon class-name='tab_icon' icon-class='subscribe'></svg-icon>
        <span class='fontSize12'>关注</span>
      </div>
      <div
        v-else
        class='follow_btn followed_btn'
        @mouseleave='collapse(departmentId)'
        @mouseover='expand(departmentId)'>
        <span class='fontSize12'>
          {{ hoverShow[departmentId] ? '取消关注' : '已关注' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import {collectDepartment} from "../../../../../../api/department";

export default {
  name: 'DepartSearchItem',
  props: {
    // eslint-disable-next-line vue/require-default-prop
    departmentId: {
      type: Number
    },
    hospitalGroupName: {
      type: String,
      default: ""
    },
    department: {
      type: String,
      default: ""
    },
    iconUrl: {
      type: String,
      default: ""
    },
    memberTotal: {
      type: Number,
      default: 0
    },
    isFollow: {
      type: Boolean
    }
  },
  data() {
    return {
      isFollowNew: this.isFollow,
      hoverShow: {}    //  是否移入
    }
  },
  methods: {
    jumpPage() {
      const {href} = this.$router.resolve({
        path: `/department/detail?departmentId=${this.departmentId}`
      })
      window.open(href, '_blank')
    },
    expand(data) {
      this.$set(this.hoverShow, data, 1)
    },
    collapse(data) {
      this.$delete(this.hoverShow, data)
    },
    followFun() {
      this.isFollowNew = !this.isFollowNew

      this.$axios.$request(collectDepartment({
        userId: this.$store.state.auth.user.id,
        departmentId: this.departmentId
      }))
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
