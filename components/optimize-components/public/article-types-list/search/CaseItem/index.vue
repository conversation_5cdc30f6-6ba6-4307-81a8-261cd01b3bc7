<template>
  <div>
    <div class='case_consulting-item'>
      <div v-if="caseList.type === 'info'" class='first_article'>
        <nuxt-link :to="{name: 'index-case-detail-pgc',query: {id: caseList.info.infoId},}"
                   target='_blank'
        >
          <div class='item-left themeBorderRadius'>
            <img
              v-if='caseList.info.infoImg'
              :src='$tool.compressImg(caseList.info.infoImg,220,124)'
              class='img_cover'
            />
            <img v-else class='img_cover' src='~assets/images/default16.png'/>
          </div>
          <div class='item-right flex_column flex_column_justify'>
            <div>
              <p class='pone text-limit-2 fontWeight'>
                <svg-icon
                  v-if='caseList.info.essences==="T"'
                  class-name='poneIcon cursor'
                  icon-class='jinghua'
                ></svg-icon>
                <span class='vertical_align_middle' v-html='caseList.info.infoTitle'></span>
              </p>
              <ul class='lable-box text-limit-2'>
                <li
                  v-for='(item, index) in caseList.info.subspecialtys'
                  :key='item.id'
                  style='display: inline'
                >
                  <p v-for='itemChildren in item.children' :key='itemChildren.id' class='lable-item'>
                    {{
                      item.name +
                      '-' +
                      itemChildren.name
                    }}
                  </p>
                </li>
                <!-- 产品标签 -->
                <div
                  v-for='item in caseList.info.productList'
                  :key='item.id'
                  class='lable-item lable-item_product flex_start flex_align_center flex_warp'
                >
                  <svg-icon
                    className='productIcon'
                    iconClass='product2'
                  ></svg-icon>
                  {{ item.name }}
                </div>
              </ul>
            </div>
            <div class='lable_time flex_between flex_align_center'>
              <div class='flex_start flex_shrink flex_align_center'>
                <div v-for='(authorList,index) in caseList.info.authorList' v-show='index<3'
                     class='article_headimage flex_start'>
                  <img v-if='authorList.headImage' :key='authorList.id'
                       :src='$tool.compressImg(authorList.headImage,16,16)'
                       class='img_cover img_radius flex_shrink'/>
                  <svg-icon v-else class-name='img_cover' icon-class='signinavatar'/>
                </div>
                <div class='headName'>
                  {{
                    caseList.info.authorList.length > 1 ? `${caseList.info.authorList[0].authorName}等${caseList.info.authorList.length}位作者` : `${caseList.info.authorList.length === 1 ? caseList.info.authorList[0].authorName : ''}`
                  }}
                </div>
              </div>
              <div>
                <span>{{ timeStamp.timestampFormat(Date.parse(caseList.info.publishDate) / 1000) }}</span>
                <span>{{ caseList.info.showViews }}阅读</span>
                <span>{{ caseList.info.showComments }}评论</span>
                <span>{{ caseList.info.showDiggs }}点赞</span>
              </div>
            </div>
          </div>
        </nuxt-link>
      </div>
      <div v-if="caseList.type === 'mp_article'" class='second_article'>
        <nuxt-link
          :to="{name: 'index-case-detail-ugc',query: {id: caseList.mp_article.id},}"
          target='_blank'
        >

          <div class='second_article_smallbox'>
            <p class='pone text-limit-2 fontWeight'>
              <svg-icon
                v-if='caseList.mp_article.essences==="T"'
                class-name='poneIcon cursor'
                icon-class='jinghua'
              ></svg-icon>
              <svg-icon v-if="caseList.mp_article.bmsAuth === 1"  icon-class="auth-new"
                        style="width: 20px;height: 20px;vertical-align:middle"/>
              <span class='vertical_align_middle' v-html='caseList.mp_article.title'></span>
            </p>
            <!-- 亚专业标签 -->
            <div id='lableBox' class='lable-box'>
              <div id='productBox' class='flex_start flex_warp'>
                <ul id='lableItemUl' class='flex_start flex_warp'>
                  <li
                    v-for='(item, index) in caseList.mp_article.subspecialtys.length>0?caseList.mp_article.subspecialtys[0].children:caseList.mp_article.subspecialtys'
                    :key='item.id'
                    class='lable-item'
                    v-html='caseList.mp_article.subspecialtys[0].name + "-" + item.name'
                  ></li>
                  <!-- 产品标签 -->
                  <li
                    v-for='(item,index) in caseList.mp_article.productList'
                    id='productItem'
                    :key='index'
                    class='lable-item flex_start flex_align_center flex_warp'
                  >
                    <svg-icon
                      className='productIcon'
                      iconClass='product2'
                    ></svg-icon>
                    <span v-html='item.name'></span>
                  </li>
                </ul>
              </div>
            </div>
            <div
              v-if="caseList.mp_article.images && caseList.mp_article.images.length > 0"
              class='flex_start flex_warp'
            >
              <template v-for='(item,index) in caseList.mp_article.images'>
                <div
                  v-if='index<3'
                  :key='index'
                  class='second_article_smallImg themeBorderRadius'
                >
                  <img :src='$tool.compressImg(item,220,124)' class='img_cover'/>
                </div>
              </template>
            </div>
            <div
              v-else
              class='flex_start flex_warp'
            >
                <div class='second_article_smallImg themeBorderRadius'>
                  <img :src='$tool.compressImg(caseList.mp_article.cover,220,124)' class='img_cover'/>
                </div>
            </div>
            <div class='Author_info'>
              <div class='left'>
                <svg-icon
                  v-if="caseList.mp_article.creator.avatarAddress === null || caseList.mp_article.creator.avatarAddress === ''"
                  className='imgbox_amll cursor'
                  iconClass='signinavatar'
                ></svg-icon>
                <img
                  v-else
                  :src='caseList.mp_article.creator.avatarAddress'
                  class='img_cover'
                />
              </div>
              <div class='right'>
                <p class='user'>
                  <span class='fontSize14' v-html='caseList.mp_article.creator.realName'></span>
                  <span v-if='false' class='follow'>+ 关注</span>
                </p>
                <div class='info'>
                  <span v-html='caseList.mp_article.creator.company'></span>
                  <div style='text-align: right'>
                    <span>{{
                        timeStamp.timestampFormat(Date.parse(caseList.mp_article.publishTime.replace(/-/g, '/')) / 1000)
                      }}</span>
                    <span>{{ caseList.mp_article.showViews }}阅读</span>
                    <span>{{ caseList.mp_article.showComments }}评论</span>
                    <span>{{ caseList.mp_article.showDiggs }}点赞</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CaseItem',
  props: {
    caseList: {
      required: true
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
