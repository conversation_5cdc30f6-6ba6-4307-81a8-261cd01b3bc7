// 病例
.case_consulting-item:hover {
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
}

.case_consulting-item {
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s;
  padding: 10px 20px 10px 10px;
  box-sizing: border-box;

  .first_article {
    .item-left {
      margin-right: 10px;
      width: 220px;
      overflow: hidden;
      height: 124px;

      img {
        display: block;
        transition: all .3s ease-in 0s;
      }
    }

    .item-right {
      flex-shrink: 0;
      width: calc(100% - 230px);
      box-sizing: border-box;

      .pone {
        font-size: 18px;
        line-height: 22px;

        .poneIcon {
          min-width: 16px;
          min-height: 16px;
          max-width: 16px;
          max-height: 16px;
          vertical-align: middle;
        }
      }

      .lable-box {
        display: flex;
        justify-items: flex-start;
        flex-wrap: wrap;
        margin-top: 10px;

        .lable-item_product {
          background: #F6F6F6 !important;
          color: #0CA92E !important;
        }

        .lable-item {
          display: inline-block;
          border-radius: 6px;
          background: #f0f9ff;
          font-size: 12px;
          color: #0a83ce;
          line-height: 22px;
          padding: 0px 9px;
          margin-right: 10px;
          margin-bottom: 10px;

          .productIcon {
            margin-right: 6px;
          }
        }
      }

      .lable_time {
        text-align: right;

        .headName {
          font-size: 12px;
          color: #708AA2;
          margin-left: 4px;
        }

        .article_headimage {
          flex-shrink: 0;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          overflow: hidden;

          &:first-child {
            margin-left: 0;
          }

          margin-left: -8px;
        }

        span {
          font-size: 12px;
          color: #8a8a8a;
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    a {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  .second_article {
    .second_article_smallImg {
      width: 220px;
      height: 124px;
      margin-right: 10px;
      margin-bottom: 18px;
      overflow: hidden;

      img {
        display: block;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .second_article_smallbox {
      .pone {
        font-size: 18px;
        line-height: 22px;
        margin-top: 5px;

        .poneIcon {
          min-width: 16px;
          min-height: 16px;
          max-width: 16px;
          max-height: 16px;
          vertical-align: middle;
        }
      }

      .lable-box {
        display: flex;
        justify-items: flex-start;
        flex-wrap: wrap;
        margin-top: 12px;

        .lable-item {
          border-radius: 11px;
          background: #f0f9ff;
          font-size: 12px;
          color: #0a83ce;
          line-height: 22px;
          padding: 0 9px;
          margin-right: 10px;
          margin-bottom: 10px;

          .productIcon {
            margin-right: 6px;
          }

          .lable-item_small {
            border-radius: 11px;
            background: #f0f9ff;
            font-size: 12px;
            color: #0a83ce;
            line-height: 22px;
            padding: 0 9px;
            margin-right: 10px;
            margin-bottom: 14px;
          }
        }
      }

      .lable_time {
        text-align: right;

        span {
          font-size: 12px;
          color: #8a8a8a;
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .Author_info {
      display: flex;
      justify-items: flex-start;
      align-items: center;
      width: 100%;

      .left {
        width: 28px;
        height: 28px;
        max-width: 28px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 8px;

        .imgbox_amll {
          width: 28px;
          height: 28px;
          max-width: 28px;
          border-radius: 50%;
          margin-right: 8px;
        }
      }

      .right {
        width: 95%;

        .user {
          display: flex;
          align-items: center;
          font-size: 16px;
          color: #333333;
          cursor: pointer;

          .follow {
            display: inline-block;
            font-size: 8px;
            border-radius: 6px;
            background: #0581ce;
            color: white;
            padding: 1px 4px;
            margin-left: 8px;
            transform: scale(0.9);
          }
        }

        .info {
          font-size: 12px;
          color: #8a8a8a;
          margin-top: 3px;

          div {
            float: right;

            span {
              margin-right: 10px;

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
      }
    }
  }
}

#productItem {
  background: #F6F6F6 !important;
  color: #0CA92E !important;
}

.lable-label {
  display: inline-block !important;
}
