<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{name:"index-info-detail",query:{id}}' class='pgc_link'>
      <div class='image'>
        <img class='img_cover' :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
             :alt='title' />
        <ArticleType v-if='isLabelShow' type='资讯' />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 20px;height: 20px"/>
            <span>{{ title }}</span>
          </p>
          <p class='ugc__label__wrapper text-limit-2'>
          <span v-for='item in subspecialtys' :key='item.id' class='ugc__label__label'>
            <template v-for='itemChildren in item.children'>
              {{ item.name }}-{{ itemChildren.name }}
            </template>
          </span>
            <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
            <svg-icon class-name='productIcon' icon-class='product2' />
            <span>{{ item.name }}</span>
          </span>
          </p>
        </div>
        <div class='author_wrapper'>
          <div class='author_content'>
            <div
              v-for='item in authorList.slice(0,3)'
              :key='item.id'
              class='image'>
              <img
                v-if='item.userAvatar'
                class='img_cover'
                :src='item.userAvatar'
                :alt='item.authorName'>
              <svg-icon
                v-else
                class-name='author__img'
                icon-class='signinavatar'
              />
            </div>
            <span class='author_name'>{{
                authorList.length > 1 ?
                  `${authorList[0].authorName}等${authorList.length}位作者`
                  :
                  authorList.length === 1 ?
                    `${authorList[0].authorName}`
                    :
                    ''
              }}</span>
          </div>
          <div class='article_info'>
            <span class='article_date'>{{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}</span>
            <span class='article_read'>{{ articleRead }}阅读</span>
            <span class='article_comment'>{{ articleComment }}评论</span>
            <span class='article_fabulous'>{{ articleFabulous }}点赞</span>
          </div>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../ArticleType/index.vue'

export default {
  name: 'PgcItem',
  components: { ArticleType },
  props: {
    id: {},
    isLabelShow: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {
      type: String,
      default: ''
    },
    subspecialtys: {
      type: Array,
      default: () => []
    },
    productList: {
      type: Array,
      default: () => []
    },
    authorList: {
      type: Array,
      default: () => []
    },
    authorName: {
      type: String,
      default: '作者名字--'
    },
    articleDate: {
      default: ''
    },
    articleRead: {
      type: Number,
      default: -1
    },
    articleComment: {
      type: Number,
      default: -1
    },
    articleFabulous: {
      type: Number,
      default: -1
    },
    bmsAuth:{}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
