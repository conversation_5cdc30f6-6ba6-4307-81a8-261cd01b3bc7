<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{path:"/cloudclassroomCourse",query:{ courseId: id }}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title' />
        <ArticleType v-if='isLabelShow' type='云课堂'/>
      </div>
      <div class='pgc_info'>
          <p class='pgc_title text-limit-2'>
            <span v-html="title"></span>
          </p>
          <p class='ugc__label__wrapper'>
              <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
                <svg-icon class-name='productIcon' icon-class='product2' />
                <span>{{ item.name }}</span>
              </span>
          </p>
          <p class='article_info'>
            <span class='author_name'>{{ authorName }}</span>
            <span class='broadcast'>{{broadcastNum}}次播放</span>
            <span class='grade'>{{score}}分</span>
            <span class='price'>
              <span v-if='!money'>免费</span>
              <span v-else class='no_free'>
                <span class='fontSize12'>￥</span>
                <span class='fontSize16'>{{money}}</span>
              </span>
            </span>
          </p>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../ArticleType/index.vue'

export default {
  name: 'CloudClassroomItem',
  components: { ArticleType },
  props: {
    id: {},
    isLabelShow:{
      type:Boolean,
      default:true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    img: {
      type: String,
      default: ''
    },
    subspecialtys: {
      type: Array,
      default: () => []
    },
    productList: {
      type: Array,
      default: () => []
    },
    authorName:{},
    broadcastNum:{},
    score:{},
    money:{}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
