<template>
  <li class='pgc_wrapper'>
    <nuxt-link :to='{ path: `/meeting/detail`,query:{id:meetingId,agendaId : agendaId,fieldsId:fieldsId } }'
               target='_blank'
               class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='meetingTitleImage ? $tool.compressImg(meetingTitleImage,220,124) : require("/assets/images/default16.png")'
          :alt='meetingName' />
        <LiveState :state='meetingLiveStatus'></LiveState>
      </div>
      <div class='pgc_info'>
        <p class='pgc_title text-limit-2' v-html="meetingName"></p>
        <p class='recording__item'>
          <span class='time'>{{ meetingDateStr }}</span>
          <span class='city'>{{ meetingAddress }}</span>
        </p>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import LiveState from '@/components/LiveState/LiveState'

export default {
  name: 'InfoItemDepartment',
  components: {
    LiveState
  },
  props: {
    agendaId: {},
    fieldsId: {},
    meetingId: {
      type: Number
    },
    meetingName: {
      type: String,
      default: '--会议名称'
    },
    meetingTitleImage: {
      type: String,
      default: ''
    },
    meetingLiveStatus: {
      type: String,
      default: 'LE'
    },
    meetingDateStr: {
      type: String,
      default: ''
    },
    meetingAddress: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
