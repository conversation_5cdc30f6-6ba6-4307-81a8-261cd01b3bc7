<template>
  <div class="course_item_wrapper" @click="jumpPageHandler">
    <div class="image">
      <img class="img_cover" :src="cover ? $tool.compressImg(cover,186,186) : require('/assets/images/default16.png')" alt="">
    </div>
    <div class="content">
      <p class="title text-limit-2">{{name}}</p>
    </div>
    <div class="hover_big_wrapper">
      <div class="hover_content">
        <p class="title text-limit-2">{{name}}</p>
        <div class="label_wrapper text-limit-1">
        <span
          v-for="(item) in subspecialties"
          :key="item.id"
          class="label">
          #{{item.name}}
        </span>
        </div>
        <div class="coures_price_wraooer">
          <MallPrice
            :price='money'
            :color='"#0581CE"'
            font-size='18px'
            decimal-font-size='14px'
            unit-font-size='14px'
          />
          <MallPrice
            v-if="activity"
            :price='activity'
            :color='"#999999"'
            font-size='14px'
            decimal-font-size='12px'
            unit-font-size='14px'
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MallPrice from "../../../page-components/mall/MallPrice/index.vue";

export default {
  name: "CourseItem",
  props:["name","cover","subspecialties","type","money","courseId","activity"],
  components: {MallPrice},
  methods:{
    jumpPageHandler(){
      this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: this.courseId } })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
