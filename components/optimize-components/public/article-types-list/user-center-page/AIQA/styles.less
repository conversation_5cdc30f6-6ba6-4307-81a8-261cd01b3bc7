.q_a_article_wrapper {
  padding: 10px 16px;

  .title {
    color: #202020;
    font-size: 18px;
    line-height: 150%;
    margin-bottom: 10px;

    .label {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-top: 2px;
      margin-right: 2px;
      background-image: url("~assets/images/ai/ai_cicon.png");
      background-size: 100%;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

  .content {
    margin-bottom: 10px;

    .time {
      color: #666;
      font-size: 12px;
      margin-right: 10px;
      line-height: 150%;
    }

    .author {
      color: #708AA2;
      font-size: 12px;
      margin-right: 10px;
      line-height: 150%;
    }
  }

  .qa_desc {
    color: #999;
    font-size: 12px;
    line-height: 150%;
  }

  &:hover {
    background: #FAFCFE;

    .title {
      color: var(--theme-color);
      cursor: pointer;
    }
  }
}
