<template>
  <div class='q_a_article_wrapper' @click="jumpPageHandler">
    <div class='title text-limit-2'>
      <svg-icon
        class-name='ai_small_icon'
        icon-class='ai_small_icon'
      ></svg-icon>
      {{question}}
    </div>
    <div class='content'>
      <span class='time'>
        {{timeStamp.timestamp_13(publishTime,'y-m-d')}}
      </span>
      <span class='author'>
        脑医汇AI问答助手
      </span>
    </div>
    <div class='qa_desc text-limit-2' v-if="regexText">
      {{regexText || "暂无回答"}}
    </div>
  </div>
</template>

<script>
export default {
  name: 'AIQA',
  props:['ids', "question","publishTime","regexText"],
  methods:{
    jumpPageHandler(){
      window.open('/askAI/char/home?chatId=' + this.ids, '_blank')
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
.ai_small_icon {
  width: 18px;
  height: 18px;
}
</style>
