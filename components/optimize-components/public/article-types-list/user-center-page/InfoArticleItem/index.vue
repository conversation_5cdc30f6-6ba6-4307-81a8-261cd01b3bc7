<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{name:"index-info-detail",query:{id}}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title' />
        <ArticleType v-if='isLabelShow' :type='showType ? showType : "文章"' />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 18px;height: 18px"/>
            <span>{{ title }}</span>
          </p>
          <p class='article_info'>
            <span class='article_date'>
              {{ publishTime }}
            </span>
            <span class='article_province'>
              <span> {{ authorNames}}</span>
            </span>
          </p>
          <p class='desc text-limit-2'>
            {{metaDescription}}
          </p>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'InfoArticleItem',
  components: { ArticleType },
  props: {
    showType:{},
    id: {},
    essences: {},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    publishTime: {},
    authorNames: {},
    img: {
      type: String,
      default: ''
    },
    metaDescription:{},
    bmsAuth:{}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
