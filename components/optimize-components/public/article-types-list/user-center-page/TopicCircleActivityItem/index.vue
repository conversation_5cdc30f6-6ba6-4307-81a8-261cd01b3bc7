<template>
  <div id="topic_circle_wrapper_activity" @click="jumpPageHandler">
    <h3>
      <img :src="require('/assets/images/topic_circle/icon_activity.png')" alt="">
      <span>{{title}}</span>
    </h3>
    <p>{{description}}</p>
  </div>
</template>

<script>
  export default {
    name: 'topicCircleWrapperActivity',
    components: {},
    props:["id","title","description"],
    methods: {
      jumpPageHandler(){
        window.open(`/topic-circle/activitydetail?id=${this.id}`, '_blank')
      }
    },
  }
</script>

<style scoped lang="less">
  #topic_circle_wrapper_activity {
    display: flex;
    flex-direction: column;
    padding: 8px 16px;
    cursor: pointer;
    &:hover{
      background: #FAFCFE;
      h3{
        color:var(--theme-color)!important;
      }
    }

    h3{
      font-size: 18px;
      img{
        width: 18px;
        height: 18px;
        border-radius: 50%;
      }
    }
    p{
      margin-top: 10px;
      font-size: 12px;
      color: #666;
      word-break: break-all;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
    }
  }
</style>
