// 云课堂
.recording_list_box {
  .recording_list {
    border-radius: 6px;
    padding: 10px 16px;

    .recording_list_left {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      flex-shrink: 0;
    }

    .recording_list_right {
      margin-left: 10px;

      .recording_list_right_title {
        color: #333;
        margin-bottom: 12px;
      }

      .recording_list_right_time {
        color: #708aa2;
        font-size: 12px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        .userName {
          margin-left: 15px;
        }

        .class-span-right {
          margin-right: 15px;
        }

        .class-item-price {
          color: #FF8C03;
          font-size: 14px;
          display: flex;
          align-items: center;
        }
        .class-item-price-discount{
          margin-left: 8px;
          text-decoration-line: line-through;
          color: rgb(153, 153, 153);
          font-size: 12px;
          display: flex;
          align-items: center;
        }
      }

      .class-lable-ul {
        margin-bottom: 4px;

        .class-lable-item {
          padding: 4px 11px;
          background: #F0F9FF;
          margin: 0 6px 6px 0;
          border-radius: 11px;
        }
      }

      .live_list_box {
        .live_list {
          background: #f1f5f9;
          padding: 10px 18px 10px 20px;

          &:nth-child(even) {
            background: #fbfbfb;
          }

          &:hover {
            background: #fbfbfb;
            cursor: pointer;

            .live_left {
              .live_title,
              .live_author {
                color: #0581ce;
              }
            }
          }

          .live_left {
            .live_title {
              color: #404040;
              margin-bottom: 5px;
              line-height: 24px;
            }

            .live_author {
              color: #708aa2;
            }
          }

          .live_right {
            width: 20px;

            .play-name, .svg-icon {
              width: 21px;
              height: 21px;
            }
          }
        }

        .open_more {
          text-align: center;
          margin-top: 15px;
          cursor: pointer;
        }
      }
    }
  }

  .desc_content{
    color: #999;
    font-size: 12px;
    line-height: normal;
  }
  &:hover{
    background: #FAFCFE;
    .recording_list_right_title{
      color: var(--theme-color)!important;
    }
  }
}
