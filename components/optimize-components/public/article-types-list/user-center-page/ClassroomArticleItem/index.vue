<template>
  <div :class="['ClassroomItem', 'cursor', className ? className : '']" @click='jumpCurriculumFun(id)'>
    <div class='recording_list_box'>
      <div class='recording_list flex_start'>
        <div class='recording_list_left'>
          <img
            :src='cover ? $tool.compressImg(cover,220,124) : require("/assets/images/default16.png")'
            class='img_cover'
           :alt="name"/>
          <ArticleType v-if='isLabelShow' :type='showType ? showType : "云课堂"' />
        </div>
        <div class='recording_list_right'>
          <p
            class='recording_list_right_title fontSize18 text-limit-1'>{{name}}</p>
          <p class='recording_list_right_time fontSize12'>
            <span
              class='class-span-right class-item-name'>
              {{speakerNames}}
            </span>
            <span class='class-span-right class-item-playnumber'>{{  $tool.formatterNum(showViews) }}次播放</span>
            <span class='class-span-right class-item-fraction'>{{ score ? score.toFixed(1) : 0 }}分</span>
            <span
              v-if="money === '' || !money"
              class='class-item-price'>
             免费
            </span>
            <span
              v-else
              class='class-item-price'>
              <span style="font-size: 14px">¥</span>{{ discount || money}}
            </span>
            <span
              v-if="discount"
              class='class-item-price-discount'>
              <span style="font-size: 14px">¥</span>{{ money }}
            </span>
          </p>
          <!-- 云课堂标签 -->
          <div class='class-lable-ul flex_start flex_warp'>
            <div
              v-for='(item,index) in subspecialties'
              :key='index'
              class='class-lable-item themeFontColor fontSize12'>
              <span>{{ item.name }}</span>
            </div>
          </div>
          <div v-if="!hideIntroduction" class="desc_content text-limit-2">
            {{introduction || "暂无描述"}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Cookie from 'js-cookie'
import ArticleType from "../../../../ArticleType/index.vue";
import { loginByToken } from '@/api/login'
import env from '@/env-module'

export default {
  name: 'ClassroomArticleItem',
  components: {ArticleType},
  props:["id","cover","name","speakerNames","showViews","score","money","subspecialties","introduction","showType","isLabelShow","discount", "hideIntroduction", "className"],
  data() {
    return {
      maxDisplay: 3// 最多展示
    }
  },
  methods: {
    jumpCurriculumFun(fatherId, id) {
      /**
       *@author:Rick  @date:2022/6/29 17:50  @method:jumpCurriculumFun
       *@desc: 跳转云课堂
       */
      this.$axios.$request(loginByToken({
        token: this.$store.state.auth.token
      })).then(res => {
        /**
         * 不管有没有登录 都跳到云课堂 如果登录 用于写cookie的
         * @type {string|string|*}
         */
        if (res.code === 1) {
          Cookie.set('medtion_member_id', this.$store.state.auth.user.id, { sameSite: 'lax' })
          window.open(env[process.env.NODE_ENV].ENV_NEW_URL + 'ocs/index.html#/course/' + fatherId + '?curriculum=' + id, '_blank')
        } else {
          window.open(env[process.env.NODE_ENV].ENV_NEW_URL + 'ocs/index.html#/course/' + fatherId + '?curriculum=' + id, '_blank')
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
