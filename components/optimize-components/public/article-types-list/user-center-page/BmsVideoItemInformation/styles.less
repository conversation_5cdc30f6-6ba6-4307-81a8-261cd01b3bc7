.pgc_wrapper {
  .pgc_link {
    display: flex;
    grid-gap: 0 10px;
    padding: 10px 16px;

    .image {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;

      .play_img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 34px;
        height: 34px;
      }
    }

    .pgc_info {
      width: calc(100% - (220px + 10px));
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .pgc_title {
        font-weight: 700;
        font-size: 18px;
        line-height: 20px;
        color: #202020;
        margin-bottom: 12px;
      }

      .ugc__label__wrapper {
        .ugc__label__product {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          padding: 0 9px;
          background: #F6F6F6;
          border-radius: 11px;
          font-size: 12px;
          color: #0CA92F;
          margin-right: 10px;
          margin-bottom: 10px;

          .productIcon {
            width: 12px;
            height: 12px;
            margin-right: 2px;
          }

        }
      }

      .time {
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #708AA2;
      }
    }
    &:hover{
      background: #FAFCFE;
      .pgc_title{
        color: var(--theme-color)!important;
      }
    }
  }

}
