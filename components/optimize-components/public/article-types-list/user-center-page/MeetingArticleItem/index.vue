<template>
  <div class="meeting_wrapper_item">
    <div class="image cursor" @click="jumpMeetingDetailsFun(null)">
      <img class="img_cover" :src="appMainPic ? appMainPic : require('/assets/images/default16.png')" alt="">
      <live-state :state='meetingLiveStatus'></live-state>
    </div>
    <div class="info">
      <p class="title text-limit-2 cursor" @click="jumpMeetingDetailsFun(null)">{{ meetingName }}</p>
      <div class="content">
        <span class="time">{{ meetingDateStr }}</span>
        <span class="address">{{ meetingAddress }}</span>
      </div>
      <ul class='live_list_box'>
        <li
          v-for='(itemList, index) in scheduleList'
          :key='itemList.id'
          v-show='expanMap[id] ? index<1000 : index<3'
          class='live_list flex_between flex_align_center'
          @click="jumpMeetingDetailsFun(itemList.id)"
        >
          <div class='live_left'>
            <p class='live_title fontSize16'>
              {{ itemList.theme }}
            </p>
            <p class='live_author fontSize12'>{{
                itemList.customName ? itemList.customName : '讲者'
              }}：{{ itemList.authorNames }}</p>
          </div>
          <div class='live_right'>
            <SummaryPopover
              v-if="summaryShow === 1"
              placement="right"
              :meeting-name="meetingName"
              :meeting-id="meetingId"
              :agenda-name="itemList.theme"
              :agenda-id="itemList.id"
            >
              <div class="flex_between flex_align_center cursor" :style="{height:'20px'}">
                <svg-icon
                  className='summary_svg cursor'
                  iconClass='summary'
                ></svg-icon>
                <span
                  style="background: linear-gradient(180deg,#48a7ff,#532ee4);background-clip: text;-webkit-background-clip: text;-webkit-text-fill-color: transparent;font-size: 14px;margin-left: 4.5px;line-height: 21px">摘要</span>
              </div>
            </SummaryPopover>
            <svg-icon icon-class='play' icon-name='play-name'></svg-icon>
          </div>
        </li>
        <li
          v-if='scheduleList.length>3'
          class='open_more fontSize12 themeFontColor'
          @click.stop='expanMap[id]?collapse(id):expand(id)'>
          {{ expanMap[id] ? '收回' : '展开更多' }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import LiveState from "../../../../../LiveState/LiveState.vue";
import SummaryPopover from "../../../../../../opt-components/popover/SummaryPopover.vue";

export default {
  name: "MeetingArticleItem",
  components: {SummaryPopover, LiveState},
  props: ["meetingName", "appMainPic", "meetingAddress", "meetingDateStr", "meetingId", "scheduleList", "id", "meetingLiveStatus", "fieldsId", "theme", "summaryShow"],
  data() {
    return {
      expanMap: {}      // 记录日程展开收回的id
    }
  },
  methods: {
    expand(data) {
      /**
       *@author:Rick  @date:2022/6/29 16:47  @method:expand
       *@desc: 日程展开 添加
       */
      this.$set(this.expanMap, data, 1)
    },
    collapse(data) {
      /**
       *@author:Rick  @date:2022/6/29 16:47  @method:collapse
       *@desc: 日程收回 删除
       */
      this.$delete(this.expanMap, data)
    },
    jumpMeetingDetailsFun(agendaId) {
      if (event.target.id === "summary_popover_btn") {
        return;
      }
      /**
       *@author:Rick  @date:2022/6/29 16:44  @method:jumpMeetingDetailsFun
       *@desc: 跳转会议详情
       */
      const {href} = this.$router.resolve({
        path: `/meeting/detail`,
        query: {id: this.meetingId, fieldsId: this.fieldsId, agendaId: agendaId}
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
