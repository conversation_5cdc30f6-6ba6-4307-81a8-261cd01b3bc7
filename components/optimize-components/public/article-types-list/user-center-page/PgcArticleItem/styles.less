.pgc_wrapper {
  border-radius: 6px;
  padding: 10px 16px;
  cursor: pointer;

  &:hover {
    background: #FAFCFE;
    .pgc_title{
      color: var(--theme-color)!important;
    }
  }

  .pgc_link {
    display: flex;
    grid-gap: 0 10px;

    .image {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
    }

    .pgc_info {
      width: calc(100% - (220px + 10px));
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .pgc_title {
        font-size: 18px;
        line-height: 1.5;
        color: #202020;
        margin-bottom: 10px;
      }

      .ugc__label__wrapper {

        .ugc__label__label {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          padding: 0 9px;
          background: #F0F9FF;
          border-radius: 11px;
          font-size: 12px;
          color: #0A83CE;
          margin-right: 10px;
          margin-bottom: 14px;
        }

        .ugc__label__product {
          display: inline-block;
          height: 22px;
          line-height: 22px;
          padding: 0 9px;
          background: #F6F6F6;
          border-radius: 11px;
          font-size: 12px;
          color: #0CA92F;
          margin-right: 10px;
          margin-bottom: 10px;

          .productIcon {
            width: 12px;
            height: 12px;
            margin-right: 2px;
          }

        }
      }

      .author_wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .author_content {
          display: flex;
          align-items: center;

          .image {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 4px;

            .author__img {
              width: 100%;
              height: 100%;
              vertical-align:unset;
            }

            &:first-child {
              margin-left: 0;
            }

            margin-left: -8px;
          }

          .author_name {
            font-size: 12px;
            line-height: 12px;
            color: #708AA2;
          }
        }

        .article_info {
          display: flex;
          flex-shrink: 0;
          grid-gap: 0 10px;
          padding-right: 5px;

          span {
            font-size: 12px;
            line-height: 12px;
            color: #708AA2;
          }
        }
      }
    }
  }

}

.edit_button{
  cursor: pointer;
  background: #708AA2;
  border-radius: 6px;
  padding: 2px 8px 2px 9px;
  color: #FFFFFF;
  font-weight: 400;
  font-size: 13px;
  line-height: 14px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;

  .icons{
    width: 13px;
    height: 13px;margin-right: 3px;
  }
}
