<template>
  <li class='pgc_wrapper'>
    <div @click="jumpPageHandler" class='pgc_link'>
      <div class='image'>
        <img class='img_cover' :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
             :alt='title' />
        <ArticleType
          v-if='isLabelShow'
          :type='type === "1" ? "随笔" : type === "2" ? "文章" : type === "3" ? "科普患教" : "病例"'
        />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 18px;height: 18px"/>
            <span>{{ title }}</span>
          </p>
          <p class='ugc__label__wrapper text-limit-2'>
          <span v-for='item in subspecialtys' :key='item.id' class='ugc__label__label'>
            <template v-for='itemChildren in item.children'>
              {{ item.name }}-{{ itemChildren.name }}
            </template>
          </span>
            <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
            <svg-icon class-name='productIcon' icon-class='product2' />
            <span>{{ item.name }}</span>
          </span>
          </p>
        </div>
        <div class='author_wrapper'>
          <div class='author_content'>
            <span class='author_name'>{{authorName}}</span>
          </div>
          <div v-if="isEdit && $store.state.auth.user.id+'' === $route.query.profileUserId+''" class="edit_button"  @click.stop='jumpCasePage(id,type)'>
            <svg-icon icon-class='edit_' class-name='icons' />
            <span>编辑</span>
          </div>
          <div v-else class='article_info'>
            <span class='article_date'>{{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}</span>
            <span class='article_read'>{{ $tool.formatterNumUnit(articleRead)}}阅读</span>
            <span class='article_comment'>{{ articleComment }}评论</span>
            <span class='article_fabulous'>{{ articleFabulous }}点赞</span>
          </div>

        </div>
      </div>
    </div>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'
import {loginByToken} from "../../../../../../api/login";

export default {
  name: 'PgcArticleItem',
  components: { ArticleType },
  props: {
    id: {},
    isEdit:{},
    type:{},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    img: {
      type: String,
      default: ''
    },
    subspecialtys: {
      type: Array,
      default: () => []
    },
    productList: {
      type: Array,
      default: () => []
    },
    authorName: {
      type: String,
      default: ''
    },
    articleDate: {
      default: ''
    },
    articleRead: {
      type: Number,
      default: 0
    },
    articleComment: {
      type: Number,
      default: 0
    },
    articleFabulous: {
      type: Number,
      default: 0
    },
    paragraphType:{},
    bmsAuth:{}
  },
  methods:{
    jumpPageHandler(){
      const { href } = this.$router.resolve(   {path:`/case/detail-ugc?id=${this.id}`})
      window.open(href, '_blank')
    },
    jumpCasePage(id,type) {
      const localToken = this.$cookies.get('medtion_token_only_sign')
      if (localToken) {
        this.$axios.$request(loginByToken({
          token: localToken
        })).then(res => {
          let url;
          if(this.paragraphType === "M"){
            url = `/publish/case/update?platform=F&id=${id}`
          }else{
            url = `/editor?type=${type}&id=${id}`
          }
          window.open(url)
        })
      } else {
        this.$toast('请先登录')
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({ name: 'signin', query: { fallbackUrl: url } })
      }
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
