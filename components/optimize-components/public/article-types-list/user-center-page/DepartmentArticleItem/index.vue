<template>
  <li class='department__item' @click='jumpDepartmentDetailHandler(id)'>
    <div class='image'>
      <img class='img_cover' :src='img ? $tool.compressImg(img,58,58) : require("/assets/images/default1.png")'
           alt='' />
    </div>
    <div class='depart__item__info'>
      <p class='title text-limit-1'>
        {{ company }}
      </p>
      <p class='company__info'>
              <span class='company__department'>
                {{ cmsDepartment }}
              </span>
        <span class='company__authentication'>
                {{ memberTotal }}位已认证成员
              </span>
      </p>
    </div>
    <UserFollowButton
      :id="id"
      :is-follow='isSubscribe'
      @follow='followHandler(id)'
    />
  </li>
</template>

<script>
import UserFollowButton from "../../../../page-components/user-center/UserFollowButton/index.vue";
import { collectDepartment } from '../../../../../../api/department'

export default {
  name: 'DepartmentArticleItem',
  components: {
    UserFollowButton
  },
  props: {
    id: {
      type: Number
    },
    img: {
      type: String,
      default: ''
    },
    company: {
      type: String,
      default: '-公司名称'
    },
    cmsDepartment: {
      type: String,
      default: '-部门名称'
    },
    memberTotal: {
      type: Number,
      default: 0
    },
    isSubscribe: {
      default: false
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-14 15:23
     * @description: 关注科室
     * ------------------------------------------------------------------------------
     */
    followHandler(departmentId) {
      this.$axios.$request(collectDepartment({
        userId: this.$store.state.auth.user.id,
        departmentId
      })).then(response => {
        if (response.code === 1) {
          if (response.result.isSubscribe === 'true') {
            this.$analysys.follow(
              document.title,
              '科室',
              this.cmsDepartment
            )
            this.$toast('关注成功')
          }
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-16 10:42
     * @description: 跳转科室详情
     * ------------------------------------------------------------------------------
     */
    jumpDepartmentDetailHandler(id) {
      const routeUrl = this.$router.resolve({
        name: 'index-department-detail',
        query: {
          departmentId: id
        }
      })
      window.open(routeUrl.href, '_blank')
    }
  }

}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
