.department__item{
  display: flex;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer ;
  padding: 8px 16px;

  &:hover{
    background: #FAFCFE;

    .depart__item__info > .title{
      color: #0581CE;
    }
  }
  .image{
    width: 58px;
    height: 58px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
  }
  .depart__item__info{
    flex: auto;
    padding: 0 20px 0 8px;
    .title{
      font-weight: 400;
      font-size: 17px;
      line-height: 21px;
      color: #333333;
      margin-bottom: 6px;
      transition: color .3s;
    }
    .company__info{
      font-size: 13px;
      .company__department{
        color: #333333;
        margin-right: 8px;
      }
      .company__authentication{
        color: #708AA2;
      }
    }
  }

}
