<template>
  <div class="topic_circle_wrapper_top" @click="jumpPageHandler">
    <div class="left">
      <div class="image">
        <img class="img_cover" :src="image ? image : require('/assets/images/default1.png')" alt="">
      </div>
      <div class="info">
        <div class="title text-limit-1">
          <svg-icon v-if="type === 'topic'" icon-class="user-top" class-name="icons"/>
          <svg-icon v-else icon-class="user-circle" class-name="icons"/>
          <span>{{title}}</span>
        </div>
        <div class="num">
          {{contents}}内容
        </div>
        <div class="desc text-limit-1">
          {{description}}
        </div>
      </div>
    </div>
    <UserFollowButton
      :is-follow='isFollow'
      @follow='followHandler(id)'
    />
  </div>
</template>

<script>
import UserFollowButton from "../../../../page-components/user-center/UserFollowButton/index.vue";

export default {
  name: "TopicCircleArticleItem",
  components: {UserFollowButton},
  props:["id","title","image","type","contents","isFollow","description"],
  methods:{
    followHandler(){
      this.$emit("follow",this.id)
    },
    jumpPageHandler(){
      if(this.type === "circle"){
        const { href } = this.$router.resolve({ path: `/topic-circle/communitycircle?id=${this.id}` })
        window.open(href, '_blank')
      }else{
        const { href } = this.$router.resolve({ path: `/topic-circle/communitytopic?id=${this.id}` })
        window.open(href, '_blank')
      }

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
