.brand_wrapper{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;

  &:hover{
    background:#FAFCFE;

    .title{
      color: var(--theme-color)!important;
    }
  }

  .left{
    display: flex;
    padding-right: 20px;

    .image{
      flex-shrink: 0;
      width: 160px;
      height: 90px;
      border-radius: 6px;
      overflow: hidden;
      margin-right: 8px;
    }
    .info{
      .title{
        color: #333;
        font-size: 18px;
        line-height: 1.5;
        margin-bottom: 10px;
      }
      .content{
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 8px;
        .line{
          margin: 0 6px;
        }
      }
      .brand{
        display: flex;
        align-items: center;
        .brand_image{
          width: 16px;
          height: 16px;
          border-radius: 4px;
          overflow: hidden;
        }
        .brand_name{
          margin-left: 4px;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }
}
