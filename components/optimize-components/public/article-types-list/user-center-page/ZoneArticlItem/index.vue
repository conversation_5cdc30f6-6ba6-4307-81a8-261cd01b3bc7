<template>
  <div class="brand_wrapper" @click="jumpPageHandler">
    <div class="left">
      <div class="image">
        <img
          class="img_cover"
          :src="logo ? $tool.compressImg(logo,160,90) : require('/assets/images/default1.png')"
          alt="">
      </div>
      <div class="info">
        <div class="title text-limit-1">{{name}}</div>
        <div class="content">
          <span> {{$tool.formatterNumUnit(views)}}浏览</span>
          <span class="line">|</span>
          <span>{{articles}}病例</span>
        </div>
        <div v-if="brandName" class="brand cursor" @click.stop="jumpBrandPageHandler">
          <div class="brand_image">
            <img
              class="img_cover"
              :src="brandLogo ? $tool.compressImg(brandLogo,16,16) :require('/assets/images/default1.png')"
              alt="">
          </div>
          <div class="brand_name">
            {{brandName}}
          </div>
        </div>
      </div>
    </div>
    <UserFollowButton
      :is-follow='subscribeStatus === "T"'
      @follow='followHandler(id)'
    />
  </div>
</template>

<script>
import UserFollowButton from "../../../../page-components/user-center/UserFollowButton/index.vue";
export default {
  name: "ZoneArticlItem",
  components:{
    UserFollowButton
  },
  props:["id","name","logo","brandLogo","subscribeStatus","products","views","brandName","articles","brandId"],
  methods:{
    followHandler(){
      this.$emit("follow",this.id)
    },
    jumpPageHandler(){
      const { href } = this.$router.resolve({ path: `/bms/classify/3/activity-details/${this.id}` })
      window.open(href, '_blank')
    },
    jumpBrandPageHandler(){
      const { href } = this.$router.resolve({ path: `/bms/classify/-/brand-details/${this.brandId}` })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
