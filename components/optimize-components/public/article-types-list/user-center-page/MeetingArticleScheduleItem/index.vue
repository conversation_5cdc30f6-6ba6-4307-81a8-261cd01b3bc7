<template>
  <div class="meeting_wrapper_item cursor" @click="jumpMeetingDetailsFun">
    <div class="image cursor">
      <img class="img_cover" :src="appMainPic ? appMainPic : require('/assets/images/default16.png')" alt="">
      <ArticleType type="日程"/>
    </div>
    <div class="info">
      <p class="title text-limit-2 cursor" >{{meetingName}}</p>
      <div class="content">
        <span class="time">{{meetingDateStr}}</span>
        <span class="address">{{meetingAddress}}</span>
      </div>
      <p class="desc text-limit-1">
        {{description}}
      </p>
    </div>
  </div>
</template>

<script>
import ArticleType from "../../../../ArticleType/index.vue";
export default {
  name: "MeetingArticleInstitutionItem",
  components: {ArticleType},
  props:["meetingName","appMainPic","meetingAddress","meetingDateStr","meetingId","scheduleList","id","meetingLiveStatus","fieldsId","description","agendaId"],
  data() {
    return {
      expanMap: {}      // 记录日程展开收回的id
    }
  },
  methods: {
    expand(data) {
      /**
       *@author:Rick  @date:2022/6/29 16:47  @method:expand
       *@desc: 日程展开 添加
       */
      this.$set(this.expanMap, data, 1)
    },
    collapse(data) {
      /**
       *@author:Rick  @date:2022/6/29 16:47  @method:collapse
       *@desc: 日程收回 删除
       */
      this.$delete(this.expanMap, data)
    },
    jumpMeetingDetailsFun(meetingId, fieldsId) {
      /**
       *@author:Rick  @date:2022/6/29 16:44  @method:jumpMeetingDetailsFun
       *@desc: 跳转会议详情
       */
      const { href } = this.$router.resolve({ path: `/meeting/detail`, query: { id: this.meetingId,fieldsId:this.fieldsId,agendaId:this.agendaId } })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
