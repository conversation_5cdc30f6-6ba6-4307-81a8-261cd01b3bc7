<template>
  <div class="course_wrapper" @click="jumpPageHandler">
    <div class="image">
      <img class="img_cover" :src="cover ? $tool.compressImg(cover,226,127) : require('/assets/images/default16.png')" alt="">
    </div>
    <div class="content">
      <p class="title text-limit-2">
        {{name}}
      </p>
      <div class="info">
        <span class="buy">
          {{buys}}人购买
        </span>
        <MallPrice
          :price='money'
          :color='"#E7732E"'
          font-size='18px'
          decimal-font-size='18px'
          unit-font-size='14px'
        />
      </div>
    </div>
  </div>
</template>

<script>
import MallPrice from "../../../../page-components/mall/MallPrice/index.vue";

export default {
  name: "CourseArticleItem",
  components: {MallPrice},
  props:["cover","name","money","buys","courseId"],
  methods:{
    jumpPageHandler(){
      this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: this.courseId } })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
