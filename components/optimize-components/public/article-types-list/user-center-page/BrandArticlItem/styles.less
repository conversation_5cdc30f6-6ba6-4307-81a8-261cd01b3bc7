.brand_wrapper{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;

  &:hover{
    background:#FAFCFE;

    .title{
      color: var(--theme-color)!important;
    }
  }

  .left{
    display: flex;
    padding-right: 20px;

    .image{
      flex-shrink: 0;
      width: 80px;
      height: 80px;
      border-radius: 6px;
      overflow: hidden;
      margin-right: 8px;
    }
    .info{
      .title{
        color: #333;
        font-size: 18px;
        line-height: 1.5;
        margin-bottom: 10px;
      }
      .content{
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 4px;
        .line{
          margin: 0 6px;
        }
      }
      .views{
        color: #999;
        font-size: 12px;
        line-height: 1.5;
      }
    }
  }
}
