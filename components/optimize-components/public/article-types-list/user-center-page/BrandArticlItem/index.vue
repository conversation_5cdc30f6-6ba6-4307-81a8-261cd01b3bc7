<template>
  <div class="brand_wrapper" @click="jumpPageHandler">
    <div class="left">
      <div class="image">
        <img
          class="img_cover"
          :src="logo ? $tool.compressImg(logo,80,80) : require('/assets/images/default1.png')"
          alt="">
      </div>
      <div class="info">
        <div class="title text-limit-1">{{brandName ? brandName + " · " + name : name}}</div>
        <div class="content">
          <span>{{products}}产品</span>
          <span class="line">|</span>
          <span>{{articles}}病例</span>
        </div>
        <div class="views">
          {{$tool.formatterNumUnit(views)}}浏览
        </div>
      </div>
    </div>
    <UserFollowButton
      :is-follow='subscribeStatus === "T"'
      @follow='followHandler(id)'
    />
  </div>
</template>

<script>
import UserFollowButton from "../../../../page-components/user-center/UserFollowButton/index.vue";
export default {
  name: "BrandArticlItem",
  components:{
    UserFollowButton
  },
  props:["id","name","logo","subscribeStatus","products","views","brandName","articles","brandId"],
  methods:{
    followHandler(){
      this.$emit("follow", {id:this.id,brandId:this.brandId})
    },
    jumpPageHandler(){
      const { href } = this.$router.resolve({ path: `/bms/classify/-/product-line-details/${this.id}` })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
