<template>
  <div class="topic_circle_wrapper_item" @click="jumpPageHandler">
    <div class="left">
      <div class="info">
        <div class="title text-limit-2">
          <span>{{questionTitle}}</span>
        </div>
        <div class="desc text-limit-2">
          <div v-if="creatorName" class="image" @click.stop="jumpHomePageHandler">
            <img
              class="img_cover "
              :src="creatorAvatar ? $tool.compressImg(creatorAvatar,18,18) : require('/assets/images/user.png')" alt="">
          </div>
          <span v-if="creatorName" class="user_name"  @click.stop="jumpHomePageHandler">
            {{creatorName}}
          </span>
          <span> {{regexText || "暂无回答"}}</span>
        </div>
      </div>
    </div>
    <UserFollowButton
      :is-follow='isFollow'
      @follow='followHandler(id)'
    />
  </div>
</template>

<script>
import UserFollowButton from "../../../../page-components/user-center/UserFollowButton/index.vue";

export default {
  name: "TopicCircleTwoArticleItem",
  components: {UserFollowButton},
  props:["id","questionTitle","regexText","creatorAvatar","creatorName","creatorId","isFollow","qaDetailUrl"],
  methods:{
    followHandler(){
      this.$emit("follow",this.id)
    },
    jumpPageHandler(){
      window.open(this.qaDetailUrl, '_blank')
    },
    jumpHomePageHandler(){
      const { href } = this.$router.resolve({ path: `/user-center?profileUserId=${this.creatorId}` })
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
