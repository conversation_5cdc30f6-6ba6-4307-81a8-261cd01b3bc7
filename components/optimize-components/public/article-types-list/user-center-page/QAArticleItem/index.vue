<template>
  <div class='q_a_article_wrapper' @click="jumpPageHandler">
    <div class='title text-limit-2'>
      <span class='label'>问答</span>
      {{title}}
    </div>
    <div class='content'>
      <span class='time'>
        {{timeStamp.timestamp_13(publishTime,'y-m-d')}}
      </span>
      <span class='author'>
        {{realName}}
      </span>
    </div>
    <div class='qa_desc text-limit-2'>
      {{regexText || "暂无回答"}}
    </div>
  </div>
</template>

<script>
export default {
  name: 'QAArticleItem',
  props:["title","publishTime","realName","regexText","QAId","qaDetailUrl"],
  methods:{
    jumpPageHandler(){
      window.open(this.qaDetailUrl, '_blank')
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
