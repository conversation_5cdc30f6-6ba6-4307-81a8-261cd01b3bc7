<template>
  <div class='follow-list-li flex_between flex_align_center'>
    <template v-if="identity === 7">
      <div
        :style="{cursor:'default'}"
        class='follow-list-li-left flex_start flex_align_center'>
        <div class='image flex_shrink img_radius overflow_hidden'>
          <img
            v-if='avatarAddress'
            :src='$tool.compressImg(avatarAddress,74,74)'
            :alt='realName'
            class='img_cover'>
          <svg-icon
            v-else
            class-name='img_cover'
            icon-class='huanzhe'
          ></svg-icon>
        </div>
        <div class='user-info'>
          <p class='info-name'>匿名用户（非医务工作者）</p>
        </div>
      </div>
    </template>

    <template v-else>
      <div
        class='follow-list-li-left flex_start flex_align_center cursor'
        @click='$store.dispatch("jumpUserCenterPageHandler",id)'>
        <div class='image flex_shrink img_radius overflow_hidden'>
          <img
            v-if='avatarAddress'
            :src='$tool.compressImg(avatarAddress,74,74)'
            :alt='realName'
            class='img_cover cursor'>
          <svg-icon
            v-else
            class-name='img_cover cursor'
            icon-class='signinavatar'
          ></svg-icon>
        </div>
        <div class='user-info'>
          <p class='info-name'>{{ realName }}</p>
          <p class='info-title fontSize14'>
            {{ company }}
            <span class='title'>{{ department }}</span>
          </p>
          <p class='info-desc fontSize14'><span>创作</span><span class='info_num themeBlackColor'>{{
              userPostNum
            }}</span><span style="margin: 0 6px;font-size: 14px;color: #666666">|</span><span>粉丝</span><span
            class='info_num themeBlackColor'>{{ fans }}</span>
          </p>
        </div>
      </div>
      <div class='follow-list-li-right' @click='followFun(id)'>
        <div v-if='!isFollow' class='follow_btn flex_shrink flex_center flex_align_center themeButton'>
          <svg-icon class-name='tab_icon' icon-class='subscribe'></svg-icon>
          <span class='fontSize12'>关注</span>
        </div>
        <div
          v-else
          class='follow_btn followed_btn'
          :style="hoverShow[id] ? {color:'#999'} : {}"
          @mouseleave='collapse(id)'
          @mouseover='expand(id)'
        >
          <span class='fontSize12'>{{ hoverShow[id] ? '取消关注' : '已关注' }}</span>
        </div>
      </div>
    </template>


  </div>
</template>

<script>
export default {
  name: 'UserItem',
  props: ["id", "avatarAddress", "realName", "company", "department", "userPostNum", "fans", "isFollow", "identity"],
  data() {
    return {
      hoverShow: {}    //  是否移入
    }
  },
  methods: {
    expand(data) {
      this.$set(this.hoverShow, data, 1)
    },
    collapse(data) {
      this.$delete(this.hoverShow, data)
    },
    followFun(id) {
      this.$emit('followFun', id)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
