<template>
  <div class='q_a_article_wrapper' @click="jumpPageHandler">
    <div class="left_image cursor">
      <div class="image">
        <span class='label'>问答</span>
        <img class="img_cover" :src="$tool.compressImg(images,220,124)" alt="">
      </div>
    </div>
    <div class="content">
      <div class='title text-limit-2'>
        {{title}}
      </div>
      <div class='content'>
      <span class='time'>
        {{timeStamp.timestamp_13(publishTime,'y-m-d')}}
      </span>
        <span class='author'>
        {{realName}}
      </span>
      </div>
      <div class='qa_desc text-limit-2'>
        {{regexText || "暂无回答"}}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QADefaultArticleItem',
  props:["title","publishTime","realName","regexText","QAId","images","qaDetailUrl"],
  methods:{
    jumpPageHandler(){
      window.open(this.qaDetailUrl, '_blank')
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
