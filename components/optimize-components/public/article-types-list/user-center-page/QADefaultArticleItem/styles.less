.q_a_article_wrapper {
  padding: 10px 16px;
  display: flex;
  cursor: pointer;

  .left_image{
    flex-shrink: 0;
    margin-right: 10px;
    .image{
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
      position: relative;

      .label {
        position: absolute;
        right: 4px;
        top: 4px;
        height: 17px;
        line-height: 17px;
        border-radius: 6px;
        background: rgb(18, 197, 236);
        padding: 0 6px;
        color: #FFF;
        font-size: 12px;
        margin-right: 2px;
      }
    }
  }

  .title {
    color: #202020;
    font-size: 18px;
    line-height: 150%;
    margin-bottom: 10px;


  }

  .content {
    margin-bottom: 10px;

    .time {
      color: #666;
      font-size: 12px;
      margin-right: 10px;
      line-height: 150%;
    }

    .author {
      color: #708AA2;
      font-size: 12px;
      margin-right: 10px;
      line-height: 150%;
    }
  }

  .qa_desc {
    color: #999;
    font-size: 12px;
    line-height: 150%;
  }

  &:hover {
    background: #FAFCFE;

    .title {
      color: var(--theme-color);
      cursor: pointer;
    }
  }
}
