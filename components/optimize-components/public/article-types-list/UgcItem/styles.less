.ugl__wrapper {
  border-radius: 6px;
  padding: 16px 10px;
  transition: all .3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }

  .ugc__title {
    font-weight: 700;
    font-size: 18px;
    line-height: 18px;
    color: #202020;
    margin-bottom: 12px;
  }

  .ugc__label__wrapper {
    display: flex;
    grid-gap: 0 10px;
    flex-wrap: wrap;
    margin-bottom: 7px;

    .ugc__label__label {
      display: inline-block;
      height: 22px;
      line-height: 22px;
      padding: 0 9px;
      background: #F0F9FF;
      border-radius: 11px;
      font-size: 12px;
      color: #0A83CE;
      margin-bottom: 7px;
    }

    .ugc__label__product {
      display: inline-block;
      height: 22px;
      line-height: 22px;
      padding: 0 9px;
      background: #F6F6F6;
      border-radius: 11px;
      font-size: 12px;
      color: #0CA92F;
      margin-bottom: 7px;

      .productIcon {
        width: 12px;
        height: 12px;
        margin-right: 2px;
      }

    }
  }

  .ugc__images {
    display: flex;
    justify-items: flex-start;
    grid-gap: 0 10px;
    margin-bottom: 18px;

    .image {
      width: 28.46054333%;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
    }
  }

  .ugc__author__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: end;

    .author_info_content {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      grid-gap: 0 8px;

      .image {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        overflow: hidden;
      }

      .author__img {
        width: 28px;
        height: 28px;
      }

      .author_info {
        .author_info_title {
          display: flex;
          align-items: center;
          grid-gap: 0 8px;

          .author_name {
            font-weight: 700;
            font-size: 14px;
            line-height: 12px;
            color: #333333;
          }

          .follow {
            height: 16px;
            line-height: 16px;
            background: #0581CE;
            border-radius: 100px;
            color: #FFFFFF;
            font-size: 8px;
            transition: all .3s;
            padding: 0 3px;

            .follow_span {
              display: inline-block;
              transform: scale(.8);
            }
          }

          .follow_ok {
            background: rgba(0, 0, 0, 0.15);
          }
        }

        .author_company {
          font-size: 10px;
          line-height: 12px;
          color: #8A8A8A;
          margin-top: 4px;
        }
      }
    }

    .article_info {
      display: flex;
      grid-gap: 0 10px;
      padding-right: 12px;

      span {
        font-size: 12px;
        line-height: 12px;
        color: #708AA2;
      }
    }
  }
}
