<template>
  <li class='ugl__wrapper' @click='jumpPageFun(id)'>
    <p class='ugc__title text-limit-2'>
      <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 20px;height: 20px"/>
      <span>{{ title }}</span>
    </p>
    <p class='ugc__label__wrapper'>
      <span v-for='item in subspecialtys' :key='item.id' class='ugc__label__label'>
        <template v-for='itemChildren in item.children'>
          {{ item.name }}-{{ itemChildren.name }}
        </template>
      </span>
      <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
          <svg-icon class-name='productIcon' icon-class='product2' />
        <span>{{ item.name }}</span>
      </span>
    </p>
    <div class='ugc__images'>
      <div v-for='(item,index) in images.slice(0,3)' :key='index' class='image'>
        <img class='img_cover' :src='$tool.compressImg(item,220,124)' :alt='title' />
      </div>
    </div>
    <div class='ugc__author__wrapper'>
      <div class='author_info_content'>
        <div class='image'>
          <img v-if='avatarAddress' class='img_cover' :src='$tool.compressImg(avatarAddress,28,28)' alt=''>
          <svg-icon v-else class-name='author__img' icon-class='signinavatar' />
        </div>
        <div class='author_info'>
          <div class='author_info_title'>
            <p class='author_name'>
              {{ authorName }}
            </p>
            <p class='follow' :class='isFollowFlag ? "follow_ok":""' @click.stop='followHandler(authorId)'>
              <span class='follow_span'>
                <span v-if='!isFollowFlag'>+</span>
                <span>{{ isFollowFlag ? '已关注' : '关注' }}</span>
              </span>
            </p>
          </div>
          <p class='author_company'>
            {{ authorCompany }}
          </p>
        </div>
      </div>
      <div class='article_info'>
        <span class='article_date'>{{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}</span>
        <span class='article_read'>{{ articleRead }}阅读</span>
        <span class='article_comment'>{{ articleComment }}评论</span>
        <span class='article_fabulous'>{{ articleFabulous }}点赞</span>
      </div>
    </div>
  </li>
</template>

<script>
export default {
  name: 'UgcItem',
  props: {
    id: {
      type: Number
    },
    title: {
      type: String,
      default: '-UGC文章标题'
    },
    avatarAddress: {
      type: String,
      default: ''
    },
    images: {
      type: Array,
      default: () => []
    },
    subspecialtys: {
      type: Array,
      default: () => []
    },
    productList: {
      type: Array,
      default: () => []
    },
    authorId: {
      type: Number
    },
    isFollow: {
      type: Boolean,
      default: false
    },
    authorName: {
      type: String,
      default: '作者名字--'
    },
    authorCompany: {
      type: String,
      default: '作者所在公司--'
    },
    articleDate: {
      default: ''
    },
    articleRead: {
      type: Number,
      default: -1
    },
    articleComment: {
      type: Number,
      default: -1
    },
    articleFabulous: {
      type: Number,
      default: -1
    },
    bmsAuth:{}
  },
  data() {
    return {
      isFollowFlag: this.isFollow
    }
  },
  watch:{
    isFollow(newValue) {
      this.isFollowFlag = newValue
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-15 15:04
     * @description: 关注作者
     * ------------------------------------------------------------------------------
     */
    followHandler(id) {
      this.$store.dispatch('follow', id)
      this.isFollowFlag = !this.isFollowFlag
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-15 15:19
     * @description: 跳转
     * ------------------------------------------------------------------------------
     */
    jumpPageFun(id) {
      const routeUrl = this.$router.resolve({
        name: 'index-case-detail-ugc',
        query: {
          id
        }
      })
      window.open(routeUrl.href, '_blank')
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
