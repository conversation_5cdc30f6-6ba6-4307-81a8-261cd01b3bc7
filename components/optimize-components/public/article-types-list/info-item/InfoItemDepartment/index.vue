<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{name:"index-info-detail",query:{id}}' class='pgc_link'>
      <div class='image'>
        <img class='img_cover' :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
             :alt='title' />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon v-if="bmsAuth === 1"  icon-class="auth-new" style="width: 20px;height: 20px;vertical-align:revert-layer"/>
            <span>{{ title }}</span>
          </p>
          <p class='ugc_company'>
            {{ authorNames }}
          </p>
        </div>
        <div class='bottom'>
          <div class='article_info'>
            <span class='article_date'>
              {{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}</span>
          </div>
        </div>

      </div>
    </nuxt-link>
  </li>
</template>

<script>
export default {
  name: 'InfoItemDepartment',
  props: {
    id: {},
    title: {
      type: String,
      default: '-资讯文章标题'
    },
    img: {
      type: String,
      default: ''
    },
    authorNames: {
      type: String,
      default: ''
    },
    articleDate: {
      default: ''
    },
    bmsAuth:{}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
