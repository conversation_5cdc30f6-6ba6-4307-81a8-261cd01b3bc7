<template>
  <li class='pgc_wrapper'>
    <nuxt-link :to='{name:"index-recruitment-training-detail",query:{id}}' class='pgc_link' target='_blank'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title' />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            {{ title }}
          </p>
          <p class='ugc_company'>
            {{ company }}
          </p>
        </div>
        <div class='bottom'>
          <p class='ugc__label__wrapper text-limit-1'>
            <span v-for='item in trainRecruitLabels' :key='item.id' class='ugc__label__label'>
              {{ item.labelName }}
            </span>
          </p>
          <div class='article_info'>
            <span class='article_date'>
              {{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}</span>
            <span class='article_province'>{{ province }}</span>
          </div>
        </div>

      </div>
    </nuxt-link>
  </li>
</template>

<script>
export default {
  name: 'InfoItemTrainOrRecruitment',
  props: {
    id: {},
    title: {
      type: String,
      default: '-招聘于培训文章'
    },
    img: {
      type: String,
      default: ''
    },
    trainRecruitLabels: {
      type: Array,
      default: () => []
    },
    company: {
      type: String,
      default: ''
    },
    articleDate: {
      default: ''
    },
    province: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
