.pgc_wrapper {
  border-radius: 6px;
  padding: 10px;
  transition: all .3s;

  &:hover {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
  }

  .pgc_link {
    display: flex;
    grid-gap: 0 10px;

    .image {
      width: 220px;
      height: 124px;
      border-radius: 6px;
      overflow: hidden;
    }

    .pgc_info {
      width: calc(100% - (220px + 10px));
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      .pgc_title {
        font-weight: 700;
        font-size: 18px;
        line-height: 24px;
        color: #202020;
        margin-bottom: 14px;
      }

      .ugc_company {
        font-size: 12px;
        line-height: 16px;
        color: #708AA2;
      }

      .ugc__label__wrapper {
        width: 60%;
        //display: flex;
        //grid-gap: 0 10px;
        //flex-wrap: wrap;

        .ugc__label__label {
          display: inline-block;
          height: 23px;
          line-height: 23px;
          padding: 0 6px;
          background: #F0F9FF;
          border-radius: 6px;
          font-size: 12px;
          color: #0581CE;
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .bottom {
        display: flex;
        justify-content: space-between;
        align-items: end;

        .article_info {
          font-size: 12px;
          line-height: 12px;
          color: #708AA2;
          margin-right: 10px;

          .article_date {
            margin-right: 10px;
          }
        }
      }
    }
  }

}
