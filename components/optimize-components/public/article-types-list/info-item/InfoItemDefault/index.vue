<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{name:"index-info-detail",query:{id}}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title' />
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon
              v-if='essences==="T"'
              class-name='poneIcon'
              icon-class='jinghua'
            ></svg-icon>
            <span class='vertical_align_middle'>{{ title }}</span>
          </p>
          <p class='article_info'>
            <span class='article_date'>
              {{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : 'xx分钟前' }}
            </span>
            <span class='article_province'>
              <svg-icon
                class-name='pthreeIcon'
                icon-class='yuanchan'
              ></svg-icon>
              <span> {{ authorNames || '神外资讯原创' }}</span>
            </span>
          </p>
          <div class="description text-limit-2">
            {{metaDescription}}
          </div>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>

export default {
  name: 'InfoItemDefault',
  props: {
    id: {},
    essences: {},
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    articleDate: {},
    authorNames: {},
    img: {
      type: String,
      default: ''
    },
    metaDescription:{
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
