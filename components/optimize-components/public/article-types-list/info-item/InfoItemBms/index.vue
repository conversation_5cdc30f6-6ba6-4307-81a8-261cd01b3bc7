<template>
  <li class='pgc_wrapper'>
    <nuxt-link target='_blank' :to='{name:"index-info-detail",query:{id}}' class='pgc_link'>
      <div class='image'>
        <img
          class='img_cover'
          :src='img ? $tool.compressImg(img,220,124) : require("/assets/images/default16.png")'
          :alt='title'/>
        <ArticleType v-if='isLabelShow' :type='isCase === "T" ? "病例" : "资讯"'/>
      </div>
      <div class='pgc_info'>
        <div class='top'>
          <p class='pgc_title text-limit-2'>
            <svg-icon
              v-if='essences==="T"'
              class-name='poneIcon'
              icon-class='jinghua'
            ></svg-icon>
            <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 20px;height: 20px"/>
            <span v-html="title"></span>
          </p>
          <p class='article_info'>
            <span class='article_date'>
              {{ articleDate ? timeStamp.timestampFormat(articleDate / 1000) : '' }}
            </span>
            <span class='article_province'>
              <svg-icon
                class-name='pthreeIcon'
                icon-class='yuanchan'
              ></svg-icon>
              <span> {{ authorNames || '神外资讯原创' }}</span>
            </span>
          </p>
          <p class='ugc__label__wrapper'>
              <span v-for='item in productList' :key='item.id' class='ugc__label__product'>
                <svg-icon class-name='productIcon' icon-class='product2'/>
                <span>{{ item.name }}</span>
              </span>
          </p>
        </div>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
import ArticleType from '../../../../ArticleType/index.vue'

export default {
  name: 'InfoItemBms',
  components: {ArticleType},
  props: {
    id: {},
    essences: {},
    isLabelShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '-PGC文章标题'
    },
    articleDate: {},
    authorNames: {},
    img: {
      type: String,
      default: ''
    },
    subspecialtys: {
      type: Array,
      default: () => []
    },
    productList: {
      type: Array,
      default: () => []
    },
    isCase: {
      type: String,
      default: 'F'
    },
    bmsAuth: {}
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
