<template>
  <div class="content">
    <div class="point1"></div>
    <div class="point2"></div>
    <div class="point3"></div>
  </div>
</template>

<script>
export default {
  name: "LoadingOpt"
}
</script>

<style scoped lang="less">
.content {
  margin: 10% auto;
  width: 150px;
}
.content > div {
  width: 20px;
  height: 20px;
  background-color: var(--theme-color);
  border-radius: 50%;
  display: inline-block;
  animation: action 1.5s infinite ease-in-out;
  animation-fill-mode: both;
}
.content .point1 {
  animation-delay: -0.3s;
}
.content .point2 {
  animation-delay: -0.1s;
}

@keyframes action {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}
</style>
