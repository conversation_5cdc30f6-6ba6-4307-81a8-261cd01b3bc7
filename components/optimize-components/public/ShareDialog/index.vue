<template>
  <el-dialog
    :before-close='() => $emit("cancel",false)'
    :show-close='false'
    :visible.sync='visible'
  >
    <svg-icon class-name='closeButton cursor' icon-class='close_button' @click='$emit("cancel",false)'/>
    <div class='share_content_box flex_between flex_align_center'>
      <div class='share_content_left flex_center flex_warp flex_align_center'>
        <p class='share_content_left_title'>微信扫一扫</p>
        <div class='shart_weixin_box'>
          <img :src='weixinUrl' alt=''/>
        </div>
      </div>
      <div class='share_content_right'>
        <p class='share_content_right_title'>分享至</p>
        <div class='share_buttonList'>
          <div class='share_box flex_start'>
            <img
              alt=''
              class='cursor'
              src='~assets/images/qqkongjian.png'
              style='min-width: 38px'
              @click="shareFun('qq')"
            />
            <img
              alt=''
              class='cursor'
              src='~assets/images/weibo.png'
              style='min-width: 38px'
              @click="shareFun('weibo')"
            />
            <tooltip :content='urlcontent' class='item' effect='dark' placement='top-start'>
              <div v-clipboard:copy='urlcontent' v-clipboard:error='onError' v-clipboard:success='onCopy'
                   class='share_button flex_center flex_align_center cursor'>
                <svg-icon class-name='lianjie_share' icon-class='lianjie'/>
                <span>复制链接</span>
              </div>
            </tooltip>
          </div>
        </div>
        <p class='share_content_right_info'>您可以直接复制短链接，分享给朋友， 也可以直接点击社交平台图标，指定分享～</p>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import qrcade from 'qrcode'
import {tooltip} from 'element-ui'
import {userInfo} from '@/api/user'

export default {
  name: 'ShareDialog',
  components: {
    tooltip
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shareTitle: {
      type: String,
      default: ''
    },
    shareImgUrl: {},
    shareDesc: {}
  },
  data() {
    return {
      weixinUrl: '',// 微信分享地址
      urlcontent: ''
    }
  },
  mounted() {
    this.urlcontent = window.location.href
    this.sharweixin()
  },
  methods: {
    onCopy(e) {
      this.$axios.$request(userInfo()).then(res => {
        if (res.code === 1) {
          this.$analysys.share(res.result.realName, '链接分享', '', '', res.result.company, document.title, '', [], [], '用户中心', '', '用户中心', res.result.title, document.title, '', '')
        }

      })
      this.$toast('内容已复制到剪切板！')
    },
    // 复制失败时的回调函数
    onError(e) {
      this.$toast.fail('抱歉，复制失败！')
    },
    // 分享事件
    shareFun(data) {
      this.$analysys.share(
        '',
        data === 'weibo' ? '微博' : 'QQ',
        '',
        '',
        '',
        document.title,
        '',
        [],
        [],
        '用户中心',
        '', '用户中心',
        '',
        document.title,
        '',
        ''
      )
      switch (data) {
        case 'weibo': {
          this.shareWeibo()
          break
        }
        case 'qq': {
          this.shareQQ()
          break
        }
      }
    },
    shareWeibo() {
      //  event.preventDefault();防止链接打开 URL：
      let _shareUrl = '//service.weibo.com/share/share.php?'
      _shareUrl += 'url=' + encodeURIComponent(document.location) // 分享地址
      _shareUrl += '&title=' + encodeURIComponent(this.shareTitle || document.title) // 分享标题
      _shareUrl += '&pic=' + encodeURIComponent(
        typeof this.shareImgUrl === 'string' && this.shareImgUrl ?
          this.shareImgUrl
          :
          'https://www.brainmed.com/_nuxt/img/logo2.d8986d1.jpg'
      )
      window.open(_shareUrl, '_blank', 'height=800,width=800', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    },
    shareQQ() {
      const imageUrl = typeof this.shareImgUrl === 'string' && this.shareImgUrl ? [this.shareImgUrl] : 'https://www.brainmed.com/_nuxt/img/logo2.d8986d1.jpg'
      const share = {
        title: this.shareTitle || document.title,
        desc: this.shareDesc || document.title,
        imageUrl,
        share_url: document.location // 注意 localhost 生成失败
      }

      // 参数pics设置分享图片的路径，多张图片以＂|＂隔开，可选参数
      const shareqqzonestring =
        'http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?title=' + share.title
        + '&desc=' + share.desc
        + '&url=' + encodeURIComponent(share.share_url)
        + '&pics=' + share.imageUrl
      // 在新窗口中打开
      window.open(shareqqzonestring, '_blank', 'height=800,width=800', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes')
    },
    // 微信分享
    sharweixin() {
      const url = window.location.href
      qrcade.toDataURL(url).then((img) => {
        this.weixinUrl = img
      }).catch((err) => {
        console.log(err)
      })
    }

  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
