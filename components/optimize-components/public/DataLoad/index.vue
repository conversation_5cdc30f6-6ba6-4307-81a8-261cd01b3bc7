<template>
  <section>
    <div v-if='loading' class='empty-tips'>
      <p
        v-loading='loading'
        :style='{height:loadingHeight}'
        class='loading-tips'
        element-loading-spinner='el-icon-loading'
        element-loading-background='rgba(255,255,255,0.6)'
        element-loading-text='加载中'
      >
      </p>
    </div>
    <div v-else-if='noMore && !loading' :style='{height:loadingHeight}' class='tips'>
      <img src='~assets/images/null_content.png' alt='' :style='{width:"30vh"}'>
      <span>{{ tips }}</span>
    </div>
    <slot v-else></slot>
  </section>
</template>

<script>
export default {
  name: 'DataLoad',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    noMore: {
      type: Boolean,
      default: false
    },
    loadingHeight: {
      type: String,
      default: '30vh'
    },
    tips: {
      type: String,
      default: '暂无更多内容 ~'
    }
  }
}
</script>

<style lang='less' scoped>
@import "styles";
</style>
