<template>
  <Card :title='title' :icon='icons' :title-font-size='titleFontSize'>
    <div class='my__attention__container' :style='isMoreFlag ? {marginBottom:"20px"} : {}'>
      <el-skeleton
        :loading='loading'
        :count='6'
        animated
      >
        <template #template>
          <div
            style='display: flex;align-items: center;height:74.7px;padding: 0 14px'
          >
            <el-skeleton-item variant='image' style='width: 58px; height: 58px;' />
            <div style='flex:auto;margin-left: 8px;flex-shrink: 0'>
              <el-skeleton-item variant='p' style='width: 60%' />
              <el-skeleton-item variant='p' style='width: 70%' />
            </div>
          </div>
        </template>
      </el-skeleton>
      <Carousel2
        :total-page='subscribeDepartmentData.page.totalPage'
        :list='subscribeDepartmentList'
        @handler='slideButtonHandler'
      >
        <template #content='slotProps'>
          <HospitalItem
            v-for='(item,listIndex) in slotProps.list'
            :key='listIndex'
            :department-id='item.cmsDepartment.id'
            :img='item.cmsDepartment.iconUrl'
            :cms-department='item.cmsDepartment.name'
            :company='item.hospitalGroupName'
            :member-total='item.memberTotal'
            :description='item.cmsDepartment.hospitalGroup.description'
          />
        </template>
      </Carousel2>
    </div>
    <div v-if='isMoreFlag' class='more'>
      <nuxt-link to='/department/all' target='_blank'>
        <span>更多科室</span>
      </nuxt-link>
    </div>
  </Card>
</template>

<script>
import { getSubscribeDepartmentList } from '../../../../../api/department'
import Carousel2 from '../../../Carousel/Carousel-2/index.vue'
import HospitalItem from './HospitalItem/index.vue'
import Card from '@/components/optimize-components/Card/index.vue'

export default {
  name: 'MyAttentionRT',
  components: {
    HospitalItem,
    Card,
    Carousel2
  },
  props: {
    title: {
      type: String,
      default: '我的关注'
    },
    isMoreFlag: {
      type: Boolean,
      default: false
    },
    icons: {},
    titleFontSize: {
      type: String,
      default: '18px'
    }
  },
  data() {
    return {
      loading: true,
      subscribeDepartmentData: {
        page: {
          totalPage: 0
        }
      },
      subscribeDepartmentList: [],
      slideNum: 0
    }
  },
  mounted() {
    this.getSubscribeDepartmentListHandler({ pageNo: 1 })
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-14 14:59
     * @description: 获取我关注我科室
     * ------------------------------------------------------------------------------
     */
    getSubscribeDepartmentListHandler({ pageNo, callback }) {
      this.$axios.$request(getSubscribeDepartmentList({
        pageNo,
        pageSize: 6
      })).then(res => {
        if (res && res.code === 1) {
          this.$emit('followNumHandler', res.list.length)
          this.subscribeDepartmentData = res
          this.subscribeDepartmentList.push(res.list)
          this.loading = false
          callback ? callback(true) : null
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-14 15:50
     * @description: 滑动按钮
     * ------------------------------------------------------------------------------
     */
    async slideButtonHandler(pageNo, callback) {
      await this.getSubscribeDepartmentListHandler({ pageNo, callback })
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
