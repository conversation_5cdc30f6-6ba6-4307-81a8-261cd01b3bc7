<template>
  <li class='wrapper_list'>
    <nuxt-link :to='{name:`index-department-detail`,query:{departmentId}}' target='_blank' class='my__attention__item'>
      <div class='images'>
        <img
          class='img_cover' :src='img ? $tool.compressImg(img,68,68) : require("/assets/images/default1.png")'
          alt=''
        />
      </div>
      <div class='infos'>
        <p class='company__title text-limit-1'>
          {{ company }}
        </p>
        <p class='company__info'>
        <span class='company__department'>
          {{ cmsDepartment }}
        </span>
          <span class='company__authentication'>
          {{ memberTotal }}位已认证成员
        </span>
        </p>
      </div>
    </nuxt-link>
  </li>
</template>

<script>
export default {
  name: 'HospitalItem',
  props: {
    departmentId: {},
    img: {
      type: String,
      default: ''
    },
    company: {
      type: String,
      default: ''
    },
    cmsDepartment: {
      type: String,
      default: '-部门名称'
    },
    memberTotal: {
      type: Number,
      default: 0
    },
    description: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
