<template>
  <UserCard icon-class='user-cenmter-team' title='所属企业团队主页'>
    <div class='content'>
      <nuxt-link
        v-for='(item,index) in departmentHomePageInfo'
        :key='index'
        class='department_item'
        target="_blank"
        :to="{path:`/bms/classify/-/product-line-details/${brandProductLineId}`}"
      >
        <span class='name'>
          {{item.hospitalName}}
        </span>
        <svg-icon icon-class='user-center-more' style='transform: rotateZ(-90deg)' class-name='icons' />
      </nuxt-link>
    </div>
  </UserCard>
</template>

<script>
import UserCard from '../UserCard/index.vue'

export default {
  name: 'AffiliationTeam',
  components: { UserCard },
  props:["departmentHomePageInfo","brandProductLineId"]
}
</script>

<style scoped lang='less'>
.department_item {
  max-width: 90%;
  display: block;
  margin-top: 20px;

  &:first-child {
    margin-top: 17px;
  }

  .name {
    color: #333;
    font-size: 16px;
    line-height: 150%;
  }

  .line {
    color: #999;
    font-size: 14px;
    line-height: calc(16px * 1.5);
    margin: 0 2px;
  }

  &:hover {
    .name {
      cursor: pointer;
      color: var(--theme-color);
    }
  }
}
</style>
