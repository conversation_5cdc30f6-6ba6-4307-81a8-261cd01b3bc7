<template>
  <section class='tabs-container'>
    <ol class='tabse_ol'>
      <template v-for='item in tabList'>
        <li
          v-if='item.isEnable'
          :id='item.id'
          :key='item.id'
          :ref='item.id'
          class='tabs_li cursor'
          :class="!item.open && isLoginUser === 'F' ? 'notOpen' : ''"
          @click='changeTabHandler(item.id,item.tabName,item.open)'>
        <span
          :id='activeTab === item.id ? "is_active" : ""'
          :ref='activeTab === item.id ? "is_active" : ""'>
          {{ item.tabName }}
        </span>
          <svg-icon v-if="!item.open && isLoginUser === 'F'" icon-class="user-center-key" class-name="icons"/>
        </li>
      </template>
      <li id='line' ref='line'/>
    </ol>
    <template>
      <slot name='right'></slot>
    </template>
  </section>
</template>

<script>
export default {
  name: 'UserCenterTab',
  props: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-10 14:53
     * @description: 传递格式为   [{ id: 'A', tabName: '近期发布' }] 的数组
     * ------------------------------------------------------------------------------
     */
    tabList: {
      type: Array,
      default: () => [
        {id: 0, tabName: 'Name1', isEnable: true},
        {id: 1, tabName: 'Name2', isEnable: true}
      ]
    },
    isLoginUser:{},
    isActiveId: {},
  },
  data() {
    return {
      changingFlag: true,
      timer: null,
      activeTab: this.isActiveId || this.tabList.find(item => item.isEnable).id
    }
  },
  watch: {
    isActiveId(newValue){
      if(newValue === "Follow"){
        const lineDom = this.$refs.line
        lineDom.style.opacity = 0;
        this.activeTab = newValue
      }else{
        this.activeTab = newValue
        this.lineHandler(newValue)
      }
    },
    tabList: {
      handler(value) {
        const id = value.find(item => item.isEnable).id
        this.activeTab = id
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.lineHandler(id)
        }, 300)
      },
      deep: true
    }
  },
  mounted() {
    this.lineHandler('is_active')
  },
  methods: {
    /**
     * 底部选中跳移动位置
     */
    lineHandler(id) {
      const idDom = this.$refs[id] ? this.$refs[id][0] : null
      if(idDom){
        const lineDom = this.$refs.line
        const idDomLeft = idDom ? idDom.offsetLeft : 0
        lineDom.style.opacity = 1
        lineDom.style.transform = `translateX(${(idDom.offsetWidth / 2) - (lineDom.offsetWidth / 2) + idDomLeft}px)`
      }
    },
    /**
     * 切换tab
     */
    changeTabHandler(id, tabName,open) {
      if(this.isLoginUser === "F" && !open){
        this.$toast("该用户隐私设置，收藏列表不可见");
        return;
      }
      if (this.changingFlag) {
        this.changingFlag = false
        this.activeTab = id
        this.lineHandler(id)
        this.$analysys.btn_click(tabName, document.title)
        this.$emit('changeTabHandler', id, val => {
          if (val) {
            this.changingFlag = true
          }
        })
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
