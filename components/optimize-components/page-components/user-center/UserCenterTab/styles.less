.tabs-container {
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  border-radius: 6px;
  height: 54px;
  line-height: 54px;
  position: relative;
  border-bottom: 1px solid #EEE;

  .tabse_ol {
    display: grid;
    justify-content: flex-start;
    grid-auto-flow: column;
    grid-gap: 0 60px;
    padding: 0 20px;
    box-sizing: border-box;
    position: relative;

    #line {
      width: 26px;
      height: 2px;
      background: #0581CE;
      border-radius: 2px;
      position: absolute;
      bottom: 0;
      transition: all .3s;
      opacity: 0;
    }

    .tabs_li {
      color: #666666;
      font-size: 16px;
      display: flex;
      align-items: center;
      .icons{
        width: 12px;
        height: 12px;
        margin-left: 4px;
      }
      &:hover {
        color: var(--theme-color);
      }

      #is_active {
        color: var(--theme-color);
      }
    }

    .notOpen{
      cursor: not-allowed;
      color: #666666!important;
    }
  }
}
