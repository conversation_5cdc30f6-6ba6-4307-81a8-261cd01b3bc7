<template>
  <MallDialog
    width='1000px'
    :title='courseDialogType === "V"?"优惠券可用课程":"兑换券可用课程"'
    @close='$emit("cancelHandler")'
  >
    <div class='mall_list_wrapper'>
      <template v-for='(item,index) in courseList'>
        <CourseItem
          v-if="index<4"
          :key="index"
          :name="item.name"
          :cover="item.cover"
          :subspecialties="item.subspecialties"
          :type="item.type"
          :money="item.money"
          :activity="item.activity ? item.activity.discount : null"
          :course-id="item.id"
        />
      </template>

    </div>
    <div v-if="courseList.length>4" class='more_tips'>
      <nuxt-link v-if='courseDialogType === "V"' target='_blank' :to='{path:`/user-center/coupons/course/${courseId}?type=V`}' class='tips'>
        查看更多>
      </nuxt-link>
      <nuxt-link v-else-if='courseDialogType === "A"' target='_blank' :to='{path:`/user-center/coupons/course/${courseId}?type=A`}' class='tips'>
        查看更多>
      </nuxt-link>
    </div>
  </MallDialog>
</template>

<script>
import MallDialog from '../../../../UI/MallDialog/index.vue'
import CourseItem from "../../../../public/article-types-list/CourseItem/index.vue";

export default {
  name: 'ChooseCourseDialog',
  components: {CourseItem, MallDialog },
  props: ["courseList", "courseId", "courseDialogType"],
  data() {
    return {}
  },
  mounted() {
  }
}
</script>

<style scoped lang='less'>
.mall_list_wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 0 18px;
  padding: 0 80px;
}

.more_tips {
  margin-top: 35px;
  font-weight: 400;
  text-align: center;

  .tips {
    cursor: pointer;
    font-size: 14px;
    line-height: 150%;
    color: #666666;
  }
}
</style>
