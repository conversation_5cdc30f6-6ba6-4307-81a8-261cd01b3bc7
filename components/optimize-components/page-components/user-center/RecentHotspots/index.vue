<template>
  <UserCard icon-class='user-center-hot' title='近期热点'>
    <el-skeleton :loading="loading" animated>
      <template slot="template">
        <div v-for="item in 2" :key="item" style="display: flex;align-items: center;justify-content: space-between;margin-bottom: 14px">
          <div style="width: calc(100% - 116px)">
            <el-skeleton-item variant="text" style="width: 50%;" />
            <el-skeleton-item variant="text" style="width: 80%;" />
          </div>
          <div>
            <el-skeleton-item
              variant="image"
              style="width: 106px; height: 60px;border-radius: 6px"
            />
          </div>
        </div>
      </template>
      <template>
        <div class="hot_wrapper" ref="hot_wrapper">
          <div class='content' ref="hot_content">
            <div
              v-for='(item,index) in recentHotspotList'
              :key='index'
              class='hot_list'
              @click="jumpPageHandler(item.contentType,item.id)"
            >
              <div class='item_left'>
                <div class='title'>
                  <p class='text-limit-2'>
                    <span class='label'>{{item.type}}</span>
                    {{item.title}}
                  </p>
                </div>
              </div>
              <div class='item_image'>
                <img class='img_cover' :src='item.cover ? $tool.compressImg(item.cover,106,60) : require("/assets/images/default16.png")' alt=''>
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>
  </UserCard>
</template>

<script>
import UserCard from '../UserCard/index.vue'
import {getRecentHotspotList} from "../../../../../api/new-user-center";

export default {
  name: 'RecentHotspots',
  components: { UserCard },
  data(){
    return{
      loading:true,
      recentHotspotList:[],
      timer:null
    }
  },
  mounted() {
    this.$axios.$request(getRecentHotspotList({
      userId:this.$store.state.auth.user.id,
      type:"U",
    })).then(res=>{
      if(res.code === 1){
        this.recentHotspotList = res.list;
        this.loading = false;

        if(this.recentHotspotList.length > 2){
          // eslint-disable-next-line no-undef
          setTimeout(()=>{
            let num = 0
            const hotWrapper = this.$refs.hot_wrapper
            const content = this.$refs.hot_content
            const maxNum = Math.ceil(this.recentHotspotList.length / 2)

            if(this.recentHotspotList.length%2 === 0){
              this.recentHotspotList.push(res.list[0])
              this.recentHotspotList.push(res.list[1])
            }else{
              this.recentHotspotList.push(res.list[3])
              this.recentHotspotList.push(res.list[0])
              this.recentHotspotList.push(res.list[1])
            }
            // eslint-disable-next-line no-undef
            this.timer = setInterval(()=>{
              if(num < maxNum){
                num++
                content.style.transition = "all .3s linear";
              }else{
                content.style.transition = "unset";
                num = 0;
              }
              content.style.transform = `translateY(-${hotWrapper.clientHeight * num}px)`
            },3000)
          },0)
        }
      }
    })
  },
  methods:{
    jumpPageHandler(type,id){
      switch (type){
        case 'meeting':{
          const { href } = this.$router.resolve({ path: '/meeting/detail', query: { id }})
          window.open(href, '_blank')
          break;
        }
        case 'info':{
          const { href } = this.$router.resolve({ path: '/info/detail', query: { id }})
          window.open(href, '_blank')
          break;
        }
        case 'mp_article':{
          const { href } = this.$router.resolve({ path: '/case/detail-ugc', query: { id }})
          window.open(href, '_blank')
          break;
        }
      }
    }
  },
  beforeDestroy() {
    // eslint-disable-next-line no-undef
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang='less'>
/deep/.hot_wrapper{
  height: 148px;
  overflow: hidden;
}
.content {
  transition: all .3s linear;
  .hot_list {
    margin-top: 14px;
    display: flex;
    cursor: pointer;

    .item_left {
      flex: auto;
      padding-right: 10px;

      .title {
        color: #333;
        font-size: 16px;
        line-height: 1.6;

        .label {
          background: #F2F9FD;
          color: var(--theme-color);
          font-size: 10px;
          line-height: 1.5;
          padding: 2px;
          border-radius: 6px;
          margin-right: 2px;
        }
      }
    }

    .item_image {
      flex-shrink: 0;
      width: 106px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
    }

    &:hover {
      .title {
        color: var(--theme-color);
      }
    }
    &:nth-child(odd){
      .title .label{
        color: #FF8C03!important;
        background: rgba(222, 128, 17, 0.05)!important;
      }
    }
  }
}
</style>
