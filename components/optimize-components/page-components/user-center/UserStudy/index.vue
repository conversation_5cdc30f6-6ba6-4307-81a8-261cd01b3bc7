<template>
  <UserCard icon-class='user-center-study' title='创作与学习'>
    <div class='content'>
      <div class='item'>
        <div class='num'>
          {{$tool.formatterNumUnitUserCenter(apiData.postNum)}}
        </div>
        <div class='name'>
          创作内容
        </div>
      </div>
      <div class='item'>
        <div class='num'>
          {{$tool.formatterNumUnitUserCenter(apiData.platformDateNum)}}
        </div>
        <div class='name'>
          云课堂/会议/手术复盘
        </div>
      </div>
      <!-- <div class='item'>
        <div class='num'>
          {{$tool.formatterNumUnitUserCenter(apiData.meetingNum)}}
        </div>
        <div class='name'>
          会议
        </div>
      </div> -->
      <div class='item'>
        <div class='num'>
          {{activeTime}}
        </div>
        <div class='name'>
          学习时长（小时）
        </div>
      </div>
    </div>
    <div v-if="isLoginUser === 'T' && false" class='content_analysis'>
      进入内容分析
    </div>
  </UserCard>
</template>

<script>
import UserCard from '../UserCard/index.vue'
import {getWebApiData} from "../../../../../api/new-user-center";

export default {
  name: 'UserStudy',
  components: { UserCard },
  props:["isLoginUser"],
  data(){
    return {
      apiData:{
        activeTime:0 ,
        postNum: 0,
        meetingNum: 0,
        courseNum: 0
      }
    }
  },
  computed: {
    activeTime() {
      return this.formatDuring(this.apiData.activeTime) < 200 ? this.formatDuring(this.apiData.activeTime) : 200 + "+"
    }
  },
  mounted() {
    this.$axios.$request(getWebApiData({
      loginUserId:this.$store.state.auth.user.id,
      profileUserId:this.$route.query.profileUserId
    })).then(res=>{
      if(res.code === 1){
        this.apiData = res.result;
      }
    })
  },
  methods:{
    formatDuring(millisecond) {
      const days = parseInt(millisecond / (1000 * 60 * 60 * 24))
      const hours = parseInt((millisecond) / (1000 * 60 * 60))
      const minutes = parseInt((millisecond % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = (millisecond % (1000 * 60)) / 1000
      return hours
    }
  }
}
</script>

<style scoped lang='less'>
.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 14px 0 0;

  .item {
    text-align: center;

    .name {
      color: #999;
      font-size: 14px;
      line-height: 150%;
    }

    .num {
      color: #333;
      font-size: 26px;
      line-height: 150%;
    }
  }
}

.content_analysis {
  margin-top: 14px;
  border-radius: 6px;
  border: 1px solid var(--unnamed, #0581CE);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  color: #0581CE;
  font-size: 14px;
  line-height: 150%;
  cursor: pointer;
}
</style>
