.user_center_nav_wrapper{
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 2000;
  overflow: hidden;
  transition: all .3s ease;
  background: rgba(255, 255, 255, 0.90);
  backdrop-filter: blur(10px);
  transform: translateY(-100%);

  .user_center_nav_content{
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    &::after{
      display: none;
    }

    .tab_list{
      display: flex;
      align-items: center;
      pointer-events: auto;

      .tab_item{
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        color: #333;
        margin-right: 60px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .icons{
          width: 12px;
          height: 12px;
          margin-left: 4px;
        }
        &:hover{
          color: var(--theme-color);
        }
        &:last-child{
          margin-right: 0;
        }
      }
      .is_active{
        color: var(--theme-color);
        position: relative;
        &::before{
          content:"";
          position: absolute;
          left: 50%;
          bottom: 0;
          width: 26px;
          height: 2px;
          transform: translateX(-50%);
          background: var(--theme-color);
        }
      }
    }
    .user_content{
      display: flex;
      align-items: center;
      pointer-events: auto;
      .content_item{
        width: 62px;
        text-align: center;
        .tips{
          font-size: 12px;
          color: #888;
          line-height: 150%;
          display: flex;
          align-items: center;
          justify-content: center;
          .user-center-key-icons{
            width: 10px;
            height: 10px;
            margin-left: 3px;
          }
        }
        .num{
          color: #333;
          font-size: 18px;
          line-height: 150%;
        }
      }
      .is_active_user_desc_content{
        .tips {
          color: var(--theme-color);
        }

        .num {
          color: var(--theme-color);
        }
      }
    }
  }
}
