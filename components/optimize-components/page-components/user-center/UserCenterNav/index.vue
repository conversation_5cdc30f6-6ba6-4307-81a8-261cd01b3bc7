<template>
  <div class="user_center_nav_wrapper" id="user_center_nav_wrapper">
    <div class="container-box user_center_nav_content">
      <div class="tab_list">
        <template v-for="(item,index) in tabList">
          <div
            v-if="item.isEnable"
            :key="index"
            class="tab_item cursor"
            :class='activeTab === item.id ? "is_active" : ""'
            :style="!item.open && isLoginUser === 'F' ? {color:'#333',cursor:'not-allowed'} : {}"
            @click='changeTabHandler(item.id,item.tabName,item.open)'
          >
            <span> {{ item.tabName }}</span>
            <svg-icon v-if="!item.open && isLoginUser === 'F'" icon-class="user-center-key" class-name="icons"/>
          </div>
        </template>
      </div>
      <div class="user_content">
        <div
          class="content_item cursor"
          :class="$route.query.appoint === 'Follow' && $route.query.type !== 'fans' ? 'is_active_user_desc_content' : ''"
          @click='changeTabHandler("Follow","关注",viewSettings.openFollow)'>
          <p class="tips">
            <span>关注</span>
            <svg-icon v-if="isLoginUser === 'F' && !viewSettings.openFollow" icon-class="user-center-key"
                      class-name="user-center-key-icons"/>
          </p>
          <p class="num">{{ $tool.formatterNumUnitUserCenter(personalInfo.followerTotal) }}</p>
        </div>
        <div
          class="content_item cursor"
          :class="$route.query.appoint === 'Follow' && $route.query.type === 'fans' ? 'is_active_user_desc_content' : ''"
          @click='changeTabHandler({name:"Follow",type:"fans"},"粉丝",viewSettings.openFollow)'>
          <p class="tips">
            <span>粉丝</span>
            <svg-icon v-if="isLoginUser === 'F' && !viewSettings.openFollow" icon-class="user-center-key"
                      class-name="user-center-key-icons"/>
          </p>
          <p class="num">{{ $tool.formatterNumUnitUserCenter(personalInfo.fansTotal) }}</p>
        </div>
        <div class="content_item">
          <p class="tips">获赞</p>
          <p class="num">{{ $tool.formatterNumUnitUserCenter(personalInfo.diggs) }}</p>
        </div>
        <div class="content_item">
          <p class="tips">被收藏</p>
          <p class="num">{{ $tool.formatterNumUnitUserCenter(personalInfo.collects) }}</p>
        </div>
      </div>
      <UserLoginStatus/>
    </div>
  </div>
</template>

<script>
import UserLoginStatus from "../../../../../opt-components/template/Header/module/UserLoginStatus/index.vue";

export default {
  name: "UserCenterNav",
  components: {
    UserLoginStatus
  },
  props: ["tabList", "isActiveId", "isLoginUser", "personalInfo", "viewSettings"],
  watch: {
    isActiveId(newValue) {
      this.activeTab = newValue;
    }
  },
  data() {
    return {
      changingFlag: true,
      activeTab: this.isActiveId,
    }
  },
  methods: {
    /**
     * 切换tab
     */
    changeTabHandler(id, tabName, open) {
      if (this.isLoginUser === "F" && !open) {
        if (tabName === "关注") {
          this.$toast("该用户隐私设置，关注列表不可见");
        } else {
          this.$toast("该用户隐私设置，收藏列表不可见");
        }
        return;
      }

      const tabWrapper = document.querySelector(".tabs-container")
      tabWrapper.scrollIntoView({behavior: 'auto'})
      if (this.changingFlag) {
        this.changingFlag = false
        this.activeTab = id
        this.$analysys.btn_click(tabName, document.title)
        this.$emit('changeTabHandler', id, val => {
          if (val) {
            this.changingFlag = true
          }
        })
      }
    }
  }
}
</script>

<style>
.nav_message_icons {
  transition: all .2s;
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
