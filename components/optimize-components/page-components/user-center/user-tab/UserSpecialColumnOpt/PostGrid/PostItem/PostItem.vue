<template>
  <li class='post-item-container'>
    <a
      :href='webApiLinkUrl ? webApiLinkUrl : type === "S" ? `/elabweb/columnlist/${id}` : `/column/detail?id=${id}&type=${type}`' class='post-item'
      target='_blank'>
      <div class='img-box'>
        <div v-if='type==="O"' class='boutique'>
          精品专栏
        </div>
        <div v-if='type==="S"' class='boutique'>
          手术复盘专栏
        </div>
        <img v-if='imgUrl' :src='$tool.compressImg(imgUrl,242,141)' alt='专栏图片' class='img_cover'>
        <img v-else alt='专栏图片' class='img_cover' src='~assets/images/default3.png'>
      </div>
      <div class='post-content'>
        <p class='title text-limit-2'>
          {{ title }}
        </p>
        <p class='info-content'>
      <span class='readNum'>
        {{ $tool.formatterNum(showViews) }}阅读
      </span>
          <span class='articleNum'>
        {{ refers }}文章
      </span>
        </p>
        <p class='time'>
          {{ timeStamp.timestamp_13(updateTime,'y-m-d') }}更新
        </p>
      </div>
    </a>
  </li>
</template>

<script>
export default {
  name: 'PostItem',
  props: {
    id: {
      type: Number,
      required: true
    },
    type: {
      type: String,
      required: true
    },
    imgUrl: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    showViews: {
      type: Number,
      required: true
    },
    refers: {
      type: Number,
      required: true
    },
    updateTime: {
      type: Number,
      required: true
    },
    webApiLinkUrl: {
      type: String,
      required: false
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
