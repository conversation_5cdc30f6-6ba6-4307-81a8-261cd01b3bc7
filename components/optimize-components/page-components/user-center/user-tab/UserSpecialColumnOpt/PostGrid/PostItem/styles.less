.post-item-container {
  border-radius: 6px;
  transition: all .3s;
  background: #FBFBFB;

  &:hover {
    box-shadow: 0px 4px 25px 0px rgba(27, 39, 71, 0.15);
  }

  &:hover .title {
    color: var(--theme-color)!important;
  }
}

.post-item {
  //display: grid;
  //grid-auto-flow: column;
  //grid-template-rows: 12px 12px 8px;
  transition: all .3s;
  background: #FBFBFB;


  .img-box {
    width: 100%;
    height: 141px;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    position: relative;

    .boutique {
      position: absolute;
      right: 6px;
      top: 6px;
      line-height: 18px;
      background: #0581CE;
      border-radius: 6px;
      font-size: 10px;
      color: #FFFFFF;
      padding: 0 8px;
    }
  }

  .post-content {
    padding: 12px;

    .title {
      font-size: 18px;
      line-height: 24px;
      color: #202020;
      margin-bottom: 12px;
      transition: all .3s;
    }

    .info-content {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 8px;

      span {
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #708AA2;
      }

      .readNum {

      }

      .articleNum {
        margin-left: 8px;
      }
    }

    .time {
      font-weight: 400;
      font-size: 10px;
      line-height: 12px;
      color: #708AA2;
    }
  }
}
