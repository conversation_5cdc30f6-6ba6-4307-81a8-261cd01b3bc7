<template>
  <ul class='column-list'>
    <PostItem
      v-for='item in columnData'
      :id='item.id'
      :key='item.id'
      :img-url='item.smallImageUrl'
      :refers='item.refers'
      :show-views='item.showViews'
      :title='item.title'
      :type='item.type'
      :update-time='item.updateTime'
      :web-api-link-url='item.webApiLinkUrl'
    />
  </ul>
</template>

<script>
import PostItem from './PostItem/PostItem.vue'

export default {
  name: 'PostGrid',
  components: {
    PostItem
  },
  mounted() {
    setTimeout(()=>{
      console.log(this.columnData)
    },3000)
  },
  props: {
    columnData: {}
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
