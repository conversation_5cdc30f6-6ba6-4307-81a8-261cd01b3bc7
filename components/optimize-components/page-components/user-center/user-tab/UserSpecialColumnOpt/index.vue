<template>
  <div class='special-column'>
    <div class='special-column-list_set' :style="columnListSet.list.length ? {padding: '20px 16px'} : {}">
      <PostGrid :column-data='columnListSet.list' />
    </div>
  </div>
</template>

<script>
import PostGrid from './PostGrid/PostGrid.vue'

export default {
  name: 'UserSpecialColumnOpt',
  components: {
    PostGrid,
  },
  props: {
    columnListSet:{}
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/17 15:46
     *  专栏分页
     */
    handleCurrentChange(item) {
      this.$emit("handleCurrentChange", {type:"column",pageNo:item})
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
