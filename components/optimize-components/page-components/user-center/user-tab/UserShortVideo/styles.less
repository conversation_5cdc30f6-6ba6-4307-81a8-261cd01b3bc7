.user_short_video_wrapper{
  background: #FFFFFF;
  overflow: hidden;
  .waterfall{
    width: 100%;
    //display: grid;
    //grid-template-columns: 1fr 1fr 1fr;
    //grid-gap: 18px;
    .col_wrapper{
      width: 260px;
      padding: 0 9px;
      box-sizing: border-box;
      float: left;
      position: relative;
    }
    .col_item{
    position: absolute;
    }

    .video_item{
      transition: all .3s;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 20px;
      .image{
        position: relative;
        cursor: pointer;
        .play_icon{
          position: absolute;
          left: 10px;
          bottom: 14px;
          display: flex;
          align-items: center;
          .icons{
            width: 18px;
            height: 13.5px;
          }
          .num{
            color: #FFF;
            font-size: 14px;
            margin-left: 5px;
          }
        }
      }
      img{
        display: block;
        width: 100%;
      }

      .title{
        background: #FBFBFB;
        padding: 14px 12px;
        color: #333;
        font-size: 18px;
        line-height: 150%;
        span{
          display: block;

        }
      }
      &:hover{
        transform: scale(1.03);
        .title{
          color: var(--theme-color)!important;
        }
      }
    }
  }
}
