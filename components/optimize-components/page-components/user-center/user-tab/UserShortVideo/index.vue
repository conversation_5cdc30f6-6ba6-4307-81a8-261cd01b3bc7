<template>
  <div class="user_short_video_wrapper" :style="userVideosData.list.length ? {padding:'20px 6px'} : {}">
    <div class="waterfall">
      <div class="video_wrapper">
        <div
          class="col_wrapper"
          ref="col1"
        >
          <transition-group name="list">
            <div
              v-for="(item,index) in dataList1"
              :key="index"
              class="item"
            >
              <div
                ref='waterfallItem'
                class="video_item"
              >
                <div
                  class="image"
                  @click="$store.commit('bms/setBmsHomeShortVideoHandler', item.id)"
                >
                  <img
                    :src="$tool.compressImg(item.cover,252,((252.33 / item.coverWidth) * item.coverHeight).toFixed(0))"
                    alt=""/>
                  <div class="play_icon">
                    <svg-icon icon-class="short_video" class-name="icons"/>
                    <span class="num">{{ $tool.formatterNumUnit(item.views) }}</span>
                  </div>
                </div>
                <div class="title">
                  <span class="text-limit-2">{{ item.title }}</span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
        <div
          class="col_wrapper"
          ref="col2"
        >
          <transition-group name="list">
            <div
              v-for="(item,index) in dataList2"
              :key="index"
              class="item"
            >
              <div
                ref='waterfallItem'
                class="video_item"
              >
                <div
                  class="image"
                  @click="$store.commit('bms/setBmsHomeShortVideoHandler', item.id)"
                >
                  <img
                    :src="$tool.compressImg(item.cover,252,((252.33 / item.coverWidth) * item.coverHeight).toFixed(0))"
                    alt=""/>
                  <div class="play_icon">
                    <svg-icon icon-class="short_video" class-name="icons"/>
                    <span class="num">{{ $tool.formatterNumUnit(item.views) }}</span>
                  </div>
                </div>
                <div class="title">
                  <span class="text-limit-2">{{ item.title }}</span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
        <div
          class="col_wrapper"
          ref="col3"
        >
          <transition-group name="list">
            <div
              v-for="(item,index) in dataList3"
              :key="index"
              class="item"
            >
              <div
                ref='waterfallItem'
                class="video_item"
              >
                <div
                  class="image"
                  @click="$store.commit('bms/setBmsHomeShortVideoHandler', item.id)"
                >
                  <img
                    :src="$tool.compressImg(item.cover,252,((252.33 / item.coverWidth) * item.coverHeight).toFixed(0))"
                    alt=""/>
                  <div class="play_icon">
                    <svg-icon icon-class="short_video" class-name="icons"/>
                    <span class="num">{{ $tool.formatterNumUnit(item.views) }}</span>
                  </div>
                </div>
                <div class="title">
                  <span class="text-limit-2">{{ item.title }}</span>
                </div>
              </div>
            </div>
          </transition-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "UserShortVideo",
  components: {},
  props: ["userVideosData"],
  data() {
    return {
      dataList1: [],
      dataList2: [],
      dataList3: [],
    }
  },
  watch: {
    "userVideosData.list": {
      handler(newValue) {
        console.log(newValue)
        this.mountMenu()
      },
      deep: true
    }
  },
  mounted() {
    // if(this.userVideosData.list>0){
    //   this.mountMenu()
    // }
  },
  methods: {
    mountMenu(arg) {
      const temp = this.userVideosData.list
      const index = arg || 0
      const refName = this.selectCol()
      if (temp.length > index) {
        this[refName].push(this.userVideosData.list[index])
        this.$nextTick(() => {
          this.mountMenu(index + 1);
        })
      }
    },
    selectCol() {
      const getHeight = (ref) => {
        return this.$refs[ref].clientHeight
      }
      const height1 = getHeight('col1')
      const height2 = getHeight('col2')
      const height3 = getHeight('col3')
      console.log(height1,height2,height3)
      switch (Math.min(height1, height2, height3)) {
        case height1:
          return 'dataList1'
        case height2:
          return 'dataList2'
        case height3:
          return 'dataList3'
      }
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
