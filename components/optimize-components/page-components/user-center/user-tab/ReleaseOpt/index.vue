<template>
  <div class='release_opt_wrapper'>
    <div class='article_wrapper_list' :style="releaseData.list.length ? {padding:'20px 0'} : {}">
      <template v-for="(item) in releaseData.list">
        <InfoArticleItem
          v-if="item.type === 'info'"
          :id="item.info.infoId"
          :key="item.info.infoId"
          :img="item.info.infoImg"
          :title="item.info.infoTitle"
          :publish-time="item.info.publishTime"
          :author-names="item.info.authorNames"
          :meta-description="item.info.metaDescription"
          :show-type="item.info.isCase === 'T' ? '病例' : null"
          :bms-auth="item.info.bmsAuth"
        />
        <PgcArticleItem
          v-else-if="item.type === 'mp_article'"
          :id="item.mp_article.id"
          :key="item.mp_article.id"
          :title="item.mp_article.title"
          :img="item.mp_article.cover"
          :subspecialtys="item.mp_article.subspecialtys"
          :product-list="item.mp_article.productList"
          :author-name="item.mp_article.creator.realName"
          :article-date='item.mp_article.creationTime'
          :article-read='item.mp_article.showViews'
          :article-comment='item.mp_article.showComments'
          :article-fabulous='item.mp_article.showDiggs'
          :paragraph-type="item.mp_article.paragraphType"
          :type="item.mp_article.type"
          :is-edit="true"
          :bms-auth="item.mp_article.bmsAuth"
        />
        <QAArticleItem
          v-else-if="item.communityQA.images.length===0 && item.type === 'communityQA'"
          :key="item.communityQA.id"
          :q-a-id="item.communityQA.id"
          :title="item.communityQA.title"
          :real-name="item.communityQA.creator.realName"
          :publish-time="item.communityQA.publishTime"
          :regex-text="item.communityQA.regexText"
          :qa-detail-url="item.communityQA.qaDetailUrl"
        />
        <QADefaultArticleItem
          v-else-if="item.communityQA.images.length>0 && item.type === 'communityQA'"
          :key="item.communityQA.id"
          :q-a-id="item.communityQA.id"
          :title="item.communityQA.title"
          :real-name="item.communityQA.creator.realName"
          :publish-time="item.communityQA.publishTime"
          :regex-text="item.communityQA.regexText"
          :images="item.communityQA.images[0]"
          :qa-detail-url="item.communityQA.qaDetailUrl"
        />
      </template>
    </div>
  </div>
</template>

<script>
import InfoArticleItem from '../../../../public/article-types-list/user-center-page/InfoArticleItem/index.vue'
import PgcArticleItem from '../../../../public/article-types-list/user-center-page/PgcArticleItem/index.vue'
import QAArticleItem from '../../../../public/article-types-list/user-center-page/QAArticleItem/index.vue'
import QADefaultArticleItem
  from "../../../../public/article-types-list/user-center-page/QADefaultArticleItem/index.vue";

export default {
  name: 'ReleaseOpt',
  components: {
    QADefaultArticleItem,
    QAArticleItem,
    InfoArticleItem,
    PgcArticleItem,
  },
  props: ["releaseData"],
  data() {
    return {
      current: 1,
    }
  },
  created() {

  },
  methods: {
    handleCurrentChange(item) {
      this.current = item;
      this.$emit("handleCurrentChange", {type: "release", pageNo: item})
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
