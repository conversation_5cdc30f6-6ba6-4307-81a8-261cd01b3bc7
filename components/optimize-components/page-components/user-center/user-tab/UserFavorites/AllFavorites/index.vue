<template>
  <div class="all_favorites_wrapper" :style="collectContentsData.list.length ? {padding:'20px 0 20px'} : {}">
    <div class="favorites_list">
      <template v-for="item in collectContentsData.list">
        <InfoArticleItem
          v-if="item.contentType === 'info' || item.contentType === 'pInfo'"
          :id="item.content.infoId"
          :key="item.userCollectId"
          :img="item.content.infoCover"
          :title="item.content.infoTitle"
          :publish-time="timeStamp.timestampFormat(item.collectTime / 1000)"
          :author-names="item.content.AuthorNames"
          :meta-description="item.content.metaDescription"
          :show-type="item.showType"
          :bms-auth="item.content.bmsAuth"
        />
        <MeetingArticleInstitutionItem
          v-else-if="item.contentType === 'meeting'"
          :id="item.content.meetingId"
          :key="item.userCollectId"
          :meeting-name="item.content.meetingName"
          :meeting-address="item.content.meetingAddress"
          :app-main-pic="item.content.meetingCover"
          :meeting-date-str="item.content.meetingDateStr"
          :meeting-id="item.content.meetingId"
          :meeting-live-status="item.content.meetingLiveStatus"
          :description="item.content.description"
        />
        <PgcArticleItem
          v-else-if="item.contentType === 'mpArticle' || item.contentType === 'pMpArticle'"
          :id="item.content.mpArticleId"
          :key="item.userCollectId"
          :title="item.content.mpArticleTitle"
          :img="item.content.mpArticleCover"
          :subspecialtys="item.content.subspecialtys"
          :product-list="item.content.productList"
          :author-name="item.content.creator.realName"
          :article-date='item.content.creationTime'
          :article-read='item.content.showViews'
          :article-comment='item.content.showComments'
          :article-fabulous='item.content.showDiggs'
          :paragraph-type="item.content.paragraphType"
          :type="item.content.type"
          :is-edit="false"
          :bms-auth="item.content.bmsAuth"
        />
        <ClassroomArticleItem
          v-else-if="item.contentType === 'courseVideo'"
          :id="item.content.courseId"
          :key="item.userCollectId"
          :cover="item.content.courseCover"
          :name="item.content.name"
          :speaker-names="item.content.speakerNames"
          :score="item.content.score"
          :show-views="item.content.showViews"
          :money="item.content.money"
          :subspecialties="item.content.subspecialties"
          :introduction="item.content.introduction"
          :is-label-show="true"
          :show-type="item.showType"
        />
        <BmsVideoItemInformation
          v-else-if="item.contentType === 'bmsVideo'"
          :id='item.content.id'
          :key="item.userCollectId"
          :img='item.content.cover'
          :title='item.content.videoName'
          :category-id='"-"'
          :product-list='item.content.productList'
          :create-time='timeStamp.timestamp_13(item.collectTime, "y-m-d")'
          :show-type="item.showType"
        />
        <VideoItemInformation
          v-else-if="item.contentType === 'shortVideo'"
          :id='item.content.id'
          :key="item.userCollectId"
          :img='item.content.cover'
          :title='item.content.title'
          :article-date='timeStamp.timestampFormat(item.collectTime / 1000)'
          :author-list='[{
            id:item.content.creator.id,
            userAvatar:item.content.creator.avatarAddress,
            authorName:item.content.creator.realName
          }]'
          :category-id='"-"'
          :article-comment='item.content.comments'
          :article-fabulous='item.content.diggs'
          :article-read='item.content.views'
          :product-list='item.content.productList'
        />
        <QADefaultArticleItem
          v-else-if="item.contentType === 'communityQA' && item.content.images.length>0"
          :key="item.userCollectId"
          :q-a-id="item.content.id"
          :title="item.content.title"
          :real-name="item.content.creator.realName"
          :publish-time="item.collectTime"
          :regex-text="item.content.regexText"
          :images="item.content.images[0]"
          :qa-detail-url="item.content.qaDetailUrl"
        />
        <QAArticleItem
          v-else-if="item.contentType === 'communityQA' && item.content.images.length===0"
          :key="item.userCollectId"
          :q-a-id="item.content.id"
          :title="item.content.title"
          :real-name="item.content.creator.realName"
          :publish-time="item.collectTime"
          :regex-text="item.content.regexText"
          :qa-detail-url="item.content.qaDetailUrl"
        />
        <MeetingArticleScheduleItem
          v-else-if="item.contentType === 'agenda'"
          :id="item.content.meetingId"
          :key="item.userCollectId"
          :meeting-name="item.content.theme"
          :app-main-pic="item.content.headImage"
          :meeting-date-str="timeStamp.timestampFormat(item.collectTime / 1000)"
          :meeting-id="item.content.meetingId"
          :description="item.content.fieldsSubject"
          :fields-id="item.content.fieldsId"
          :agenda-id="item.content.id"
        />
        <ElabItem
          v-if='item.contentType === "elab"'
            :id='item.content.id'
            :key='item.userCollectId'
            :case-name='item.content.caseName'
            :elab-surgical-classification='item.content.elabSurgicalClassification'
            :hospital='item.content.hospital'
            :author-list='item.content.authorList'
            :classification-list='item.content.classificationList'
            :image='item.content.image'
            :share-desc="item.content.shareDesc"
          />
        <AIQA
          v-if="item.contentType === 'aiQaDialogue'"
          :ids="item.content.chatId"
          :publish-time="item.collectTime"
          :question="item.content.question"
          :regex-text="item.content.answer"
        />

      </template>
    </div>
  </div>
</template>

<script>
import InfoArticleItem from "../../../../../public/article-types-list/user-center-page/InfoArticleItem/index.vue";
import ClassroomArticleItem
  from "../../../../../public/article-types-list/user-center-page/ClassroomArticleItem/index.vue";
import MeetingArticleInstitutionItem
  from "../../../../../public/article-types-list/user-center-page/MeetingArticleInstitutionItem/index.vue";
import PgcArticleItem from "../../../../../public/article-types-list/user-center-page/PgcArticleItem/index.vue";
import QADefaultArticleItem
  from "../../../../../public/article-types-list/user-center-page/QADefaultArticleItem/index.vue";
import QAArticleItem from "../../../../../public/article-types-list/user-center-page/QAArticleItem/index.vue";
import VideoItemInformation
  from "../../../../../public/article-types-list/user-center-page/VideoItemInformation/index.vue";
import BmsVideoItemInformation
  from "../../../../../public/article-types-list/user-center-page/BmsVideoItemInformation/index.vue";
import MeetingArticleScheduleItem
  from "../../../../../public/article-types-list/user-center-page/MeetingArticleScheduleItem/index.vue";

  import ElabItem
  from "../../../../../public/article-types-list/bms/ElabItem/index.vue";
import AIQA from '../../../../../public/article-types-list/user-center-page/AIQA/index.vue'
/**
 * ------------------------------------------------------------------------------
 * <AUTHOR> (Rick)         -- 2023-07-10 13:44
 * info：文章 -
 * meeting：会议 -
 * mpArticle：病例 -
 * courseVideo：新版云课堂视频 -
 * qaTopic：问答 x
 * bmsVideo：品牌视频 -
 * pInfo：科普患教（文章） -
 * pMpArticle：科普患教（病例） -
 * shortVideo：短视频 -
 * courseNote：云课堂笔记 x
 * communityQA：社区:问题/回答 -
 * agenda：日程 -
 * ------------------------------------------------------------------------------
 */
export default {
  name: "AllFavorites",
  components: {
    AIQA,
    VideoItemInformation,
    InfoArticleItem,
    ClassroomArticleItem,
    MeetingArticleInstitutionItem,
    PgcArticleItem,
    QADefaultArticleItem,
    QAArticleItem,
    BmsVideoItemInformation,
    MeetingArticleScheduleItem,
    ElabItem
  },
  props: ["collectContentsData"],
}
</script>

<style scoped lang="less">
.all_favorites_wrapper {
  .favorites_list {
    display: flex;
    flex-flow: column;
    grid-gap: 20px 0;
  }
}
.favorites_list /deep/ li{
  padding: 10px 16px;
  width: 100%;
  box-sizing: border-box;


  .list_strip_box{
  margin-top: 0;
  }
}
</style>
