<template>
  <div class="user_favorites_wrapper">
    <div class="second_tab">
      <div class="tab">
        <div
          v-for="(item,index) in secondTab"
          :key="index"
          class="tab_item"
          :class="isActiveType===item.type ? 'is_active' : ''"
          @click="selectHandler(item.type,item.component)"
        >
          {{item.name}}
        </div>
      </div>
      <el-popover
        v-if="isLoginUser === 'T'"
        placement="bottom"
        trigger="click"
        popper-class="user-center-user-favorites-el-popover"
       >
        <div class="user-center__popover____wrapper">
          <div class="user-center__popover____item">
            <Checkbox v-model="releaseCheck" @change="viewSettingHandler(true)">公开我的收藏</Checkbox>
          </div>
          <div class="user-center__popover____item">
            <Checkbox v-model="privacyCheck" @change="viewSettingHandler(false)">私密我的收藏</Checkbox>
          </div>
        </div>
        <div slot="reference" class="edit_btn">
          <svg-icon icon-class="user-center-edit" class-name="icons"/>
          <span>编辑</span>
        </div>
      </el-popover>
    </div>
    <div class="favorites_content">
      <component
        :is="isActiveTab"
        :collect-contents-data="collectContentsData"
      />
    </div>
  </div>
</template>

<script>
import {Checkbox} from "element-ui";
import {saveWebApiHomeViewSettings} from "../../../../../../api/new-user-center";
// edit
import ElabWeb from "./ElabFavorites/index.vue";
import AllFavorites from "./AllFavorites/index.vue";
import FavoritesShortVideo from "./FavoritesShortVideo/index.vue";
export default {
  name: "UserFavorites",
  components:{
    Checkbox,
    'all':AllFavorites,
    'shortVideo' :FavoritesShortVideo,
    'elab':ElabWeb
  },
  props:["collectContentsData","viewSettings","collectContentsType","isLoginUser"],
  data(){
    return{
      releaseCheck:false,
      privacyCheck:false,
      secondTab:[
        {name: "全部",isEnable:true,type:"all",component:"all"},
        {name: "文章",isEnable:true,type:"article",component:"all"},
        {name: "课程",isEnable:true,type:"course",component:"all"},
        {name: "会议",isEnable:true,type:"meeting",component:"all"},
        {name: "病例",isEnable:true,type:"cases",component:"all"},
        {name: "问答",isEnable:true,type:"communityQA",component:"all"},
        {name: "短视频",isEnable:true,type:"shortVideo",component: "shortVideo"},
        {name: "手术复盘",isEnable:true,type:"elab",component: "elab"},
        {name: "其他视频",isEnable:true,type:"otherVideo",component:"all"},
      ],
      isActiveTab:'all',
      isActiveType:'all',
      tabFlag:true,
    }
  },
  mounted() {
    console.log(121)
    if(this.collectContentsType !== "all"){
      this.selectHandler('all','all')
    }
    this.viewSettings.openCollect ? this.releaseCheck = true : this.privacyCheck = true;
  },
  methods:{
    selectHandler(type,component){
      if(this.tabFlag){
        this.tabFlag = false;
        this.isActiveType = type;
        this.isActiveTab = component;
        this.$emit("changeFavorite",type,val=>{
          if(val){
            this.tabFlag = true;
          }
        })
      }
    },
    viewSettingHandler(type){
      if(type){
        this.privacyCheck = false;
      }else{
        this.releaseCheck = false;
      }
      this.$axios.$request(saveWebApiHomeViewSettings({
        type:"C",
        isTrue:type
      })).then(res=>{
        console.log(res)
      })
    }
  }
}
</script>


<style>
.user-center-user-favorites-el-popover{
  width: 128px;
  height: 75px;
  border-radius: 12px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
</style>
<style scoped lang="less">
.user-center__popover____wrapper{
  .user-center__popover____item{
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    &:last-child{
      margin-bottom: 0;
    }

    /deep/.el-checkbox__label{
      font-size: 14px;
      line-height: 150%;
      color: #666666;
      padding-left: 5px;
    }
    /deep/ .is-checked {
      .el-checkbox__inner {
        background: #0581CE;
        border: 1px solid var(--theme-color) !important;
      }
    }

    /deep/ .el-checkbox__inner {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      border: 1px solid #999999;
    }

    /deep/ .el-checkbox__inner::after {
      //top: 3px;
      //left: 6px;
    }
  }
}
@import "./styles";
</style>
