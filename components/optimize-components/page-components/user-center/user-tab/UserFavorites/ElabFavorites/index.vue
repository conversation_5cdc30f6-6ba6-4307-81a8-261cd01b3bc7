<template>
  <div class="bg-white">
    <div v-for="(item) in collectContentsData.list" :key="item.content.id" class="list_strip_box" @click="navGetTo(item.content.id)">
      <div class="list_img" :style="`background-image: url(${item.content.image})`">
        <div v-if="item.content.elabSurgicalClassification.code === '全息手术'" class="subscript">
          <img src="~assets/images/elabweb/loadding.png" />
          <span>全景手术</span>
        </div>
      </div>
      <div class="list_txt_box">
        <div class="">
          <h3>{{ item.content.caseName }}</h3>
          <div v-if="item.content.elabSurgicalClassification.code === '全息手术'" class="authorList">
            <div class="author">{{ item.content.hospital&&item.content?item.content.hospital.name:'' }}</div>
          </div>
          <div v-else>
            <div v-if="item.content.authorList && item.content.authorList.length>0" class="authorList">
              <div v-for="(sitem) in item.content.authorList" :key="sitem.id" class="author curBox" @click.stop="navGetToCenter(sitem.id)">{{ sitem.realName }}</div>
            </div>
          </div>
        </div>
        <div class="">
          <div class="tagList">
            <div v-for="sitem in item.content.classificationList" :key="sitem.id" class="tag">#{{sitem.name}}</div>
          </div>
          <div class="introduce">{{item.content.shareDesc?item.content.shareDesc:''}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElabFavorites',
  props: {
    collectContentsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {

    }
  },
  mounted() {
    console.log('-------',this.$props.collectContentsData);
  },
  methods: {
    navGetTo(pid) {
        this.$router.push({ path: '/elabweb/detail', query: { id: pid }})
    },

    // 前往术者中心
    navGetToCenter(id) {
      this.$router.push({
        path: '/user-center',
        query: {
          profileUserId: id
        }
      })
    },
  }
}
</script>


<style lang="less" scoped>
.bg-white{
  background-color: #fff;
}
.list_strip_box {
  background-color: #fff;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
  margin-bottom: 19px;
  .list_img {
    width: 220px;
    height: 124px;
    flex-shrink: 0;
    border-radius: 8px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-right: 16px;
    position: relative;

    .subscript {
      border-radius: 3px;
      position: absolute;
      left: 8px;
      top: 8px;
      display: flex;
      align-items: center;
      height: auto;
      width: 66px;
      padding: 3px 2px;
      box-sizing: border-box;
      height: 23px;
      background: rgba(0, 0, 0, 0.20);
      backdrop-filter: blur(36px);

      span {
        color: #FFF;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17;
      }

      img {
        display: block;
        width: 11px;
        height: 11px;
        flex-shrink: 0;
        margin-right: 2px;
      }
    }
  }

  .list_txt_box {
    width: calc(100% - 236px);
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    h3 {
      color: #333;
      text-overflow: ellipsis;
      font-size: 18px;
      font-weight: 500;
      line-height: 27px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      margin-bottom: 10px;
    }

    .authorList {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .author {
        color: #708AA2;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17px;
        padding: 3.104px 3.104px 3.104px 6.208px;
        margin-right: 12px;
        cursor: pointer;
      }

      .curBox {
        background: rgba(112, 138, 162, 0.08);
        color: #0581CE !important;
      }
    }

    .tagList {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 4px;

      .tag {
        color: #708AA2;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 17px;
        margin-right: 16px;
        margin-bottom: 8px;
      }
    }

    .introduce {
      color: #708AA2;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 17px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
