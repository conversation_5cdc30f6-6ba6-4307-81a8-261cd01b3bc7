<template>
  <div class="user_short_video_wrapper" :style="collectContentsData.list.length ? {padding:'20px 0 20px'} : {}">
    <div class="waterfall">
      <ul class="video_wrapper" ref="video_wrapper">
        <li
          v-for="(item,index) in collectContentsData.list"
          ref='waterfallItem'
          :key="index"
          class="video_item"
        >
          <div
            class="image"
            @click="$store.commit('bms/setBmsHomeShortVideoHandler', item.content.id)"
          >
            <img :src="$tool.compressImg(item.content.cover,252,((252.33 / item.content.coverWidth) * item.content.coverHeight).toFixed(0))" alt=""/>
            <div class="play_icon">
              <svg-icon icon-class="short_video" class-name="icons"/>
              <span class="num">{{$tool.formatterNumUnit(item.content.views)}}</span>
            </div>
          </div>
          <div class="title">
            <span class="text-limit-2">{{item.content.title}}</span>
          </div>
          <div v-if="item.content.creator" class="user_info">
            <div class="user cursor"  @click="jumpUserPageHandler(item.content.creator.id)">
              <div class="image">
                <img class="img_cover" :src="item.content.creator.avatarAddress ?$tool.compressImg(item.content.creator.avatarAddress,24,24) : require('/assets/images/user.png')" alt="">
              </div>
              <div class="user_name">
                {{item.content.creator.realName}}
              </div>
            </div>
            <div class="diggs" @click="diggsHandler(item.content.id)">
              <svg-icon icon-class="user-center-diggs" class-name="icons"/>
              <span class="num">{{$tool.formatterNumUnit(item.content.diggs)}}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {videoDigg} from "../../../../../../../api/bms";

export default {
  name: "FavoritesShortVideo",
  components: {},
  props:["collectContentsData"],
  data(){
    return{
      array: [] // 定义空数组存储元素高度
    }
  },
  watch:{
    'collectContentsData.list':{
      handler(value){
        setTimeout(()=>{
          this.array = []
          this.getWaterfall()
          console.log(this.array)
          this.$refs.video_wrapper.style.height = Math.max(...this.array) + "px"
        },50)
      },
      deep:true
    }
  },
  mounted() {

  },
  methods:{
    diggsHandler(id){
      this.$axios.$request(videoDigg({
        videoId: id
      })).then(res => {
        if (res.code === 1 && res.result.isDigg === 'T') {
          this.collectContentsData.list.forEach(item => {
            if(item.content.id === id){
              this.$set(item.content,"diggs",item.content.diggs+1)
            }
          })
        } else {
          this.collectContentsData.list.forEach(item => {
            if(item.content.id === id){
              this.$set(item.content,"diggs",item.content.diggs > 0 ? item.content.diggs-1 : item.content.diggs)
            }
          })
        }
      })
    },
    jumpUserPageHandler(id){
      const { href } = this.$router.resolve(
        { path: `/user-center?profileUserId=${id}&`}
      )
      window.open(href, '_blank')
    },
    getWaterfall() {
      // 定义布局的列数为2
      const columns = 3;
      // 获取每个子元素的DOM
      const item = this.$refs.waterfallItem;
      console.log("item",item)
      for (let i = 0; i < item.length; i++) {
        // 遍历整个子元素的DOM集合
        if (i < columns) {
          // 小于columns的子元素作为第一行
          item[i].style.top = 20 + 'px';
          item[i].style.left = item[0].clientWidth * i + "px";
          console.log("offsetWidth", item[0].clientHeight)
          // 遍历结束时，数组this.array保存的是第一行子元素的元素高度
          this.array.push(item[i].clientHeight);
          console.log("this.array",this.array)
        } else {
          // 大于等于columns的子元素将作其他行
          const minHeight = Math.min(...this.array); //  找到第一列的最小高度
          const index = this.array.findIndex(item => item === minHeight) // 找到最小高度的索引
          // 设置当前子元素项的位置
          item[i].style.top = this.array[index] +25+ "px";
          item[i].style.left = item[index].offsetLeft + "px";
          // 重新定义数组最小项的高度 进行累加
          this.array[index]+= item[i].clientHeight
          console.log("this.array[index]",this.array[index])
        }
      }
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
