.user_short_video_wrapper{
  background: #FFFFFF;
  .waterfall{
    width: 100%;
    .video_wrapper{
      width: 100%;
      overflow: hidden;
      position: relative;

      .video_item{
        width: 243.5px;
        position: absolute;
        left: -500px;
        border-radius: 6px;
        overflow: hidden;
        padding: 0 9px 20px;
        .image{
          background: #939393;
          position: relative;
          cursor: pointer;
          border-radius: 6px 6px 0 0;
          overflow: hidden;
          .play_icon{
            position: absolute;
            left: 10px;
            bottom: 14px;
            display: flex;
            align-items: center;
            .icons{
              width: 18px;
              height: 13.5px;
            }
            .num{
              color: #FFF;
              font-size: 14px;
              margin-left: 5px;
            }
          }
        }
        img{
          display: block;
          width: 100%;
        }

        .title{
          padding: 14px 12px 0;
          background: #FBFBFB;
          color: #333;
          font-size: 18px;
          line-height: 150%;
          span{

          }
        }

        .user_info{
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #FBFBFB;
          padding: 12px 12px 14px;
          .user{
            display: flex;
            align-items: center;
            .image{
              width: 24px;
              height: 24px;
              border-radius: 50%;
              margin-right: 6px;
              flex-shrink: 0;
              overflow: hidden;
            }
            .user_name{
              font-size: 12px;
              color: #748FA8;

            }
          }
          .diggs{
            display: flex;
            align-items: center;
            color: #999;
            font-size: 14px;
            cursor: pointer;
            .icons{
              width: 20px;
              height: 20px;
            }
          }
        }
        &:hover{
          .title{
            color: var(--theme-color)!important;
          }
        }
      }
    }
  }
}
