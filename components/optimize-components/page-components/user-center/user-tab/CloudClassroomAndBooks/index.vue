<template>
  <div class="cloud_classroom_and_books_wrapper" :style="merchandiseList.list.length || userCourseVideos.list.length ? {padding:'20px 0'} : {}">
    <div v-if="merchandiseList.list.length>0" style="margin-bottom: 20px">
      <MyBooks :merchandise-list="merchandiseList" :is-login-user="isLoginUser"/>
    </div>
    <MyCloudClassroom v-if="userCourseVideos.list.length" :user-course-videos="userCourseVideos"/>
  </div>
</template>

<script>
import MyBooks from "./MyBooks/index.vue";
import MyCloudClassroom from "./MyCloudClassroom/index.vue";

export default {
  name: "CloudClassroomAndBooks",
  props:["merchandiseList","userCourseVideos","isLoginUser"],
  components: {MyCloudClassroom, MyBooks}
}
</script>

<style scoped lang="less">
.cloud_classroom_and_books_wrapper{
  background: #FFFFFF;
}
</style>
