.my_books_wrapper{
  padding: 0 16px;

  .title_content{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .tips{
      color: #333;
      line-height: 150%;
      font-size: 20px;
    }

    .slide_wrapper {
      display: flex;
      align-content: center;

      .icons {
        width: 20px;
        height: 20px;
        color: rgba(#666666, 0.4);
        cursor: not-allowed;
      }

      .is_active{
        color: rgba(#666666, 1);
        cursor: pointer;
      }

      .left_icons {
        transform: rotateZ(180deg);
        margin-right: 10px;
      }
    }
  }

  .content_big_wrapper{
    overflow: hidden;
    padding: 5px 0;
  }
  .books_wrapper{
    display: flex;
    transition: all .6s;
    .books_item{
      flex-shrink: 0;
      width: 186px;
      margin-right: 18px;
      &:last-child{
        margin-right: 0;
      }
    }
  }
}
