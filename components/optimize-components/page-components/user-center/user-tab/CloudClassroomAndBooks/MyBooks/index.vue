<template>
  <div class="my_books_wrapper">
    <div class="title_content">
      <div class="tips">
        {{isLoginUser === 'T' ? "我" : "Ta"}}的书籍
      </div>
      <div class="slide_wrapper">
        <svg-icon
          :style='slideNum>0? { color: "unset",cursor: "pointer"} : {}'
          icon-class='user-center-slide'
          class-name='left_icons icons'
          @click='slideHandler("left")' />
        <svg-icon
          :style='slideNum+1<Math.ceil(merchandiseList.list.length / 3)? { color: "unset",cursor: "pointer"} : {}'
          icon-class='user-center-slide'
          class-name='right_icons icons'
          @click='slideHandler("right")' />
      </div>
    </div>

    <div class="content_big_wrapper">
      <div class="books_wrapper" ref="books_wrapper">
        <div
          v-for='item in merchandiseList.list'
          :key='item.id'
          class="books_item"
          ref="books_item"
        >
          <BooksCouponItem
            :id='item.id'
            :cover='item.cover'
            :name='item.name'
            :price='item.price'
            :discount-price='item.discountPrice'
            :introduction='item.introduction'
            :press-name='item.pressName'
            image-height='186px'
            is-default-price='F'
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BooksCouponItem from "../../../../../public/article-types-list/BooksCouponItem/index.vue";

export default {
  name: "MyBooks",
  components: {BooksCouponItem},
  props:["merchandiseList","isLoginUser"],
  data(){
    return{
      slideNum:0
    }
  },
  methods:{
    slideHandler(type){
      const bookWrapper = this.$refs.books_wrapper;
      const bookItemWidth = this.$refs.books_item[0].clientWidth
      const maxNum = Math.ceil(this.merchandiseList.list.length / 3)
      if(type === "left"){
        if(this.slideNum>0){
          this.slideNum -= 1;
          bookWrapper.style.transform = `translateX(calc(-${bookItemWidth*3*this.slideNum}px - ${(18*3)*this.slideNum}px))`
        }
      }

      if(type === "right"){
        if(this.slideNum+1 < maxNum){
          this.slideNum += 1;
          bookWrapper.style.transform = `translateX(calc(-${bookItemWidth*3*this.slideNum}px - ${(18*3)*this.slideNum}px))`
        }
      }


    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
