<template>
  <div v-if="allFollowersData.elab.length" class='special-column'>
    <div class='special-column-list_set'>
      <ul class='column-list'>
        <li v-for='item in allFollowersData.elab' :key="item.id" class='post-item-container'>
          <a :href='`/elabweb/columnlist/${item.id}`' class='post-item'
            target='_blank'>
            <div class='img-box'>
              <div v-if='item.type === "O"' class='boutique'>
                全景手术
              </div>
              <img v-if='item.cover' :src='$tool.compressImg(item.cover, 242, 141)' alt='专栏图片' class='img_cover'>
              <img v-else alt='专栏图片' class='img_cover' src='~assets/images/default3.png'>
            </div>
            <div class='post-content'>
              <p class='title text-limit-2'>
                {{ item.title }}
              </p>
              <p class='info-content'>
                <span class='readNum'>
                  {{ $tool.formatterNum(item.cases) }}病例
                </span>
                <span class='articleNum'>
                  {{ item.views }}观看
                </span>
              </p>
              <p class='time'>
                {{ timeStamp.timestamp_13(item.updateTime, 'y-m-d') }}更新
              </p>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElabColumn',
  props: {
    allFollowersData: {}
  }
}
</script>

<style lang='less' scoped>
.special-column {
  .special-column-list_set {
    padding: 20px 16px;
    background: #FFF;
  }
}

.column-list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 20px 18px;
}


.post-item-container {
  border-radius: 6px;
  transition: all .3s;
  background: #FBFBFB;

  &:hover {
    background: #FAFCFE;
  }

  &:hover .title {
    color: var(--theme-color) !important;
  }
}

.post-item {
  //display: grid;
  //grid-auto-flow: column;
  //grid-template-rows: 12px 12px 8px;
  transition: all .3s;
  background: #FBFBFB;


  .img-box {
    width: 100%;
    height: 141px;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    position: relative;

    .boutique {
      position: absolute;
      right: 6px;
      top: 6px;
      line-height: 18px;
      background: #0581CE;
      border-radius: 6px;
      font-size: 10px;
      color: #FFFFFF;
      padding: 0 8px;
    }
  }

  .post-content {
    padding: 12px;

    .title {
      font-size: 18px;
      line-height: 24px;
      color: #202020;
      margin-bottom: 12px;
      transition: all .3s;
    }

    .info-content {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 8px;

      span {
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #708AA2;
      }

      .readNum {}

      .articleNum {
        margin-left: 8px;
      }
    }

    .time {
      font-weight: 400;
      font-size: 10px;
      line-height: 12px;
      color: #708AA2;
    }
  }
}
</style>
