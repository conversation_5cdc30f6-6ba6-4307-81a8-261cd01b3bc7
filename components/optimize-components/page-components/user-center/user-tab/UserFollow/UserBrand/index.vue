<template>
  <div class="user_brand_wrapper" :style="allFollowersData.brandAndLineList.length ? {padding:'20px 0'}  :{}">
    <div
      v-for="(item,index) in allFollowersData.brandAndLineList"
      :key="index"
      class="user_brand_item">
      <BrandArticlItem
        v-if="item.type === 'brand'"
        :id="item.brand.id"
        :name="item.brand.name"
        :logo="item.brand.logo"
        :products="item.brand.products"
        :views="item.brand.views"
        :articles="item.brand.articles"
        :subscribe-status="item.brand.subscribeStatus"
        @follow="followHandler"
      />
      <BrandArticlItem
        v-else-if="item.type === 'brandProductLine'"
        :id="item.brandProductLine.id"
        :name="item.brandProductLine.name"
        :logo="item.brandProductLine.shareImage"
        :products="item.brandProductLine.products"
        :views="item.brandProductLine.views"
        :brand-name="item.brandProductLine.brand.name"
        :articles="item.brandProductLine.articles"
        :subscribe-status="item.brandProductLine.subscribeStatus"
        :brand-id="item.brandProductLine.brand.id"
        @follow="followHandler"
      />
    </div>
  </div>
</template>

<script>
import BrandArticlItem from "../../../../../public/article-types-list/user-center-page/BrandArticlItem/index.vue";
import {subscribeBrand} from "../../../../../../../api/bms";

export default {
  name: "UserBrand",
  components: {BrandArticlItem},
  props:["allFollowersData"],
  methods:{
    followHandler({id,brandId}){
      this.$axios.$request(subscribeBrand({
        brandId:brandId || id,
        productLineId: id
      })).then(res=>{
        if(res.code === 1){
          if(res.result.followStatus === "T"){
            this.$toast("关注成功")
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.user_brand_wrapper{
  .user_brand_item{
    margin-bottom: 20px;
    &:last-child{
      margin-bottom: 0;
    }
  }
}
</style>
