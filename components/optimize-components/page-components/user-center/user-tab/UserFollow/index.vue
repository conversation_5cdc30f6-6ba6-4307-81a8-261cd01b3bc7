<template>
  <div class="user_favorites_wrapper">
    <div class="second_tab">
      <div class="tab">
        <template v-for="(item,index) in secondTab">
          <div
            v-if="!item.child"
            :key="index"
            class="tab_item"
            :class="isActiveType===item.type ? 'is_active' : ''"
            @click="selectHandler(item.type,[],item.typeArticle,item.name,item.type)"
          >
            {{item.name}}
          </div>
          <el-popover
            v-else
            :key="index"
            placement="bottom"
            width="auto"
            trigger="click"
            popper-class="user-center-user-follow-el-popover"
          >
            <div class="content_list">
              <div
                v-for="itemC in item.child"
                :key="itemC.name"
                class="item"
                :class="isActiveType === itemC.type ? 'is_child_active' : ''"
                @click="selectHandler(itemC.type,item.child,itemC.typeArticle,itemC.name,item.type)"
              >
                {{itemC.name}}
              </div>
            </div>
            <div
              slot="reference"
              :key="index"
              class="tab_item"
              :class="item.child.filter(item => item.type === isActiveType).length>0 ? 'is_active' : ''"
            >
              <span :ref="'tabChildItem'+item.type">{{item.name}}</span>
              <svg-icon icon-class="user_center-down" class-name="icons"/>
            </div>
          </el-popover>
        </template>

      </div>
      <el-popover
        v-if="isLoginUser === 'T'"
        placement="bottom"
        trigger="click"
        popper-class="user-center-user-favorites-el-popover"
       >
        <div class="user-center__popover____wrapper">
          <div class="user-center__popover____item">
            <Checkbox v-model="releaseCheck" @change="viewSettingHandler(true)">公开我的关注</Checkbox>
          </div>
          <div class="user-center__popover____item">
            <Checkbox v-model="privacyCheck" @change="viewSettingHandler(false)">私密我的关注</Checkbox>
          </div>
        </div>
        <div slot="reference" class="edit_btn">
          <svg-icon icon-class="user-center-edit" class-name="icons"/>
          <span>编辑</span>
        </div>
      </el-popover>
    </div>
    <div class="favorites_content">
      <component
        :is="isActiveType"
        :all-followers-data="allFollowersData"
        :user-fans-data="userFansData"
      />
    </div>
  </div>
</template>

<script>
import {Checkbox} from "element-ui";
import { saveWebApiHomeViewSettings} from "../../../../../../api/new-user-center";
import SpecialColumnOpt from "../SpecialColumnOpt/index.vue";
import FollowUsers from "./FollowUsers/index.vue";
import DepartmentUsers from "./DepartmentUsers/index.vue";
import TopicCircleUsers from "./TopicCircleUsers/index.vue";
import UserBrand from "./UserBrand/index.vue";
import UserZone from "./UserZone/index.vue";
import UserProduct from "./UserProduct/index.vue";
import FansUsers from "./FansUsers/index.vue";
import ElabColumn from "./ElabColumn/index.vue";
export default {
  name: "UserFollow",
  components:{
    Checkbox,
    FollowUsers,
    SpecialColumnOpt,
    DepartmentUsers,
    TopicCircleUsers,
    UserBrand,
    UserZone,
    UserProduct,
    FansUsers,
    ElabColumn
  },
  props:["allFollowersData","viewSettings","userFansData","allFollowersType","isLoginUser"],
  data(){
    return{
      releaseCheck:false,
      privacyCheck:false,
      secondTab:[
        {name: `${this.isLoginUser === 'T' ? "我" : "Ta"}关注的人`,isEnable:true,type:"FollowUsers",typeArticle:'user'},
        {name: `${this.isLoginUser === 'T' ? "我" : "Ta"}关注的专栏`,isEnable:true,type:"special",child:[
            {name: "关注的文章专栏",isEnable:true,type:"SpecialColumnOpt",typeArticle:'special'},
            {name: "关注的手术复盘专栏",isEnable:true,type:"ElabColumn",typeArticle:'elab'},
            {name: "关注的科室",isEnable:true,type:"DepartmentUsers",typeArticle:'department'},
            {name: "关注的话题圈子",isEnable:true,type:"TopicCircleUsers",typeArticle:'communityQA'},
          ]},
        {name: `${this.isLoginUser === 'T' ? "我" : "Ta"}关注的品牌`,isEnable:true,type:"brand",child:[
            {name: "关注的品牌",isEnable:true,type:"UserBrand",typeArticle:'brandAndLineList'},
            {name: "关注的专区",isEnable:true,type:"UserZone",typeArticle:'brandActivityList'},
            {name: "关注的产品",isEnable:true,type:"UserProduct",typeArticle:'productList'},
          ]},
        {name: `关注${this.isLoginUser === 'T' ? "我" : "Ta"}的粉丝`,isEnable:true,type:"FansUsers",typeArticle:'fans'},
      ],
      isActiveType:this.$route.query.type === 'fans' ? 'FansUsers' : 'FollowUsers',
    }
  },
  watch:{
    '$route.query.type': {
      handler(newVal,oldVal){
        if(newVal === 'fans'){
          this.isActiveType = 'FansUsers'
          this.selectHandler("FansUsers",[],"fans", `关注${this.isLoginUser === 'T' ? "我" : "Ta"}的粉丝`,"")
        }else{
          this.isActiveType = 'FollowUsers'
          this.selectHandler("FollowUsers",[],"user",`${this.isLoginUser === 'T' ? "我" : "Ta"}关注的人`,"")
        }
      }
      },
      deep: true
  },
  mounted() {
    console.log('allFollowersData',this.allFollowersData);
    if(this.allFollowersType !== "user"){
      this.selectHandler("FollowUsers",[],"user",`${this.isLoginUser === 'T' ? "我" : "Ta"}关注的人`,"")
    }
    if(this.$route.query.type === 'fans'){
      this.isActiveType = 'FansUsers'
    }
    this.viewSettings.openFollow ? this.releaseCheck = true : this.privacyCheck = true;

  },
  methods:{
    selectHandler(type,child,typeArticle,name,FType){
      if(child.length>0){
        this.$refs["tabChildItem"+FType][0].innerText = `${this.isLoginUser === 'T' ? "我" : "Ta"}${name}`
      }

      this.isActiveType = type;
      this.$emit("changeFollow",typeArticle)

      // if(type === 'FansUsers'){
      //   this.$router.push({path:this.$route.fullPath,query:{type:'fans'}})
      // }else{
      //   this.$router.push({path:this.$route.fullPath,query:{type:null}})
      // }

    },
    viewSettingHandler(type){
      if(type){
        this.privacyCheck = false;
      }else{
        this.releaseCheck = false;
      }
      this.$axios.$request(saveWebApiHomeViewSettings({
        type:"F",
        isTrue:type
      })).then(res=>{})
    },
  }
}
</script>


<style>
.user-center-user-favorites-el-popover{
  width: 128px;
  height: 75px;
  border-radius: 12px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
}
.user-center-user-follow-el-popover{
  min-width: unset;
  width: auto;
  padding: 10px 0;
  border-radius: 12px;
}
</style>
<style scoped lang="less">
.tab_item{
  margin-right: 48px!important;
  display: flex;
  align-items: center;
  .icons{
    width: 20px;
    height: 20px;
  }
}
.content_list{
  .item{
    text-align: center;
    padding: 6px 16px;
    font-size: 14px;
    color: #999;
    line-height: 1.5;

    &:hover{
      cursor: default;
      color: #333;
      background: #F2F9FD;
    }
  }
  .is_child_active{
    cursor: default;
    color: #333;
    background: #F2F9FD;
  }
}
.user-center__popover____wrapper{
  .user-center__popover____item{
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    &:last-child{
      margin-bottom: 0;
    }

    /deep/.el-checkbox__label{
      font-size: 14px;
      line-height: 150%;
      color: #666666;
      padding-left: 5px;
    }
    /deep/ .is-checked {
      .el-checkbox__inner {
        background: #0581CE;
        border: 1px solid var(--theme-color) !important;
      }
    }

    /deep/ .el-checkbox__inner {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      border: 1px solid #999999;
    }

    /deep/ .el-checkbox__inner::after {
      //top: 3px;
      //left: 6px;
    }
  }
}
@import "./styles";
</style>
