<template>
  <div class="topic_circle_wrapper" :style="allFollowersData.communityQA.length ? {padding:'20px 0'} : {}">
    <div class="wrapper_item">
      <template v-for="(item) in allFollowersData.communityQA">
        <TopicCircleActivityItem
          v-if="item.type === 'activity'"
          :id="item.activity.id"
          :key="item.id"
          :title="item.activity.title"
          :description="item.activity.latestContentTitle"
        />
        <TopicCircleArticleItem
          v-if="item.type === 'circle'"
          :id="item.circle.id"
          :key="item.id"
          :title="item.circle.title"
          :type="item.type"
          :is-follow="item.circle.isFollow"
          :contents="item.circle.contents"
          :image="item.circle.image"
          :description="item.circle.description"
          @follow="(id) => followHandler('C',id)"
        />
        <TopicCircleArticleItem
          v-else-if="item.type === 'topic'"
          :id="item.topic.id"
          :key="item.id"
          :title="item.topic.title"
          :type="item.type"
          :is-follow="item.topic.isFollow"
          :contents="item.topic.contents"
          :image="item.topic.image"
          :description="item.topic.description"
          @follow="(id) => followHandler('T',id)"
        />
        <TopicCircleTwoArticleItem
          v-else-if="item.type === 'question'"
          :id="item.question.questionId"
          :key="item.id"
          :regex-text="item.question.regexText"
          :question-title="item.question.questionTitle"
          :creator-avatar="item.question.creatorAvatar"
          :creator-id="item.question.creatorId"
          :creator-name="item.question.creatorName"
          :is-follow="item.question.isFollow"
          :qa-detail-url="item.question.qaDetailUrl"
          @follow="(id) => followHandler('Q',id)"
        />
      </template>
    </div>
  </div>
</template>

<script>
import {follow} from "../../../../../../../api/topic-circle";
import TopicCircleArticleItem
  from "../../../../../public/article-types-list/user-center-page/TopicCircleArticleItem/index.vue";
import TopicCircleTwoArticleItem
  from "../../../../../public/article-types-list/user-center-page/TopicCircleTwoArticleItem/index.vue";
import TopicCircleActivityItem
  from "../../../../../public/article-types-list/user-center-page/TopicCircleActivityItem/index.vue";
export default {
  name: "TopicCircle",
  components: {
    TopicCircleArticleItem,
    TopicCircleTwoArticleItem,
    TopicCircleActivityItem
  },
  props:["allFollowersData"],
  methods:{
    followHandler(type,id){
      this.$axios.$request(follow({
        contentId:id,
        userId:this.$store.state.auth.user.id,
        type
      })).then(res=>{
        if(res.code === 1){
          console.log(res)
        }
      })
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.topic_circle_wrapper{
  .wrapper_item{
    display: flex;
    flex-flow: column;
    grid-gap: 20px 0 ;
  }
}
</style>
