<template>
  <div class="department_wrapper" :style="allFollowersData.department.length ? {padding:'20px 0'} : {}">
    <div class="department_list">
      <template v-for="(item,index) in allFollowersData.department">
        <DepartmentArticleItem
          :id="item.cmsDepartment.id"
          :key="index"
          :company="item.hospitalGroupName"
          :img="item.cmsDepartment.iconUrl"
          :is-subscribe="item.isSubscribe"
          :member-total="item.memberTotal"
          :cms-department="item.cmsDepartment.name"
        />
      </template>
    </div>
  </div>
</template>

<script>
import DepartmentArticleItem
  from "../../../../../public/article-types-list/user-center-page/DepartmentArticleItem/index.vue";

export default {
  name: "DepartmentUsers",
  components: {DepartmentArticleItem},
  props:["allFollowersData"]
}
</script>

<style scoped lang="less">
.department_wrapper{
  .department_list{
    display: flex;
    flex-flow: column;
    grid-gap: 20px 0;
  }
}
</style>
