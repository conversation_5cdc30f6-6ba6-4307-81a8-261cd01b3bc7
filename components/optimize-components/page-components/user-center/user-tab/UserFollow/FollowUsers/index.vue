<template>
  <div class="follow_users_wrapper" :style="allFollowersData.user.length ? {padding:'20px 0'} : {}">
    <div class="wrapper">
      <template v-for="item in allFollowersData.user">
        <UserItem
          :id="item.id"
          :key="item.id"
          :real-name="item.realName"
          :company="item.company"
          :avatar-address="item.avatarAddress"
          :department="item.department"
          :is-follow="item.isFollow"
          :fans="item.fans"
          :user-post-num="item.userPostNum"
          @followFun="followFun"
        />
      </template>
    </div>
  </div>
</template>

<script>
import UserItem from "../../../../../public/article-types-list/user-center-page/UserItem/index.vue";

export default {
  name: "FollowUsers",
  components: {UserItem},
  props:["allFollowersData"],
  methods:{
    followFun(id){
      this.allFollowersData.user.forEach(item=>{
        if(item.id === id){
          this.$set(item,"isFollow",!item.isFollow)
        }
      })
      this.$store.dispatch('follow', id)
    }
  }
}
</script>

<style scoped>

</style>
