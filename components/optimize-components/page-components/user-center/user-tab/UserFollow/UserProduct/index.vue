<template>
  <div class="user_product_wrapper" :style="allFollowersData.productList.length ? {padding:'20px 16px'} : {}">
    <template v-for="item in allFollowersData.productList">
      <UserProductsItem
        :id='item.id'
        :key='item.id'
        :title='item.name'
        :category-name='item.categoryName ? item.categoryName : ""'
        :logo='item.brandShareImage'
        :brand-name='item.brandName'
        :img='item.image'
        :category-id='"-"'
        :brand-id='item.brandId'
        :brand-product-line-id='item.brandProductLine ? item.brandProductLine.id : null'
      />
    </template>
  </div>
</template>

<script>
import UserProductsItem from "../../../../../public/article-types-list/user-center-page/UserProductsItem/index.vue";

export default {
  name: "UserProduct",
  components: {UserProductsItem},
  props:["allFollowersData"]
}
</script>

<style scoped lang="less">
.user_product_wrapper{
  display: grid;
  grid-gap: 20px 18px;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 0 16px;
}
</style>
