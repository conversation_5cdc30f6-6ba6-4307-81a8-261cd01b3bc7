<template>
  <div class="follow_users_wrapper" :style="userFansData.list.length ? {padding:'20px 0'} : {}">
    <div class="wrapper">
      <template v-for="item in userFansData.list">
        <UserItem
          :id="item.id"
          :key="item.id"
          :real-name="item.realName"
          :company="item.company"
          :avatar-address="item.avatarAddress"
          :department="item.department"
          :is-follow="item.isFollow"
          :fans="item.fans"
          :user-post-num="item.userPostNum"
          :identity="item.identity"
          @followFun="followFun"
        />
      </template>
    </div>
  </div>
</template>

<script>
import UserItem from "../../../../../public/article-types-list/user-center-page/UserItem/index.vue";

export default {
  name: "FansUsers",
  components: {UserItem},
  props:["userFansData"],
  methods:{
    followFun(id){
      this.userFansData.list.forEach(item=>{
        if(item.id === id){
          this.$set(item,"isFollow",!item.isFollow)
        }
      })
      this.$store.dispatch('follow', id)
    }
  }
}
</script>

<style scoped>

</style>
