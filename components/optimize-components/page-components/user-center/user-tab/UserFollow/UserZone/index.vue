<template>
  <div class="user_zone_wrapper" :style="allFollowersData.brandActivityList.length ? {padding:'20px 0'} : {}">
    <template v-for="item in allFollowersData.brandActivityList">
      <ZoneArticlItem
        :id="item.id"
        :key="item.id"
        :logo="item.cover"
        :views="item.views"
        :articles="item.contents"
        :name="item.name"
        :subscribe-status="'T'"
        :brand-name="item.brand.name"
        :brand-logo="item.brand.shareImage"
        :brand-id="item.brand.id"
        @follow="followHandler"
      />
    </template>
  </div>
</template>

<script>
import ZoneArticlItem from "../../../../../public/article-types-list/user-center-page/ZoneArticlItem/index.vue";
import {subscribeActivity} from "../../../../../../../api/bms";

export default {
  name: "UserZone",
  components: {ZoneArticlItem},
  props:["allFollowersData"],
  methods:{
    followHandler(id){
      this.$axios.request(subscribeActivity({
        activityId: id
      })).then(res=>{
        if(res.code === 1){
          if(res.result.followStatus === "T"){
            this.$toast("关注成功")
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.user_zone_wrapper{
  display: flex;
  flex-flow: column;
  grid-gap: 20px 0;
}
</style>
