<template>
  <div class="meeting_opt_wrapper" :style="meetingDataList.length ? {padding:'20px 0'} : {}">
    <div
      v-if="userCategory === 'P'"
      class="userCategory_P">
      <MeetingArticleItem
        v-for="item in meetingDataList"
        :id="item.id"
        :key="item.id"
        :meeting-name="item.meetingName"
        :meeting-address="item.meetingAddress"
        :app-main-pic="item.appMainPic"
        :meeting-date-str="item.meetingDateStr"
        :meeting-id="item.meetingId"
        :schedule-list="item.itemList"
        :meeting-live-status="item.meetingLiveStatus"
        :fields-id="item.fieldsId"
        :theme="item.theme"
        :summary-show="item.summaryShow"
      />
    </div>
    <div
      v-else
      class="userCategory_E">
      <MeetingArticleInstitutionItem
        v-for="item in meetingDataList"
        :id="item.id"
        :key="item.id"
        :meeting-name="item.meetingName"
        :meeting-address="item.meetingAddress"
        :app-main-pic="item.appMainPic"
        :meeting-date-str="item.meetingDateStr"
        :meeting-id="item.id"
        :meeting-live-status="item.meetingLiveStatus"
        :description="item.description"
      />
    </div>
  </div>
</template>

<script>
import MeetingArticleItem from "../../../../public/article-types-list/user-center-page/MeetingArticleItem/index.vue";
import MeetingArticleInstitutionItem
  from "../../../../public/article-types-list/user-center-page/MeetingArticleInstitutionItem/index.vue";

export default {
  name: "MeetingOpt",
  components: {MeetingArticleInstitutionItem, MeetingArticleItem},
  props:["meetingData",'userCategory'],
  computed:{
    meetingDataList(){
      if(this.userCategory === "P"){
        return this.$tool.mergeToolArray(this.meetingData.list, 'meetingId')
      }else{
       return this.meetingData.list
      }
    }
  },
}
</script>

<style scoped lang="less">
.meeting_opt_wrapper{
  background: #FFF;

  .userCategory_P{
    display: grid;
    grid-gap: 20px 0;
  }
  .userCategory_E{
    display: grid;
    grid-gap: 20px 0;
  }
}
</style>
