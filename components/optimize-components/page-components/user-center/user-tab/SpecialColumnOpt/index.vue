<template>
  <div v-if="allFollowersData.special.length" class='special-column'>
    <div class='special-column-list_set'>
      <PostGrid :column-data='allFollowersData.special' />
    </div>
  </div>
</template>

<script>
import PostGrid from './PostGrid/PostGrid.vue'

export default {
  name: 'SpecialColumnOpt',
  components: {
    PostGrid,
  },
  props: {
    allFollowersData:{}
  },
  methods: {
    /**
     *  @author:<PERSON>  @date:2022/10/17 15:46
     *  专栏分页
     */
    handleCurrentChange(item) {
      this.$emit("handleCurrentChange", {type:"column",pageNo:item})
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
