<template>
  <UserCard icon-class='user-center-department' title='所属科室主页'>
    <div class='content'>
      <template  v-for='item in departmentHomePageInfo'>
        <nuxt-link
          v-if="item.isDepartmentHomePage"
          :key='item.departmentId'
          class='department_item'
          target="_blank"
          :to="{path:`/department/detail?departmentId=${item.departmentId}`}"
        >
        <span class='name'>
          {{item.hospitalName}}
        </span>
          <span class='line'>|</span>
          <span class='name'>
          {{item.departmentName}}
        </span>
          <svg-icon icon-class='user-center-more' style='transform: rotateZ(-90deg)' class-name='icons' />
        </nuxt-link>
      </template>
    </div>
  </UserCard>
</template>

<script>
import UserCard from '../UserCard/index.vue'

export default {
  name: 'AffiliationDepartment',
  components: { UserCard },
  props:["departmentHomePageInfo"]
}
</script>

<style scoped lang='less'>
.department_item {
  max-width: 90%;
  display: block;
  margin-top: 20px;

  &:first-child {
    margin-top: 17px;
  }

  .name {
    color: #333;
    font-size: 16px;
    line-height: 150%;
  }

  .line {
    color: #999;
    font-size: 14px;
    line-height: calc(16px * 1.5);
    margin: 0 2px;
  }

  &:hover {
    .name {
      cursor: pointer;
      color: var(--theme-color);
    }
  }
}
</style>
