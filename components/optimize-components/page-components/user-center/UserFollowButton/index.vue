<template>
  <button
    :style='{width,height,borderRadius:radius,background,gridGap:gap}'
    class='follow__button'
    :class='isFollowFlag ? "follow__ok" : ""'
    @mouseleave='collapse(id)'
    @mouseover='expand(id)'
    @click.stop='followHandler()'
  >
    <svg-icon v-show='!isFollowFlag' class-name='tab_icon' :style='{width:iconSize,height:iconSize,color:iconColor}'
              :icon-class='iconName'></svg-icon>
    <span :style='{fontSize,lineHeight:height,color:!isFollowFlag ? color : null}'>{{ isFollowFlag ? hoverShow[id] ? '取消关注' : tipsOkName : tipsNoName }}</span>
  </button>
</template>

<script>
export default {
  name: 'UserFollowButton',
  props: {
    iconColor:{},
    color:{
      type: String,
      default: '#FFFFFF'
    },
    tipsOkName: {
      type: String,
      default: '已关注'
    },
    tipsNoName: {
      type: String,
      default: '关注'
    },
    id: {
      type: Number,
      default: -1
    },
    width: {
      type: String,
      default: '62px'
    },
    height: {
      type: String,
      default: '24px'
    },
    radius: {
      type: String,
      default: '18px'
    },
    fontSize: {
      type: String,
      default: '12px'
    },
    background: {
      type: String,
      default: ''
    },
    gap: {
      type: String,
      default: ''
    },
    iconSize: {
      type: String,
      default: '9px'
    },
    iconName: {
      type: String,
      default: 'subscribe'
    },
    isFollow: {
      default: false
    }
  },
  data() {
    return {
      hoverShow:{},
      isFollowFlag: this.isFollow
    }
  },
  watch: {
    '$store.state.auth.token'(value) {
      if (!this.$store.state.auth.token) {
        this.isFollowFlag = false
      }
    },
    isFollow(value) {
      this.isFollowFlag = this.isFollow
    }
  },

  methods: {
    expand(data) {
      this.$set(this.hoverShow, data, 1)
    },
    collapse(data) {
      this.$delete(this.hoverShow, data)
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-10 18:21
     * @description: 关注
     * ------------------------------------------------------------------------------
     */
    followHandler() {
      this.isFollowFlag = !this.isFollowFlag
      this.$emit('follow', this.id !== -1 ? this.id : null)
    }
  }
}
</script>

<style scoped lang='less'>
.follow__button {
  user-select: none;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  grid-gap: 0 3px;
  background: #0581CE;
  border: none;
  cursor: pointer;

  &:hover {
    background: #0581CE;
    span {
      color: #FFFFFF;
    }
    .tab_icon{
      color: #ffffff!important;
    }
  }

  span {
    font-size: 12px;
    color: #FFFFFF;
  }

  .tab_icon {
    width: 9px;
    height: 9px;
    color: #D8D8D8;
  }
}

.follow__ok {
  cursor: default;
  background: #F8F8F8!important;
  span{
    color:#999!important;
  }
}
</style>
