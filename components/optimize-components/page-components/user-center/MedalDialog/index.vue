<template>
  <Dialog @close="$emit('close')">
    <div class="content">
      <div class="medal_bg_image">
        <img
          v-show="medalInfos.userGet"
          ref="medal_bg_image"
          src="~assets/images/user-center/<EMAIL>" alt="">
        <img
          v-show="!medalInfos.userGet"
          src="~assets/images/user-center/<EMAIL>" alt="">
      </div>
      <div class="medal_image">
        <img :src="medalInfos.userGet ? medalInfos.icon : medalInfos.grayIcon" alt="">
      </div>
      <p class="title">
        {{medalInfos.rankName}}
      </p>
      <div class="grade">
        Lv{{medalInfos.rank}} {{medalInfos.medalRankRuleList[0].description}}
      </div>

      <div class="grad_list">
        <div
          v-for="item in medalsInfo.medalRankList"
          :key="item.id"
          class="grad_item"
          :class="medalInfos.id === item.id ? 'active_grad' : ''"
          @click="selectMedalHandler(item.id)"
        >
          <div
            :class="!item.userGet ? 'grad_item_normal':''"
            class="image"
          >
            <img :src="item.userGet ? $tool.compressImg(item.icon,30,30) : $tool.compressImg(item.grayIcon,30,30)" alt="">
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script>
import Dialog from "../../../public/Dialog/index.vue";

export default  {
  name: "MedalDialog",
  components: {Dialog},
  props:["medalsInfo"],
  data(){
    return{
      medalInfos:{
        medalRankRuleList:[
          {description:""}
        ]
      },
      num:1
    }
  },
  mounted() {
    this.$nextTick(()=>{
      // eslint-disable-next-line no-undef
      setTimeout(()=>{
        this.$refs.medal_bg_image.style.transform = "rotate(60deg)"
      },0)
    })

    this.medalInfos = this.medalsInfo.medalRankList.filter(item => item.rank === this.medalsInfo.rank)[0]
  },
  methods:{
    selectMedalHandler(id){
      this.medalInfos = this.medalsInfo.medalRankList.filter(item => item.id === id)[0];

      // eslint-disable-next-line no-undef
      setTimeout(()=>{
        this.num+=1;
        this.$refs.medal_bg_image.style.transform = `rotate(${60 * this.num}deg)`
      },300)

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
