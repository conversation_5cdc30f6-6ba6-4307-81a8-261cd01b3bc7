<template>
  <div v-if="teamDataList.length>0" class="card_wrapper" :style="teamDataList.length>0 ? {marginBottom:'20px'} : {}">
    <div class="title">
      <div class='default'>
        <svg-icon icon-class='user-center-team' class-name='icons'/>
        <p class='title_content'>
          <span>{{isLoginUser === "T" ? "我加入的团队" : "Ta加入的团队"}}</span>
          <span class='small_title'>{{ teamDataList.length }}个</span>
        </p>
      </div>
    </div>
    <el-skeleton v-if="loading" style="width: 100%">
      <template slot="template">
        <div style="display: flex;align-items: center;justify-content: space-between;margin-bottom: 14px;margin-top: 16px">
          <div>
            <el-skeleton-item
              variant="image"
              style="width: 106px; height: 60px;border-radius: 6px"
            />
          </div>
          <div style="width: calc(100% - 116px)">
            <el-skeleton-item variant="text" style="width: 60%;" />
            <el-skeleton-item variant="text" style="width: 80%;" />
          </div>
        </div>
      </template>
    </el-skeleton>
    <ul v-else class='teams-list-box'>
      <li v-for='(item,index) in teamDataList' :key='item.user.id'
          class='list-item flex_between flex_align_center cursor'
          @click='jumpUserCenterPageHandler(item.user.id)'>
        <div class='list-item-left flex_start'>
          <div class='image flex_shrink img_radius'>
            <img v-if='item.user.avatarAddress' :src='$tool.compressImg(item.user.avatarAddress,60,60)' alt=''
                 class='img_cover'>
            <svg-icon
              v-else
              className='imgbox_amll img_cover cursor'
              iconClass='signinavatar'
            ></svg-icon>
          </div>
          <div class='info'>
            <p class='info-name fontSize16 text-limit-1'>{{ item.user.realName ? item.user.realName : '该用户没有名字' }}</p>
            <p class='info-desc text-limit-2'>{{ item.user.introduction }}</p>
          </div>
        </div>
        <div v-if="item.user.id !== $store.state.auth.user.id"  class='list-item-right flex_shrink' @click.stop='followFun(item.user.id)'>
          <div v-if='!item.user.isFollow' class='follow_btn flex_shrink flex_center flex_align_center themeButton'>
            <svg-icon className='tab_icon' iconClass='subscribe'></svg-icon>
            <span class='fontSize12'>关注</span>
          </div>
          <div v-else class='follow_btn followed_btn' @mouseleave='collapse(index)'
               @mouseover='expand(index)'>
            <span class='fontSize12'>{{ hoverShow[index] ? '取消关注' : '已关注' }}</span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import CardTitle from '@/components/UI/Card/CardTitle/CardTitle'
import { getWebApiMembersByAdministratorId } from '@/api/user-center'

export default {
  name: 'UserCenterJoinTeam',
  components: {
    CardTitle
  },
  props:["isLoginUser"],
  data() {
    return {
      loading:true,
      hoverShow: {},
      teamDataList: []  // 团队数据列表
    }
  },
  mounted() {
    this.getWebApiMembersByAdministratorIdHandler()
  },
  methods: {
    jumpUserCenterPageHandler(id) {
      /**
       *@author:Rick  @date:2022/6/30 18:02  @method:jumpUserCenterPageHandler
       *@desc: 跳转用户主页
       */
      const { href } = this.$router.resolve({ path: `/user-center?profileUserId=${id}` })
      window.open(href, '_blank')
    },
    async followFun(id) {
      /**
       *@author:Rick  @date:2022/6/30 17:07  @method:followFun
       *@desc: 关注作者
       */
      await this.$store.dispatch('follow', id)
      await this.getWebApiMembersByAdministratorIdHandler()
    },
    getWebApiMembersByAdministratorIdHandler() {
      /**
       *@author:Rick  @date:2022/6/30 16:07  @method:getWebApiMembersByAdministratorIdHandler
       *@desc: 获取用户团队接口
       */
      this.$axios.$request(getWebApiMembersByAdministratorId({
        viewerId: this.$store.state.auth.user.id,
        personId: this.$route.query.profileUserId,
        pageNo: 1,
        pageSize: 999
      })).then(res => {
        if (res && res.code === 1) {
          this.teamDataList = res.list;
          this.loading = false;
          this.$emit("joinTeamEnableHandler",res.list.length > 0)
        }
      })
    },
    expand(data) {
      this.$set(this.hoverShow, data, 1)
    },
    collapse(data) {
      this.$delete(this.hoverShow, data)
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
