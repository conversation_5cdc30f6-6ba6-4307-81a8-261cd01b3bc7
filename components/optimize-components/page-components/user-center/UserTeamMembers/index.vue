<template>
  <div class='card_wrapper'>
    <div class='title'>
      <div class='default'>
        <svg-icon icon-class='user-center-team' class-name='icons'/>
        <p class='title_content'>
          <span>团队成员</span>
          <span class='small_title'>{{ membersByAdministratorData.page.totalCount }}人</span>
        </p>
      </div>
      <div v-if="membersByAdministratorData.list.length>1" class='slide_wrapper'>
        <svg-icon
          :style='num > 0 ? {color:"rgba(102, 102, 102, 0.8)",cursor:"pointer"} : {}'
          icon-class='user-center-slide'
          class-name='icons left_icons'
          @click='slideHandler("left")'
        />
        <svg-icon
          :style='(num+1) < membersByAdministratorData.page.totalPage * 2 - 1? {color:"rgba(102, 102, 102, 0.8)",cursor:"pointer"} : {}'
          icon-class='user-center-slide'
          class-name='icons'
          @click='slideHandler("right")'
        />
      </div>
    </div>
    <div class='tips'>
      如需管理团队，请前往脑医汇APP
    </div>
    <el-skeleton v-if="loading" style="width: 100%" animated>
      <template slot="template">
        <div style="display: flex;justify-content: space-between">
          <div v-for="item in 4" :key="item" style="display: flex;align-items: center;flex-flow: column">
            <el-skeleton-item variant="image" style="width:60px; height: 60px;border-radius: 50%"/>
            <div style="width: 100%;text-align: center;margin-top: 8px">
              <el-skeleton-item variant="text" style="width: 70%;"/>
            </div>
          </div>
        </div>
      </template>
    </el-skeleton>
    <div v-else class='user_content'>
      <div class='wrapper' ref='wrapper'>
        <div v-for='(item,index) in membersByAdministratorData.list' :key='index' class='content'>
          <div v-for='itemC in item' :key='itemC.user.id' class='user_item' @click="jumpUserPageHandler(itemC.user.id)">
            <div class='image'>
              <img class='img_cover'
                   :src='itemC.user.avatarAddress ? $tool.compressImg(itemC.user.avatarAddress,60,60) : require("/assets/images/user.png")'
                   :alt='itemC.user.realName'>
            </div>
            <div class='user_name text-limit-1'>
              {{ itemC.user.realName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getWebApiMembersByAdministratorId} from "../../../../../api/user-center";

export default {
  name: 'UserTeamMembers',
  components: {},
  props: [],
  data() {
    return {
      loading: true,
      num: 0,
      membersByAdministratorData: {
        list: [],
        page: {}
      },
      i4Result: [],
      maxNum: 0
    }
  },
  mounted() {
    this.getMembersListHandler({pageNo: 1})
  },
  methods: {
    jumpUserPageHandler(profileUserId) {
      window.location.href = `user-center?profileUserId=${profileUserId}`
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-07-05 16:39
     * 获取团队成员/ 加入的企业
     * ------------------------------------------------------------------------------
     */
    getMembersListHandler({pageNo = 1}) {
      this.$axios.$request(getWebApiMembersByAdministratorId({
        viewerId: this.$store.state.auth.user.id,
        personId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 8
      })).then(res => {
        if (res.code === 1) {
          this.membersByAdministratorData = res;

          for (let i = 0; i < res.list.length; i += 4) {
            this.i4Result.push(res.list.slice(i, i + 4));
          }

          this.membersByAdministratorData.list = this.i4Result


          this.loading = false;
        }
      })
    },
    slideHandler(type) {
      const wrapper = this.$refs.wrapper
      if (type === 'left') {
        if (this.num > 0) {
          this.num -= 1
        }
        wrapper.style.transform = `translateX(-${this.num}00%)`

      } else {
        if (this.num + 1 < this.membersByAdministratorData.page.totalPage * 2 - 1) {
          this.num += 1
          if (this.num > this.maxNum) {
            this.getMembersListHandler({pageNo: this.num + 1});
            this.maxNum = this.num
          }
          console.log(this.maxNum)

          wrapper.style.transform = `translateX(-${this.num}00%)`
        }
      }
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
