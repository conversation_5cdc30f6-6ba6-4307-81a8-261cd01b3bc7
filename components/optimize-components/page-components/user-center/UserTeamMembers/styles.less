.card_wrapper {
  padding: 18px 14px;
  border-radius: 6px;
  background: #FFF;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .default {
      display: flex;
      align-items: center;

      .icons {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }

      color: #333;
      font-size: 20px;
      line-height: 150%;

      .title_content {
        display: flex;
        align-items: end;
      }

      .small_title {
        font-size: 16px;
        line-height: 1.5;
        margin-left: 4px;
      }
    }

    .slide_wrapper {
      display: flex;
      align-content: center;

      .icons {
        width: 20px;
        height: 20px;
        color: rgba(#666666, 0.4);
        cursor: not-allowed;
      }

      .left_icons {
        transform: rotateZ(180deg);
        margin-right: 10px;
      }
    }

  }

  .tips {
    color: #999;
    font-size: 12px;
    line-height: 150%;
    padding-left: 30px;
    margin: 4px 0 14px;
  }

  .user_content {
    overflow: hidden;
  }

  .wrapper {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    transition: all .2s;

  }

  .content {
    flex-shrink: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;

    .user_item {
      cursor: pointer;
      max-width: 80px;
      flex-shrink: 0;
      display: flex;
      flex-flow: column;
      align-items: center;

      &:nth-child(4n) {
        margin-right: 0;
      }

      .image {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
      }

      .user_name {
        color: #333;
        text-align: center;
        font-size: 16px;
        line-height: 150%;
        margin-top: 10px;
      }
    }
  }
}
