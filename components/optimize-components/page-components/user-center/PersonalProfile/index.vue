<template>
  <div class='personal_profile'>
    <div
      class='user_image'
      id="demoCanvas"
    >
      <div class='image'>
        <img
          class='img_cover'
          :src='personalInfo.avatarAddress ? $tool.compressImg(personalInfo.avatarAddress,168,168) : require("/assets/images/user.png")'
          alt=''>
      </div>
      <div
        v-if='bgType !== "svga" && personalInfo.avatarPendant'
        class='image bg_image'
        :style='{background:`url(${personalInfo.avatarPendant.icon})  center center / 100% no-repeat`}'
      >
      </div>
    </div>
    <div class='user_information'>
      <div class='user_title'>
        <div class='user_name'>{{personalInfo.realName}}</div>
        <el-popover
          v-if='personalInfo.medalPendantIcon'
          placement="bottom"
          width="140"
          trigger="hover"
          popper-class="user-center-el-popover"
        >
          <div class="medal_popover">
            <div class="image">
              <img :src="personalInfo.medalPendantIcon" style="width: 100%;height: 100%" alt="">
            </div>
            <p class="medal_title">
              {{personalInfo.medalName}}
            </p>
            <p class="medal_condition">
              Lv{{personalInfo.medalRank}} {{personalInfo.condition}}
            </p>
          </div>
        <div
          slot="reference"
          @click="medalsInfoHandler(personalInfo.medalName,personalInfo.medalRank)"
          :style='{background:`url(${personalInfo.medalPendantIcon})  center center / 100% no-repeat`}'
          class='medal cursor'></div>
        </el-popover>
        <div class='grade'>
          <SvgIcon :icon-class='`user-center-level-${personalInfo.levelNumber}`' class-name='icons' />
        </div>
      </div>
      <div class="user_center_info_content">
        <div
          v-for="item in personalInfo.departmentHomePageInfoList"
          :key="item.departmentId"
          class='hospital_content'>
          <span class='name'>{{item.hospitalName}}</span>
          <span v-if='item.departmentName' class='line'>|</span>
          <span class='name'>{{item.departmentName}}</span>
        </div>
        <div class='title'>
          {{personalInfo.title}}
        </div>
        <div
          class='self_introduction'
          ref="self_introduction"
          :style="!isMore ? {maxHeight:'36px',overflow:'hidden'} : {}"
          :class="isShow && !isMore ? 'text-limit-2' : ''"
        >
          <div
            ref="self_content"
            class='self_content'
          >
            {{ personalInfo.resume ? personalInfo.resume.introduction || '暂无简介' : '暂无简介' }}
          </div>
        </div>
      </div>
      <div v-if='personalInfo.resume && personalInfo.resume.introduction && isShow' class='look_more cursor' @click='isMore = !isMore'>
        <span class='name'>{{ isMore ? '收起详细资料' : '查看详细资料' }}</span>
        <svg-icon
          :style='isMore ? {transform: "rotateZ(180deg)"} : {}'
          icon-class='user-center-more'
          class-name='icons'
        />
      </div>
    </div>
    <div class='user_introduction'>
      <div class='content_list'>
        <div
          :class="$route.query.appoint === 'Follow' && $route.query.type !== 'fans' ? 'is_active_user_desc_content' : ''"
          class='user_desc_content cursor'
          @click="selectTabHandler('follow')"
        >
          <div class='name'>
            <span>关注</span>
            <svg-icon v-if="isLoginUser === 'F' && !viewSettings.openFollow" icon-class="user-center-key" class-name="user-center-key-icons"/>
          </div>
          <div class='num'>
            {{$tool.formatterNumUnitUserCenter(personalInfo.followerTotal)}}
          </div>
        </div>
        <div
          :class="$route.query.appoint === 'Follow' && $route.query.type === 'fans' ? 'is_active_user_desc_content' : ''"
          class='user_desc_content cursor'
          @click="selectTabHandler('follow_fans')"
        >
          <div class='name'>
            <span>粉丝</span>
            <svg-icon v-if="isLoginUser === 'F' && !viewSettings.openFollow" icon-class="user-center-key" class-name="user-center-key-icons"/>
          </div>
          <div class='num'>
            {{$tool.formatterNumUnitUserCenter(personalInfo.fansTotal)}}
          </div>
        </div>
        <div class='user_desc_content'>
          <div class='name'>
            获赞
          </div>
          <div class='num'>
            {{$tool.formatterNumUnitUserCenter(personalInfo.diggs)}}
          </div>
        </div>
        <div class='user_desc_content'>
          <div class='name'>
            被收藏
          </div>
          <div class='num'>
            {{$tool.formatterNumUnitUserCenter(personalInfo.collects)}}
          </div>
        </div>
      </div>
      <div class='button_list' :style='personalInfo.isLoginUser === "F" ? {paddingLeft:"160px"} : {}'>
        <div
          v-if='personalInfo.isAuth === "0" && personalInfo.isLoginUser === "T"'
          class='button not_certified'
          @click='$store.dispatch("authenticationHandler")'
        >
          去认证
        </div>
        <div
          v-if='personalInfo.isLoginUser === "T" && personalInfo.isAuth !== "0"'
          class='button certified'
          :style='personalInfo.isAuth === "2" ? {color: "#D29E38"} : personalInfo.isAuth === "3" ? {color: "#CE3505"} : {color: "#0581CE",border:"1px solid #0581CE"}'
        >

          {{ personalInfo.isAuth === '1' ?  '已认证' : personalInfo.isAuth === '2' ? '待审核' : personalInfo.isAuth === '3' ? '未通过' : ''  }}
        </div>

        <div
          v-if='personalInfo.isLoginUser === "T"'
          class='button'
          :class="!personalInfo.userIsPerfect ? 'is_edit_button' : ''"
          @click='jumpEditHandler'>
          <svg-icon icon-class='user-center-edit' class-name='icons' />
          <span>编辑个人资料</span>
        </div>
        <FollowButton
          v-if='personalInfo.isLoginUser === "F"'
          icon-size='10px'
          font-size='14px'
          gap='0 5px'
          icon-name='follow___'
          width='84px'
          height='40px'
          radius='6px'
          background='#0581CE'
          :is-follow='personalInfo.followStatus === "FD"'
          @follow='followHandler'
        />
        <div class='button' @click='shareHandler'>
          <svg-icon icon-class='user-center-share' class-name='icons' />
          <span>分享</span>
        </div>
      </div>
    </div>
    <ShareDialog
      :visible='isShareVisible'
      @cancel='isShareVisible = false'
    />
  </div>
</template>

<script>
import FollowButton from '../../../public/FollowButton/index.vue'
import ShareDialog from '../../../public/ShareDialog/index.vue'
export default {
  name: 'PersonalProfile',
  components: { ShareDialog, FollowButton },
  props:["personalInfo","isLoginUser","viewSettings"],
  data() {
    return {
      isMore: false,
      isShareVisible: false,
      bgType:'',
      isShow:false,
    }
  },
  mounted() {
    this.isShow = this.$refs.self_content.clientHeight > this.$refs.self_introduction.clientHeight;

    if(this.personalInfo.avatarPendant && this.personalInfo.avatarPendant.icon){
      const str = this.personalInfo.avatarPendant.icon.split(".")
      this.bgType = str[str.length-1]
      if(this.bgType === 'svga'){
        this.initMachineSVGA()
      }
    }
  },
  methods: {
    selectTabHandler(type){
      if(this.isLoginUser === 'F' && !this.viewSettings.openFollow){
        this.$toast("该用户隐私设置，关注列表不可见");
        return;
      }

      switch (type){
        case 'follow':{
          this.$emit("changeTabHandler","Follow")
          break;
        }
        case 'follow_fans':{
          this.$emit("changeTabHandler",{name:'Follow',type:'fans'})
          break;
        }
      }
    },
    medalsInfoHandler(name,rank){
      const id = this.personalInfo.userGetMedalInfos.filter(item => item.medalName === name)[0].id
      this.$emit("medalsInfoHandler",{id,rank})
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-07-04 15:48
     * 关注
     * ------------------------------------------------------------------------------
     */
    followHandler(){
      this.$store.dispatch('follow', this.personalInfo.id)
    },
    initMachineSVGA(){
      // eslint-disable-next-line no-undef
      const SVGA  = require("svgaplayerweb")
      const player = new SVGA.Player('#demoCanvas');
      const parser = new SVGA.Parser('#demoCanvas');
      // 必须是服务器地址或者是线上的地址 本地是不可以的 会报错
      parser.load(this.personalInfo.avatarPendant.icon, function(videoItem) {
        player.setVideoItem(videoItem);
        player.startAnimation();
      })
    },
    jumpEditHandler() {
      window.location.href = '/personaldata'
    },
    shareHandler() {
      this.isShareVisible = true
    }
  }
}
</script>


<style>
.user-center-el-popover{
  min-width: 140px!important;
  width: auto!important;
  border-radius: 10px;
  box-sizing: border-box;
}
</style>
<style scoped lang='less'>
.medal_popover{
  text-align: center;
  .image{
    width: 72px;
    height: 72px;
    margin: 0 auto;
  }
  .medal_title{
    font-size: 12px;
    color: #333;
    font-weight: 400;
    margin: 8px 0 5px;
  }
  .medal_condition{
    color: #666;
    font-size: 10px;
  }
}
@import "./styles";
</style>
