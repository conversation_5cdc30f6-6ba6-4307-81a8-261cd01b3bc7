.personal_profile {
  display: flex;
  justify-content: space-between;
  background: #FFFFFF;
  border-radius: 0 0 6px 6px;
  position: relative;

  .user_image {
    flex-shrink: 0;
    width: 228px;
    height: 228px;
    margin-top: -40px;
    position: relative;
    .image {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%,-50%);
      flex-shrink: 0;
      width: 168px;
      height: 168px;
      border-radius: 50%;
      overflow: hidden;
      //margin-top: -10px;
    }
    .bg_image{
      width: 100%;
      height: 100%;
    }
  }

  .user_information {
    flex: auto;
    padding-right: 40px;

    .user_title {
      display: flex;
      align-items: center;
      margin: 10px 0;

      .user_name {
        color: #202020;
        font-size: 24px;
        line-height: 1.5;
        margin-right: 6px;
      }

      .medal {
        margin-right: 6px;
        width: 27px;
        height: 27px;
      }

      .grade {
        .icons {
          width: 42px;
          height: 18px;
        }
      }
    }

    .user_center_info_content{}
    .hospital_content {
      margin-bottom: 4px;
      display: flex;
      align-items: center;

      .name {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }

      .line {
        color: #CCC;
        line-height: 1.5;
        font-size: 12px;
        margin: 0 8px;
      }
    }

    .title {
      color: #999;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 10px;
    }

    .self_introduction {
      color: #666;
      font-size: 12px;
      line-height: 1.5;
      position: relative;
      margin-bottom: 20px;
      .momomo{
        position: absolute;
        right: 0;
        bottom: 0;
        background: #ffffff;
      }
    }

    .look_more {
      margin-bottom: 20px;
      font-size: 12px;
      line-height: 2;
      color: #708AA2;

      .icons {
        transition: all .3s;
      }
    }
  }

  .user_introduction {
    flex-shrink: 0;
    width: 347px;
    padding-right: 20px;

    .content_list {
      display: flex;
      justify-content: space-between;
      margin-top: 40px;

      .user_desc_content {
        text-align: center;

        .name {
          color: #888;
          font-size: 16px;
          line-height: 150%;
          display: flex;
          justify-content: center;
          align-items: center;
          .user-center-key-icons{
            width: 12px;
            height: 12px;
            margin-left: 3px;
          }
        }

        .num {
          color: #333;
          text-align: center;
          font-size: 26px;
          line-height: 150%;
        }
      }
      .is_active_user_desc_content{
        .name {
          color: var(--theme-color);
        }

        .num {
          color: var(--theme-color);
        }
      }
    }

    .button_list {
      margin: 42px 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .button {
        padding: 9px 17px;
        box-sizing: border-box;
        border-radius: 6px;
        border: 1px solid #0581CE;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #0581CE;
        font-size: 14px;
        line-height: 1.5;

        .icons {
          width: 20px;
          height: 20px;
        }
      }

      .not_certified {
        background: #0581CE;
        color: #FFF;
      }

      .certified {
        cursor: not-allowed;
        border: 1px solid #EEE;
      }

      .is_edit_button {
        position: relative;

        &::before {
          content: "";
          position: absolute;
          right: -2px;
          top: -2px;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: #EB3323;
        }
      }
    }
  }
}
