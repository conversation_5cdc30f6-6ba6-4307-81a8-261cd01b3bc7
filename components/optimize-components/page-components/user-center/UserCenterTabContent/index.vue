<template>
  <div id="tab_wrapper" ref="tab_wrapper" class='tab_wrapper'>
    <component
      :is='type'
      :is-login-user="isLoginUser"
      :view-settings="viewSettings"
      :user-category="userCategory"
      :release-data="releaseData"
      :column-list-set="columnListSet"
      :meeting-data="meetingData"
      :merchandise-list="merchandiseList"
      :elab-case-list="elabCaseList"
      :user-course-videos="userCourseVideos"
      :user-videos-data="userVideosData"
      :collect-contents-data="collectContentsData"
      :all-followers-data="allFollowersData"
      :user-fans-data="userFansData"
      :collect-contents-type="collectContentsType"
      :all-followers-type="allFollowersType"
      @changeFavorite="changeFavoriteHandler"
      @handleCurrentChange="handleCurrentChange"
      @changeFollow="changeFollowHandler"
    />
    <LoadingOpt v-if="loading"/>
    <EmptyData
      v-show="!loading && type === 'ReleaseOpt' ? !releaseData.list.length : !loading && type === 'ElabWeb' ? !elabCaseList.list.length: !loading && type === 'UserSpecialColumnOpt' ? !columnListSet.list.length :  !loading && type === 'MeetingOpt' ? !meetingData.list.length : !loading && type === 'CloudClassroomAndBooks' ? !userCourseVideos.list.length :  !loading && type === 'UserShortVideo' ? !userVideosData.list.length  :  !loading && type === 'UserFavorites' ? !collectContentsData.list.length  :  !loading && type === 'Follow' && allFollowersType !== 'fans'? !allFollowersData[allFollowersType].length :  !loading && type === 'Follow' && allFollowersType === 'fans'? !userFansData.list.length   : null"
      height='30vh'
      msg='暂无内容 ~'
    />
  </div>
</template>

<script>
import EmptyData from "../../../UI/EmptyData/index.vue";
import ReleaseOpt from '../user-tab/ReleaseOpt/index.vue'
import UserSpecialColumnOpt from "../user-tab/UserSpecialColumnOpt/index.vue";
import MeetingOpt from "../user-tab/MeetingOpt/index.vue";
import CloudClassroomAndBooks from "../user-tab/CloudClassroomAndBooks/index.vue";
import UserShortVideo from "../user-tab/UserShortVideo/index.vue";
import UserFavorites from "../user-tab/UserFavorites/index.vue";
import UserFollow from "../user-tab/UserFollow/index.vue";
import ElabWeb from "../user-tab/ElabWeb/index.vue";
import {
  getAllFollowers,
  getCollectContents,
  getUserCourseVideos,
  getUserMerchandiseList,
  getUserVideos,
  getWebApiAllUserInfosAndArticles,
  getWebApiUserFans,
  getWebApiUserMeetings,
  getUserSpecials,
  getUserElabCaseList
} from "../../../../../api/new-user-center";
import LoadingOpt from "../../../public/Loading/index.vue";

export default {
  name: 'UserCenterTabContent',
  components: {
    EmptyData,
    LoadingOpt,
    ReleaseOpt,
    UserSpecialColumnOpt,
    MeetingOpt,
    CloudClassroomAndBooks,
    UserShortVideo,
    UserFavorites,
    ElabWeb,
    'Follow': UserFollow
  },
  props: ['type', 'userCategory', 'viewSettings', "isLoginUser"],
  data() {
    return {
      scrollFlag: true,
      loading: true,
      releasePageNo: 1,
      columnListPageNo: 1,
      meetingPageNo: 1,
      userCourseVideosPageNo: 1,
      elabWebPageNo: 1,
      userVideosPageNo: 1,
      collectContentsPageNo: 1,
      allFollowersPageNo: 1,
      collectContentsType: "all",
      allFollowersType: 'user',
      releaseData: {
        list: [],
        page: {}
      },
      columnListSet: {
        list: [],
        page: {}
      },
      meetingData: {
        list: [],
        page: {}
      },
      merchandiseList: {
        list: [],
        page: {}
      },
      userCourseVideos: {
        list: [],
        page: {}
      },
      userVideosData: {
        list: [],
        page: {}
      },
      collectContentsData: {
        list: [],
        page: {}
      },
      elabCaseList: {
        list: [],
        page: {}
      },
      allFollowersData: {
        "user": [],
        "department": [],
        "special": [],
        "topic": [],
        "communityQA": [],
        "brandActivityList": [],
        "brandAndLineList": [],
        "productList": [],
        "elab": [],
        "page": {}
      },
      userFansData: {
        list: [],
        page: {}
      }
    }
  },
  watch: {
    type(newValue) {
      if (newValue) {
        this.scrollFlag = true;
        if (newValue === 'UserShortVideo') {
          // 获取短视频列表
          this.loading = true;
          this.userVideosData = {
            list: [],
            page: {}
          }
          this.userVideosPageNo = 1;
          this.getUserVideosHandler({pageNo: 1})
        }
      }
    },

    '$store.state.global.scroll'(scroll) {

      /* ------------------------------------------------------------------------------ */
      // 右侧导航
      const rightWrapperFather = document.getElementById("right_slide_wrapper_father")
      const rightWrapper = document.getElementById("right_slide_wrapper")
      const userCenterPage = document.querySelector(".user_center_page_wrapper")

      // < 一屏
      if (rightWrapperFather.clientHeight + 80 < window.innerHeight) {

        // > 预定高度
        if (scroll + 70 > rightWrapperFather.offsetTop) {
          const H = userCenterPage.clientHeight - rightWrapperFather.clientHeight

          // > 页面高度
          if (scroll > H) {
            rightWrapper.style.top = 70 - 80 - (scroll - H) + "px"
          } else {
            rightWrapperFather.style.height = rightWrapper.clientHeight + "px"
            rightWrapper.style.cssText = `
           position: fixed;
           top: 70px;
           width: 387px
          `
          }
        } else {
          rightWrapperFather.style.height = "unset"
          rightWrapper.style.cssText = `
           position: unset;
           top: unset;
           width:unset
          `
        }
      } else {
        // > 一屏
        if (scroll > (rightWrapperFather.offsetTop + rightWrapperFather.clientHeight - window.innerHeight + 10)) {

          const H = userCenterPage.clientHeight - rightWrapperFather.clientHeight

          if (scroll > H) {
            rightWrapper.style.bottom = 10 + (scroll - H) + "px"
          } else {
            rightWrapperFather.style.height = rightWrapper.clientHeight + "px"
            rightWrapper.style.cssText = `
           position: fixed;
           bottom: 10px;
           width: 387px
          `
          }

        } else {
          rightWrapperFather.style.height = "unset"
          rightWrapper.style.cssText = `
           position: unset;
           bottom: unset;
           width:unset
          `
        }
      }

      /* ------------------------------------------------------------------------------ */
      // 上滑导航
      const userCenterNav = document.getElementById("user_center_nav_wrapper")

      const navHeader = document.querySelector('#nav_header')
      const tabWrapper = this.$refs.tab_wrapper

      if (scroll > (tabWrapper.offsetTop - 60)) {
        userCenterNav.style.cssText = 'transform:translateY(0%)'

        if (!this.$store.state.cs.TerminalType) {
          navHeader.style.cssText = 'transform:translateY(-100%)'
        }

      } else {
        userCenterNav.style.cssText = 'transform:translateY(-100%)'

        if (!this.$store.state.cs.TerminalType) {
          // eslint-disable-next-line no-unused-expressions
          navHeader ? navHeader.style.cssText = 'transform:translateY(0%)' : null

        }


      }


      /* ------------------------------------------------------------------------------ */
      // 下拉加载
      // 窗口高度
      const windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 页面高度
      const documentHeight = document.documentElement.scrollHeight || document.body.scrollHeight
      if (windowHeight + scroll >= (documentHeight - 500)) {
        if (!this.scrollFlag) return;
        this.scrollFlag = false;
        switch (this.type) {
          case "ReleaseOpt": {
            if (this.releasePageNo < this.releaseData.page.totalPage) {
              // 获取动态列表
              this.releasePageNo += 1;
              this.loading = true;
              this.getWebApiUserInfosAndArticlesHandler({pageNo: this.releasePageNo});
            }
            break;
          }
          case "UserSpecialColumnOpt": {
            if (this.columnListPageNo < this.columnListSet.page.totalPage) {
              // 获取专栏列表
              this.columnListPageNo += 1;
              this.loading = true;
              this.getUserSpecialsHandler({pageNo: this.columnListPageNo});
            }
            break;
          }
          case "MeetingOpt": {
            if (this.meetingPageNo < this.meetingData.page.totalPage) {
              // 获取会议列表
              this.meetingPageNo += 1;
              this.loading = true;
              this.getWebApiUserMeetingsHandler({pageNo: this.meetingPageNo});
            }
            break;
          }
          case "ElabWeb": {
            if (this.elabWebPageNo < this.elabCaseList.page.totalPage) {
              // 获取会议列表
              this.elabWebPageNo += 1;
              this.loading = true;
              this.getUserElabCaseList({pageNo: this.elabWebPageNo});
            }
            break;
          }
          case "CloudClassroomAndBooks": {
            if (this.userCourseVideosPageNo < this.userCourseVideos.page.totalPage) {
              // 获取云课堂列表
              this.userCourseVideosPageNo += 1;
              this.loading = true;
              this.getUserCourseVideosHandler({pageNo: this.userCourseVideosPageNo})
            }
            break;
          }
          case "UserShortVideo": {
            if (this.userVideosPageNo < this.userVideosData.page.totalPage) {
              // 获取短视频列表
              this.userVideosPageNo += 1;
              this.loading = true;
              this.getUserVideosHandler({pageNo: this.userVideosPageNo})
            }
            break;
          }
          case "UserFavorites": {
            if (this.collectContentsPageNo < this.collectContentsData.page.totalPage) {
              // 获取收藏列表
              this.collectContentsPageNo += 1;
              this.loading = true;
              this.getCollectContentsHandler({pageNo: this.collectContentsPageNo, type: this.collectContentsType})
            }
            break;
          }
          case "Follow": {

            if (this.allFollowersType === "fans") {
              if (this.allFollowersPageNo < this.userFansData.page.totalPage) {
                // 获取粉丝列表
                this.allFollowersPageNo += 1;
                this.loading = true;
                this.getWebApiUserFansHandler({pageNo: this.allFollowersPageNo})
              }
            } else {
              if (this.allFollowersPageNo < this.allFollowersData.page.totalPage) {
                // 获取关注列表
                this.allFollowersPageNo += 1;
                this.loading = true;
                this.getAllFollowersHandler({pageNo: this.allFollowersPageNo, type: this.allFollowersType})
              }
            }

            break;
          }
        }
      }
    }
  },
  mounted() {
    // 获取动态列表
    this.getWebApiUserInfosAndArticlesHandler({pageNo: 1});
    // 获取专栏列表
    this.getUserSpecialsHandler({pageNo: 1});
    // 获取会议列表
    this.getWebApiUserMeetingsHandler({pageNo: 1});
    // 获取书籍列表
    this.getUserMerchandiseListHandler({pageNo: 1})
    // 获取云课堂列表
    this.getUserCourseVideosHandler({pageNo: 1})
    // 获取短视频列表
    this.getUserVideosHandler({pageNo: 1})
    // 获取收藏列表
    this.getCollectContentsHandler({pageNo: 1, type: 'all'})
    // 获取关注列表
    this.getAllFollowersHandler({pageNo: 1, type: 'user'})
    // 获取粉丝列表
    this.getWebApiUserFansHandler({pageNo: 1})
    // 获取手术复盘列表
    this.getUserElabCaseList({pageNo: 1})
  },
  methods: {
    // 动态列表
    getWebApiUserInfosAndArticlesHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getWebApiAllUserInfosAndArticles({
        userId: this.$store.state.auth.user.id,
        profileUserId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 20
      })).then(res => {
        if (res && res.code === 1) {
          this.releaseData.list = this.releaseData.list.concat(res.list);
          this.releaseData.page = res.page;
          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 专栏列表
    getUserSpecialsHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getUserSpecials({
        userId: this.$store.state.auth.user.id,
        profileUserId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 20
      })).then(response => {
        if (response && response.code === 1) {
          this.columnListSet.list = this.columnListSet.list.concat(response.list);
          this.columnListSet.page = response.page;

          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 会议列表
    getWebApiUserMeetingsHandler({pageNo = 1, backFn}) {
      const lastMeetingId = this.meetingData.list.length > 0 ? this.meetingData.list[this.meetingData.list.length - 1].id : null
      this.$axios.$request(getWebApiUserMeetings({
        userId: this.$route.query.profileUserId,
        lastMeetingId,
        type: "M",
        pageNo,
        pageSize: 20
      })).then(res => {
        if (res.code === 1) {
          this.meetingData.list = this.meetingData.list.concat(res.list);
          this.meetingData.page = res.page;
          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 手术复盘列表
    getUserElabCaseList({pageNo = 1, backFn}) {
      this.$axios.$request(getUserElabCaseList({
        profileUserId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 8
      })).then(res => {
        if (res.code === 1) {
          this.elabCaseList.list = this.elabCaseList.list.concat(res.list);
          this.elabCaseList.page = res.page;
          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 书籍列表
    getUserMerchandiseListHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getUserMerchandiseList({
        profileUserId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 8
      })).then(res => {
        if (res.code === 1) {
          this.merchandiseList = res;
          if (backFn) {
            backFn()
          }
        }
      })
    },
    // 获取云课堂列表
    getUserCourseVideosHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getUserCourseVideos({
        userId: this.$route.query.profileUserId,
        type: "C",
        pageNo,
        pageSize: 20,
      })).then(res => {
        if (res.code === 1) {
          this.userCourseVideos.list = this.userCourseVideos.list.concat(res.list);
          this.userCourseVideos.page = res.page;


          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 获取短视频列表
    getUserVideosHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getUserVideos({
        creatorId: this.$route.query.profileUserId,
        pageNo,
        pageSize: 20,
        loginUserId: this.$store.state.auth.user.id
      })).then(res => {
        if (res.code === 1) {
          this.userVideosData.list = res.list;
          // this.userVideosData.list = this.userVideosData.list.concat(res.list);
          this.userVideosData.page = res.page;
          if (backFn) {
            backFn()
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 获取收藏列表
    getCollectContentsHandler({pageNo = 1, backFn, type = "all"}) {
      /**
       * 全部类型：all，文章：article ，云课堂：course，会议：meeting ，病例：cases ，短视频：shortVideo ，其他视频：otherVideo，社区问答：communityQA
       */
      this.$axios.$request(getCollectContents({
        collectUserId: this.$route.query.profileUserId,
        type,
        searchCode: "",
        pageNo,
        pageSize: 20
      })).then(res => {
        if (res.code === 1) {
          this.collectContentsData.list = this.collectContentsData.list.concat(res.list);
          this.collectContentsData.page = res.page;
          if (backFn) {
            backFn(true)
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 切换收藏列表
    changeFavoriteHandler(type, backFn) {
      this.collectContentsType = type;
      this.loading = true;
      this.collectContentsPageNo = 1;
      this.collectContentsData = {
        list: [],
        page: {}
      }

      this.getCollectContentsHandler({pageNo: 1, type, backFn})
    },
    // 获取我的关注
    getAllFollowersHandler({pageNo = 1, type, backFn}) {
      this.$axios.$request(getAllFollowers({
        userId: this.$route.query.profileUserId,
        viewerId: this.$store.state.auth.user.id,
        type,
        pageNo,
        pageSize: 20
      })).then(res => {
        if (res.code === 1) {
          if (res.result) {
            this.allFollowersData[type] = this.allFollowersData[type].concat(res.result[type]);
            this.allFollowersData.page = res.result.page;
          }

          if (backFn) {
            backFn(true)
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 切换关注
    changeFollowHandler(type, backFn) {
      this.loading = true;
      this.allFollowersType = type;
      this.allFollowersPageNo = 1;
      this.allFollowersData = {
        "user": [],
        "department": [],
        "special": [],
        "topic": [],
        "communityQA": [],
        "brandActivityList": [],
        "brandAndLineList": [],
        "productList": [],
        "elab": [],
        "page": {}
      }
      if (type === "fans") {
        this.userFansData.list = [];
        this.getWebApiUserFansHandler({pageNo: 1})
      } else {
        this.getAllFollowersHandler({pageNo: 1, type})
      }
    },
    // 获取粉丝列表
    getWebApiUserFansHandler({pageNo = 1, backFn}) {
      this.$axios.$request(getWebApiUserFans({
        personId: this.$route.query.profileUserId,
        viewerId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 20
      })).then(res => {
        if (res.code === 1) {
          this.userFansData.list = this.userFansData.list.concat(res.list);
          this.userFansData.page = res.page;

          if (backFn) {
            backFn(true)
          }

          this.scrollFlag = true;
          this.loading = false;
        }
      })
    },
    // 翻页
    handleCurrentChange({type, pageNo}) {
      const backFn = () => {
        this.$refs.tab_wrapper.classList.remove("tab_wrapper_current");
        this.$tool.scrollIntoTop()
      }
      this.$refs.tab_wrapper.classList.add("tab_wrapper_current");
      switch (type) {
        case "release": {
          this.getWebApiUserInfosAndArticlesHandler({pageNo, backFn});
          break;
        }
        case "column": {
          this.getUserSpecialsHandler({pageNo, backFn});
          break;
        }
      }
    }
  },
}
</script>

<style scoped lang='less'>
.tab_wrapper {
  min-height: 200px;
}

.tab_wrapper_current {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, .4);
    z-index: 1;
  }
}
</style>
