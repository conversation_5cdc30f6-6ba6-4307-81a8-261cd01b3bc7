.edit_wrapper_content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 30px;
  border-radius: 16px;
  background: #FFF;
  width: 770px;
  box-sizing: border-box;

  .tips {
    color: #333;
    /* 16雅黑正文 */
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
  }

  .image {
    width: 100%;
    height: 300px;
  }

  .avatar-container{
    width: 100%;
    height: 300px;
    .avatar-crop{
      width: 100%;
      height: 100%;
    }
  }

  .btn_content {
    display: flex;
    align-content: center;
    margin-top: 16px;
    justify-content: center;

    .btn {
      width: 148px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
    }

    .cancel_btn {
      width: 146px;
      height: 38px;
      border: 1px solid var(--theme-color);
      color: var(--theme-color);
      margin-right: 20px;
    }

    .upload_btn {
      background: var(--theme-color);
      color: #FFF;
    }
  }
}
