<template>
  <NewBackdrop>
    <div class='edit_wrapper_content'>
      <div class='tips'>
        推荐尺寸1200X506
      </div>
      <div v-if="!preview" class='image'>
        <img
          class='img_cover'
          :src='personalBgi ? $tool.compressImg(personalBgi,710,300) : require("/assets/images/user-center/bg_default.jpg")'
          alt=''>
      </div>
      <div v-show="preview" class='avatar-container'>
        <div class='avatar-crop'>
          <component
            v-bind:is="cropper"
            ref='cropper'
            class='crop-box'
            :img="currOptions.img"
            :auto-crop='currOptions.autoCrop'
            :fixed-box='currOptions.fixedBox'
            :can-move-box='currOptions.canMoveBox'
            :auto-crop-width='currOptions.autoCropWidth'
            :auto-crop-height='currOptions.autoCropHeight'
            :center-box='currOptions.centerBox'
            :fixed='currOptions.fixed'
            :fixed-number='currOptions.fixedNumber'
            :can-move='currOptions.canMove'
            :can-scale='currOptions.canScale'
          ></component>
        </div>
      </div>
      <div class='btn_content'>
        <div class='cancel_btn btn' @click='$emit("cancel")'>取消</div>
        <Upload
          v-if="!preview"
          class="avatar-uploader"
          :http-request='httpRequestReply'
          :show-file-list='false'
          action='string'
          accept="image/*"
        >
          <div class='upload_btn btn'>上传</div>
        </Upload>
        <div v-else class='upload_btn btn' @click="imgDataResultBlob">保存</div>
      </div>
    </div>
  </NewBackdrop>
</template>

<script>
import Vue from "vue";
import {Upload} from "element-ui";
// import {VueCropper} from 'vue-cropper'
import NewBackdrop from "../../../UI/NewBackdrop/index.vue";
import {uploadPersonalBgi} from "../../../../../api/new-user-center";
import {uploadOssImage} from "../../../../../api/upload";

export default {
  name: 'EditBgImageDialog',
  components: {NewBackdrop, Upload},
  props: ["personalBgi"],
  data() {
    return {
      cropper: "",
      preview: "",
      currOptions: {
        img: '',             // 裁剪图片的地址
        outputSize: 1,       // 裁剪生成图片的质量(可选0.1 - 1)
        outputType: 'jpeg',  // 裁剪生成图片的格式（jpeg || png || webp）
        info: true,          // 图片大小信息
        canScale: true,      // 图片是否允许滚轮缩放
        autoCrop: true,      // 是否默认生成截图框
        autoCropWidth: 710,  // 默认生成截图框宽度
        autoCropHeight: 300, // 默认生成截图框高度
        fixed: false,         // 是否开启截图框宽高固定比例
        fixedNumber: [1.53, 1], // 截图框的宽高比例
        full: false,         // false按原比例裁切图片，不失真
        fixedBox: true,      // 固定截图框大小，不允许改变
        canMove: true,      // 上传图片是否可以移动
        canMoveBox: false,    // 截图框能否拖动
        original: false,     // 上传图片按照原始比例渲染
        centerBox: true,    // 截图框是否被限制在图片里面
        height: true,        // 是否按照设备的dpr 输出等比例图片
        infoTrue: true,     // true为展示真实输出图片宽高，false展示看到的截图框宽高
        maxImgSize: 3000,    // 限制图片最大宽度和高度
        enlarge: 1,          // 图片根据截图框输出比例倍数
        mode: 'auto'  // 图片默认渲染方式
      }
    }
  },
  mounted() {
    // eslint-disable-next-line no-undef
    const res = require("vue-cropper");
    Vue.component("VueCropper", res.VueCropper);
    this.cropper = "vue-cropper";


  },
  methods: {
    httpRequestReply(item) {
      // 验证图片格式大小信息
      const isJPG = item.file.type.startsWith('image/')
      const isLt2M = item.file.size / 1024 / 1024 < 2
      if (!isJPG) {
        this.$message.error('请选择图片文件')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      const fileObj = item.file
      // 图片格式大小信息没问题 执行上传图片的方法
      if (isJPG && isLt2M === true) {
        // 定义FormData对象 存储文件
        const mf = new FormData()
        // 将图片文件放入mf
        mf.append('file', fileObj)
        mf.append('module', 'user-center')
        this.$axios.$request(uploadOssImage(mf)).then((res) => {
          if (res && res.code === 1) {
            this.preview = res.result.filePath
            this.currOptions.img = res.result.filePath
          }
        })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-07-06 10:11
     * 获取截图信息
     * ------------------------------------------------------------------------------
     */
    imgDataResultBlob(data) {
      this.$refs.cropper.getCropData(data => {
        let formData = new FormData();
        formData.append('image', this.base64ConvertFile(data))
        this.$axios.$request(uploadPersonalBgi(formData)).then(res => {
          if (res.code === 1) {
            this.$toast("保存成功!")
            this.$emit("cancel")
            this.$emit("imgDataResultBlob",res.result.filePath)
          }
        })
      })
      // const myBlob = new window.Blob([data], {type: 'image/jpeg'})
      // 转成url 可用来回显
      // this.fileImg = window.URL.createObjectURL(myBlob)
    },
    // 将base64转换为file继承自blob
    base64ConvertFile(urlData, filename) { // 64转file
      if (typeof urlData != 'string') {
        this.$toast("urlData不是字符串")
        return;
      }
      var arr = urlData.split(',')
      var type = arr[0].match(/:(.*?);/)[1]
      var fileExt = type.split('/')[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], `${filename}.${fileExt}`, {type});
    },
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
