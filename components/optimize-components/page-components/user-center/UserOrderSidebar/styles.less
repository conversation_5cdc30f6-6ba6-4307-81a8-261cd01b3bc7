.order_side_bar_Wrapper{
  background: #FCFCFC;
  border-radius: 6px;
  padding: 10px 0;
  position: sticky;
  top: 70px;
  .side_bar_item{
    cursor: pointer;
    .name{
      user-select: none;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
      color: #999999;
      padding: 8px 0 8px 27px;
      position: relative;

      .child_wrapper{
        height: 24px;
        transition: all .3s;

        .child_name{
          display: flex;
          align-items: center;

          .icons{
            transition: all .3s;
            margin-left: 6px;
            transform: rotateZ(-90deg);
          }
        }
        .child_name_isactive{
          position: relative;
          &::after{
            content:"";
            display: block;
            position: absolute;
            left: -14px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 14px;
            background: #0581CE;
            border-radius: 6px;
          }
        }
        .child_item{
          font-size: 14px;
          padding-left: 4px;
          margin-top: 18px;
          &:hover{
            color: #333;
          }
        }
        .is_active_item{
          color: #333;
        }
      }
    }
    .is_active{
      color: #333333;
      &::after{
        content:"";
        display: block;
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 14px;
        background: #0581CE;
        border-radius: 6px;
      }
    }
    //&:hover{
    //  background: #F2F9FD;
    //}
  }
}
