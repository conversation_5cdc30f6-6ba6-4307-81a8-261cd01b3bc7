<template>
  <div class='order_side_bar_Wrapper'>
    <div
      v-for='(item,index) in list'
      :key='index'
      class='side_bar_item'
      @click='$router.push({path:item.url})'
    >
      <div
        :class='$route.path === item.url ? "is_active" : ""'
        class='name'
      >
        <div
          v-if="item.child.length"
          class="child_wrapper"
          :style="isOpen === item.name ? {height:`${42*(item.child.length+1)-18}px`,overflow:'hidden'} : {overflow:'hidden'}"
        >
          <div class="child_name" :class="isOpen ? 'child_name_isactive' : ''"  @click.stop="!isOpen ? isOpen = item.name : isOpen = null">
            <span>{{item.name}}</span>
            <svg-icon
              icon-class="user-center-order-2"
              class-name="icons"
              :style="isOpen === item.name ? {transform: 'unset'} : {}"
            />
          </div>
          <template v-for="(itemC,indexC) in item.child">
            <p
              :key="indexC"
              class="child_item"
              :class='$route.path === itemC.url ? "is_active_item" : ""'
              @click.stop='$router.push({path:itemC.url})'
            >{{itemC.name}}</p>
          </template>
        </div>
        <span v-else>{{item.name}}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserOrderSidebar',
  props:["isOpenData"],
  data(){
    return{
      isOpen:this.isOpenData,
      list:[
        {name:"我的订单",url:"",child:[
            {name:"实物订单",url:"/user-center/physical-orders"},
            {name:"虚拟订单",url:"/user-center/virtual-orders"},
          ]},
        {name:"我的优惠券",url:"/user-center/coupons",child:[]},
      ]
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
