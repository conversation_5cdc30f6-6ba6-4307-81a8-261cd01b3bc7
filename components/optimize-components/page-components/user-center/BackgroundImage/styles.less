.background_image_wrapper {
  width: 100%;
  height: 240px;
  border-radius: 6px 6px 0 0;
  background: rgba(153, 153, 153, 0.51);
  overflow: hidden;
  position: relative;
  background: url("assets/images/user-center/bg_default.jpg");
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: center center;
  &::before{
    content:"";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.1);
  }

  img {
    width: 100%;
  }

  .edit_background {
    opacity: 0;
    position: absolute;
    right: 10px;
    top: 10px;
    padding: 5px 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    font-size: 12px;
    line-height: 150%;
    color: #FFFFFF;
    cursor: pointer;
    transition: all .3s;

    &:hover {
      background: rgba(0, 0, 0, 0.4);
    }
  }

  &:hover{
    .edit_background{
      opacity: 1;
    }
  }
}
