<template>
  <div
    class='background_image_wrapper'
    :style='personalBgi ? {
      background: `url(${bgPath ||  personalBgi}) center center / 100% no-repeat`,
    } : {}'
  >
    <div v-if='isLoginUser === "T"' class='edit_background' @click='editBgImageHandler'>
      编辑背景图片
    </div>
    <transition name="el-fade-in-linear">
      <EditBgImageDialog
        v-if='isEditBgImg'
        :personal-bgi="bgPath || personalBgi"
        @cancel='isEditBgImg = false'
        @imgDataResultBlob="imgDataResultBlob"
      />
    </transition>
  </div>
</template>

<script>
import EditBgImageDialog from '../EditBgImageDialog/index.vue'

export default {
  name: 'BackgroundImage',
  components: {EditBgImageDialog},
  props: ['personalBgi', 'isLoginUser'],
  mounted() {
  },
  data() {
    return {
      isEditBgImg: false,
      bgPath:""
    }
  },
  methods: {
    editBgImageHandler() {
      this.isEditBgImg = true
    },
    imgDataResultBlob(path){
      this.bgPath = path
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
