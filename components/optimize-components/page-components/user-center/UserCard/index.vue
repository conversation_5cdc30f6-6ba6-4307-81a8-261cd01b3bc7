<template>
  <div class='card_wrapper'>
    <div class='title'>
      <div class='default'>
        <svg-icon :icon-class='iconClass' class-name='icons' />
        <span>{{ title }}</span>
      </div>
      <slot name='title-right'></slot>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'UserCard',
  props: ['iconClass', 'title']
}
</script>

<style scoped lang='less'>
.card_wrapper {
  padding: 18px 14px;
  border-radius: 6px;
  background: #FFF;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .default {
      display: flex;
      align-items: center;

      .icons {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }

      color: #333;
      font-size: 20px;
      line-height: 150%;
    }

  }
}
</style>
