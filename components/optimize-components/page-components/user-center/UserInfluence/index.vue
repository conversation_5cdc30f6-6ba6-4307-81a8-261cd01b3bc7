<template>
  <UserCard icon-class='user-center-ying' title='影响力'>
    <div class='content'>
      <div class='item'>
        <div class='tips_name'>
          身份认证
        </div>
        <div class='status' :style="{color:authNameColor}">
          {{authName}}
        </div>
      </div>
      <div class='item'>
        <div class='tips_name'>
          入驻时间
        </div>
        <div class='grade'>
          {{personalInfo.entryTime}}
        </div>
      </div>
      <div class='item'>
        <div class='tips_name'>
          用户等级
        </div>
        <div class='grade'>
          Lv{{personalInfo.levelNumber}}
        </div>
      </div>
      <div class='item' style="align-items: start">
        <div class='tips_name'>
          我的勋章
        </div>
        <div class='medal-wrapper'>
          <div
            v-for="item in personalInfo.userGetMedalInfos"
            :key="item.id"
            class="medal_item"
            @click="medalsInfoHandler(item.id,item.userMedalRank)"
          >
            <el-popover
              placement="bottom"
              width="140"
              trigger="hover"
              popper-class="user-center-el-popover"
              >
              <div class="medal_popover">
                <div class="image">
                  <img :src="item.userGet ? item.icon : item.grayIcon" style="width: 100%;height: 100%" alt="">
                </div>
                <p class="medal_title">
                  {{item.medalName}}
                </p>
                <p class="medal_condition">
                  Lv{{item.userMedalRank}} {{item.descriptionList[0]}}
                </p>
              </div>
              <img  slot="reference" :src="item.userGet ? item.icon : item.grayIcon" class="image" alt="">
            </el-popover>
          </div>
        </div>
      </div>
    </div>
  </UserCard>
</template>

<script>
import UserCard from '../UserCard/index.vue'

export default {
  name: 'UserInfluence',
  components: { UserCard },
  props:["personalInfo"],
  computed:{
    authName(){
      // 0：未认证；1：已认证；2：待认证 ； 3：认证未过
      if(this.personalInfo.isAuth === "0"){
        return "未认证"
      }
      if(this.personalInfo.isAuth === "1"){
        return "已认证"
      }
      if(this.personalInfo.isAuth === "2"){
        return "待审核"
      }
      if(this.personalInfo.isAuth === "3"){
        return "未通过"
      }

      return ""
    },
    authNameColor(){
      // 0：未认证；1：已认证；2：待认证 ； 3：认证未过
      if(this.personalInfo.isAuth === "0"){
        return "#666"
      }
      if(this.personalInfo.isAuth === "1"){
        return "#0581CE"
      }
      if(this.personalInfo.isAuth === "2"){
        return "#D29E38"
      }
      if(this.personalInfo.isAuth === "3"){
        return "#CE3505"
      }

      return "#666"
    }
  },
  data(){
    return{

    }
  },
  methods:{
    medalsInfoHandler(id,rank){
      this.$emit("medalsInfoHandler",{id,rank})
    }
  }
}
</script>

<style>
.user-center-el-popover{
  min-width: 140px!important;
  width: auto!important;
  border-radius: 10px;
  box-sizing: border-box;
}
</style>
<style scoped lang='less'>
.medal_popover{
  text-align: center;
  .image{
    width: 72px;
    height: 72px;
    margin: 0 auto;
  }
  .medal_title{
    font-size: 12px;
    color: #333;
    font-weight: 400;
    margin: 8px 0 5px;
  }
  .medal_condition{
    color: #666;
    font-size: 10px;
  }
}
.content {
  .item {
    &:first-child {
      margin-top: 17px;
    }

    &:last-child {
      margin-bottom: 0;
    }

    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .tips_name {
      flex-shrink: 0;
      color: #333;
      font-size: 16px;
      line-height: 150%;
      margin-right: 20px;
    }

    .grade {
      color: #666;
      font-size: 14px;
      line-height: 150%;
    }

    .status {
      width: 48px;
      height: 22px;
      border-radius: 4px;
      border: 1px solid #EEE;
      background: #F8F8F8;
      color: #666;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .medal-wrapper {
      display: flex;
      align-items: center;
      grid-gap: 16px 16px;
      margin-left: -2px;
      flex-wrap: wrap;

      .medal_item{
        &:last-child {
          margin-right: 0;
        }
       .image{
         width: 26px;
         height: 26px;
         display: block;
         cursor: pointer;
       }
      }
      .icons {
        width: 26px;
        height: 26px;
        margin-right: 12px;

      }
    }
  }
}
</style>
