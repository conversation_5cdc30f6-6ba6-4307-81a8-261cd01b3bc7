
.replace_wrapper {
  display: flex;
  align-items: center;
  color: #0581CE;
  font-size: 12px;
  cursor: pointer;

  .search_replace_icons {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
}

.wrapper {
  .list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8.5px 0;

    .image_wrapper {
      width: 40px;
      height: 40px;
      flex-shrink: 0;
      margin-right: 12px;
      border-radius: 50%;
      overflow: hidden;
    }

    .user_info {
      flex: auto;

      .content {
        color: #333;
        margin-bottom: 4px;

        .user_name {
          font-size: 14px;
          margin-right: 8px;
          font-weight: 600;
        }

        .title {
          font-size: 14px;
        }
      }

      .company {
        color: #758AA0;
        font-size: 12px;
      }
    }

    &:hover {
      .content {
        .user_name {
          color: var(--theme-color)
        }

        .title {
          color: var(--theme-color)
        }
      }

      .company {
        color: #50789C;
      }
    }
  }
}
