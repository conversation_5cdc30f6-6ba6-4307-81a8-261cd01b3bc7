<template>
  <SearchListCard
    v-if="list.length>0"
    bg-color="linear-gradient(180deg, #E4F5FF -15.67%, #FFF 37.33%)"
    font-color="#333333"
    title="知名专家"
    icon="search_zhuanjia"
  >
    <template #title_right>
      <div class="replace_wrapper userselect_none" @click="replaceHandler">
        <div v-if="loading" style="width: 16px;height: 16px;margin-right: 4px">
          <i class="el-icon-loading"></i>
        </div>
        <SvgIcon v-else icon-class="search_replace" class-name="search_replace_icons"/>
        <span>换一换</span>
      </div>
    </template>
    <ul class="wrapper">
      <li v-for="item in list" :key="item.id">
        <a :href="`/user-center?profileUserId=${item.id}`" target="_blank" class="list">
          <div class="image_wrapper">
            <ImageCDN :src="item.avatarAddress" :type-user="true" width="40" height="40"/>
          </div>
          <div class="user_info">
            <div class="content">
              <span class="user_name">{{ item.realName }}</span>
              <span class="title">{{ item.title }}</span>
            </div>
            <div class="company text-limit-1">{{ item.company }}</div>
          </div>
        </a>
      </li>
    </ul>
  </SearchListCard>
</template>

<script>
import SearchListCard from "../SearchListCard/index.vue";
import {getExpertList} from "../../../../../api/search";
import ImageCDN from "../../../ImageCDN/index.vue";

export default {
  name: "RenownedExperts",
  components: {
    ImageCDN,
    SearchListCard
  },
  data() {
    return {
      list: [],
      loading: false
    }
  },
  mounted() {
    this.getListHandler()
  },
  methods: {
    async getListHandler() {
      const res = await this.$axios.$request(getExpertList({
        userId: this.$store.state.auth.user.id,
        limit: 4
      }))
      this.list = res.list;
      this.loading = false
    },
    async replaceHandler() {
      this.loading = true
      await this.getListHandler()
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
