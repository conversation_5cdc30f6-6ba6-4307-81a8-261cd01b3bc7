<template>
  <div class="high_search_tab_wrapper">
    <Tabs v-model="activeName" @tab-click="tabHandleClick">
      <TabPane label="图文" name="1">
        <SearchDataSorting
          :is-default-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <ImageTextTab :search-data="searchData"/>
        </DropdownLoading>

      </TabPane>
      <TabPane label="视频" name="2">
        <SearchDataSorting
          :is-essences-enable="false"
          :is-default-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <VideoTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import {Tabs, TabPane} from "element-ui";
import DropdownLoading from "../../../public/DropdownLoading/index.vue";
import {webHighSearch} from "../../../../../api/search";
import SearchDataSorting from "../SearchDataSorting/index.vue";
import ImageTextTab from "./ImageTextTab/index.vue";
import VideoTab from "./VideoTab/index.vue";

export default {
  name: "HighSearchTab",
  components: {
    SearchDataSorting,
    Tabs,
    TabPane,
    ImageTextTab,
    VideoTab,
    DropdownLoading
  },
  data() {
    return {
      current: 1,
      loading: true,
      searchData: {
        list: [],
        page: {}
      },
      activeName: '1',
      activeSort: "1",
      orderRule: 1,
      essences: "F",
      source: null
    }
  },
  mounted() {
    (async () => {
      await this.getHighSearchResultHandler()
      if (this.searchData.list.length === 0) {
        this.activeName = '2'
        await this.tabHandleClick()
      }
    })()


  },
  methods: {
    async getHighSearchResultHandler(params = {}) {
      const titleKeywords = this.$route.query.titleKeywords || ""
      const contentKeywords = this.$route.query.contentKeywords || ""
      const authorKeywords = this.$route.query.authorKeywords || ""
      const firstTime = this.$route.query.firstTime || ""
      const lastTime = this.$route.query.lastTime || ""

      const type = Object.prototype.hasOwnProperty.call(params, 'type') ? params.type : 1;
      const essences = Object.prototype.hasOwnProperty.call(params, 'essences') ? params.essences : 'F';
      const orderRule = Object.prototype.hasOwnProperty.call(params, 'orderRule') ? params.orderRule : 1;
      const pageno = Object.prototype.hasOwnProperty.call(params, 'pageNo') ? params.pageNo : 1;
      const PageUp = Object.prototype.hasOwnProperty.call(params, 'PageUp') ? params.PageUp : false;

      const CancelToken = this.$axios.CancelToken;
      this.source = CancelToken.source();

      const res = await this.$axios.$request(webHighSearch({
        type,
        titleKeywords,
        contentKeywords,
        authorKeywords,
        essences,
        userId: this.$store.state.auth.user.id,
        order: 'desc',
        pageno,
        pagesize: 9,
        orderRule,
        firstTime,
        lastTime
      }, this.source.token))

      if (res.code === 1 && PageUp === false) {
        this.$set(this.searchData, "list", res.list)
        this.$set(this.searchData, "page", res.page)
      }

      if (res.code === 1 && PageUp === true) {
        this.$set(this.searchData, "list", this.searchData.list.concat(res.list))
        this.$set(this.searchData, "page", res.page)
      }

      this.loading = false;


    },

    tabHandleClick(tab) {
      this.$analysys.btn_click(this.activeName === '1' ? "图文" : "视频", "高级搜索结果页")

      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      this.activeSort = "1"
      this.orderRule = 1;
      this.essences = "F";
      this.loading = true;
      this.searchData = {
        list: [],
        page: {}
      }
      this.current = 1;

      this.getHighSearchResultHandler({
        type: this.activeName
      })
    },
    hitBottomChangeHandler(isBottom) {
      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      this.loading = true;
      if (isBottom) {
        this.current += 1;

        this.getHighSearchResultHandler({
          PageUp: true,
          pageNo: this.current,
          type: this.activeName,
          orderRule: this.orderRule,
          essences: this.essences,
        })
      }
    },
    changeSortHandler(key) {
      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      this.loading = true;
      this.searchData = {
        list: [],
        page: {}
      }
      this.current = 1;

      switch (key) {
        case '': {
          this.orderRule = "";
          this.essences = "F";
          break
        }
        case '1': {
          this.orderRule = 1;
          this.essences = "F";
          break
        }
        case '2': {
          this.orderRule = 2;
          this.essences = "F";
          break
        }
        case '3': {
          this.orderRule = 1;
          this.essences = "T";
          break
        }
      }

      this.getHighSearchResultHandler({
        orderRule: this.orderRule,
        essences: this.essences,
        type: this.activeName
      })

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
