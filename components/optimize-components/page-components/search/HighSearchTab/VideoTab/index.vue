<template>
  <div class="tab_wrapper">
    <div v-for="(item,index) in searchData.list" :key="index" class="tab_list_wrapper">
      <MeetingSearchItem
        v-if="item.type === 'meeting'"
        :meeting-data='item'
      />

      <CloudClassroomItem
        v-else-if="item.type === 'ocs_course'"
        :cloud-classroom='item'
      />

      <ShortVideoSearchItem
        v-else-if="item.type === 'shortVideo'"
        :id="item.shortVideo.id"
        :title="item.shortVideo.title"
        :cover="item.shortVideo.cover"
        :description="item.shortVideo.description"
        :real-name="item.shortVideo.creator.realName"
        :online-time="item.shortVideo.onlineTime"
      />

      <ElabSearchItem
        v-else-if='item.type === "elab"'
        :id='item.elab.id'
        :case-name='item.elab.caseName'
        :elab-surgical-classification='item.elab.elabSurgicalClassification'
        :hospital='item.elab.hospital'
        :author-list='item.elab.authorList'
        :classification-list='item.elab.classificationList'
        :share-desc="item.elab.shareDesc"
        :image='item.elab.image'
      />

    </div>
  </div>
</template>

<script>
import {
  MeetingSearchItem,
  CloudClassroomItem,
  ShortVideoSearchItem,
  ElabSearchItem
} from "../../../../public/article-types-list/search";

export default {
  name: "VideoTab",
  components: {
    MeetingSearchItem,
    CloudClassroomItem,
    ShortVideoSearchItem,
    ElabSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
