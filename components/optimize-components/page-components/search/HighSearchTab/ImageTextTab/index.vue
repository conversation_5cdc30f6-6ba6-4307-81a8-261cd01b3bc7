<template>
  <div class="tab_wrapper">
    <div v-for="(item,index) in searchData.list" :key="index" class="tab_list_wrapper">
      <InfoItem
        v-if="item.type === 'info'"
        :info-id="item.info.infoId"
        :info-img="item.info.infoImg"
        :essences="item.info.essences"
        :info-title="item.info.infoTitle"
        :publish-date="item.info.publishDate"
        :author-list="item.info.authorList"
        :author-names="item.info.authorNames"
        :search-description="item.info.searchDescription"
      />

      <CaseItem
        v-else-if="item.type === 'mp_article'"
        :case-list="item"
      />

      <ArticleList
        v-else-if='item.type === "community_qa"'
        :key="index"
        :data_list="[item.community_qa]"
      />

    </div>
  </div>
</template>

<script>
import {InfoItem, CaseItem} from "../../../../public/article-types-list/search";
import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
export default {
  name: "ImageTextTab",
  components: {
    InfoItem,
    CaseItem,
    ArticleList
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
