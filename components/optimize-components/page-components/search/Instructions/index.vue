<template>
  <div class="instructions_wrapper">
    <div class="instructions_content">
      <div class="title_bg"></div>
      <div class="content_wrapper">
        <p class="info_title">高级搜索的适用范围：</p>
        <p class="info_p" style="margin-bottom: 16px">高级搜索目前适用于文章、课程、会议及其他类型视频。</p>
        <div class="title_1_bg"></div>
        <div class="title_content">
          <p class="info_p">可在“标题”项输入以下内容：</p>
          <p class="info_p"><b>[文章类]</b> 文章标题 </p>
          <p class="info_p"><b>[课程类]</b> 课程标题、课程子标题 </p>
          <p class="info_p"><b>[会议类]</b> 会议标题、会场标题、日程标题 </p>
          <p class="info_p"><b>[其他类型视频]</b> 短视频标题、测评视频标题、解读视频标题</p>
        </div>
        <div class="title_2_bg"></div>
        <div class="title_content">
          <p class="info_p">可检索“文章类”的正文内容。</p>
        </div>
        <div class="title_3_bg"></div>
        <div class="title_content">
          <p class="info_p">可检索文章作者、课程讲师、会议讲师。</p>
        </div>

        <div class="title_4_bg"></div>
        <div class="title_content">
          <p class="info_p">可直接点击一周、一月、半年、不限按钮，也可自主填写发布时间区间。</p>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "InstructionsComponent"
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
