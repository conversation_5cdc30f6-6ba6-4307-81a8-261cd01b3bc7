<template>
  <SearchListCard
    bg-color="linear-gradient(180deg, #FF8567 -137.44%, #FFF 28.64%)"
    font-color="#F34000"
    title="热门搜索"
    icon="hot_search"
    :bg-image-url="require('~/assets/images/search/hot_search.png')"
  >
    <ul class="list_wrapper">
      <li v-for="(item,index) in list" :key="item.id" class="list_item cursor" @click='jumpPageFun(item.type,item.id)'>
        <span class="num">{{ index + 1 }}</span>
        <span class="list_title text-limit-1">{{ item.title }}</span>
      </li>
    </ul>
  </SearchListCard>
</template>

<script>
import SearchListCard from "../SearchListCard/index.vue";
import env from "../../../../../env-module";

export default {
  name: "HotSearch",
  components: {
    SearchListCard
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    jumpPageFun(type, infoId) {
      switch (type) {
        case 'info': {
          // 咨询
          const {href} = this.$router.resolve({name: 'index-info-detail', query: {id: infoId}})
          window.open(href, '_blank')
          break
        }
        case 'ocs_course': {
          // 云课堂
          window.open(env[process.env.NODE_ENV].ENV_NEW_URL + '/ocs/index.html#/pages/coursePlay/coursePlay?courseId=' + infoId, '_blank')
          break
        }
        case 'mp_article': {
          // 病例
          const {href} = this.$router.resolve({
            name: 'index-case-detail-ugc',
            query: {id: infoId}
          })
          window.open(href, '_blank')
          break
        }
        case 'meeting': {
          // 病例
          const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: infoId}})
          window.open(href, '_blank')
          break
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
