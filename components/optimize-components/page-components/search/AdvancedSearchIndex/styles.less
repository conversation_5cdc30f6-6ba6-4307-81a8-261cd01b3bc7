
.search_index_wrapper {
  user-select: none;
  display: grid;
  gap: 20px 0;
}

.index_wrapper {
  display: flex;
  align-items: center;

  .index_title {
    color: #676C74;
    font-size: 16px;
    font-weight: 500;
    margin-right: 16px;
    flex-shrink: 0;
  }

  /deep/ .el-input__inner {
    height: 44px;
    line-height: 44px;
    border-radius: 4px;
    background: #F4F6F8;
    font-size: 16px;
    border: none;

    &:hover {
      outline: 0.5px solid #C7C7C7;
      background: #F4F6F8;
    }
  }
  .index_btn {
    border-radius: 6px;
    margin-left: 16px;
    flex-shrink: 0;
    width: 42px;
    height: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed #0581CE;
    background-color: #F2F8FC;

    .icons {
      width: 18px;
      height: 18px;
      color: var(--theme-color);
    }

    &:hover {
      cursor: pointer;
      border: 1px dashed #999EA4;
      background-color: #F4F6F8;

      .icons {
        color: #999EA4;
      }
    }


  }
}

.index_also_wrapper {
  padding-left: 60px;
  display: grid;
  gap: 20px 0;
  position: relative;

  &::after {
    content: "";
    height: 100%;
    border-left: 2px dashed #708AA2;
    position: absolute;
    left: 14px;
    top: -22px;
  }

  .index_also_children {
    user-select: none;
    position: relative;

    &::after {
      width: 40px;
      content: "并且";
      color: #708AA2;
      font-size: 12px;
      text-align: center;
      position: absolute;
      left: -46px;
      top: 3px;
    }

    &::before {
      content: "";
      width: 40px;
      border-bottom: 2px dashed #708AA2;
      position: absolute;
      left: -46px;
      top: 52%;
      transform: translateY(-50%);
      //padding-bottom: 4px;
    }
  }
}
