<template>
  <div class="search_index_wrapper">
    <div class="index_wrapper">
      <div class="index_title">{{ title }}</div>
      <el-input v-model.trim="indexObj.indexValue1" :placeholder="`${title}中包含的关键字`"></el-input>
      <div v-if="!isAddBtnHidden" class="index_btn" @click="addIndexHandler">
        <svg-icon icon-class="search_index_add_" class-name="icons"/>
      </div>
      <div v-else class="index_btn" style="opacity: 0;cursor: default;"></div>
    </div>
    <div v-show="Object.keys(indexObj).length>1" class="index_also_wrapper">
      <div
        v-for="(item,index) in Object.keys(indexObj).slice(1)"
        :key="index"
        class="index_wrapper index_also_children">
        <div class="index_title">{{ title }}</div>
        <el-input
          v-model.trim="indexObj[`${item}`]"
          :placeholder="`${title}中包含的关键字`"/>
        <div class="index_btn" @click="delIndexHandler(item)">
          <svg-icon icon-class="search_index_del" class-name="icons"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdvancedSearchIndex",
  props: {
    title: {
      type: String,
      default: "正文"
    },
    keywords: {
      type: String,
      default: ""
    },
    isAddBtnHidden: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      indexObj: {
        indexValue1: ""
      }
    }
  },
  watch: {
    indexObj: {
      handler(newValue) {
        if (newValue) {
          let keywordsName = ""
          for (const key in newValue) {
            if (newValue[key]) {
              keywordsName += newValue[key] + ","
            }
          }
          keywordsName = keywordsName.substring(0, keywordsName.length - 1)
          this.$emit("update:keywords", keywordsName)
        }
      },
      deep: true
    }
  },
  methods: {
    addIndexHandler() {
      this.$set(this.indexObj, new Date().getTime(), "")
    },
    delIndexHandler(item) {
      this.$delete(this.indexObj, item)
    },
    clearIndexHandler() {
      this.indexObj = {
        indexValue1: ""
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
