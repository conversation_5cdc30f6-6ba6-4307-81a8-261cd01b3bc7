
.search_prompt_wrapper {
  padding: 7px;


  .search_history_wrapper {
    margin-bottom: 24px;

    .search_history_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .name {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .clear {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999EA4;

        .icons {
          width: 15px;
          height: 15px;
          margin-right: 4px;
        }
      }
    }

    .search_history_content {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .search_history_item {
        flex-shrink: 0;
        height: 24px;
        border-radius: 4px;
        background: #F4F6F8;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 4px;
        color: #0581CE;
        font-size: 14px;
      }
    }
  }

  .hot_search_wrapper {
    .hot_search_title {
      font-size: 16px;
      margin-bottom: 16px;
      color: #333333;
      font-weight: 600;
    }

    .hot_search_content {
      display: grid;
      grid-template-columns: calc(50% - 12px) calc(50% - 12px);
      gap: 16px 20px;

      .hot_search_item {
        padding-right: 20px;
        color: #333333;
        font-size: 14px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          color: var(--theme-color);
        }

        .num {
          font-size: 18px;
          color: #C7C7C7;
          font-weight: 800;
          margin-right: 6px;
          flex-shrink: 0;
        }

        &:nth-child(1) {
          .num {
            color: #E4320D;
          }
        }

        &:nth-child(2) {
          .num {
            color: #F27026;
          }
        }

        &:nth-child(3) {
          .num {
            color: #E79E0C;
          }
        }
      }


    }
  }

  .search_result_list_wrapper {
    display: grid;
    gap: 20px;

    .search_result_item {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      line-height: 22px;

      &:hover {
        color: var(--theme-color);
        cursor: pointer;
      }
    }
  }

  .search_senior_wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    a {
      flex: 1;
      &:first-of-type {
        margin-right: 10px;
      }
    }

    .search_senior_btn, .search_senior_btn_ai {
      width: 100%;
      display: block;
      cursor: pointer;
      margin-top: 22px;

    }
  }
}
