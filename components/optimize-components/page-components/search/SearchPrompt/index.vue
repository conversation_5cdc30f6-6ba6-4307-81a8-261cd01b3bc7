<template>
  <div class="search_prompt_wrapper">
    <div v-show="!searchValue">
      <div v-show="historyList.length>0" class="search_history_wrapper">
        <div class="search_history_title">
          <div class="name">搜索历史</div>
          <div class="clear cursor" @click="clearHistory">
            <svg-icon icon-class="search_delete" class-name="icons"/>
            <span>清空</span>
          </div>
        </div>
        <div class="search_history_content">
          <div
            v-for="(item,index) in historyList"
            :key="index"
            class="search_history_item cursor"
            :style="isAdvancedSearchShow ? {maxWidth: '29%'} : {maxWidth: '132px'}"
            @click="historyJumpHandler(item.name)">
            <span class="text-limit-1">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <div class="hot_search_wrapper">
        <div class="hot_search_title">热门搜索</div>
        <ul class="hot_search_content">
          <li
            v-for="(item,index) in hotSearchList"
            :key="item.id"
            class="hot_search_item"
            @click='jumpPageFun(item.type,item.id,item.title)'>
            <span class="num">{{ index + 1 }}</span>
            <span
              class="text-limit-1">{{ item.title }}</span>
          </li>
        </ul>
      </div>
    </div>

    <div v-show="searchValue">
      <ul class="search_result_list_wrapper">
        <li v-for="item in promptList" :key="item.id" class="search_result_item" @click="historyJumpHandler(item.name)"
            v-html="highLight(item.name)"></li>
      </ul>
    </div>

    <div v-if="isAdvancedSearchShow && !searchValue" class="search_senior_wrapper">
      <a target="_blank" href="/askAI/char/home" @click="analysysAiHandler">
        <img src="~assets/images/ai/search_ai_btn.png" alt="" class="search_senior_btn_ai">
      </a>
      <a target="_blank" href="/advanced-search" @click="analysysHandler">
        <img src="~assets/images/search/search_senior_nav3.png" alt="" class="search_senior_btn">
      </a>
    </div>
  </div>
</template>

<script>
import {getLeaderBoard, sugrec} from "../../../../../api/search";
import env from "../../../../../env-module";

export default {
  name: "SearchPrompt",
  props: {
    searchValue: {
      type: String,
      default: ""
    },
    isAdvancedSearchShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      historyList: [],
      hotSearchList: [],
      promptList: [],
      source: null
    }
  },
  watch: {
    searchValue(newValue) {
      if (newValue !== "") {
        this.promptList = []
        this.getPromptListHandler(newValue)
      } else {
        this.$emit("closePopUp", true)
      }
    }
  },
  mounted() {
    this.getHotSearchHandler()
  },
  methods: {
    // 埋点
    analysysHandler() {
      this.$analysys.btn_click('高级搜索', "首页")
    },
    analysysAiHandler() {
      this.$analysys.btn_click('AI搜索', "首页")
    },
    // 获取搜索提示词
    getPromptListHandler(value) {
      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      const CancelToken = this.$axios.CancelToken;
      this.source = CancelToken.source();

      if (value !== "") {
        this.$axios.$request(sugrec({
          keywords: value,
          limit: 10
        }, this.source.token)).then(res => {
          if (res.code === 1) {

            if (res.list.length < 1) {
              this.$emit("closePopUp", false)
            } else {
              this.$emit("closePopUp", true)
            }

            this.promptList = res.list

          }
        })
      }

      return this.promptList.length > 0;


    },
    // 获取搜索历史
    getHistoryHandler() {
      const searchForTips = window.localStorage.getItem('brainmed_history_search')

      if (searchForTips) {
        const data = JSON.parse(searchForTips)

        this.historyList = data
      }
    },
    // 添加历史搜索
    addHistoryHandler(value) {
      let historyArr = JSON.parse(window?.localStorage.getItem("brainmed_history_search") || "[]") || []
      historyArr.unshift({name: value})

      const uniqueArr = historyArr.filter((item, index, self) =>
        index === self.findIndex(obj => JSON.stringify(obj) === JSON.stringify(item))
      );

      historyArr = uniqueArr.splice(0, 10)
      window.localStorage.setItem("brainmed_history_search", JSON.stringify(historyArr))
    },
    // 获取热门搜索
    async getHotSearchHandler() {
      const res = await this.$axios.$request(getLeaderBoard({
        limit: 10
      }))

      if (res.code === 1) {
        this.hotSearchList = res.list;
      }
    },
    jumpPageFun(type, infoId, name) {
      this.$analysys.resource_click({resource_title: name, resource_type: '热门搜索'})
      switch (type) {
        case 'info': {
          // 咨询
          const {href} = this.$router.resolve({name: 'index-info-detail', query: {id: infoId}})
          window.open(href, '_blank')
          break
        }
        case 'ocs_course': {
          // 云课堂
          window.open(env[process.env.NODE_ENV].ENV_NEW_URL + '/ocs/index.html#/pages/coursePlay/coursePlay?courseId=' + infoId, '_blank')
          break
        }
        case 'mp_article': {
          // 病例
          const {href} = this.$router.resolve({
            name: 'index-case-detail-ugc',
            query: {id: infoId}
          })
          window.open(href, '_blank')
          break
        }
        case 'meeting': {
          // 病例
          const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: infoId}})
          window.open(href, '_blank')
          break
        }
      }
    },
    historyJumpHandler(value) {
      this.addHistoryHandler(value)

      if (this.$route.path === '/search') {
        window.location.href = `/search?keywords=${value}`
      } else {
        window.open(`/search?keywords=${value}`)
      }

    },
    clearHistory() {
      this.historyList = [];
      window.localStorage.setItem("brainmed_history_search", JSON.stringify([]))
    },
    highLight(title) {
      // 如果标题中包含，关键字就替换一下
      if (title.includes(this.searchValue)) {
        title = title.replace(
          this.searchValue,
          // 这里是替换成html格式的数据，最好再加一个样式权重，保险一点
          '<span style="color:red!important;">' + this.searchValue + '</span>'
        )
        return title
      }
      // 不包含的话还用这个
      else {
        return title
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
