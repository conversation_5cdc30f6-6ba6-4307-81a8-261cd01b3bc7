<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index" class="list_item">
        <CaseItem
          v-if="item.type === 'mp_article'"
          :key="index"
          :case-list="item"
        />
        <InfoItem
          v-else-if="item.type === 'info'"
          :key="index"
          :info-id="item.info.infoId"
          :info-img="item.info.infoImg"
          :essences="item.info.essences"
          :info-title="item.info.infoTitle"
          :publish-date="item.info.publishDate"
          :author-list="item.info.authorList"
          :author-names="item.info.authorNames"
          :search-description="item.info.searchDescription"
          :bms-auth="item.info.bmsAuth"
        />
      </div>
    </template>
  </div>
</template>

<script>
import CaseItem from "../../../../public/article-types-list/search/CaseItem/index.vue";
import InfoItem from "../../../../public/article-types-list/search/InfoItem/index.vue";

export default {
  name: "CaseTab",
  components: {
    InfoItem,
    CaseItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
