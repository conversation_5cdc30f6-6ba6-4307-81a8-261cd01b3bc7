<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <CaseCompetitionSearchItem
          v-if='item.type === "mcs_competition"'
          :key="index"
          :case-competition='item'
        />
      </div>
    </template>

  </div>
</template>

<script>
import {CaseCompetitionSearchItem} from "../../../../public/article-types-list/search";

export default {
  name: "CaseCompetitionTab",
  components: {
    CaseCompetitionSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
