<template>
  <div class="all_tab_wrapper">
    <div class="list_wrapper">
      <template v-for="(item,index) in searchData.list">
        <SearchProductLineItem
          v-if="item.type === 'bms_brand_product_line'"
          :key="index"
          :product-line-details='item.bms_brand_product_line'
          @followProductLineFun='followProductLineFun'/>

        <ProductSearchItem
          v-else-if='item.type === "bms_product"'
          :id="item.bms_product.id"
          :categoryId="item.bms_product.brandProductLine.categoryId"
          :isLabelShow="false"
          :title="item.bms_product.name"
          :img="item.bms_product.image"
          :categoryName="item.bms_product.category.name"
          :brandImg="item.bms_product.brand.shareImage"
          :brandName="item.bms_product.brand.name"
        />
        <UserItem
          v-else-if='item.type === "user"'
          :key="index"
          :user-id="item.user.id"
          :avatar-address="item.user.avatarAddress"
          :real-name="item.user.realName"
          :company="item.user.company"
          :department="item.user.department"
          :fans="item.user.fans"
          :is-follow="item.user.isFollow"
          @followFun='followFun'
        />

        <InfoItem
          v-else-if="item.type === 'info'"
          :key="index"
          :info-id="item.info.infoId"
          :info-img="item.info.infoImg"
          :essences="item.info.essences"
          :info-title="item.info.infoTitle"
          :publish-date="item.info.publishDate"
          :author-list="item.info.authorList"
          :author-names="item.info.authorNames"
          :search-description="item.info.searchDescription"
          :bms-auth="item.info.bmsAuth"
        />

        <CaseItem
          v-else-if="item.type === 'mp_article'"
          :key="index"
          :case-list="item"
        />

        <CloudClassroomItem
          v-else-if="item.type === 'ocs_course'"
          :key="index"
          :cloud-classroom='item'
        />

        <MeetingSearchItem
          v-else-if="item.type === 'meeting'"
          :key="index"
          :meeting-data='item'
        />

        <ElabSearchItem
          v-else-if='item.type === "elab"'
          :id='item.elab.id'
          :key="index"
          :case-name='item.elab.caseName'
          :elab-surgical-classification='item.elab.elabSurgicalClassification'
          :hospital='item.elab.hospital'
          :author-list='item.elab.authorList'
          :classification-list='item.elab.classificationList'
          :share-desc="item.elab.shareDesc"
          :image='item.elab.image'
        />

        <ArticleList
          v-else-if='item.type === "community_qa"'
          :key="index"
          :data_list="[item.community_qa]"
        />

        <CompileSearchItem
          v-else-if='item.type === "info" && item.info.compileArticle === "T"'
          :key="index"
          :consulting-data='item'
        />

        <CaseCompetitionSearchItem
          v-if='item.type === "mcs_competition"'
          :key="index"
          :case-competition='item'
        />

        <ShortVideoSearchItem
          v-else-if="item.type === 'shortVideo'"
          :id="item.shortVideo.id"
          :key="index"
          :title="item.shortVideo.title"
          :cover="item.shortVideo.cover"
          :description="item.shortVideo.description"
          :real-name="item.shortVideo.creator.realName"
          :online-time="item.shortVideo.onlineTime"
          :bms-auth="item.shortVideo.bmsAuth"
        />

        <DepartSearchItem
          v-else-if="item.type === 'department'"
          :key="index"
          :department-id="item.department.id"
          :hospital-group-name="item.department.hospitalGroupName"
          :department="item.department.name"
          :icon-url="item.department.iconUrl"
          :member-total="item.department.memberTotal"
          :is-follow="item.department.isSubscribe"
        />

        <AdSearchItem
          v-else-if="item.type === 'advertisement'"
          :ad="item.ad"
        />

        <BooksWrapper
          v-else-if="item.type === 'merchandise'"
          :key="index"
          :books-list="Array.isArray(item.merchandise) ? item.merchandise : [] "
          @changeTab="(tab) => $emit('changeTab',tab)"
        />

        <InterpretingVideoSearchItem
          v-else-if="item.type === 'interpreting_video'"
          :key="index"
          :info-id="item.interpreting_video.id"
          :info-img="item.interpreting_video.cover"
          :info-title="item.interpreting_video.videoName"
          :publish-date="item.interpreting_video.publishTime"
          :author-name="item.interpreting_video.author.authorName"
          :search-description="item.interpreting_video.description"
        />

      </template>
    </div>
  </div>
</template>

<script>
import SearchProductLineItem from "../../../../public/article-types-list/bms/SearchProductLineItem/index.vue";
import {
  UserItem,
  InfoItem,
  CaseItem,
  CloudClassroomItem,
  MeetingSearchItem,
  ElabSearchItem,
  CompileSearchItem,
  CaseCompetitionSearchItem,
  ShortVideoSearchItem,
  DepartSearchItem,
  ProductSearchItem,
  BooksWrapper,
  AdSearchItem,
  InterpretingVideoSearchItem
} from "../../../../public/article-types-list/search";
import {subscribeBrand} from "../../../../../../api/bms";
import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
import brandAdJump from "../../../../../../assets/helpers/brandAdJump.js";
import ZipImg from "../../../../../../opt-components/component/ZipImg/index.vue";

export default {
  name: "AllTab",
  components: {
    ZipImg,
    UserItem,
    SearchProductLineItem,
    InfoItem,
    CaseItem,
    CloudClassroomItem,
    MeetingSearchItem,
    ElabSearchItem,
    ArticleList,
    CompileSearchItem,
    CaseCompetitionSearchItem,
    ShortVideoSearchItem,
    DepartSearchItem,
    ProductSearchItem,
    BooksWrapper,
    AdSearchItem,
    InterpretingVideoSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
  data() {
    return {}
  },
  mounted() {

  },
  methods: {
    // 关注产线
    followProductLineFun({brandId, productLineId}, backFn) {
      this.$axios.$request(subscribeBrand({
        brandId,
        productLineId
      })).then(res => {
        if (res.code === 1) {
          backFn(res.result.followStatus)
        }
      })
    },
    // 关注用户
    followFun(id) {
      this.$store.dispatch('follow', id)
    },
  }

}
</script>

<style scoped lang="less">
@import "./styles";
</style>
