<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <CompileSearchItem
          v-if='item.type === "info"'
          :key="index"
          :consulting-data='item'
        />
      </div>
    </template>
  </div>
</template>

<script>
import {CompileSearchItem} from "../../../../public/article-types-list/search";

export default {
  name: "CompileTab",
  components: {
    CompileSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
