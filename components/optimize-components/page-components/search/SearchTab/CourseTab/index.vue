<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index" class="list_item">
        <CloudClassroomItem
          v-if="item.type === 'ocs_course'"
          :key="index"
          :cloud-classroom='item'
        />
      </div>
    </template>
  </div>
</template>

<script>
import CloudClassroomItem from "../../../../public/article-types-list/search/CloudClassroomItem/index.vue";

export default {
  name: "CourseTab",
  components: {CloudClassroomItem},
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
