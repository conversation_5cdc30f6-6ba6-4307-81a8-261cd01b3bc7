<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <ShortVideoSearchItem
          v-if="item.type === 'shortVideo'"
          :id="item.shortVideo.id"
          :key="index"
          :title="item.shortVideo.title"
          :cover="item.shortVideo.cover"
          :description="item.shortVideo.description"
          :real-name="item.shortVideo.creator.realName"
          :online-time="item.shortVideo.onlineTime"
          :bms-auth="item.shortVideo.bmsAuth"
        />
      </div>
    </template>

  </div>
</template>

<script>
import {ShortVideoSearchItem} from "../../../../public/article-types-list/search";

export default {
  name: "ShortVideoTab",
  components: {
    ShortVideoSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
