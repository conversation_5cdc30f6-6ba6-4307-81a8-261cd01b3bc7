<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <UserItem
          v-if="item.type === 'user'"
          :user-id="item.user.id"
          :avatar-address="item.user.avatarAddress"
          :real-name="item.user.realName"
          :company="item.user.company"
          :department="item.user.department"
          :fans="item.user.fans"
          :is-follow="item.user.isFollow"
          @followFun='followFun'
        />
      </div>
    </template>

  </div>
</template>

<script>
import {UserItem} from "../../../../public/article-types-list/search";

export default {
  name: "UserTab",
  components: {UserItem},
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
  methods: {
    // 关注用户
    followFun(id) {
      this.$store.dispatch('follow', id)
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
