<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <MeetingSearchItem
          v-if="item.type === 'meeting'"
          :key="index"
          :meeting-data='item'
        />
      </div>
    </template>

  </div>
</template>

<script>
import {MeetingSearchItem} from "../../../../public/article-types-list/search";

export default {
  name: "MeetingTab",
  components: {
    MeetingSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
