<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index" class="list_item">
        <InfoItem
          v-if="item.type === 'info'"
          :info-id="item.info.infoId"
          :info-img="item.info.infoImg"
          :essences="item.info.essences"
          :info-title="item.info.infoTitle"
          :publish-date="item.info.publishDate"
          :author-list="item.info.authorList"
          :author-names="item.info.authorNames"
          :search-description="item.info.searchDescription"
          :bms-auth="item.info.bmsAuth"
        />
      </div>
    </template>
  </div>
</template>

<script>
import InfoItem from "../../../../public/article-types-list/search/InfoItem/index.vue";

export default {
  name: "InfoTab",
  components: {InfoItem},
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
