<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="item.id" class="list_item">
        <BooksSearchItem
          v-if="item.type === 'merchandise'"
          :book-id="item.merchandise.id"
          :title="item.merchandise.name"
          :cover="item.merchandise.cover"
          :price="item.merchandise.price"
          :press-name="item.merchandise.osmPress.name"
          :introduction="item.merchandise.introduction"
          :author-list="item.merchandise.authorList"
          :discount-price="item.merchandise.discountPrice"
        />
      </div>
    </template>
  </div>
</template>

<script>
import BooksSearchItem from "../../../../public/article-types-list/search/BooksWrapper/BooksSearchItem";

export default {
  name: "BookTab",
  components: {BooksSearchItem},
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
