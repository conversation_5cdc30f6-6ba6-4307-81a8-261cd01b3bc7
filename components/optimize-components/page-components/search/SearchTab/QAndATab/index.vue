<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <ArticleList
          v-if='item.type === "community_qa"'
          :key="index"
          :data_list="[item.community_qa]"
        />
      </div>
    </template>

  </div>
</template>

<script>
import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
export default {
  name: "QAndATab",
  components:{
    ArticleList
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
