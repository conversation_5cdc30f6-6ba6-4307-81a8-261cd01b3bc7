<template>
  <div class="list_wrapper">
    <template v-if="searchData.list.length>0">
      <div v-for="(item,index) in searchData.list" :key="index">
        <ElabSearchItem
          v-if='item.type === "elab"'
          :id='item.elab.id'
          :key="index"
          :case-name='item.elab.caseName'
          :elab-surgical-classification='item.elab.elabSurgicalClassification'
          :hospital='item.elab.hospital'
          :author-list='item.elab.authorList'
          :classification-list='item.elab.classificationList'
          :share-desc="item.elab.shareDesc"
          :image='item.elab.image'
        />
      </div>
    </template>

  </div>
</template>

<script>
import {ElabSearchItem} from "../../../../public/article-types-list/search";

export default {
  name: "ElabTab",
  components:{
    ElabSearchItem
  },
  props: {
    searchData: {
      type: Object,
      default: () => {
        return {
          list: [],
          page: {}
        }
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
