<template>
  <div class="search_tab_wrapper">
    <Tabs v-model="activeName" @tab-click="tabHandleClick">
      <TabPane label="全部" name="99">
        <SearchDataSorting :is-bms-auth="true" :active-sort.sync="activeSort" @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <AllTab
            :search-data="searchData"
            @changeTab="(tab) => selectTabHandler(tab)"
          />
        </DropdownLoading>
      </TabPane>

      <TabPane label="用户" name="5" :lazy="true">
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <UserTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="资讯" name="1" :lazy="true">
        <SearchDataSorting :is-bms-auth="true" :active-sort.sync="activeSort" @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <InfoTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="云课堂" name="2" :lazy="true">
        <SearchDataSorting :is-essences-enable="false" :active-sort.sync="activeSort"
                           @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <CourseTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="病例" name="12" :lazy="true">
        <SearchDataSorting :is-bms-auth="true" :active-sort.sync="activeSort" @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <CaseTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="会议" name="3" :lazy="true">
        <SearchDataSorting
          :is-essences-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <MeetingTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="指南" name="0001" :lazy="true">
        <SearchDataSorting :is-essences-enable="false"
                           :active-sort.sync="activeSort"
                           :is-hot-enable="false"
                           :is-bms-auth="true"
                           @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <AllTab
            :search-data="searchData"
            @changeTab="(tab) => selectTabHandler(tab)"
          />
        </DropdownLoading>
      </TabPane>

      <TabPane label="全景手术" name="13" :lazy="true">
        <SearchDataSorting
          :is-essences-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <ElabTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane label="编译" name="7" :lazy="true">
        <SearchDataSorting
          :is-bms-auth="true"
          :is-essences-enable="true"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <CompileTab :search-data="searchData"/>
        </DropdownLoading>
      </TabPane>

      <TabPane disabled name="9898">
        <div slot="label">
          <el-popover
            popper-class="search_page_tab_popover"
            :visible-arrow="false"
            v-model="moreVisible"
            placement="bottom"
            width="116"
            trigger="click">
            <div>
              <div class="more_btn_wrapper" :class="activeName === '14' ? 'more_btn__active_wrapper' : ''"
                   @click="selectTabHandler('14')">
                <svg-icon icon-class="search_qa" class-name="icons"/>
                <span>问答</span>
              </div>
              <div class="more_btn_wrapper" :class="activeName === '10' ? 'more_btn__active_wrapper' : ''"
                   @click="selectTabHandler('10')">
                <svg-icon icon-class="search_dsp" class-name="icons"/>
                <span>短视频</span>
              </div>
              <div class="more_btn_wrapper" :class="activeName === '15' ? 'more_btn__active_wrapper' : ''"
                   @click="selectTabHandler('15')">
                <svg-icon icon-class="search_book" class-name="icons"/>
                <span>书籍</span>
              </div>
              <div class="more_btn_wrapper" :class="activeName === '11' ? 'more_btn__active_wrapper' : ''"
                   @click="selectTabHandler('11')">
                <svg-icon icon-class="search_blds" class-name="icons"/>
                <span>病例大赛</span>
              </div>
            </div>
            <div slot="reference" class="more_tab_btn">
              <span ref="more_tab_btn">更多</span>
              <svg-icon ref="more_tab_icon" icon-class="search_more" class-name="icons"/>
            </div>
          </el-popover>
        </div>
      </TabPane>

      <div v-if="activeName === '14'">
        <SearchDataSorting
          :key="activeName"
          :is-essences-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <QAndATab :search-data="searchData"/>
        </DropdownLoading>
      </div>


      <div v-if="activeName === '10'">
        <SearchDataSorting
          :is-bms-auth="true"
          :key="activeName"
          :is-essences-enable="false"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <ShortVideoTab :search-data="searchData"/>
        </DropdownLoading>
      </div>

      <div v-if="activeName === '11'">
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <CaseCompetitionTab :search-data="searchData"/>
        </DropdownLoading>
      </div>

      <div v-if="activeName === '15'">
        <SearchDataSorting
          :key="activeName"
          :is-essences-enable="false"
          :is-default-enable="false"
          hot-name="热销"
          :active-sort.sync="activeSort"
          @changeSortHandler="changeSortHandler"/>
        <DropdownLoading
          :loading="loading"
          :empty="searchData.list.length===0"
          :no-more="current >= searchData.page.totalPage"
          @hit-bottom="hitBottomChangeHandler">
          <BookTab :search-data="searchData"/>
        </DropdownLoading>
      </div>

    </Tabs>
  </div>
</template>

<script>
import {Tabs, TabPane} from "element-ui";
import {getGuideConsensus, search} from "../../../../../api/search";
import DropdownLoading from "../../../public/DropdownLoading/index.vue";
import SearchDataSorting from "../SearchDataSorting/index.vue";
import {
  AllTab,
  UserTab,
  InfoTab,
  CourseTab,
  CaseTab,
  MeetingTab,
  ElabTab,
  QAndATab,
  CompileTab,
  ShortVideoTab,
  CaseCompetitionTab,
  BookTab
} from "./index";

export default {
  name: "SearchTab",
  components: {
    SearchDataSorting,
    DropdownLoading,
    Tabs,
    TabPane,
    AllTab,
    UserTab,
    InfoTab,
    CourseTab,
    CaseTab,
    MeetingTab,
    ElabTab,
    QAndATab,
    CompileTab,
    ShortVideoTab,
    CaseCompetitionTab,
    BookTab
  },
  props: ["tabList"],
  data() {
    return {
      moreVisible: false,
      orderRule: "",
      essences: 'F',
      activeSort: "",
      bmsAuth: null,
      current: 1,
      loading: true,
      activeName: '99',
      searchData: {
        list: [],
        page: {}
      },
      source: null
    }
  },
  mounted() {
    this.getSearchResultHandler()
  },
  methods: {
    async getSearchResultHandler(params = {}) {
      const PageUp = Object.prototype.hasOwnProperty.call(params, 'PageUp') ? params.PageUp : false;
      const pageNo = Object.prototype.hasOwnProperty.call(params, 'pageNo') ? params.pageNo : 1;
      const type = Object.prototype.hasOwnProperty.call(params, 'type') ? params.type : 99;
      const essences = Object.prototype.hasOwnProperty.call(params, 'essences') ? params.essences : 'F';
      const orderRule = Object.prototype.hasOwnProperty.call(params, 'orderRule') ? params.orderRule : null;
      const bmsAuth = Object.prototype.hasOwnProperty.call(params, 'bmsAuth') ? params.bmsAuth : null;

      const CancelToken = this.$axios.CancelToken;
      this.source = CancelToken.source();

      const keywords = this.$route.query.keywords
      if (type === '0001') {
        // 指南
        const res = await this.$axios.$request(getGuideConsensus({
          keywords,
          pageno: pageNo,
          pagesize: 9,
          sort: orderRule ? orderRule : 0,
          bmsAuth
        }, this.source.token))

        if (res.code === 1 && PageUp === false) {
          this.$set(this.searchData, "list", res.list)
          this.$set(this.searchData, "page", res.page)
        }

        if (res.code === 1 && PageUp === true) {
          this.$set(this.searchData, "list", this.searchData.list.concat(res.list))
          this.$set(this.searchData, "page", res.page)
        }
      } else {
        // 全局搜索
        const res = await this.$axios.$request(search({
          platform: 'A',
          keywords,
          essences,
          userId: this.$store.state.auth.user.id,
          order: 'desc',
          pageno: pageNo,
          pagesize: 9,
          type,
          orderRule,
          bmsAuth
        }, this.source.token))

        if (res.code === 1 && PageUp === false) {
          this.$set(this.searchData, "list", res.list)
          this.$set(this.searchData, "page", res.page)
        }

        if (res.code === 1 && PageUp === true) {
          this.$set(this.searchData, "list", this.searchData.list.concat(res.list))
          this.$set(this.searchData, "page", res.page)
        }
      }

      this.loading = false;
    },

    // 翻页
    async hitBottomChangeHandler(isBottom) {
      this.loading = true;
      if (isBottom) {
        this.current += 1;

        await this.getSearchResultHandler({
          PageUp: true,
          pageNo: this.current,
          type: this.activeName,
          orderRule: this.orderRule,
          essences: this.essences,
          bmsAuth: this.bmsAuth,
        })
      }

    },

    // 排序切换
    changeSortHandler(key) {
      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      this.loading = true;
      this.searchData = {
        list: [],
        page: {}
      }
      this.current = 1;

      switch (key) {
        case '': {
          this.bmsAuth = null
          this.orderRule = "";
          this.essences = "F";
          break
        }
        case '1': {
          this.orderRule = 1;
          this.essences = "F";
          this.bmsAuth = null
          break
        }
        case '2': {
          this.orderRule = 2;
          this.essences = "F";
          this.bmsAuth = null
          break
        }
        case '3': {
          this.orderRule = "";
          this.essences = "T";
          this.bmsAuth = null
          break
        }
        case '4': {
          this.bmsAuth = 1
          this.orderRule = "";
          this.essences = "F";
        }
      }

      this.getSearchResultHandler({
        orderRule: this.orderRule,
        bmsAuth: this.bmsAuth,
        essences: this.essences,
        type: this.activeName
      })
    },

    tabHandleClick() {
      // eslint-disable-next-line no-undef
      setTimeout(() => {
        const barDom = document.querySelector('.el-tabs__active-bar')
        barDom.style.opacity = 1
      }, 200)

      this.$refs.more_tab_icon.$el.style.color = "#333"
      this.$refs.more_tab_icon.$el.style.transform = "unset"
      this.$refs.more_tab_btn.style.color = "#333"
      this.$refs.more_tab_btn.innerText = "更多"

      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }

      this.activeSort = ""
      this.orderRule = "";
      this.essences = "F";
      this.loading = true;
      this.searchData = {
        list: [],
        page: {}
      }
      this.current = 1;

      this.getSearchResultHandler({
        type: this.activeName
      })
    },
    selectTabHandler(key) {
      this.moreVisible = false
      const barDom = document.querySelector('.el-tabs__active-bar')
      barDom.style.opacity = 0

      switch (key) {
        case '10': {
          this.$refs.more_tab_btn.innerText = "短视频"
          this.$analysys.btn_click("更多-短视频", "搜索结果页")
          break;
        }
        case '11': {
          this.$refs.more_tab_btn.innerText = "病例大赛"
          this.$analysys.btn_click("更多-病例大赛", "搜索结果页")
          break;
        }
        case '14': {
          this.$refs.more_tab_btn.innerText = "问答"
          this.$analysys.btn_click("更多-问答", "搜索结果页")
          break;
        }
        case '15': {
          this.$refs.more_tab_btn.innerText = "书籍"
          this.$analysys.btn_click("更多-书籍", "搜索结果页")
          break;
        }
      }
      this.$refs.more_tab_icon.$el.style.transform = "rotateZ(-180deg)"
      this.$refs.more_tab_icon.$el.style.color = "#0581CE"
      this.$refs.more_tab_btn.style.color = "#0581CE"

      this.activeName = key;
      if (this.source) {
        this.source.cancel('Operation canceled by the user');
      }
      this.activeSort = ""
      this.orderRule = "";
      this.essences = "F";
      this.loading = true;
      this.searchData = {
        list: [],
        page: {}
      }
      this.current = 1;

      this.getSearchResultHandler({
        type: this.activeName
      })
    }
  }
}
</script>

<style lang="less">
.search_page_tab_popover {
  min-width: 60px;
  padding: 8px 0;
  border-radius: 8px;

  .more_btn_wrapper {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    color: #333;
    font-size: 14px;

    .icons {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    &:hover {
      cursor: pointer;
      background: #EFFAFF;
      color: #0581CE;

      .icons {
        color: #0581CE;
      }
    }
  }

  .more_btn__active_wrapper {
    cursor: pointer;
    background: #EFFAFF;
    color: #0581CE;

    .icons {
      color: #0581CE;
    }
  }
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
