
.search_tab_wrapper {
  border-radius: 8px;
  background: #FFFFFF;
  padding-top: 12px;


  /deep/ .el-tabs__nav {
    //width: calc(100% - 48px);
    margin: 0 24px;
    //display: flex;
    //align-items: center;
    //justify-content: space-between;
  }

  /deep/.el-tabs__header{
    margin-bottom: 24px;
  }
  /deep/ .el-tabs__item {
    height: auto;
    line-height: 20px;
    padding: 12px 21.5px;
    font-size: 16px;
    color: #333;
    font-weight: 600;
  }
  /deep/#tab-9898{
    width: 84px;
    flex-shrink: 0;
  }

  /deep/ .el-tabs__nav-wrap::after {
    background: #E6E6E6;
    height: 1px;
  }

  /deep/.el-tabs__item.is-active{
    color: var(--theme-color);
  }

  /deep/ .el-tabs__active-bar {
    height: 3px;
    border-radius: 8px;
  }

  /deep/ .more_tab_btn {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    .icons {
      transition: all .3s;
      color: #333;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      margin-left: 4px;
    }
  }
}
