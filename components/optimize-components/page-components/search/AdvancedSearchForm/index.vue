<template>
  <div class="advanced_search_form">
    <Toast ref="Toast"/>
    <div class="advanced_search_title"></div>
    <div class="advanced_search_form_content">
      <div class="content_title">
        使用以下条件精确搜索结果范围
      </div>
      <div class="content_tips">
        您可增加多个条件进行筛选
      </div>

      <div class="index_container">
        <AdvancedSearchIndex
          ref="contentKeywordRef"
          title="正文"
          :keywords.sync="contentKeywords"
        />
        <AdvancedSearchIndex
          ref="titleKeywordRef"
          title="标题"
          :keywords.sync="titleKeywords"
        />
        <AdvancedSearchIndex
          ref="authorKeywordRef"
          title="作者"
          :is-add-btn-hidden="true"
          :keywords.sync="authorKeywords"
        />
      </div>

      <div class="content_title" style="margin-bottom: 24px">
        发布时间
      </div>

      <div>
        <ReleaseTime
          ref="releaseTime"
          :first-time-props.sync="firstTime"
          :last-time-props.sync="lastTime"
        />
      </div>

      <div class="advanced_btn_wrapper">
        <div class="clear_btn btn_content" @click="clearHandler">清空</div>
        <div id="themeButton" class="search_btn btn_content" @click="searchHandler">搜索</div>
      </div>
    </div>
  </div>
</template>

<script>
import AdvancedSearchIndex from "../AdvancedSearchIndex/index.vue";
import ReleaseTime from "../ReleaseTime/index.vue";
import Toast from "../../../public/Toast/index.vue";

export default {
  name: "AdvancedSearchForm",
  components: {
    AdvancedSearchIndex,
    ReleaseTime,
    Toast
  },
  data() {
    return {
      contentKeywords: "",
      titleKeywords: "",
      authorKeywords: "",
      firstTime: "",
      lastTime: "",
    }
  },
  methods: {
    searchHandler() {

      if (this.contentKeywords === "" && this.titleKeywords === "" && this.authorKeywords === "") {
        this.$refs.Toast.$toast("请输入一个关键字")
        return;
      }

      if ((this.firstTime && !this.lastTime) || (this.lastTime && !this.firstTime)) {
        this.$refs.Toast.$toast("请正确选择时间")
        return;

      } else if (this.firstTime && this.lastTime) {
        if (new Date(this.firstTime).getTime() > new Date(this.lastTime).getTime()) {
          this.$refs.Toast.$toast("请正确选择时间")
          return;
        }
      }


      const contentKey = this.contentKeywords ? 'contentKeywords=' + this.contentKeywords + '&' : ''
      const titleKeywords = this.titleKeywords ? 'titleKeywords=' + this.titleKeywords + '&' : ''
      const authorKeywords = this.authorKeywords ? 'authorKeywords=' + this.authorKeywords + '&' : ''
      const firstTime = this.firstTime ? 'firstTime=' + this.firstTime + '&' : ''
      const lastTime = this.lastTime ? 'lastTime=' + this.lastTime + '&' : ''

      const formatTitle = this.titleKeywords.replace(/,/g, ";")
      const formatContent = this.contentKeywords.replace(/,/g, ";")
      const formatAuthor = this.authorKeywords.replace(/,/g, ";")
      // 埋点
      this.$analysys.advanced_search({
        title: formatTitle,
        content: formatContent,
        author: formatAuthor,
        searchEndTime: this.lastTime
      })

      window.open(`/advanced-search-result?${contentKey + titleKeywords + authorKeywords + firstTime + lastTime}`)
    },
    clearHandler() {
      this.$refs.contentKeywordRef.clearIndexHandler()
      this.$refs.titleKeywordRef.clearIndexHandler()
      this.$refs.authorKeywordRef.clearIndexHandler()
      this.$refs.releaseTime.clearHandler()
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
