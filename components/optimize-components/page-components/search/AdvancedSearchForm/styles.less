.advanced_search_form {
  width: 840px;
  border-radius: 8px;
  background: #FFF;
  position: relative;

  .advanced_search_title {
    width: 100%;
    height: 62px;
    background: url("assets/images/search/search_senior_page.jpg");
  }

  .advanced_search_form_content {
    padding: 24px 24px 64px;

    .content_title {
      font-weight: 500;
      color: #333;
      font-size: 16px;
      margin-bottom: 6px;
      position: relative;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: -24px;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 16px;
        border-radius: 0 2px 2px 0;
        background: #0581CE;
      }
    }

    .content_tips {
      font-size: 14px;
      color: #708AA2;
      line-height: 22px;
      margin-bottom: 24px;
    }

    .index_container {
      margin-bottom: 40px;
      display: grid;
      gap: 24px;
    }

    .advanced_btn_wrapper {
      display: flex;
      justify-content: end;
      gap: 24px;
      margin-top: 64px;

      .btn_content {
        width: 100px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px !important;
        cursor: pointer;
      }

      .clear_btn {
        color: #999EA4;
        background: #F4F6F8;
      }

      .search_btn {
        background: #0581CE;
        color: #FFF;
      }
    }
  }
}
