<template>
  <SearchListCard
    v-if="list.length>0"
    bg-color="linear-gradient(180deg, #1A83FF -137.44%, #FFF 28.64%)"
    font-color="#0581CE"
    title="热门话题"
    icon="search_talk"
    :bg-image-url="require('~/assets/images/search/hot_talk.png')"
  >
    <ul class="list_wrapper">
      <li v-for="(item,index) in list" :key="item.id" class="list_item">
        <span class="num">{{ index + 1 }}</span>
        <a :href="`/topic-circle/communitytopic?id=${item.associationId}`"  target="_blank" class="list_title text-limit-1">{{ item.title}}</a>
      </li>
    </ul>
  </SearchListCard>
</template>

<script>
import SearchListCard from "../SearchListCard/index.vue";
import {getTopicList} from "../../../../../api/search";

export default {
  name: "HotTalk",
  components: {
    SearchListCard
  },
  data(){
    return {
      list:[]
    }
  },
  mounted() {
    this.$axios.$request(getTopicList({
      limit:10
    })).then(res=>{
      if(res.code === 1){
        this.list = res.list
      }
    })
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
