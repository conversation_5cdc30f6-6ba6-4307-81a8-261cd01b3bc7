<template>
  <div class="card_container" :style="{background:bgColor}">
    <div v-if="bgImageUrl" class="card_bg_image">
      <img :src="bgImageUrl" alt="bgImage">
    </div>
    <div class="card_title">
      <div class="title_left">
        <SvgIcon :icon-class="icon" class-name="icons"/>
        <span class="title" :style="{color: fontColor}">{{ title }}</span>
      </div>
      <div class="title_right">
        <slot name="title_right"></slot>
      </div>
    </div>

    <div class="card_content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "SearchListCard",
  props: {
    bgColor: {
      type: String,
      default: "linear-gradient(180deg, #FF8567 -137.44%, #FFF 28.64%)"
    },
    bgImageUrl: {
      default: "",
      required: false,
      type: String,
    },
    fontColor: {
      type: String,
      default: "#F34000"
    },
    title: {
      required: false,
      type: String,
      default: "title"
    },
    icon: {
      type: String,
      default: "hot_search"
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
