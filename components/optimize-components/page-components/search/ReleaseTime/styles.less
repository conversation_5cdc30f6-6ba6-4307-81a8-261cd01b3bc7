
.release_time_wrapper {
  display: flex;
  align-items: center;
  gap: 0 40px;

  .time_btn_wrapper {
    display: flex;
    align-items: center;
    gap: 24px;

    .time_btn {
      width: 80px;
      height: 38px;
      border-radius: 4px;
      background: #F4F6F8;
      font-size: 14px;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      user-select: none;
    }

    .time_btn_active {
      outline: 1px solid #0581CE;
      background: #EFFAFF;
      color: #0581CE;
    }
  }

  .time_picker_wrapper {
    display: flex;
    align-items: center;

    /deep/.el-date-editor.el-input, .el-date-editor.el-input__inner{
      width: 169px;
    }

    /deep/.el-input__inner{
      border: none;
      border-radius: 4px;
      background: #F4F6F8;
      color: #50789C;
      //padding: 0;
      //text-align: center;

      &::placeholder{
        color: #50789C;
      }
    }

    /deep/.el-input__icon{
      color: #50789C;
    }

    .separator {
      width: 13px;
      height: 1px;
      background-color: #708AA2;
      margin: 0 3px;
    }
  }
}
