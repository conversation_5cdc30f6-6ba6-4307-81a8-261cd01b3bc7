<template>
  <div class="release_time_wrapper">
    <div class="time_btn_wrapper">
      <div
        v-for="(item,index) in timeArr"
        :key="index"
        class="time_btn"
        :class="activeTime === item.key ? 'time_btn_active' : ''"
        @click="changeTimeBtn(item.key)">{{ item.name }}
      </div>
    </div>
    <div class="time_picker_wrapper">
      <DatePicker
        v-model="firstTime"
        type="date"
        placeholder="起始日期"
        value-format="yyyy-MM-dd"
        @change="changeTimeHandler"
      >
      </DatePicker>
      <div class="separator"></div>
      <DatePicker
        v-model="lastTime"
        type="date"
        placeholder="截止日期"
        value-format="yyyy-MM-dd"
        @change="changeTimeHandler"
      >
      </DatePicker>
    </div>
  </div>
</template>

<script>
import {DatePicker} from "element-ui";

export default {
  name: "ReleaseTime",
  components: {
    DatePicker
  },
  props: {
    firstTimeProps: {
      type: String,
      default: ""
    },
    lastTimeProps: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      timeArr: [
        {name: "不限", key: "no"},
        {name: "一周", key: "week"},
        {name: "一月", key: "month"},
        {name: "半年", key: "year"},
      ],
      activeTime: "no",
      firstTime: "",
      lastTime: ""
    }
  },
  watch: {
    firstTime(newValue) {
      this.$emit("update:firstTimeProps", newValue)
    },
    lastTime(newValue) {
      this.$emit("update:lastTimeProps", newValue)
    }
  },
  methods: {
    changeTimeHandler() {
      if (!this.firstTime && !this.lastTime) {
        this.activeTime = "no"
      } else {
        this.activeTime = ""
      }
    },
    changeTimeBtn(key) {
      this.activeTime = key

      // 获取当前日期
      const currentDate = new Date();
      switch (key) {
        case "no" : {
          this.firstTime = ""
          this.lastTime = ""
          break
        }
        case "week" : {
          // 获取一周前的日期
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

          // 格式化日期为 "YYYY-MM-DD" 的格式
          this.firstTime = oneWeekAgo.toISOString().slice(0, 10);
          this.lastTime = currentDate.toISOString().slice(0, 10);

          break;
        }
        case "month" : {
          // 获取一个月前的日期
          const oneMonthAgo = new Date();
          oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

          // 格式化日期为 "YYYY-MM-DD" 的格式
          this.firstTime = oneMonthAgo.toISOString().slice(0, 10);
          this.lastTime = currentDate.toISOString().slice(0, 10);

          break;
        }
        case "year" : {
          // 获取一个月前的日期
          const sixMonthAgo = new Date();
          sixMonthAgo.setMonth(sixMonthAgo.getMonth() - 6);

          // 格式化日期为 "YYYY-MM-DD" 的格式
          this.firstTime = sixMonthAgo.toISOString().slice(0, 10);
          this.lastTime = currentDate.toISOString().slice(0, 10);

          break;
        }
      }
    },

    clearHandler() {
      this.activeTime = "no"
      this.firstTime = ""
      this.lastTime = ""
    }


  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
