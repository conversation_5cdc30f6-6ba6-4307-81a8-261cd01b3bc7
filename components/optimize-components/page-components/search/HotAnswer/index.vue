<template>
  <SearchListCard
    v-if="list.length>0"
    bg-color="linear-gradient(180deg, #6D6AFF -137.44%, #FFF 28.64%)"
    font-color="#4E31FF"
    title="热门回答"
    icon="search_answer"
    :bg-image-url="require('~/assets/images/search/hot_answer.png')"
  >
    <ul class="list_wrapper">
      <li v-for="(item,index) in list" :key="item.id" class="list_item">
        <span class="num">{{ index + 1 }}</span>
        <a
          target="_blank" class="list_title text-limit-1"
          :href="`${item.type === 'Q' ? `/topic-circle/questionhome?id=${item.parentQaId}` : `/topic-circle/answerhome/${item.associationId}`}`"
        >
          {{ item.title }}
        </a>
      </li>
    </ul>
  </SearchListCard>
</template>

<script>
import SearchListCard from "../SearchListCard/index.vue";
import {getQuestionAnswer} from "../../../../../api/search";

export default {
  name: "HotAnswer",
  components: {
    SearchListCard
  },
  data() {
    return {
      list: []
    }
  },
  mounted() {
    this.$axios.$request(getQuestionAnswer({
      limit: 10
    })).then(res => {
      if (res.code === 1) {
        this.list = res.list
      }
    })
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
