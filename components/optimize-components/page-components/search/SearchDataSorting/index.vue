<template>
  <div class="search_sort_btn_wrapper">
    <template v-for="(item,index) in sortList">
      <div
        v-if="item.isEnable"
        :key="index"
        class="search_sort_btn"
        :class="activeSort === item.key ? 'search_sort_btn_active' : ''"
        @click="changeSortHandler(item.key)"
      >
        {{ item.name }}
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "SearchDataSorting",
  props: {
    isBmsAuth:{
      type: Boolean,
      default: false
    },
    isDefaultEnable: {
      type: Boolean,
      default: true
    },
    isEssencesEnable: {
      type: Boolean,
      default: true
    },
    hotName: {
      type: String,
      default: "最热"
    },
    isHotEnable:{
      type: Boolean,
      default: true
    },
    activeSort: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      sortList: [
        {name: "综合排序", key: "", isEnable: this.isDefaultEnable},
        {name: "最新", key: "1", isEnable: true},
        {name: this.hotName, key: "2", isEnable: this.isHotEnable},
        {name: "精华", key: "3", isEnable: this.isEssencesEnable},
        {name: "品牌认证", key: "4", isEnable: this.isBmsAuth},
      ]
    }
  },
  methods: {
    // 列表排序切换
    changeSortHandler(key) {
      this.$emit("update:activeSort", key)
      this.$emit("changeSortHandler", key)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
