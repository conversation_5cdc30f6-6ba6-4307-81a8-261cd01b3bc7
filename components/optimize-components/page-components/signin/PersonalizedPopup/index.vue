<template>
  <transition v-if="visible" name='el-fade-in-linear'>
    <NewBackdrop :pellucidity="0.3">
      <div class="personalized_content">
        <div class="personalized_content_title">
          <div class="step">
            <span style="color: var(--theme-color)">{{ step }}/</span>
            <span>2</span>
          </div>
          <div class="tips">
            {{
              step === 1 ?
                "请选择您感兴趣的专业"
                :
                "根据专业选择，推荐您关注以下专家关注后可获得对方最新的动态"
            }}
          </div>
        </div>
        <div v-show="step === 1" class="personalized_content_tips">
          我们将根据您的选择，为您推荐喜欢的内容
        </div>


        <PersonalizedStep1
          v-show="step === 1"
          :subspecialty-list="SubspecialtyList"
          :user-speciality-id="userSpecialityId"
          @addSpecialityStr="(data) => specialityStr = data"
        />
        <PersonalizedStep2
          v-show="step === 2"
          :expert-by-speciality-list="ExpertBySpecialityList"
          @refreshExpert="refreshExpertHandler"
          @addFollowerIdStr="(data) => followerIdStr = data"
        />


        <button id="themeButton" class="personalized_button" @click="submitHandler">
          <i v-if="loading" class="el-icon-loading"></i>
          <span v-else>{{ step === 1 ? "下一步" : "完成" }}</span>
        </button>
      </div>
    </NewBackdrop>
  </transition>

</template>

<script>
import NewBackdrop from "../../../UI/NewBackdrop/index.vue";
import {
  batchFollow,
  getExpertBySpeciality,
  getInterestedSubspecialtyList,
  saveUserInterestedSubspecialty
} from "../../../../../api/personalized/default";
import PersonalizedStep1 from "./PersonalizedStep1/index.vue";
import PersonalizedStep2 from "./PersonalizedStep2/index.vue";
import {userInfo} from "../../../../../api/user";

export default {
  name: "PersonalizedPopup",
  components: {PersonalizedStep2, PersonalizedStep1, NewBackdrop},
  data() {
    return {
      visible: false,
      loading: false,
      step: 1,
      specialityStr: "",
      followerIdStr: "",
      SubspecialtyList: [],
      ExpertBySpecialityList: [],
      userSpecialityId:null
    }
  },
  mounted() {
    this.getInterestedSubspecialtyListHandler()
  },
  methods: {
    async getInterestedSubspecialtyListHandler() {
      const [request1,request2] =await Promise.all([
        this.$axios.$request(getInterestedSubspecialtyList({
          loginUserId: this.$store.state.auth.user.id
        })),
        this.$axios.$request(userInfo())
      ])

      if (request1.code === 1) {
        this.SubspecialtyList = request1.list;
      }

      if (request2.code === 1) {
        if (request2.result.specialityList && request2.result.specialityList.length > 0) {
          this.userSpecialityId = request2.result.specialityList[0].id
        }
      }
      this.visible = true;
    },
    getExpertBySpecialityHandler({backFn = null}) {
      this.$axios.$request(getExpertBySpeciality({
        specialityStr: this.specialityStr
      })).then(res => {
        if (res.code === 1) {
          this.ExpertBySpecialityList = res.list

          if (backFn) {
            backFn(true)
          }
        }
      })
    },
    refreshExpertHandler(data, backFn) {
      this.getExpertBySpecialityHandler({backFn})
    },
    submitHandler() {
      if (this.step === 1) {

        if (this.specialityStr) {
          this.loading = true;
          this.getExpertBySpecialityHandler({
            backFn: () => {
              this.step = 2
              this.loading = false;
              this.$axios.$request(saveUserInterestedSubspecialty({
                specialityStr: this.specialityStr
              })).then(res => {
                if (res.code === 1) {
                  window.localStorage.setItem("personalized_popup_visible", 'false');
                }
              })
            }
          })
        } else {
          this.$toast("最少选择一个")
        }


      } else if (this.step === 2) {
        if (this.followerIdStr) {
          this.$axios.$request(batchFollow({
            followerIdStr: this.followerIdStr
          }))
        }
        this.$emit("cancel", false)
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
