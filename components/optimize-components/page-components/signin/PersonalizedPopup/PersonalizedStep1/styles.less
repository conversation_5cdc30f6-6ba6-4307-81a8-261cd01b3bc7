.sub_major_wrapper{
  .sub_major_list{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-gap: 24px;

    .sub_major_item{
      cursor: pointer;
      flex-shrink: 0;
      width: 179px;
      height: 107px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-flow: column;
      border: 1px solid #E9EEF2;
      background: #F8F8F8;
      border-radius: 8px;
      position: relative;

      .sub_major_image{
        width: 60px;
        height: 60px;
      }
      .sub_major_name{
        font-size: 12px;
        color: #676C74;
      }
      .icons{
        position: absolute;
        right: -1px;
        top: -1px;
        width: 28px;
        height: 28px;
      }
      &:hover{
        border: 1px solid #D3F1FF;
        background: #EFFAFF;
      }
    }

    .sub_major_item_active{
      border: 1px solid #D3F1FF;
      background: #EFFAFF;
    }
  }
}
