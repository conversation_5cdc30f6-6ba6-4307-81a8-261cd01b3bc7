<template>
  <div class="sub_major_wrapper">
    <div class="sub_major_list">
      <div
        v-for="item in SubspecialtyList"
        :key="item.id"
        class="sub_major_item"
        :style="userSpecialityId === item.id ? {cursor:'not-allowed'} : {}"
        :class="envMap[item.id] ? 'sub_major_item_active' : ''"
        @click="addSubspecialHandler(item.id)"
      >
        <div class="sub_major_image">
          <img class="img_cover" :src="$tool.compressImg(item.dictIcon,60,60)" alt="">
        </div>
        <div class="sub_major_name">{{ item.name }}</div>
        <svg-icon v-if="envMap[item.id] && userSpecialityId !== item.id" icon-class="active_sub_major"
                  class-name="icons"/>
        <svg-icon v-if="userSpecialityId === item.id" icon-class="ganxingqu_default" class-name="icons"/>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "PersonalizedStep1",
  props: ["SubspecialtyList", "userSpecialityId"],
  data() {
    return {
      envMap: {},
    }
  },
  mounted() {
    if (this.SubspecialtyList.length) {
      this.SubspecialtyList.forEach(item => {
        if (item.interestedStatus === "T") {
          this.addSubspecialHandler(item.id)
        }


      })
    }
    if (this.userSpecialityId && !this.envMap.hasOwnProperty(this.userSpecialityId) && this.SubspecialtyList.filter(item => item.id === this.userSpecialityId).length > 0) {
      this.addSubspecialHandler(this.userSpecialityId)
    }
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-09-14 10:36
     * 废弃
     * ------------------------------------------------------------------------------
     */
    // this.$axios.$request(getUserInterestedSubspecialtyList()).then(res=>{
    //   if(res.code === 1 && res.list && res.list.length>0){
    //     res.list.forEach(item => {
    //       this.addSubspecialHandler(item.id)
    //     })
    //   }
    // })
  },
  methods: {
    addSubspecialHandler(id) {
      let SpecialityStr = ""
      if (this.envMap.hasOwnProperty(id)) {

        if (this.userSpecialityId !== id) {
          this.$delete(this.envMap, id)
        }

      } else {
        this.$set(this.envMap, id, 1)
      }

      for (const key in this.envMap) {
        SpecialityStr += key + ","
      }
      SpecialityStr = SpecialityStr.substring(0, SpecialityStr.length - 1)

      console.log(SpecialityStr)
      this.$emit("addSpecialityStr", SpecialityStr)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
