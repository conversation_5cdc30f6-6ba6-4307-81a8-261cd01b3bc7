<template>
  <div class="sub_major_wrapper">
    <div class="sub_major_list">
      <div
        v-for="item in ExpertBySpecialityList"
        :key="item.id"
        class="sub_major_item"
        @click="addSubspecialHandler(item.id)"
      >
        <div class="sub_major_image">
          <img class="img_cover"
               :src="item.avatarAddress ? $tool.compressImg(item.avatarAddress,48,48) : require('/assets/images/user.png')">
        </div>
        <div class="right_information">
          <div class="title">
            <span class="name text-limit-1">{{ item.realName }}</span>
            <span class="title">{{ item.title }}</span>
          </div>
          <div class="company text-limit-1">{{ item.company }}</div>
        </div>
        <svg-icon v-if="envMap[item.id]" icon-class="active_sub_major" class-name="icons"/>
        <svg-icon v-else icon-class="not_active_sub_major" class-name="icons"/>
      </div>
    </div>
    <div class="Refresh" @click="refreshExpert">
      <svg-icon ref="Refresh" icon-class="Refresh" class-name="icons"/>
      <span>刷新</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "PersonalizedStep2",
  props: ["ExpertBySpecialityList"],
  watch: {
    ExpertBySpecialityList(newValue) {
      newValue.forEach(item => {
        this.addSubspecialHandler(item.id)
      })
    }
  },
  data() {
    return {
      deg: 1,
      flag: true,
      envMap: {}
    }
  },
  methods: {
    refreshExpert() {
      if (this.flag) {
        this.flag = false;
        this.$emit("refreshExpert", null, val => {
          if (val) {
            this.flag = true;
            this.envMap = {};
          }
        })
        const svgDom = this.$refs.Refresh.$el
        svgDom.style.transform = `rotateZ(${360 * this.deg}deg)`
        this.deg += 1;
      }
    },
    addSubspecialHandler(id) {
      let followerIdStr = ""
      if (this.envMap.hasOwnProperty(id)) {
        this.$delete(this.envMap, id)
      } else {
        this.$set(this.envMap, id, 1)
      }

      for (const key in this.envMap) {
        followerIdStr += key + ","
      }
      followerIdStr = followerIdStr.substring(0, followerIdStr.length - 1)

      console.log(followerIdStr)

      this.$emit("addFollowerIdStr", followerIdStr)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
