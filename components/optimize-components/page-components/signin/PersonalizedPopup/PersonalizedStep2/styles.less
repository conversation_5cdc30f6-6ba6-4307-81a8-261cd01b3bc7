.sub_major_wrapper {
  margin: 46px 0 52px;

  .sub_major_list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 17px;

    .sub_major_item {
      cursor: pointer;
      flex-shrink: 0;
      height: 82px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F6F6F6;
      border-radius: 8px;
      position: relative;
      padding: 0 8px;

      .sub_major_image {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        margin-right: 8px;
        border: 1px solid #FFF;
        border-radius: 50%;
        overflow: hidden;
      }

      .right_information {
        flex: auto;
        padding-right: 20px;

        .title {
          display: flex;
          align-items: center;

          .name {
            margin-right: 8px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }

          .title {
            flex-shrink: 0;
            color: #708AA2;
            font-size: 12px;
            font-weight: 400;
          }
        }

        .company {
          margin-top: 8px;
          color: #C7C7C7;
          font-size: 12px;
        }
      }

      .icons {
        position: absolute;
        right: -1px;
        top: -1px;
        width: 28px;
        height: 28px;
      }
    }
  }

  .Refresh {
    user-select: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #708AA2;
    font-size: 12px;
    margin: 24px 0 0;

    .icons {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      transition: all .6s;
    }
  }
}
