<template>
  <div class="comment_list_item_container">
    <div v-if="identity === 7" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="avatarAddress"
      />
    </div>
    <a v-else target="_blank" :href="`/user-center?profileUserId=${creatorId}`" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="avatarAddress"
      />
    </a>
    <div class="item_content">
      <div class="user_name">
        <span>{{ identity === 7 ? '匿名用户（非医务工作者）' : realName }}</span>
      </div>
      <div class="comment_text">
        {{ text }}
      </div>
      <div class="comment_console">
        <span class="time">{{ creationDate }}</span>
        <div class="operate">
          <div class="like" :class="isLike ? 'active_like' : ''" @click="diggHandler">
            <svg-icon icon-class="comment_zan-2" class-name="icon"/>
            <span>{{ likes }}</span>
          </div>
          <div class="like" @click="replyHandler">
            <svg-icon icon-class="comment-reply-2" class-name="icon"/>
            <span>回复</span>
          </div>
        </div>
      </div>

      <div v-if="childComments.length>0" class="child_list_wrapper">
        <ChildListItem
          v-for="(item,index) in newChildComments"
          v-show="showMore ? true : index < 3"
          :id="item.id"
          :key="item.id"
          :child-avatar="item.childAvatar"
          :child-name="item.childName"
          :text="item.text"
          :child-creation-date="item.childCreationDate"
          :diggs="item.diggs"
          :is-digg="typeof item.isDigg === 'string' ? item.isDigg === 'true' : item.isDigg"
          :parent="item.parent"
          :child-id="item.childId"
          :identity="item.identity"
          :parent-identity="item.parentIdentity"
          @childReply="childReplyHandler(item.identity === 7 ? '匿名用户（非医务工作者）' : item.childName,item.id)"
          v-on='$listeners'
        />
        <div v-if="newChildComments.length>3" class="show_more" @click="showMoreHandler">
          <span>{{ showMore ? '收回' : '更多' }}</span>
          <svg-icon
            icon-class="comment_more"
            class-name="more_svg"
            :style="showMore ? {transform:'rotateZ(180deg)',marginBottom:'0',marginTop:'4px'} : {}"/>
        </div>
      </div>

      <transition name="el-fade-in">
        <div v-show="replyId" :style="{marginTop: '20px'}">
          <ReplyInput
            :id="id"
            :reply-name="replyName"
            @replyPostHandler="replyPostHandler"
            v-on='$listeners'
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import ReplyInput from "../../ReplyInput/index.vue";
import {ZipImg} from "../../../index";
import ChildListItem from "../ChildListItem/index.vue";

export default {
  name: "ListItem",
  components: {ChildListItem, ZipImg, ReplyInput},
  props: {
    id: {
      type: Number,
      default: null
    },
    creatorId: {
      type: Number,
      default: null
    },
    avatarAddress: {
      type: String,
      default: ""
    },
    realName: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    },
    creationDate: {
      type: String,
      default: ""
    },
    diggs: {
      type: Number,
      default: 0
    },
    isDigg: {
      type: Boolean,
      default: false
    },
    childComments: {
      type: Array,
      default: () => []
    },
    identity: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showMore: false,
      replyId: null,
      replyName: "",
      isLike: this.isDigg,
      likes: this.diggs,
      newChildComments: this.childComments || []
    }
  },
  methods: {
    showMoreHandler() {
      this.showMore = !this.showMore
    },
    diggHandler() {
      this.isLike = !this.isLike;
      this.isLike ? this.likes += 1 : this.likes -= 1
      this.$emit('likeHandler', this.id, (isSuccess) => {
        if (!isSuccess) {
          if (this.isLike) {
            this.isLike = false;
            this.likes -= 1
          }
        }
      });
    },
    replyHandler() {
      this.replyName = this.identity === 7 ? '匿名用户（非医务工作者）' : this.realName;
      this.replyId = this.replyId === this.id ? null : this.id;


      // eslint-disable-next-line no-undef
      setTimeout(() => {
        if (this.replyId) {
          window.document.getElementById(`replyInput-${this.id}`).focus()
        }
      }, 100)
    },
    replyPostHandler(value, backFn) {
      if (!value) {
        this.$toast('请输入评论内容!')
      } else {
        this.$emit('pushHandler', {info: value, parentId: this.replyId}, (result) => {
          if (result) {
            const newArr = {
              id: result.id,
              childId: result.creator.id,
              childName: result.creator.realName,
              childAvatar: result.creator.avatarAddress,
              childCreationDate: this.timeStamp.timestampFormat(result.creationDate / 1000),
              diggs: result.diggs,
              isDigg: false,
              text: result.text,
              parent: this.newChildComments.filter(item => item.id === this.replyId).length > 0 ? this.replyName : "",
            }
            this.newChildComments.unshift(newArr)
            backFn("")
            this.replyId = null;
          }
        })
      }
    },
    childReplyHandler(name, id) {
      this.replyName = name;
      this.replyId = this.replyId === id ? null : id;


      // eslint-disable-next-line no-undef
      setTimeout(() => {
        if (this.replyId) {
          window.document.getElementById(`replyInput-${this.id}`).focus()
        }
      }, 100)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
