
.comment_list_item_container {
  display: flex;
  align-items: start;
  border-bottom: 0.5px solid rgba(225, 225, 225, 0.42);
  padding-bottom: 24px;
  margin-bottom: 24px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .user_image {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    margin-right: 16px;
  }

  .item_content {
    flex: 1;

    .user_name {
      color: #676C74;
      margin-bottom: 8px;
    }

    .comment_text {
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 9.5px;
    }

    .comment_console {
      display: flex;
      align-items: center;

      .time {
        color: #708AA2;
        margin-right: 26px;
        font-size: 12px;
      }

      .operate {
        display: flex;
        align-items: center;

        .like {
          user-select: none;
          margin-right: 26px;
          display: flex;
          align-items: center;
          color: #708AA2;
          font-size: 12px;
          cursor: pointer;

          .icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
          }

          &:hover {
            color: var(--theme-color);

            .icon {
              color: var(--theme-color);
            }
          }
        }

        .active_like {
          color: var(--theme-color);

          .icon {
            color: var(--theme-color);
          }
        }
      }
    }

    .child_list_wrapper {
      margin-top: 24px;

      .show_more {
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #0581CE;
        line-height: 22px;
        cursor: pointer;

        .more_svg {
          width: 14px;
          height: 14px;
          margin-left: 2px;
          margin-bottom: 2px;
          transition: all .2s;
        }
      }
    }
  }
}
