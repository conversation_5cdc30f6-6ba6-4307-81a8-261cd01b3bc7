
.post_input_wrapper {
  border-radius: 8px;
  overflow: hidden;

  /deep/ .el-textarea__inner {
    height: auto;
    min-height: 35px!important;
    max-height: 200px;
    background: #F4F6F8;
    color: #333;
    font-size: 16px;
    border: none;
    border-radius: 0;
    padding: 12px;

    &::placeholder {
      color: #708AA2;
    }

    &::-webkit-scrollbar {
      width: 4px;
    }
  }

  .comment_console {
    background: #F4F6F8;
    display: flex;
    align-items: center;
    justify-content: end;
    min-height: 50px;
    padding: 0 12px 0 16px;
    user-select: none;

  }
}
