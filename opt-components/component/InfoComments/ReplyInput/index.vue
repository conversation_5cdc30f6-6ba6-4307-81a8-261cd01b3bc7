<template>
  <div class="post_input_wrapper">
    <el-input
      :id="`replyInput-${id}`"
      v-model.trim="postValue"
      type="textarea"
      :placeholder="`回复${replyName}:`"
      resize="none"
      maxlength="150"
      @keyup.enter.native="postHandler"
    ></el-input>
    <div class="comment_console">
      <PostBtn @onClick="postHandler"/>
    </div>
  </div>
</template>

<script>
import PostBtn from "../PostBtn/index.vue";

export default {
  name: "ReplyInput",
  components: {PostBtn},
  props: {
    id: {
      type: Number,
      default: null
    },
    replyName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      postValue: ""
    }
  },
  methods: {
    postHandler() {
      this.$emit('replyPostHandler', this.postValue, value => {
        this.postValue = value
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
