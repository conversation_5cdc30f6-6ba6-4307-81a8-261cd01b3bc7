<template>
  <div class="post_button" :class="value.length>0 ? 'active_button' : ''" @click.stop="$emit('onClick','')">发表</div>
</template>

<script>
export default {
  name: "PostBtn",
  props:{
    value:{
      type:String,
      default:""
    }
  }
}
</script>

<style scoped lang="less">
.post_button {
  width: 68px;
  height: 37px;
  border-radius: 4px;
  background: #A7BACB;
  color: #EFFAFF;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  user-select: none;
  transition: all .2s;

  &:hover {
    background: var(--theme-color);
    color: #FFFFFF;
  }

  &:active {
    background: #46B0F2;
    color: #FFFFFF;
  }
}
.active_button{
  background: var(--theme-color);
  color: #FFFFFF;
}
</style>
