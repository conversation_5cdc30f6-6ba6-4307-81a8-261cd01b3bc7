<template>
  <div class="latest_comments_wrapper">
    <div class="title">最新评论</div>
    <div style="margin-bottom: 32px">
      <PostInput @postHandler="postHandler"/>
    </div>
    <div>
      <DropdownLoading
        :loading="loading"
        :empty="commentDataList.length===0"
        :no-more="current >= commentData.page.totalPage"
        :hide-empty-tips="true"
        :empty-tips-value="`没有更多评论了~`"
        @hit-bottom="hitBottomChangeHandler"
      >
        <CommentList
          :list="commentDataList"
          v-on='$listeners'
        />
        <template #emptyTipsSlot>
          <div style="color: #999EA4; text-align: center;font-size: 16px;margin-bottom: 32px">没有更多评论了~</div>
        </template>

      </DropdownLoading>
      <FixedPostInput @postHandler="postHandler"/>
    </div>
  </div>
</template>

<script>
import PostInput from "./PostInput/index.vue";
import FixedPostInput from "./FixedPostInput/index.vue";
import CommentList from "./CommentList/index.vue";
import DropdownLoading from "../../../components/optimize-components/public/DropdownLoading/index.vue";

export default {
  name: "InfoComments",
  components: {
    PostInput,
    CommentList,
    FixedPostInput,
    DropdownLoading
  },
  props: {
    commentData: {
      type: Object,
      default: () => {
        return {
          list: [
            {
              id: null,
              text: "",
              isDigg: false,
              diggs: 0,
              creator: {},
              creationDate: null,
              childComments: [],
            }
          ],
          page: {}
        }
      }
    }
  },
  data() {
    return {
      commentDataList: [],
      loading: true,
      current: 1
    }
  },
  computed: {
    computedFormatList() {
      if (this.commentData.list.length > 0) {
        return this.commentData.list.map(item => {
          return {
            id: item.id,
            text: item.text,
            isDigg: item.isDigg,
            diggs: item.diggs,
            creator: {
              id: item.creator.id,
              avatarAddress: item.creator.avatarAddress,
              realName: item.creator.realName,
              identity: item.creator.identity
            },
            creationDate: this.timeStamp.timestampFormat(item.creationDate / 1000),
            childComments: item.childComments && item.childComments.length > 0 ? item.childComments.map(itemChild => {
              return {
                id: itemChild.id,
                childId: itemChild.childId,
                childName: itemChild.child,
                childAvatar: itemChild.childAvatar,
                childCreationDate: this.timeStamp.timestampFormat(itemChild.creationDate / 1000),
                diggs: itemChild.diggs,
                isDigg: itemChild.isDigg,
                text: itemChild.text,
                parent: itemChild.parent,
                identity: itemChild.identity,
                parentIdentity:itemChild.parentIdentity
              }
            }) : []
          }
        })
      } else {
        return []
      }
    }
  },
  watch: {
    commentDataList: {
      handler() {
        this.loading = false;
      },
      deep: true
    },
    computedFormatList(newValue) {
      this.commentDataList = newValue;
    }
  },
  methods: {
    hitBottomChangeHandler(isBottom) {
      this.loading = true;
      if (isBottom) {
        this.current += 1;
        this.$emit('pageUp', this.current, val => {
          if (val) {
            this.loading = false;
          }
        })

      }
    },
    postHandler(value, backFn) {
      this.$emit('pushHandler', {info: value}, (result) => {
        if (result) {
          backFn("")
          const newArr = {
            id: result.id,
            text: result.text,
            isDigg: false,
            diggs: result.diggs,
            creator: {
              id: result.creator.id,
              avatarAddress: result.creator.avatarAddress,
              realName: result.creator.realName,
            },
            creationDate: this.timeStamp.timestampFormat(result.creationDate / 1000),
            childComments: []
          }
          this.commentDataList.unshift(newArr)
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
