<template>
  <div class="info_content_wrapper">
    <article
      v-if="typeof content !== 'string'"
      ref="infoArticleContent"
      :class='(!$store.state.auth.token && articleHideNum > 3) ? "hide-article-content" :""'
      @click="previewHandler"
    >
      <CompatibleArticle :content="content"/>
    </article>


    <article
      v-else
      ref="infoArticleContent"
      :class='(!$store.state.auth.token && articleHideNum > 3) ? "hide-article-content" :""'
      @click="previewHandler"
      v-html="content"
    >
    </article>

    <el-image
      ref="preview"
      style="width: 100px; height: 100px;display: none"
      :preview-src-list="srcList">
    </el-image>
  </div>
</template>

<script>
import CompatibleArticle from "./CompatibleArticle/index.vue";

export default {
  name: "RichTextContent",
  components: {
    CompatibleArticle
  },
  props: {
    content: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      srcList: [],
      articleHideNum: 1,
    }
  },
  mounted() {
    const iframeArr = document.querySelectorAll('iframe')
    const textArea = document.querySelectorAll('article textarea')
    for (let i = 0; i < iframeArr.length; i++) {
      const srcName = iframeArr[i].getAttribute('src')
      const video = document.createElement('video')
      video.style.width = '100%'
      video.style.background = 'black'
      video.setAttribute('controls', 'controls')
      video.style.height = iframeArr[i].offsetWidth * (9 / 16) + 'px'
      video.src = srcName

      const parentElement = iframeArr[i].parentElement
      parentElement.insertAdjacentElement('afterbegin', video)

      iframeArr[i].remove()
    }

    if (textArea && textArea.length > 0) {
      for (let i = 0; i < textArea.length; i++) {
        if (!textArea[i].value) {
          textArea[i].remove()
        } else {
          const p = document.createElement('p')
          p.innerHTML = textArea[i].value
          textArea[i].parentNode.replaceChild(p, textArea[i])
        }
      }
    }

  },
  methods: {
    previewHandler(event) {
      const mateStr = "patient.medtion.com"
      const tagParentName = event?.target?.parentNode?.tagName
      const tagParentHref = event?.target?.parentNode?.href
      const tagName = event?.target?.tagName
      const tagHref = event?.target?.href


      if ((tagName === 'A' && tagHref && tagHref.includes(mateStr)) || (tagParentName === "A" && tagParentHref && tagParentHref.includes(mateStr))) {
        this.$analysys.new_btn_click({
          userId: this.$store.state.auth.user.id,
          btnName: "跳转患者端",
          pageName: document.title,
          tourl: tagHref || tagParentHref
        })
      }

      if (event?.target?.tagName === 'IMG' && tagParentName !== "A" && event?.target.src) {
        this.srcList = [event?.target.src]
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.$refs.preview.clickHandler()
        }, 0)
      }
    }
  }
}
</script>

<style>
article video {
  max-width: 100%;
  width: 100%;
}

article img {
  max-width: 100%;
  width: 100%;
  height: auto;
  cursor: zoom-in;
}

article a > img {
  cursor: pointer;
}

article section {
  max-width: 100%;
}

article iframe {
  width: 100%;
  min-height: 45vh;
}


</style>
<style scoped lang="less">
@import "./styles";
@import "./adapter.less";
</style>
