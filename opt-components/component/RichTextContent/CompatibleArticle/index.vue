<template>
  <div>
    <template v-for="(item,index) in content">
      <div v-if='item.type === "txt"' :key="index">
        <img v-if='item.resource' :src='item.resource' alt=''/>
        <p v-if='item.text' v-html='item.text'></p>
      </div>
      <div v-else-if='item.type === "title"' :key="index">
        <img v-if='item.resource' :src='item.resource' alt=''/>
        <p v-if='item.text' v-html='item.text'></p>
      </div>
      <div v-else-if='item.type === "img"' :key="index">
        <img v-if='item.resource' :src='item.resource' alt=''/>
        <p v-if='item.text' v-html='item.text'></p>
      </div>
      <div v-else-if='item.type === "video"' :key="index">
        <video
          v-if='item.resource'
          :poster='item.image'
          :src='item.resource'
          class='video'
          controls='controls'
          loop='-1'
          muted='muted'
          type='video/mp4'
        >
          <p>你的浏览器不支持video标签.</p>
        </video>
        <p v-if='item.text' v-html='item.text'></p>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "CompatibleArticle",
  props: ["content"]
}
</script>

<style scoped lang="less">

</style>
