/*新版编辑器标题*/
/deep/ article {
  line-height: 1.8;

  ul li:before {
    display: inline-block;
    content: '•';
    width: 20px;
    font-size: inherit;
    text-align: center;
    text-indent: 0 ! important;
  }

  ol {
    counter-reset: item;
  }

  ol li:before {
    display: inline-block;
    content: counter(item) ".";
    counter-increment: item;
    font-size: inherit;
    min-width: 20px;
    text-align: center;
  }

  h2 {
    margin: 1.2rem 0 .8rem;
    padding: 0;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.1rem;
    color: #333333;
  }

  h2.htwo {
    position: relative;
    padding-left: 0.6rem;
  }

  .imageDelete, .imageEdit {
    position: absolute;
    display: none;
  }

  h2.htwo::before {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    content: '';
    width: 0.2rem;
    height: 1.1rem;
    background: #0581ce;
  }

  h2.hthree {
    position: relative;
    padding-bottom: 0.6rem;
    text-align: center;
  }

  h2.hthree::after {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    content: '';
    width: 1.1rem;
    height: 0.2rem;
    background: #0581ce;
  }

  h2.hone {
    position: relative;
    padding-left: 0.9rem;
  }

  h2.hone::before {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    content: '';
    width: 0.5rem;
    height: 0.6rem;
  }

  h2.hfour {
    position: relative;
    padding: 0.3rem 0.2rem;
    background-color: #0581ce;
    color: #fff;
    /* display: inline-block; */
    display: inline-flex;
    display: table;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    left: 50%;
    transform: translateX(-50%);
    /* line-height: 30px; */
  }

  h2.hfour::before {
    position: absolute;
    left: 0.2rem;
    top: 0.2rem;
    content: '';
    width: 100%;
    height: 100%;
    border: 0.05rem solid #0581CE;
    box-sizing: border-box;
  }

  h2.hfive {
    position: relative;
    padding: 0.3rem 0.55rem;
    background-color: #0581ce;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 0.2rem;
    display: inline-flex;
    display: table;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }

  p.tp_desc {
    margin-top: 0.6rem;
    margin-bottom: .6rem;
    text-align: center;
    font-size: 0.6rem;
    line-height: 1.2em;
    color: #999;
  }

  .old_text_box {
    position: relative;
    margin: 0;
  }

  img:not(.imageDiv img) {
    margin-bottom: 1.2rem;
  }

  .imageDiv {
    margin-bottom: 1.2rem;
  }

  .noMarginBottom {
    margin-bottom: 0;
  }

  .imageDelete, .imageEdit {
    display: none;
    visibility: hidden;
  }

  li p {
    display: inline;
  }

  ul, article ol, article li {
    list-style: none;
    padding: 0 !important;
    margin: 0 !important;
  }

  .quote_box, .link_box {
    position: relative;
    margin: 10px 0;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
    background: rgba(5, 129, 206, 0.04);
    border-radius: 4px;
    display: flex;
    align-items: center;
  }

  .link_box {
    line-height: 1;
    display: flex;
    align-items: center;
  }

  .link_box .link_icon {
    margin-right: 8px;
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/question/icon-lj.png') no-repeat center /
      cover;
  }

  .link_box i {
    font-style: normal;
    color: #279aee;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    text-decoration: underline;
  }

  .close {
    display: none !important;
  }

  textarea {
    font-size: 16px !important;
    line-height: normal !important;
    height: 20px !important;
    max-width: 100%;
    min-width: 100%;
    resize: none;
    white-space: nowrap;
    display: inline-block !important;
  }

  .imageDelete, .imageEdit, .close_btn, .progress_bar, .video_play_btn, .webModule, .close {
    display: none !important;
    visibility: hidden !important;
  }
}
