<template>
  <div
    :class="isFollowFlag ? 'product_share_btn_active' : ''" class="product_share_btn cursor"
    @click.stop='followHandler()'>
    <svg-icon :icon-class="isFollowFlag ? 'SubscribeDefaultBtn' : 'SubscribeDefaultBtn'" class-name="icons"/>
    <span>{{ isFollowFlag ? '已关注' : '关注' }}</span>
  </div>
</template>

<script>
export default {
  name: "SubscribeDefaultBtn",
  props: {
    isFollow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFollowFlag: this.isFollow
    }
  },
  watch: {
    '$store.state.auth.token'(value) {
      if (!this.$store.state.auth.token) {
        this.isFollowFlag = false
      }
    },
    isFollow(value) {
      this.isFollowFlag = this.isFollow
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (<PERSON>)
     * @date 2023-02-10 18:21
     * @description: 关注
     * ------------------------------------------------------------------------------
     */
    followHandler() {
      this.isFollowFlag = !this.isFollowFlag
      this.$emit('follow', this.id !== -1 ? this.id : null)
    }
  }
}
</script>

<style scoped lang="less">
.product_share_btn {
  width: 80px;
  height: 32px;
  border-radius: 4px;
  background: #0581CE;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFF;
  font-size: 14px;

  .icons {
    width: 15px;
    height: 15px;
    margin-right: 8px;
    color: #FFF;
  }

  &:hover {
    background: #46B0F2;
  }
}

.product_share_btn_active {

  .icons {
    color: white;
  }
}
</style>
