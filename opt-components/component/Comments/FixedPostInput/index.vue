<template>
  <div id="fixed_post_input_wrapper" class="fixed_post_input_wrapper">
    <div class="fixed_post_input_container">
      <div v-if="$store.state.auth.token" class="image">
        <zip-img
          :width="54"
          :height="54"
          :type-user="true"
          :src="userAvatarAddress"
          fill
        />
      </div>
      <div v-if="$store.state.auth.token" class="input_wrapper">
        <el-input
          id="PostInput"
          v-model.trim="postValue"
          type="textarea"
          placeholder="说点什么"
          resize="none"
          maxlength="150"
          @keyup.enter.native="postHandler"
        ></el-input>
        <div class="post_btn_b">
          <PostBtn @onClick="postHandler"/>
        </div>
      </div>

      <div v-else class="not_token">
        <span class="login_btn" @click="loginFun">登录</span>
        <span>发表你的评论</span>
      </div>
    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../index";
import PostBtn from "../PostBtn/index.vue";

export default {
  name: "FixedPostInput",
  components: {ZipImg, PostBtn},
  data() {
    return {
      postValue: ""
    }
  },
  computed: {
    userAvatarAddress() {
      return this.$store.state.auth.user.avatarAddress
    },
  },
  methods: {
    postHandler() {
      if (!this.postValue) {
        this.$toast('请输入评论内容!')
      } else {
        this.$emit('postHandler', this.postValue, value => {
          this.postValue = value;
        })
      }
    },
    // 跳转登录
    loginFun() {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
    },
  }
}
</script>

<style scoped lang="less">
.fixed_post_input_wrapper {
  position: sticky;
  bottom: 0;
  margin: 0 -24px -27px;
  display: none;


  padding: 18px 24px;
  background: #FFF;
  box-shadow: 0px -3px 25px 0px rgba(0, 0, 0, 0.04);
  //box-shadow: 0px -3px 25px 0px red;
  border-top: 1px solid #E3E5E7;
  transition: all .3s;

  .fixed_post_input_container {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;

    .input_wrapper {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .not_token {
      text-align: center;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #708AA2;

      .login_btn {
        margin-right: 8px;
        color: #0581CE;
        cursor: pointer;
      }
    }
  }

  .image {
    flex-shrink: 0;
    width: 54px;
    height: 54px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    margin-right: 16px;
  }

  /deep/ .el-textarea {
    width: calc(100% - 54px);
  }

  /deep/ .el-textarea__inner {
    height: 54px;
    border: none;
    background: #F4F6F8;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;

    &::placeholder {
      line-height: 34px;
    }

    &:focus {
      &::placeholder {
        line-height: normal;
      }
    }
  }

  .post_btn_b {
    //position: absolute;
    //top: 35px;
    //right: 0;
    margin-left: 16px;
  }
}
</style>
