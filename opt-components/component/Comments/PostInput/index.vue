<template>
  <div>
    <div id="post_input_wrapper" ref="post_input_wrapper" class="post_input_wrapper">
      <el-input
        id="PostInput"
        v-model.trim="postValue"
        type="textarea"
        placeholder="添加评论..."
        resize="none"
        maxlength="150"
        @keyup.enter.native="postHandler"
      ></el-input>
      <div class="comment_console">
        <div>
          <div v-if="!$store.state.auth.token" class="not_token">
            <span class="login_btn" @click="loginFun">登录</span>
            <span>发表你的评论</span>
          </div>
        </div>
        <PostBtn :value="postValue" v-if="$store.state.auth.token" @onClick="postHandler"/>
      </div>
    </div>
  </div>
</template>

<script>
import PostBtn from "../PostBtn/index.vue";

export default {
  name: "PostInput",
  components: {PostBtn},
  data() {
    return {
      postValue: "",
      bannerIO: null,
      commentListVisible: false
    }
  },
  mounted() {
    const dom = this.$refs.post_input_wrapper
    const listDom = document.getElementById('comment_list_container')

    const fixedDom = document.getElementById('fixed_post_input_wrapper')
    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {

        if (entry.isIntersecting && entry.target.id === "comment_list_container") {
          this.commentListVisible = true
        } else if (!entry.isIntersecting && entry.target.id === "comment_list_container") {
          this.commentListVisible = false
        }

        if (!entry.isIntersecting && entry.target.id === "post_input_wrapper") {
          if (this.commentListVisible) {
            fixedDom.style.display = 'block'
            // eslint-disable-next-line no-undef
            setTimeout(() => {
              fixedDom.style.opacity = '1'
            }, 100)
          }
        } else {
          fixedDom.style.opacity = '0'
          // eslint-disable-next-line no-undef
          setTimeout(() => {
            fixedDom.style.display = 'none'
          }, 100)
        }
      })
    })

    this.bannerIO.observe(dom);
    this.bannerIO.observe(listDom);
  },
  beforeDestroy() {
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  },
  methods: {
    // 跳转登录
    loginFun() {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
    },
    postHandler() {
      if (!this.postValue) {
        this.$toast('请输入评论内容!')
      } else {
        this.$emit('postHandler', this.postValue, value => {
          this.postValue = value;
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
