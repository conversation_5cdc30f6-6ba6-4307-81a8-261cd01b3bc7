<template>
  <div id="comment_list_container" class="comment_list_container">
    <ListItem
      v-for="(item) in list"
      :id="item.id"
      :key="item.id"
      :avatar-address="item.creator.avatarAddress"
      :real-name="item.creator.realName"
      :text="item.text"
      :creation-date="item.creationDate"
      :is-digg="typeof item.isDigg === 'string' ? item.isDigg === 'true' : item.isDigg"
      :diggs="item.diggs"
      :child-comments="item.childComments"
      :creator-id="item.creator.id"
      :identity="item.creator.identity"
      :child-comments-count="item.childCommentsCount"
      v-on='$listeners'
    />
  </div>
</template>

<script>
import ListItem from "./ListItem/index.vue";

export default {
  name: "CommentList",
  components: {
    ListItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    console.log(this.list, "格式化的数据")
  }
}
</script>

<style scoped lang="less">
.comment_list_container {
  margin-bottom: 15px;
}
</style>
