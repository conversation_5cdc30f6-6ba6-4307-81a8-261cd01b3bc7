<template>
  <div class="comment_child_list_item_container">
    <div v-if="identity === 3" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="childAvatar"
      />
    </div>
    <a v-else target="_blank" :href="`/user-center?profileUserId=${childId}`" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="childAvatar"
      />
    </a>
    <div class="item_content">
      <div class="user_name">
        <span>{{ identity === 3 ? '匿名用户（非医务工作者）' : childName }}</span>
        <span v-if="parent" class="user_reply">@ {{ parent }} :</span>
      </div>
      <div class="comment_text">
        {{ text }}
      </div>
      <div class="comment_console">
        <span class="time">{{ childCreationDate }}</span>
        <div class="operate">
          <div class="like" :class="isLike ? 'active_like' : ''" @click="diggHandler">
            <svg-icon icon-class="comment_zan-2" class-name="icon"/>
            <span>{{ likes }}</span>
          </div>
          <div class="like" @click="$emit('childReply','')">
            <svg-icon icon-class="comment-reply-2" class-name="icon"/>
            <span>回复</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../../index";

export default {
  name: "ChildListItem",
  components: {ZipImg},
  props: {
    id: {
      type: Number,
      default: null
    },
    childId: {
      type: Number,
      default: null
    },
    childAvatar: {
      type: String,
      default: ""
    },
    childName: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    },
    childCreationDate: {
      type: String,
      default: ""
    },
    diggs: {
      type: Number,
      default: 0
    },
    isDigg: {
      type: Boolean,
      default: false
    },
    parent: {
      type: String,
      default: ""
    },
    identity: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isLike: this.isDigg,
      likes: this.diggs
    }
  },
  methods: {
    diggHandler() {
      this.isLike = !this.isLike;
      this.isLike ? this.likes += 1 : this.likes -= 1
      this.$emit('likeHandler', this.id, (isSuccess) => {
        if (!isSuccess) {
          if (this.isLike) {
            this.isLike = false;
            this.likes -= 1
          }
        }
      });
    },
    replyHandler() {

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
