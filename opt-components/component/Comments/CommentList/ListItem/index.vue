<template>
  <div class="comment_list_item_container">
    <div v-if="identity === 3" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="avatarAddress"
      />
    </div>
    <a v-else target="_blank" :href="`/user-center?profileUserId=${creatorId}`" class="user_image">
      <ZipImg
        :width="40"
        :height="40"
        fill
        :type-user="true"
        :src="avatarAddress"
      />
    </a>
    <div class="item_content">
      <div class="user_name">
        <span>{{ identity === 3 ? '匿名用户（非医务工作者）' : realName }}</span>
      </div>
      <div class="comment_text">
        {{ text }}
      </div>
      <div class="comment_console">
        <span class="time">{{ creationDate }}</span>
        <div class="operate">
          <div class="like" :class="isLike ? 'active_like' : ''" @click="diggHandler">
            <svg-icon icon-class="comment_zan-2" class-name="icon"/>
            <span>{{ likes }}</span>
          </div>
          <div class="like" @click="replyHandler">
            <svg-icon icon-class="comment-reply-2" class-name="icon"/>
            <span>回复</span>
          </div>
        </div>
      </div>

      <div v-if="childComments.length>0" class="child_list_wrapper">
        <ChildListItem
          v-for="(item,index) in newChildComments"
          v-show="showMore === false ? index < 2 : true"
          :id="item.id"
          :key="item.id"
          :child-avatar="item.childAvatar"
          :child-name="item.childName"
          :text="item.text"
          :child-creation-date="item.childCreationDate"
          :diggs="item.diggs"
          :is-digg="typeof item.isDigg === 'string' ? item.isDigg === 'true' : item.isDigg"
          :parent="item.parent"
          :child-id="item.childId"
          :identity="item.identity"
          @childReply="childReplyHandler(item.childName,item.id)"
          v-on='$listeners'
        />

        <div v-if="childCommentsCount > 2 && !showMore" class="show_more" @click="showMoreHandler">
          <i v-if="loading" class="el-icon-loading"></i>
          <span v-else>{{ `共${childCommentsCount}条回复 点击展开` }}</span>

        </div>


        <div v-if="showMore" class="pagination_wrapper" style="display: flex;align-items: center;flex-wrap: wrap">
          <el-pagination
            @current-change="handleCurrentChange"
            :page-size="4"
            :hide-on-single-page="true"
            layout="total, prev, pager, next"
            prev-text="上一页"
            next-text="下一页"
            :total="childCommentsCount">
          </el-pagination>
          <div class="sq" @click="showMore = false"
               style="color: #666;font-size: 14px;margin-left: 15px;cursor: pointer">收起
          </div>
        </div>

      </div>

      <transition name="el-fade-in">
        <div v-show="replyId" :style="{marginTop: '20px'}">
          <ReplyInput
            :id="id"
            :reply-name="replyName"
            @replyPostHandler="replyPostHandler"
            v-on='$listeners'
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import ReplyInput from "../../ReplyInput/index.vue";
import {ZipImg} from "../../../index";
import ChildListItem from "../ChildListItem/index.vue";

export default {
  name: "ListItem",
  components: {ChildListItem, ZipImg, ReplyInput},
  props: {
    id: {
      type: Number,
      default: null
    },
    creatorId: {
      type: Number,
      default: null
    },
    avatarAddress: {
      type: String,
      default: ""
    },
    realName: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    },
    creationDate: {
      type: String,
      default: ""
    },
    diggs: {
      type: Number,
      default: 0
    },
    isDigg: {
      type: Boolean,
      default: false
    },
    childComments: {
      type: Array,
      default: () => []
    },
    childCommentsCount: {
      type: Number,
      default: 0
    },
    identity: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      showMore: null,
      replyId: null,
      replyName: "",
      isLike: this.isDigg,
      likes: this.diggs,
      newChildComments: this.childComments || []
    }
  },
  methods: {
    handleCurrentChange(current) {
      this.$emit('getChildComment', {parentCommentId: this.id, pageNo: current}, (res) => {

        this.newChildComments = res.list.map(itemChild => {
          return {
            id: itemChild.id,
            childId: itemChild.commentator.id,
            childName: itemChild.commentator.realName,
            childAvatar: itemChild.commentator.avatarAddress,
            childCreationDate: this.timeStamp.timestampFormat(Date.parse(itemChild.commentTime) / 1000),
            diggs: itemChild.diggs,
            isDigg: JSON.stringify(itemChild.diggStatus === 'T'),
            text: itemChild.text,
            parent: itemChild.parentUser.realName,
            identity: itemChild.commentator.identity
          }
        })
      })
    },
    showMoreHandler() {
      this.loading = true;

      this.$emit('getChildComment', {parentCommentId: this.id, pageNo: 1}, (res) => {

        this.newChildComments = res.list.map(itemChild => {
          return {
            id: itemChild.id,
            childId: itemChild.commentator.id,
            childName: itemChild.commentator.realName,
            childAvatar: itemChild.commentator.avatarAddress,
            childCreationDate: this.timeStamp.timestampFormat(Date.parse(itemChild.commentTime) / 1000),
            diggs: itemChild.diggs,
            isDigg: JSON.stringify(itemChild.diggStatus === 'T'),
            text: itemChild.text,
            parent: itemChild.parentUser.realName,
            identity: itemChild.commentator.identity
          }
        })
        this.showMore = true
        this.loading = false
      })
    },
    diggHandler() {
      this.isLike = !this.isLike;
      this.isLike ? this.likes += 1 : this.likes -= 1
      this.$emit('likeHandler', this.id, (isSuccess) => {
        if (!isSuccess) {
          if (this.isLike) {
            this.isLike = false;
            this.likes -= 1
          }
        }
      });
    },
    replyHandler() {
      this.replyName = this.realName;
      this.replyId = this.replyId === this.id ? null : this.id;


      // eslint-disable-next-line no-undef
      setTimeout(() => {
        if (this.replyId) {
          window.document.getElementById(`replyInput-${this.id}`).focus()
        }
      }, 100)
    },
    replyPostHandler(value, backFn) {
      if (!value) {
        this.$toast('请输入评论内容!')
      } else {
        this.$emit('pushHandler', {info: value, parentId: this.replyId}, (result) => {
          if (result) {
            const newArr = {
              id: result.id,
              childId: result.commentator.id,
              childName: result.commentator.realName,
              childAvatar: result.commentator.avatarAddress,
              childCreationDate: this.timeStamp.timestampFormat(Date.parse(result.commentTime) / 1000),
              diggs: result.diggs,
              isDigg: false,
              text: result.text,
              parent: this.newChildComments.filter(item => item.id === this.replyId).length > 0 ? this.replyName : "",
            }
            this.newChildComments.unshift(newArr)
            backFn("")
            this.replyId = null;
          }
        })
      }
    },
    childReplyHandler(name, id) {
      this.replyName = name;
      this.replyId = this.replyId === id ? null : id;


      // eslint-disable-next-line no-undef
      setTimeout(() => {
        if (this.replyId) {
          window.document.getElementById(`replyInput-${this.id}`).focus()
        }
      }, 100)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
