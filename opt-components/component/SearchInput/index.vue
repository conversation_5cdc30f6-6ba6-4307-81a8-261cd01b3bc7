<template>
  <div :style="{width:`${width}px`}" class="search_input_wrapper_component">
    <el-popover
      v-model="visible"
      placement="bottom"
      :visible-arrow="false"
      :width="width"
      trigger="manual"
      popper-class="search_input_wrapper_component_popover"
    >
      <div v-show="historyList.length>0" class="search_history_wrapper">
        <div class="search_history_title">
          <div class="name">搜索历史</div>
          <div class="clear cursor" @click="clearHistory">
            <svg-icon icon-class="search_delete" class-name="icons"/>
            <span>清空</span>
          </div>
        </div>
        <div class="search_history_content">
          <div
            v-for="(item,index) in historyList"
            :key="index"
            class="search_history_item cursor"
            :style="{maxWidth: '132px'}"
            @click="historyJumpHandler(item.name)">
            <span class="text-limit-1">{{ item.name }}</span>
          </div>
        </div>
      </div>

      <div slot="reference" class="search_input_wrapper">
        <div class="search_input">
          <el-input
            v-model="searchValue"
            placeholder="输入关键词搜索"
            @keyup.enter.native="searchHandler"
            @blur="visible = false;"
            @focus="focusHandler"
            @change="changeInput"
          >
            <div slot="append" class="search_btn_wrapper">
              <svg-icon v-show="searchValue" class-name='search_clear_svg cursor' icon-class='search_clear_svg'
                        @click="clearSearchValue"/>
              <div class="search_btn cursor" @click="searchHandler">
                <svg-icon class-name='icons' icon-class='search '/>
              </div>
            </div>
          </el-input>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: "SearchInput",
  props: {
    width: {
      type: Number,
      default: 360
    },
    searchHistoryKey: {
      type: String,
      default: ""
    },
  },
  data() {
    return {
      searchValue: "",
      visible: false,
      historyList: []
    }
  },
  mounted() {
    this.getHistoryHandler()
  },
  methods: {
    changeInput(value) {
      this.$emit('syncInputValue', value)
    },
    clearSearchValue() {
      this.searchValue = "";
      this.$emit('syncInputValue', "")
    },
    searchHandler() {
      if (!this.searchHistoryKey) {
        this.$toast("请输入历史key")
        return;
      }

      const value = this.searchValue

      if (value) {
        let historyArr = JSON.parse(window?.localStorage.getItem(`brainmed_history_search_${this.searchHistoryKey}`) || "[]") || []
        historyArr.unshift({name: value})

        const uniqueArr = historyArr.filter((item, index, self) =>
          index === self.findIndex(obj => JSON.stringify(obj) === JSON.stringify(item))
        );

        historyArr = uniqueArr.splice(0, 10)
        window.localStorage.setItem(`brainmed_history_search_${this.searchHistoryKey}`, JSON.stringify(historyArr))
      }

      this.$emit('searchHandler', value)
    },
    getHistoryHandler() {
      const searchForTips = window.localStorage.getItem(`brainmed_history_search_${this.searchHistoryKey}`)

      if (searchForTips) {
        const data = JSON.parse(searchForTips)
        this.historyList = data
      }
    },
    clearHistory() {
      this.historyList = [];
      window.localStorage.setItem(`brainmed_history_search_${this.searchHistoryKey}`, JSON.stringify([]))
    },
    historyJumpHandler(name) {
      this.searchValue = name;
      this.$emit('searchHandler', name)
    },
    focusHandler() {
      this.getHistoryHandler()

      if (this.historyList.length > 0) {
        this.visible = true
      }
    },
  }
}
</script>

<style>
.search_input_wrapper_component_popover {
  padding: 12px;
  box-sizing: border-box;

  .search_history_wrapper {
    margin-bottom: 12px;

    .search_history_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .name {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .clear {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999EA4;

        .icons {
          width: 15px;
          height: 15px;
          margin-right: 4px;
        }
      }
    }

    .search_history_content {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .search_history_item {
        flex-shrink: 0;
        height: 24px;
        border-radius: 4px;
        background: #F4F6F8;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 4px;
        color: #0581CE;
        font-size: 14px;
      }
    }
  }
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
