<template>
  <img
    style="object-fit: cover;background: #d5d5d5;display: block"
    :style="fill ? {position:'absolute',left:0,top:0,width:'100%',height:'100%'} : {width: width ? width +'px' : '100%',height: height ? height +'px' :'100%'}"
    :src="srcDir"
    :title="alt"
    :alt="alt"
    @click="$emit('click')"
  >
</template>

<script>
export default {
  name: "ZipImg",
  props: {
    width: {
      type: Number,
      default: null
    },
    height: {
      type: Number,
      default: null
    },
    alt: {
      type: String,
      default: ""
    },
    src: {
      type: String,
      default: ""
    },
    typeUser: {
      type: Boolean,
      default: false
    },
    fill: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    srcDir() {
      if (this.src && this.width && this.height) {
        return this.$tool.compressImg(this.src, this.width, this.height)
      }

      if (!this.src && !this.typeUser) {
        // eslint-disable-next-line no-undef,import/no-absolute-path
        return require("/assets/images/default1.png")
      }

      if (!this.src && this.typeUser) {
        // eslint-disable-next-line no-undef,import/no-absolute-path
        return require("/assets/images/user.png")
      }


      return this.src
    }
  }
}
</script>

<style scoped lang="less">

</style>
