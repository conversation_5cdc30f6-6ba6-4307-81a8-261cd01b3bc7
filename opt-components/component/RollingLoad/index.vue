<template>
  <div class="dropdown_loading_wrapper">
    <template>
      <div style="position: relative;">
        <slot></slot>
        <div
          ref="targetDom"
          style="width: 300px;height: 30px;position: absolute;bottom: 100px;opacity: 0;z-index: -1"></div>
      </div>
    </template>

    <template v-if="loading">
      <slot name="loading"></slot>
      <div v-if="!hasLoading" class="content">
        <div class="point1"></div>
        <div class="point2"></div>
        <div class="point3"></div>
      </div>
    </template>

    <template v-else-if="!loading && empty">
      <slot v-if="hideEmptyTips" name="emptyTipsSlot"/>
      <EmptyData v-else :height="emptyHeight"/>
    </template>

    <template v-else-if="!loading && noMore">
      <div style="height: 100px;line-height:100px;text-align: center;color: #888888;user-select: none">
        {{ emptyTipsValue }}
      </div>
    </template>
  </div>
</template>

<script>
import EmptyData from "../../../components/optimize-components/UI/EmptyData/index.vue";

export default {
  name: "RollingLoad",
  components: {EmptyData},
  props: {
    customLoading: {
      type: Boolean,
      default: false
    },
    emptyTipsValue: {
      type: String,
      default: "到底了"
    },
    hideEmptyTips: {
      type: Boolean,
      default: false
    },
    emptyHeight: {
      type: String,
      default: "30vh"
    },
    loading: {
      type: Boolean,
      default: false
    },
    noMore: {
      type: Boolean,
      default: false
    },
    empty: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      bannerIO: null
    }
  },
  computed: {
    hasLoading() {
      return this.$slots.loading !== undefined;
    }, // 主要看这一部分即可
  },
  watch: {
    loading(newValue) {
      if (newValue === false && this.noMore === false) {
        this.$nextTick(() => {
          const targetDom = this.$refs.targetDom
          this.bannerIO?.observe(targetDom);
        })
      } else {
        this.$nextTick(() => {
          this.bannerIO?.disconnect()
        })
      }
    }
  },
  mounted() {

    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          if (!this.noMore) {
            this.$emit("hit-bottom", true)
          }

        }
      })
    });

  },
  beforeDestroy() {
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  }
}
</script>

<style scoped lang="less">
.content {
  margin: 15% auto;
  width: 150px;
  text-align: center;
}

.content > div {
  width: 20px;
  height: 20px;
  background-color: var(--theme-color);
  border-radius: 50%;
  display: inline-block;
  animation: action 1.5s infinite ease-in-out;
  animation-fill-mode: both;
}

.content .point1 {
  animation-delay: -0.3s;
}

.content .point2 {
  animation-delay: -0.1s;
}

@keyframes action {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}
</style>
