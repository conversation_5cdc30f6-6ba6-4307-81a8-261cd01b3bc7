/deep/ .el-dialog {
  width: 450px !important;
  border-radius: 6px;
  padding: 30px;
  max-height: 50%;
  overflow-y: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  margin-top: 0vh !important;

  &::-webkit-scrollbar {
    display: none;
  }
}

/deep/ .el-dialog__header {
  display: none;
}

/deep/ .el-dialog__body {
  padding: 0;
}

/deep/ .closeButton {
  position: absolute;
  right: 12px;
  top: 12px;
  width: 24px;
  height: 24px;
}

.share_content_box {
  .share_content_left {
    width: 50%;

    .shart_weixin_box {
      width: 140px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .share_content_right {
    width: 50%;
    position: relative;
    padding-left: 30px;

    &::after {
      display: block;
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      height: 100%;
      background: #DBE5EB;
    }

    .share_buttonList {
      margin: 14px 0 20px;

      .share_box {
        img {
          width: 38px;
          height: 38px;
          margin-right: 14px;
        }

        .share_button {
          width: 90px;
          line-height: 38px;
          text-align: center;
          border-radius: 19px;
          font-size: 12px;
          border: 1px solid #EAEAEA;

          .lianjie_share {
            width: 15px;
            height: 15px;
            margin-right: 7px;
          }
        }
      }
    }

    .share_content_right_info {
      color: #848484;
      display: block;
      font-size: 12px;
    }
  }
}
