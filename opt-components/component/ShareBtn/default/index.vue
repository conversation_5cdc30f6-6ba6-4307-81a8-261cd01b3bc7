<template>
  <div>
    <div class="product_share_btn cursor" @click="shareFlag = true">
      <svg-icon icon-class="share_btn" class-name="icons"/>
      <span>分享</span>
    </div>
    <client-only>
      <Share
        :share-desc='shareDesc'
        :share-images='shareImages'
        :share-flag='shareFlag'
        @editFlag='shareOpenFun'/>
    </client-only>
  </div>
</template>

<script>
import Share from "../ShareDialog/index.vue";

export default {
  name: "ProductShareButton",
  components: {Share},
  props: {
    shareDesc: {
      type: String,
      default: ''
    },
    shareImages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      shareFlag: false
    }
  },
  methods: {
    shareOpenFun() {
      this.shareFlag = !this.shareFlag
    }
  }
}
</script>

<style scoped lang="less">
.product_share_btn {
  width: 78px;
  height: 30px;
  border: 1px solid #0581CE;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0581CE;
  font-size: 14px;

  .icons {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: #0581CE;
  }
  &:hover{
    border: 1px solid #46B0F2;
    color: #46B0F2;
    .icons{
      color: #46B0F2;
    }
  }
}
</style>
