
footer {
  background-color: #344A61;
  padding: 40px 0;
  background-image: url("assets/images/footer/bg_color_1.jpg");
  background-repeat: no-repeat;
  background-size: cover;


  .footer_container {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;


    .footer_content_left {
      flex-shrink: 0;
      width: 876px;

      .footer_content_nav {
        margin-bottom: 34px;
      }

      .web_tips {
        p, a {
          line-height: 18px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.50);

          &:hover {
            color: rgba(255, 255, 255, 1)!important;
          }
        }
      }

      .nav_list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px 25px;

        .nav_item {
          display: flex;
          align-items: center;
          color: #EFFAFF;
          font-size: 16px;
          font-weight: 400;

          .icon_wrapper {
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            margin-right: 8px;
          }


          &:hover {
            color: #EFFAFF;
            opacity: 0.6;
          }
        }
      }
    }

    .footer_content_right {
      flex-shrink: 0;
      width: 258px;

      .footer_qr_code_wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 24px;

        .footer_qr_code_item {
          width: 70px;
          height: 70px;
          border-radius: 4px;
          background: #FFF;
          position: relative;
          padding: 4px;
          box-sizing: border-box;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.10);
            transition: opacity .2s ease;
          }

          .img_item {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .qr_code_fixed {
            position: absolute;
            top: -172px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 120px;
            border-radius: 8px;
            background: #FFF;
            transition: top .2s ease;
            opacity: 0;
            z-index: -1;
            padding: 5px;

            &::before {
              content: "";
              display: block;
              position: absolute;
              bottom: -24px;
              left: 50%;
              transform: translateX(-50%);
              border: 16px solid transparent;
              border-top-color: #fff;
            }

            .img_item{
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          &:hover {
            &::before {
              opacity: 0;
            }

            .qr_code_fixed {
              z-index: 10;
              top: -144px;
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
