<template>
  <footer>
    <div class="footer_container">
      <div class="footer_content_left">
        <div class="footer_content_nav">
          <ul class="nav_list">
            <template v-for="(item,index) in navList">
              <li v-if="item.isEnable" :key="index">
                <a target="_self" :href="item.url" class="nav_item">
                  <div class="icon_wrapper">
                    <svg-icon
                      :icon-class="item.icon"
                      :style="{width:item.iconSize,height:item.iconSize}"
                      class-name="icons"/>
                  </div>
                  <span>{{ item.name }}</span>
                </a>
              </li>
            </template>

          </ul>
        </div>
        <div class="web_tips">
          <p style="margin-bottom: 4px">Copyright © 2014-2025 Brainmed All Rights Reserved. </p>
          <p style="margin-bottom: 6px">
            <a target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011202014644">
              沪ICP备14035871号-3 | 沪网审 [2014-08-26] 版权归属上海脑医汇数字科技有限公司
            </a>
          </p>
          <div>
            <a
              target='_blank'
              href='http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011202014644'
              style='display:inline-block;text-decoration:none;height:20px;line-height:20px;'>
              <img src='~assets/images/put_on_record.png' style='float:left;'/>
              <p style='float:left;height:20px;line-height:20px;margin: 0px 0px 0px 5px; color:rgba(255, 255, 255, 0.5);'>
                沪公网安备 31011202014644号
              </p>
            </a>
          </div>
        </div>
      </div>
      <div class="footer_content_right">
        <div class="footer_qr_code_wrapper">
          <div v-for="(item,index) in qrCodeList" :key="index" class="footer_qr_code_item">
            <img :src="item.url" alt="brainmed-logo" class="img_item">
            <div class="qr_code_fixed">
              <div
                class="img_item"
                :style="{background:`url(${require('/assets/images/footer/footer-logo.png')})`,backgroundPosition:item.position}">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import shenwai from '@/assets/images/footer/shenwai.png'
import shenjie from '@/assets/images/footer/shenjie.png'
import naoyi from '@/assets/images/footer/naoyi.png'
import shennei from '@/assets/images/footer/shennei.png'
import arbrain from '@/assets/images/footer/aibrainn.png'
import logo from '@/assets/images/footer/logo-new.jpg'

export default {
  name: "FooterNav",
  data() {
    return {
      qrCodeList: [
        {url: shenwai, position: "0 0"},
        {url: shenjie, position: "-130px 0"},
        {url: naoyi, position: "-260px 0"},
        {url: shennei, position: "-390px 0"},
        {url: arbrain, position: "-520px 0"},
        {url: logo, position: "-650px 0"},
      ]
    }
  },
  computed: {
    navList() {
      return this.$store.state.global.footerNav
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
