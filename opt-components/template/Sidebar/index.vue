<template>
  <div class="sidebar_wrapper">
    <!-- 脑医汇App -->
    <div v-if="isAppShow" class="brainmed_app">
      <p class="brainmed_content">
        <span>脑医汇</span>
        <span>App<svg-icon icon-class="nao_app" class-name="icons"/></span>
      </p>
      <div class="qr_code">
        <img src="~assets/images/app_code.png" alt=""/>
      </div>
    </div>
    <!--返回顶部按钮-->
    <Backtop :right="30">
      <div class="back_top_wrapper">
        <svg-icon icon-class="back-top_svg" class-name="icons "/>
      </div>
    </Backtop>
  </div>
</template>

<script>
import {Backtop} from "element-ui";

export default {
  name: "HomeSidebar",
  components: {
    Backtop
  },
  props: {
    isAppShow: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
