.sidebar_wrapper{
  /deep/ .el-backtop {
    width: 50px;
    height: 50px;
  }

  .back_top_wrapper {
    width: 100%;
    height: 100%;
    background: #FDFEFE;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.2);

    .icons {
      width: 100%;
      height: 100%;
    }
  }

  .brainmed_app{
    z-index: 999;
    position: fixed;
    right: 30px;
    bottom:110px;
    width: 50px;
    height: 50px;
    color: #0581CE;
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #DEF2FF 0%, #FBFDFF 100%);
    cursor: pointer;
    &:hover{
      .qr_code{
        display: block;
      }
    }

    .qr_code{
      display: none;
      position: absolute;
      right: calc(100% + 10px);
      top: 50%;
      transform: translateY(-50%);
      width: 100px;
      height: 100px;
      padding: 5px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      img{
        width: 100%;
        height: 100%;
        display: block;
      }
    }

    .brainmed_content{
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      span{
        line-height: 19px;
      }
      .icons{
        width: 8px;
        height: 11px;
        margin-left: 2px;
      }
    }

  }
}
