<template>
  <div class="opt-page-container" :style="styles">
    <div class="left_container">
      <slot name="left"></slot>
    </div>
    <div class="right_container">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "ContainerVideoPage",
  props: {
    styles: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style scoped lang="less">
.opt-page-container {
  width: 1235px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  box-sizing: content-box;

  .left_container {
    width: 840px;
  }

  .right_container {
    width: 369px;
    margin-left: 26px;
  }

}
</style>
