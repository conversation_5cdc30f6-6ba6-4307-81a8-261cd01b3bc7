<template>
  <div id="nav_en_btn" class="en_btn_wrapper">
    <a
      :href="enHref"
      target="_self"
      class="en_btn">EN</a>
  </div>
</template>

<script>
export default {
  name: "EnBtn",
  computed: {
    enHref() {
      const token = this.$store.state.auth.token
      // eslint-disable-next-line no-undef
      return process.env.NODE_ENV === 'production' ?
        `https://en.brainmed.com${token ? '?token=' + token : ''}`
        :
        `https://endev.brainmed.com${token ? '?token=' + token : ''}`
    }
  }
}
</script>

<style scoped lang="less">
.en_btn_wrapper {
  flex-shrink: 0;

  .en_btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.90);

    &:hover {
      border: 1px solid rgba(90, 192, 255, 0.40);
      background: #279AE2;
      color: rgba(255, 255, 255, 0.70);
    }
  }
}
</style>
