<template>
  <div id="nav_login" class="user_login_status">
    <el-popover
      v-if="$store.state.auth.token"
      :visible-arrow="false"
      placement="bottom-end"
      popper-class="nav-popver-header-top"
      trigger="hover"
      @show="infoLoadingShow = !infoLoadingShow"
    >
      <UserPopUp
        :info-loading-show="infoLoadingShow"
        :user-avatar-address="userAvatarAddress"/>
      <div slot="reference" class="user_image">
        <img
          v-if="userAvatarAddress"
          :src="userAvatarAddress"
          alt="userAvatarAddress"
          class="img_cover"
          @click="jumpPageHandler"/>
        <svg-icon
          v-else
          class-name="icons"
          icon-class="signinavatar"
          @click="jumpPageHandler"/>
      </div>
    </el-popover>
    <div v-else class="not_logged">
      <span @click="loginFun">登录</span>
    </div>
  </div>
</template>

<script>
import UserPopUp from '@/components/PageComponents/Nav/UserPopUp/UserPopUp'

export default {
  name: "UserLoginStatus",
  components: {
    UserPopUp
  },
  data() {
    return {
      infoLoadingShow: false,
    }
  },
  computed: {
    userAvatarAddress() {
      return this.$store.state.auth.user.avatarAddress
    },
  },
  methods: {
    // 登录跳转
    loginFun() {
      // 改变登录状态
      window.localStorage.setItem('conditionChange', true)
      /**
       * 如果已经登录了 就刷新页面
       */
      if (this.$cookies.get('medtion_token_only_sign')) {
        window.location.reload()
      } else {
        this.$analysys.btn_click('登录', document.title)
        if (
          this.$route.path === '/personaldata' ||
          this.$route.name === 'index-user-center' ||
          this.$route.name === 'index-browsing-history'
        ) {
          this.$router.push('/')
          this.$toast('正在退出..')
        } else {
          this.$store.commit('editBackUrl', window.location.href)
          this.$router.push({
            name: 'signin',
            query: {
              fallbackUrl: this.$route.fullPath,
              source: this.$route.name === 'index-meeting-detail' && (this.$route.query.id === '2903' || this.$route.query.id === '3114') ? 'meeting_3114' : null
            },
          })
        }
      }
    },
    jumpPageHandler() {
      window.location.href = `/user-center?profileUserId=${this.$store.state.auth.user.id}`
    },
  }
}
</script>

<style scoped lang="less">
.user_image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;

  .icons {
    width: 100%;
    height: 100%;
  }
}

.user_login_status {
  flex-shrink: 0;
  width: 32px;
}

.not_logged {
  cursor: pointer;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.90);
  font-weight: 500;
}
</style>
