<template>
  <div id="nav_release" class="release_wrapper">
    <div
      class="release_btn"
      @click="publishHandler">
      <svg-icon class-name="icons" icon-class="release_nav"/>
      <span style="line-height: 14px">发布</span>
    </div>
    <!-- 企业用户点击发布显示发需求和回答 -->
    <CorporateIdentityPublishArticle v-if="isCorporateIdentity" :visible="publishArticleVisible" @showHiddenFn="showHiddenFn"/>
    <!-- 其他用户按照正常逻辑 -->
    <PublishArticle v-else :visible="publishArticleVisible" @showHiddenFn="showHiddenFn"/>
  </div>
</template>

<script>
import CorporateIdentityPublishArticle from '@/components/PageComponents/Nav/CorporateIdentityPublishArticle'
import PublishArticle from '@/components/PageComponents/Nav/PublishArticle/PublishArticle'

export default {
  name: "ReleaseBtn",
  components: {
    PublishArticle,
    CorporateIdentityPublishArticle
  },
  data() {
    return {
      publishArticleVisible: false,
      isCorporateIdentity: false, // 企业用户身份 发布内容：true:企业身份 false:其他身份
    }
  },
  methods: {
    publishHandler() {
      this.publishArticleVisible = true
      this.$analysys.btn_click('发布', '脑医汇')
    },
    showHiddenFn(flag) {
      this.publishArticleVisible = flag
    },
    // 判断身份
    setUserIdentity(){
      // const localToken = this.$cookies.get('medtion_token_only_sign')
      // const isLogged = this.$store.state.auth.isLogged
      // if (localToken || isLogged) {
      //   // 判断登录
      //   this.$axios.$request(loginByToken({
      //     token: localToken
      //   })).then(response => {
      //     this.$axios.$request(userInfo()).then(res => {
      //       if (res && res.code === 1 && res.result.identity === 4) {
      //         this.isCorporateIdentity = true
      //       }
      //     })
      //   })
      // }
      const localToken = this.$cookies.get('medtion_token_only_sign')
      const isLogged = this.$store.state.auth.isLogged
      if (localToken || isLogged) {
        if(this.$store.state.auth.user.identity === 4){
          this.isCorporateIdentity = true
        }else{
          this.isCorporateIdentity = false
        }
      }else{
        this.isCorporateIdentity = false
      }
    }
  },
  mounted(){
    this.setUserIdentity();
  }
}
</script>

<style scoped lang="less">
.release_wrapper {
  .release_btn {
    width: 78px;
    height: 32px;
    border-radius: 20px;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0581CE;

    font-size: 14px;
    font-weight: 500;
    cursor: pointer;

    .icons {
      width: 15px;
      height: 14px;
      color: #0581CE;
      margin-right: 5px;
    }

    &:hover {
      color: #46B0F2;

      .icons {
        color: #46B0F2;
      }
    }
  }
}
</style>
