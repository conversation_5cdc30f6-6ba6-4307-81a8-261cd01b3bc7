<template>
  <div id="search_and_release_content" class="search_and_release_wrapper">
    <div id="nav_search_input" class="search_input_content" :style="isHighlight? {width:'100%'} : {}">
      <el-popover
        v-model="visible"
        placement="bottom-end"
        :visible-arrow="false"
        width="460"
        trigger="manual"
        @show="showTipsHandler"
      >
        <SearchPrompt
          ref="SearchPrompt"
          :search-value="searchValue"
          :is-advanced-search-show="true"
          @show="showTipsHandler"
          @closePopUp="(flag) => visible = flag"
        />
        <div slot="reference" class="search_input_wrapper" :class="isHighlight ? '' : 'search_input_active_wrapper'">
          <el-input
            ref="searchInput"
            v-model="searchValue"
            :placeholder="searchCueWord.length>0 ? searchCueWord[cueWordIndex].name : ''"
            clearable
            @keyup.enter.native="searchHandler"
            @focus="focusHandler"
          >
            <div slot="append" class="search_btn_wrapper">
              <div class="search_btn" :class="isHighlight ? 'search_active_btn' : ''" @click="searchHandler">
                <svg-icon class-name='icons' icon-class='search'/>
              </div>
            </div>
          </el-input>
        </div>
      </el-popover>
    </div>
    <slot name="right"></slot>
  </div>
</template>

<script>
import {SearchPrompt} from "../../../../../components/optimize-components/page-components/search";
import {hots} from "@/api/search";
// import {getWebApiPersonalWebsite} from "../../../../../api/lucky";
export default {
  name: "SearchInput",
  components: {
    SearchPrompt
  },
  data() {
    return {
      isHighlight: false,
      visible: false,
      searchValue: "",
      searchCueWord: [],
      cueWordIndex: 0,
      timer: null,
    }
  },
  mounted() {
    (() => {
      const dictIdStr = ""
      this.$axios.$request(hots({
        dictIdStr
      })).then(res => {
        if (res.code === 1) {
          this.searchCueWord = res.list;
          this.cueWordHandler()
        }
      })
    })()

    window.addEventListener('click', this.handleMouseUp, false);
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleMouseUp, false)
    if (this.timer) {
      // eslint-disable-next-line no-undef
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    cueWordHandler() {
      const inputDom = this.$refs.searchInput?.$el?.querySelector('input')
      // eslint-disable-next-line no-undef
      this.timer = setInterval(() => {
        if ((this.cueWordIndex + 1) < this.searchCueWord.length) {
          inputDom.classList.remove("bottomInput")
          inputDom.classList.remove("defaultInput")
          inputDom.classList.add("topInput")
          // eslint-disable-next-line no-undef
          setTimeout(() => {
            this.cueWordIndex = this.cueWordIndex + 1
            inputDom.classList.remove("topInput")
            inputDom.classList.add("bottomInput")
          }, 300)

          // eslint-disable-next-line no-undef
          setTimeout(() => {
            inputDom.classList.remove("bottomInput")
            inputDom.classList.add("defaultInput")
          }, 350)
        } else {
          inputDom.classList.remove("bottomInput")
          inputDom.classList.remove("defaultInput")
          inputDom.classList.add("topInput")
          // eslint-disable-next-line no-undef
          setTimeout(() => {
            this.cueWordIndex = 0
            inputDom.classList.remove("topInput")
            inputDom.classList.add("bottomInput")
          }, 300)
          // eslint-disable-next-line no-undef
          setTimeout(() => {
            inputDom.classList.remove("bottomInput")
            inputDom.classList.add("defaultInput")
          }, 350)
        }
      }, 3000)
    },
    focusHandler() {
      const navRelease = document.getElementById('nav_release')
      if (navRelease) {
        navRelease.style.display = "none"
      }

      if (this.timer) {
        // eslint-disable-next-line no-undef
        clearInterval(this.timer)
        this.timer = null
      }

      this.isHighlight = true;

      if (this.searchValue !== "") {
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.visible = this.$refs.SearchPrompt.getPromptListHandler(this.searchValue);
        }, 300)
      } else {
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.visible = true;
        }, 300)
      }

    },
    handleMouseUp(e) {
      // 点击的区域被包含在组件的区域内
      if (document.querySelector('main')?.contains(e.target)) {
        this.onBlurHandler()
      }

      // if (document.querySelector('.search_prompt_wrapper')?.contains(e.target)) {
      //   this.onBlurHandler()
      // }
    },
    onBlurHandler() {
      if (!this.timer) {
        this.cueWordHandler();
      }

      this.isHighlight = false;

      this.visible = false;

      // eslint-disable-next-line no-undef
      setTimeout(() => {
        this.visible = false;

      }, 10)

      const navRelease = document.getElementById('nav_release')
      // eslint-disable-next-line no-undef
      setTimeout(() => {
        if (navRelease) {
          navRelease.style.display = "unset"
        }
      }, 250)
    },
    showTipsHandler() {
      this.$refs.SearchPrompt.getHistoryHandler()
    },
    searchHandler() {
      const cueWordValue = this.searchCueWord.length > 0 ? this.searchCueWord[this.cueWordIndex]?.name : ""

      if (!this.searchValue.trim() && !cueWordValue) {
        this.$toast("请输入关键字")
      } else if (this.searchValue.trim()) {

        if (this.$route.path === '/search') {
          window.location.href = `/search?keywords=${this.searchValue}`
        } else {
          window.open(`/search?keywords=${this.searchValue}`)
        }
        this.$refs.SearchPrompt.addHistoryHandler(this.searchValue)

        this.$analysys.search(
          null,
          null,
          false,
          this.searchValue,
          false,
          '全局搜索'
        )

      } else if (cueWordValue) {

        if (this.$route.path === '/search') {
          window.location.href = `/search?keywords=${cueWordValue}`
        } else {
          window.open(`/search?keywords=${cueWordValue}`)
        }
        this.$refs.SearchPrompt.addHistoryHandler(cueWordValue)

        this.$analysys.search(
          null,
          null,
          false,
          cueWordValue,
          false,
          '全局搜索'
        )

      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
