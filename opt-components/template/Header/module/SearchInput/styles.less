
.search_and_release_wrapper {
  width: 295px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search_input_content {
  flex-shrink: 0;
  width: 193px;
  position: relative;
  transition: width .2s ease;
}

.search_input_wrapper {
  width: 100%;
  height: 32px;
  border-radius: 30px;
  background: #F8F9FA;
  display: flex;
  justify-content: center;
  align-items: center;
  //transition: all .15s;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.30);

  /deep/ .el-input-group__append {
    padding: 0;
    background: #F8F9FA !important;
  }

  /deep/ .el-input__suffix {
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    right: 2px;
  }

  /deep/ .el-input__suffix i {
    width: 16px;
    height: 16px;
    background-color: #919191;
    border-radius: 50%;
    color: #F8F9FA;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;

    &::before {
      content: '\e6db';
    }
  }


  /deep/ .topInput::placeholder {
    transition: all .15s;
    transform: translateY(-150%);
  }

  /deep/ .bottomInput::placeholder {
    transform: translateY(150%);
  }

  /deep/ .defaultInput::placeholder {
    transition: all .15s;
    transform: translateY(0%);
  }

  /deep/ .el-input {
    background: #F8F9FA;
    border: none;
  }

  /deep/ .el-input__inner {
    border-radius: unset;
    padding: 0 20px 0 22px;
    color: #999EA4;
  }

  /deep/ .el-input__inner, /deep/ .el-input-group__append {
    height: 32px;
    line-height: 32px;
    border: none;
    background: #F8F9FA;
  }

  /deep/ .search_btn_wrapper {
    //padding-right: 4px;
    height: 100%;
    display: flex;
    align-items: center;
  }

  /deep/ .search_btn {
    width: 32px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    justify-content: start;
    align-items: center;
    cursor: pointer;

    .icons {
      width: 16px;
      height: 16px;
      color: #999EA4;
    }
  }

  //
  ///deep/ .search_active_btn {
  //  border-radius: 20px;
  //  background: #0581CE;
  //
  //  .icons {
  //    color: #FFFFFF;
  //  }
  //}
}

.search_input_active_wrapper {
  border: 1px solid rgba(90, 192, 255, 0.40);
  background: #279AE2;

  /deep/ .el-input__inner {
    background: #279AE2 !important;
    color: rgba(255, 255, 255, 0.70);
    //padding: 0 24px 0 16px !important;
    &::placeholder {
      color: rgba(255, 255, 255, 0.70);
    }
  }

  /deep/ .el-input-group__append {
    background: #279AE2 !important;
  }

  /deep/ .search_btn {
    .icons {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}
