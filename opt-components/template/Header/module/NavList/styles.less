.nav_dir_wrapper {
  flex-shrink: 0;
  height: 100%;
  display: flex;
  align-items: center;

  .dir_content {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-shrink: 0;

    .dir_item {
      .dir_item_a {
        display: block;
        color: rgba(255, 255, 255, 0.90);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;

        &:hover {
          color: #FFF;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -8px;
            width: 12px;
            height: 4px;
            border-radius: 21px;
            background: #FFF;
          }
        }
      }
    }

    .dir_item_active {
      color: #FFF;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -8px;
        width: 12px;
        height: 4px;
        border-radius: 21px;
        background: #FFF;
      }
    }
  }
}
