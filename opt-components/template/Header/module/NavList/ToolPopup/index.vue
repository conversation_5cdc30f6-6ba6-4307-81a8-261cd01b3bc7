<template>
  <el-popover
    placement="bottom-start"
    :visible-arrow="false"
    width="388"
    trigger="hover"
    :tabindex="10"
    @show="show = true"
    @hide="show = false"
  >
    <div class="tool_popup_wrapper">
      <ul class="tool_list">
        <template v-for="(item,index) in list">
          <li v-show="item.list" :key="index" class="tool_item_wrapper">
            <div class="tool_title">
              <SvgIcon :icon-class="item.icon" class-name="tool_svg"/>
              <span>{{ item.name }}</span>
            </div>
            <div class="tool_content">
              <nuxt-link
                v-for="itemC in item.list"
                :key="itemC.id"
                :target="itemC.name === '投审稿' ? '_self' : '_blank'"
                :to="itemC.url"
                class="tool_item text-limit-1"
                :title="itemC.name"
                @click="analysysHandler(itemC.name)"
              >
                {{ itemC.name }}
              </nuxt-link>
            </div>
          </li>
        </template>

      </ul>
    </div>
    <div slot="reference" class="tool_popup_dir_a" :class="show ? 'active_tool_popup_dir_a' : ''">
      <span>{{ name }}</span>
      <SvgIcon icon-class="nav_select" :class-name="show ? 'nav_select active_nav_select' : 'nav_select'"/>
    </div>
  </el-popover>
</template>

<script>
import {getWebApiFirstCategoryList} from "../../../../../../api/bms";
import {getWebApiChannel} from "../../../../../../api/channel";

export default {
  name: "ToolPopup",
  props: ["name"],
  data() {
    return {
      show: false,
      list: [
        {name: '学术资源', icon: 'nav_tool_2', list: this.$store.state.global.AcademicResourcesList},
        {name: '亚专业', icon: 'nav_tool_1', list: this.$store.state.global.channelData},
        {name: '医药器械', icon: 'nav_tool_3', list: this.$store.state.global.firstCategoryList},
      ]
    }
  },
  computed: {
    firstCategoryList() {
      return this.$store.state.global.firstCategoryList
    },
    channelData() {
      return this.$store.state.global.channelData
    }
  },
  watch: {
    firstCategoryList(newValue) {
      this.$set(this.list[2], 'list', newValue)
    },
    channelData(newValue) {
      this.$set(this.list[1], 'list', newValue)
    }
  },
  mounted() {
    this.getDataHandler()
  },
  methods: {
    getDataHandler() {
      if (this.firstCategoryList.length === 0) {
        this.$axios.$request(getWebApiFirstCategoryList()).then(res => {

          if (res.code === 1) {
            this.$store.commit('global/setFirstCategoryListHandler', this.formatFirstCategoryList(res.list))
          }
        })
      }

      if (this.channelData.length === 0) {
        this.$axios.$request(getWebApiChannel()).then(res => {
          if (res.code === 1) {
            this.$store.commit('global/setChannelDataHandler', this.formatChannelList(res.list))
          }
        })
      }
    },
    // 埋点
    analysysHandler(name) {
      this.$analysys.btn_click(`${this.title}-${name}`, window.document.title)
    },
    formatFirstCategoryList(list) {
      if (list.length > 0) {
        return list.map(item => {
          return {
            id: item.id,
            name: item.name,
            url: `/bms/home/<USER>
          }
        })
      }

      return []
    },
    formatChannelList(list) {
      if (list.length > 0) {
        return list.map(item => {
          return {
            id: item.channelId,
            name: item.name,
            url: `/channel/home?channelId=${item.channelId}&channelTitle=${encodeURIComponent(item.name)}&mpId=${item.mpSubspecialityId}&ocsId=${item.ocsSubspecialityId}`
          }
        })
      }

      return []
    }
  }
}
</script>

<style scoped lang="less">
.tool_popup_dir_a {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.90);
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  cursor: default;

  .nav_select {
    width: 12px;
    height: 12px;
    margin-left: 6px;
    transition: all .2s ease;
  }

  .active_nav_select {
    transform: rotateZ(-180deg);
  }
}
.active_tool_popup_dir_a{
  color: #FFF;
}

.tool_popup_wrapper {
  padding: 4px;

  .tool_list {
    .tool_item_wrapper {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .tool_title {
        display: flex;
        align-items: center;
        color: #304B64;
        font-size: 14px;
        margin-bottom: 12px;

        .tool_svg {
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }

      .tool_content {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .tool_item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          width: 84px;
          height: 26px;
          text-align: center;
          line-height: 26px;
          border-radius: 4px;
          border: 0.5px solid rgba(112, 138, 162, 0.20);
          background: linear-gradient(0deg, rgba(112, 138, 162, 0.04) 0%, rgba(112, 138, 162, 0.04) 100%), #FFF;
          cursor: pointer;

          &:hover {
            border: 0.5px solid rgba(48, 75, 100, 0.24);
            background: linear-gradient(0deg, rgba(48, 75, 100, 0.10) 0%, rgba(48, 75, 100, 0.10) 100%), #FFF;
            color: #304B64;
          }
        }
      }
    }
  }
}
</style>
