<template>
  <div id="nav_list" class="nav_dir_wrapper">
    <ul class="dir_content">
      <template v-for="(item,index) in navList">
        <li
          v-if="item.isEnable && !item.popupType"
          :key="index"
          class="dir_item"
          :class="$store.state.navPath === item.activeUrl ? 'dir_item_active' : ''">
          <nuxt-link target="_self" :to="item.url" class="dir_item_a" @click="buryingPoint(item.name)">
            {{ item.name }}
          </nuxt-link>
        </li>
        <!--全部工具-->
        <li
          v-else-if="item.isEnable && item.popupType === 'ToolPopup'"
          :key="index"
          class="dir_item">
          <ToolPopup :name="item.name"/>
        </li>
      </template>
    </ul>
  </div>
</template>

<script>
import ToolPopup from "./ToolPopup/index.vue";

export default {
  name: "NavList",
  components: {ToolPopup},
  data() {
    return {
      navList: [
        {name: '首页', url: '/', activeUrl: '/', isEnable: true,},
        {name: '资讯', url: '/info', activeUrl: '/info', isEnable: true,},
        {name: '会议', url: '/meeting/home', activeUrl: '/meeting', isEnable: true,},
        {name: '病例', url: '/case', activeUrl: '/case', isEnable: true,},
        {name: '云课堂', url: '/mooc', activeUrl: '/mooc', isEnable: true,},
        {name: '全景手术', url: '/holographic', activeUrl: '/holographic', isEnable: true,},
        {name: '投审稿', url: '/personalfile', activeUrl: '/personalfile', isEnable: false,},
        {name: '更多', url: '/personalfile', activeUrl: '/personalfile', isEnable: true, popupType: 'ToolPopup'},
        {
          name: '关于我们',
          url: '/introduce?optionActive=aboutUs',
          activeUrl: '/introduce?optionActive=aboutUs',
          isEnable: true,
        },
      ]
    }
  },
  methods:{
    buryingPoint(name){
      this.$analysys.btn_click(name, document.title)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
