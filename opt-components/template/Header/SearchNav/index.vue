<template>
  <header>
    <div id="nav_header" class="fixed_header">
      <Container :styles="{height:'100%'}">
        <div id="nav_header_content" class="header_content">
          <div class="search_header_left">
            <Logo/>
            <img src="~assets/images/nav/search_tips.png" alt="" class="logo_tips">
          </div>
          <div class="search_header_right">
            <ReleaseBtn/>
            <UserMessage/>
            <UserLoginStatus/>
            <EnBtn/>
          </div>
        </div>
      </Container>
    </div>
  </header>
</template>

<script>
import Container from "../../Container";
import EnBtn from "../module/EnBtn/index.vue";
import Logo from "../module/Logo/index.vue";
import UserMessage from "../module/UserMessage/index.vue";
import UserLoginStatus from "../module/UserLoginStatus/index.vue";
import ReleaseBtn from "../module/ReleaseBtn/index.vue";
export default {
  name: "HeaderNav",
  components: {
    Container,
    ReleaseBtn,
    EnBtn,
    Logo,
    UserMessage,
    UserLoginStatus
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
