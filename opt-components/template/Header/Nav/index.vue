<template>
  <header>
    <div id="nav_header" class="fixed_header">
      <Container :styles="{height:'100%'}">
        <div id="nav_header_content" class="header_content">
          <div id="nav_content_left" class="nav_content_left">
            <Logo/>
            <NavList/>
          </div>
          <div id="nav_content_right" class="nav_content_right">
            <SearchInput>
              <template #right>
                <ReleaseBtn/>
              </template>
            </SearchInput>
            <UserMessage/>
            <UserLoginStatus/>
            <EnBtn/>
          </div>
        </div>
      </Container>
    </div>
  </header>
</template>

<script>
import Container from "../../Container";
import NavList from "../module/NavList";
import SearchInput from "../module/SearchInput/index.vue";
import EnBtn from "../module/EnBtn/index.vue";
import Logo from "../module/Logo/index.vue";
import UserMessage from "../module/UserMessage/index.vue";
import UserLoginStatus from "../module/UserLoginStatus/index.vue";
import ReleaseBtn from "../module/ReleaseBtn/index.vue";

export default {
  name: "HeaderNav",
  components: {
    Container,
    NavList,
    SearchInput,
    EnBtn,
    Logo,
    UserMessage,
    UserLoginStatus,
    ReleaseBtn
  },
  data() {
    return {}
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
