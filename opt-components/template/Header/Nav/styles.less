header {
  width: 100%;
  height: 60px;
}

#nav_header {
  transition: all .2s ease;
}

.fixed_header {
  position: fixed;
  top: 0;
  width: 100%;
  height: 60px;
  background: #0581CE;
  z-index: 2000;

  .header_content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .nav_content_left{
      display: flex;
      align-items: center;
      gap: 0 40px;
    }
    .nav_content_right {
      display: flex;
      align-items: center;
      gap: 0 24px;
    }
  }
}
