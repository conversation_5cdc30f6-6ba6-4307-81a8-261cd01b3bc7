<template>
  <div class="filter_wrapper">
    <div class="filter_instrument_btn filter_instrument_first_btn">
      <span ref="filter_instrument_span" class="btn_name text-limit-1">全部器械</span>
      <i class='el-icon-caret-bottom' style='font-size: 20px; margin-left: 4px'/>

      <div class="filter_instrument_content">
        <div class="first_level_wrapper">
          <div
            class="first_level_item"
            :class="activeSecond === null ? 'active_first_level_item' :''"
            @click="changeSecondHandler(null)">全部器械
          </div>
          <div v-for="(item,index) in secondCategoryList"
               :key="item.id"
               class="first_level_item"
               :class="activeSecond === item.id ? 'active_first_level_item' :''"
               @click="changeSecondHandler(item.id,item.name)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="second_level_wrapper">
          <div v-for="item in operationManualThirdCategoryList" :key="item.id"
               class="second_level_item" :class="activeThird === item.name? 'active_second_level_item' :''"
               @click="changeThirdHandler(item.id,item.name)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>

    <div class="filter_instrument_btn">
      <span ref="filter_type_span" class="btn_name text-limit-1">全部类型</span>
      <i class='el-icon-caret-bottom' style='font-size: 20px; margin-left: 4px'/>

      <div class="filter_type_content">
        <div :class="activeType === null ? 'active_type_item' :''"
             class="type_item" @click="changeType(null)">全部
        </div>
        <div v-for="item in filterTypes" :key="item.code"
             :class="activeType === item.code ? 'active_type_item' :''"
             class="type_item" @click="changeType(item.code,item.desc)">{{ item.desc }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import {getOperationManualThirdCategoryList} from "../../../../api/instrument.js";

export default {
  name: "FilterInstrument",
  props: {
    filterTypes: {
      default: () => []
    },
    secondCategoryList: {
      default: () => []
    },
  },
  data() {
    return {
      activeType: null,
      activeSecond: null,
      activeThird: null,
      operationManualThirdCategoryList: []
    }
  },
  mounted() {
    if (this.activeSecond === null) {
      this.activeThird = "全部器械"
      this.operationManualThirdCategoryList = [{
        id: null,
        name: "全部器械"
      }]
    } else {
      this.$axios.$request(getOperationManualThirdCategoryList()).then(res => {
        if (res.code === 1) {
          this.operationManualThirdCategoryList = res.list
        }
      })
    }
  },
  methods: {
    changeType(id, name) {
      this.activeType = id;
      this.$emit('changeType', id)
      const span = this.$refs.filter_type_span
      span.innerText = name || "全部类型"
    },
    changeSecondHandler(id, name) {
      const dom = document.querySelector('.second_level_wrapper')
      dom.scrollTop = 0
      this.activeSecond = id;
      // this.activeThird = null;
      if (id === null) {
        this.operationManualThirdCategoryList = [{
          id: null,
          name: "全部器械"
        }]
      } else {
        this.$axios.$request(getOperationManualThirdCategoryList({
          categoryId: id
        })).then(res => {
          if (res.code === 1) {
            this.operationManualThirdCategoryList = res.list
            this.operationManualThirdCategoryList.unshift({
              id,
              name: `全部${name}`
            })
          }
        })
      }

    },
    changeThirdHandler(id, name) {
      this.activeThird = name;
      this.$emit('changeCategoryId', {id,name})
      const span = this.$refs.filter_instrument_span
      span.innerText = name || "全部器械"

    }
  }
}
</script>

<style scoped lang="less">
.filter_wrapper {
  display: flex;
  align-items: center;
  gap: 32px;
  z-index: 10;

  .filter_instrument_btn {
    height: 49px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #676C74;
    cursor: default;
    position: relative;

    .btn_name {
      text-align: center;
      min-width: 64px;
    }

    i {
      transition: all .10s;
    }

    &:hover {
      color: #0581CE;

      i {
        transform: rotate(-180deg);
      }

      .filter_type_content {
        display: block;
      }
    }

    .filter_type_content {
      display: none;
      position: absolute;
      left: 50%;
      top: 95%;
      transform: translateX(-50%);
      width: 80px;
      box-shadow: 10px 11px 30px 0px rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      overflow: hidden;

      .type_item {
        width: 100%;
        height: 36px;
        background: white;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: #676C74;
        font-size: 14px;
      }

      .active_type_item {
        background: #EFFAFF;
        color: #0581CE;
      }
    }


    .filter_instrument_content {
      height: 240px;
      overflow: hidden;
      position: absolute;
      left: 0;
      top: 95%;
      box-shadow: 10px 11px 30px 0 rgba(0, 0, 0, 0.15);
      border-radius: 6px;
      display: none;
      align-items: start;
      background: white;
      border: 0.5px solid #E8E8E8;


      .first_level_wrapper {
        &::-webkit-scrollbar {
          width: 4px;
        }

        background: #F8F8F8;
        width: 98px;
        padding: 10px 0;
        height: 100%;
        overflow: auto;

        .first_level_item {
          height: 34px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          margin-bottom: 8px;
          color: #333;
          font-size: 14px;
        }

        .active_first_level_item {
          background: #0581CE;
          color: #FFF;
        }
      }

      .second_level_wrapper {
        width: 280px;
        height: 100%;
        overflow: auto;
        background: white;
        padding: 20px 16px 0;
        box-sizing: border-box;

        &::-webkit-scrollbar {
          width: 4px;
        }

        .second_level_item {
          margin-bottom: 20px;
          color: #666;
          font-size: 14px;
          cursor: pointer;
        }

        .active_second_level_item {
          color: #0581CE;
        }
      }
    }
  }

  .filter_instrument_first_btn {
    &:hover {
      .filter_instrument_content {
        display: flex;
      }
    }
  }
}
</style>
