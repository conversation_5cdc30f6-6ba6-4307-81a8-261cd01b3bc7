<template>
  <div class="column_tab">
    <div
      v-for="item in list"
      :key="item.id"
      class="column_item"
      :class="siteType === item.code ? 'active_column_item' : ''"
      @click="change(item.code)"
    >
      {{ item.title }}
    </div>
  </div>
</template>

<script>
export default {
  name: "ColumnTab",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    siteType: {
      type: String,
      default: null
    }
  },
  methods: {
    change(code) {
      this.$emit('changeTab', code)
    }
  }
}
</script>

<style scoped lang="less">
.column_tab {
  display: flex;
  align-items: center;
  gap: 45px;
  border-bottom: 1px solid #E6E6E6;
  padding: 16px 0 12px;
  margin-bottom: 24px;
  background: white;
  z-index: 10;

  .column_item {
    color: #676C74;
    font-size: 16px;
    line-height: 20px;
    cursor: pointer;
    user-select: none;
  }

  .active_column_item {
    color: #333;
    font-weight: 600;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -14px;
      width: 16px;
      height: 4px;
      border-radius: 2px;
      background: #0581CE;
    }
  }
}
</style>
