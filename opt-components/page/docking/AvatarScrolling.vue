<template>
  <div v-show="timer" class="slider_head_img">
    <div class="out">
      <div class="inner">
        <div class="inner_img" ref="headimgList" v-for="(item,index) in sliderHeadImgList" :key="index">
          <img :src="item" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AvatarScrolling",
  props: {
    sliderHeadImgList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      timer: null,
      // 定义位置数组
      idArr: ['first', 'second', 'right', 'last']
    }
  },
  mounted() {
    this.init()
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    init() {
      // 初始化图片位置，根据id不同，初始化样式
      var img = this.$refs.headimgList
      img[0].id = this.idArr[0]
      img[1].id = this.idArr[1]
      img[3].id = this.idArr[3]
      for (let i = 0; i < (this.sliderHeadImgList.length - 4); i++) {
        this.idArr.splice(3, 0, 'left')
      }
      this.initialize()
      // 定时器图片轮播
      this.timer = setInterval(this.next, 2000)
    },
    next() {
      // 将位置数组的最后一个元素删除，并将删除元素添加到位置数组第一个
      this.idArr.unshift(this.idArr.pop())
      this.initialize()
    },
    // 将位置数组元素作为id赋值给img，达到轮播效果
    initialize() {
      var img = this.$refs.headimgList
      for (var i = 0; i < img.length; i++) {
        img[i].id = this.idArr[i]
      }
    }
  }
}
</script>

<style scoped lang="less">
.slider_head_img {
  position: relative;
  display: flex;
  align-items: start;
  width: 72px;
  height: 26px;
  margin-left: -24px;
  margin-right: 36px;
}

.out {
  width: 100%;
  height: 100%;
  position: relative;

  .inner {
    width: 100%;
    height: 24px;
    transition: 1.2s ease;

    .inner_img {
      width: 24px;
      height: 24px;
      position: absolute;
      transition: 1.2s ease;
      display: flex;
      border-radius: 50%;
      border: 1px solid #F4F4F4;

      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
  }

  //设置id样式
  #last {
    display: flex;
    transform: translateX(24px);
    z-index: 1;
    opacity: 1;
  }

  #first {
    display: flex;
    transform: translateX(36px) scale(1);
    z-index: 2;
    opacity: 1;
  }

  #second {
    display: flex;
    transform: translateX(48px);
    z-index: 3;
    opacity: 1;
  }

  #left {
    //display: none;
    transform: translateX(0px) scale(0);
    z-index: 1;
    opacity: 0;
  }

  #right {
    display: flex;
    transform: translateX(84px) scale(0);
    z-index: 1;
    opacity: 0;
  }
}
</style>
