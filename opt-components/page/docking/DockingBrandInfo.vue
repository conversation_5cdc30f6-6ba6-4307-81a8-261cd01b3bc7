<template>
  <div class="docking_brand_wrapper">
    <div class="brand_header">
      <div class="brand_info">
        <a target="_blank" :href="`/bms/classify/3/product-line-details/${publisher.productLineId}`" class="image">
          <zip-img
            fill
            :width="32"
            :height="32"
            :src="publisher.productLineShareImage"
          />
        </a>
        <span>{{ publisher.brandName }}·{{ publisher.productLineName }}</span>
      </div>
      <a target="_blank" :href="`/bms/classify/3/product-line-details/${publisher.productLineId}`" class="brand_btn">进入主页</a>
    </div>

    <div v-if="productLineList.length>0" class="product_catalog_wrapper">
      <div class="catalog_title">
        <div class="name">产品目录</div>

        <div class='more cursor' @click="lookMoreHandler">
          <span>查看更多</span>
          <svg-icon icon-class='more_brand' class-name='icons'/>
        </div>
      </div>

      <ul class='catalog_list'>
        <NewProductsItem
          v-for='item in productLineList'
          :id='item.id'
          :key='item.id'
          :category-id='`-`'
          :title='item.name'
          :category-name='item.category.name'
          :logo='item.brand.shareImage'
          :brand-name='item.brand.name'
          :img='item.image'
          :brand-is-show='false'
          :bms-auth="item.bmsAuth"
          :brand-product-line-id='item.brandProductLine ? item.brandProductLine.id : null'
        />
      </ul>
    </div>

    <div v-if="infoList.length>0" class="product_catalog_wrapper">
      <div class="catalog_title">
        <div class="name">品牌资讯</div>

        <div class='more cursor' @click="lookInfoMoreHandler">
          <span>查看更多</span>
          <svg-icon icon-class='more_brand' class-name='icons'/>
        </div>
      </div>

      <RecommendDataList
        :data-list="infoList"
      />
    </div>


  </div>
</template>

<script>
import ZipImg from "../../component/ZipImg/index.vue";
import NewProductsItem
  from "../../../components/optimize-components/public/article-types-list/bms/NewProductsItem/index.vue";
import {getBrandProductPage, getContentPageByCategoryId} from "../../../api/bms.js";
import RecommendDataList
  from "../../../components/optimize-components/page-components/bms/BrandContent/BrandTabs/RecommendDataList/index.vue";

export default {
  name: "DockingBrandInfo",
  props: ["publisher"],
  components: {NewProductsItem, ZipImg, RecommendDataList},
  data() {
    return {
      productLineList: [],
      infoList: []
    }
  },
  mounted() {
    Promise.all([
      this.$axios.$request(getBrandProductPage({
        brandId: this.publisher.brandId,
        productLineId: this.publisher.productLineId,
        categoryId: null,
        loginUserId: this.$store.state.auth.user.id,
        pageNo: 1,
        pageSize: 4
      })),
      this.$axios.$request(getContentPageByCategoryId({
        brandId: null,
        productLineId: this.publisher.productLineId,
        productId: null,
        categoryId: null,
        pageNo: 1,
        pageSize: 3,
        loginUserId: this.$store.state.auth.user.id
      }))
    ]).then(res => {

      if (res[0].code === 1) {
        this.productLineList = res[0].list
      }

      if (res[1].code === 1) {
        this.infoList = res[1].list
      }
    })

  },
  methods: {
    lookMoreHandler() {
      const {href} = this.$router.resolve({path: `/bms/classify/-/product-directory?brandId=${this.publisher.brandId}&from=product-line-${this.publisher.productLineId}`})
      window.open(href, '_blank')
    },
    lookInfoMoreHandler() {
      const {href} = this.$router.resolve({path: `/bms/classify/3/product-line-details/${this.publisher.productLineId}`})
      window.open(href, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
.docking_brand_wrapper {
  border-radius: 8px;
  background: #FAFBFD;
  margin-bottom: 30px;

  .brand_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid #EEE;

    .brand_info {
      display: flex;
      align-items: center;

      .image {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        margin-right: 8px;
      }

      color: #333;
      font-size: 17px;
    }

    .brand_btn {
      width: 70px;
      height: 22px;
      border-radius: 100px;
      border: 1px solid #0581CE;
      color: #0581CE;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .product_catalog_wrapper {
    padding: 24px 20px;

    .catalog_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .name {
        color: #666;
        font-size: 20px;
        font-weight: 600;
      }

      .more {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 14px;

        .icons {
          width: 10px;
          height: 10px;
          margin-left: 2px;
        }
      }
    }

    .catalog_list {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 14px;
    }

    /deep/.recommend_list_wrapper{
      li {
        background: white;
      }
    }
  }
}
</style>
