<template>
  <section class='tabs-container' ref='tabs_brand' id='tabs_brand'>
    <ol class='tabse_ol'>
      <template v-for='item in tabList'>
        <li
          v-if='item.isEnable'
          :id='item.id'
          :key='item.id'
          :ref='item.id'
          class='tabs_li cursor'
          @click='changeTabHandler(item.id)'>
        <span
          :id='activeComponent === item.id ? "is_active" : ""'
          :ref='activeComponent === item.id ? "is_active" : ""'>
          {{ item.tabName }}
        </span>
        </li>
      </template>
      <li id='line' ref='line'/>
    </ol>
    <template>
      <slot name='right'></slot>
    </template>
  </section>
</template>

<script>
export default {
  name: 'DockingTabs',
  props: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (<PERSON>)
     * @date 2023-02-10 14:53
     * @description: 传递格式为   [{ id: 'A', tabName: '近期发布' }] 的数组
     * ------------------------------------------------------------------------------
     */
    tabList: {
      type: Array,
      default: () => [
        {id: 0, tabName: 'Name1', isEnable: true},
        {id: 1, tabName: 'Name2', isEnable: true}
      ]
    },
    activeComponent: {}
  },
  data() {
    return {
      activeTab: this.tabList.find(item => item.isEnable).id
    }
  },
  watch: {
    tabList: {
      handler(value) {
        const id = value.find(item => item.isEnable).id
        this.activeTab = id
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.lineHandler(id)
        }, 400)
      },
      deep: true
    },
    activeComponent(value) {
      if (value) {
        setTimeout(() => {
          this.lineHandler(this.activeComponent)
        }, 40)
      }
    }
  },
  mounted() {
    this.lineHandler('is_active')
  },
  methods: {
    /**
     * 底部选中跳移动位置
     */
    lineHandler(id) {
      const idDom = this.$refs?.[id]?.[0]
      if (id && idDom) {
        const lineDom = this.$refs.line
        const idDomLeft = idDom ? idDom.offsetLeft : 0
        lineDom.style.opacity = 1
        lineDom.style.transform = `translateX(${(idDom.offsetWidth / 2) - (lineDom.offsetWidth / 2) + idDomLeft}px)`
      }
    },
    /**
     * 切换tab
     */
    changeTabHandler(id) {
      const dom = this.$refs.tabs_brand
      window.scrollTo({top: 0, behavior: 'smooth'})

      this.$emit('changeTabHandler', id, val => {
        if (val) {
          this.activeTab = id
          this.lineHandler(id)
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
.tabs-container {
  user-select: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  line-height: 56px;
  position: sticky;
  top: 60px;
  background: white;
  border-bottom: 1px solid #EEE;
  z-index: 1;

  .tabse_ol {
    display: grid;
    justify-content: flex-start;
    grid-auto-flow: column;
    grid-gap: 0 40px;
    padding: 0 22px;
    box-sizing: border-box;
    position: relative;

    #line {
      width: 26px;
      height: 2px;
      background: #0581CE;
      border-radius: 2px;
      position: absolute;
      bottom: 0;
      transition: all .3s;
      opacity: 0;
    }

    .tabs_li {
      color: #666666;
      font-size: 16px;

      &:hover {
        color: var(--theme-color);
      }

      #is_active {
        color: var(--theme-color);
        font-weight: 700;
      }
    }
  }
}

</style>
