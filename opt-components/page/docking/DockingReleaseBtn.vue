<template>
  <div class="docking_btn_wrapper">
    <a v-if="releaseBtnShow" target="_blank" href="/idp/release" class="docking_btn" @click="$analysys.btn_click('发布对接', '产业对接')">
      <img src="~assets/images/industrial/release_docking.png" alt="发布对接">
      <p class="btn_name">发布对接</p>
    </a>
    <a v-if="listBtnShow" target="_blank" href="/idp/docking-list" class="docking_btn"
       @click="$analysys.btn_click('对接列表', '产业对接')">
      <img src="~assets/images/industrial/docking_list.png">
      <p class="btn_name">对接列表</p>
    </a>
  </div>
</template>

<script>
export default {
  name: "DockingReleaseBtn",
  props: {
    releaseBtnShow: {
      type: Boolean,
      default: true
    },
    listBtnShow:{
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped lang="less">
.docking_btn_wrapper {
  position: sticky;
  top: calc(50vh - 100px);
  float: right;
  margin-right: -112px;

  .docking_btn {
    width: 88px;
    height: 104px;
    border-radius: 8px;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    flex-flow: column;

    img {
      width: 38px;
      height: 38px;
    }

    .btn_name {
      margin-top: 10px;
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
