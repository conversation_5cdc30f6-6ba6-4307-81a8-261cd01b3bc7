<template>
  <ul class="side_nav_container">
    <li
      v-for="(item,index) in data"
      :key="index"
      class="side_nav_item"
      @click="changeIndex(index)"
    >
      <span
        class="side_nav_item_content"
        :class="activeIndex === index ? 'active_side_nav_item_content' : ''">{{ item.name }}</span>
    </li>
  </ul>
</template>

<script>
export default {
  name: "SideNav",
  props: {
    linkIndex: {
      type: Number,
      default: 0
    },
    data: {
      type: Array,
      default: () => [],
    }
  },
  data() {
    return {
      activeIndex: this.linkIndex
    }
  },
  watch: {
    linkIndex(newValue) {
      this.activeIndex = newValue
    }
  },
  methods: {
    changeIndex(index) {
      this.activeIndex = index;
      this.$emit('changeIndex', index)
    }
  }
}
</script>

<style scoped lang="less">
.side_nav_container {
  height: calc(100vh - 82px);
  overflow: auto;
  position: sticky;
  top: 80px;
  background: white;

  &::-webkit-scrollbar {
    width: 0px;
  }

  .side_nav_item {
    user-select: none;
    margin-bottom: 24px;

    .side_nav_item_content {
      color: #484949;
      font-size: 14px;
      height: 24px;
      line-height: 24px;
      display: inline-block;
      cursor: pointer;

      &:hover {
        color: var(--theme-color);
      }
    }

    .active_side_nav_item_content {
      color: var(--theme-color);
      font-weight: 500;
      font-size: 16px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
