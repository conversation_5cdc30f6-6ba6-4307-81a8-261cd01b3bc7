/deep/ .pause {
  display: block !important;
}


#prctureButton {
  display: none;
  width: 26px;
  height: 26px;
  float: right;
  margin-right: 10px;
  margin-top: 10px;
  position: relative;

  .tips {
    display: none;
    position: absolute;
    left: 50%;
    bottom: 48px;
    color: white;
    background: #3c3c3c;
    box-shadow: 0 0 5px 0 rgb(0 0 0 / 10%);
    height: 28px;
    margin: 0;
    padding-left: 5px;
    padding-right: 5px;
    line-height: 28px;
    font-size: 10px;
    white-space: nowrap;
    transform: translateX(-50%);
    z-index: 3000;
  }

  &:hover .tips {
    display: block;

  }

  img {
    width: 26px;
    height: 26px;
    cursor: pointer;
  }
}

/deep/ .playing {
  z-index: -10 !important;
}

/deep/ .prism-big-play-btn {
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%);
  z-index: 80;
  display: block !important;
}

/deep/ .layui-flow-more {
  margin: 20px auto !important;
}

/deep/ .prism-info-display {
  z-index: 0 !important;
}

/deep/ .prism-controlbar-bg {
  background: rgba(0, 0, 0, 0.7) !important;
}

// 弹幕
/deep/ .ali-danmuku-control {
  display: none;
}

/deep/ .aliplayer-danmuku .danmu {
  height: 95% !important;
}

/deep/ .prism-loading {
  transform: translate(-50%, -50%);
}

/**
短视频定制
 */

/deep/ .prism-controlbar {
  height: 65px !important;
}

//
/deep/ .prism-controlbar-bg {
  background: unset !important;
  //height: 65px !important;
}

//
/deep/ .prism-progress {
  //left: calc(50% + 20px) !important;
  //transform: translateX(-50%);
  width: calc(100% - 24px - 80px - 28px - 24px - 60px) !important;
  //bottom: 50% !important;
  //transform: translateY(50%);

  //left: 145px !important;
  //bottom: 18px !important;
  //margin-left: 0 !important;
  //margin-top: 20px !important;
  //top: 2px !important;
  //transform: translateX(-45%) !important;
}
