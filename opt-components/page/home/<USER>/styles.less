
.home_banner_wrapper {
  display: grid;
  grid-template-columns: 50% 50%;
  border-radius: 8px;
  overflow: hidden;


  .left_banner {

    .banner_item {
      background: #D9D9D9;
      overflow: hidden;
      position: relative;

      /deep/ .el-carousel__container {
        height: unset;
        padding-bottom: 56.25%;
      }

      /deep/ .el-carousel__arrow {
        font-size: 18px;
        font-weight: bold;
      }

      /deep/ .carousel_image_wrapper {
        width: 100%;
        height: 100%;
        position: relative;
        cursor: pointer;
      }

      /deep/ .carousel_image_mask {
        z-index: 1;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8));

        .mask_p {
          position: absolute;
          left: 0;
          bottom: 32px;
          font-size: 14px;
          color: #FFFFFF;
          padding: 0 16px;
        }
      }

      /deep/ .el-carousel__indicators--outside {
        position: absolute;
        left: 0;
        bottom: 10px;
        padding: 0 16px;

        .el-carousel__indicator--horizontal {
          transition: all .3s;
          padding: 0;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          background: #808081;
          margin-right: 3px;

          &:last-child {
            margin-right: 0;
          }

          .el-carousel__button {
            width: 3px;
            height: 3px;
            border-radius: 50%;
            background: #808081;
          }
        }

        .is-active {
          width: 20px;
          border-radius: 8px;
          background: white;

          .el-carousel__button {
            width: 20px;
            border-radius: 2px;
            background: white;
          }
        }
      }
    }
  }

  .right_banner {
    display: grid;
    grid-template-columns: 1fr 1fr;

    .banner_item {
      background: #D9D9D9;
      overflow: hidden;
      position: relative;
      width: 100%;
      cursor: pointer;

      img {
        display: block;
        transition: all .1s ease-in 0s;
      }

      .banner_mask {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8));
        transition: background .4s ease;

        .banner_text {
          transition: all .2s ease;
          width: 100%;
          padding: 0 16px;
          box-sizing: border-box;
          color: #FFF;
          font-size: 14px;
          line-height: 20px;

          position: absolute;
          top: calc(100% - 36px);
        }
      }

      &:hover {
        img {
          transform: scale(1.05);
          transition: transform .2s ease-in;
        }

        .banner_mask {
          background: rgba(0, 0, 0, 0.2);

          .banner_text {
            -webkit-line-clamp: 4;
            line-clamp: 4;

            top: 50%;
            transform: translateY(-50%);
          }
        }

      }
    }
  }
}
