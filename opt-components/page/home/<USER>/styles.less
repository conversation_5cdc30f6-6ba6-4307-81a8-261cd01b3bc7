
.today_live_wrapper {
  border-radius: 8px;
  background: #FFF;
  padding: 16px;

  .title_wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .title_left {
      display: flex;
      align-items: center;
      color: var(--font-color-333);
      font-size: 16px;
      font-weight: 500;

      .icons {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        margin-right: 6px;
      }
    }

    .title_right {
      display: flex;
      align-items: center;
      gap: 0 8px;
      color: #708AA2;
      font-size: 12px;
      user-select: none;

      .slider_btn {
        width: 20px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #F4F4F4;
        border-radius: 50%;

        .icons {
          width: 8px;
          height: 10px;
          color: #C7C7C7;
        }
      }

      .slider_btn_active {
        background: #708AA2;

        .icons {
          color: #FFFFFF;
        }
      }

      .slider_left {
        transform: rotateY(180deg);
        color: #708AA2;
        fill: #FFFFFF !important;
      }
    }
  }

  .content {
    overflow: hidden;

    .list_wrapper {
      display: flex;
      flex-wrap: nowrap;
      transition: transform .2s ease;

      .item_wrapper {
        width: 100%;
        flex-shrink: 0;

        .item_li {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .content_item_wrapper {
          .icons {
            width: 16px;
            height: 16px;
            vertical-align: middle;
          }

          .label {
            font-size: 12px;
            display: inline;
            font-weight: 600;
            margin-right: 1.5px;
          }

          .label-1 {
            color: #E43F1D;
          }

          .label-2 {
            color: rgb(243, 152, 0);
          }

          .info {
            vertical-align: middle;
            line-height: 16px;
          }

          .content_item_title {
            color: var(--font-color-333);
            font-size: 14px;
            font-weight: 500;

            &:hover {
              .info {
                color: #676C74;
              }
            }
          }

          .time {
            color: #708AA2;
            font-size: 12px;
            margin-top: 8px;
          }
        }
      }
    }
  }


  .today_btn {
    width: 100%;
    height: 33px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: rgba(117, 138, 160, 0.10);
    color: #708AA2;
    font-size: 12px;
    margin-top: 16px;

    &:hover {
      background: rgba(117, 138, 160, 0.10);
      color: #50789C;
    }
  }
}
