<template>
  <HomeListCard
    title="医药器械"
    bg-color="#50B390"
    :bg-img="require('assets/images/home/<USER>')"
    :list="formatList"
  />
</template>

<script>
import {HomeListCard} from "../../../ui";

export default {
  name: "MedicalDevices",
  components: {
    HomeListCard
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    formatList() {
      if (this.list.length > 0) {
        return this.list.map(item => {
          return {
            id: item.id,
            name: item.name,
            url: item.url
          }
        })
      }

      return []
    }
  }
}
</script>

<style scoped>

</style>
