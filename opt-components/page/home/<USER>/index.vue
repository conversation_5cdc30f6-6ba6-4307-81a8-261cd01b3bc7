<template>
  <HomeListCard
    title="学术资源"
    bg-color="#E5B557"
    :bg-img="require('assets/images/home/<USER>')"
    :list="AcademicResourcesList"
  />
</template>

<script>
import {HomeListCard} from "../../../ui";

export default {
  name: "AcademicResources",
  components: {
    HomeListCard
  },
  computed:{
    AcademicResourcesList(){
      return this.$store.state.global.AcademicResourcesList
    }
  }
}
</script>

<style scoped>

</style>
