<template>
  <HomeCard title="最新资讯" more-btn-href="/info">
    <div class="content">
      <div class="content_left">
        <InfoHomeDefaultItem
          v-for="(item,index) in firstData"
          :key="index"
          :info-id="item.infoId"
          :info-img="item.shareImage || item.infoImg"
          :info-title="item.infoTitle"
          :publish-date="item.publishDate"
          :author-name-list="item.authorNameList"
          :is-product="item.isProduct"
        />
      </div>
      <div class="content_right">
        <InfoHomeListItem
          v-for="item in residueData"
          :key="item.id"
          :info-id="item.infoId"
          :info-img="item.shareImage || item.infoImg"
          :info-title="item.infoTitle"
          :publish-date="item.publishDate"
          :author-name-list="item.authorNameList"
          :is-product="item.isProduct"
        />
      </div>
    </div>
  </HomeCard>
</template>

<script>
import {HomeCard} from "../../../ui";
import {InfoHomeDefaultItem, InfoHomeListItem} from "../../../data-list";

export default {
  name: "NewInfoModule",
  components: {
    HomeCard,
    InfoHomeDefaultItem,
    InfoHomeListItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    firstData() {
      if (this.list.length > 0) {
        return this.list.slice(0, 1)
      }

      return []
    },
    residueData() {
      if (this.list.length > 1) {
        return this.list.slice(1, this.list.length)
      }

      return []
    }
  },
  mounted() {
    console.log(this.list)
    console.log(this.firstData)
    console.log(this.residueData)
  }
}
</script>

<style scoped lang="less">
.content {
  display: grid;
  grid-template-columns: 432px 1fr;
  gap: 22px;

  .content_left {
  }

  .content_right {
    display: grid;
    gap: 22px;
  }
}
</style>
