<template>
  <HomeCard svg="home-card-popular" title="热门会议" more-btn-href="/meeting/home">
    <div class="content">
      <MeetingHomeDefaultItem
        v-for="item in list"
        :key="item.id"
        :meeting-id="item.id"
        :cover="item.appMainPic || item.playerCover || item.titlePic"
        :title="item.meetingName"
        :meeting-date-str="item.meetingDateStr"
        :province="item.province"
        :city="item.city"
        :meeting-live-status="item.meetingLiveStatus"
        :hls-live-url="item.hlsLiveUrl"
      />
    </div>
  </HomeCard>
</template>

<script>
import {HomeCard} from "../../../ui";
import {MeetingHomeDefaultItem} from "../../../data-list";

export default {
  name: "PopularMeetings",
  components: {
    HomeCard,
    MeetingHomeDefaultItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px 36px;
}
</style>
