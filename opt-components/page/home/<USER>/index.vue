<template>
  <div class="home_banner_wrapper">
    <div class="left_banner">
      <div id="index_banner_wrapper" ref="index_banner_wrapper" class="banner_item">
        <el-carousel
          indicator-position='outside'
          :interval="3000"
          :arrow="carouselList.length > 1 ? 'hover' : 'never'"
          @change="changeHandler"
        >
          <el-carousel-item v-for="item in carouselList" :key="item.adId">
            <div class="carousel_image_wrapper" @click="jumpBannerFun(item)">
              <ZipImg
                fill
                :src="item.image"
                :width="564"
                :height="318"
                :alt="item.name"
              />
              <div class="carousel_image_mask">
                <p class="text-limit-1 mask_p">{{ item.name }}</p>
              </div>
            </div>

          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div id="right_banner" ref="right_banner" class="right_banner">
      <div v-for="item in fourBannerList" :key="item.adId" class="banner_item" @click="jumpBannerFun(item)">
        <ZipImg
          :src="item.image"
          :width="282"
          :height="158"
          :alt="item.name"
          fill
        />
        <div class="banner_mask">
          <p class="banner_text text-limit-1">{{ item.name }}</p>
        </div>
      </div>
      <template v-for="(item,index) in  bannerList.length < 5 ? 5-bannerList.length : 0">
        <div v-if="bannerList.length < 5" :key="index" class="banner_item">
          <img class="img_cover" src="~assets/images/default16.png" alt="" style="position: absolute;left: 0;top: 0">
        </div>
      </template>

    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../../component";
import brandAdJump from "../../../../assets/helpers/brandAdJump";

export default {
  name: "HomeBanner",
  components: {
    ZipImg
  },
  props: {
    bannerList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      bannerIO: null,
      isCurrentPage: true,
      bannerLock: false,
      isExposureBanner: false,
      bannerIndex: 0,
      bannerIndexNum: 0,
      middleLock: false,
    }
  },
  computed: {
    fourBannerList() {
      if (this.bannerList.length >= 5) {
        return this.bannerList.slice(-4);
      } else if (this.bannerList.length > 0) {
        return this.bannerList.slice(1, this.bannerList.length);
      } else {
        return []
      }
    },
    carouselList() {
      if (this.bannerList.length >= 5) {
        return this.bannerList.slice(0, this.bannerList.length - 4);
      } else if (this.bannerList.length > 0) {
        return this.bannerList.slice(0, 1);
      } else {
        return []
      }
    }
  },
  mounted() {
    document.addEventListener("visibilitychange", this.isCurrentHandler);

    const bannerWrapper = this.$refs.index_banner_wrapper
    const rightBanner = this.$refs.right_banner

    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && entry.target.id === "index_banner_wrapper") {
          const bannerAdItem = this.carouselList[this.bannerIndex]
          this.isExposureBanner = true;


          if (!this.bannerLock) {
            this.bannerLock = true;
            this.$analysys.ad_exposure({
              adExtras: bannerAdItem.extras,
              adModule: bannerAdItem.module,
              exposureLocation: bannerAdItem.clickLocation,
              adCode: bannerAdItem.code,
              adId: bannerAdItem.adId,
              adName: bannerAdItem.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: bannerAdItem.extras,
            })
          }


        } else if (entry.target.id === "index_banner_wrapper" && !entry.isIntersecting) {
          this.isExposureBanner = false;
        } else if (entry.isIntersecting && entry.target.id === "right_banner" && !this.middleLock) {
          this.middleLock = true;
          this.fourBannerList?.forEach(item => {
            this.$analysys.ad_exposure({
              adExtras: item.extras,
              adModule: item.module,
              exposureLocation: item.clickLocation,
              adCode: item.code,
              adId: item.adId,
              adName: item.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: item.extras,
            })
          })
        }
      })
    });

    this.bannerIO.observe(bannerWrapper);
    this.bannerIO.observe(rightBanner);

  },
  beforeDestroy() {
    document.removeEventListener("visibilitychange", this.isCurrentHandler);
    this.isExposureBanner = false;
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  },
  methods: {
    // 切换走马灯
    changeHandler(index) {
      const item = this.carouselList[index]
      if (this.bannerIndex !== index && this.isExposureBanner && this.bannerIndexNum < this.carouselList.length && this.isCurrentPage) {
        this.bannerIndexNum = this.bannerIndexNum + 1
        this.$analysys.ad_exposure({
          adExtras: item.extras,
          adModule: item.module,
          exposureLocation: item.clickLocation,
          adCode: item.code,
          adId: item.adId,
          adName: item.name,
          unionid: this.$store.state.auth.unionid,
          adUrl: item.extras,
        })
      }
      this.bannerIndex = index;

    },

    // 监听是否为当前页面
    isCurrentHandler() {
      if (document.visibilityState === "hidden") {
        this.isCurrentPage = false;
      } else if (document.visibilityState === "visible") {
        this.isCurrentPage = true;
      }
    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
