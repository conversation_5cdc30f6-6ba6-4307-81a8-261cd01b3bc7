<template>
  <div :id='playerId' :style='playStyle' class='prism-player'></div>
</template>

<script>

export default {
  name: 'AliPlayerLive',
  components: {},
  props: {
    playStyle: {
      type: String,
      default: ''
    },
    aliplayerSdkPath: {
      // Aliplayer 代码的路径
      type: String,
      default: 'https://g.alicdn.com/de/prismplayer/2.8.1/aliplayer-min.js'
    },
    /**
     * 自动播放
     */
    autoplay: {
      type: Boolean,
      default: true
    },
    /**
     * 是否直播
     */
    isLive: {
      type: Boolean,
      default: false
    },
    /**
     * 加密播放
     */
    encryptType: {
      type: Number,
      default: null
    },
    /**
     * H5是否内置播放，有的Android浏览器不起作用。
     */
    playsinline: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    /**
     * 控制面板的实现，默认值为：hover。取值：
     * click：点击。
     * hover：停留。
     * always：一直。
     * never：隐藏整个控制面板。
     */
    controlBarVisibility: {
      type: String,
      default: 'hover'
    },
    /**
     * 控制栏自动隐藏时间，单位毫秒。
     */
    showBarTime: {
      type: String
    },
    /**
     * 指定使用H5播放器。
     */
    useH5Prism: {
      type: Boolean,
      default: false
    },
    /**
     * 指定使用Flash播放器
     */
    useFlashPrism: {
      type: Boolean,
      default: false
    },
    /**
     * 媒体转码服务的媒体ID。
     */
    vid: {
      type: String,
      default: ''
    },
    /**
     * 播放权证，获取播放权重
     */
    playauth: {
      type: String,
      default: ''
    },
    /**
     * 频播放地址URL，单独URL。默认状态，表示使用vid+playauth。
     */
    source: {
      type: String,
      default: ''
    },
    /**
     * 播放器默认封面图片，请填写正确的图片URL地址。需要autoplay值为false时，才生效
     */
    cover: {
      type: String,
      default: ''
    },
    /**
     * 指定播放地址格式，只有使用vid的播放方式时支持可选值，取值：
     * mp4
     * hls
     * flv
     * mp3
     * 默认为空，仅H5支持。
     */
    format: {
      type: String
    },
    /**
     * 功能组件布局配置，不传该字段使用默认布局。取值：false隐藏所有功能组件
     */
    skinLayout: {
      type: Array,
      default() {
        return []
      }
    },
    /**
     * 声明视频播在界面上的位置，默认值为center。取值：
     * center：居中。
     * top：顶部。
     */
    x5_video_position: {
      type: String,
      default: 'top'
    },
    /**
     * 声明启用同层H5播放器，启用时取值：H5。
     */
    x5_type: {
      type: String
    },
    /**
     * 声明视频播放时是否进入到TBS的全屏模式，取值：
     * false：不把视频做为背景。
     * true：把视频做为背景。
     * 默认值为false。
     */
    x5_fullscreen: {
      type: Boolean,
      default: false
    },
    /**
     * 声明TBS播放器支持的方向，取值：
     * landscape：横屏。
     * portraint：竖屏。
     */
    x5_orientation: {
      type: String,
      // eslint-disable-next-line vue/require-valid-default-prop
      default: 'landscape'
    },
    /**
     * 延迟播放时间，单位：秒。
     */
    autoPlayDelay: {
      type: Number,
      default: 0
    },
    /**
     * 延迟播放提示文本，更多信息
     */
    autoPlayDelayDisplayText: {
      type: String,
      default: ''
    },
    /**
     * 国际化，默认为zh-cn。如果未设置，则采用浏览器语言。取值：
     * zh-cn：中文。
     * en-us：英文。
     */
    language: {
      type: String,
      default: 'zh-cn'
    },
    /**
     * 显示视频清晰度，多个使用半角逗号（,）分隔，比如：‘FD,LD’，此值是vid对应流清晰度的一个子集，仅H5模式支持。取值：
     * FD（流畅）
     * LD（标清）
     * SD（高清）
     * HD（超清）
     * OD（原画）
     * 2K（2K）
     * 4K（4K）
     */
    definition: {
      type: String
    },
    /**
     * 默认视频清晰度，此值是vid对应流的一个清晰度，仅H5模式支持。取值：
     * FD（流畅）
     * LD（标清）
     * SD（高清）
     * HD（超清）
     * OD（原画）
     * 2K（2K）
     * 4K（4K）
     */
    defaultDefinition: {
      type: String
    },
    /**
     * 是否静音 默认否
     */
    isMute:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      CM: {},// 弹幕管理器
      player: null,// 播放器
      danmukuList: [
        {
          'mode': 8,            // mode 表示弹幕的类型，参考 弹幕类型 https://github.com/jabbany/CommentCoreLibrary/blob/master/docs/CommentTypes.md
          'text': '',       // text 表示弹幕的文字内容。注意：在创造弹幕对象后，对 text 的更改将无意义。
          'stime': 1000,        // stime 表示弹幕相对于视频位置的开始时间（ms），0即在视频开始立即出现
          'size': 20,           // 弹幕的文字大小
          'color': 0xFFFFFF    // 文字颜色
        }
      ],
      playerId: 'aliplayer_' + Math.random().toString(36).substr(2),
      scriptTagStatus: 0,
      isReload: false,
      instance: null,
      liveSkin: [
        {name: 'bigPlayButton', align: 'blabs', x: 30, y: 80},
        {name: 'errorDisplay', align: 'tlabs', x: 0, y: 0},
        {name: 'infoDisplay', align: 'cc'},
        {
          name: 'controlBar', align: 'blabs', x: 0, y: 0,
          children: [
            {name: 'liveDisplay', align: 'tlabs', x: 15, y: 6},
            {name: 'fullScreenButton', align: 'tr', x: 10, y: 10},
            {name: 'subtitle', align: 'tr', x: 15, y: 12},
            {name: 'setting', align: 'tr', x: 15, y: 12},
            {
              'name': 'snapshot',
              'align': 'tr',
              'x': 8,
              'y': 9
            },
            {name: 'volume', align: 'tr', x: 5, y: 10}
          ]
        }
      ],
      playSkin: [
        {name: 'bigPlayButton', align: 'blabs', x: 30, y: 80},
        {
          name: 'H5Loading', align: 'cc'
        },
        {name: 'errorDisplay', align: 'tlabs', x: 0, y: 0},
        {name: 'infoDisplay'},
        {name: 'tooltip', align: 'blabs', x: 0, y: 56},
        {
          name: 'controlBar', align: 'blabs', x: 0, y: 0,
          children: [
            {name: 'playButton', align: 'tl', x: 10, y: 10},
            {name: 'timeDisplay', align: 'tl', x: 10, y: 2},
            {name: 'progress', align: 'blabs', x: 130, y: 0},
            {name: 'fullScreenButton', align: 'tr', x: 10, y: 11},
            {name: 'volume', align: 'tr', x: 5, y: 9}

          ]
        }
      ]
    }
  },
  created() {
    // if (window.Aliplayer !== undefined) {
    //   // 如果全局对象存在，说明编辑器代码已经初始化完成，直接加载编辑器
    //   this.scriptTagStatus = 2;
    //   this.initAliplayer();
    // } else {
    //   // 如果全局对象不存在，说明编辑器代码还没有加载完成，需要加载编辑器代码
    //   this.insertScriptTag();
    // }
  },
  watch: {
    // 深度监听
    playauth: {
      handler(newV, oldV) {
        if (newV) {
          this.scriptTagStatus = 2
          this.initAliplayer()
          if (window.Aliplayer !== undefined) {
            // 如果全局对象存在，说明编辑器代码已经初始化完成，直接加载编辑器
            this.scriptTagStatus = 2
          } else {
            // 如果全局对象不存在，说明编辑器代码还没有加载完成，需要加载编辑器代码
            this.insertScriptTag()
          }
        }
      },
      deep: true
    },
    source: {
      handler(newV, oldV) {
        if (newV) {
          this.scriptTagStatus = 2
          this.initAliplayer()
          if (window.Aliplayer !== undefined) {
            // 如果全局对象存在，说明编辑器代码已经初始化完成，直接加载编辑器
            this.scriptTagStatus = 2
          } else {
            // 如果全局对象不存在，说明编辑器代码还没有加载完成，需要加载编辑器代码
            this.insertScriptTag()
          }
        }

      },
      deep: true
    }
  },
  mounted() {
    /**
     * 弹幕控制器
     */
    if (window.Aliplayer !== undefined) {
      // 如果全局对象存在，说明编辑器代码已经初始化完成，直接加载编辑器
      this.scriptTagStatus = 2
      this.initAliplayer()
    } else {
      // 如果全局对象不存在，说明编辑器代码还没有加载完成，需要加载编辑器代码
      this.insertScriptTag()
    }
  },
  methods: {
    insertScriptTag() {
      const _this = this
      let playerScriptTag = document.getElementById('playerScriptTag')
      // 如果这个tag不存在，则生成相关代码tag以加载代码
      if (playerScriptTag === null) {
        playerScriptTag = document.createElement('script')
        playerScriptTag.type = 'text/javascript'
        playerScriptTag.src = this.aliplayerSdkPath
        playerScriptTag.id = 'playerScriptTag'
        const s = document.getElementsByTagName('head')[0]
        s.appendChild(playerScriptTag)
      }
      if (playerScriptTag.loaded) {
        _this.scriptTagStatus++
      } else {
        playerScriptTag.addEventListener('load', () => {
          _this.scriptTagStatus++
          playerScriptTag.loaded = true
          _this.initAliplayer()
        })
      }
      _this.initAliplayer()
    },
    initAliplayer() {
      const _this = this
      // scriptTagStatus 为 2 的时候，说明两个必需引入的 js 文件都已经被引入，且加载完成
      if (
        _this.scriptTagStatus === 2 &&
        (_this.instance === null || _this.reloadPlayer)
      ) {
        _this.instance && _this.instance.dispose()

        // document.querySelector("#" + _this.playerId).innerHTML = "";
        // Vue 异步执行 DOM 更新，这样一来代码执行到这里的时候可能 template 里面的 script 标签还没真正创建
        // 所以，我们只能在 nextTick 里面初始化 Aliplayer
        _this.$nextTick(() => {
          /**
           * 功能组件布局配置，不传该字段使用默认布局。取值：false隐藏所有功能组件。
           * @type {[{name: string, x: number, y: number, align: string},{name: string, x: number, y: number, align: string},{name: string},{children: [{name: string, x: number, y: number, align: string},{name: string, x: number, y: number, align: string},{name: string, x: number, y: number, align: string}], name: string, x: number, y: number, align: string}]}
           */
          let skin = _this.playSkin // 默认是录播
          if (_this.isLive) {
            skin = _this.liveSkin
          }

          _this.instance = window.Aliplayer({
            id: _this.playerId,
            autoplay: _this.autoplay,
            isLive: _this.isLive,
            source: _this.source,
            cover: _this.cover,
            vid: _this.vid,
            'extraInfo': {
              'crossOrigin': 'anonymous'
            },
            playauth: _this.playauth,
            encryptType: _this.encryptType,
            playsinline: _this.playsinline,
            width: _this.width,
            height: _this.height,
            showBarTime: _this.showBarTime,
            format: _this.format,
            keyShortCuts: true,
            preload: true,// 自动加载
            rePlay: true,// 重播视频
            controlBarVisibility: _this.controlBarVisibility,
            useH5Prism: _this.useH5Prism,
            useFlashPrism: _this.useFlashPrism,
            definition: _this.definition,
            defaultDefinition: _this.defaultDefinition,
            rtmpBufferTime: 0,
            showBuffer: false,
            qualitySort: 'desc',
            language: _this.language,
            x5_video_position: _this.x5_video_position,
            x5_type: _this.x5_type,
            x5_fullscreen: _this.x5_fullscreen,
            x5_orientation: _this.x5_orientation,
            autoPlayDelay: _this.autoPlayDelay,
            autoPlayDelayDisplayText: _this.autoPlayDelayDisplayText,
            enableStashBufferForFlv: false,
            liveRetry: 0,
            hlsFrameChasing: false,
            'skinLayout': skin // 播放器按钮样式
          }, (player) => {
            if(_this.isMute){
              player.mute();
            }

            // eslint-disable-next-line no-undef
            setTimeout(() => {
              this.$emit('getPlayer', player)
            }, 2000)

            // _this.CM = player.getComponent('AliplayerDanmuComponent').CM
            _this.player = player
          })
          // 绑定事件，当 AliPlayer 初始化完成后，将编辑器实例通过自定义的 ready 事件交出去
          _this.instance.on('ready', () => {
            this.$emit('ready', _this.instance)
          })
          _this.instance.on('play', () => {
            this.$emit('play', _this.instance)
          })
          _this.instance.on('pause', () => {
            this.$emit('pause', _this.instance)
          })
          _this.instance.on('ended', () => {
            this.$emit('ended', _this.instance)
          })
          _this.instance.on('liveStreamStop', () => {
            this.$emit('liveStreamStop', _this.instance)
          })
          _this.instance.on('m3u8Retry', () => {
            this.$emit('m3u8Retry', _this.instance)
          })
          _this.instance.on('hideBar', () => {
            this.$emit('hideBar', _this.instance)
          })
          _this.instance.on('waiting', () => {
            this.$emit('waiting', _this.instance)
          })
          _this.instance.on('snapshoted', () => {
            this.$emit('snapshoted', _this.instance)
          })

          _this.instance.on('timeupdate', () => {
            this.$emit('timeupdate', _this.instance)
          })
          _this.instance.on('requestFullScreen', () => {
            this.$emit('requestFullScreen', _this.instance)
          })
          _this.instance.on('cancelFullScreen', () => {
            this.$emit('cancelFullScreen', _this.instance)
          })
          _this.instance.on('error', () => {
            this.$emit('error', _this.instance)
          })
          _this.instance.on('startSeek', () => {
            this.$emit('startSeek', _this.instance)
          })
          _this.instance.on('completeSeek', () => {
            this.$emit('completeSeek', _this.instance)
          })
          _this.instance.on('completeSeek', () => {
            this.$emit('completeSeek', _this.instance)
          })
        })
      }
    },
    /**
     * 播放视频
     */
    play() {
      this.instance.play()
    },
    /**
     * 暂停视频
     */
    pause() {
      this.instance.pause()
    },
    /**
     * 重播视频
     */
    replay() {
      this.instance.replay()
    },
    /**
     * 跳转到某个时刻进行播放
     * @argument time 的单位为秒
     */
    seek(time) {
      this.instance.seek(time)
    },
    /**
     * 获取当前时间 单位秒
     */
    getCurrentTime() {
      return this.instance.getCurrentTime()
    },
    /**
     *获取视频总时长，返回的单位为秒
     * @returns 返回的单位为秒
     */
    getDuration() {
      return this.instance.getDuration()
    },
    /**
     获取当前的音量，返回值为0-1的实数ios和部分android会失效
     */
    getVolume() {
      return this.instance.getVolume()
    },
    /**
     设置音量，vol为0-1的实数，ios和部分android会失效
     */
    setVolume(vol) {
      this.instance.setVolume(vol)
    },
    /**
     *直接播放视频url，time为可选值（单位秒）目前只支持同种格式（mp4/flv/m3u8）之间切换暂不支持直播rtmp流切换
     *@argument url 视频地址
     *@argument time 跳转到多少秒
     */
    loadByUrl(url, time) {
      this.instance.loadByUrl(url, time)
    },
    /**
     * 设置播放速度
     *@argument speed 速度
     */
    setSpeed(speed) {
      this.instance.setSpeed(speed)
    },
    /**
     * 设置播放器大小w,h可分别为400px像素或60%百分比chrome浏览器下flash播放器分别不能小于397x297
     *@argument w 播放器宽度
     *@argument h 播放器高度
     */
    setPlayerSize(w, h) {
      this.instance.setPlayerSize(w, h)
    },
    /**
     * 目前只支持HTML5界面上的重载功能,暂不支持直播rtmp流切换m3u8）之间切换,暂不支持直播rtmp流切换
     *@argument vid 视频id
     *@argument playauth 播放凭证
     */
    reloaduserPlayInfoAndVidRequestMts(vid, playauth) {
      this.instance.reloaduserPlayInfoAndVidRequestMts(vid, playauth)
    },
    reloadPlayer() {
      this.isReload = true
      this.initAliplayer()
      this.isReload = false
    }
  }
}
</script>

<style lang='less' scoped>
@import "styles";
</style>
