<template>
  <HomeListCard
    title="亚专业"
    bg-color="#2F96D6"
    :bg-img="require('assets/images/home/<USER>')"
    :list="formatList"
  />
</template>

<script>
import {HomeListCard} from "../../../ui";

export default {
  name: "SubMajor",
  components: {
    HomeListCard
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    formatList() {
      if (this.list.length > 0) {
        return this.list.map(item => {
          return {
            id: item.channelId,
            name: item.name,
            url: `/channel/home?channelId=${item.channelId}&channelTitle=${encodeURIComponent(item.name)}&mpId=${item.mpSubspecialityId}&ocsId=${item.ocsSubspecialityId}`
          }
        })
      }

      return []
    }
  },
  mounted() {
    if (this.list.length > 0) {
      const list = this.list.map(item => {
        return {
          id: item.channelId,
          name: item.name,
          url: `/channel/home?channelId=${item.channelId}&channelTitle=${encodeURIComponent(item.name)}&mpId=${item.mpSubspecialityId}&ocsId=${item.ocsSubspecialityId}`
        }
      })
      this.$store.commit('global/setChannelDataHandler', list)
    } else {
      this.$store.commit('global/setChannelDataHandler', [])
    }
  }
}
</script>

<style scoped>

</style>
