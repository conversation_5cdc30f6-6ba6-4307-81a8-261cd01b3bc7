<template>
  <div class="today_live_wrapper">
    <div class="title_wrapper">
      <div class="title_left">
        <svg-icon icon-class="today-live" class-name="icons"/>
        <span>今日直播</span>
      </div>
      <div v-if="todayFormatData.length > 1" class="title_right">
        <div
          class="slider_btn slider_left"
          :class="index > 1 ? 'slider_btn_active' : ''"
          @click="sliderHandler('left')">
          <svg-icon icon-class="home_slider_right" class-name="icons"/>
        </div>

        <span>{{ index }}/{{ todayFormatData.length }}</span>

        <div
          class="slider_btn slider_right"
          :class="index < todayFormatData.length ? 'slider_btn_active' : ''"
          @click="sliderHandler('right')">
          <svg-icon icon-class="home_slider_right" class-name="icons"/>
        </div>
      </div>
    </div>

    <div class="content">
      <div ref="list_wrapper" class="list_wrapper">
        <ul v-for="(item,indexF) in todayFormatData" :key="indexF" class="item_wrapper">
          <li v-for="itemChild in item" :key="itemChild.id" class="item_li">
            <nuxt-link
              :to='{ path: `/meeting/detail`,query: {id:itemChild.id} }'
              target='_blank'
              class="content_item_wrapper"
            >
              <div class="content_item_title text-limit-2">
                <img
                  v-if="itemChild.meetingLiveStatus === 'LI'"
                  src="~assets/images/home/<USER>"
                  alt=""
                  class="icons">
                <i v-if="itemChild.meetingLiveStatus === 'LI'" class="label-1 label">正在直播</i>

                <svg-icon v-if="itemChild.meetingLiveStatus === 'NS'" icon-class="home-live-2" class-name="icons"/>
                <i v-if="itemChild.meetingLiveStatus === 'NS'" class="label-2 label">即将直播</i>

                <span class="info">{{ itemChild.meetingName }}</span>
              </div>
              <!--              <div class="time">-->
              <!--                {{ timeStamp.timestamp_13(itemChild.startTime, 'h-m') }}-->
              <!--                - -->
              <!--                {{ timeStamp.timestamp_13(itemChild.endTime, 'h-m') }}-->
              <!--              </div>-->
              <div v-if="itemChild.reminderDates.length>0" class="time">
                今日{{ timeStamp.timestamp_13(itemChild.reminderDates[0], 'h-m') }}
              </div>
            </nuxt-link>
          </li>
        </ul>
      </div>
    </div>


    <a target="_blank" href="/meeting/home" class="today_btn" @click="moreHandler">
      点击查看更多直录播内容
    </a>
  </div>
</template>

<script>
export default {
  name: "TodayLive",
  props: {
    todayMeetingList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      index: 1
    }
  },
  computed: {
    todayFormatData() {
      if (this.todayMeetingList && this.todayMeetingList.length > 0) {
        const array = this.todayMeetingList
        const groupSize = 3;
        const result = [];

        for (let i = 0; i < array.length; i += groupSize) {
          const group = array.slice(i, i + groupSize);
          result.push(group);
        }

        return result
      } else {
        return []
      }
    }
  },
  mounted() {

  },
  methods: {
    moreHandler() {
      this.$analysys.btn_click(`查看更多直录播内容`, window.document.title)
    },
    sliderHandler(type) {
      const wrapperDom = this.$refs.list_wrapper
      if (type === 'left' && this.index > 1) {
        this.index -= 1;
        wrapperDom.style.transform = `translateX(-${this.index - 1}00%)`

      } else if (type === 'right' && (this.index < this.todayFormatData.length)) {
        wrapperDom.style.transform = `translateX(-${this.index}00%)`
        this.index += 1;
      }

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
