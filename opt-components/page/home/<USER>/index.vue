<template>
  <div class="recommend_book_wrapper">
    <div class="title">
      <svg-icon icon-class="recommend-book" class-name="icons"/>
      <span>推荐好书</span>
    </div>

    <div class="content">
      <BookCityHomeItem
        v-for="item in list"
        :key="item.id"
        :book-id="item.id"
        :title="item.name"
        :image="item.cover"
        :author-names="item.authorNames"
        @click="bookHandler(item.name)"
      />
    </div>
  </div>
</template>

<script>
import {BookCityHomeItem} from "../../../data-list";

export default {
  name: "RecommendBook",
  components: {BookCityHomeItem},
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    bookHandler(name) {
      this.$analysys.btn_click(`推荐好书-（${name}）`, '脑医汇 - 神外资讯、神介资讯 - 领先的临床神经科学互联网平台')
    }
  }
}
</script>

<style scoped lang="less">
.recommend_book_wrapper {
  border-radius: 8px;
  background: #FFF;
  padding: 16px;

  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: var(--font-color-333);
    margin-bottom: 16px;
    font-weight: 500;

    .icons {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }
  }

  .content {
    display: grid;
    gap: 16px 0;
  }
}
</style>
