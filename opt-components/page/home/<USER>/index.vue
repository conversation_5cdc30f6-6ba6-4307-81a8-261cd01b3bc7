<template>
  <div id="middle_banner" ref="middle_banner" class="middle_banner_wrapper">
    <div class="fixed_banner" :class="index < (list.length - 1) ? 'fixed_banner_right' : ''">
      <div class="middle_banner_content" ref="fixed_banner">
        <div v-for="(item,indexF) in list" :key="indexF" class="banner_item">
          <zip-img
            :width="359"
            :height="119"
            :src="item.image"
            :alt="item.name"
            @click="jumpBannerFun(item)"
          />
        </div>
      </div>
    </div>

    <div class="slider_wrapper">
      <div
        v-if="(index-1) > 0"
        class="slider slider_left"
        @click="sliderHandler('left')">
        <svg-icon icon-class="icon_slide_n" class-name="icons"/>
      </div>
      <div
        v-if="index < (list.length - 1)"
        class="slider slider_right"
        @click="sliderHandler('right')">
        <svg-icon icon-class="icon_slide_n" class-name="icons"/>
      </div>
    </div>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";
import brandAdJump from "../../../../assets/helpers/brandAdJump";

export default {
  name: "MiddleBanner",
  components: {ZipImg},
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      bannerIO: null,
      bannerLock: false,
      index: 1
    }
  },
  mounted() {
    const middleBanner = this.$refs.middle_banner

// eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && entry.target.id === "middle_banner" && !this.bannerLock) {
          this.bannerLock = true;
          this.list?.forEach(item => {
            this.$analysys.ad_exposure({
              adExtras: item.extras,
              adModule: item.module,
              exposureLocation: item.clickLocation,
              adCode: item.code,
              adId: item.adId,
              adName: item.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: item.extras,
            })
          })
        }
      })
    });

    this.bannerIO.observe(middleBanner);
  },
  beforeDestroy() {
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  },
  methods: {
    sliderHandler(type) {
      const wrapperDom = this.$refs.fixed_banner
      if (type === 'left' && this.index > 0) {
        this.index -= 1;
        wrapperDom.style.transform = `translateX(-${(this.index - 1) * (359 + 16)}px)`

      } else if (type === 'right' && (this.index < (this.list.length - 1))) {
        wrapperDom.style.transform = `translateX(-${this.index * (359 + 16)}px)`
        this.index += 1;
      }

    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
  }
}
</script>

<style scoped lang="less">
.middle_banner_wrapper {
  position: relative;


  .fixed_banner_right {
    &::before {
      content: "";
      z-index: 10;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      box-shadow: 0 0 100px 100px #FFFFFF;
    }
  }

  .fixed_banner {
    position: relative;
    overflow: hidden;
  }

  .middle_banner_content {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    transition: transform .2s ease;

    .banner_item {
      flex-shrink: 0;
      width: 359px;
      height: 119px;
      background-color: #D9D9D9;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
    }
  }

  .slider_wrapper {
    .slider {
      z-index: 11;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.2);
      border-radius: 50%;
      cursor: pointer;

      .icons {
        width: 30px;
        height: 30px;
        color: white;
      }

      &:active {
        .icons {
          color: rgb(224, 225, 225);
        }
      }
    }

    .slider_left {
      left: -15px;
      transform: rotateY(180deg) translateY(-50%);
    }

    .slider_right {
      right: -15px;
    }
  }
}
</style>
