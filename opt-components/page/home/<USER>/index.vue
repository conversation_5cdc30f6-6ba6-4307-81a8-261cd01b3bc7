<template>
  <div class="case_competition_wrapper">
    <div class="title"></div>
    <div class="content">
      <CaseCompetitionHomeItem
        v-for="item in list"
        :key="item.id"
        :image="item.coverImage"
        :title="item.name"
        :status="item.status"
        :code="item.code"
        :competition-url="item.competitionUrl"
      />
    </div>
  </div>
</template>

<script>
import {CaseCompetitionHomeItem} from "../../../data-list";

export default {
  name: "CaseCompetition",
  components: {
    CaseCompetitionHomeItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
.case_competition_wrapper {
  border-radius: 8px;
  background: linear-gradient(180deg, #FFDEBF -18.3%, #FFF 31.11%);

  .title {
    width: 100%;
    height: 32px;
    background: url("assets/images/home/<USER>") no-repeat;
    margin-bottom: 16px;
  }

  .content {
    padding: 0 10px 16px;
    box-sizing: border-box;
    display: grid;
    gap: 16px 0;
  }
}
</style>
