<template>
  <HomeCard svg="home-card-class" title="精选课程" more-btn-href="/mooc">
    <div v-if="loading" class="content">
      <el-skeleton v-for="item in 6" :key="item" style="width: 240px" animated>
        <template slot="template">
          <el-skeleton-item variant="image" style="width: 240px; height: 135px;"/>
          <div
            style="height: 130px;display: flex;flex-flow: column;justify-content: space-between;padding: 16px 0;box-sizing: border-box">
            <div>
              <el-skeleton-item variant="p" style="width: 80%"/>
              <el-skeleton-item variant="p" style="width: 50%"/>
            </div>
            <div style="display: flex; align-items: center; justify-content: space-between">
              <el-skeleton-item variant="text" style="margin-right: 16px;"/>
              <el-skeleton-item variant="text" style="width: 30%;"/>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <div v-else class="content">
      <ClassHomeDefaultItem
        v-for="item in list"
        :key="item.id"
        :class-id="item.id"
        :cover="item.cover"
        :title="item.name"
        :show-views="item.showViews"
        :money="item.money"
        :is-try-and-see="item.isTrialCourse"
      />
    </div>
  </HomeCard>
</template>

<script>
import {HomeCard} from "../../../ui";
import {ClassHomeDefaultItem} from "../../../data-list";

export default {
  name: "SelectedClass",
  components: {
    HomeCard,
    ClassHomeDefaultItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped lang="less">
.content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px 36px;
}
</style>
