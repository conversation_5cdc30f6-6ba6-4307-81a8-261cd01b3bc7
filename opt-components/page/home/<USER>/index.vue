<template>
  <HomeCard svg="home-card-case" title="最新病例" more-btn-href="/case">
    <div class="content">
      <CaseHomeDefaultItem
        v-for="(item,index) in formatList"
        :key="index"
        :info-id="item.infoId"
        :info-img="item.shareImage || item.infoImg"
        :info-title="item.infoTitle"
        :publish-date="item.publishDate"
        :author-name-list="item.authorNameList"
        :is-product="item.isProduct"
        :article-type="item.articleType"
      />
    </div>
  </HomeCard>
</template>

<script>
import {HomeCard} from "../../../ui";
import {CaseHomeDefaultItem} from "../../../data-list";

export default {
  name: "SelectedCase",
  components: {
    HomeCard,
    CaseHomeDefaultItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    formatList() {
      if (this.list.length > 0) {
        return this.list.map(item => {
          return {
            articleType: item.type === "P" ? "P" : "U",
            infoId: item.type === "P" ? item.info.id : item.article.id,
            infoImg: item.type === "P" ? (item.info.shareImage || item.info.infoImg) : item.article.cover,
            infoTitle: item.type === "P" ? item.info.title : item.article.title,
            publishDate: item.type === "P" ? Date.parse(item.info.publishDate.replace(/-/g, '/')) / 1000 : item.article.publishTime / 1000,
            authorNameList: item.type === "P" ? item.info.authorList.map(itemChild => {
              return {
                id: itemChild.id,
                authorName: itemChild.authorName,
                userAvatar: itemChild.userAvatar,
              }
            }) : [{
              id: item.article.creator.id,
              authorName: item.article.creator.realName,
              userAvatar: item.article.creator.avatarAddress
            }],
            isProduct: item.type === "P" ? item.info.productList.length > 0 : item.article.productList.length > 0
          }
        })
      }

      return []
    }
  }
}
</script>

<style scoped lang="less">
.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}
</style>
