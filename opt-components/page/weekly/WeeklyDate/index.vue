<template>
  <div class="weekly_date_wrapper">
    <div class="title"></div>
    <div ref="date_wrapper" class="date_wrapper">
      <div v-for="(item,index) in 48" :key="item" class="date_item">
        第{{ index + 1 }}期
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "WeeklyDate",
  data() {

  }
}
</script>

<style scoped lang="less">
.weekly_date_wrapper {
  position: sticky;
  top: 84px;
  border-radius: 8px;
  overflow: hidden;

  .title {
    width: 168px;
    height: 58px;
    background: url("assets/images/info/title.jpg");
  }

  .date_wrapper {
    padding: 16px 25px;
    max-height: calc(100vh - 300px);
    overflow: auto;
    background: #FFF;

    &::-webkit-scrollbar {
      width: 4px;
    }

    .date_item {
      cursor: pointer;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        color: #46B0F2;
      }
    }

    .data_active {
      color: var(--theme-color);
    }
  }

}
</style>
