<template>
  <div class="q_and_a_container" @click.stop="$emit('onClick','')">
    <div class="title text-limit-2">
      <svg-icon icon-class="q_and_a_icon" class-name="icons"/>
      <span>{{ title }}</span>
    </div>
    <div class="content text-limit-5">{{ subtitle }}</div>
    <div class="time">{{ publishDate }}</div>
  </div>
</template>

<script>
export default {
  name: "QAndAItem",
  props: {
    title: {
      type: String,
      default: ""
    },
    subtitle: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    }
  }
}
</script>

<style scoped lang="less">
.q_and_a_container {
  border-radius: 8px;
  background: #FAFAFA;
  padding: 12px;

  .title {
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    margin-bottom: 16px;
    cursor: pointer;

    .icons {
      width: 20px;
      height: 20px;
    }
  }

  .content {
    color: #666;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .time {
    color: #999EA4;
    font-size: 14px;
  }
}
</style>
