<template>
  <div class="article_container" @click.stop="$emit('onClick','')">
    <div class="image">
      <ZipImg
        :width="240"
        :height="135"
        :src="cover"
        fill
      />
      <div v-if="isVideo" class="mask">
        <svg-icon icon-class="icon_play_n" class-name="icons"/>
      </div>
      <div v-if="isHolographicSurgery" class="label">全景手术</div>
    </div>
    <div class="content">
      <div class="title text-limit-2">{{ title }}</div>
      <div class="info">
        <div class="author_name">
          <span class="name">{{ userName }}</span>
        </div>
        <div class="time">{{ publishDate }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../../../component";

export default {
  name: "ArticleItem",
  components: {ZipImg},
  props: {
    isVideo: {
      type: Boolean,
      default: false,
      require: false
    },
    isHolographicSurgery: {
      type: Boolean,
      default: false,
      require: false
    },
    cover: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    userName: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    }
  }
}
</script>

<style scoped lang="less">
.article_container {
  display: flex;
  cursor: pointer;

  .image {
    position: relative;
    width: 240px;
    height: 135px;
    margin-right: 24px;
    flex-shrink: 0;
    border-radius: 6px;
    overflow: hidden;

    .label {
      position: absolute;
      left: 8px;
      top: 8px;
      width: 64px;
      height: 23px;
      line-height: 23px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: linear-gradient(90deg, #55BCFC 0%, #319DDF 100%);
      color: #FFF;
      font-size: 12px;
    }

    .mask {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .3);
      display: flex;
      justify-content: center;
      align-items: center;

      .icons {
        width: 48px;
        height: 48px;
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-flow: column;
    justify-content: space-between;


    .title {
      font-size: 16px;
      line-height: 22px;
      padding-right: 20%;
    }

    .info {
      .author_name {
        font-size: 14px;
        color: #50789C;
        margin-bottom: 6px;

        .name {
          cursor: pointer;
          margin-right: 6px;

          &:hover {
            color: var(--theme-color);
          }
        }
      }

      .time {
        font-size: 14px;
        color: #999EA4;
      }
    }
  }
}
</style>
