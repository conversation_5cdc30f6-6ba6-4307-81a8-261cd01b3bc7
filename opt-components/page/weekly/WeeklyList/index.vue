<template>
  <div class="weekly_list_wrapper">
    <template v-for="(item,index) in list">
      <div
        :key="index"
        class="weekly_list_item">
        <div class="content_wrapper">
          <div class="title">
            <svg-icon v-if="item.code === 'elab_case'" icon-class="sfb" class-name="icon"/>
            <svg-icon v-if="item.code === 'meeting' || item.code === 'new_meeting'" icon-class="hylb" class-name="icon"/>
            <svg-icon v-if="item.code === 'answer'" icon-class="rmhd" class-name="icon"/>
            <svg-icon v-if="item.code === 'books'" icon-class="book" class-name="icon"/>
            <svg-icon v-if="item.code === 'cases'" icon-class="case" class-name="icon"/>
            <svg-icon v-if="item.code === 'guide'" icon-class="gw" class-name="icon"/>
            <svg-icon v-if="item.code === 'recruitment_training'" icon-class="rt" class-name="icon"/>
            <svg-icon v-if="item.code === 'wind_vane'" icon-class="zx" class-name="icon"/>
            <svg-icon v-if="!isNaN(Number(item.code))" icon-class="zx" class-name="icon"/>
            <span>{{ item.type }}</span>
          </div>

          <ul v-if="getType(item.code)" class="content_list">
            <li v-for="itemC in item.list" :id="`locateId${itemC.id}`" :key="itemC.id" class="content_item">
              <!--info，mp_article,answer，meeting，video，elab_case，-->
              <ArticleItem
                v-if="itemC.type === 'elab_case'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                :is-holographic-surgery="itemC.isHolographicSurgery"
                @onClick="jumpHandler('elab',itemC.id)"
              />
              <ArticleItem
                v-else-if="itemC.type === 'info'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('info',itemC.id)"
              />
              <ArticleItem
                v-else-if="itemC.type === 'mp_article'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('mp_article',itemC.id)"
              />

              <QAndAItem
                v-else-if="itemC.type === 'answer'"
                :title="itemC.title"
                :subtitle="itemC.subtitle"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('answer',itemC.id)"
              />

              <ArticleItem
                v-else-if="itemC.type === 'meeting'"
                :cover="itemC.cover"
                :title="itemC.title"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('meeting',itemC.id)"
              />

              <ArticleItem
                v-else-if="itemC.type === 'video'"
                :is-video="true"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('video',itemC.id)"
              />
            </li>
          </ul>
          <ul v-else class="content_list">
            <li v-for="itemC in item.list.slice(0,3)" :id="`locateId${itemC.id}`" :key="itemC.id" class="content_item">
              <!--info，mp_article,answer，meeting，video，elab_case，-->
              <ArticleItem
                v-if="item.code === 'recruitment_training' && itemC.type === 'info'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.hospitalGroupName"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('info',itemC.id)"
              />
              <ArticleItem
                v-else-if="itemC.type === 'elab_case'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                :is-holographic-surgery="itemC.isHolographicSurgery"
                @onClick="jumpHandler('elab',itemC.id)"
              />
              <ArticleItem
                v-else-if="itemC.type === 'info'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('info',itemC.id)"
              />
              <ArticleItem
                v-else-if="itemC.type === 'mp_article'"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('mp_article',itemC.id)"
              />

              <QAndAItem
                v-else-if="itemC.type === 'answer'"
                :title="itemC.title"
                :subtitle="itemC.subtitle"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('answer',itemC.id)"
              />

              <ArticleItem
                v-else-if="itemC.type === 'meeting'"
                :cover="itemC.cover"
                :title="itemC.title"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('meeting',itemC.id)"
              />

              <ArticleItem
                v-else-if="itemC.type === 'video'"
                :is-video="true"
                :cover="itemC.cover"
                :title="itemC.title"
                :user-name="itemC.name"
                :publish-date="timeStamp.timestampFormat(itemC.publishDate / 1000)"
                @onClick="jumpHandler('video',itemC.id)"
              />
              <div v-else-if="item.code === 'books'" class="books_search_item cursor" @click="jumpHandler('book',itemC.id)">
                <div class="image">
                  <zip-img
                    :width="220"
                    :height="220"
                    :src="itemC.cover"
                    fill
                  />
                </div>
                <div class="item_content">
                  <div class="content_title text-limit-1" v-html="itemC.title"></div>
                  <div class="books_info">
                    <div class="info_left">
                      <p v-if="itemC.author" class="author_tips">作者：<span v-html="itemC.author"></span></p>
                      <p v-if="itemC.translator" class="author_tips">译者：<span v-html="itemC.translator"></span></p>
                      <p v-if="itemC.pressName" class="author_tips">出版社：<span v-html="itemC.pressName"></span></p>
                    </div>
                    <div class="info_right">
                      <div class="price">
                        <p>¥</p>
                        <p class="number">{{ itemC.price }}</p>
                      </div>
                      <!--          <div v-if="discountPrice && price" class="old_price">-->
                      <!--            <p>¥</p>-->
                      <!--            <p class="number">{{ price }}</p>-->
                      <!--          </div>-->
                    </div>
                  </div>

                  <div v-if="itemC.introduction" class="book_introduction">
                    <div class="introduction_content text-limit-5" v-html="itemC.introduction"></div>
                    <div class="book_label">
                      <svg-icon icon-class="book_label" class-name="label_icon"/>
                      <span>图书简介</span>
                    </div>
                  </div>
                </div>
              </div>

            </li>
            <li v-if="item.list.length > 3" class="more_btn" @click="moreHandle(item.code)">- 查看更多 -</li>
          </ul>
        </div>
      </div>
    </template>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import ShortVideoPlayback from "../../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import ArticleItem from "./ArticleItem/index.vue";
import QAndAItem from "./QAndAItem/index.vue";
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "WeeklyList",
  components: {ZipImg, ArticleItem, QAndAItem, ShortVideoPlayback},
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.$nextTick(() => {
      const targetDom = document.getElementById(`locateId${this.$route.query.locateId}`)
      if (targetDom) {
        window.scrollTo({top: (targetDom.offsetTop - 60), behavior: 'smooth'})
      }
    })
  },
  methods: {
    jumpHandler(type, id) {
      console.log(type, id)
      switch (type) {
        case 'info': {
          window.open(`/info/detail?id=${id}`)
          break;
        }
        case 'mp_article': {
          window.open(`/case/detail-ugc?id=${id}`)
          break;
        }
        case 'elab' : {
          window.open(`/elabweb/detail?id=${id}`)
          break;
        }
        case 'answer' : {
          window.open(`/topic-circle/answerhome/${id}`)
          break;
        }
        case 'meeting' : {
          window.open(`/meeting/detail?id=${id}`)
          break;
        }
        case 'video' : {
          this.$store.commit('bms/setBmsHomeShortVideoHandler', id)
          break;
        }
        case 'book' : {
          window.open(`/mall/product/${id}`)
          break;
        }
        default: {
          window.open(`/`)
        }
      }
    },
    getType (type) {
      const arr = ['new_meeting','books','recruitment_training','wind_vane','cases','guide']
      return !arr.includes(type)
    },
    moreHandle (code) {
      const homeUrl = {
        'new_meeting': '/meeting/home',
        'guide': '/guidance',
        'recruitment_training': '/recruitment-training',
        'books': '/mall',
        'wind_vane': '/special/detail?id=584',
        'cases': '/case',
      }
      window.open(location.origin + homeUrl[code], '_blank');

    }
  }
}
</script>

<style scoped lang="less">
.weekly_list_wrapper {
  .more_btn {
    width: 100%;
    padding-top: 24px;
    text-align: center;
    color: #999;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; /* 112.5% */
    letter-spacing: 0.1px;
  }
  .weekly_list_item {
    padding: 24px;
    border-radius: 8px;
    background: #FFF;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .content_wrapper {
      .title {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 24px;

        .icon {
          width: 28px;
          height: 28px;
          margin-right: 5px;
        }
      }

      .content_list {
        .content_item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .info_type_wrapper {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        .type_name {
          font-size: 16px;
          position: relative;
          margin-bottom: 16px;

          &::before {
            content: "";
            width: 5px;
            height: 12px;
            background: #0581CE;
            position: absolute;
            left: -24px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
}
.books_search_item {
  display: flex;
  align-items: start;

  .image {
    border-radius: 8px;
    background: #F4F6F8;
    overflow: hidden;
    position: relative;
    width: 220px;
    height: 220px;
    flex-shrink: 0;
  }

  .item_content {
    margin-left: 16px;
    flex: 1;

    .content_title {
      color: #333;
      font-size: 16px;
      line-height: 21px;
      margin-bottom: 12px;
      font-weight: 600;
    }

    .books_info {
      min-height: 54px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .info_left {
        .author_tips {
          color: #999EA4;
          font-size: 12px;
          line-height: 18px;
        }
      }

      .info_right {
        display: flex;
        align-items: center;

        .price {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #0581CE;
          font-weight: 500;
          gap: 2px;

          .number {
            font-size: 18px;
            font-weight: 600;
          }
        }
        .old_price{
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 12px;
          color:#999999;
          font-weight: 500;
          gap: 2px;
          margin-left: 10px;
          text-decoration-line:line-through;

          .number {
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
    }

    .book_introduction {
      height: 121px;
      border-radius: 8px;
      background: #F4F6F8;
      padding: 8px 8px 26px;
      position: relative;
      box-sizing: border-box;

      .introduction_content {
        color: #708AA2;
        font-size: 12px;
        line-height: 18px;
      }

      .book_label {
        position: absolute;
        right: 10px;
        bottom: 6px;
        color: #B3C2D0;
        font-size: 12px;

        .label_icon {
          width: 12px;
          height: 12px;
        }
      }
    }

  }
}
</style>
