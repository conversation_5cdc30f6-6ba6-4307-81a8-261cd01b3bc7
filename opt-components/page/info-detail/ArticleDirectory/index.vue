<template>
  <div id="article_directory_container" style="position: absolute;left: calc(-24px + -32px + 5px)">
    <div class="article_directory_wrapper">
      <div class="directory_btn" @click="isShow = !isShow">
        <img v-show="!isShow" src="~assets/images/info/directory.png" alt="">
        <img v-show="isShow" src="~assets/images/info/directory2.png" alt="">
      </div>

      <transition name='el-fade-in'>
        <div v-show="isShow" class="directory_content_wrapper">
          <div  class="directory_content">
            <div class="arrow"/>
            <div
              v-for='(item,index) in list'
              :key='index'
              :top='item.top'
              class='titleFirst catalogue-first'
            >
              <h1 v-if='item.label === "H1"' @click='scrollTopHandler(item.top,item.labelName)'>{{
                  item.labelName
                }}</h1>
              <div v-if='item.label === "H2"' class='second' @click='scrollTopHandler(item.top,item.labelName)'>
                <h2>{{ item.labelName }}</h2>
              </div>
              <div v-if='item.label === "H3"' class='three' @click='scrollTopHandler(item.top,item.labelName)'>
                <h3>{{ item.labelName }}</h3>
              </div>
              <div v-if='item.label === "H4"' class='four' @click='scrollTopHandler(item.top,item.labelName)'>
                <h4>{{ item.labelName }}</h4>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: "ArticleDirectory",
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isShow: false
    }
  },
  methods: {
    scrollTopHandler(top, name) {
      this.activeId = name
      window.scrollTo({
        top,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style scoped lang="less">
#article_directory_container {
  display: none;
}

.article_directory_wrapper {
  width: 32px;
  position: fixed;
  top: 80vh;
  z-index: 10;

  .directory_btn {
    width: 32px;
    height: 70px;
    cursor: pointer;

    img {
      width: 100%;
      height: auto;
    }
  }

  .directory_content_wrapper {
    width: 264px;
    height: 415px;
    position: absolute;
    left: calc(100% + 24px - 5px);
    bottom: calc(-100% + 18px);
  }

  .directory_content {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 16px;
    border-radius: 8px;
    background: #464646;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      width: 4px;
      background: #464646 !important;
    }

    &::-webkit-scrollbar-thumb {
      background: #676C74;
    }


    .arrow {
      position: absolute;
      left: -10px;
      bottom: 62px;
      width: 0;
      height: 0;
      transform: rotate(-180deg);
      border-style: solid;
      border-width: 10px 0 10px 10px;
      border-color: transparent transparent transparent #464646;
    }


    .titleFirst {
      //margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      h1 {
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: #F8F8F8;
        cursor: pointer;
        transition: all .3s;
        margin-top: 20px;
        margin-bottom: 12px;

        &:first-child {
          margin-top: 0;
        }

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .second {
      h2 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #F8F8F8;
        margin-left: 5px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .three {
      h3 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #F8F8F8;
        margin-left: 20px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .four {
      h4 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #F8F8F8;
        margin-left: 30px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .is_active {
      h1 {
        color: #0582CE;
      }

      h2 {
        color: #0582CE;
      }

      h3 {
        color: #0582CE;
      }

      h4 {
        color: #0582CE;
      }
    }
  }
}
</style>
