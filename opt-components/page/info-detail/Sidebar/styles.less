.sidebar_wrapper {
  position: sticky;
  left: 0;
  top: calc(60px + 24px);
  border-radius: 49px;
  background: #FFF;
  width: 66px;
  padding: 24px 0;
  user-select: none;
  float: left;
  margin-left: calc(0px - (66px + 24px + 24px));
  z-index: 10;

  .side_handle {
    .sidebar_item {
      text-align: center;
      margin-bottom: 16px;
      cursor: pointer;

      &:last-child {
        margin-bottom: 0;
      }

      .sidebar_icon {
        width: 25px;
        height: 25px;
        color: #333333;
        transition: all .3s;
      }

      .tips {
        color: #333;
        font-size: 14px;
        margin-top: 4px;
        line-height: 22px;
        transition: all .3s;
      }

      &:hover {
        .sidebar_icon, .tips {
          color: #0581CE;
        }
      }
    }

    .active_sidebar {
      .sidebar_icon, .tips {
        color: #0581CE;
      }
    }
  }


  .hr {
    margin: 14px auto 12px;
    width: 24px;
    height: 1px;
    background: #E8E8E8;
  }

  .share_tips {
    color: #999EA4;
    font-size: 12px;
    margin-bottom: 12px;
    text-align: center;
  }

  .share_list {
    display: flex;
    flex-flow: column;
    align-items: center;
    gap: 16px 0;

    .share_icons {
      width: 34px;
      height: 34px;
      cursor: pointer;
    }
  }
}
