<template>
  <div id="sidebar_wrapper_tool_bars_id" class="sidebar_wrapper">
    <div class="side_handle">
      <div class="sidebar_item" @click="commentHandler">
        <SvgIcon icon-class="side-1" class-name="sidebar_icon"/>
        <div class="tips">{{ comments > 0 ? comments : '评论' }}</div>
      </div>
      <div v-if="isEnableLikeButton" :class="isDigg ? 'active_sidebar' : ''" class="sidebar_item"
           @click="likeHandler(1)">
        <SvgIcon icon-class="side-2" class-name="sidebar_icon"/>
        <div class="tips">{{ diggNum > 0 ? diggNum : '点赞' }}</div>
      </div>
      <div :class="isCollect ? 'active_sidebar' : ''" class="sidebar_item" @click="collectHandler(1)">
        <SvgIcon icon-class="side-3" class-name="sidebar_icon"/>
        <div class="tips">{{ collectNum > 0 ? collectNum : '收藏' }}</div>
      </div>
    </div>
    <div class="hr"></div>
    <div class="share_tips">分享至</div>
    <div class="share_list">
      <el-popover
        placement='right-start'
        popper-class='popper-box'
        title='微信扫码分享'
        trigger='click'
        width='50'
        :visible-arrow="false"
      >
        <img
          :src='wxUrl'
          alt='微信分享'
          style='width:100%; text-align: center'
        />
        <svg-icon slot='reference' icon-class="info_wx" class-name="share_icons" @click="shareHandler('wx')"/>
      </el-popover>
      <svg-icon icon-class="info-weibo" class-name="share_icons" @click="shareHandler('wb')"/>
    </div>
  </div>
</template>

<script>
import qrcade from "qrcode";

export default {
  name: "SidebarComponent",
  props: {
    isEnableLikeButton: {
      type: Boolean,
      default: true
    },
    articleType: {
      type: String,
      default: "pgc"
    },
    comments: {
      type: Number,
      default: 0
    },
    showDiggs: {
      type: Number,
      default: 0
    },
    showCollects: {
      type: Number,
      default: 0
    },
    isArticleDigg: {
      type: Boolean,
      default: false
    },
    isArticleCollect: {
      type: Boolean,
      default: false
    },
    metaDescription: {
      type: String,
      default: ""
    }
  },
  watch: {
    isArticleDigg(newValue) {
      this.likeHandler(2)
    },
    isArticleCollect(newValue) {
      this.collectHandler(2)
    }
  },
  data() {
    return {
      diggNum: this.showDiggs,
      collectNum: this.showCollects,
      isDigg: this.isArticleDigg,
      isCollect: this.isArticleCollect,
      wxUrl: ''
    }
  },
  methods: {
    commentHandler() {
      const DOM = window.document.getElementById('PostInput')
      if (DOM) {
        DOM.focus();
      }
    },
    likeHandler(type) {
      if (type === 1) {
        this.$emit('articleLikeHandler')
      } else if (type === 2) {
        if (this.isDigg) {
          this.isDigg = false;
          this.diggNum = this.diggNum > 0 ? this.diggNum - 1 : 0;
        } else {
          this.isDigg = true;
          this.diggNum = this.diggNum + 1;
        }
      }
    },
    collectHandler(type) {
      if (type === 1) {
        this.$emit('articleCollectHandler')
      } else if (type === 2) {
        if (this.isCollect) {
          this.isCollect = false;
          this.collectNum = this.collectNum > 0 ? this.collectNum - 1 : 0;
        } else {
          this.isCollect = true;
          this.collectNum = this.collectNum + 1;
        }
      }
    },
    shareHandler(type) {
      switch (type) {
        case 'wx': {
          // eslint-disable-next-line no-undef
          const url = location.href
          qrcade.toDataURL(url).then((img) => {
            this.wxUrl = img
          })
          this.$analysys.BM_share({
            share_content_id: this.$route.query.id,
            share_content_type: this.articleType === 'pgc' ? 'info' : 'mp'
          })
          break;
        }
        case 'wb': {
          //  event.preventDefault();防止链接打开 URL：
          let _shareUrl = '//service.weibo.com/share/share.php?'
          _shareUrl += 'url=' + encodeURIComponent(document.location) // 分享地址
          _shareUrl += '&title=' + encodeURIComponent(document.title) // 分享标题
          window.open(_shareUrl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes')
          this.$analysys.BM_share({
            share_content_id: this.$route.query.id,
            share_content_type: this.articleType === 'pgc' ? 'info' : 'mp'
          })
          break;
        }
        case 'qq': {
          const share = {
            title: document.title,
            desc: this.metaDescription,
            image_url: [''],
            share_url: document.location // 注意 localhost 生成失败
          }
          // eslint-disable-next-line camelcase
          const image_urls = share.image_url.map(function (image) {
            return encodeURIComponent(image)
          })
          // 跳转地址
          const qqurl = 'https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=' + encodeURIComponent(share.share_url) + '&title=' + share.title + '&pics=' + image_urls.join('|') + '&summary=' + share.desc
          window.open(qqurl, '_blank', 'height=300,width=500', 'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
          )
          this.$analysys.BM_share({
            share_content_id: this.$route.query.id,
            share_content_type: this.articleType === 'pgc' ? 'info' : 'mp'
          })
          break;
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
