<template>
  <div class="RelatedArticles_wrapper">
    <div class="title">相关文章</div>
    <div class="related_list">
      <ArticlesItem
        v-for="item in list"
        :key="item.articleId"
        :article-type="item.ffrom"
        :article-id="item.articleId"
        :title="item.title"
        :author="item.author"
        :cover="item.cover"
      />
    </div>
    <transition name='el-fade-in-linear'>
      <RecommendDialog
        v-if='isRecommendDialog'
        :recommend-list='todayRecommendList'
        :info-title="infoTitle"
        @cancel='isRecommendDialog = false'
      />
    </transition>
  </div>
</template>

<script>
import RecommendDialog from "../../../../components/optimize-components/page-components/info/RecommendDialog/index.vue";
import {getRandomInfoRelatedContent} from "../../../../api/article";
import ArticlesItem from "./ArticlesItem/index.vue";

export default {
  name: "RelatedArticles",
  components: {ArticlesItem, RecommendDialog},
  props: {
    articleType: {
      type: String,
      default: 'pgc'
    },
    list: {
      type: Array,
      default: () => []
    },
    infoTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      todayRecommendList: [],
      isRecommendDialog: false
    }
  },
  mounted() {
    this.getRandomInfoRelatedContentHandler()
  },
  methods: {
    // 获取今日推荐
    getRandomInfoRelatedContentHandler() {
      const time = new Date().getMonth() + 1 + '-' + new Date().getDate()
      const obj = JSON.parse(JSON.stringify(window.localStorage.getItem('RandomInfoRelatedContent')))
      if (!obj || time + '' !== obj + '') {
        this.$axios.$request(getRandomInfoRelatedContent({
          infoId: this.$route.query.id,
          meetingLimit: 10,
          courseLimit: 30
        })).then(res => {
          if (res.code === 1) {
            this.todayRecommendList = res.list
            this.isRecommendDialog = true
            window.localStorage.setItem('RandomInfoRelatedContent', time)
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
