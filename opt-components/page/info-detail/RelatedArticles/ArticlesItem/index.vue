<template>
  <a target="_blank" :href="articleType === 'pgc' ? `/info/detail?id=${articleId}` : `/case/detail-ugc?id=${articleId}` " class="article_item_wrapper">
    <div class="image">
      <zip-img
        :width="186"
        :height="104"
        :src="cover"
        :alt="title"
        fill
      />
    </div>
    <div class="content">
      <p class="title text-limit-2">{{ title }}</p>
      <p class="author text-limit-2">{{ author }}</p>
    </div>
  </a>
</template>

<script>
import ZipImg from "../../../../component/ZipImg/index.vue";

export default {
  name: "ArticlesItem",
  components: {ZipImg},
  props: {
    articleType: {
      type: String,
      default: 'pgc'
    },
    articleId: {
      type: Number,
      default: null
    },
    title: {
      type: String,
      default: ''
    },
    author: {
      type: String,
      default: ''
    },
    cover: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="less">
.article_item_wrapper {
  width: 100%;
  flex-shrink: 0;
  background: #F8F8F8;
  border-radius: 8px;
  overflow: hidden;
  display: block;

  .image {
    padding-bottom: 56.25%;
    position: relative;

    img{
      transition: all .2s;
    }
  }

  .content {
    padding: 16px 8px;
  }

  .title {
    color: #333;
    font-size: 14px;
    line-height: 20px;
    height: 40px;
    margin-bottom: 28px;
  }

  .author {
    color: #999EA4;
    font-size: 12px;
  }

  &:hover{
    .content > .title{
      color: #0581CE;
    }
    .image > img{
      scale: 1.05;
    }
  }

}
</style>
