.info_top_title_wrapper {
  margin-bottom: 16px;

  .top_time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    position: relative;

    .time {
      color: #C7C7C7;
      font-size: 14px;
    }

    .sub_major {
      position: absolute;
      right: 0;
      top: -3px;
      border-radius: 4px;
      background: #F4F6F8;
      padding: 4px 8px;
      display: flex;
      overflow: hidden;
      box-sizing: border-box;
      transition: all .3s;
      z-index: 10;

      .major_list {
        display: flex;
        flex-flow: column;
        cursor: default;
        user-select: none;

        .major_name {
          color: #708AA2;
          font-size: 12px;
          margin-bottom: 8px;
          text-align: left;

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            color: #0581CE;
          }

          &:active {
            color: #0581CE;
          }
        }
      }

      .major_select {
        flex-shrink: 0;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: rgba(112, 138, 162, 0.20);
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 1.5px 0 0 8px;

        .major_icon {
          width: 8px;
          height: 8px;
          color: #708AA2;;
          transition: all .2s;
        }
      }


    }
  }

  .info_title {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    margin-bottom: 16px;
    display: flex;
    align-items: start;

    .auth_title_wrapper {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      display: flex;
      justify-content: start;
      align-items: center;
    }
  }

  .author_list {

  }

  .title_source {
    color: #999EA4;
    font-size: 14px;
    line-height: 24px; /* 171.429% */
    margin-bottom: 16px;
  }
}
