
.author_wrapper {
  display: inline-block;
  flex-shrink: 0;
  border-radius: 4px;
  background: #F8F8F8;
  padding: 8px;
  box-sizing: border-box;

  .author_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .author_content {
    display: flex;
    align-items: center;

    .image {
      flex-shrink: 0;
      width: 39px;
      height: 39px;
      margin-right: 8px;
      border-radius: 50%;
      overflow: hidden;
    }

    .info {
      .title {
        font-size: 14px;
        color: #333;
        margin-bottom: 7px;
      }

      .company {
        color: #999EA4;
        font-size: 12px;
      }
    }
  }

  .follow {
    user-select: none;
    margin-left: 10px;
    flex-shrink: 0;
    width: 45px;
    height: 24px;
    border: 0.5px solid #0581CE;
    background: rgba(5, 129, 206, 0.05);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0581CE;
    font-size: 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all .2s;

    &:hover {
      background: rgba(70, 176, 242, 0.05);
      color: #46B0F2;
    }
  }
}

.popover_content_wrapper {
  padding: 8px;

  .author_content {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .image {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      margin-right: 16px;
      border-radius: 50%;
      overflow: hidden;
    }

    .info {
      .title {
        font-size: 14px;
        color: #333;
        margin-bottom: 7px;
      }

      .company {
        color: #999EA4;
        font-size: 12px;
      }
    }
  }

  .author_info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info_item {
      flex-shrink: 0;
      text-align: center;
      max-width: 25%;
      font-size: 16px;
      color: #333;

      .tips {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .author_btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 26px;

    .home_page {
      flex-shrink: 0;
      width: 85px;
      height: 30px;
      background: white;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #0581CE;
      font-size: 14px;
      cursor: pointer;
      border-radius: 4px;
      border: 1px solid #0581CE;

      &:hover {
        background: rgba(5, 129, 206, 0.05);
      }
    }

    .follow_btn {
      user-select: none;
      flex-shrink: 0;
      width: 85px;
      height: 30px;
      border-radius: 4px;
      border: 1px solid #0581CE;
      background: #0581CE;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #FFF;
      font-size: 14px;
      cursor: pointer;
      &:hover{
        border: 1px solid #46B0F2;
        background: #46B0F2;
      }
      &:active{
        border: 1px solid #0581CE;
        background: #0581CE;
      }
    }
  }
}

.default_author {
  display: inline-block;
  flex-shrink: 0;
  margin-right: 16px;
  margin-bottom: 10px;

  .author_name {
    color: #676C74;
    font-size: 16px;
  }

  .author_name_a {
    color: #0581CE;
    font-size: 16px;
    &:hover{
      color:#46B0F2;
    }
  }
}
