<template>
  <el-popover
    placement="bottom-start"
    width="210"
    trigger="hover"
    :visible-arrow="false"
    :disabled="popoverDisabled"
  >
    <div class="popover_content_wrapper">
      <div class="author_content">
        <div class="image">
          <ZipImg
            :width="39"
            :height="39"
            :type-user="true"
            :src="avatarAddress"
          />
        </div>
        <div class="info">
          <p class="title text-limit-1">{{ authorName }}</p>
          <p class="company text-limit-1">{{ company }}</p>
        </div>
      </div>

      <div class="author_info">
        <div class="info_item">
          <p class="text-limit-1">{{ postNum }}</p>
          <p class="tips">发布</p>
        </div>
        <div class="info_item">
          <p class="text-limit-1">{{ fans }}</p>
          <p class="tips">粉丝</p>
        </div>
        <div class="info_item">
          <p class="text-limit-1">{{ userDiggs }}</p>
          <p class="tips">获赞</p>
        </div>
      </div>

      <div v-if="authorUserId" class="author_btn">
        <a target="_blank" :href="`/user-center?profileUserId=${authorUserId}`" class="home_page">个人主页</a>
        <div class="follow_btn" @click.stop="followHandler">
          {{ followFlag === 'FD' ? '已关注' : '关注' }}
        </div>
      </div>
    </div>


    <div v-if="authorType === 1" slot="reference" class="default_author">
      <div v-if="!authorUserId" class="author_name">{{ authorName }}</div>
      <a
        v-else
        class="author_name_a"
        target="_blank"
        :href="`/user-center?profileUserId=${authorUserId}`">
        {{ authorName }}
      </a>
    </div>

    <div v-if="authorType === 2" slot="reference" class="author_wrapper" :style="styles">
      <div class="author_box">
        <div class="author_content">
          <div class="image" :class="authorUserId ? 'cursor' : ''" @click="jumpUser">
            <ZipImg
              :width="39"
              :height="39"
              :type-user="true"
              :src="avatarAddress"
            />
          </div>
          <div class="info">
            <p class="title text-limit-1">{{ authorName }}</p>
            <p class="company text-limit-1">{{ company }}</p>
          </div>
        </div>
        <div v-if="authorUserId" class="follow" @click.stop="followHandler">
          {{ followFlag === 'FD' ? '已关注' : '关注' }}
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script>
import {ZipImg} from "../../../../component";

export default {
  name: "AuthorItem",
  components: {ZipImg},
  props: {
    popoverDisabled: {
      type: Boolean,
      default: false
    },
    authorType: {
      type: Number,
      default: 1
    },
    styles: {
      type: String,
      default: 'width:240px;margin:0 16px 16px 0;'
    },
    avatarAddress: {
      type: String,
      default: ""
    },
    authorName: {
      type: String,
      default: ""
    },
    company: {
      type: String,
      default: ""
    },
    postNum: {
      type: Number,
      default: 0
    },
    fans: {
      type: Number,
      default: 0
    },
    userDiggs: {
      type: Number,
      default: 0
    },
    authorUserId: {
      type: Number,
      default: null
    },
    followStatus: {
      type: String,
      default: "NF"
    }
  },
  data() {
    return {
      followFlag: this.followStatus
    }
  },
  methods: {
    followHandler() {
      this.followFlag = this.followFlag === 'FD' ? 'NF' : 'FD';

      this.$store.dispatch('follow', this.authorUserId)
    },
    jumpUser() {
      if (this.authorUserId) {
        window.open(`/user-center?profileUserId=${this.authorUserId}`)
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
