<template>
  <div class="info_top_title_wrapper">
    <div class="top_time">
      <div class="time">{{ publishDate }}发布 | {{ $tool.formatterNum(showViews) }}阅读</div>
      <div
        v-if="attrs.length>0" class="sub_major"
        :style="isExpand ? {} : {maxHeight:'25px'}"
        @click="isExpand = !isExpand">
        <div class="major_list">
          <div
            v-for="item in attrs"
            :key="item.name"
            class="major_name"
          >
            {{ item.name }}
          </div>
        </div>
        <div v-if="attrs.length>1" class="major_select">
          <svg-icon
            :style="isExpand ? {transform: 'rotateZ(-180deg)'} : {}"
            icon-class="search_more"
            class-name="major_icon cursor"/>
        </div>
      </div>

    </div>
    <div class="info_title">
      <div v-if="bmsAuth === 1" class="auth_title_wrapper">
        <svg-icon icon-class="auth-new" class-name="auth_title" style="width: 20px;height: 20px"/>
      </div>
      <p>{{ title }}</p>
    </div>
    <div class="author_list">
      <AuthorItem
        v-for="item in authorList"
        :key="item.authorId"
        :author-type="template === 'guide' ? 1 : 2"
        :avatar-address="item.avatarAddress || item.headImage"
        :author-name="item.authorName"
        :company="item.company"
        :post-num="item.postNum"
        :fans="item.fans"
        :user-diggs="item.userDiggs"
        :author-user-id="item.authorUserId"
        :follow-status="item.followStatus"
        :popover-disabled="!item.authorUserId"
      />
    </div>

    <div v-if="guideSource" class="title_source"><span style="font-weight: bold">出处: </span>{{ guideSource }}</div>

    <Weekly
      v-if="Object.keys(articleHonerInfo).length>0"
      :article-honer-info="articleHonerInfo"
      :article-type="articleType"/>

  </div>
</template>

<script>
import AuthorItem from "./AuthorItem/index.vue";
import Weekly from "./Weekly/index.vue";

export default {
  name: "InfoTopTitle",
  components: {AuthorItem, Weekly},
  props: {
    template: {
      type: String,
      default: ''
    },
    guideSource: {
      type: String,
      default: ""
    },
    articleType: {
      type: String,
      default: "pgc"
    },
    title: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    },
    showViews: {
      type: Number,
      default: 0
    },
    attrs: {
      type: Array,
      default: () => []
    },
    authorList: {
      type: Array,
      default: () => []
    },
    articleHonerInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    bmsAuth: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isExpand: false
    }
  },
  mounted() {
    window.addEventListener('click', this.handleMouseUp, false);
  },
  destroyed() {
    window.removeEventListener('click', this.handleMouseUp, false)
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleMouseUp, false)
  },
  methods: {
    handleMouseUp(e) {
      const self = document.querySelector('.sub_major')?.contains(e.target)
      if (!self) {
        this.isExpand = false;
      }
    }
  },
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
