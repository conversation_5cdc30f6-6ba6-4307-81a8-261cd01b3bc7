<template>
  <div class="weekly_wrapper">
    <div class="weekly_tips_wrapper cursor" @click="popHandler">
      <div class="weekly_left">
        <SvgIcon icon-class="weekly_icon" class-name="weekly_icon"/>
        <span>{{ articleHonerInfoStr }}</span>
      </div>
      <SvgIcon
        :style="weeklyVisible ? 'transform: rotateZ(-180deg)' : ''"
        icon-class="weekly_select"
        class-name="weekly_select_icon"/>
    </div>
    <transition name="el-fade-in-linear">
      <div v-show="weeklyVisible" class="weekly_pop">
        <div v-if="articleHonerInfo.weekly">
          <div class="weekly_tips_wrapper cursor" :style="articleHonerInfo.collect ? {marginBottom:'16px'} : {}"
               @click="weeklyHandler">
            <div class="weekly_left">
              <SvgIcon icon-class="weekly_sl" class-name="weekly_icon"/>
              <span class="text-limit-1"><b>周刊收录</b> {{ articleHonerInfo.weekly.weeklyName }}</span>
            </div>
            <SvgIcon style="transform: rotateZ(-90deg)" icon-class="weekly_select" class-name="weekly_select_icon"/>
          </div>
        </div>
        <div v-if="articleHonerInfo.collect">
          <div class="weekly_tips_wrapper cursor" @click="collectionHandler">
            <div class="weekly_left">
              <SvgIcon icon-class="collection_weekly" class-name="weekly_icon"/>
              <span><b>达人收藏</b> {{
                  articleHonerInfo.collect.collectorNameList[0] + (articleHonerInfo.collect.collectorNameList.length > 1 ? `等${articleHonerInfo.collect.collectorNameList.length}位达人已收藏` : '')
                }}</span>
            </div>
            <SvgIcon
              :style="weeklyCollection ? 'transform: rotateZ(-180deg)' : 'transform: rotateZ(0deg)'"
              icon-class="weekly_select"
              class-name="weekly_select_icon"/>
          </div>
          <CollapseTransition>
            <div v-show="weeklyCollection" class="collection_wrapper">
              <p class="collection_title">以下达人已收藏</p>
              <ul class="collection_list">
                <li class="collection_item">
                  <AuthorItem
                    v-for="(item,index) in collectData.list"
                    :key="index"
                    :author-type="2"
                    styles="width:calc(100% - 8px);margin:0 8px 8px 0"
                    :popover-disabled="true"
                    :avatar-address="item.userAvatar"
                    :author-name="item.realName"
                    :company="item.company"
                    :author-user-id="item.userId"
                    :follow-status="item.isFollow ? 'FD' : 'NF'"
                  />
                </li>
                <li
                  v-show="current < collectData.page.totalPage"
                  class="more_btn cursor"
                  @click="moreHandler">
                  查看更多
                </li>
              </ul>
            </div>
          </CollapseTransition>

        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import AuthorItem from "../AuthorItem/index.vue";
import {getArticleCollectionExpertPage} from "../../../../../api/article";

export default {
  name: "WeeklyComponent",
  components: {AuthorItem, CollapseTransition},
  props: {
    articleType: {
      type: String,
      default: "pgc"
    },
    articleHonerInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      current: 1,
      weeklyVisible: false,
      weeklyCollection: true,
      collectData: {
        list: [],
        page: {}
      }
    }
  },
  computed: {
    articleHonerInfoStr() {
      let str = ""
      Object.keys(this.articleHonerInfo).forEach(function (key) {
        str += (key === 'collect' ? '达人收藏' : key === 'weekly' ? '周刊收录' : '') + "、"
      })

      str = str.substring(0, str.length - 1)

      if (Object.keys(this.articleHonerInfo).length > 1) {
        str = str + Object.keys(this.articleHonerInfo).length + "项荣誉"
      }
      return str
    }
  },
  mounted() {
    this.getCollection({pageNo: 1})

    window.addEventListener('click', this.handleMouseUp, false);
  },
  destroyed() {
    window.removeEventListener('click', this.handleMouseUp, false)
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleMouseUp, false)
  },
  methods: {
    handleMouseUp(e) {
      const self = document.querySelector('.weekly_wrapper')?.contains(e.target)
      if (!self) {
        this.weeklyVisible = false;
      }
    },
    popHandler() {
      this.weeklyVisible = !this.weeklyVisible;
    },
    weeklyHandler() {
      window.open(`/info/weekly?weeklyId=${this.articleHonerInfo.weekly.weeklyId}&locateId=${this.$route.query.id}`)
    },
    collectionHandler() {
      this.weeklyCollection = !this.weeklyCollection
    },
    getCollection({pageNo = 1}) {
      this.$axios.$request(getArticleCollectionExpertPage({
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.query.id,
        articleType: this.articleType === 'pgc' ? 'I' : 'A',
        pageNo,
        pageSize: 5
      })).then(res => {
        if (res.code === 1) {
          this.collectData.list = this.collectData.list.concat(res.list)
          this.collectData.page = res.page
        }
      })
    },
    moreHandler() {
      this.current += 1;
      this.getCollection({pageNo: this.current})
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles.less";
</style>
