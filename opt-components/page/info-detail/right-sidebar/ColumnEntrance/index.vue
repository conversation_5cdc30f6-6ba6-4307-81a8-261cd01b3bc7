<template>
  <Card>
    <CardTitle title="来自于专栏"/>
    <ul class="column_list">
      <li v-for="item in articleSpecials" :key="item.id" class="column_item">
        <a
          target="_blank"
          :href="`/column/detail?id=${item.id}&type=${item.type}`"
          class="title text-limit-2">{{ item.title }}</a>
        <div class="content">
          <div class="content_tips">{{ $tool.formatterNum(item.views)}}阅读 | {{$tool.formatterNum(item.contents)}}内容</div>
          <a target="_blank" :href="`/column/detail?id=${item.id}&type=${item.type}`" class="btn">
            <span>进入专栏</span>
            <svg-icon icon-class="info_column_go" class-name="go_icon"/>
          </a>
        </div>
      </li>
    </ul>
  </Card>
</template>

<script>
import Card from "../InfoCard/index.vue";
import CardTitle from "../InfoCardTitle/index.vue";

export default {
  name: "ColumnEntrance",
  components: {CardTitle, Card},
  props: {
    articleSpecials: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
.column_list {
  .column_item {
    cursor: default;
    position: relative;
    border-bottom: 0.5px solid #F4F6F8;
    margin: 0 -16px 16px;
    padding: 0 16px 16px;

    &:last-child{
      padding: 0 16px 0;
      margin-bottom: 0;
      border-bottom: none;
    }
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 5px;
      width: 3px;
      height: 10px;
      background: #0581CE;
      border-radius: 0 2px 2px 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 12px;

      &:hover {
        color: #0581CE;
      }
    }

    .content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .content_tips {
        font-size: 14px;
        color: #999EA4;
      }

      .btn {
        cursor: pointer;
        color: #50789C;
        font-size: 12px;
        display: flex;
        align-items: center;

        .go_icon {
          width: 16px;
          height: 16px;
          color: #50789C;
        }

        &:hover {
          color: #0581CE;

          .go_icon {
            color: #0581CE;
          }
        }
      }
    }
  }
}
</style>
