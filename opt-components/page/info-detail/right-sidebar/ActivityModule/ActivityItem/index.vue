<template>
  <div slot="reference" class="author_wrapper">
    <div class="author_box">
      <div class="author_content">
        <a target="_blank" :href="`/bms/classify/-/activity-details/${id}`" class="image">
          <ZipImg
            :width="39"
            :height="39"
            :src="cover"
          />
        </a>
        <div class="info">
          <a target="_blank" :href="`/bms/classify/-/activity-details/${id}`" class="title text-limit-1">{{ title }}</a>
          <p class="company text-limit-1">
            {{ `${$tool.formatterNum(views)}阅读 | ${$tool.formatterNum(contents)}内容` }}</p>
        </div>
      </div>
      <div class="follow" @click="followHandler">
        {{ isFollow ? '已关注' : '关注'}}
      </div>
    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../../../../component";
import {subscribeActivity} from "../../../../../../api/bms";

export default {
  name: "ActivityItem",
  components: {ZipImg},
  props: {
    id: {
      type: Number,
      default: null
    },
    title: {
      type: String,
      default: ""
    },
    cover: {
      type: String,
      default: ""
    },
    views: {
      type: Number,
      default: 0
    },
    contents: {
      type: Number,
      default: 0
    },
    subscribeStatus:{
      type: String,
      default: "F"
    }
  },
  data(){
    return {
      isFollow:this.subscribeStatus === "T"
    }
  },
  methods:{
    followHandler(){
      this.isFollow = !this.isFollow
      this.$axios.request(subscribeActivity({
        activityId: this.id
      }))
    }
  }
}
</script>

<style scoped lang="less">
.author_wrapper {
  flex-shrink: 0;
  border-radius: 4px;
  background: #F8F8F8;
  padding: 8px;
  box-sizing: border-box;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  .author_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .author_content {
    flex: 1;
    display: flex;
    align-items: center;

    .image {
      flex-shrink: 0;
      width: 39px;
      height: 39px;
      margin-right: 12px;
      border-radius: 4px;
      overflow: hidden;
    }

    .info {
      .title {
        font-size: 14px;
        color: #333;
        margin-bottom: 7px;
      }

      .company {
        color: #999EA4;
        font-size: 12px;
      }
    }
  }

  .follow {
    user-select: none;
    flex-shrink: 0;
    width: 45px;
    height: 24px;
    border: 0.5px solid #46B0F2;
    background: rgba(70, 176, 242, 0.05);
    color: #46B0F2;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: all .2s;

    &:hover {
      border: 0.5px solid #0581CE;
      background: rgba(5, 129, 206, 0.05);
      color: #0581CE;
    }
  }
}
</style>
