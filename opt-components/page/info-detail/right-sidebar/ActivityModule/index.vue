<template>
  <div class="activity_module_wrapper">
    <ActivityItem
      v-for="item in activityList"
      :id="item.id"
      :key="item.id"
      :title="item.name"
      :cover="item.shareImage || item.cover"
      :views="item.views"
      :contents="item.contents"
      :subscribe-status="item.subscribeStatus"
    />
  </div>
</template>

<script>
import ActivityItem from "./ActivityItem/index.vue";

export default {
  name: "ActivityModule",
  components: {ActivityItem},
  mounted() {
    console.log(this.activityList)
  },
  props: {
    activityList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
.activity_module_wrapper {
  border-radius: 8px;
  background: #FFF;
  padding: 8px;
}
</style>
