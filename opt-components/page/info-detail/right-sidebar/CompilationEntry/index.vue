<template>
  <a target="_blank" href="/compile" class="wrapper">
    <div class="content">
      <SvgIcon icon-class="compilation" class-name="pdf_icon"/>
      <div class="title">
        <p class="name">精选编译</p>
        <p class="tips">前沿资讯尽在掌握</p>
      </div>
    </div>
    <a target="_blank" href="/compile" class="entry_btn">
      进入
    </a>
  </a>
</template>

<script>
export default {
  name: "CompilationEntry",
}
</script>

<style scoped lang="less">
.wrapper {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background: #FFF;

  .content {
    display: flex;
    align-items: center;


    .pdf_icon {
      width: 45px;
      height: 45px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .title {
      .name {
        font-size: 14px;
        font-weight: 500;
        line-height: 16px;
        margin-bottom: 6px;
      }

      .tips {
        color: #999;
        font-size: 12px;
      }

    }
  }

  .entry_btn {
    width: 54px;
    height: 22px;
    border-radius: 4px;
    border: 0.5px solid #0581CE;
    background: rgba(5, 129, 206, 0.05);
    color: var(--theme-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;

    &:hover {
      color: #46B0F2;
    }
  }

  &:hover {
    .download_icon {
      color: var(--theme-color);
    }
  }
}
</style>
