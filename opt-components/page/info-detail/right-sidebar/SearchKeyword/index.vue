<template>
  <InfoCard>
    <InfoCardTitle title="关键词搜索" icon="info_search_keyword"/>
    <div class="keyword_wrapper">
      <a
        v-for="item in searchKeyWordList"
        :key="item"
        target="_blank"
        :href="`/search?keywords=${item}`"
        class="keyword_name">{{ item }}</a>
    </div>
  </InfoCard>
</template>

<script>
import InfoCard from "../InfoCard/index.vue";
import InfoCardTitle from "../InfoCardTitle/index.vue";

export default {
  name: "SearchKeyword",
  components: {
    InfoCard,
    InfoCardTitle
  },
  props: {
    searchKeyWordList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
.keyword_wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;

  .keyword_name {
    display: block;
    padding: 7px;
    border-radius: 4px;
    border: 0.5px solid rgba(112, 138, 162, 0.20);
    background: linear-gradient(0deg, rgba(112, 138, 162, 0.04) 0%, rgba(112, 138, 162, 0.04) 100%), #FFF;
    color: #333;
    font-size: 14px;
    line-height: 14px;
    flex-shrink: 0;
    transition: all .2s;

    &:hover {
      cursor: pointer;
      color: #0581CE;
      border: 0.5px solid rgba(5, 129, 206, 0.20);
      background: linear-gradient(0deg, rgba(5, 129, 206, 0.04) 0%, rgba(5, 129, 206, 0.04) 100%), #FFF;
    }
  }
}

</style>
