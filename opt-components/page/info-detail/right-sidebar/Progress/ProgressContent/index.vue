<template>
  <InfoCard styles="padding:16px 0">
    <div class="title_wrapper">
      <InfoCardTitle :title="obj.title" icon="progress_icon"/>
      <div v-if="obj.infos.length>1" class="slider_wrapper">
        <SvgIcon
          style="transform:rotateZ(180deg);margin-right: 10px"
          icon-class="right_info"
          class-name="icons"
          :style="num > 0 ? {color:'#708AA2'} : {cursor:'not-allowed',color: '#C7C7C7'}"
          @click="sliderHandler('left')"
        />
        <SvgIcon
          icon-class="right_info"
          class-name="icons"
          :style="num < (obj.infos.length - 1) ? {color:'#708AA2'} : {cursor:'not-allowed',color: '#C7C7C7'}"
          @click="sliderHandler('right')"
        />
      </div>
    </div>


    <div class="container">
      <div ref="data_wrapper" class="data_wrapper">
        <ul v-for="(infos,infoIndex) in obj.infos" :key="infoIndex" class="data_content">
          <li v-for="itemC in infos" :key="itemC.id" class="data_item">
            <a class="text-limit-1" target="_blank" :href="`/info/detail?id=${itemC.id}`">{{ itemC.title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </InfoCard>
</template>
<script>
import InfoCard from "../../InfoCard/index.vue";
import InfoCardTitle from "../../InfoCardTitle/index.vue";

export default {
  name: "ProgressContent",
  components: {InfoCard, InfoCardTitle},
  props: {
    obj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      num: 0
    }
  },
  methods: {
    sliderHandler(type) {
      const dom = this.$refs.data_wrapper

      if (type === 'left') {
        if (this.num > 0) {
          this.num -= 1;
        }

      } else if (type === 'right') {
        if (this.num < (this.obj.infos.length - 1)) {
          this.num += 1;
        }
      }

      dom.style.transform = `translateX(-${this.num}00%)`
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .info_card_wrapper {
  padding: 0;
}

.title_wrapper {
  position: relative;
  margin: 0 16px;

  .slider_wrapper {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;

    .icons {
      width: 12px;
      height: 12px;
      cursor: pointer;

      &:hover {
        color: var(--theme-color);
      }
    }
  }
}

.container {
  width: 100%;
  overflow: hidden;
}

.data_wrapper {
  display: flex;
  flex-wrap: nowrap;
  transition: all .2s ease;

  .data_content {
    flex-shrink: 0;
    width: 100%;

    .data_item {
      font-size: 14px;
      line-height: 22px;
      position: relative;
      padding: 0 16px;

      &:hover {
        a {
          color: var(--theme-color);
        }
      }

      &::after {
        content: "";
        position: absolute;
        left: 0;
        top: 5px;
        width: 3px;
        height: 10px;
        background: #0581CE;
        border-radius: 0 2px 2px 0;
      }

      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

</style>
