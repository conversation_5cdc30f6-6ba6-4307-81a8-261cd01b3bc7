<template>
  <div class="progress_wrapper">
    <div v-for="(item,index) in set" :key="index" class="progress_content">
      <ProgressContent :obj="item"/>
    </div>
  </div>
</template>

<script>
import ProgressContent from "./ProgressContent/index.vue";

export default {
  name: "ProgressComponent",
  components: {
    ProgressContent
  },
  props: {
    guideAdditionInfoList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      set: []
    }
  },
  fetch() {
    this.set = this.guideAdditionInfoList.map(item => {

      const arr = []
      for (let j = 0; j < item.infos.length; j += 3) {
        arr.push(item.infos.slice(j, j + 3))
      }

      return {
        title: item.title,
        infos: arr
      }
    })
  },
  mounted() {
    console.log(this.set, "this.set")
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
