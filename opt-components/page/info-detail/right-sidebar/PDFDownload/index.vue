<template>
  <a target="_blank" :href="pdfUrl" class="download_wrapper">
    <div class="content">
      <SvgIcon icon-class="pdf_down" class-name="pdf_icon"/>
      <span>PDF下载</span>
    </div>
    <SvgIcon icon-class="download" class-name="download_icon"/>
  </a>
</template>

<script>
export default {
  name: "PDFDownload",
  props: {
    pdfUrl: {
      type: String,
      default: ""
    }
  }
}
</script>

<style scoped lang="less">
.download_wrapper {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background: #FFF;

  .content {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;

    .pdf_icon {
      width: 37.525px;
      height: 37.525px;
      margin-right: 8px;
      flex-shrink: 0;
    }
  }

  .download_icon {
    width: 20px;
    height: 20px;
    color: #708AA2;
  }

  &:hover {
    .download_icon {
      color: var(--theme-color);
    }
  }
}
</style>
