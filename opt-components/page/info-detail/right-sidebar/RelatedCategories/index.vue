<template>
  <InfoCard>
    <div v-if="articleSpecials.length>0 && template" class="categories_wrapper">
      <InfoCardTitle title="专栏" icon="categories_2" bottom="12px"/>
      <div class="content">
        <a
          v-for="item in articleSpecials"
          :key="item.id"
          target="_blank"
          :href="`/column/detail?id=${item.id}&type=${item.type}`"
          class="content_item text-limit-3">
          {{ item.title }} >>
        </a>
      </div>
    </div>

    <div v-if="infoMiniTool.length>0" class="categories_wrapper">
      <InfoCardTitle title="相关临床评分小工具" icon="categories_1" bottom="12px"/>
      <div class="content">
        <a
          v-for="item in infoMiniTool"
          :key="item.id"
          target="_blank"
          :href="item.scoreToolUrl"
          :title="item.scoreToolKeywords"
          class="content_item text-limit-3">
          {{ item.scoreToolName }} >>
        </a>
      </div>
    </div>

    <div v-if="talkList.length>0" class="categories_wrapper">
      <InfoCardTitle title="所属话题" icon="categories_3" bottom="12px"/>
      <div class="content">
        <a
          v-for="item in talkList"
          :key="item.id"
          target="_blank"
          :href="`/topic-circle/communitytopic?id=${item.id}`"
          class="content_item text-limit-3"
          :title="item.description">
          #{{ item.title }} >>
        </a>
      </div>
    </div>

    <div v-if="circleList.length>0 && template" class="categories_wrapper">
      <InfoCardTitle title="所属圈子" icon="categories_5" bottom="12px"/>
      <div class="content">
        <a
          v-for="item in circleList"
          :key="item.id"
          target="_blank"
          :href="`/topic-circle/communitycircle?id=${item.id}`"
          class="content_item text-limit-3">
          {{ item.title }} >>
        </a>
      </div>
    </div>

    <div v-if="infoProducts.length>0" class="categories_wrapper">
      <InfoCardTitle title="相关产品" icon="categories_4" bottom="12px"/>
      <div class="content">
        <a
          v-for="item in infoProducts"
          :key="item.id"
          target="_blank"
          :href="`/bms/classify/-/product-details/${item.id}`"
          class="content_item text-limit-3">
          #{{ item.name }}
        </a>
      </div>
    </div>


  </InfoCard>
</template>

<script>
import InfoCard from "../InfoCard/index.vue";
import InfoCardTitle from "../InfoCardTitle/index.vue";

export default {
  name: "RelatedCategories",
  components: {InfoCard, InfoCardTitle},
  props: {
    template: {
      type: String,
      default: ''
    },
    infoMiniTool: {
      type: Array,
      default: () => []
    },
    articleSpecials: {
      type: Array,
      default: () => []
    },
    circleList: {
      type: Array,
      default: () => []
    },
    talkList: {
      type: Array,
      default: () => []
    },
    infoProducts: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
