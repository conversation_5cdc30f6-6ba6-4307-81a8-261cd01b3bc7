<template>
  <div>
    <div v-for="item in circleList" :key="item.id" class="circle_entrance_wrapper">
      <div class="content">
        <a class="image" target="_blank" :href="`/topic-circle/communitycircle?id=${item.id}`">
          <zip-img
            :width="39"
            :height="39"
            :src="item.shareImage || item.image || item.thumbnail"
          />
        </a>
        <div class="info">
          <p class="title text-limit-1">{{ item.title }}</p>
          <p class="tips">{{ $tool.formatterNum(item.contents) }}内容{{ $tool.formatterNum(item.views) }}阅读</p>
        </div>
      </div>
      <a target="_blank" :href="`/topic-circle/communitycircle?id=${item.id}`" class="entrance_btn">进圈子</a>
    </div>
  </div>
</template>

<script>
import ZipImg from "../../../../component/ZipImg/index.vue";

export default {
  name: "CircleEntrance",
  components: {ZipImg},
  props: {
    circleList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
.circle_entrance_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 4px;
  background: #FFF;
  padding: 8px;

  .content {
    display: flex;
    align-items: center;

    .image {
      width: 39px;
      height: 39px;
      flex-shrink: 0;
      margin-right: 12px;
      border-radius: 4px;
      overflow: hidden;
      display: block;
    }

    .info {
      .title {
        color: #333;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 14px;
      }

      .tips {
        font-size: 12px;
        color: #999;
      }
    }
  }

  .entrance_btn {
    display: block;
    width: 43px;
    height: 22px;
    line-height: 22px;
    border-radius: 4px;
    border: 0.5px solid #0581CE;
    background: rgba(5, 129, 206, 0.05);
    font-size: 12px;
    color: #0581CE;
    text-align: center;
    transition: all .2s;
    cursor: pointer;

    &:active, &:hover {
      background: rgba(70, 176, 242, 0.05);
      color: #46B0F2;
    }
  }
}
</style>
