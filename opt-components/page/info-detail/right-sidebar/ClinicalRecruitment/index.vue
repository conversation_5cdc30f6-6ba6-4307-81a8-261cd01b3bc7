<template>
  <a class='label-item' href="/clinical/release" target="_blank" @click="handler">
    <div class='left-info'>
      <div class='img-box'>
        <img src="~assets/images/clinical/label.jpg" alt="" class="img_cover">
      </div>
      <div class='label-desc'>
        <p class='title'>
          临床招募
        </p>
        <p class='desc'>
          招募代发布 快速找患者
        </p>
      </div>
    </div>
    <SvgIcon icon-class="right_info" class-name="icons"/>
  </a>
</template>

<script>
export default {
  name: "ClinicalRecruitment",
  methods: {
    handler() {
      this.$analysys.new_btn_click({
        userId: this.$store.state.auth.user.id,
        btnName: "临床招募",
        pageName: document.title
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles.less";
</style>
