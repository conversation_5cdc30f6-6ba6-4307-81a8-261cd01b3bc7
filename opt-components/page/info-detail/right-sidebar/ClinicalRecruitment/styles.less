.label-item {
  padding: 8px;
  border-radius: 4px;
  background: #FBFBFB;

  display: flex;
  justify-content: space-between;
  cursor: pointer;
  align-items: center;


  .left-info {
    display: flex;
    align-items: center;

    .img-box {
      width: 39px;
      height: 39px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .label-desc {
      .title {
        font-weight: 500;
        font-size: 14px;
        line-height: 14px;
        color: var(--font-color-333);
        margin-bottom: 8px;
        text-align: left;
      }

      .desc {
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        color: #999999;
      }
    }
  }

  .icons {
    width: 12px;
    height: 12px;
    color: #708AA2;
    margin-right: 6px;
  }

  &:hover {
    .icons {
      color: var(--theme-color);
    }
  }

}
