<template>
  <div class="card_title" :style="{marginBottom:bottom}">
    <svg-icon :icon-class="icon" class-name="card_title_icon"/>
    <span class="tips">{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: "InfoCardTitle",
  props: {
    title: {
      type: String,
      default: "标题"
    },
    icon: {
      type: String,
      default: "column_icon"
    },
    bottom: {
      type: String,
      default: "18px"
    }
  }
}
</script>

<style scoped lang="less">
.card_title {
  display: flex;
  align-items: center;

  .card_title_icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    margin-right: 8px;
  }

  .tips {
    font-size: 16px;
    color: #333;
    font-weight: 500;
    line-height: 22px;
  }
}
</style>
