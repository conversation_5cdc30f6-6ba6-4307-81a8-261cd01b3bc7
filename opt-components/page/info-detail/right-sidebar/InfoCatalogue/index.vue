<template>
  <div v-if='catalogueList.length>0' class='catalogue-container'>
    <div class='catalogue-button' @click='show = !show'>
      <div class="title">
        <svg-icon icon-class="mulu" class-name="title_svg"/>
        <span>目录</span>
      </div>
      <svg-icon
        icon-class="mulu_select"
        :style='{transform: show ? "none":"rotateZ(180deg)"}'
        class-name='arrow'/>
    </div>


    <CollapseTransition>
      <div v-show='show' class='catalogue-content'>
        <div class='line'/>
        <div v-for='(item,index) in catalogueList' :key='index'
             :top='item.top'
             class='titleFirst catalogue-first'
             @click='handleScroll'>
          <h1 v-if='item.label === "H1"' @click='scrollTopHandler(item.top,item.labelName)'>{{ item.labelName }}</h1>
          <div v-if='item.label === "H2"' class='second' @click='scrollTopHandler(item.top,item.labelName)'>
            <h2>{{ item.labelName }}</h2>
          </div>
          <div v-if='item.label === "H3"' class='three' @click='scrollTopHandler(item.top,item.labelName)'>
            <h3>{{ item.labelName }}</h3>
          </div>
          <div v-if='item.label === "H4"' class='four' @click='scrollTopHandler(item.top,item.labelName)'>
            <h4>{{ item.labelName }}</h4>
          </div>
        </div>
      </div>
    </CollapseTransition>

    <ArticleDirectory :list="catalogueList"/>
  </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import ArticleDirectory from "../../ArticleDirectory/index.vue";

export default {
  name: 'InfoCatalogue',
  components: {
    CollapseTransition,
    ArticleDirectory
  },
  data() {
    return {

      scrollTop: 0,           // 滚动条
      show: true,
      catalogueList: []     // 目录列表
    }
  },
  mounted() {
    this.getGenerateArticleDirectoryHandler()

    if (this.catalogueList.length > 0) {
      window.addEventListener('scroll', this.handleScroll)
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll) // 组件销毁时移除监听
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll) // 组件销毁时移除监听
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/18 18:22
     *  滚动条
     */
    handleScroll(e) {
      /**
       * 高亮选项卡.  滚动距离 >= 实际距离    [那么]
       * @type {number}
       */
      this.scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop

      const catalogueWrapper = document.querySelector('.catalogue-container')
      const catalogueFirst = document.querySelectorAll('.catalogue-first')
      const directory = document.getElementById('article_directory_container')
      const leftPageWrapper = document.querySelector('.page_left_wrapper')

      const scrollTopWindow = Number(this.scrollTop)
      for (let i = 0; i < catalogueFirst.length; i++) {
        if (scrollTopWindow > (Number(catalogueFirst[i].getAttribute('top') - 1)) && scrollTopWindow < Number(catalogueFirst[(i + 1) > catalogueFirst.length - 1 ? catalogueFirst.length - 1 : (i + 1)].getAttribute('top'))) {
          catalogueFirst[i].classList.add('is_active')
        } else {
          catalogueFirst[i].classList.remove('is_active')
        }
      }

      if (scrollTopWindow > catalogueWrapper.offsetHeight) {
        directory.style.display = "block"

        if (scrollTopWindow + 800 > leftPageWrapper.offsetHeight) {
          directory.style.display = "none"
        }
      } else {
        directory.style.display = "none"
      }
    },

    activeLoadScroll(sections) {

    },
    /**
     *  @author:Rick  @date:2022/10/18 15:40
     *  获取文章目录
     */
    getGenerateArticleDirectoryHandler() {

      const dom = document.querySelectorAll('article h1,article h2,article h3,article h4')


      for (let i = 0; i < dom.length; i++) {
        this.catalogueList.push({
          label: String(dom[i].tagName),
          labelName: dom[i]?.getElementsByTagName('a')?.[0]?.getAttribute('name'),
          top: dom[i].offsetTop
        })
      }
      // this.$refs.article.(this.$refs.article)
    },
    scrollTopHandler(top, name) {
      this.activeId = name
      window.scrollTo({
        top,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
