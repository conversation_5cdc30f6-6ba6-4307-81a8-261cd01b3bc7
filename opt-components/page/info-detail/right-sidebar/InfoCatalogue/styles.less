.catalogue-container {
  background: #FBFBFB;
  border-radius: 6px;
  overflow: hidden;
  position: relative;

  .catalogue-button {
    height: 48px;
    background: #50789C;
    background: url("assets/images/info/catalogue.png") 100% 100% no-repeat;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .title {
      font-weight: 500;
      color: #FFF;
      display: flex;
      align-items: center;
      line-height: 24px;

      .title_svg {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
    }

    .arrow {
      width: 20px;
      height: 20px;
      transition: all .3s;
    }
  }

  .catalogue-content {
    padding: 16px 16px 18px;
    max-height: 40vh;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 4px;
    }

    .titleFirst {
      //margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      h1 {
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: #333333;
        cursor: pointer;
        transition: all .3s;
        margin-top: 20px;
        margin-bottom: 12px;

        &:first-child {
          margin-top: 0;
        }

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .second {
      h2 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #333;
        margin-left: 5px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .three {
      h3 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #676C74;
        margin-left: 20px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .four {
      h4 {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #666666;
        margin-left: 30px;
        cursor: pointer;
        transition: all .3s;
        margin-bottom: 12px;

        &:hover {
          color: var(--theme-color);
        }
      }
    }

    .is_active {
      h1 {
        color: #0582CE;
      }

      h2 {
        color: #0582CE;
      }

      h3 {
        color: #0582CE;
      }

      h4 {
        color: #0582CE;
      }
    }
  }
}
