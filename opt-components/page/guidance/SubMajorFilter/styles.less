.sub-majorfilter {
  user-select: none;

  .filter-list {
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .filter-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-right: 24px;
      transition: all .3s;
      position: relative;
      padding: 8px;
      border-radius: 8px;
      background: #F8F8F8;

      span {
        font-size: 14px;
        line-height: 18px;
        color: #676C74;
      }

      i {
        color: #676C74!important;
        transition: all .3s;
      }

      &:hover {
        background: #EFFAFF;

        span, i {
          color: #0581CE!important;
        }
      }

      &:hover i {
        transform: rotateZ(180deg);
      }

      &:last-child {
        margin-right: 0;
      }

      &:hover .filter-dataset-box {
        display: block;
      }

      .filter-dataset-box {
        z-index: 2;
        display: none;
        min-width: 60px;
        position: absolute;
        white-space: nowrap;
        left: 50%;
        top: 100%;
        transform: translateX(-50%);
        padding-top: 6px;

        .filter-dataset-list {
          text-align: center;
          background-color: white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
          border-radius: 4px;

          .filter-dataset-item {
            padding: 0 18px;
            line-height: 36px;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
          }

          .is_active {
            background: rgba(5, 129, 206, 0.15);
            color: var(--theme-color);
          }
        }
      }

    }
  }
}
