<template>
  <el-popover
    :placement="placement"
    width="387"
    trigger="click"
    :visible-arrow="false"
    popper-class="summary_popover"
    @show="showHandler"
    v-model="visible"
  >
    <div class="summary_popover_wrapper">
      <div class="popover_title">
        <p>AI日程摘要</p>
        <svg-icon icon-class="close_icon" class-name="close_svg" @click="visible = false"/>
      </div>
      <template v-if="!summaryData.id">
        <div :style="{padding:'16px'}">
          <el-skeleton :rows="4" animated :throttle="500"/>
        </div>
      </template>

      <template v-else>
        <!--日程-->
        <div class="schedule_wrapper">
          <p class="schedule_title text-limit-1">{{ summaryData.theme }}</p>
          <p v-if="summaryData.authorList && summaryData.authorList.length >0" class="schedule_author">
            {{ summaryData.authorList.map(item => item.authorName).join() }}</p>
        </div>
        <!--内容-->
        <div class="summary_content_wrapper">{{ summaryData.summary }}</div>
        <!--关键字-->
        <div v-if="summaryData.keywordsList && summaryData.keywordsList.length>0" class="summary_keywords_wrapper">
          <p class="keywords_title">关键词：</p>
          <ul class="keywords_ul">
            <li
              v-for="item in summaryData.keywordsList"
              :key="item"
              class="keywords_item">
              <a target="_blank" :href="`/search?keywords=${item}`">{{ item }}</a>
            </li>
          </ul>
        </div>
        <p class="summary_tip">
          内容由AI生成，仅供参考
        </p>
      </template>
    </div>
    <div slot="reference" id="summary_popover_btn">
      <slot></slot>
    </div>
  </el-popover>

</template>

<script>
import {getMeetingAgendaSummary} from "../../api/meeting.js";

export default {
  name: "SummaryPopover",
  props: {
    meetingName:{},
    meetingId:{},
    agendaId: {
      type: Number,
      default: 0
    },
    agendaName:{},
    placement: {
      type: String,
      default: "bottom-end"
    },
  },
  data() {
    return {
      summaryData: {},
      visible: false
    }
  },
  methods: {
    showHandler() {
      this.$analysys.ai_Abstract({
        meeting_name: this.meetingName,
        agenda_id:  this.agendaId,
        meeting_id:  this.meetingId,
        agenda_name:  this.agendaName
      })

      if (!this.summaryData.id) {
        this.getSummaryData()
      }
    },
    getSummaryData() {
      this.$axios.$request(getMeetingAgendaSummary({
        agendaId: this.agendaId
      })).then(res => {
        if (res.code === 1) {
          this.summaryData = res.result
        }

        if (res.code === 500) {
          this.visible = false
        }
      })
    }
  }
}
</script>

<style>
.summary_popover {
  padding: 0 !important;
}
</style>
<style scoped lang="less">
.summary_popover_wrapper {
  padding: 0 0 16px;

  .popover_title {
    font-size: 18px;
    color: #333;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    position: relative;
    background: linear-gradient(91deg, #ECD8FF 0%, #C0D8FF 50.5%, #B3FFFA 100%);
    margin-bottom: 16px;

    p {
      z-index: 1;
    }

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(0deg, #FFF 0, rgba(255, 255, 255, 0.00) 100%);
    }

    .close_svg {
      cursor: pointer;
      width: 24px;
      height: 24px;
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .schedule_wrapper {
    margin: 0 16px 16px;
    border-radius: 6px;
    background: #F8F8F8;
    padding: 10px 14px;

    .schedule_title {
      color: #333;
      font-size: 14px;
      line-height: 1.5;
    }

    .schedule_author {
      color: #708AA2;
      font-size: 12px;
      margin-top: 4px;
    }
  }

  .summary_content_wrapper {
    margin-bottom: 24px;
    padding: 0 16px;
    max-height: 48vh;
    overflow: auto;
  }

  .summary_keywords_wrapper {
    padding: 0 16px;

    .keywords_title {
      color: #999;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 14px;
    }

    .keywords_ul {
      display: flex;
      flex-wrap: wrap;
      row-gap: 12px;
      column-gap: 8px;

      .keywords_item {
        border-radius: 155px;
        border: 1px solid #DBEDF7;
        background: #FAFCFE;
        padding: 4px 12px;
        font-size: 12px;
        color: #64889F;
        cursor: pointer;
      }
    }
  }

  .summary_tip {
    text-align: center;
    color: #BBBBBB;
    font-size: 12px;
    line-height: 1.5;
    margin-top: 16px;
    user-select: none;
  }
}

#summary_popover_btn {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
