<template>
  <a :href="`/info/detail?id=${infoId}`" target="_blank">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="image"
          fill
        />
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <SvgIcon v-if="essences === 'T' && essencesFlag" icon-class="essences" class-name="essences"/>
          <svg-icon v-if="bmsAuth === 1" icon-class="auth-new" style="width: 18px;height: 18px;vertical-align:sub"/>
          <span>{{ title }}</span>
        </p>
        <div class="tips">
          <div class="tips_left">
            <p class="author text-limit-1">{{ authorNames }}</p>
            <p class="time">{{ publishDate }}</p>
          </div>
          <div class="tips_right">
            <div v-if="productList.length > 0" class="product_icon">
              <SvgIcon icon-class="product-home" class-name="icons"/>
              <div class="product_list_popup">
                <div class="product_list_popup_content">
                  <a
                    v-for="item in productList"
                    :key="item.id"
                    target="_blank"
                    :href="`/bms/classify/-/product-details/${item.productId || item.id}`"
                    class="product_item">
                    <span class="text-limit-1">#{{ item.name }}</span>
                    <svg-icon icon-class="product_jump" class-name="product_jump_svg"/>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
</template>

<script>
import {ZipImg} from "../../../../component";

export default {
  name: "InfoDefaultItem",
  components: {
    ZipImg
  },
  props: {
    infoId: {
      type: Number,
      default: null
    },
    image: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    authorNames: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    },
    productList: {
      type: Array,
      default: () => []
    },
    essences: {
      type: String,
      default: "F"
    },
    essencesFlag: {
      type: Boolean,
      default: true
    },
    bmsAuth:{}
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
