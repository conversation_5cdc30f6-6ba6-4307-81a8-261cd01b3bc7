.wrapper {
  .image {
    border-radius: 8px;
    overflow: hidden;
    padding-bottom: 75%;
    position: relative;

    img{
      transition: all .1s ease-in 0s;
    }

  }

  .info_content {
    padding-top: 8px;

    .title {
      font-size: 14px;
      line-height: 20px;
      height: 40px;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .info_tips {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .tips_left {
        font-size: 12px;
        color: #999EA4;
      }

      .product_icon {
        width: 22px;
        height: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: rgba(98, 168, 63, 0.12);
        transition: all .2s ease;

        .icons {
          width: 12px;
          height: 12px;
        }

        &:hover {
          background: rgba(98, 168, 63, 0.20);
        }
      }
    }
  }

  &:hover {
    .image img{
      transform: scale(1.05);
      transition: transform .2s ease-in;
    }
    .info_content .title{
      color: var(--theme-color);
    }
  }
}
