<template>
  <div>
    <nuxt-link target="_blank" :to="`/info/detail?id=${infoId}`" class="wrapper">
      <div class="image">
        <zip-img
          :src="infoImg"
          :alt="infoTitle"
          :width="126"
          :height="126"
        />
      </div>
      <div class="info_content">
        <div class="title text-limit-2">
          {{ infoTitle }}
        </div>
        <div class="info_tips">
          <div class="tips_left">
            <p style="margin-bottom: 3px">{{ userName}}</p>
            <p>{{
                publishDate && timeStamp.timestampFormat(
                  Date.parse(publishDate.replace(/-/g, '/')) / 1000
                )
              }}</p>
          </div>
          <div v-if="isProduct" class="product_icon">
            <SvgIcon icon-class="product-home" class-name="icons"/>
          </div>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import ZipImg from "../../../../component/ZipImg/index.vue";

export default {
  name: "InfoHomeListItem",
  components: {ZipImg},
  props: {
    infoId: {
      type: Number,
      default: null
    },
    infoImg: {
      type: String,
      default: ""
    },
    infoTitle: {
      type: String,
      default: ""
    },
    publishDate: {},
    authorNameList: {
      type: Array,
      default: () => []
    },
    isProduct:{
      type:Boolean,
      default:false
    }
  },
  computed: {
    userName() {
      if (this.authorNameList.length === 0) {
        return "脑医资讯助手"
      } else if (this.authorNameList.length === 1) {
        return this.authorNameList[0].authorName
      } else if (this.authorNameList.length > 1) {
        return this.authorNameList[0].authorName + `等${this.authorNameList.length}位作者`
      }else{
        return ""
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
