<template>
  <a :href="guidanceData.url" target="_blank">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="guidanceData.image"
          fill
        />
        <div v-if="guidanceData.isPlay" class="mask">
          <svg-icon icon-class="icon_play_n" class-name="icons"/>
        </div>
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <svg-icon v-if="guidanceData.bmsAuth === 1" icon-class="auth-new" style="width: 18px;height: 18px;vertical-align:sub"/>
          <span>{{ guidanceData.title }}</span>
        </p>
        <div class="tips">
          <div class="tips_left">
            <p class="author text-limit-1">{{ guidanceData.authorNames }}</p>
            <p class="time">{{ guidanceData.publishDate }}</p>
          </div>
          <div class="tips_right">
            <div v-for="item in guidanceData.guideTypeList" :key="item.id" class="typeName">
              #{{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
</template>

<script>
import {ZipImg} from "../../../../component";

export default {
  name: "InfoDefaultItem",
  components: {
    ZipImg
  },
  props: {
    guidanceData: {
      type: Object,
      default: () => {
      }
    }
  },

}
</script>

<style scoped lang="less">
@import "./styles";
</style>
