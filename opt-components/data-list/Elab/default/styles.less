.info_default_item_container {
  border-radius: 8px;
  background: #F8F8F8;
  transition: all .2s;

  .image {
    width: 100%;
    height: 135px;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    position: relative;

    img {
      transition: all .2s ease;
    }

    .label {
      position: absolute;
      left: 8px;
      top: 8px;
      width: 64px;
      height: 24px;
      border-radius: 4px;
      background: linear-gradient(90deg, #55BCFC 100%, #319DDF 100%);
      color: #FFF;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .content {
    padding: 16px;

    .title {
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      height: 40px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .tips {
      .tips_content {
        display: flex;
        align-items: center;
        height: 24px;
        margin-bottom: 2px;
      }

      .company {
        color: #999EA4;
        font-size: 12px;
      }

      .author_list {
        height: 100%;
        border-radius: 2px;
        background: rgba(112, 138, 162, 0.08);
        display: flex;
        align-items: center;
        padding: 0 4px;

        .tag {
          color: #0581CE;
          font-size: 12px;
          margin-right: 6px;

          &::after {
            content: ",";
          }

          &:last-child {
            &::after {
              content: "";
            }
          }
        }
      }

      .tag_list {
        color: #708AA2;
        font-size: 12px;

        span {
          margin-right: 6px;
          line-height: 22px;
        }
      }
    }
  }

  &:hover {
    .content > .title {
      color: var(--theme-color);
    }

    .image img {
      transform: scale(1.05);
    }
  }
}
