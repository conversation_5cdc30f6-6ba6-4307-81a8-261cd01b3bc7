<template>
  <a :href="`/elabweb/detail?id=${infoId}`" target="_blank">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="image"
          fill
        />
        <div v-if="isLabel" class="label">
          全景手术
        </div>
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <span>{{ title }}</span>
        </p>

        <div class="tips">
          <div class="tips_content">
            <div v-if="isLabel" class="company">{{ hospital }}</div>
            <div v-else-if="authorList.length>0" class="author_list text-limit-1">
              <span v-for="item in authorList" :key="item.id" class="tag">{{ item.realName }}</span>
            </div>
            <div v-else class="company">{{ hospital }}</div>
          </div>

          <div class="tag_list text-limit-1">
            <span v-for="item in classificationList" :key="item.id" class="tag">#{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </a>
</template>

<script>
import {ZipImg} from "../../../component";

export default {
  name: "ElabDefaultItem",
  components: {
    ZipImg
  },
  props: {
    infoId: {
      type: Number,
      default: null
    },
    isLabel: {
      type: Boolean,
      default: true
    },
    image: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    hospital: {
      type: String,
      default: ""
    },
    authorList: {
      type: Array,
      default: () => []
    },
    classificationList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
