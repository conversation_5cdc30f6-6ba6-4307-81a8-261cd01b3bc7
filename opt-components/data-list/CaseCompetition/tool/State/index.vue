<template>
  <!--病例大赛状态-->
  <div :style="{background:states[state].bgColor,color:states[state].color}" class="state_wrapper">
    <svg-icon icon-class="case-comtition-state" class-name="icons"/>
    <span>{{ states[state].lable }}</span>
  </div>
</template>

<script>
export default {
  name: 'CompetitionState',
  props: {
    /**
     * state 状态
     */
    state: {
      type: String,
      default: '0',
      required: true
    },
  },
  data() {
    return {
      states: {
        "0": {
          lable: '未开始',
          bgColor: '#FFEECF',
          color:'#E76333'
        },
        "1": {
          lable: '评审中',
          bgColor: 'rgba(98, 168, 63, 0.20)',
          color:'#4A9823'
        },
        "2": {
          lable: '报名中',
          bgColor: '#E76333',
          color:'#FFF'
        },
        "3": {
          lable: '已结束',
          bgColor: '#E76333',
          color:'#FFF'
        },
      }
    }
  }
}
</script>

<style scoped lang='less'>
.state_wrapper {
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  .icons{
    width: 11px;
    height: 11px;
    margin-right: 1px;
  }
}
</style>
