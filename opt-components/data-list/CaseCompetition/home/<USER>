.wrapper {
  display: flex;

  .image {
    width: 80px;
    height: 60px;
    flex-shrink: 0;
    margin-right: 10px;
    border-radius: 4px;
    overflow: hidden;
  }

  .info {
    .title {
      height: 38px;
      font-size: 14px;
      margin-bottom: 7px;
    }

    .state {
      display: flex;
      justify-content: end;
    }
  }

  &:hover {
    .info > .title {
      color: var(--theme-color);
    }
  }
}
