<template>
  <div class="wrapper cursor" @click="jumpHandler">
    <div class="image">
      <ZipImg
        :width="80"
        :height="60"
        :alt="title"
        :src="image"
      />
    </div>
    <div class="info">
      <div class="title text-limit-2">{{ title }}</div>
      <div class="state">
        <State :state="status"/>
      </div>
    </div>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";
import State from "../tool/State/index.vue";

export default {
  name: "CaseCompetitionHomeItem",
  components: {ZipImg, State},
  props: {
    image: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: '0'
    },
    code: {},
    competitionUrl: {}
  },
  methods: {
    jumpHandler() {
      if (this.competitionUrl) {
        window.open(this.competitionUrl, '_blank')
      } else {
        window.open(this.code + '/', '_blank')
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
