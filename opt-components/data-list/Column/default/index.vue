<template>
  <a target="_blank" :href="webApiLinkUrl || `/column/detail?id=${columnId}&type=O`" class="column_default_container">
    <div class="wrapper">
      <div class="image">
        <zip-img
          :width="264"
          :height="148"
          :src="smallImageUrl"
        />
      </div>
      <div class="content">
        <p class="title text-limit-1">{{ title }}</p>
        <div class="tips">
          <span>{{ views }}阅读 {{ refers }}文章</span>
          <span>{{ updateTime }}更新</span>
        </div>
      </div>
    </div>
    <div class="line_1"/>
    <div class="line_2"/>
  </a>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "ColumnDefaultItem",
  components: {ZipImg},
  props: {
    webApiLinkUrl: {},
    columnId: {},
    smallImageUrl: {},
    title: {},
    views: {},
    refers: {},
    updateTime: {},
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
