.column_default_container {
  display: block;

  .wrapper{
    border: 0.5px solid #D4D3D3;
    background: #F8F8F8;
    border-radius: 8px;
  }

  .image {
    width: 100%;
    height: 148.5px;
    border-radius: 8px 8px 0 0;
    overflow: hidden;

    img {
      transition: all .2s;
    }
  }

  .content {
    padding: 12px;

    .title {
      color: #333;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      height: 20px;
      margin-bottom: 12px;
    }

    .tips {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999EA4;
      font-size: 12px;
    }
  }

  .line_1 {
    background: #E2E2E2;
    width: 90%;
    height: 7px;
    margin: 0 auto;
    border-radius: 0 0 8px 8px;
  }
  .line_2{
    background: #F1F2F3;
    width: 85%;
    height: 7px;
    margin: 0 auto;
    border-radius: 0 0 8px 8px;
  }

  &:hover {
    .image img {
      transform: scale(1.05);
    }

    .content .title {
      color: var(--theme-color);
    }
  }
}
