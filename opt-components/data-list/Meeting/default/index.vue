<template>
  <a :href="`/meeting/detail?id=${meetingId}`" target="_blank">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="image"
          fill
        />
        <div class="mask">
          <svg-icon icon-class="icon_play_n" class-name="icons"/>
        </div>
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <span>{{ title }}</span>
        </p>
        <div class="tips">{{publishDate}}</div>
      </div>
    </div>
  </a>
</template>

<script>
import {ZipImg} from "../../../component";

export default {
  name: "MeetingDefaultItem",
  components: {
    ZipImg
  },
  props: {
    meetingId: {
      type: Number,
      default: null
    },
    image: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
