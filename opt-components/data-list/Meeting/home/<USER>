
.wrapper {

  .content {
    display: block;

    .wrapper_content {
      width: 100%;
      position: relative;
      padding-bottom: 56.25%;
      border-radius: 8px;
      overflow: hidden;
    }

    .image {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;

      .mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, .3);
        opacity: 0;
        transition: opacity .2s ease-in-out;
        display: flex;
        justify-content: center;
        align-items: center;

        .icons {
          width: 48px;
          height: 48px;
        }
      }

      .meeting_status {
        position: absolute;
        right: 8px;
        top: 8px;
      }

      .enter_live {
        position: absolute;
        right: 8px;
        bottom: 16px;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.60);
        width: 86px;
        height: 24px;
        font-size: 12px;
        color: #FFF;
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity .2s ease;

        .icons {
          width: 10px;
          height: 10px;
        }
      }
    }

    .live_video_wrapper {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;

      background: rgba(0, 0, 0, 1);
      border-radius: 8px;


      .vide_wrapper {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;

        /deep/ .prism-controlbar {
          display: none !important;
        }

        .enter_live {
          z-index: 2;
          position: absolute;
          right: 8px;
          bottom: 16px;
          border-radius: 4px;
          background: rgba(0, 0, 0, 0.60);
          width: 86px;
          height: 24px;
          font-size: 12px;
          color: #FFF;
          display: flex;
          justify-content: center;
          align-items: center;

          .icons {
            width: 10px;
            height: 10px;
          }
        }
      }
    }

    .description {
      padding: 16px 0 0;

      .title {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        height: 40px;
        margin-bottom: 16px;
      }

      .tips {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time {
          font-size: 12px;
          color: #708AA2;
        }

        .city {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #708AA2;

          .icons {
            width: 15px;
            height: 15px;
            margin-right: 1px;
          }
        }
      }
    }
  }

  &:hover {
    .content > .wrapper_content .image .mask {
      opacity: 1 !important;
    }

    .content > .wrapper_content .image > .enter_live {
      opacity: 1 !important;
    }

    .title {
      color: var(--theme-color);
    }

    .image > .mask {
      opacity: 1;
    }
  }
}
