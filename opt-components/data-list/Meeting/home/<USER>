<template>
  <div
    class="wrapper"
    @mouseleave="leaveHandler"
    @mouseover="overHandler"
  >
    <nuxt-link
      :to='{ path: `/meeting/detail`,query:{id:meetingId} }'
      target='_blank'
      class="content">
      <div class="wrapper_content">
        <transition name="el-fade-in-linear">
          <div v-show="meetingLiveStatus === 'LI' && isHover" class="live_video_wrapper">
            <div class="vide_wrapper">
              <AliPlayerLive
                :autoplay='true'
                :is-live='true'
                :is-mute="true"
                :source='hlsLiveUrl ? hlsLiveUrl.split("http:").join("") : ""'
                :use-h5-prism='true'
                control-bar-visibility='hover'
                height='100%'
                width='100%'
                :cover="cover"
                @getPlayer="getPlayer"
              >
              </AliPlayerLive>
              <div class="enter_live">
                <span>进入直播间</span>
                <SvgIcon icon-class="meeintg-enter-live" class-name="icons"/>
              </div>

            </div>
          </div>
        </transition>
        <transition name="el-fade-in-linear">
          <div v-show="!isHover" class="image">
            <zip-img
              fill
              :width="240"
              :height="135"
              :src="cover"
              :alt="title"
            />
            <div class="meeting_status">
              <MeetingState :state="meetingLiveStatus"/>
            </div>
            <div v-if="meetingLiveStatus === 'LI'" class="enter_live">
              <span>进入直播间</span>
              <SvgIcon icon-class="meeintg-enter-live" class-name="icons"/>
            </div>
            <div v-if="meetingLiveStatus === 'LE'" class="mask">
              <svg-icon icon-class="icon_play_n" class-name="icons"/>
            </div>
          </div>
        </transition>
      </div>
      <div class="description">
        <p class="title text-limit-2">{{ title }}</p>
        <div class="tips">
          <div class="time">{{ meetingDateStr }}</div>
          <div class="city">
            <SvgIcon icon-class="meeintg-date-str" class-name="icons"/>
            <span>{{ province + `${city ? `-` + city : ''}` }}</span>
          </div>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";
import MeetingState from "../tool/MeetingState/index.vue";
import AliPlayerLive from "../../../page/home/<USER>/index.vue";

export default {
  name: "MeetingHomeDefaultItem",
  components: {ZipImg, MeetingState, AliPlayerLive},
  props: {
    meetingId: {
      type: Number,
      default: null
    },
    cover: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    meetingDateStr: {
      type: String,
      default: null
    },
    province: {
      type: String,
      default: null
    },
    city: {
      type: String,
      default: null
    },
    meetingLiveStatus: {
      type: String,
      default: null
    },
    hlsLiveUrl: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      player: null,
      isHover: false
    }
  },
  methods: {
    getPlayer(player) {
      this.player = player;
    },
    overHandler() {
      if (this.player) {
        const status = this.player.getStatus()

        console.log(status, "直播播放器状态")

        if (status !== 'error') {
          this.isHover = this.meetingLiveStatus === 'LI' && this.hlsLiveUrl;
        }
      }


    },
    leaveHandler() {
      this.isHover = false;
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
