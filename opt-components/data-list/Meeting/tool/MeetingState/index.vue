<template>
  <!--  直播状态-->
  <div :style='{background:states[state].color}' class='position_top'>
    <img v-if="state === 'LI'" src="~assets/images/home/<USER>" alt="live" class="live_img">
    <svg-icon v-else :icon-class="states[state].icon" class-name="icons"/>
    <span>
      {{ states[state].lable }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'MeetingState',
  /**
   * 会议状态
   */
  props: {
    state: {
      type: String,
      default: 'NL',
      required: true
    }
  },
  data() {
    return {
      states: {
        'LE': {
          lable: '录播',
          color: '#0581CE',
          icon: 'state-le'
        },
        'NS': {
          lable: '预告',
          color: '#12AD6C',
          icon: 'state-ns'
        },
        'LI': {
          lable: '直播',
          color: '#ED4B00',
          icon: ''
        },
        'NL': {
          lable: '',
          color: ''
        }
      }
    }
  }
}
</script>

<style lang='less' scoped>
.position_top {
  width: 53px;
  height: 24px;
  border-radius: 4px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;

  .live_img {
    width: 20px;
    height: 20px;
    margin-right: 1px;
  }

  .icons {
    width: 13px;
    height: 13px;
    margin-right: 4px;
  }
}
</style>
