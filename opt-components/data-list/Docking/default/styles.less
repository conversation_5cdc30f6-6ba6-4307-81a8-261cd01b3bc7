
.docking_default_item_wrapper {
  cursor: pointer;
  display: block;
  border-radius: 8px;
  background: #FFF;
  padding: 16px 18px;
  transition: all .3s ease;

  .header_tip {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;

    .label_wrapper {
      span {
        color: #88AAC2;
        font-size: 14px;
        margin-right: 12px;
        line-height: 1.5;
      }
    }

    .link_label {
      flex-shrink: 0;
      color: #00B75D;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  .title {
    color: #333;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 12px;
    height: 48px;

    .title_label {
      vertical-align: 1px;
      margin-bottom: 4px;
      font-size: 11px;
      padding: 0 4px;
      border-radius: 4px;
    }
  }

  .docking_info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .publisher {
      display: flex;
      align-items: center;
      color: #666;
      font-size: 14px;

      .image {
        flex-shrink: 0;
        border-radius: 50%;
        overflow: hidden;
      }

      span {
        margin-left: 4px;
      }
    }

    .release_time {
      color: #666;
      font-size: 12px;
      flex-shrink: 0;
      margin-left: 8px;
    }
  }

  &:hover{
    .title{
      color: #0581CE;
    }
  }
}

.docking_default_item_hove_wrapper {
  border: 1px solid #EAEEF0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 2px 20px 3px rgba(0, 0, 0, 0.1);

    .title{
      color: #0581CE;
    }
  }
}
