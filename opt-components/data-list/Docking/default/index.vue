<template>
  <div :class="isMoveEffect ? 'docking_default_item_hove_wrapper' :''" class="docking_default_item_wrapper"
       @click="jumpPage">
    <div class="header_tip">
      <div class="label_wrapper">
        <div class="label_wrapper text-limit-1">
        <span
          v-for="item in typeList"
          :key="item.id"
          class="label">#{{ item.name }}</span>
        </div>
      </div>
      <div v-if="links>0" class="link_label">{{ `已成功链接${links}次` }}</div>
    </div>

    <div :style="!isMoveEffect ? {height:'unset'} : {}" class="title text-limit-2">
      <span v-if="classification.name"
            :style="{
        background:labelStatus.hasOwnProperty(classification.name) ? labelStatus[classification.name].bgColor : labelStatus['神外'].bgColor,
            color:labelStatus.hasOwnProperty(classification.name) ? labelStatus[classification.name].color : labelStatus['神外'].color}"
            class="title_label">{{ classification.name }}</span>
      <span v-html="title"></span>
    </div>

    <div class="docking_info">

      <div v-if="publishSubject === 1" class="publisher" @click.stop="jumpUserPage">
        <div class="image">
          <ZipImg
            :src="publisher.avatarAddress || require('/assets/images/industrial/avatar.png')"
            :width="28"
            :height="28"
          />
        </div>
        <span class="text-limit-1">{{ publishName }}</span>
      </div>


      <div v-else-if="publishSubject !== 1 && publisher.identity === 4" class="publisher" @click.stop="jumpProductPage">
        <div class="image">
          <ZipImg
            :src="publisher.productLineShareImage || require('/assets/images/industrial/avatar.png')"
            :width="28"
            :height="28"
          />
        </div>
        <span class="text-limit-1">{{
            publisher.brandName ? `${publisher.brandName}·${publisher.productLineName}` : publisher.company
          }}</span>
      </div>

      <div v-else-if="publishSubject !== 1 && publisher.identity === 1" class="publisher"
           @click.stop="jumpDepartmentPage">
        <div class="image">
          <ZipImg
            :src="publisher.departmentIcon || require('/assets/images/industrial/avatar.png')"
            :width="28"
            :height="28"
          />
        </div>
        <span class="text-limit-1">{{
            publisher.hospitalName ? `${publisher.hospitalName}·${publisher.departmentName}` : publisher.company
          }}</span>
      </div>

      <div v-else class="publisher">
        <div class="image">
          <ZipImg
            :src="publisher.avatarAddress || require('/assets/images/industrial/avatar.png')"
            :width="28"
            :height="28"
          />
        </div>
        <span class="text-limit-1">{{ publisher.company || publisher.realName }}</span>
      </div>

      <div v-if="periodOfValidity" class="release_time">{{ periodOfValidity }}截止</div>
    </div>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "DockingDefaultItem",
  components: {ZipImg},
  props: {
    isMoveEffect: {
      type: Boolean,
      default: true
    },
    infoId: {
      type: Number,
      default: null
    },
    title: {
      type: String,
      default: ''
    },
    typeList: {
      type: Array,
      default: () => []
    },
    classification: {
      type: Object,
      default: {}
    },
    publisher: {
      type: Object,
      default: {}
    },
    publishSubject: {
      type: Number,
      default: null
    },
    publishName: {
      type: String,
      default: ''
    },
    periodOfValidity: {
      type: String,
      default: ''
    },
    links: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      labelStatus: {
        "药品": {
          bgColor: 'rgba(6, 217, 90, 0.10)', color: '#06D95A'
        },
        "神外": {
          bgColor: 'rgba(55, 139, 242, 0.10)', color: '#378BF2'
        },
        "神介": {
          bgColor: 'rgba(3, 183, 214, 0.10)', color: '#03B7D6'
        },
        "诊断": {
          bgColor: 'rgba(108, 11, 235, 0.10)', color: '#6C0BEB'
        },
        "设备": {
          bgColor: 'rgba(11, 67, 235, 0.10)', color: '#0B43EB'
        }
      }
    }
  },
  methods: {
    jumpPage() {
      const {href} = this.$router.resolve({
        path: `/idp/detail/${this.infoId}`
      })
      window.open(href, '_blank')
    },
    jumpUserPage() {
      const {href} = this.$router.resolve({
        path: `/user-center?profileUserId=${this.publisher.id}`
      })
      window.open(href, '_blank')
    },
    jumpProductPage() {
      if (this.publisher.productLineId) {
        const {href} = this.$router.resolve({
          path: `/bms/classify/3/product-line-details/${this.publisher.productLineId}`
        })
        window.open(href, '_blank')
      }
    },
    jumpDepartmentPage() {
      if (this.publisher.departmentId) {
        const {href} = this.$router.resolve({
          path: `/department/detail?departmentId=${this.publisher.departmentId}`
        })
        window.open(href, '_blank')
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
