.info_default_item_container {
  border-radius: 8px;
  background: #F8F8F8;
  transition: all .2s;

  .image {
    width: 100%;
    height: 135px;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    position: relative;

    .mask {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .3);
      transition: opacity .2s ease-in-out;
      display: flex;
      justify-content: center;
      align-items: center;

      .icons {
        width: 48px;
        height: 48px;
      }
    }
  }

  .content {
    padding: 16px;

    .title {
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      height: 40px;
      font-weight: bold;
      margin-bottom: 22px;

      .essences{
        width: 16px;
        height: 16px;
        margin-bottom: 2px;
        vertical-align:middle;
      }
    }

    .tips {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 38px;


      .tips_left {
        flex: 1;

        .author {
          color: #999EA4;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 4px;
        }

        .time {
          color: #999EA4;
          font-size: 12px;
        }
      }

      .tips_right {
        flex-shrink: 0;
        padding-left: 10px;
        .product_icon {
          margin-left: 6px;
          flex-shrink: 0;
          width: 22px;
          height: 22px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 4px;
          background: rgba(98, 168, 63, 0.12);
          transition: all .2s ease;
          position: relative;

          .icons {
            width: 12px;
            height: 12px;
          }

          &:hover {
            background: rgba(98, 168, 63, 0.20);

            .product_list_popup {
              display: block !important;
            }
          }

          .product_list_popup {
            z-index: 5;
            display: none;
            width: 202px;
            position: absolute;
            right: 0;
            top: calc(100% - 10px);
            padding-top: 22px;

            .product_list_popup_content {
              background: rgba(0, 0, 0, 0.65);
              backdrop-filter: saturate(50%) blur(4px);
              border-radius: 8px;
            }

            .product_item {
              padding: 8px 10px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              color: #FFF;
              font-size: 12px;
              line-height: 18px;
              border-bottom: 1px solid #666E7F80;

              .product_jump_svg {
                width: 16px;
                height: 16px;
              }

              &:hover {
                color: #CDCDCD;
              }
            }
          }
        }
      }
    }
  }

  &:hover {
    .content > .title {
      color: var(--theme-color);
    }
  }
}
