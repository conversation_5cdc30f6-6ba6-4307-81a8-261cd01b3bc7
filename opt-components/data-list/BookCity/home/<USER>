<template>
  <div @click="$emit('click')">
    <nuxt-link :to="`/mall/product/${bookId}`" target="_blank" class="wrapper">
      <div class="image">
        <ZipImg
          :width="80"
          :height="60"
          :alt="title"
          :src="image"
        />
      </div>
      <div class="info">
        <div class="title text-limit-2">{{ title }}</div>
        <div class="author text-limit-1">
          {{authorNames}}
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "BookCityHomeItem",
  components: {ZipImg},
  props: {
    bookId: {
      type: Number,
      default: null
    },
    image: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    authorNames:{
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
