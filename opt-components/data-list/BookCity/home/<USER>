.wrapper {
  display: flex;

  .image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    margin-right: 10px;
    border-radius: 4px;
    overflow: hidden;
    background: #708AA214;
  }

  .info {
    .title {
      height: 38px;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .author {
      color: #666;
      font-size: 12px;
    }
  }

  &:hover {
    .info > .title {
      color: var(--theme-color);
    }
  }
}
