<template>
  <div class="cursor" @click="$router.push({ path: '/cloudclassroomCourse', query: { courseId: classId } })">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="image"
          fill
        />
        <div class="mask">
          <svg-icon icon-class="icon_play_n" class-name="icons"/>
        </div>
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <span>{{ title }}</span>
        </p>
        <div class="tips">{{ $tool.formatterNumUnit(showViews) }}次播放</div>
      </div>
    </div>
  </div>
</template>

<script>
import {ZipImg} from "../../../component";

export default {
  name: "ClassDefaultItem",
  components: {
    ZipImg
  },
  props: {
    classId: {
      type: Number,
      default: null
    },
    image: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    showViews: {
      type: Number,
      default: 0
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
