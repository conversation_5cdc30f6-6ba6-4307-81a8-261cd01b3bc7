.info_default_item_container {
  border-radius: 8px;
  background: #F8F8F8;
  transition: all .2s;

  .image {
    width: 100%;
    height: 135px;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    position: relative;

    .mask {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .3);
      transition: opacity .2s ease-in-out;
      display: flex;
      justify-content: center;
      align-items: center;

      .icons {
        width: 48px;
        height: 48px;
      }
    }

    img {
      transition: all .2s ease;
    }
  }

  .content {
    padding: 16px;

    .title {
      color: #333333;
      font-size: 14px;
      line-height: 20px;
      height: 40px;
      font-weight: bold;
      margin-bottom: 42px;

      .essences {
        width: 16px;
        height: 16px;
        margin-bottom: 2px;
        vertical-align: middle;
      }
    }

    .tips {
      font-size: 12px;
      line-height: 18px;
      color: #999EA4;
    }
  }

  &:hover {
    .content > .title {
      color: var(--theme-color);
    }

    .image img {
      transform: scale(1.05);
    }
  }
}
