<template>
  <div class="wrapper">
    <nuxt-link :to="{ path: '/cloudclassroomCourse', query: { courseId: classId } }" class="content">
      <div class="image">
        <zip-img
          :width="240"
          :height="135"
          :src="cover"
          :alt="title"
        />
        <div v-if="isTryAndSee === 'T'" class="try_see">
          <svg-icon icon-class="try_see_play" class-name="icons"/>
          <span>试看</span>
        </div>
        <div class="mask">
          <svg-icon icon-class="icon_play_n" class-name="icons"/>
        </div>
      </div>
      <div class="description">
        <p class="title text-limit-2">{{ title }}</p>
        <div class="tips">
          <div class="play_num">{{ $tool.formatterNumUnit(showViews) }}次播放</div>
          <div class="price">{{ money ? '￥' + money : '' }}</div>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "ClassHomeDefaultItem",
  components: {ZipImg},
  props: {
    classId: {
      type: Number,
      default: null
    },
    cover: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    showViews: {
      type: Number,
      default: null
    },
    money: {
      type: Number,
      default: null
    },
    isTryAndSee: {
      type: String,
      default: "T"
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
