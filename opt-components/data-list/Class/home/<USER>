
.wrapper {

  .content {
    border-radius: 8px;
    overflow: hidden;
    display: block;

    .image {
      width: 100%;
      height: 135px;
      position: relative;

      .try_see {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.26);
        width: 53px;
        height: 24px;
        border-radius: 3px;
        font-size: 12px;
        color: #FFF;
        backdrop-filter: blur(34px);

        display: flex;
        align-items: center;
        justify-content: center;

        .icons {
          width: 11px;
          height: 11px;
          margin-right: 1.5px;
        }
      }

      .mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(0, 0, 0, .3);
        opacity: 0;
        transition: opacity .2s ease-in-out;
        display: flex;
        justify-content: center;
        align-items: center;

        .icons {
          width: 48px;
          height: 48px;
        }
      }
    }

    .description {
      padding: 16px 0;

      .title {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        height: 40px;
        margin-bottom: 36px;
      }

      .tips {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .play_num {
          padding: 3px;
          border-radius: 3px;
          background: rgba(112, 138, 162, 0.08);
          font-size: 12px;
          color: #708AA2;
        }

        .price {
          font-size: 16px;
          color: #E76333;
          font-weight: 600;
        }
      }
    }
  }

  &:hover {
    .content > .image .mask {
      opacity: 1;
    }

    .title {
      color: var(--theme-color);
    }
  }
}
