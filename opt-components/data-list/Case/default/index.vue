<template>
  <a :href="`/case/detail-ugc?id=${infoId}`" target="_blank">
    <div class="info_default_item_container">
      <div class="image">
        <ZipImg
          :width="240"
          :height="135"
          :src="image"
          fill
        />
      </div>
      <div class="content">
        <p class="title text-limit-2">
          <SvgIcon v-if="essences === 'T'" icon-class="essences" class-name="essences"/>
          <svg-icon v-if="bmsAuth === 1"  icon-class="auth-new" style="width: 18px;height: 18px;vertical-align: sub"/>
          <span>{{ title }}</span>
        </p>
        <div class="tips">
          <div class="tips_left">
            <div class="user_image">
              <ZipImg
                :width="27"
                :height="27"
                :src="authorImage"
                :type-user="true"
                fill
              />
            </div>
            <div class="author_info">
              <p class="author text-limit-1">{{ authorName }}</p>
              <p class="time">{{ publishDate }}</p>
            </div>
          </div>
          <div class="tips_right">
            <div v-if="productList.length > 0" class="product_icon">
              <SvgIcon icon-class="product-home" class-name="icons"/>
              <div class="product_list_popup">
                <div class="product_list_popup_content">
                  <a
                    v-for="item in productList"
                    :key="item.id"
                    target="_blank"
                    :href="`/bms/classify/-/product-details/${item.productId || item.id}`"
                    class="product_item">
                    <span class="text-limit-1">#{{ item.name }}</span>
                    <svg-icon icon-class="product_jump" class-name="product_jump_svg"/>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a>
</template>

<script>
import {ZipImg} from "../../../component";

export default {
  name: "MPArticleDefaultItem",
  components: {
    ZipImg
  },
  props: {
    infoId: {
      type: Number,
      default: null
    },
    image: {
      type: String,
      default: ""
    },
    authorImage: {
      type: String,
      default: ""
    },
    title: {
      type: String,
      default: ""
    },
    authorName: {
      type: String,
      default: ""
    },
    publishDate: {
      type: String,
      default: ""
    },
    productList: {
      type: Array,
      default: () => []
    },
    essences: {
      type: String,
      default: "F"
    },
    bmsAuth:{}
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
