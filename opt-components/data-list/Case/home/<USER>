<template>
  <div>
    <nuxt-link
      target="_blank"
      :to="articleType === 'P' ? `/case/detail-pgc?id=${infoId}` : `/case/detail-ugc?id=${infoId}`"
      class="wrapper">
      <div class="image">
        <zip-img
          :src="infoImg"
          :alt="infoTitle"
          :width="126"
          :height="126"
        />
      </div>
      <div class="info_content">
        <div class="title text-limit-2">
          {{ infoTitle }}
        </div>
        <div class="info_tips">
          <div class="tips_left">
            <div v-if="authorNameList.length>0" class="userAvatar_wrapper">
              <div v-for="item in authorNameList.slice(0,2)" :key="item.id" class="userAvatar_item">
                <zip-img
                  :type-user="true"
                  :src="item.userAvatar"
                  :alt="item.authorName"
                  :width="27"
                  :height="27"
                />
              </div>
            </div>

            <div class="info">
              <p class="text-limit-1" style="margin-bottom: 3px">{{ userName }}</p>
              <p class="text-limit-1">{{ publishDate && timeStamp.timestampFormat(publishDate) }}</p>
            </div>
          </div>
          <div v-if="isProduct" class="product_icon">
            <SvgIcon icon-class="product-home" class-name="icons"/>
          </div>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script>
import ZipImg from "../../../component/ZipImg/index.vue";

export default {
  name: "CaseHomeDefaultItem",
  components: {ZipImg},
  props: {
    articleType: {
      type: String,
      default: "P"
    },
    infoId: {
      type: Number,
      default: null
    },
    infoImg: {
      type: String,
      default: ""
    },
    infoTitle: {
      type: String,
      default: ""
    },
    publishDate: {},
    authorNameList: {
      type: Array,
      default: () => []
    },
    isProduct: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    userName() {
      if (this.authorNameList.length === 0) {
        return "脑医资讯助手"
      } else if (this.authorNameList.length === 1) {
        return this.authorNameList[0].authorName
      } else if (this.authorNameList.length > 1) {
        return this.authorNameList[0].authorName + `等${this.authorNameList.length}位作者`
      } else {
        return ""
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
