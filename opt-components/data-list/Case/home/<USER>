
.wrapper {
  display: flex;

  .image {
    flex-shrink: 0;
    border-radius: 8px 0 0 8px;
    overflow: hidden;
    width: 126px;
    height: 126px;

    img{
      transition: all .1s ease-in 0s;
    }
  }

  .info_content {
    flex: auto;
    margin-left: 12px;
    display: flex;
    flex-flow: column;
    justify-content: space-between;

    .title {
      font-size: 14px;
      font-weight: 600;
    }

    .info_tips {
      display: flex;
      justify-content: space-between;
      align-items: end;

      .tips_left {
        display: flex;
        align-items: center;

        .userAvatar_wrapper {
          display: flex;
          align-items: center;
          margin-right: 8px;

          .userAvatar_item {
            width: 27px;
            height: 27px;
            border-radius: 50%;
            overflow: hidden;
            margin-left: -13.5px;

            &:first-child {
              margin-left: 0;
            }
          }
        }

        .info {
          p {
            font-size: 12px;
            color: #999EA4;
          }
        }


      }

      .product_icon {
        margin-left: 6px;
        flex-shrink: 0;
        width: 22px;
        height: 22px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: rgba(98, 168, 63, 0.12);
        transition: all .2s ease;

        .icons {
          width: 12px;
          height: 12px;
        }

        &:hover {
          background: rgba(98, 168, 63, 0.20);
        }
      }
    }
  }

  &:hover {
    .info_content .title {
      color: var(--theme-color);
    }

    .image img {
      transform: scale(1.05);
      transition: transform .2s ease-in;
    }
  }
}
