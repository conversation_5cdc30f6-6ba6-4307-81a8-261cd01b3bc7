<template>
  <div class="home_list_card_wrapper" :style="{background:bgColor}">
    <div class="home_list_card_content" :style="{backgroundImage:`url(${bgImg})`}">
      <div class="title">{{ title }}</div>
      <div class="content">
        <nuxt-link
          v-for="item in list"
          :key="item.id"
          :target="item.name === '投审稿' ? '_self' : '_blank'"
          :to="item.url"
          class="card_item text-limit-1"
          :title="item.name"
          @click="analysysHandler(item.name)"
        >
          {{ item.name }}
        </nuxt-link>
      </div>
    </div>


  </div>
</template>

<script>
export default {
  name: "HomeListCard",
  props: {
    title: {
      type: String,
      default: "标题"
    },
    bgImg: {},
    bgColor: {
      type: String,
      default: '#2F96D6'
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 埋点
    analysysHandler(name) {
      this.$analysys.btn_click(`${this.title}-${name}`, window.document.title)
    }
  }
}
</script>

<style scoped lang="less">
.home_list_card_wrapper {
  border-radius: 8px;
  overflow: hidden;
  user-select: none;

  .home_list_card_content {
    padding: 20px 14px 14px;
    background-repeat: no-repeat;
    background-size: 100% auto;
  }


  .title {
    font-size: 16px;
    color: #FFF;
    font-weight: 500;
    margin-bottom: 12px;
  }

  .content {
    border-radius: 8px;
    background: #FFFFFF;
    display: grid;
    gap: 0 5px;
    grid-template-columns: 1fr 1fr 1fr;

    .card_item {
      color: var(--font-color-333);
      text-align: center;
      height: 38px;
      line-height: 38px;
      font-size: 14px;

      &:hover {
        color: var(--theme-color);
      }
    }
  }
}
</style>
