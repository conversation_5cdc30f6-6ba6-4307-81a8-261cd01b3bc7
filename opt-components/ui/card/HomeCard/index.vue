<template>
  <div class="home_card_wrapper">
    <div class="home_card_title">
      <div class="left_item">
        <svg-icon :icon-class="svg" class-name="title_svg"/>
        <span>{{ title }}</span>
      </div>
      <a target="_blank" :href="moreBtnHref" class="right_item" @click="analysysFun">
        <span>{{ moreBtnTitle }}</span>
        <svg-icon icon-class="home-more" class-name="title_svg"/>
      </a>
    </div>

    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "HomeCard",
  props: {
    title: {
      type: String,
      default: "标题"
    },
    svg: {
      type: String,
      default: "home-card"
    },
    moreBtnHref: {
      type: String,
      default: "/"
    },
    moreBtnTitle: {
      type: String,
      default: "查看更多"
    }
  },
  methods:{
    analysysFun(){
      this.$analysys.btn_click(`${this.title}-查看更多`, window.document.title)
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
