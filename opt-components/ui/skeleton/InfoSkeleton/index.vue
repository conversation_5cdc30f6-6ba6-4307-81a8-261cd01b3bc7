<template>
  <el-skeleton style="width: 100%" :loading="loading" animated>
    <template slot="template">
      <div v-for="item in limit" :key="item">
        <el-skeleton-item
          variant="image"
          style="width: 100%; height: 135px;border-radius: 8px 8px 0 0"
        />
        <div
          style="border-radius:0 0 8px 8px;background: #F8F8F8; height: 133px;padding: 16px;box-sizing: border-box;display: flex;flex-flow: column;justify-content: space-between">
          <el-skeleton-item variant="h3" style="width: 90%;"/>
          <el-skeleton-item variant="h3" style="width: 50%;"/>
          <div>
            <el-skeleton-item variant="h4" style="width: 30%;"/>
            <el-skeleton-item variant="h4" style="width: 20%;"/>
          </div>
        </div>
      </div>
    </template>
  </el-skeleton>
</template>

<script>
export default {
  name: "InfoSkeleton",
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 16
    }
  }
}
</script>

<style scoped lang="less">

</style>
