// import axios from 'axios'
// import getCookie from '@/uitls/get-cookie'
//
// /**
//  * 使用本次存储的数据去请求axios 请求不到,所以使用中间件的方式拿cookie
//  * @param app
//  * @param req
//  * @param redirect
//  * @param route
//  * @returns {Promise<void>}
//  */
// export default async function({ app, req, redirect, route }) {
//   let token
//   if (req) { //req只有在服务端会触发
//     //当用户第一次进入页面时，会先触发服务端，继续访问其他页面，触发客户端
//     //F5刷新后，相当于第一次进入页面，触发服务端
//     //客户端req:404
//     token = getCookie.getcookiesInServer(req).token
//     //从服务端获取cookie
//   } else {
//     token = getCookie.getcookiesInClient('token')
//     //从客户端获取cookie
//   }
//   if (!token) {
//     // redirect({ path: '/user/login' });
//     //服务端进行重定向时， 会导致window、sessionStorage这些 全局变量无法使用
//     //需要使用process.client 判断是否是客户端
//     //如果在客户端（浏览器），进行重定向则无需
//   } else {
//     const myaxios = axios.create({
//       withCredentials: true, //  send cookies when cross-domain requests
//       timeout: 30000 //  request timeout
//     })
//     myaxios.interceptors.request.use(
//       config => {
//         console.log('中间件axios触发请求拦截')
//         config.headers['Authorization'] = `Bearer ${token}`  //请求头加上token
//         return config
//       },
//       err => {
//         return Promise.reject(err)
//       })
//   }
// }
