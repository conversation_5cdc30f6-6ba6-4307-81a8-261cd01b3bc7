// middleware/auth.js

function checkUserAuthentication(app, store) {
  // 这里你需要自己实现一个函数来检查用户是否已经登录
  // 如果用户已经登录则返回 true，否则返回 false
  const token = app.$cookies.get('medtion_token_only_sign-name')
  const vtoken = store.state.auth.token
  const isLogin = !!(token || vtoken)
  return isLogin
}
export default function ({ route, redirect, store, app, req }) {
  // 检查用户是否已经登录
  const isAuthenticated = checkUserAuthentication(app, store) // 你需要自己实现这个函数
  //
  // 如果用户没有登录且访问的页面需要登录，则重定向到登录页
  if (!isAuthenticated && route.path.includes('/message-center')) {
    let fullUrl = req.headers.host + req.originalUrl
    fullUrl = req.protocol ? req.protocol + '://' + fullUrl : fullUrl // 完整路径
    store.commit('editBackUrl', fullUrl + `?&fallbackUrl=${fullUrl}`) // 用于微信跳转
    redirect({
      path: '/signin',
      query: { fallbackUrl: req.originalUrl, thirdParty: 'thirdParty' },
    })
  }
}
