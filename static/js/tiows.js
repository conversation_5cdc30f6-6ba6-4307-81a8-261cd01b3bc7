if (typeof (tio) === 'undefined') {
  tio = {}
}
Date.prototype.Format = function(fmt) { //author: meizz
  var o = {
    'M+': this.getMonth() + 1,                 //月份
    'd+': this.getDate(),                    //日
    'h+': this.getHours(),                   //小时
    'm+': this.getMinutes(),                 //分
    's+': this.getSeconds(),                 //秒
    'q+': Math.floor((this.getMonth() + 3) / 3), //季度
    'S': this.getMilliseconds()             //毫秒
  }
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  for (var k in o)
    if (new RegExp('(' + k + ')').test(fmt))
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
  return fmt
}

function getDateDiff(startDate, endDate) {
  var startTime = new Date(startDate)
  var endTime = new Date(endDate)
  var days = (startTime - endTime) / (1000 * 60 * 60 * 24)
  return days
}

// eslint-disable-next-line no-unused-vars
var tio = {}
tio.ws_protocol = 'ws'
tio.heartbeatTimeout = 50000
tio.reconnInterval = 1000
tio.binaryType = 'blob'
tio.ws_url
tio.ip = {}
var host = window.location.hostname
var protocolStr = document.location.protocol
if (protocolStr == 'http:') {
  tio.ws_protocol = 'ws'
} else if (protocolStr == 'https:') {
  tio.ws_protocol = 'wss'
} else {
  tio.ws_protocol = 'ws'
  console.log('other protocol')
}
if (host.indexOf('www') >= 0 || host.indexOf('ni') >= 0) {
  tio.ip = '************'
  tio.ws_url = 'ws.medtion.com/wss/'
  // tio.ip = "*************";
} else {
  if (host.indexOf('localhost') >= 0 || host.indexOf('127.0.0.1') >= 0) {
    tio.ip = '127.0.0.1'
    tio.ws_url = 'wsdev.medtion.com/wss/'
  } else {
    tio.ip = '*************'
    tio.ws_url = 'wsdev.medtion.com/wss/'
  }
}

tio.port = 9326
tio.ws = {}
tio.formatDateTime = {}
tio.currentUser = {}
tio.messageInfoType = {
  MSG_PING: '0',
  MSG_READY: '1',
  MSG_MESSAGE: 'msg',
  MSG_USERINFO: '1314'
}

tio.MessageTargetType = {
  FRIEND: 'signal',
  CHAT_GROUP: 'group'
}

tio.other = {
  DIGG: 'digg',
  JOIN_GROUP: 'join',
  CHAT_GROUP: '1',
  ONLINE_COUNT: 'count'
}

/**
 * 请求状态码
 * @type {{CHAT_GROUP: string, DIGG: string, JOIN_GROUP: string, ONLINE_COUNT: string}}
 */
tio.request = {
  DIGG: 'digg',
  JOIN_GROUP: 'join',
  CHAT_GROUP: '1',
  ONLINE_COUNT: 'count',
  GROUP_GIFT: 'groupGift',
  BARRAGE: 'barrage',
  AGENDABARRAGE: 'agendaBarrage'
}
/**
 * 响应状态码
 * @type {{DIGG: string, GROUP_GIFT: string, JOIN_GROUP: string}}
 */
tio.response = {
  popup:'popup',
  DIGG: 'digg',
  GROUP_GIFT: 'groupGift',
  JOIN_GROUP: 'join',
  SWITCH_GROUP_VOTE_STATUS: 'switch_group_vote_status',
  MEETING_FIELDS_MANUAL_STATUS: 'meeting_fields_manual_status',
  BARRAGE: 'barrage', // 直播
  AGENDABARRAGE: 'agendaBarrage',// 录播
  SWITCH_QUESTIONNAIRE_STATUS: 'switch_questionnaire_status', // 问卷调查开个
  QUESTIONNAIRE_SEND: 'switch_questionnaire_send', // 问卷调查弹框下发
  QUESTION_ITEM_IS_ANSWER: 'switch_question_item_is_answer'

}
/**
 * @param {*} ws_protocol wss or ws
 * @param {*} ip
 * @param {*} port
 * @param {*} paramStr 加在ws url后面的请求参数，形如：name=张三&id=12
 * @param {*} param 作为tio.ws对象的参数，由业务自己使用，框架不使用
 * @param {*} handler
 * @param {*} heartbeatTimeout 心跳时间 单位：毫秒
 * @param {*} reconnInterval 重连间隔时间 单位：毫秒
 * @param {*} binaryType 'blob' or 'arraybuffer';//arraybuffer是字节
 */

tio.ws2 = function(url, paramStr, param, handler, heartbeatTimeout, reconnInterval, binaryType) {
  this.url = tio.ws_protocol + '://' + url
  this.binaryType = binaryType || 'arraybuffer'
  if (paramStr) {
    this.url += '?' + paramStr
    this.reconnUrl = this.url + '&'
  } else {
    this.reconnUrl = this.url + '?'
  }
  this.reconnUrl += 'tiows_reconnect=true'
  this.param = param

  this.handler = handler
  this.heartbeatTimeout = heartbeatTimeout
  this.reconnInterval = reconnInterval

  this.lastInteractionTime = function() {
    if (arguments.length == 1) {
      this.lastInteractionTimeValue = arguments[0]
    }
    return this.lastInteractionTimeValue
  }

  this.heartbeatSendInterval = heartbeatTimeout / 2

  this.connect = function(isReconnect) {
    var _url = this.url
    if (isReconnect) {
      _url = this.reconnUrl
    }
    var ws = new WebSocket(_url)
    this.ws = ws

    ws.binaryType = this.binaryType // 'arraybuffer'; // 'blob' or 'arraybuffer';//arraybuffer是字节
    var self = this
    ws.onopen = function(event) {
      console.log('websocket已连接')
      self.handler.onopen.call(self.handler, event, ws)
      self.lastInteractionTime(new Date().getTime())

      self.pingIntervalId = setInterval(function() {
        self.ping(self)
      }, self.heartbeatSendInterval)
    }
    ws.onmessage = function(event) {
      self.handler.onmessage.call(self.handler, event, ws)
      self.lastInteractionTime(new Date().getTime())
    }
    ws.onclose = function(event) {
      console.log('websocket 断开: ' + event.code + ' ' + event.reason + ' ' + event.wasClean, event)
      clearInterval(self.pingIntervalId) // clear send heartbeat task

      try {
        self.handler.onclose.call(self.handler, event, ws)
      } catch (error) {
      }
      self.reconn(event)
    }
    ws.onerror = function(event) {
      self.handler.onerror.call(self.handler, event, ws)
    }

    return ws
  }

  this.reconn = function(event) {
    var self = this
    setTimeout(function() {
      var ws = self.connect(true)
      self.ws = ws
    }, self.reconnInterval)
  }

  this.ping = function() {
    var iv = new Date().getTime() - this.lastInteractionTime() // 已经多久没发消息了
    // 单位：秒
    if ((this.heartbeatSendInterval + iv) >= this.heartbeatTimeout) {
      this.handler.ping(this.ws)
    }
  }

  this.close = function() {
    this.ws.onclose = function() {
    }
    this.ws.close()
  }

  this.send = function(data) {
    this.ws.send(data)
  }

  this.joinGroup = function(module, groupId, userId) {
    var sendInfo = {
      code: tio.request.JOIN_GROUP,
      module: module,
      groupId: groupId,
      userId: userId || null
    }
    this.ws.send(JSON.stringify(sendInfo))
  }

  this.sendDigg = function(module, groupId, userId) {
    var sendInfo = {
      code: tio.request.DIGG,
      module: module,
      groupId: groupId,
      userId: userId || null
    }
    this.ws.send(JSON.stringify(sendInfo))
  }
  this.sendSvg = function(module, groupId, userId, params) {
    var sendInfo = {
      code: tio.request.GROUP_GIFT,
      module: module,
      groupId: groupId,
      groupGift: params,
      userId: userId || null
    }
    this.ws.send(JSON.stringify(sendInfo))
  }
  this.sendBarrage = function(module, fieldId, userid, text, color, agendaId, videoCurrentDuration) {
    // 判断手机环境
    var u = navigator.userAgent
    var loginMethod = '3'
    var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1
    // android终端
    var isiOS = !!u.match(/(i[^;]+;( U;))? CPU.+Mac OS X/)
    if (!isAndroid && !isiOS) loginMethod = '1'
    var sendInfo = {
      code: (agendaId ? tio.request.AGENDABARRAGE : tio.request.BARRAGE),
      module: module,
      groupId: fieldId,
      barrageInfo: {
        creatorId: userid,
        text: text,
        color: color,
        fieldsId: fieldId,
        sendTime: new Date().Format('yyyy-MM-dd hh:mm:ss'),
        loginMethod: loginMethod,
        agendaId: agendaId,
        videoCurrentDuration: videoCurrentDuration
      }
    }
    this.ws.send(JSON.stringify(sendInfo))
  }
}

tio.formatDateTime = function(e) {
  var t = e.getFullYear(), a = e.getMonth() + 1
  a = 10 > a ? '0' + a : a
  var n = e.getDate()
  n = 10 > n ? '0' + n : n
  var s = e.getHours()
  s = 10 > s ? '0' + s : s
  var l = e.getMinutes()
  l = 10 > l ? '0' + l : l
  var r = e.getSeconds()
  return r = 10 > r ? '0' + r : r, t + '-' + a + '-' + n + ' ' + s + ':' + l + ':' + r
}
