var wsim_fieldsId // 会议第一个会场
var wsim_presentFieldsId // 当前会场
var wsim_module
var wsim_handler
var wsim_userId
var wsim_dataHandle
var tiows
var test = 'test'
// 点赞
// firstFieldsId 会议第一个会场 点赞用
// presentFieldsId 当前会场id 给弹幕用
// eslint-disable-next-line camelcase
function wsim_initModule(userId, firstFieldsId, module, btn_class, dataHandle, presentFieldsId) {
  wsim_userId = userId
  wsim_fieldsId = firstFieldsId
  wsim_presentFieldsId = presentFieldsId
  wsim_module = module
  wsim_dataHandle = dataHandle
  wsim_handler = new IMHandler()
  openSocket()
}

/**
 * 点赞
 */
function likeHeatFun(userId) {
  tiows.sendDigg(wsim_module, wsim_fieldsId, userId + '')
}

// 弹幕
// eslint-disable-next-line camelcase
function wsim_initBarrage(userId, fieldsId, module, btn_class, agendaId, sendComments, text, color, isLive) {
  let currentTime = document.querySelectorAll('video')[0].currentTime.toFixed()
  if (isLive) {
    sendComments(text) // 如果是直播 要发送到评论
    currentTime = null
  }
  tiows.sendBarrage(wsim_module, fieldsId, userId, text, color, agendaId, currentTime)
  // if(document.getElementsByClassName(btn_class).length){
  //   for(var i=0,len=document.getElementsByClassName(btn_class).length;i<len;i++){
  //     // 点击 发送弹幕
  //     document.getElementsByClassName(btn_class)[i].addEventListener('click', function () {
  //       var text = document.querySelector('.barrage_input input').value.trim().replace(/\s+/g,' ');
  //       var color = document.querySelector('.barrage_input input').attributes.data_color.value;
  //       // 判断是否登录
  //       if(userId){
  //         // var barrage_time = parseInt($('video')[0].currentTime.toFixed());
  //         document.querySelector('.barrage_input input').value = '';
  //         if(text){
  //
  //           // isLive状态在会议中用来判断是否是直播
  //           if(isLive == 'live'){
  //             sendComments(text);
  //           }
  //         }
  //       }else{
  //         alert('请在微信浏览器中打开')
  //       }
  //     }, false);
  //     // 回车 发送弹幕
  //     $(".barrage_input input").on("keypress", function (e) {
  //       var keycode = e.keyCode;
  //       keywords = $('.search input').val();
  //       //keycode是键码，13也是电脑物理键盘的 enter
  //       if (keycode == "13") {
  //         e.preventDefault();
  //         var text = document.querySelector('.barrage_input input').value.trim().replace(/\s+/g,' ');
  //         var color = document.querySelector('.barrage_input input').attributes.data_color.value;
  //         // 判断是否登录
  //         if(userId){
  //           var barrage_time = parseInt($('video')[0].currentTime.toFixed());
  //           document.querySelector('.barrage_input input').value = '';
  //           if(text){
  //             tiows.sendBarrage(wsim_module, fieldsId, userId + "", text, color, agendaId, barrage_time);
  //           }
  //         }else{
  //           alert('请在微信浏览器中打开')
  //         }
  //       }
  //     })
  //   }
  // }
}

var IMHandler = function() {
  this.onopen = function(event, ws) {
    var data = event.data
    if (wsim_fieldsId === wsim_presentFieldsId) {
      tiows.joinGroup(wsim_module, wsim_fieldsId, wsim_userId)
    } else {
      tiows.joinGroup(wsim_module, wsim_fieldsId, wsim_userId)
      tiows.joinGroup(wsim_module, wsim_presentFieldsId, wsim_userId) // 加入弹幕群组
    }
  }
  /**
   * 收到服务器发来的消息
   * @param {*} event
   * @param {*} ws
   */
  this.onmessage = function(event, ws) {
    var data = event.data
    // var msgBody = eval('(' + data + ')');
    var reMsg = JSON.parse(data)
    //如果是消息类型
    if (reMsg.code === 1) {
      var respData = reMsg.data
      if (wsim_dataHandle) {
        wsim_dataHandle(respData)
      }
    } else {
      tiows.joinGroup(wsim_module, wsim_fieldsId, wsim_userId)
    }

  }
  this.onclose = function(e, ws) {
    // error(e, ws)
  }

  this.onerror = function(e, ws) {
    // error(e, ws)
  }
  /**
   * 发送心跳，本框架会自动定时调用该方法，请在该方法中发送心跳
   * @param {*} ws
   */
  this.ping = function(ws) {
    var sendInfo = {
      code: tio.messageInfoType.MSG_PING,
      module: wsim_module
    }
    tiows.send(JSON.stringify(sendInfo))
  }
}

/**
 * socket连接
 */
function openSocket() {
  tiows = new tio.ws2(tio.ws_url, null, null, wsim_handler, tio.heartbeatTimeout, tio.reconnInterval, tio.binaryType)
  tiows.connect()
}
