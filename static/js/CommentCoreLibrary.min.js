/*!Copyright(c) CommentCoreLibrary v0.11.1 (//github.com/jabbany/CommentCoreLibrary) - Licensed under the MIT License */
var CommentUtils,BinArray=function(){var n={bsearch:function(t,e,r){if(!Array.isArray(t))throw new Error("Bsearch can only be run on arrays");if(0===t.length)return 0;if(r(e,t[0])<0)return 0;if(0<=r(e,t[t.length-1]))return t.length;for(var i=0,n=0,o=0,s=t.length-1;i<=s;){if(o++,0<=r(e,t[(n=Math.floor((s+i+1)/2))-1])&&r(e,t[n])<0)return n;if(r(e,t[n-1])<0)s=n-1;else{if(!(0<=r(e,t[n])))throw new Error("Program Error. Inconsistent comparator or unsorted array!");i=n}if(1500<o)throw new Error("Iteration depth exceeded. Inconsistent comparator or astronomical dataset!")}return-1},binsert:function(t,e,r){var i=n.bsearch(t,e,r);return t.splice(i,0,e),i}};return n}(),CommentManager=function(){var e=function(t,e){return t.stime>e.stime?2:t.stime<e.stime?-2:t.date>e.date?1:t.date<e.date?-1:null!=t.dbid&&null!=e.dbid?t.dbid>e.dbid?1:t.dbid<e.dbid?-1:0:0};function t(t){var i=0;this._listeners={},this._lastPosition=0,this.stage=t,this.options={global:{opacity:1,scale:1,className:"cmt"},scroll:{opacity:1,scale:1},limit:0,seekTrigger:2e3},this.timeline=[],this.runline=[],this.position=0,this.factory=null,this.filter=null,this.csa={scroll:new CommentSpaceAllocator(0,0),top:new AnchorCommentSpaceAllocator(0,0),bottom:new AnchorCommentSpaceAllocator(0,0),reverse:new CommentSpaceAllocator(0,0),scrollbtm:new CommentSpaceAllocator(0,0)},this.width=this.stage.offsetWidth,this.height=this.stage.offsetHeight,this._startTimer=function(){if(!(0<i)){var e=(new Date).getTime(),r=this;i=window.setInterval(function(){var t=(new Date).getTime()-e;e=(new Date).getTime(),r.onTimerEvent(t,r)},10)}},this._stopTimer=function(){window.clearInterval(i),i=0}}return t.prototype.stop=function(){this._stopTimer(),this.runline.forEach(function(t){t.stop()})},t.prototype.start=function(){this._startTimer()},t.prototype.seek=function(t){this.position=BinArray.bsearch(this.timeline,t,function(t,e){return t<e.stime?-1:t>e.stime?1:0})},t.prototype.validate=function(t){return null!=t&&this.filter.doValidate(t)},t.prototype.load=function(t){this.timeline=t,this.timeline.sort(e),this.dispatchEvent("load")},t.prototype.insert=function(t){BinArray.binsert(this.timeline,t,e)<=this.position&&this.position++,this.dispatchEvent("insert")},t.prototype.clear=function(){for(;0<this.runline.length;)this.runline[0].finish();this.dispatchEvent("clear")},t.prototype.setBounds=function(){for(var t in this.width=this.stage.offsetWidth,this.height=this.stage.offsetHeight,this.dispatchEvent("resize"),this.csa)this.csa[t].setBounds(this.width,this.height);this.stage.style.perspective=this.width/Math.tan(55*Math.PI/180)/2+"px",this.stage.style.webkitPerspective=this.width/Math.tan(55*Math.PI/180)/2+"px"},t.prototype.init=function(t){if(this.setBounds(),null==this.filter&&(this.filter=new CommentFilter),null==this.factory)switch(t){case"legacy":this.factory=CommentFactory.defaultFactory();break;default:case"css":this.factory=CommentFactory.defaultCssRenderFactory()}},t.prototype.time=function(t){if(t-=1,this.position>=this.timeline.length||Math.abs(this._lastPosition-t)>=this.options.seekTrigger){if(this.seek(t),this._lastPosition=t,this.timeline.length<=this.position)return}else this._lastPosition=t;for(var e=[];this.position<this.timeline.length&&this.timeline[this.position].stime<=t;this.position++)0<this.options.limit&&this.runline.length+e.length>=this.options.limit||this.validate(this.timeline[this.position])&&e.push(this.timeline[this.position]);0<e.length&&this.send(e)},t.prototype.rescale=function(){},t.prototype._preprocess=function(t){return 8===t.mode?(console.log(t),this.scripting&&console.log(this.scripting.eval(t.code)),null):(null!=this.filter&&(t=this.filter.doModify(t)),t)},t.prototype._allocateSpace=function(t){switch(t.mode){default:case 1:this.csa.scroll.add(t);break;case 2:this.csa.scrollbtm.add(t);break;case 4:this.csa.bottom.add(t);break;case 5:this.csa.top.add(t);break;case 6:this.csa.reverse.add(t);break;case 7:case 17:}},t.prototype.send=function(t){Array.isArray(t)||(t=[t]),0!==(t=t.map(this._preprocess.bind(this)).filter(function(t){return null!==t})).length&&t.map(function(t){return this.factory.create(this,t)}.bind(this)).map(function(t){return this._allocateSpace(t),t}.bind(this)).forEach(function(t){t.y=t.y,this.dispatchEvent("enterComment",t),this.runline.push(t)}.bind(this))},t.prototype.finish=function(t){this.dispatchEvent("exitComment",t),this.stage.removeChild(t.dom);var e=this.runline.indexOf(t);switch(0<=e&&this.runline.splice(e,1),t.mode){default:case 1:this.csa.scroll.remove(t);break;case 2:this.csa.scrollbtm.remove(t);break;case 4:this.csa.bottom.remove(t);break;case 5:this.csa.top.remove(t);break;case 6:this.csa.reverse.remove(t);break;case 7:}},t.prototype.addEventListener=function(t,e){void 0!==this._listeners[t]?this._listeners[t].push(e):this._listeners[t]=[e]},t.prototype.dispatchEvent=function(t,e){if(void 0!==this._listeners[t])for(var r=0;r<this._listeners[t].length;r++)try{this._listeners[t][r](e)}catch(t){console.error(t.stack)}},t.prototype.onTimerEvent=function(t,e){for(var r=0;r<e.runline.length;r++){e.runline[r].time(t)}},t}(),__extends=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(t,e){function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}(),CoreComment=function(){function a(t,e){if(void 0===e&&(e={}),this.mode=1,this.stime=0,this.text="",this.ttl=4e3,this.dur=4e3,this.cindex=-1,this.motion=[],this.movable=!0,this._alphaMotion=null,this.absolute=!0,this.align=0,this.axis=0,this._alpha=1,this._size=25,this._color=16777215,this._border=!1,this._shadow=!0,this._font="",this._transform=null,this._className="",!t)throw new Error("Comment not bound to comment manager.");if(this.parent=t,e.hasOwnProperty("stime")&&(this.stime=e.stime),e.hasOwnProperty("mode")?this.mode=e.mode:this.mode=1,e.hasOwnProperty("dur")&&(this.dur=e.dur,this.ttl=this.dur),this.dur*=this.parent.options.global.scale,this.ttl*=this.parent.options.global.scale,e.hasOwnProperty("text")&&(this.text=e.text),e.hasOwnProperty("motion")){this._motionStart=[],this._motionEnd=[],this.motion=e.motion;for(var r=0,i=0;i<e.motion.length;i++){this._motionStart.push(r);var n=0;for(var o in e.motion[i]){var s=e.motion[i][o];n=Math.max(s.dur+s.delay,n),null!==s.easing&&void 0!==s.easing||(e.motion[i][o].easing=a.LINEAR)}r+=n,this._motionEnd.push(r)}this._curMotion=0}e.hasOwnProperty("color")&&(this._color=e.color),e.hasOwnProperty("size")&&(this._size=e.size),e.hasOwnProperty("border")&&(this._border=e.border),e.hasOwnProperty("opacity")&&(this._alpha=e.opacity),e.hasOwnProperty("alpha")&&(this._alphaMotion=e.alpha),e.hasOwnProperty("font")&&(this._font=e.font),e.hasOwnProperty("x")&&(this._x=e.x),e.hasOwnProperty("y")&&(this._y=e.y),e.hasOwnProperty("shadow")&&(this._shadow=e.shadow),e.hasOwnProperty("align")&&(this.align=e.align),e.hasOwnProperty("axis")&&(this.axis=e.axis),e.hasOwnProperty("transform")&&(this._transform=new CommentUtils.Matrix3D(e.transform)),e.hasOwnProperty("position")&&"relative"===e.position&&(this.absolute=!1,this.mode<7&&console.warn("Using relative position for CSA comment.")),e.hasOwnProperty("className")&&(this._className=e.className)}return a.prototype._toggleClass=function(t,e){if(void 0===e&&(e=!1),this.dom)if(this.dom.classList)this.dom.classList.toggle(t,e);else{var r=this.dom.className.split(" "),i=r.indexOf(t);0<=i&&!e?(r.splice(i,1),this.dom.className=r.join(" ")):i<0&&e&&(r.push(t),this.dom.className=r.join(" "))}},a.prototype.init=function(t){void 0===t&&(t=null),this.dom=null!==t?t.dom:document.createElement("div"),this.dom.className=this.parent.options.global.className,""!==this._className&&(this.dom.className+=" "+this._className),this.dom.appendChild(document.createTextNode(this.text)),this.dom.textContent=this.text,this.dom.innerText=this.text,this.size=this._size,16777215!=this._color&&(this.color=this._color),this.shadow=this._shadow,this._border&&(this.border=this._border),""!==this._font&&(this.font=this._font),void 0!==this._x&&(this.x=this._x),void 0!==this._y&&(this.y=this._y),(1!==this._alpha||this.parent.options.global.opacity<1)&&(this.alpha=this._alpha),null===this._transform||this._transform.isIdentity()||(this.transform=this._transform.flatArray),0<this.motion.length&&this.animate()},Object.defineProperty(a.prototype,"x",{get:function(){return null!==this._x&&void 0!==this._x||(this.axis%2==0?this.align%2==0?this._x=this.dom.offsetLeft:this._x=this.dom.offsetLeft+this.width:this.align%2==0?this._x=this.parent.width-this.dom.offsetLeft:this._x=this.parent.width-this.dom.offsetLeft-this.width),this.absolute?this._x:this._x/this.parent.width},set:function(t){this._x=t,this.absolute||(this._x*=this.parent.width),this.axis%2==0?this.dom.style.left=this._x+(this.align%2==0?0:-this.width)+"px":this.dom.style.right=this._x+(this.align%2==0?-this.width:0)+"px"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"y",{get:function(){return null!==this._y&&void 0!==this._y||(this.axis<2?this.align<2?this._y=this.dom.offsetTop:this._y=this.dom.offsetTop+this.height:this.align<2?this._y=this.parent.height-this.dom.offsetTop:this._y=this.parent.height-this.dom.offsetTop-this.height),this.absolute?this._y:this._y/this.parent.height},set:function(t){this._y=t,this.absolute||(this._y*=this.parent.height),this.axis<2?this.dom.style.top=this._y+(this.align<2?0:-this.height)+"px":this.dom.style.bottom=this._y+(this.align<2?-this.height:0)+"px"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"bottom",{get:function(){var t=Math.floor(this.axis/2)===Math.floor(this.align/2);return this.y+(t?this.height:0)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"right",{get:function(){var t=this.axis%2==this.align%2;return this.x+(t?this.width:0)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"width",{get:function(){return null!==this._width&&void 0!==this._width||(this._width=this.dom.offsetWidth),this._width},set:function(t){this._width=t,this.dom.style.width=this._width+"px"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"height",{get:function(){return null!==this._height&&void 0!==this._height||(this._height=this.dom.offsetHeight),this._height},set:function(t){this._height=t,this.dom.style.height=this._height+"px"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"size",{get:function(){return this._size},set:function(t){this._size=t,this.dom.style.fontSize=this._size+"px"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"color",{get:function(){return this._color},set:function(t){var e=(this._color=t).toString(16);e=6<=e.length?e:new Array(6-e.length+1).join("0")+e,this.dom.style.color="#"+e,0===this._color&&this._toggleClass("reverse-shadow",!0)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"alpha",{get:function(){return this._alpha},set:function(t){this._alpha=t,this.dom.style.opacity=Math.min(this._alpha,this.parent.options.global.opacity)+""},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"border",{get:function(){return this._border},set:function(t){this._border=t,this._border?this.dom.style.border="1px solid #00ffff":this.dom.style.border="none"},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"shadow",{get:function(){return this._shadow},set:function(t){this._shadow=t,this._shadow||this._toggleClass("no-shadow",!0)},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"font",{get:function(){return this._font},set:function(t){this._font=t,0<this._font.length?this.dom.style.fontFamily=this._font:this.dom.style.fontFamily=""},enumerable:!0,configurable:!0}),Object.defineProperty(a.prototype,"transform",{get:function(){return this._transform.flatArray},set:function(t){this._transform=new CommentUtils.Matrix3D(t),null!==this.dom&&(this.dom.style.transform=this._transform.toCss())},enumerable:!0,configurable:!0}),a.prototype.time=function(t){this.ttl-=t,this.ttl<0&&(this.ttl=0),this.movable&&this.update(),this.ttl<=0&&this.finish()},a.prototype.update=function(){this.animate()},a.prototype.invalidate=function(){this._x=null,this._y=null,this._width=null,this._height=null},a.prototype._execMotion=function(t,e){for(var r in t)if(t.hasOwnProperty(r)){var i=t[r];this[r]=i.easing(Math.min(Math.max(e-i.delay,0),i.dur),i.from,i.to-i.from,i.dur)}},a.prototype.animate=function(){if(this._alphaMotion&&(this.alpha=(this.dur-this.ttl)*(this._alphaMotion.to-this._alphaMotion.from)/this.dur+this._alphaMotion.from),0!==this.motion.length){var t=Math.max(this.ttl,0),e=this.dur-t-this._motionStart[this._curMotion];return this._execMotion(this.motion[this._curMotion],e),this.dur-t>this._motionEnd[this._curMotion]?(this._curMotion++,void(this._curMotion>=this.motion.length&&(this._curMotion=this.motion.length-1))):void 0}},a.prototype.stop=function(){},a.prototype.finish=function(){this.parent.finish(this)},a.prototype.toString=function(){return["[",this.stime,"|",this.ttl,"/",this.dur,"]","(",this.mode,")",this.text].join("")},a.LINEAR=function(t,e,r,i){return t*r/i+e},a}(),ScrollComment=function(i){function t(t,e){var r=i.call(this,t,e)||this;return r.dur*=r.parent.options.scroll.scale,r.ttl*=r.parent.options.scroll.scale,r}return __extends(t,i),Object.defineProperty(t.prototype,"alpha",{set:function(t){this._alpha=t,this.dom.style.opacity=Math.min(Math.min(this._alpha,this.parent.options.global.opacity),this.parent.options.scroll.opacity)+""},enumerable:!0,configurable:!0}),t.prototype.init=function(t){void 0===t&&(t=null),i.prototype.init.call(this,t),this.x=this.parent.width,this.parent.options.scroll.opacity<1&&(this.alpha=this._alpha),this.absolute=!0},t.prototype.update=function(){this.x=this.ttl/this.dur*(this.parent.width+this.width)-this.width},t}(CoreComment),CommentFactory=function(){function e(){this._bindings={}}return e._simpleCssScrollingInitializer=function(t,e){var r=new CssScrollComment(t,e);switch(r.mode){case 1:r.align=0,r.axis=0;break;case 2:r.align=2,r.axis=2;break;case 6:r.align=1,r.axis=1}return r.init(),t.stage.appendChild(r.dom),r},e._simpleScrollingInitializer=function(t,e){var r=new ScrollComment(t,e);switch(r.mode){case 1:r.align=0,r.axis=0;break;case 2:r.align=2,r.axis=2;break;case 6:r.align=1,r.axis=1}return r.init(),t.stage.appendChild(r.dom),r},e._simpleAnchoredInitializer=function(t,e){var r=new CoreComment(t,e);switch(r.mode){case 4:r.align=2,r.axis=2;break;case 5:r.align=0,r.axis=0}return r.init(),t.stage.appendChild(r.dom),r},e._advancedCoreInitializer=function(t,e){var r=new CoreComment(t,e);return r.init(),r.transform=CommentUtils.Matrix3D.createRotationMatrix(0,e.rY,e.rZ).flatArray,t.stage.appendChild(r.dom),r},e.defaultFactory=function(){var t=new e;return t.bind(1,e._simpleScrollingInitializer),t.bind(2,e._simpleScrollingInitializer),t.bind(6,e._simpleScrollingInitializer),t.bind(4,e._simpleAnchoredInitializer),t.bind(5,e._simpleAnchoredInitializer),t.bind(7,e._advancedCoreInitializer),t.bind(17,e._advancedCoreInitializer),t},e.defaultCssRenderFactory=function(){var t=new e;return t.bind(1,e._simpleCssScrollingInitializer),t.bind(2,e._simpleCssScrollingInitializer),t.bind(6,e._simpleCssScrollingInitializer),t.bind(4,e._simpleAnchoredInitializer),t.bind(5,e._simpleAnchoredInitializer),t.bind(7,e._advancedCoreInitializer),t.bind(17,e._advancedCoreInitializer),t},e.defaultCanvasRenderFactory=function(){throw new Error("Not implemented")},e.defaultSvgRenderFactory=function(){throw new Error("Not implemented")},e.prototype.bind=function(t,e){this._bindings[t]=e},e.prototype.canCreate=function(t){return this._bindings.hasOwnProperty(t.mode)},e.prototype.create=function(t,e){if(null===e||!e.hasOwnProperty("mode"))throw new Error("Comment format incorrect");if(!this._bindings.hasOwnProperty(e.mode))throw new Error("No binding for comment type "+e.mode);return this._bindings[e.mode](t,e)},e}(),CommentSpaceAllocator=(__extends=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(t,e){function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}(),function(){function t(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this._pools=[[]],this.avoid=1,this._width=t,this._height=e}return t.prototype.willCollide=function(t,e){return t.stime+t.ttl>=e.stime+e.ttl/2},t.prototype.pathCheck=function(t,e,r){for(var i=t+e.height,n=e.right,o=0;o<r.length;o++)if(!(r[o].y>i||r[o].bottom<t)){if(!(r[o].right<e.x||r[o].x>n))return!1;if(this.willCollide(r[o],e))return!1}return!0},t.prototype.assign=function(t,e){for(;this._pools.length<=e;)this._pools.push([]);var r=this._pools[e];if(0===r.length)return t.cindex=e,0;if(this.pathCheck(0,t,r))return t.cindex=e,0;for(var i=0,n=0;n<r.length&&!((i=r[n].bottom+this.avoid)+t.height>this._height);n++)if(this.pathCheck(i,t,r))return t.cindex=e,i;return this.assign(t,e+1)},t.prototype.add=function(t){t.height>this._height?(t.cindex=-2,t.y=0):(t.y=this.assign(t,0),BinArray.binsert(this._pools[t.cindex],t,function(t,e){return t.bottom<e.bottom?-1:t.bottom>e.bottom?1:0}))},t.prototype.remove=function(t){if(!(t.cindex<0)){if(t.cindex>=this._pools.length)throw new Error("cindex out of bounds");var e=this._pools[t.cindex].indexOf(t);e<0||this._pools[t.cindex].splice(e,1)}},t.prototype.setBounds=function(t,e){this._width=t,this._height=e},t}()),AnchorCommentSpaceAllocator=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.prototype.add=function(t){e.prototype.add.call(this,t),t.x=(this._width-t.width)/2},t.prototype.willCollide=function(t,e){return!0},t.prototype.pathCheck=function(t,e,r){for(var i=t+e.height,n=0;n<r.length;n++)if(!(r[n].y>i||r[n].bottom<t))return!1;return!0},t}(CommentSpaceAllocator);!function(t){var e=function(){function h(t){if(this._internalArray=null,!Array.isArray(t))throw new Error("Not an array. Cannot construct matrix.");if(16!=t.length)throw new Error("Illegal Dimensions. Matrix3D should be 4x4 matrix.");this._internalArray=t}return Object.defineProperty(h.prototype,"flatArray",{get:function(){return this._internalArray.slice(0)},set:function(t){throw new Error("Not permitted. Matrices are immutable.")},enumerable:!0,configurable:!0}),h.prototype.isIdentity=function(){return this.equals(h.identity())},h.prototype.dot=function(t){for(var e=this._internalArray.slice(0),r=t._internalArray.slice(0),i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],n=0;n<4;n++)for(var o=0;o<4;o++)for(var s=0;s<4;s++)i[4*n+o]+=e[4*n+s]*r[4*s+o];return new h(i)},h.prototype.equals=function(t){for(var e=0;e<16;e++)if(this._internalArray[e]!==t._internalArray[e])return!1;return!0},h.prototype.toCss=function(){for(var t=this._internalArray.slice(0),e=0;e<t.length;e++)Math.abs(t[e])<1e-6&&(t[e]=0);return"matrix3d("+t.join(",")+")"},h.identity=function(){return new h([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])},h.createScaleMatrix=function(t,e,r){return new h([t,0,0,0,0,e,0,0,0,0,r,0,0,0,0,1])},h.createRotationMatrix=function(t,e,r){var i=Math.PI/180,n=e*i,o=r*i,s=Math.cos,a=Math.sin;return new h([s(n)*s(o),s(n)*a(o),a(n),0,-a(o),s(o),0,0,-a(n)*s(o),-a(n)*a(o),s(n),0,0,0,0,1].map(function(t){return 1e-10*Math.round(1e10*t)}))},h}();t.Matrix3D=e}(CommentUtils||(CommentUtils={}));__extends=this&&this.__extends||function(){var i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])};return function(t,e){function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}();var CssCompatLayer=function(){function t(){}return t.transform=function(t,e){t.style.transform=e,t.style.webkitTransform=e,t.style.msTransform=e,t.style.oTransform=e},t}(),CssScrollComment=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._dirtyCSS=!0,t}return __extends(t,e),t.prototype.init=function(t){void 0===t&&(t=null),e.prototype.init.call(this,t),this._toggleClass("css-optimize",!0)},Object.defineProperty(t.prototype,"x",{get:function(){return this.ttl/this.dur*(this.parent.width+this.width)-this.width},set:function(t){if(null!==this._x&&"number"==typeof this._x){var e=t-this._x;this._x=t,CssCompatLayer.transform(this.dom,"translateX("+(this.axis%2==0?e:-e)+"px)")}else this._x=t,this.absolute||(this._x*=this.parent.width),this.axis%2==0?this.dom.style.left=this._x+(this.align%2==0?0:-this.width)+"px":this.dom.style.right=this._x+(this.align%2==0?-this.width:0)+"px"},enumerable:!0,configurable:!0}),t.prototype.update=function(){this._dirtyCSS&&(this.dom.style.transition="transform "+this.ttl+"ms linear",this.x=-this.width,this._dirtyCSS=!1)},t.prototype.invalidate=function(){e.prototype.invalidate.call(this),this._dirtyCSS=!0},t.prototype.stop=function(){e.prototype.stop.call(this),this.dom.style.transition="",this.x=this._x,this._x=null,this.x=this.x,this._dirtyCSS=!0},t}(ScrollComment),CommentFilter=function(){function t(){this.rules=[],this.modifiers=[],this.allowUnknownTypes=!0,this.allowTypes={1:!0,2:!0,4:!0,5:!0,6:!0,7:!0,8:!0,17:!0}}return t.prototype.doModify=function(t){return this.modifiers.reduce(function(t,e){return e(t)},t)},t.prototype.beforeSend=function(t){return t},t.prototype.doValidate=function(r){return!!r.hasOwnProperty("mode")&&(!((!this.allowUnknownTypes||r.mode.toString()in this.allowTypes)&&!this.allowTypes[r.mode.toString()])&&this.rules.every(function(t){try{var e=function e(t,r){for(var i=t.subject.split("."),n=r;0<i.length;){var o=i.shift();if(""!==o&&(n.hasOwnProperty(o)&&(n=n[o]),null==n)){n=null;break}}if(null===n)return!0;switch(t.op){case"<":return n<t.value;case">":return n>t.value;case"~":case"regexp":return new RegExp(t.value).test(n.toString());case"=":case"eq":return t.value===("number"==typeof n?n:n.toString());case"!":case"not":return!e(t.value,n);case"&&":case"and":return!!Array.isArray(t.value)&&t.value.every(function(t){return e(t,n)});case"||":case"or":return!!Array.isArray(t.value)&&t.value.some(function(t){return e(t,n)});default:return!1}}(t,r)}catch(t){e=!1}return"accept"===t.mode?e:!e}))},t.prototype.addRule=function(t){if("accept"!==t.mode&&"reject"!==t.mode)throw new Error("Rule must be of accept type or reject type.");this.rules.push(t)},t.prototype.removeRule=function(t){var e=this.rules.indexOf(t);return 0<=e&&(this.rules.splice(e,1),!0)},t.prototype.addModifier=function(t){if("function"!=typeof t)throw new Error("Modifiers need to be functions.");this.modifiers.push(t)},t}(),CommentProvider=function(){function n(){this._started=!1,this._destroyed=!1,this._staticSources={},this._dynamicSources={},this._parsers={},this._targets=[]}return n.SOURCE_JSON="JSON",n.SOURCE_XML="XML",n.SOURCE_TEXT="TEXT",n.BaseHttpProvider=function(s,a,h,l,c){return new Promise(function(t,e){var r=new XMLHttpRequest,i=a;if(l&&("POST"===s||"PUT"===s)){i+="?";var n=[];for(var o in l)l.hasOwnProperty(o)&&n.push(encodeURIComponent(o)+"="+encodeURIComponent(l[o]));i+=n.join("&")}r.onload=function(){200<=this.status&&this.status<300?t(this.response):e(new Error(this.status+" "+this.statusText))},r.onerror=function(){e(new Error(this.status+" "+this.statusText))},r.open(s,i),r.responseType="string"==typeof h?h:"",void 0!==c?r.send(c):r.send()})},n.JSONProvider=function(t,e,r,i){return n.BaseHttpProvider(t,e,"json",r,i).then(function(t){return t})},n.XMLProvider=function(t,e,r,i){return n.BaseHttpProvider(t,e,"document",r,i).then(function(t){return t})},n.TextProvider=function(t,e,r,i){return n.BaseHttpProvider(t,e,"text",r,i).then(function(t){return t})},n.prototype.addStaticSource=function(t,e){if(this._destroyed)throw new Error("Comment provider has been destroyed, cannot attach more sources.");return e in this._staticSources||(this._staticSources[e]=[]),this._staticSources[e].push(t),this},n.prototype.addDynamicSource=function(t,e){if(this._destroyed)throw new Error("Comment provider has been destroyed, cannot attach more sources.");return e in this._dynamicSources||(this._dynamicSources[e]=[]),this._dynamicSources[e].push(t),this},n.prototype.addTarget=function(t){if(this._destroyed)throw new Error("Comment provider has been destroyed, cannot attach more targets.");if(!(t instanceof CommentManager))throw new Error("Expected the target to be an instance of CommentManager.");return this._targets.push(t),this},n.prototype.addParser=function(t,e){if(this._destroyed)throw new Error("Comment provider has been destroyed, cannot attach more parsers.");return e in this._parsers||(this._parsers[e]=[]),this._parsers[e].unshift(t),this},n.prototype.applyParsersOne=function(n,o){return new Promise(function(t,e){if(o in this._parsers){for(var r=0;r<this._parsers[o].length;r++){var i=null;try{i=this._parsers[o][r].parseOne(n)}catch(t){console.error(t)}if(null!==i)return void t(i)}e(new Error("Ran out of parsers for they target type"))}else e(new Error('No parsers defined for "'+o+'"'))}.bind(this))},n.prototype.applyParsersList=function(n,o){return new Promise(function(t,e){if(o in this._parsers){for(var r=0;r<this._parsers[o].length;r++){var i=null;try{i=this._parsers[o][r].parseMany(n)}catch(t){console.error(t)}if(null!==i)return void t(i)}e(new Error("Ran out of parsers for the target type"))}else e(new Error('No parsers defined for "'+o+'"'))}.bind(this))},n.prototype.load=function(){if(this._destroyed)throw new Error("Cannot load sources on a destroyed provider.");var t=[];for(var e in this._staticSources)t.push(Promises.any(this._staticSources[e]).then(function(t){return this.applyParsersList(t,e)}.bind(this)));return 0===t.length?Promise.resolve([]):Promises.any(t).then(function(t){for(var e=0;e<this._targets.length;e++)this._targets[e].load(t);return Promise.resolve(t)}.bind(this))},n.prototype.start=function(){if(this._destroyed)throw new Error("Cannot start a provider that has been destroyed.");return this._started=!0,this.load().then(function(t){for(var r in this._dynamicSources)this._dynamicSources[r].forEach(function(t){t.addEventListener("receive",function(t){for(var e=0;e<this._targets.length;e++)this._targets[e].send(this.applyParserOne(t,r))}.bind(this))}.bind(this));return Promise.resolve(t)}.bind(this))},n.prototype.send=function(t,e){throw new Error("Not implemented")},n.prototype.destroy=function(){return this._destroyed||(this._destroyed=!0),Promise.resolve()},n}(),Promises=function(){var t={any:function(s){return Array.isArray(s)?0===s.length?Promise.reject():new Promise(function(e,r){for(var i=!1,n=0,o=[],t=0;t<s.length;t++)s[t].then(function(t){n++,i||(i=!0,e(t))}).catch(function(e){return function(t){n++,o[e]=t,n===s.length&&(i||r(o))}}(t))}):Promise.resolve(s)}};return t}(),BilibiliFormat=function(){var e={},y=function(t){if("["!==t.charAt(0))return t;switch(t.charAt(t.length-1)){case"]":return t;case'"':return t+"]";case",":return t.substring(0,t.length-1)+'"]';default:return y(t.substring(0,t.length-1))}},o=function(t){return t=(t=(t=(t=t.replace(new RegExp("</([^d])","g"),"</disabled $1")).replace(new RegExp("</(S{2,})","g"),"</disabled $1")).replace(new RegExp("<([^d/]W*?)","g"),"<disabled $1")).replace(new RegExp("<([^/ ]{2,}W*?)","g"),"<disabled $1")};return e.XMLParser=function(t){this._attemptFix=!0,this._logBadComments=!0,"object"==typeof t&&(this._attemptFix=!1!==t.attemptFix,this._logBadComments=!1!==t.logBadComments)},e.XMLParser.prototype.parseOne=function(t){try{var e=t.getAttribute("p").split(",")}catch(t){return null}var r=t.textContent,i={};if(i.stime=Math.round(1e3*parseFloat(e[0])),i.size=parseInt(e[2]),i.color=parseInt(e[3]),i.mode=parseInt(e[1]),i.date=parseInt(e[4]),i.pool=parseInt(e[5]),i.position="absolute",null!=e[7]&&(i.dbid=parseInt(e[7])),i.hash=e[6],i.border=!1,i.mode<7)i.text=r.replace(/(\/n|\\n|\n|\r\n)/g,"\n");else if(7===i.mode)try{this._attemptFix&&(r=y(r).replace(/\t/,"\\t"));var n=JSON.parse(r);if(i.shadow=!0,i.x=parseFloat(n[0]),i.y=parseFloat(n[1]),(Math.floor(i.x)<i.x||Math.floor(i.y)<i.y)&&(i.position="relative"),i.text=n[4].replace(/(\/n|\\n|\n|\r\n)/g,"\n"),i.rZ=0,i.rY=0,7<=n.length&&(i.rZ=parseInt(n[5],10),i.rY=parseInt(n[6],10)),i.motion=[],i.movable=!1,11<=n.length){i.movable=!0;var o=500,s={x:{from:i.x,to:parseFloat(n[7]),dur:o,delay:0},y:{from:i.y,to:parseFloat(n[8]),dur:o,delay:0}};if(""!==n[9]&&(o=parseInt(n[9],10),s.x.dur=o,s.y.dur=o),""!==n[10]&&(s.x.delay=parseInt(n[10],10),s.y.delay=parseInt(n[10],10)),11<n.length&&(i.shadow="false"!==n[11]&&!1!==n[11],null!=n[12]&&(i.font=n[12]),14<n.length)){"relative"===i.position&&(this._logBadComments&&console.warn("Cannot mix relative and absolute positioning!"),i.position="absolute");for(var a=n[14],h={x:s.x.from,y:s.y.from},l=[],c=new RegExp("([a-zA-Z])\\s*(\\d+)[, ](\\d+)","g"),p=a.split(/[a-zA-Z]/).length-1,u=c.exec(a);null!==u;){switch(u[1]){case"M":h.x=parseInt(u[2],10),h.y=parseInt(u[3],10);break;case"L":l.push({x:{from:h.x,to:parseInt(u[2],10),dur:o/p,delay:0},y:{from:h.y,to:parseInt(u[3],10),dur:o/p,delay:0}}),h.x=parseInt(u[2],10),h.y=parseInt(u[3],10)}u=c.exec(a)}s=null,i.motion=l}null!==s&&i.motion.push(s)}i.dur=2500,n[3]<12&&(i.dur=1e3*n[3]);var d=n[2].split("-");if(null!=d&&1<d.length){var f=parseFloat(d[0]),m=parseFloat(d[1]);(i.opacity=f)!==m&&(i.alpha={from:f,to:m})}}catch(t){this._logBadComments&&(console.warn("Error occurred in JSON parsing. Could not parse comment."),console.log("[DEBUG] "+r))}else 8===i.mode?i.code=r:this._logBadComments&&(console.warn("Unknown comment type : "+i.mode+". Not doing further parsing."),console.log("[DEBUG] "+r));return null!==i.text&&"string"==typeof i.text&&(i.text=i.text.replace(/\u25a0/g,"█")),i},e.XMLParser.prototype.parseMany=function(t){var e=[];try{e=t.getElementsByTagName("d")}catch(t){return null}for(var r=[],i=0;i<e.length;i++){var n=this.parseOne(e[i]);null!==n&&r.push(n)}return r},e.TextParser=function(t){this._allowInsecureDomParsing=!0,this._attemptEscaping=!0,this._canSecureNativeParse=!1,"object"==typeof t&&(this._allowInsecureDomParsing=!1!==t.allowInsecureDomParsing,this._attemptEscaping=!1!==t.attemptEscaping),"undefined"!=typeof document&&document&&document.createElement||(this._allowInsecureDomParsing=!1),"undefined"!=typeof DOMParser&&null!==DOMParser&&(this._canSecureNativeParse=!0),(this._allowInsecureDomParsing||this._canSecureNativeParse)&&(this._xmlParser=new e.XMLParser(t))},e.TextParser.prototype.parseOne=function(t){if(this._allowInsecureDomParsing){var e=t;this._attemptEscaping&&(e=o(e));var r=document.createElement("div");r.innerHTML=e;var i=r.getElementsByTagName("d");return 1!==i.length?null:this._xmlParser.parseOne(i[0])}if(this._canSecureNativeParse){var n=new DOMParser;return this._xmlParser.parseOne(n.parseFromString(t,"application/xml"))}throw new Error("Secure native js parsing not implemented yet.")},e.TextParser.prototype.parseMany=function(t){if(this._allowInsecureDomParsing){var e=t;this._attemptEscaping&&(e=o(e));var r=document.createElement("div");return r.innerHTML=e,this._xmlParser.parseMany(r)}if(this._canSecureNativeParse){var i=new DOMParser;return this._xmlParser.parseMany(i.parseFromString(t,"application/xml"))}throw new Error("Secure native js parsing not implemented yet.")},e}(),AcfunFormat=function(){var e={JSONParser:function(t){this._logBadComments=!0,this._logNotImplemented=!1,"object"==typeof t&&(this._logBadComments=!1!==t.logBadComments,this._logNotImplemented=!0===t.logNotImplemented)}};return e.JSONParser.prototype.parseOne=function(t){var e={};if("object"!=typeof t||null==t||!t.hasOwnProperty("c"))return null;var r=t.c.split(",");if(6<=r.length){if(e.stime=1e3*parseFloat(r[0]),e.color=parseInt(r[1]),e.mode=parseInt(r[2]),e.size=parseInt(r[3]),e.hash=r[4],e.date=parseInt(r[5]),e.position="absolute",7!==e.mode)e.text=t.m.replace(/(\/n|\\n|\n|\r\n|\\r)/g,"\n"),e.text=e.text.replace(/\r/g,"\n"),e.text=e.text.replace(/\s/g," ");else{try{var i=JSON.parse(t.m)}catch(t){return this._logBadComments&&(console.warn("Error parsing internal data for comment"),console.log("[Dbg] "+e.text)),null}if(e.position="relative",e.text=i.n,e.text=e.text.replace(/\ /g," "),"number"==typeof i.a?e.opacity=i.a:e.opacity=1,"object"==typeof i.p?(e.x=i.p.x/1e3,e.y=i.p.y/1e3):(e.x=0,e.y=0),"number"==typeof i.c)switch(i.c){case 0:e.align=0;break;case 2:e.align=1;break;case 6:e.align=2;break;case 8:e.align=3;break;default:this._logNotImplemented&&console.log("Cannot handle aligning to center! AlignMode="+i.c)}if(e.axis=0,e.shadow=i.b,e.dur=4e3,"number"==typeof i.l&&(e.dur=1e3*i.l),null!=i.z&&0<i.z.length){e.movable=!0,e.motion=[];for(var n=0,o={x:e.x,y:e.y,alpha:e.opacity,color:e.color},s=0;s<i.z.length;s++){var a=null!=i.z[s].l?1e3*i.z[s].l:500;n+=a;var h={};i.z[s].hasOwnProperty("rx")&&"number"==typeof i.z[s].rx&&this._logNotImplemented&&console.log("Encountered animated x-axis rotation. Ignoring."),i.z[s].hasOwnProperty("e")&&"number"==typeof i.z[s].e&&this._logNotImplemented&&console.log("Encountered animated y-axis rotation. Ignoring."),i.z[s].hasOwnProperty("d")&&"number"==typeof i.z[s].d&&this._logNotImplemented&&console.log("Encountered animated z-axis rotation. Ignoring."),i.z[s].hasOwnProperty("x")&&"number"==typeof i.z[s].x&&(h.x={from:o.x,to:i.z[s].x/1e3,dur:a,delay:0}),i.z[s].hasOwnProperty("y")&&"number"==typeof i.z[s].y&&(h.y={from:o.y,to:i.z[s].y/1e3,dur:a,delay:0}),o.x=h.hasOwnProperty("x")?h.x.to:o.x,o.y=h.hasOwnProperty("y")?h.y.to:o.y,i.z[s].hasOwnProperty("t")&&"number"==typeof i.z[s].t&&i.z[s].t!==o.alpha&&(h.alpha={from:o.alpha,to:i.z[s].t,dur:a,delay:0},o.alpha=h.alpha.to),i.z[s].hasOwnProperty("c")&&"number"==typeof i.z[s].c&&i.z[s].c!==o.color&&(h.color={from:o.color,to:i.z[s].c,dur:a,delay:0},o.color=h.color.to),e.motion.push(h)}e.dur=n+(e.moveDelay?e.moveDelay:0)}i.hasOwnProperty("w")&&(i.w.hasOwnProperty("f")&&(e.font=i.w.f),i.w.hasOwnProperty("l")&&Array.isArray(i.w.l)&&0<i.w.l.length&&this._logNotImplemented&&console.log("[Dbg] Filters not supported! "+JSON.stringify(i.w.l))),null!=i.r&&null!=i.k&&(e.rX=i.r,e.rY=i.k)}return e}return this._logBadComments&&(console.warn("Dropping this comment due to insufficient parameters. Got: "+r.length),console.log("[Dbg] "+t.c)),null},e.JSONParser.prototype.parseMany=function(t){if(!Array.isArray(t))return null;for(var e=[],r=0;r<t.length;r++){var i=this.parseOne(t[r]);null!==i&&e.push(i)}return e},e.TextParser=function(t){this._jsonParser=new e.JSONParser(t)},e.TextParser.prototype.parseOne=function(t){try{return this._jsonParser.parseOne(JSON.parse(t))}catch(t){return console.warn(t),null}},e.TextParser.prototype.parseMany=function(t){try{return this._jsonParser.parseMany(JSON.parse(t))}catch(t){return console.warn(t),null}},e}(),CommonDanmakuFormat=function(){var t={},e=function(t){return"number"==typeof t.mode&&"number"==typeof t.stime&&((8!==t.mode||"string"==typeof t.code)&&"string"==typeof t.text)};return t.JSONParser=function(){},t.JSONParser.prototype.parseOne=function(t){return e(t)?t:null},t.JSONParser.prototype.parseMany=function(t){return t.every(e)?t:null},t.XMLParser=function(){},t.XMLParser.prototype.parseOne=function(t){var e={};try{e.stime=parseInt(t.getAttribute("stime")),e.mode=parseInt(t.getAttribute("mode")),e.size=parseInt(t.getAttribute("size")),e.color=parseInt(t.getAttribute("color")),e.text=t.textContent}catch(t){return null}return e},t.XMLParser.prototype.parseMany=function(t){try{var e=t.getElementsByTagName("comment")}catch(t){return null}for(var r=[],i=0;i<e.length;i++){var n=this.parseOne(e[i]);null!==n&&r.push(n)}return r},t}();
