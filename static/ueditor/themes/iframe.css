body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: 14px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

a {
  color: #09f;
  text-decoration: none;
}

a:hover,
a:focus {
  color: #09f;
  text-decoration: none;
}

blockquote {
  padding: 0 0 0 15px;
  margin: 0 0 18px;
  border-left: 5px solid #EEE;
}

img + br {
  display: block;
  padding: 4px 0;
  content: ' ';
}

body p {
  line-height: 1.8;
  font-size: 16px;
  margin: 0;
}

iframe {
  border: none;
}

img {
  max-width: 100%;
}

img[data-word-image] {
  cursor: pointer;
}

pre {
  margin: .5em 0;
  padding: .4em .6em;
  border-radius: 8px;
  background: #f8f8f8;
  line-height: 1.5;
}

/*交互操作*/
img {
  cursor: auto;
}

ul,ol,li{
  list-style: none;
  padding: 0!important;
  margin: 0 0 1em!important;
}
ol {
  counter-reset: item;
}
ol li:before {
  display: inline-block;
  content: counter(item) ".";
  counter-increment: item;
  font-size: inherit;
  min-width: 20px;
  text-align: center;
}
ul li:before {
  display: inline-block;
  content: '•';
  width: 20px;
  font-size: inherit;
  text-align: center;
  text-indent: 0 !important;
}
li p{
  display: inline;
}
.edui-quick-operate-active {
  background: #E6ECFF;
}

/* 引入样式 */
.editCiting {
  margin: 10px 0;
  padding: 14px;
  display: block;
  color: #222;
  cursor: pointer;
  background: #f6f6f6;
  border-radius: 4px;
}
.editCiting:hover{
  color: #222;
}
.editCiting .questionsAnswers{
  display: flex;
  flex-direction: column;
  border: 1px solid #F6F6F6;
  background: #F6F6F6;
  border-radius: 4px;
}
.editCiting .questionsAnswers span{
  font-size: 16px;
  font-weight: bold;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 24px;
  margin-bottom: 9px;
}
.editCiting .questionsAnswers span img{
  height: 20px;
  vertical-align: middle;
  margin-top: -4px;
  margin-right: 8px;
}
.editCiting .questionsAnswers .description{
  font-size: 14px;
  margin-bottom: 10px;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.editCiting .questionsAnswers .date{
  font-size: 14px;
  color: #999;
  font-style: normal;
}
.editCiting .info{
  display: flex;
}
.editCiting .info .cover{
  width: 156px;
  height: 88px;
  flex: 0 0 auto;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.editCiting .info .cover img{
  width: 100%;
  height: 100%;
}
.editCiting .info .cover .mask{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.30);
}
.editCiting .info .cover .mask .triangle{
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: url("//www.brainmed.com/app/shortplayer/img/icon_triangle.png")no-repeat center;
  background-size: 34%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #fff;
  color: #fff;
  border-radius: 50%;
  font-size: 28px;
}
.editCiting .info .right{
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.editCiting .info .right span{
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  font-weight: bold;
  line-height: 24px;
}
.editCiting .info .right span i{
  background: #EC4E1C;
  color: #fff;
  font-size: 15px;
  font-style: normal;
  padding: 0 2px;
  border-radius: 4px;
  margin-right: 5px;
}
.editCiting .info .right b{
  font-weight: normal;
  color: #999999;
  font-size: 14px;
}
.editCiting .info .right b i{
  font-style: normal;
}
* {
  clear: none !important;
}
* {
  clear: none !important;
}
/*新音频控件*/
/* .audio_box {*/
/*    padding: 0 0.6rem;*/
/*    width: 100%;*/
/*    height: 3.6rem;*/
/*    background: #f6f6f6;*/
/*    border-radius: 0.2rem;*/
/*    box-sizing: border-box;*/
/*    display: flex;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*  }*/
/* .audio_btn {*/
/*    margin-left: 0.2rem;*/
/*    margin-right: 0.6rem;*/
/*    flex-shrink: 0;*/
/*    width: 1.5rem;*/
/*    height: 1.5rem;*/
/*    border-radius: 50%;*/
/*    background-image: url('~assets/images/edit_qa/play.png');*/
/*    background-repeat: no-repeat;*/
/*    background-position: center;*/
/*    background-size: cover;*/
/*  }*/
/* .audio_progress_bar {*/
/*    flex: 1;*/
/*    !*overflow: hidden;*!*/
/*  }*/
/* .audio_bar_top {*/
/*    position: relative;*/
/*    margin-top: 0.4rem;*/
/*    width: 100%;*/
/*    height: 0.2rem;*/
/*    background: #d9d9d9;*/
/*    border-radius: 5rem;*/
/*  }*/
/* .line_blue {*/
/*    position: absolute;*/
/*    top: 0;*/
/*    left: 0;*/
/*    height: 0.2rem;*/
/*    background: #0581ce;*/
/*    width: 0;*/
/*    border-radius: 5rem;*/
/*  }*/
/* .line_circular {*/
/*    position: absolute;*/
/*    top: 50%;*/
/*    left: 0;*/
/*    width: 0.8rem;*/
/*    height: 0.8rem;*/
/*    transform: translate(-50%, -50%);*/
/*    background: rgba(5, 129, 206, 0.2);*/
/*    border-radius: 50%;*/
/*  }*/
/* .line_circular div {*/
/*    position: absolute;*/
/*    top: 50%;*/
/*    left: 50%;*/
/*    transform: translate(-50%, -50%);*/
/*    width: 0.4rem;*/
/*    height: 0.4rem;*/
/*    background: #0581ce;*/
/*    border-radius: 50%;*/
/*  }*/
/* .audio_bar_btm {*/
/*    margin-top: 0.2rem;*/
/*    width: 100%;*/
/*    display: flex;*/
/*    justify-content: space-between;*/
/*    align-items: center;*/
/*    font-family: 'PingFang SC';*/
/*    font-size: 0.6rem;*/
/*    line-height: 0.6rem;*/
/*    color: #999999;*/
/*  }*/
/*!*新版编辑器标题*!*/
h2 {
  margin: 1.2rem 0 0.8rem;
  padding: 0;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.1rem;
  color: #333333;
}
h2.htwo {
  position: relative;
  padding-left: 0.6rem;
}
h2.htwo::before {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  content: '';
  width: 0.2rem;
  height: 1.1rem;
  background: #0581ce;
}
h2.hthree {
  position: relative;
  padding-bottom: 0.6rem;
  text-align: center;
}
h2.hthree::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  content: '';
  width: 1.1rem;
  height: 0.2rem;
  background: #0581ce;
}
h2.hone {
  position: relative;
  padding-left: 0.9rem;
}
h2.hone::before {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  content: '';
  width: 0.5rem;
  height: 0.6rem;
  background: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/triger.png') no-repeat center /
      cover;
}
h2.hfour {
  position: relative;
  padding: 0.3rem 0.2rem;
  background-color: #0581ce;
  color: #fff;
  /* display: inline-block; */
  display: inline-flex;
  display: table;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  left: 50%;
  transform: translateX(-50%);
  /* line-height: 30px; */
}
h2.hfour::before {
  position: absolute;
  left: 0.2rem;
  top: 0.2rem;
  content: '';
  width: 100%;
  height: 100%;
  border: 0.05rem solid #0581ce;
  box-sizing: border-box;
}

h2.hfive {
  position: relative;
  padding: 0.3rem 0.55rem;
  background-color: #0581ce;
  color: #fff;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 0.2rem;
  display: inline-flex;
  display: table;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}
/*新病例*/
/*图片描述*/
p.tp_desc {
  margin-top: 0.6rem;
  margin-bottom: 0.6rem;
  text-align: center;
  font-size: 0.6rem;
  line-height: 1.2em;
  color: #999;
}
img {
  display: block;
  width: 100%;
  max-width: 100%!important;
}
/*兼容旧版段落标题*/
.old_text_box {
  position: relative;
  margin: 0;
}
img:not(.imageDiv img) {
  margin-bottom: 20px;
}
.imageDiv {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  font-size: 0;
}
.noMarginBottom{
  margin-bottom: 0;
}
.close{
  display: none !important;
}
/*隐藏新编辑器操作按钮*/
.progress_bar,
.video_play_btn
  /*.textarea*/
{
  display: none!important;
  visibility: hidden;
}
.imageDiv .textarea{
  font-size: 16px!important;
  line-height: normal!important;
  height: 20px!important;
  max-width: 100%;
  min-width: 100%;
  resize: none;
  white-space: nowrap;
  border: 1px solid transparent!important;
}
.imageDiv .textarea:focus{
  border: 1px solid #eee!important;
}
.loadingclass{
  width: 100px!important;
  height: 100px!important;
  margin: 0 auto 10px!important;
}
/*视频*/
/* .video_box {*/
/*  position: relative;*/
/*  width: 100%;*/
/*  margin-top: 0.75rem;*/
/*  border-radius: 8px;*/
/*  overflow: hidden;*/
/*  background-color: #f8f8f8;*/
/*}*/
/* .uploading {*/
/*  background: rgba(0, 0, 0, 0.3);*/
/*}*/
/* .video_container {*/
/*  width: 100%;*/
/*  overflow: hidden;*/
/*  border-radius: 8px;*/
/*}*/
/* .video_container video {*/
/*  display: block;*/
/*  width: 100%;*/
/*  height: 100%;*/
/*  object-fit: contain;*/
/*}*/
/* .close_btn {*/
/*   display: none;*/
/*    position: absolute;*/
/*    top: 0.4rem;*/
/*    right: 0.4rem;*/
/*    width: 0.9rem;*/
/*    height: 0.9rem;*/
/*    !*background-image: url('html_delete.png');*!*/
/*    background-repeat: no-repeat;*/
/*    background-position: center;*/
/*    background-size: cover;*/
/*  }*/
/* .video_play_btn {*/
/*  position: absolute;*/
/*  top: 50%;*/
/*  left: 50%;*/
/*  transform: translate(-50%, -50%);*/
/*  width: 1.55rem;*/
/*  height: 1.55rem;*/
/*  background-repeat: no-repeat;*/
/*  background-position: center;*/
/*  background-size: cover;*/
/*}*/
/* .progress_bar {*/
/*  width: 100%;*/
/*  padding-top: 0.35rem;*/
/*  box-sizing: border-box;*/
/*}*/
/* .progress_bar_text {*/
/*  margin-bottom: 0.4rem;*/
/*  font-family: 'PingFang SC';*/
/*  font-style: normal;*/
/*  font-weight: 400;*/
/*  font-size: 0.6rem;*/
/*  line-height: 0.6rem;*/
/*  color: #666666;*/
/*}*/
/* .progress_bar_text i {*/
/*  font-style: normal;*/
/*}*/
/* .progress_bar_line {*/
/*  position: relative;*/
/*  width: 100%;*/
/*  height: 0.1rem;*/
/*  background: #eeeeee;*/
/*}*/
/* .progress_bar_line_up {*/
/*  position: absolute;*/
/*  top: 0;*/
/*  left: 0;*/
/*  height: 100%;*/
/*  background: #0581ce;*/
/*}*/
/**
      引用
       */
.quote_box,
.link_box {
  position: relative;
  margin: 10px 0;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  background: rgba(5, 129, 206, 0.04);
  border-radius: 4px;
}
/*.close {*/
/*    position: absolute;*/
/*    top: .3rem;*/
/*    right: .3rem;*/
/*    padding: 0 .2rem;*/
/*    box-sizing: border-box;*/
/*    width: 2.3rem;*/
/*    height: 1rem;*/
/*    background: rgba(0, 0, 0, 0.45);*/
/*    border-radius: .55rem;*/
/*    color: #fff;*/
/*    font-size: .5rem;*/
/*    line-height: 1rem;*/
/*    display: flex;*/
/*    justify-content: space-between;*/
/*    align-items: center;*/
/*}*/
/*.close .close-icon {*/
/*    width: .7rem;*/
/*    height: .7rem;*/
/*    border-radius: 50%;*/
/*    overflow: hidden;*/
/*    background-image: url("_files/h5_images/icon-circle-close.png");*/
/*    background-repeat: no-repeat;*/
/*    background-size: cover;*/
/*    background-position: center;*/
/*}*/
.quote_box .quote_detail {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.quote_box .quote_detail .quote_ava, .quote_short_ava {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
  width: 156px;
  height: 88px;
  border-radius: 4px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-image: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/logo/img_thumbnail_video_16_9.png');
  position: relative;
}
.quote_box .quote_detail .quote_ava .triangle, .quote_short_ava::after{
  content:'';
  display: inline-block;
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: url(//www.brainmed.com/app/shortplayer/img/icon_triangle.png) no-repeat center;
  background-size: 34%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #fff;
  color: #fff;
  border-radius: 50%;
  font-size: 28px;
}
.quote_box .quote_detail .quote_ava .mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  /* 因为App发的没有办法加遮罩，这里的遮罩不显示 */
  /*background: rgba(0, 0, 0, 0.3);*/
}

.quote_box .quote_detail .quote_ava .triangle {
  width: 38px;
  height: 38px;
  line-height: 38px;
  text-align: center;
  background: url('//www.brainmed.com/app/shortplayer/img/icon_triangle.png')
  no-repeat center;
  background-size: 34%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #fff;
  color: #fff;
  border-radius: 50%;
  font-size: 28px;
}
.quote_box .quote_detail .quote_right,.quote_short_right {
  overflow: hidden;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.quote_title {
  color: #222;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.quote_name, .quote_short_name {
  color: #999;
  font-size: 14px;
  line-height: 14px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.quote_box .quote_desc {
  display: none;
  margin-top: 0.5rem;
  font-size: 0.6rem;
  line-height: 0.85rem;
  color: #888888;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/**
链接
 */
.link_box {
  line-height: 1;
  display: flex;
  align-items: center;
}
.link_box .link_icon {
  margin-right: 8px;
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/question/icon-lj.png') no-repeat center /
      cover;
}
.link_box i {
  font-style: normal;
  color: #279aee;
  font-size: 16px;
  font-weight: 500;
  line-height: 18px;
  text-decoration: underline;
}
.text_title {
  padding: 0;
}
.top_left {
  border-bottom: 0.5px solid rgba(116, 143, 168, 0.1);
}
/**
问答
 */
.que-icon1::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: '';
  /*flex-shrink: 0;*/
  /*display: inline-block;*/
  width: 22px;
  height: 22px;
  background-image: url('https://www.brainmed.com/static/images/icon-question-wh.png?v=1.01');
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  /*margin-right: .05rem;*/
  /*vertical-align: text-top;*/
}
.que-icon2::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  content: '';
  /*flex-shrink: 0;*/
  /*display: inline-block;*/
  width: 22px;
  height: 22px;
  background-image: url('https://www.brainmed.com/static/images/icon-answer.png');
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  /*flex-shrink: 0;*/
  /*display: inline-block;*/
  /*width: .8rem;*/
  /*height: 1.05rem;*/
  /*background-image: url("/static/images/icon-answer.png?v=1.01");*/
  /*background-repeat: no-repeat;*/
  /*background-size: 100%;*/
  /*background-position: center;*/
  /*margin-right: .05rem;*/
  /*vertical-align: text-top;*/
}
.quote_q_title {
  width: 100%;
  /*overflow: hidden;*/
  /*display: flex;*/
  /*justify-content: flex-start;*/
  /*align-items: center;*/
  position: relative;
  padding-left: 30px;
  box-sizing: border-box;
  overflow: hidden;
  font-size: 16px;
  font-weight: 500;
  color: #222;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/*.quote_q_text {*/
/*    overflow: hidden;*/
/*    font-size: .75rem;*/
/*    line-height: 1.05rem;*/
/*    color: #333333;*/
/*    white-space: nowrap;*/
/*    overflow: hidden;*/
/*    text-overflow: ellipsis;*/
/*}*/
.quote_q_content {
  margin-top: 16px;
  color: #222;
  font-size: 15px;
  line-height: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
}
/**
短视频
 */
.short_time {
  padding: 0.2rem 0.25rem;
  position: absolute;
  right: 0.3rem;
  bottom: 0.3rem;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 1.05rem;
  font-style: normal;
  font-size: 0.5rem;
  line-height: 0.5rem;
  color: #ffffff;
  display: none;
}
.quote_short_desc {
  margin-top: 0.5rem;
  font-size: 0.6rem;
  line-height: 0.85rem;
  height: 2.55rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  font-family: 'PingFang SC';
  color: #999999;
  display: none;
}
.quote_box i {
  font-style: normal;
}
.video_container .uploading video{
  width: 100%;
}
.imageBox{
  position: relative;
}
.imageDiv .editImgBtn,.imageDiv .imageEdit{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 24px;
  background: rgba(0, 0, 0, 0.5);
  margin: auto;
  width: 116px;
  line-height: 38px;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}
.imageEdit:hover{
  opacity: 0.8;
}
.editImgBtn_icon{
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.imageDiv .editImgDeleteBtn,.imageDiv .imageDelete{
  width: 30px;
  height: 30px;
  position: absolute;
  top: 12px;
  right: 12px;
  cursor: pointer;
}
.webModule{
  display: flex!important;
}
.imageUrl{
  min-height: 86px;
}
/* 上传视频 */
.video_box{
  position: relative;
}
.videoUrl,.video_container{
  width: 100%;
}
.loadingVideo{
  width: 100px!important;
  height: 100px!important;
  margin: 0 50%;
  transform: translate(-50%,0);
}
.none{
  display: none;
}
.close_btn{
  width: 30px;
  height: 30px;
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex!important;
  visibility: visible;
  background-size: cover;
  cursor: pointer;
  background: url("/ueditor/themes/default/myImages/icon_edit_img_delete.png")no-repeat center center;
  background-size: cover;
}
#initContent{
  display: none;
}
