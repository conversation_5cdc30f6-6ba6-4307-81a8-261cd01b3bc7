// element-ui.js
import Vue from 'vue'
import {
  Dialog,
  Input,
  Button,
  Form,
  FormItem,
  Icon,
  Collapse,
  CollapseItem,
  Header,
  Aside,
  Main,
  Footer,
  MessageBox,
  Message,
  Row,
  Col,
  Container,
  Popover,
  Pagination,
  Carousel,
  CarouselItem,
  Breadcrumb,
  BreadcrumbItem,
  Skeleton,
  SkeletonItem,
  Image,
  Loading,
  Radio,
  RadioGroup,
  InfiniteScroll
} from 'element-ui'
import locale from 'element-ui/lib/locale/lang/en'

import BmBreadcrumb from '../components/BmBreadcrumb/BmBreadcrumb.vue'

const components = [
  Dialog,
  Input,
  Button,
  Form,
  FormItem,
  Icon,
  Collapse,
  CollapseItem,
  Header,
  Aside,
  Main,
  Footer,
  MessageBox,
  Message,
  Row,
  Col,
  Container,
  Popover,
  Pagination,
  Carousel,
  CarouselItem,
  Breadcrumb,
  BreadcrumbItem,
  Skeleton,
  SkeletonItem,
  Image,
  Radio,
  RadioGroup
]
Vue.prototype.$msgbox = MessageBox
Vue.prototype.$message = Message
const Element = {
  install (Vue) {
    components.forEach(component => {
      Vue.component(component.name, component)
    })
    Vue.component("bm-breadcrumb", BmBreadcrumb)
  }
}

Vue.use(Element, { locale })
Vue.use(Loading)
Vue.use(Loading.directive)
Vue.use(InfiniteScroll)
