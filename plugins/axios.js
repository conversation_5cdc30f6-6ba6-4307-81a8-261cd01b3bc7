import {Message} from 'element-ui'
import qs from 'qs'
import {Toast} from 'vant'
import env from '/env-module'
import <PERSON><PERSON> from 'js-cookie'

export default function ({store, redirect, app: {$axios}, app}) {
  // 后端接口地址
  // $axios.defaults.baseURL = env[process.env.NODE_ENV].ENV_API
  // 响应数据格式
  $axios.defaults.responseType = 'json'
  // 超时设置
  $axios.defaults.timeout = 30000
  // 重试次数
  $axios.defaults.retry = 3
  // 重试时间
  $axios.defaults.retryDelay = 100
  // Request拦截器：设置Token
  $axios.onRequest((config) => {
    return new Promise((resolve, reject) => {
      /**
       * post传参序列排序
       * 如果是保存投票接口 不需要序列化
       */

      if (
        config.headers.common &&
        config.headers.common['user-agent'] &&
        (
          config.headers.common['user-agent'].includes("spider") ||
          config.headers.common['user-agent'].includes("bingbot") ||
          config.headers.common['user-agent'].includes("Googlebot") ||
          config.headers.common['user-agent'].includes("360spider")
        )
      ) {
        config.headers['User-Agent'] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
        config.headers.common['user-agent'] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
      }
      if (config.method === 'post') {
        if (config.url === '/wapi/meeting/saveMeetingCheckinInfo.do'
          || config.url === '/toolScore/saveToolScore.jspx'
          || config.url === '/wapi/community/saveQa.do'
          || config.url === '/wapi/v3/mp/saveDraft.do'
          || config.url === '/wapi/v3/mp/updateDraft.do'
          || config.url === '/wapi/v3/mp/update.do'
          || config.url === '/wapi/v3/mp/pc_save.do'
          || config.url === '/wapi/community/updateQa.do'
          || config.url === '/gaiapi/get_query_list'
        ) {
          // eslint-disable-next-line no-self-assign
          config.data = config.data
        } else {
          config.data = config.data instanceof FormData ? config.data : qs.stringify(config.data)
        }
      }
      /**
       * 筛选一些接口地址  某些接口地址不需要携带token
       * @type {string}
       */
      const configdomain = config.url.split('/')[2]
      const configUrl = config.url.split('/')[1]
      const wsUrl = configUrl !== 'ws' // 请求地址不等于ws
      const backurl = config.url.indexOf('loginByToken.jspx') === -1 // 请求接口地址 不是loginByToken.jspx 这个接口
      const wsDev = configdomain !== 'wsdev.medtion.com' // 请求接口域名不等于 wsdev这个域名
      const ws = configdomain !== 'ws.medtion.com'// 请求接口域名不等于 ws这个域名
      const globalUrl = configUrl !== "api"
      /**
       * 获取token
       * @type {string}
       */

      const cookieOld = Cookie.get('medtion_token_only_sign')
      const tokenCookie = app.$cookies.get('medtion_token_only_sign') // cookie存储的token
      const tokenVuex = store.state.auth.token // vuex存储的cookie
      // TODO 如果有token 并且请求地址不是ws的 并且请求地址不是获取弹幕地址的  就代表登录成功, 在之后的请求中待上token 并且不是loginByToken这个接口
      /**
       * 如果cookie下有token 或者vuex下有token 并且 地址不等于ws 并且请求地址不是wsdev这个域名 并且不是ws这个域名 并且不是loginBytoken 这个接口
       * 都通过后请求头中添加token
       */
      if ((cookieOld || tokenCookie) && wsUrl && wsDev && ws && backurl && globalUrl) {
        config.headers.Authorization = `Bearer ${cookieOld || tokenCookie}`
      }
      //其他的请求前业务逻辑 比如：api map
      resolve(config)
    })
  })
  // Error拦截器：出现错误的时候被调用，根据状态码做对应判断并显示全局Message
  $axios.onError((error) => {
    const code = parseInt(error.response && error.response.status)
    const errorValue = {
      message: null
    }
    switch (code) {
      case 400:
        errorValue.message = '请求错误(400)'
        break
      case 401:
        errorValue.message = '请登录后访问(401)'
        break
      case 403:
        errorValue.message = '拒绝访问(403)'
        break
      case 404:
        errorValue.message = '请求出错(404)'
        break
      case 408:
        errorValue.message = '请求超时(408)'
        break
      case 500:
        errorValue.message = '服务器错误(500)'
        break
      case 501:
        errorValue.message = '服务未实现(501)'
        break
      case 502:
        errorValue.message = '网络错误(502)'
        break
      case 503:
        errorValue.message = '服务不可用(503)'
        break
      case 504:
        errorValue.message = '网络超时(504)'
        break
      case 505:
        errorValue.message = 'HTTP版本不受支持(505)'
        break
      default: {
        break
      }
    }
    if (errorValue.message) {
      Toast(errorValue.message)
      // redirect('/signin');

    }
    return error
  })
  // Response拦截器：对正常返回的数据进行处理
  $axios.onResponse((response) => {
    const responseValue = {
      message: null
    }
    switch (response.data ? response.data.code : '00') {
      case 400:
        responseValue.message = response.data.message
        break
      case 401:
        responseValue.message = response.data.message
        store.commit('editBackUrl', window.location.href)
        if (app.router.history._startLocation.includes('/meeting/detail?id=2903') || app.router.history._startLocation.includes('/meeting/detail?id=3114')) {
          app.router.push({name: 'signin', query: {fallbackUrl: window.location.href, source: 'meeting_3114'}})
        } else {
          app.router.push({name: 'signin', query: {fallbackUrl: window.location.href}})
        }
        break
      case 403:
        responseValue.message = response.data.message
        break
      case 404:
        responseValue.message = response.data.message
        break
      case 408:
        responseValue.message = response.data.message
        break
      case 500:
        responseValue.message = response.data.message
        break
      case 501:
        responseValue.message = response.data.message
        break
      case 502:
        responseValue.message = response.data.message
        break
      case 503:
        responseValue.message = response.data.message
        break
      case 504:
        responseValue.message = response.data.message
        break
      case 505:
        responseValue.message = response.data.message
        break
      case '-1':
        responseValue.message = response.data.msg
        break
      case '00':
        responseValue.message = '服务器错误'
        break
    }
    // 这个接口不提示
    const backurl = response.config.url.indexOf('loginByToken.jspx')
    const addWebApiChoice = response.config.url.includes('/wapi/lottery/addWebApiChoice.do')
    if (
      responseValue.message !== null
      && response.config.url !== '/wapi/meeting/getMeetingRequireCheckinInfo.do'
      && backurl === -1
      && !addWebApiChoice
    ) {
      Toast(responseValue.message)
    }
    return response
  })
}
