import env from '/env-module'
import {loginByToken} from '@/api/login'
import Cookie from 'js-cookie'
import Vue from 'vue'

export default ({app, $axios, store, route, redirect}) => {
  // nuxt前置路由守卫
  app.router.beforeEach((to, from, next) => {
    if (to.name !== 'index-browsing-history' || to.name !== 'index-personaldata' || to.name !== 'index-user-center') {
      store.commit('setuserPopUpAcriveFn', '')
    }
    // 进入消息中心
    if (to.path.includes('/message-center')) {
      if (!store.state.auth.token) {
        store.commit(
          'editBackUrl',
          window.location.href +
          `?&fallbackUrl=${env[process.env.NODE_ENV].ENV_API + to.path}`
        ) // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.fullPath, thirdParty: 'thirdParty'},
        })
      }
    }
    switch (to.name) {
      case 'index-user-center': {
        store.commit('editColumnListState', 'Release')
        break
      }
    }
    if (to.fullPath === '/channel/home') {
      return
    }
    switch (to.path) {
      case '/cloud': {
        // 跳转云课堂详情 同步登录
        const path = to.query.path
        $axios.$request(loginByToken(
          {token: store.state.auth.token})).then((res) => {
          /**
           * 不管有没有登录 都跳到云课堂
           */
          Cookie.set('medtion_member_id', store.state.auth.user.id, {sameSite: 'lax'})
          window.open(env[process.env.NODE_ENV].ENV_NEW_URL + 'ocs/index.html#' + path)
        })
        return
      }
      case '/personalfile': {
        // 跳转投审稿
        if (!store.state.auth.token) {
          store.commit(
            'editBackUrl',
            window.location.href +
            `?&fallbackUrl=${
              env[process.env.NODE_ENV].ENV_API + 'contributionLogin.jspx'
            }`
          ) // 用于微信跳转
          next({
            path: '/signin',
            query: {fallbackUrl: to.fullPath, thirdParty: 'thirdParty', ForceLogin: null}
          })
        } else {
          to.query.thirdParty ? window.location.replace(env[process.env.NODE_ENV].ENV_API + 'contributionLogin.jspx' + `${store.state.auth.token ? '?token=' + store.state.auth.token : ''}`)
            :
            window.open(
              env[process.env.NODE_ENV].ENV_API + 'contributionLogin.jspx' + `${store.state.auth.token ? '?token=' + store.state.auth.token : ''}`
            )
        }
        return
      }
      case '/mooc': {
        // 跳转云课堂
        if (!store.state.auth.token) {
          store.commit('editBackUrl', window.location.href + `?&fallbackUrl=${
            'https://' + window.location.host + '/mooc/%23/'
          }`
          ) // 用于微信跳转
          next({
            path: '/signin',
            query: {fallbackUrl: '/mooc', thirdParty: 'thirdParty'}
          })
        } else {
          $axios.$request(loginByToken({token: store.state.auth.token})).then((res) => {
            /**
             * 不管有没有登录 都跳到云课堂 如果登录 用于写cookie的
             * @type {string|string|*}
             */
            Cookie.set('medtion_member_id', store.state.auth.user.id, {sameSite: 'lax'})
            Cookie.set('xuanwu_member_unionid', store.state.auth.unionid, {sameSite: 'lax'})

            if (to.query.thirdParty) {
              window.location.replace('https://' + window.location.host + '/mooc/#/')

            } else {
              window.open('https://' + window.location.host + '/mooc/#/')
            }
          })
        }
        return
      }
    }
    if (/\/topic-circle/.test(to.path)) {
      // 跳转话题圈子
      if (!store.state.auth.token) {
        store.commit(
          'editBackUrl',
          window.location.href +
          `?&fallbackUrl=${
            env[process.env.NODE_ENV].ENV_API + 'contributionLogin.jspx'
          }`
        )
        // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.fullPath, ForceLogin: null}
        })
        return;
      }
    }
    // 编辑器页面必须登录，不写这个点击叉号无效
    if (/\/editor/.test(to.query.fallbackUrl) && !/ForceLogin/.test(to.fullPath)) {
      // 跳转话题圈子
      if (!store.state.auth.token) {
        store.commit(
          'editBackUrl',
          window.location.href +
          `?&fallbackUrl=${
            env[process.env.NODE_ENV].ENV_API + 'contributionLogin.jspx'
          }`
        )// 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.query.fallbackUrl, ForceLogin: null}
        })
        return;
      }
    }
    switch (to.fullPath) {
      case '/introduce': {
        // 跳转关于我们 禁止跳转 要带参数
        return
      }
    }


    // 跳转发布
    if (to.path === '/release') {
      if (!store.state.auth.token) {
        store.commit(
          'editBackUrl',
          window.location.href +
          `?&fallbackUrl=${
            env[process.env.NODE_ENV].ENV_API + '/mppc/list.jspx'
          }`
        ) // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.fullPath, thirdParty: 'thirdParty'}
        })
      } else {
        to.query.thirdParty ? window.location.replace(env[process.env.NODE_ENV].ENV_API + '/mppc/list.jspx' + `${store.state.auth.token ? '?token=' + store.state.auth.token : ''}`)
          :
          window.open(
            env[process.env.NODE_ENV].ENV_API + '/mppc/list.jspx' + `${store.state.auth.token ? '?token=' + store.state.auth.token : ''}`
          )
      }
      return
    } else if (to.path === '/patient') {
      // 传递userid时加密
      let key =
        'r29Us7YbEsJXEnwWJ8WLPljnhmsS7f7W0T0cx8aocuz9vvzuGdIMts9pQGxOeub61AmuhZCkEj12R2z5SjhyBmIiU4ayk7MUHya4ueCTpnG4gM5SRFEW4W9c3BRHC1py'
      const userIdAes = window.CryptoJS.AES.encrypt(
        String(store.state.auth.user.id),
        key
      ).toString()
      if (!store.state.auth.token) {
        store.commit(
          'editBackUrl',
          window.location.href +
          `?&fallbackUrl=${
            env[process.env.NODE_ENV].ENV_PATIENT +
            '/followDoctor/index/homepage' +
            `?doctorId=${userIdAes}`
          }`
        ) // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.fullPath, thirdParty: 'thirdParty'}
        })
      } else {
        to.query.thirdParty
          ? (
            window.location.replace(env[process.env.NODE_ENV].ENV_PATIENT +
              '/followDoctor/index/homepage' +
              `?doctorId=${userIdAes}`)
          )
          : window.open(
            env[process.env.NODE_ENV].ENV_PATIENT +
            '/followDoctor/index/homepage' +
            `?doctorId=${userIdAes}`
          )
      }
      return false
    } else if (to.path === '/cloudclassroomCourse') {
      /**
       * 云课堂列表跳转
       */
      if (!store.state.auth.token) {
        store.commit('editBackUrl', window.location.href + `?&fallbackUrl=${env[process.env.NODE_ENV].ENV_NEW_URL + 'ocs/index.html%23/pages/coursePlay/coursePlay?courseId=' + to.query.courseId}`
        ) // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.path + `?courseId=${to.query.courseId}`, thirdParty: 'thirdParty'}
        })
      } else {
        $axios.$request(loginByToken({token: store.state.auth.token})).then((res) => {
          /**
           * 不管有没有登录 都跳到云课堂 如果登录 用于写cookie的
           * @type {string|string|*}
           */
          if (res.code === 1) {
            Cookie.set('medtion_member_id', store.state.auth.user.id, {sameSite: 'lax'})
            const url =
              store.state.back_url.split('fallbackUrl=').length > 1
                ? store.state.back_url
                  .split('fallbackUrl=')[1]
                  .replace('%23', '#')
                : env[process.env.NODE_ENV].ENV_NEW_URL +
                'ocs/index.html#/pages/coursePlay/coursePlay?courseId=' +
                to.query.courseId
            to.query.thirdParty
              ? (window.location.href = url)
              : window.open(
                env[process.env.NODE_ENV].ENV_NEW_URL +
                'ocs/index.html#/pages/coursePlay/coursePlay?courseId=' +
                to.query.courseId,
                '_blank'
              )
          }
        })
      }
      return false
    } else if (to.path === '/myCourses') {
      /**
       * 云课堂列表跳转
       */
      if (!store.state.auth.token) {
        store.commit('editBackUrl', window.location.href + `?&fallbackUrl=${env[process.env.NODE_ENV].ENV_NEW_URL + 'ocs/index.html%23/course/' + to.query.courseId}`
        ) // 用于微信跳转
        next({
          path: '/signin',
          query: {fallbackUrl: to.path, thirdParty: 'thirdParty'}
        })
      } else {
        $axios.$request(loginByToken({token: store.state.auth.token})).then((res) => {
          /**
           * 不管有没有登录 都跳到云课堂 如果登录 用于写cookie的
           * @type {string|string|*}
           */
          if (res.code === 1) {
            Cookie.set('medtion_member_id', store.state.auth.user.id, {sameSite: 'lax'})
            const url =
              store.state.back_url.split('fallbackUrl=').length > 1
                ? store.state.back_url
                  .split('fallbackUrl=')[1]
                  .replace('%23', '#')
                : env[process.env.NODE_ENV].ENV_NEW_URL +
                '/mooc/#/myCourses'
            to.query.thirdParty
              ? (window.location.href = url)
              : window.open(
                env[process.env.NODE_ENV].ENV_NEW_URL +
                '/mooc/#/myCourses',
                '_blank'
              )
          }
        })
      }
      return false
    }


    // 路由守卫 如果说没有token 不能去在个人中心页面
    const tokenCookie = app.$cookies.get('medtion_token_only_sign') // cookie存储的token
    if (to.path === '/personaldata' && (!tokenCookie || tokenCookie === '')) {
      next(false)
      return false
    }
    next()
  })
  // 后置路由守卫
  app.router.afterEach((to, from) => {
    if (from.name) {
      if (to.path !== '/channel/home' && to.path !== '/meeting/detail' && to.path !== '/user-center' && to.name !== 'index-bms-classify-categoryId-comparison-productCategoryId-modelId') {
        // scroll bar Top
        // firefox scroll bar Top
        document.body.scrollTop = 0
        document.documentElement.scrollTop = 0
      }
    }
    store.commit('editNavPathFun', '/' + to.path.split('/')[1]) // 导航高亮
    // eslint-disable-next-line no-undef
    setTimeout(() => {
      Vue.prototype.$analysys.pageview(store.state.auth.user.id, store.state.auth.unionid, document.title, to.fullPath)
    }, 1000)
  })
}
