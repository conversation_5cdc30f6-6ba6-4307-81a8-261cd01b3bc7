import createPersistedState from 'vuex-persistedstate'

// 这里什么都可以写, 不过如果你需要用到 nuxt 本身, 必须通过 export 的方式暴露函数来接收
/**
 * vuex持久化缓存
 * 注意 nuxt是通过服务器端渲染 , 此插件只是用于保存在客户端 当页面中需要用到服务端虚渲染 要拿数据时, 可以通过cookie 去拿token  userid 等,
 * @param store
 */
export default ({ store }) => {
  // 如果想要等到 nuxt 加载完毕
  // 再执行代码, 有一个函数叫做 window.onNuxtReady()
  // 会话存储
  window.onNuxtReady((val) => {
    createPersistedState({
      storage: sessionStorage,
      reducer (val) {
        return {
          registerPassword: val.registerPassword
          , accountType: val.accountType
          , personalCenterState: val.personalCenterState// 个人中心状态
          , seletIdentity: val.seletIdentity // 选择身份
          // , searchListData: val.searchListData//  搜素列表
          // , searchTabsCurrent: val.searchTabsCurrent // 搜素下标
          // , seachInfo: val.seachInfo// 搜素内容
          // , searchListTotal: val.searchListTotal// 搜索条数
          // , identityInformation: val.identityInformation // 注册完善信息
          , back_url: val.back_url // 返回地址
          , consultingPageInfo: val.consultingPageInfo // 记录资讯的选中状态
          // , searchListCurrent: val.searchListCurrent
          , userPopUpAcrive: val.userPopUpAcrive
          , openeds: val.minitool.openeds //
          , scoreCache: val.minitool.scoreCache
        }
      }
    })(store)
  })
  window.onNuxtReady((val) => {
    createPersistedState({
      storage: localStorage,
      reducer (val) {
        return {
          historicalSearchInfo: val.historicalSearchInfo// 历史搜索
          ,departmentHistoricalSearchInfo:val.departmentHistoricalSearchInfo
        }
      }
    })(store)
  })
}
