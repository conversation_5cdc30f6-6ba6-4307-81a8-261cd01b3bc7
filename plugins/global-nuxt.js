/**
 * 全局变量
 */
import Vue from 'vue'
/**
 * 易观埋点
 */
// eslint-disable-next-line import/no-absolute-path
import Cookies from 'cookie-universal-nuxt'// 获取cookie
import VueClipboard from 'vue-clipboard2'
import timeStamp from '@/assets/js/conversion-time' // 处理时间
import tool from '@/assets/js/tool-set' // 工具集合
const main = {
  install(Vue) {
    // Vue.prototype.$cookies = Cookies;// 全局api
    Vue.prototype.timeStamp = timeStamp // 转换时间戳的方法
    Vue.prototype.$tool = tool // 工具集合
    Vue.prototype.$appid = 'wx204e9888a42c9142'
    Vue.prototype.$DDNS_URL = encodeURIComponent('http://www.brainmed.com/wxweb/redirect.jspx')
  }
}
Vue.use(VueClipboard)// 全局复制插件
Vue.use(main) // 这里不能丢

// 这里是 为了在 asyncData 方法中使用
export default ({app, req, store}, inject) => {
  // console.log("全局")
  let TerminalType = ""
  const userAgent = req ? req.headers['user-agent'] : ""
  if (userAgent.includes('BrainMedPC_Win')) {
    TerminalType = "BrainMedPC_Win"
  }
  if (userAgent.includes('BrainMedPC_Mac')) {
    TerminalType = "BrainMedPC_Mac"
  }
  if (TerminalType) {
    app.$cookies.set("TerminalType", TerminalType)
    store.commit('cs/setTerminalType', TerminalType)
  }

  // Set the function directly on the context.app object
  app.timeStamp = timeStamp // 日期格式转换
  app.$tool = tool // 日期格式转换
  app.$uploadUrl = '/wapi/upload/uploadOssImage.do' // 上传图片路径
  app.$appid = 'wx204e9888a42c9142' // 微信登录 appid\
  app.$DDNS_URL = encodeURIComponent('https://www.brainmed.com')
}
