/**
 * 资料库相关接口
 * <AUTHOR>
 * @date 2025-03-14
 */

/**
 * 获取资料库列表
 * @param obj
 * @returns {{method: string, params: {}, url: string}}
 */
export function getFavoritesByUserId(obj = {}) {
  return {
    url: '/wapi/v1/favorite/getFavoritesByUserId.do',
    method: 'get',
    params: obj
  }
}

/**
 * 获取关注的资料库列表
 * @param obj
 * @returns {{method: string, params: {}, url: string}}
 */
export function getFollowFavoritesByUserId(obj = {}) {
  return {
    url: '/wapi/v1/favorite/getFollowFavoritesByUserId.do',
    method: 'get',
    params: obj
  }
}

/**
 * 获取资料库详情
 * @param obj
 * @returns {{method: string, params: {}, url: string}}
 */
export function getFavoriteDetail(obj = {}) {
  return {
    url: '/wapi/v1/favorite/getFavoriteDetail.do',
    method: 'get',
    params: obj
  }
}

/**
 * 根据资料库ID获取资料库的收藏内容
 * @param obj
 * @returns {{method: string, params: {}, url: string}}
 */
export function getUserCollectContentByFavoriteId(obj = {}) {
  return {
    url: '/wapi/v1/favorite/getUserCollectContentByFavoriteId.do',
    method: 'get',
    params: obj
  }
}

/**
 * 创建资料库
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function saveFavorite(obj = {}) {
  return {
    url: '/wapi/v1/favorite/saveFavorite.do',
    method: 'post',
    data: obj
  }
}

/**
 * 修改资料库
 * @param obj
 * @returns {{method: string, params: {}, url: string}}
 */
export function updateFavorite(obj = {}) {
  return {
    url: '/wapi/v1/favorite/updateFavorite.do',
    method: 'post',
    params: obj
  }
}

/**
 * 删除资料库
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function deleteFavorite(obj = {}) {
  return {
    url: '/wapi/v1/favorite/deleteFavorite.do',
    method: 'post',
    data: obj
  }
}

/**
 * 操作资料库时：批量添加资料库内容
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function addFavoriteContent(obj = {}) {
  return {
    url: '/wapi/v1/favorite/addFavoriteContent.do',
    method: 'post',
    data: obj
  }
}

/**
 * 操作资料库时：批量移除资料库内容
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function deleteFavoriteContent(obj = {}) {
  return {
    url: '/wapi/v1/favorite/deleteFavoriteContent.do',
    method: 'post',
    data: obj
  }
}

/**
 * 操作资料库时：批量移动资料库内容
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function moveFavoriteContent(obj = {}) {
  return {
    url: '/wapi/v1/favorite/moveFavoriteContent.do',
    method: 'post',
    data: obj
  }
}

/**
 * 关注/取消关注资料库
 * @param obj
 * @returns {{method: string, data: {}, url: string}}
 */
export function followFavorite(obj = {}) {
  return {
    url: '/wapi/v1/favorite/followFavorite.do',
    method: 'post',
    data: obj
  }
}
