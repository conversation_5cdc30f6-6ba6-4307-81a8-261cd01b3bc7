/deep/ .check_wrapper {
  position: relative;

  &::before {
    z-index: 10;
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.department_wrapper {
  padding-left: 70px;
  border-radius: 6px;
  border: 1px solid #EBEBEB;
  box-sizing: border-box;

  /deep/ .el-input__inner {
    padding-left: 6px !important;
  }

  /deep/ .el-input {
    border-radius: 6px;
    border: unset !important;

    &:focus-within {
      border: unset !important;
    }
  }
}

.default_wrapper {
  height: 48px;
  line-height: 48px;
  padding-left: 115px;
  border-radius: 6px;
  border: 1px solid #EBEBEB;
  box-sizing: border-box;

  /deep/ .el-input__inner {
    padding-left: 6px !important;
  }

  /deep/ .el-input {
    border-radius: 6px;
    border: unset !important;

    &:focus-within {
      border: unset !important;
    }
  }
}

/deep/ .el-select__tags {
  box-sizing: border-box;
  flex-wrap: nowrap;
  width: 360px;
  flex-shrink: 0;
  overflow: hidden;
  padding-left: 74px;
}

body, #__nuxt, #__layout, #__layout {
  height: 100%;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type='number'] {
  -moz-appearance: textfield !important;
}

/deep/ .el-form-item {
  margin-bottom: 15px;
}

/deep/ .el-input__inner {
  width: 100%;
  height: 48px;
  line-height: 48px;
  background: #F5FBFE;
  border: none;
  font-size: 16px;
}

.demoChoiceForm {
  .input-p-select {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translate(0, -50%);
    z-index: 999;
    font-size: 18px;
    color: #666666;
  }
  /deep/ .el-cascader{
    width: 100%;
    .el-input__inner{
      padding-left: 105px!important;
    }
  }
  /deep/ .el-select {
    width: 100%;
  }

  /deep/ .el-input__suffix-inner, /deep/ .el-input__icon {
    line-height: 48px;
  }

  ///deep/.el-icon-arrow-down:before {
  //  font-family: "iconfont";
  //  content: "\e641";
  //  font-size: 18px;
  //  color: #0581CE;
  //  transform: rotateZ(180deg);
  //}

  /deep/ .el-icon-arrow-up:before {
    font-family: "iconfontNew";
    content: "\e641";
    font-size: 18px;
    color: #0581CE;
  }

  /deep/ .product_select_select .el-input__inner {
    padding-left: 95px !important;
  }

  /deep/ .el-input--suffix .el-input__inner {
    padding-left: 75px;
  }

  /deep/ .threeItme .el-input--suffix .el-input__inner {
    padding-left: 95px;
  }

  /deep/ .el-input__inner {
    background: #FFFFFF;
  }

  /deep/ .el-input {
    border-radius: 6px;
    border: 1px solid #EBEBEB;

    &:focus-within {
      border: 1px solid #0581CE;
    }
  }

  /deep/ .el-form-item {
    margin-bottom: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /deep/ .el-input-group__prepend {
    background: #FFFFFF;
    padding-left: 20px;
    font-size: 18px;
    color: #666666;
    line-height: 48px;
  }
}

/deep/ .el-input__suffix-inner {
  line-height: 54px;
}

/deep/ .el-form-item__content {
  line-height: 0;
  position: relative;
}

/deep/ .el-checkbox__inner {
  border-radius: 4px;
}

/deep/ .el-radio__input.is-checked + .el-radio__label {
  color: #666666;
}

/deep/ .el-form-item__error {
  display: none;
}

/deep/ .el-input__validateIcon {
  display: none;
}

.lable_tong {
  margin-bottom: 25px;
  text-align: center;
}

.button_item {
  width: 100%;
  border-radius: 12px !important;
  height: 47px;
  text-align: center;
  line-height: 47px;
  padding: 0;
}

.remember_box {
  margin-bottom: 15px;

  .closepassword {
    font-size: 14px;
    color: #FF922D;
  }
}

.passwordflag {
  width: 20px;
  height: 20px;
  margin-right: 19px;
}

/deep/ .el-input-group__prepend {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 0 0 24px;
  color: #759BB2;
  font-size: 16px;
  line-height: 48px;
}

/deep/ .el-input-group__append {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 24px;

  .verificationcode {
    font-size: 16px;
    color: #0581CE;
    line-height: 19px;

    span {
      color: #9FCFED;
      margin-right: 3px;
    }
  }
}

.signin-bg-box {
  height: 100vh;
  background-image: url("~assets/images/bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  //background-size: 100% 100%;
  background-position: center;

  .login-header-logo {
    margin-top: 10px;
    width: 302px;

    img {
      width: 100%;
    }
  }

  .signin-center-box {
    position: absolute;
    left: 50%;
    top: calc(50%);
    width: 488px;
    transform: translate(-50%, -50%);
    background: #FFFFFF;
    border-radius: 30px;
    padding: 36px 47px 40px;
    box-sizing: border-box;
    box-shadow: 0px 10px 15px 1px rgba(0, 0, 0, 0.1);
    max-height: 90vh;
    overflow-y: auto;

    .close {
      width: 22px;
      height: 22px;
      text-align: center;
      line-height: 20px;
      background: #EBEBEB;
      position: absolute;
      top: 30px;
      right: 30px;

      &:hover i {
        color: #B1B1B1;
      }

      i {
        font-size: 13px;
        color: #949494;
      }
    }

    .weixin-login-box {
      margin-bottom: 37px;
      text-align: center;

      .weixin-box {
        width: 208px;
        height: 208px;
        border: 1px solid #F2F2F2;
        box-sizing: border-box;
        margin: 0 auto 29px;

        .weixin-tip {
          color: #666666;
        }

        #weixinbox {
          height: 100%;

          .title {
            display: none;
          }
        }
      }
    }

    .login-switching-list {
      margin-bottom: 44px;
      align-items: end;
      position: relative;

      .verificationcode-tip {
        position: absolute;
        left: 0;
        top: calc(100% + 6px);
        color: #666666;
      }

      .login-switching-item {
        margin-right: 15px;
        font-size: 20px;
        font-weight: 550;
        color: #888888;

        &:last-child {
          margin-right: 0;
        }
      }

      .is_active {
        font-size: 24px;
        color: #000000;
      }
    }

    .login-button_list {
      margin-bottom: 30px;
    }

    .tip-buttom {
      line-height: 16px;

      .tip-register {
        font-size: 14px;
        color: #92A3AE;
        text-align: center;
        line-height: 16px;
      }
    }

    .register-center-identity-box {
      .identity-selection-list-box {
        margin-bottom: 40px;

        .identity-selection-list {
          height: 68px;
          background: #F8F8F8;
          border-radius: 12px;
          margin-bottom: 14px;
          padding-left: 25px;

          &:last-child {
            margin-bottom: 0;
          }

          .identity-selection-list-icon {
            margin-right: 18px;

            .identity-selection-icon {
              width: 30px;
              height: 30px;
            }
          }

          .identity-selection-list-infomation {
            .identity-selection-list-identity-name {
              line-height: 25px;
              margin-bottom: 4px;
              font-size: 18px;
            }

            .identity-selection-list-identity-content {
              color: #666666;
              line-height: 16px;
            }
          }

        }
      }

      .is_active_identity {
        background: #FFFFFF !important;
        outline: 1px solid #0581CE;

        .identity-selection-list-infomation {
          .identity-selection-list-identity-name {
            color: #0581CE;
          }

          .identity-selection-list-identity-content {
            color: #759BB2 !important;
          }
        }
      }
    }

    .identity-authentication {
      .login-switching-list {
        margin-bottom: 20px;
      }
    }

    .register-center-identity-button_item {
      background: #0581CE;
      color: #EEF8FF;

      &:active {
        background: #EEF8FF;
        color: #0581CE;
      }
    }

    .againwrite {
      text-align: center;
      color: #759BB2;
      margin-top: 20px;
      line-height: 18px;
      cursor: pointer;
    }

    .identity-authentication-tip {
      color: #888888;
      margin: 6px 0 40px 20px;
    }
  }
}
/deep/ .activeCompanyWrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
  }
}
