<template>
  <div class='signin-bg-box'>
    <div class='container-box'>
      <div class='login-header-logo'>
        <nuxt-link to='/'>
          <img alt='' class='img_render' src='~assets/images/login-logo.png'>
        </nuxt-link>
      </div>
    </div>
    <!-- 登录输入框 -->
    <div class='signin-center-box'>
      <!-- 注册输入框 -->
      <div v-if='$store.state.identityInformation === "Register"' class='register-center-box'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            注册
          </li>
        </ul>
        <!-- 登录表单 -->
        <el-form ref='loginForm' :model='registerForm' :rules='registerRules'>
          <!-- 账号密码登录 -->
          <el-form-item prop='phoneAndEmail'>
            <el-input v-model='registerForm.phoneAndEmail' auto-complete='new-password'>
              <p slot='prepend' class='input-p'>手机号/邮箱:</p>
            </el-input>
          </el-form-item>
          <el-form-item>
            <SliderVerification
              :reload="reloadSlider"
              @finishSlide="finishSlide"
            />
          </el-form-item>
          <el-form-item prop='captcha'>
            <el-input v-model='registerForm.captcha' auto-complete='new-password' type='number'>
              <p slot='prepend' class='input-p'>验证码:</p>
              <template slot='append'>
                <GetPhoneVerification
                  :sliderVerificationCode="sliderVerificationCode"
                  :account-number='registerForm.phoneAndEmail'
                  @getValidationFun='getValidationFun'
                  @finishCode="reloadSlider = true"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop='password'>
            <el-input
              v-model='registerForm.password' :type="passwordflag?'text':'password'"
              auto-complete='new-password'>
              <svg-icon
                slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
              <p slot='prepend' class='input-p'>设置密码:</p>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button id='themeButton' class='button_item' type='primary' @click='registerFun("loginForm")'>注册
            </el-button>
          </el-form-item>
          <el-form-item v-if='loginType !== "weixin"' prop='agreement'>
            <p class='lable_tong'>
              <radio v-model='registerForm.agreement' label='1'>
                <agreement></agreement>
              </radio>
            </p>
          </el-form-item>
        </el-form>
        <!-- 微信登录/邮箱登录 -->
        <!-- tips buttom -->
        <div class='tip-buttom'>
          <p class='tip-register'>
            <span>已有账号</span>
            <nuxt-link :to='{name:"signin"}'>
              <span class='register fontSize16 themeFontColor' @click="analysysFun('去登录')">去登录</span>
            </nuxt-link>
          </p>
        </div>
        <!-- 微信登录/邮箱登录 -->
        <!-- close -->
        <nuxt-link to='/'>
          <div class='close img_radius'>
            <i class='el-icon-close'></i>
          </div>
        </nuxt-link>
      </div>
      <!-- 注册选择身份信息 -->
      <div v-if='$store.state.identityInformation === "SelectIdentity"' class='register-center-identity-box'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            身份选择
          </li>
          <li class='verificationcode-tip fontSize12'>
            请选择您的身份，以便我们更好的为您服务
          </li>
        </ul>
        <ul class='identity-selection-list-box'>
          <li
            v-for='(item) in identitySelectionData'
            :key='item.name'
            :class='{is_active_identity:identityCurrent === item.id}'
            class='identity-selection-list flex_start flex_align_center cursor' @click='identityCurrent = item.id'>
            <div class='identity-selection-list-icon'>
              <SvgIcon :icon-class='item.name' class-name='identity-selection-icon'/>
            </div>
            <div class='identity-selection-list-infomation'>
              <p class='identity-selection-list-identity-name fontWeight'>{{ item.identityName }}</p>
              <p class='identity-selection-list-identity-content fontSize14'>{{ item.identityContent }}</p>
            </div>
          </li>
        </ul>
        <div class='register-center-identity-button_item button_item cursor' @click='selectidentityFun()'>下一步</div>
      </div>
      <!-- 医务工作者 -->
      <div v-if='$store.state.identityInformation === 1' class='register-center-identity-box identity-authentication'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            基础信息
          </li>
        </ul>
        <el-form
          ref='saveIdentityInfoForm' :model='saveIdentityInfoForm' :rules='saveIdentityInfoRules'
          class='demoChoiceForm'
          status-icon>
          <el-form-item prop='realName'>
            <el-input v-model='saveIdentityInfoForm.realName' autocomplete='off' placeholder='请输入姓名' type='text'>
              <p slot='prepend' class='input-p'>姓名:</p>
            </el-input>
          </el-form-item>
          <el-form-item prop='company'>
            <autocomplete
              v-model='saveIdentityInfoForm.company' :fetch-suggestions='querySearch'
              :trigger-on-focus='false'
              class='inline-input'
              placeholder='请输入医院'
              style='width: 100%'
              value-key='name'
              @select="selectCompanyHandler"
              @change="selectCompanyHandler"
            >
              <p slot='prepend' class='input-p'>医院:</p>
              <template slot-scope='{ item }'>
                <div class='name'>{{ item.name }}</div>
              </template>
            </autocomplete>
          </el-form-item>
          <el-form-item prop='department'>
            <div>
              <div class='input-p-select'>科室:</div>
              <Select
                v-model='saveIdentityInfoForm.department'
                collapse-tags
                multiple
                placeholder='请选择科室'
                popper-class="personalInformationForm_department_select"
                no-data-text="请先选择医院"
              >
                <Option
                  v-for="item in selectDepartmentOpt"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                >
                  <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                  <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                  <span style="margin-left: 10px">{{ item.name }}</span>
                </Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item prop='speciality'>
            <div>
              <div class='input-p-select'>亚专业:</div>
              <Select v-model='saveIdentityInfoForm.speciality' class='threeItme' placeholder='请选择亚专业'>
                <Option
                  v-for='item in specialitieList' :key='item.id' :label='item.name'
                  :value='item.id'></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item prop='title'>
            <div class='input-p-select'>职称:</div>
            <Select
              v-model='saveIdentityInfoForm.title'
              multiple
              collapse-tags
              placeholder='请选择职称'
              popper-class="personalInformationForm_department_select"
              @change="changeTitleHandler"
            >
              <Option
                v-for='item in titleList?titleList.listA:[]'
                :key='item.id' :label='item.name' :value='item.name'>
                <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                <span style="margin-left: 10px">{{ item.name }}</span>
              </Option>
              <div class="line" style="margin: 8px 10px;width:100%;height:1px;background: #EEE"></div>
              <Option
                v-for='item in titleList?titleList.listB:[]'
                :key='item.id' :label='item.name' :value='item.name'>
                <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                <span style="margin-left: 10px">{{ item.name }}</span>
              </Option>
              <div class="line" style="margin: 8px 10px;width:100%;height:1px;background: #EEE"></div>
              <Option
                v-for='item in titleList?titleList.listC:[]'
                :key='item.id' :label='item.name' :value='item.name'>
                <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                <span style="margin-left: 10px">{{ item.name }}</span>
              </Option>
            </Select>
          </el-form-item>
          <el-form-item v-if='$store.state.accountType === "tel"' prop='email'>
            <el-input
              v-model='saveIdentityInfoForm.email' autocomplete='off' placeholder='请输入邮箱 (必填)'
              type='text'>
              <p slot='prepend' class='input-p'>邮箱:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='wechatpassword'>
            <el-input
              v-model='saveIdentityInfoForm.wechatpassword' autocomplete='off' placeholder='请输入密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>设置密码:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='confirmpassword'>
            <el-input
              v-model='saveIdentityInfoForm.confirmpassword' autocomplete='off' placeholder='请确认密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>确认密码:</p>
            </el-input>
          </el-form-item>
        </el-form>
        <p class='identity-authentication-tip fontSize12'>
          *该邮箱信息仅用于身份认证，不可作为登录账号使用
        </p>
        <div
          class='register-center-identity-button_item button_item cursor'
          @click='submitSaveIdentityInfoFun("saveIdentityInfoForm")'>完成
        </div>
        <p
          v-if="$route.query.source !== 'meeting_3114'"
          class='againwrite fontSize16'
          @click='replaceIdentityHandler'>
          重新选择身份</p>
      </div>
      <!-- 医务工作者结束 -->
      <!-- 医学生开始 -->
      <div
        v-if='$store.state.identityInformation === 2'
        class='register-center-identity-box identity-authentication'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            基础信息
          </li>
        </ul>
        <el-form
          ref='saveIdentityInfoForm' :model='saveIdentityInfoForm' :rules='saveIdentityInfoRules'
          class='demoChoiceForm'
          status-icon>
          <el-form-item prop='realName'>
            <el-input v-model='saveIdentityInfoForm.realName' autocomplete='off' placeholder='请输入姓名' type='text'>
              <p slot='prepend' class='input-p'>姓名:</p>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model='saveIdentityInfoForm.company' autocomplete='off' placeholder='请输入学校名称'
              type='text'>
              <p slot='prepend' class='input-p'>学校:</p>
            </el-input>
          </el-form-item>
          <el-form-item prop='speciality'>
            <div>
              <div class='input-p-select'>亚专业:</div>
              <Select v-model='saveIdentityInfoForm.speciality' class='threeItme' placeholder='请选择亚专业'>
                <Option
                  v-for='item in specialitieList' :key='item.id' :label='item.name'
                  :value='item.id'></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item v-if='$store.state.accountType === "tel"' prop='email'>
            <el-input
              v-model='saveIdentityInfoForm.email' autocomplete='off' placeholder='请输入邮箱 (必填)'
              type='text'>
              <p slot='prepend' class='input-p'>邮箱:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='wechatpassword'>
            <el-input
              v-model='saveIdentityInfoForm.wechatpassword' autocomplete='off' placeholder='请输入密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>设置密码:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='confirmpassword'>
            <el-input
              v-model='saveIdentityInfoForm.confirmpassword' autocomplete='off' placeholder='请确认密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>确认密码:</p>
            </el-input>
          </el-form-item>
        </el-form>
        <p class='identity-authentication-tip fontSize12'>
          *该邮箱信息仅用于身份认证，不可作为登录账号使用
        </p>
        <div
          class='register-center-identity-button_item button_item cursor'
          @click='submitSaveIdentityInfoFun("saveIdentityInfoForm")'>完成
        </div>
        <p class='againwrite fontSize16' @click='replaceIdentityHandler'>
          重新选择身份</p>
      </div>
      <!-- 医学生结束 -->
      <!-- 企业人士开始 -->
      <div v-if='$store.state.identityInformation === 4' class='register-center-identity-box identity-authentication'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            基础信息
          </li>
        </ul>
        <el-form
          ref='saveIdentityInfoForm' :model='saveIdentityInfoForm' :rules='saveIdentityInfoRules'
          class='demoChoiceForm'
          status-icon>
          <el-form-item prop='realName'>
            <el-input v-model='saveIdentityInfoForm.realName' autocomplete='off' placeholder='请输入姓名' type='text'>
              <p slot='prepend' class='input-p'>姓名:</p>
            </el-input>
          </el-form-item>
          <el-form-item prop="typesOfEnterprisePersonnel">
            <div class="default_wrapper">
              <div class='input-p-select'>企业类型:</div>
              <radio-group
                :value="typesOfEnterprisePersonnel"
                @input="changeTypesOfEnterprisePersonnel"
              >
                <Radio :label="0">原厂/总代</Radio>
                <Radio :label="1">经销商及相关企业</Radio>
              </radio-group>
            </div>
          </el-form-item>
          <el-form-item
            :class="typesOfEnterprisePersonnel !== 0 && typesOfEnterprisePersonnel !== 1 ? 'activeCompanyWrapper' : ''"
            @click.native="activeCompanyHandler"
          >
            <div>
              <div class='input-p-select'>企业:</div>
              <Select
                v-model="saveIdentityInfoForm.company"
                filterable
                remote
                placeholder="请输入企业"
                :remote-method="remoteMethod"
                @change="changeCompanyHandler"
                :loading="identityType4loading">
                <Option
                  style="max-width: 396px"
                  class="text-limit-1"
                  v-for="item in companyOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name">
                </Option>
                <div
                  style="background: #ffffff;height: 50px;display: flex;align-items: center;justify-content: center;width: 100%">
                  <div style="font-size: 12px;margin-right: 15px">没有找到您想要的企业?</div>
                  <button
                    id="themeButton"
                    style="border: none;line-height: 25px;color: #FFFFFF;font-size: 14px"
                    @click="addCompanyHandler"
                  >添加企业
                  </button>
                </div>

                <p slot='prepend' class='input-p'>企业名称:</p>
                <div v-if="!identityType4loading" slot="empty"
                     style="color: #c0c4cc; text-align: center; line-height: 50px">
                  <p>暂无数据</p>
                  <div
                    style="background: #ffffff;height: 50px;display: flex;align-items: center;justify-content: center;width: 100%">
                    <div style="font-size: 12px;margin-right: 15px">没有找到您想要的企业?</div>
                    <button
                      id="themeButton"
                      style="border: none;line-height: 25px;color: #FFFFFF;font-size: 14px"
                      @click="addCompanyHandler"
                    >添加企业
                    </button>
                  </div>
                </div>
              </Select>
            </div>
          </el-form-item>
          <el-form-item
            :class="typesOfEnterprisePersonnel !== 0 && typesOfEnterprisePersonnel !== 1 ? 'activeCompanyWrapper' : ''"
            @click.native="activeCompanyHandler"
            prop="productLineName">
            <div>
              <div class='input-p-select'>产品线:</div>
              <Select
                v-model="saveIdentityInfoForm.productLineName"
                placeholder="请选择产品线"
                class="product_select_select"
                no-data-text="请先选择企业"
              >
                <Option
                  v-for="item in productLineNameList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item prop="department">
            <div>
              <div class='input-p-select'>部门:</div>
              <Select
                v-model="saveIdentityInfoForm.department"
                placeholder="请选择部门"
                @change="changeDepartment"
              >
                <Option
                  v-for="item in departmentList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                ></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item
            v-if="saveIdentityInfoForm.department === '销售部'"
            prop="salesArea">
            <el-input
              v-model="saveIdentityInfoForm.salesArea"
              placeholder="请输入销售大区"
            >
              <p slot='prepend' class='input-p'>销售大区:</p>
            </el-input>
          </el-form-item>

          <el-form-item v-if="typesOfEnterprisePersonnel === 1">
            <el-input
              v-model="saveIdentityInfoForm.proxyBrand"
              placeholder="请输入代理品牌"
            >
              <p slot='prepend' class='input-p'>代理品牌:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if="typesOfEnterprisePersonnel === 1">
            <div>
              <div class='input-p-select'>代理区域:</div>
              <Cascader
                v-model="saveIdentityInfoForm.proxyArea"
                :options="proxyAreaOptions"
                placeholder="请选择代理区域"
                clearable>
              </Cascader>
            </div>
          </el-form-item>

          <el-form-item prop='speciality'>
            <div>
              <div class='input-p-select'>亚专业:</div>
              <Select v-model='saveIdentityInfoForm.speciality' class='threeItme' placeholder='请选择亚专业'>
                <Option
                  v-for='item in specialitieList' :key='item.id' :label='item.name'
                  :value='item.id'></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item v-if='$store.state.accountType === "tel"' prop='email'>
            <el-input
              v-model='saveIdentityInfoForm.email' autocomplete='off' placeholder='请输入邮箱 (必填)'
              type='text'>
              <p slot='prepend' class='input-p'>邮箱:</p>
            </el-input>
          </el-form-item>


          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='wechatpassword'>
            <el-input
              v-model='saveIdentityInfoForm.wechatpassword' autocomplete='off' placeholder='请输入密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>设置密码:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='confirmpassword'>
            <el-input
              v-model='saveIdentityInfoForm.confirmpassword' autocomplete='off' placeholder='请确认密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>确认密码:</p>
            </el-input>
          </el-form-item>
        </el-form>
        <p class='identity-authentication-tip fontSize12'>
          <span v-if='$store.state.accountType === "tel"'>*该邮箱信息仅用于身份认证，不可作为登录账号使用</span>
        </p>
        <div
          class='register-center-identity-button_item button_item cursor'
          @click='submitSaveIdentityInfoFun("saveIdentityInfoForm")'>完成
        </div>
        <p class='againwrite fontSize16' @click='replaceIdentityHandler'>
          重新选择身份</p>
      </div>
      <!-- 企业人士结束 -->
      <!-- 科研人员开始 -->
      <div
        v-if='$store.state.identityInformation === 5'
        class='register-center-identity-box identity-authentication'>
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            基础信息
          </li>
        </ul>
        <el-form
          ref='saveIdentityInfoForm' :model='saveIdentityInfoForm' :rules='saveIdentityInfoRules'
          class='demoChoiceForm'
          status-icon>
          <el-form-item prop='realName'>
            <el-input v-model='saveIdentityInfoForm.realName' autocomplete='off' placeholder='请输入姓名' type='text'>
              <p slot='prepend' class='input-p'>姓名:</p>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model='saveIdentityInfoForm.company' autocomplete='off' placeholder='请输入单位名称'
              type='text'>
              <p slot='prepend' class='input-p'>单位名称:</p>
            </el-input>
          </el-form-item>
          <el-form-item prop='speciality'>
            <div>
              <div class='input-p-select'>亚专业:</div>
              <Select v-model='saveIdentityInfoForm.speciality' class='threeItme' placeholder='请选择亚专业'>
                <Option
                  v-for='item in specialitieList' :key='item.id' :label='item.name'
                  :value='item.id'></Option>
              </Select>
            </div>
          </el-form-item>
          <el-form-item v-if='$store.state.accountType === "tel"' prop='email'>
            <el-input
              v-model='saveIdentityInfoForm.email' autocomplete='off' placeholder='请输入邮箱 (必填)'
              type='text'>
              <p slot='prepend' class='input-p'>邮箱:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='wechatpassword'>
            <el-input
              v-model='saveIdentityInfoForm.wechatpassword' autocomplete='off' placeholder='请输入密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>设置密码:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='$route.query.wechat_perfect === "T"' prop='confirmpassword'>
            <el-input
              v-model='saveIdentityInfoForm.confirmpassword' autocomplete='off' placeholder='请确认密码 (必填)'
              type='password'>
              <p slot='prepend' class='input-p'>确认密码:</p>
            </el-input>
          </el-form-item>
        </el-form>
        <p class='identity-authentication-tip fontSize12'>
          *该邮箱信息仅用于身份认证，不可作为登录账号使用
        </p>
        <div
          class='register-center-identity-button_item button_item cursor'
          @click='submitSaveIdentityInfoFun("saveIdentityInfoForm")'>完成
        </div>
        <p class='againwrite fontSize16' @click='replaceIdentityHandler'>
          重新选择身份</p>
      </div>
      <!-- 科研人员结束 -->
    </div>
    <Confirm
      :close-button-flag="false"
      :visible="emptyConfirmFlag"
      grid-gap="20px"
      buttonName="切换"
      @confirm="emptyConfirmHandler"
      @cancelFn="emptyCancelHandler"
    >
      <template #content>
        <div style="width:100%;display: flex;flex-flow: column;justify-content: center;padding: 12px 0 30px">
          <p style="text-align: center">切换企业类型后将清空企业信息，</p>
          <p style="text-align: center">是否确认切换</p>
        </div>
      </template>
    </Confirm>
  </div>
</template>

<script>
import {Autocomplete, Checkbox, Option, Radio, Select, RadioGroup, Cascader} from 'element-ui'
import {
  addDepartmentMemberByUserId, getAllProvinceCityInfo,
  getBrandListByKeywords,
  getBrandProductLineListByBrandName, getCompanyDepartments, getDepartmentList, getShowHospitalList,
  saveCompanyToBrand, userInfo
} from "../../api/user";
import GetVerification from '@/components/GetVerification/GetVerification' // 获取验证码插件
import {
  emailRegister,
  getDepartments,
  getEmailCaptcha,
  getPhoneCaptcha,
  getSpecialities,
  getTitles,
  hospitalList,
  perfectIdentityInfo,
  phoneRegister,
  saveIdentityInfo,
  saveWeChatUserIdentityInfo
} from '@/api/register'
import validate from '@/validate/form-validate' // 表单验证
import Agreement from '@/components/PageComponents/Agreement/Agreement'
import Confirm from "../../components/optimize-components/UI/Confirm/index.vue";
import {pcTextArr} from "element-china-area-data";
import SliderVerification from "../../components/GetVerification/SliderVerification.vue";
import GetPhoneVerification from "../../components/GetVerification/GetPhoneVerification.vue";

export default {
  name: 'Index',
  components: {
    GetPhoneVerification,
    SliderVerification,
    Checkbox,
    Radio,
    Select
    , Option
    , GetVerification
    , Agreement
    , Autocomplete,
    RadioGroup,
    Confirm,
    Cascader
  },
  data() {
    const confirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback({tip: '请再次输入密码', isEnable: false})
      } else if (value !== this.saveIdentityInfoForm.wechatpassword) {
        callback({tip: '两次密码不一致', isEnable: false})
      } else {
        callback({isEnable: true})
      }
    }
    return {
      reloadSlider: false,
      sliderVerificationCode: {},
      proxyAreaOptions: [],
      emptyConfirmFlag: false,
      titleList: null,
      specialitieList: [],
      userName: null,
      hospitalDefaultList: [],
      departmentList: [],
      productLineNameList: [],
      companyOptions: [],
      queryCompany: "",
      identityType4loading: false,
      departmentOpt: [],
      loginType: 'account',
      passwordflag: false,
      loginSwitching: [
        {name: '账号登录', id: 1},
        {name: '验证码登录', id: 2}
      ],// 登录选择
      identityCurrent: 1,// 选择身份下标
      loginSwitchCurrent: 1,// 登录选择下标
      registerForm: {}, // 注册表单,
      saveIdentityInfoForm: {},// 完善信息表单
      typesOfEnterprisePersonnel: null, // 企业类型
      typesOfEnterprisePersonnelDefault: null, // 企业类型
      hospitalInfo: '',// 搜索医院
      saveIdentityInfoRules: {
        realName: [
          {validator: validate.loginformValidate().realName, trigger: 'blur'}
        ],
        email: [
          {validator: validate.loginformValidate().Email, trigger: 'blur'}
        ],
        wechatpassword: [
          {validator: validate.elformValidate().RegisterPaswword, trigger: 'blur'}
        ],
        confirmpassword: [
          {validator: confirmPassword, trigger: 'blur'}
        ],
        company: [
          {validator: validate.identityFormRules().hospital, trigger: 'blur'}
        ],
        department: [
          {validator: validate.identityFormRules().department, trigger: 'blur'}
        ],
        speciality: [
          {validator: validate.identityFormRules().speciality, trigger: 'blur'}
        ],
        title: [
          {validator: validate.identityFormRules().title, trigger: 'blur'}
        ],
        productLineName: [
          {validator: validate.identityFormRules().productLineName, trigger: 'blur'}
        ],
        salesArea: [
          {validator: validate.identityFormRules().salesArea, trigger: 'blur'}
        ],
      },// 完善信息表单验证
      registerRules: {
        phoneAndEmail: [
          {validator: validate.elformValidate().PhoneAndEmail, trigger: 'change'}
        ],
        password: [
          {validator: validate.elformValidate().RegisterPaswword, trigger: 'change'}
        ],
        agreement: [
          {validator: validate.elformValidate().Agreement, trigger: 'change'}
        ],
        captcha: [
          {validator: validate.elformValidate().Captcha, trigger: 'change'}
        ],
      },// 表单验证
      errorMessage: '', // 验证错误
      identitySelectionData: [
        {id: 1, name: 'Medicalwork', identityName: '医务工作者', identityContent: '医生、药师、护士'},
        {id: 2, name: 'Medicalstudents', identityName: '医学生', identityContent: '医学院在校学生'},
        {id: 4, name: 'Businesspeople', identityName: '企业人士', identityContent: '从事制药、医疗器械等工作'},
        {
          id: 5,
          name: 'Scientificresearchers',
          identityName: '科研人员',
          identityContent: '从事科学研究、科学实验等工作'
        }
      ],// 身份选择
      medicalWorkForm: {}, // 医务工作者表单
      medicalStudentsForm: {},// 医学生表单
      businessPeopleForm: {},// 企业人士表单
      scientificresearchersForm: {},// 科研人员表单
      registerFlag: true,// 注册
      identityInformation: '',// 选择身份信息
      selectDepartment: [],// 科室列表
      selectTitle: [],// 职称列表
      selectSpeciality: [],// 亚专业列表
      selectDepartmentOpt: []
    }
  },
  head() {
    return {
      title: '注册'
    }
  },
  watch: {},
  mounted() {
    this.getDataHandler()

    if (this.typesOfEnterprisePersonnel) {
      this.$axios.$request(getBrandListByKeywords({
        keywords: '',
        typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.companyOptions = res.list
        }
      })
    }

    this.$axios.$request(getCompanyDepartments()).then(res => {
      if (res.code === 1) {
        this.departmentList = res.list;
      }
    })

    this.$axios.$request(getShowHospitalList()).then(res => {
      if (res.code === 1) {
        this.hospitalDefaultList = res.list
      }
    })

    if (this.$store.state.identityInformation === "SelectIdentity" && this.$store.state.auth.token) {

      this.$store.commit('editIdentityInformationFun', this.$store.state.auth.user.identity)
      this.$axios.$request(userInfo()).then(res => {
        if (res.code === 1) {
          const data = res.result

          // 切换身份


          let userObj = {}

          if (this.$store.state.identityInformation === 1) {
            userObj = {
              realName: data.realName,
              company: data.company,
              department: data.departmentList,
              speciality: data.specialityList && data.specialityList.length > 0 ? data.specialityList[0].id : null,
              title: data.title.split(','),
            }
          }

          if (this.$store.state.identityInformation === 2) {
            userObj = {
              realName: data.realName,
              company: data.company,
              speciality: data.specialityList && data.specialityList.length > 0 ? data.specialityList[0].id : null,
            }
          }

          if (this.$store.state.identityInformation === 4) {
            userObj = {
              realName: data.realName,
              company: data.company,
              productLineName: data.productLineName,
              department: data.department,
              salesArea: data.salesArea,
              speciality: data.specialityList && data.specialityList.length > 0 ? data.specialityList[0].id : null,
            }
          }

          if (this.$store.state.identityInformation === 5) {
            userObj = {
              realName: data.realName,
              company: data.company,
              speciality: data.specialityList && data.specialityList.length > 0 ? data.specialityList[0].id : null,
            }
          }

          this.saveIdentityInfoForm = JSON.parse(JSON.stringify(userObj))
        }
      })
    }

  },
  methods: {
    finishSlide(data) {
      this.sliderVerificationCode = {
        sessionId: data.sessionId,
        sig: data.sig,
        token: data.token,
      }
      this.reloadSlider = false
    },
    activeCompanyHandler() {
      if (this.typesOfEnterprisePersonnel !== 0 && this.typesOfEnterprisePersonnel !== 1) {
        this.$toast('请先选择企业类型')
      }
    },
    emptyConfirmHandler(params, callBackFn) {
      this.typesOfEnterprisePersonnel = this.typesOfEnterprisePersonnelDefault
      callBackFn(false)
      this.emptyConfirmFlag = false;
      this.$axios.$request(getBrandListByKeywords({
        keywords: '',
        typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.companyOptions = res.list
        }
      })
      this.$set(this.saveIdentityInfoForm, 'proxyBrand', "")
      this.$set(this.saveIdentityInfoForm, 'proxyArea', [])
      this.$set(this.saveIdentityInfoForm, 'company', "")
      this.$set(this.saveIdentityInfoForm, 'productLineName', "")
      this.$set(this.saveIdentityInfoForm, 'department', "")
      this.$set(this.saveIdentityInfoForm, 'salesArea', "")
      this.productLineNameList = []
    },
    emptyCancelHandler() {
      this.emptyConfirmFlag = false;
    },
    changeTypesOfEnterprisePersonnel(value) {
      this.typesOfEnterprisePersonnelDefault = value
      this.emptyConfirmFlag = true
    },
    async getDataHandler() {
      const [titles, specialities, allProvinceCityInfo] = await Promise.all([
        this.$axios.$request(getTitles()),
        this.$axios.$request(getSpecialities()),
        this.$axios.$request(getAllProvinceCityInfo())
      ])

      if (titles.code === 1) {
        this.titleList = titles.result
      }

      if (specialities.code === 1) {
        this.specialitieList = specialities.list
      }

      if (allProvinceCityInfo.code === 1) {
        this.proxyAreaOptions = allProvinceCityInfo.list.map(item => {
          return {
            value: item.province,
            label: item.province,
            children: ["北京市", "天津市", "上海市", "重庆市"].includes(item.province) ? [
              {
                value: item.province,
                label: item.province,
              }
            ] : [{value: "全部", label: "全部"}].concat(item.cityList.map(itemCity => {
              return {
                value: itemCity.city,
                label: itemCity.city,
              }
            }))
          }
        })
      }
    },
    replaceIdentityHandler() {
      this.$store.commit("editIdentityInformationFun", "SelectIdentity")
      this.saveIdentityInfoForm = {}
    },
    changeDepartment() {
      this.$set(this.saveIdentityInfoForm, 'salesArea', null)
    },
    selectCompanyHandler(item) {
      this.saveIdentityInfoForm.department = [];
      this.$axios.$request(getDepartmentList({
        hospitalName: item.name
      })).then(res => {
        if (res.code === 1) {
          this.selectDepartmentOpt = res.list.map((item, index) => {
            return {
              id: index,
              name: item
            }
          })
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-09-21 15:46
     * 远程搜索企业
     * ------------------------------------------------------------------------------
     */
    remoteMethod(query) {
      this.queryCompany = query;
      if (query !== '') {
        this.identityType4loading = true;
        // eslint-disable-next-line no-undef
        this.$axios.$request(getBrandListByKeywords({
          keywords: query,
          typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
        })).then(res => {
          if (res.code === 1) {
            this.identityType4loading = false;
            this.companyOptions = res.list;

            // this.$axios.$request(getBrandProductLineListByBrandName({
            //   brandName: query,
            //   typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
            // })).then(res => {
            //   if (res.code === 1) {
            //     this.productLineNameList = res.list
            //   }
            // })
          }
        })
      } else {
        this.companyOptions = [];
      }
    },
    addCompanyHandler() {
      this.$axios.$request(saveCompanyToBrand({
        company: this.queryCompany,
        typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.companyOptions.push({
            id: new Date().getTime(),
            name: this.queryCompany
          })
          this.saveIdentityInfoForm.company = this.queryCompany
          this.changeCompanyHandler(this.queryCompany)
        }
      })
    },
    changeCompanyHandler(params) {
      this.$set(this.saveIdentityInfoForm, 'productLineName', null)
      this.$axios.$request(getBrandProductLineListByBrandName({
        brandName: params,
        typesOfEnterprisePersonnel: this.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.productLineNameList = res.list
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-09-20 17:22
     * 切换职称
     * ------------------------------------------------------------------------------
     */
    changeTitleHandler(params) {
      let listA = []
      let listB = []
      let listC = []

      params.forEach(itemParams => {
        for (const key in this.titleList) {
          this.titleList[key].forEach((item, index) => {
            if (key === "listA" && itemParams === item.name) {
              listA = [itemParams]
            }

            if (key === "listB" && itemParams === item.name) {
              listB = [itemParams]
            }

            if (key === "listC" && itemParams === item.name) {
              listC = [itemParams]
            }

          })
        }
      })
      this.saveIdentityInfoForm.title = listA.concat(listB, listC)
    },
    /**
     * 输入医院进行搜索
     * @param queryString
     * @param cb
     */
    querySearch(queryString, cb) {
      if (queryString) {
        this.$axios.$request(hospitalList({
          hospital: queryString
        })).then(res => {
          if (res && res.code === 1) {
            const restaurants = res.list
            // 调用 callback 返回建议列表的数据
            cb(restaurants)
          }
        })
      } else {
        cb(this.hospitalDefaultList)
      }
    },
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    },
    // 注册
    registerFun(formName) {
      this.$analysys.btn_click('注册', document.title)
      // 为了按顺序验证
      const storage = []
      let flag = true
      this.$refs[formName].validateField(['captcha', 'phoneAndEmail', 'password', 'agreement'], (valid) => {
        storage.push(valid)
      })
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false
          this.$toast.fail(storage[i].tip)
          return
        }
      }
      // 全部通过 or 某个没过
      if (flag) {
        this.$store.commit('editregisterPasswordFun', this.registerForm.password)
        switch (this.$store.state.accountType) {
          case 'tel':
            this.$axios.$request(phoneRegister({
              phone: this.registerForm.phoneAndEmail,
              password: this.registerForm.password,
              captcha: this.registerForm.captcha
            })).then(res => {
              if (res && res.code === 1) {
                /**
                 * 通过验证码验证后,然后去完善信息
                 */
                this.userName = this.registerForm.phoneAndEmail
                this.$store.commit('auth/setUserFn', {
                  user: {username: this.registerForm.phoneAndEmail}
                })

                if (this.$route.query.source === 'meeting_3114') {
                  this.$store.commit('editIdentityInformationFun', 1)// 到身份选择页面
                } else {
                  this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                }

              }
            })
            break
          default:
            this.$axios.$request(emailRegister({
              email: this.registerForm.phoneAndEmail,
              password: this.registerForm.password,
              captcha: this.registerForm.captcha
            })).then(res => {
              /**
               * 通过验证码验证后,然后去完善信息
               */
              if (res && res.code === 1) {
                this.userName = this.registerForm.phoneAndEmail
                this.$store.commit('auth/setUserFn', {
                  user: {username: this.registerForm.phoneAndEmail}
                })

                if (this.$route.query.source === 'meeting_3114') {
                  this.$store.commit('editIdentityInformationFun', 1)// 到身份选择页面
                } else {
                  this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                }
              }
            })
            break
        }
      }
    },
    // 完善个人信息
    submitSaveIdentityInfoFun(formName) {
      this.analysysFun('完善信息')
      const storage = []
      let flag = true
      this.$refs[formName].validateField(['realName', 'email', 'wechatpassword', 'confirmpassword'], (valid) => {
        storage.push(valid)
      })
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false
          this.$toast(storage[i].tip)
          return
        }
      }


      let department = "";
      if (this.saveIdentityInfoForm.department && this.saveIdentityInfoForm.department.length && typeof this.saveIdentityInfoForm.department === 'object') {
        this.saveIdentityInfoForm.department.forEach((item) => {
          department += item + ",";
        });
        department = department.substring(0, department.length - 1);
      } else if (this.saveIdentityInfoForm.department && typeof this.saveIdentityInfoForm.department === 'string') {
        department = this.saveIdentityInfoForm.department
      }

      let title = "";
      if (this.saveIdentityInfoForm.title && this.saveIdentityInfoForm.title.length) {
        this.saveIdentityInfoForm.title.forEach((item) => {
          title += item + ",";
        });
        title = title.substring(0, title.length - 1);
      }

      let departmentCode = ""
      if (this.$store.state.identityInformation === 4) {
        const departmentArr = this.departmentList.filter(item => item.name === this.saveIdentityInfoForm.department)
        if (departmentArr.length > 0) {
          departmentCode = departmentArr[0].code
        }
      }

      const str = validate.identityFormVerification(this.$store.state.identityInformation, {
        username: this.$store.state.auth.user.username,
        identity: this.$store.state.identityInformation,
        realName: this.saveIdentityInfoForm.realName,
        email: this.$store.state.accountType === 'tel' ? this.saveIdentityInfoForm.email : this.$store.state.accountType,
        typesOfEnterprisePersonnel: this.$store.state.identityInformation === 4 ? this.typesOfEnterprisePersonnel : null,
        company: this.saveIdentityInfoForm.company,
        proxyBrand: this.saveIdentityInfoForm.proxyBrand,
        proxyArea: this.saveIdentityInfoForm.proxyArea ? this.saveIdentityInfoForm.proxyArea.join(',') : null,
        department,
        title,
        specialityIds: this.saveIdentityInfoForm.speciality,
        // 产品线
        productLineName: this.saveIdentityInfoForm.productLineName,
        // 销售大区
        salesArea: this.saveIdentityInfoForm.salesArea,
        departmentCode
      })

      if (str) {
        this.$toast(str)
        return;
      }

      // 通过验证并且未登录 注册  (正常注册)
      if (flag && !this.$store.state.auth.token && !this.$route.query.wechat_perfect) {
        this.$axios.$request(saveIdentityInfo({
          username: this.userName,
          password: this.$store.state.registerPassword,
          identity: this.$store.state.identityInformation,
          realName: this.saveIdentityInfoForm.realName,
          email: this.$store.state.accountType === 'tel' ? this.saveIdentityInfoForm.email : this.$store.state.auth.user.username,
          typesOfEnterprisePersonnel: this.$store.state.identityInformation === 4 ? this.typesOfEnterprisePersonnel : null,
          company: this.saveIdentityInfoForm.company,
          proxyBrand: this.saveIdentityInfoForm.proxyBrand,
          proxyArea: this.saveIdentityInfoForm.proxyArea ? this.saveIdentityInfoForm.proxyArea.join(',') : null,
          department,
          title,
          specialityIds: this.saveIdentityInfoForm.speciality,
          // 产品线
          productLineName: this.saveIdentityInfoForm.productLineName,
          // 销售大区
          salesArea: this.saveIdentityInfoForm.salesArea,
          departmentCode
        })).then((res) => {
          if (res && res.code === 1) {
            this.$axios.$request(addDepartmentMemberByUserId());
            this.$store.commit('auth/registerLogin', {
              token: res.result.token,
              user: res.result.user,
              this: this
            })
          } else {
            /**
             * 埋点
             */
            this.$analysys.register(this.$store.state.accountType === 'tel' ? '手机号注册' : '邮箱注册', null, false)
          }
        })
        // 通过验证并且已经登录   (已经登录了, 来完成个人信息的)
      } else if (flag && this.$store.state.auth.token && !this.$route.query.wechat_perfect) {
        this.$axios.$request(perfectIdentityInfo({
          username: this.$store.state.auth.user.username,
          identity: this.$store.state.identityInformation,
          realName: this.saveIdentityInfoForm.realName,
          email: this.$store.state.accountType === 'tel' ? this.saveIdentityInfoForm.email : this.$store.state.accountType,
          typesOfEnterprisePersonnel: this.$store.state.identityInformation === 4 ? this.typesOfEnterprisePersonnel : null,
          company: this.saveIdentityInfoForm.company,
          proxyBrand: this.saveIdentityInfoForm.proxyBrand,
          proxyArea: this.saveIdentityInfoForm.proxyArea ? this.saveIdentityInfoForm.proxyArea.join(',') : null,
          department,
          title,
          specialityIds: this.saveIdentityInfoForm.speciality,
          // 产品线
          productLineName: this.saveIdentityInfoForm.productLineName,
          // 销售大区
          salesArea: this.saveIdentityInfoForm.salesArea,
          departmentCode
        })).then((res) => {
          if (res && res.code === 1) {
            this.$axios.$request(addDepartmentMemberByUserId());
            /**
             * 埋点
             */
            this.$analysys.register(this.$store.state.accountType === 'tel' ? '手机号注册' : '邮箱注册', null, false)
            // 将token和 userid 保存到cookies中 方便在服务器端渲染时可以拿到
            this.$toast({
              message: '保存成功',
              type: 'success',
              duration: 1000,
              onClose: () => {
                this.$router.replace({path: '/'})
              }
            })
          } else {
            /**
             * 埋点
             */
            this.$analysys.register(this.$store.state.accountType === 'tel' ? '手机号注册' : '邮箱注册', null, false)
          }
        })
        /**
         * 通过验证 并且是微信登录完善信息
         */
      } else if (flag && this.$route.query.wechat_perfect === 'T') {
        this.$axios.$request(saveWeChatUserIdentityInfo({
          username: this.$route.query.phone,
          password: this.saveIdentityInfoForm.wechatpassword,
          identity: this.$store.state.identityInformation,
          realName: this.saveIdentityInfoForm.realName,
          email: this.$store.state.accountType === 'tel' ? this.saveIdentityInfoForm.email : this.$store.state.accountType,
          typesOfEnterprisePersonnel: this.$store.state.identityInformation === 4 ? this.typesOfEnterprisePersonnel : null,
          company: this.saveIdentityInfoForm.company,
          proxyBrand: this.saveIdentityInfoForm.proxyBrand,
          proxyArea: this.saveIdentityInfoForm.proxyArea ? this.saveIdentityInfoForm.proxyArea.join(',') : null,
          department,
          title,
          specialityIds: this.saveIdentityInfoForm.speciality,
          unionid: this.$store.state.auth.unionid,
          nickname: this.$store.state.auth.weChatInfo.nickname,
          // 产品线
          productLineName: this.saveIdentityInfoForm.productLineName,
          // 销售大区
          salesArea: this.saveIdentityInfoForm.salesArea,
          departmentCode
        })).then((res) => {
          if (res && res.code === 1) {
            this.$axios.$request(addDepartmentMemberByUserId());
            this.$store.commit('auth/registerLogin', {
              token: res.result.token,
              user: res.result.user,
              this: this
            })
          } else {
            /**
             * 埋点
             */
            this.$analysys.register(this.$store.state.accountType === 'tel' ? '手机号注册' : '邮箱注册', null, false)
          }
        })
      }
    },
    // 选择身份信息
    selectidentityFun() {
      const current = this.identitySelectionData.findIndex(item => item.id === this.identityCurrent)
      this.analysysFun(this.identitySelectionData[current].identityName)
      this.$store.commit('editIdentityInformationFun', this.identitySelectionData[current].id)// 找到下标后取出名字给到此变量
      Promise.all([this.$axios.$request(getDepartments()), this.$axios.$request(getTitles()), this.$axios.$request(getSpecialities())]).then(res => {
        const arrayselect = [
          res[0].list,
          res[1].result,
          res[2].list
        ]
        this.$store.commit('editSeletIdentityFun', arrayselect)
      })
    },
    // 获取验证码
    getValidationFun(item, callback) {
      this.reloadSlider = false;
      this.$analysys.btn_click('获取验证码', document.title)
      this.$store.commit('editAccountTypeFun', item)
      if (item === 'tel') {
        this.$axios.$request(getPhoneCaptcha({
          phone: this.registerForm.phoneAndEmail,
          sessionId: this.sliderVerificationCode.sessionId,
          sig: this.sliderVerificationCode.sig,
          token: this.sliderVerificationCode.token
        })).then(res => {
          if (res && res.code === 1) {
            return callback(true)
          } else {
            return callback(false)
          }
        })
      } else if (item === 'email') {
        this.$axios.$request(getEmailCaptcha({
          email: this.registerForm.phoneAndEmail
        })).then(res => {
          if (res && res.code === 1) {
            return callback(true)
          } else {
            return callback(false)
          }
        })
      }
    }
  }
}
</script>
<style lang="less">
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0;
  right: 0;
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

.el-cascader-panel .el-cascader-menu__wrap {
  height: 204px
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

.personalInformationForm_department_select .el-select-dropdown__item {
  height: 44px;
  line-height: 44px;
  display: flex;
  align-items: center;
}

.personalInformationForm_department_select .el-select-dropdown__item::after {
  display: none;
}

.personalInformationForm_department_select .selected {
  color: var(--theme-color);
  font-weight: unset;
}

.personalInformationForm_department_select .selected .select_icon {
  display: block !important;
}

.personalInformationForm_department_select .selected .not_select_icon {
  display: none !important;
}
</style>
<style lang='less' scoped>
@import "./index";
</style>
