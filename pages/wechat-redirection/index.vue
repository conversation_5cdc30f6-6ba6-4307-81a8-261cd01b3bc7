<template>
  <div></div>
</template>

<script>
export default {
  name: "WeChatRedirection",
  mounted() {
    const _this = this;

    if (_this.$route.query.code && _this.$route.query.source === 'questions') {
      _this.$store.dispatch('auth/wxQuestionsLogin',_this)
    } else if (_this.$route.query.code && !_this.$route.query.wechat_login) {
      (async () => {
        await _this.$store.dispatch('auth/wxLogin', _this)
        /**
         * 有倒计时的话 代表用户是记住密码了,  失效时间  - 当前时间 = 倒计时
         * 如果 失效时间 < 6s 直接退出
         * 如果失效时间 小于0 直接退出
         * 每次进入页面 监听 3 t
         */
        if (_this.$cookies.get('medtion_expirationTime_only_sign')) {
          let timeout = _this.$cookies.get('medtion_expirationTime_only_sign') - Date.now()
          // eslint-disable-next-line no-unused-expressions
          isNaN(timeout) ? timeout = null : null
          if (timeout < 6000) {
            _this.$toast('登录失效,请重新登录')
            _this.$store.commit('auth/logout', {this: _this})
            return
          }
          // eslint-disable-next-line no-undef
          _this.timeAuth = setTimeout(() => {
            _this.$toast('登录失效,请重新登录')
            // 判断timeout的值
            _this.$store.commit('auth/logout', {this: _this})
          }, timeout)
        }
      })()
    }
  }
}
</script>

<style scoped>

</style>
