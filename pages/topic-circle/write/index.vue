<template>
  <div id="edit">
    <div class="write">
      <input v-model="contentTitle" class="editTitle" :class="$route.query.id?'answer':''" type="text" :placeholder="placeholder" maxlength="50"
             :readonly="isReadonly">
      <ul v-if="$route.query.id || $route.query.anid" class="tag" @click="isShowEditorQuestion_fn">
        <li>
          查看问题详情
          <i class="el-icon-arrow-right"></i>
        </li>
      </ul>
      <vue-ueditor-wrap v-model="content"
                        editor-id="editor"
                        :config="editorConfig"
                        :editorDependencies="['topic_circle_ueditor.config.js','ueditor.all.js']"
                        style="min-height: 80vh"
      />
      <div class="setting" ref="setting">
        <ul>
          <li v-if="isQuestion">
            <span class="required">所属话题</span>
            <dl v-if="topicList.length" class="topicListDom">
              <dd v-for="(item,index) in topicList" :key="index">
                <img @click="removeTopicList_fn(item)" src="~/assets/images/topic_circle/icon_close.png" alt="" />
                #{{item.title}}
              </dd>
            </dl>
            <div v-if="topicList.length<5" @click="addTopic_fn" class="btn">
              请添加话题
            </div>
          </li>
          <li v-if="isQuestion">
            <span>所属圈子</span>
            <dl v-if="circleList.length" class="topicListDom">
              <dd v-for="(item,index) in circleList" :key="index">
                <img @click="removeCircleList_fn(item)" src="~/assets/images/topic_circle/icon_close.png" alt="">
                {{item.title}}
              </dd>
            </dl>
            <div v-else @click="addCircle_fn" class="btn">请添加圈子</div>
          </li>
          <li>
            <span>匿名设置</span>
            <div>
              <el-radio v-model="isAnonymity" label="0">不启用匿名</el-radio>
              <el-radio v-model="isAnonymity" label="1">启用匿名</el-radio>
              <p class="msg">仅认证用户可使用匿名功能</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 底部导航 -->
    <div class="bottomMenu">
      <div class="main">
        <div class="settingBtn" @click="onBacktop_fn">
          {{isBackTop?'返回顶部':'发布设置'}}
          <i  :class='isBackTop?"el-icon-arrow-up":"el-icon-arrow-down"' style='margin-left: 5px'></i>
        </div>
        <div @click="isAuth_fn" class="sendBtn active">
          发布
        </div>
      </div>
    </div>

    <AddTopic v-if="showTopicBox" :topicList="topicList"/>
    <AddCircle v-if="showCircleBox" :topicList="circleList"/>
    <Identification v-if="isIdentificationAlert" @isAnonymityAlert="isAnonymityAlert_fn"/>
    <Anonymity v-if="isAnonymityAlert"/>
    <Audit v-if="auditAlert"/>
    <!-- 图片编辑 -->
    <ImgEditor v-if="$store.state.editor.showImgEditUrl" />
    <!-- 文件导入 -->
    <ImportFile v-if="$store.state.editor.showImportFile"/>
    <!-- 查看问题详情 -->
    <EditorQuestion v-if="$store.state.topicCircle.isShowEditorQuestion"/>
    <!-- 短视频弹框 -->
    <ShortVideoPlayback
      :video-id='$store.state.topicCircle.topicCircleHomeShortVideoId'
      :visible='$store.state.topicCircle.topicCircleHomeShortVisible'
      @cancelFn='(flag) => $store.commit("topicCircle/setTopicCircleHomeShortVisible",flag)'
    />
    <!-- 视频导入 -->
    <ImportVideo v-if="$store.state.editor.showImportVideo"/>
  </div>
</template>
<script>
  import VueUeditorWrap from 'vue-ueditor-wrap'
  import qs from 'qs'
  import AddTopic from '@/components/optimize-components/TopicCircle/AddTopic'
  import AddCircle from '@/components/optimize-components/TopicCircle/AddCircle'
  import Identification from '@/components/optimize-components/TopicCircle/Identification'
  import Anonymity from '@/components/optimize-components/TopicCircle/Anonymity'
  import Audit from '@/components/optimize-components/TopicCircle/Audit'
  import ImgEditor from '@/components/optimize-components/editor/ImgEditor'
  import ImportFile from '@/components/optimize-components/editor/ImportFile'
  import EditorQuestion from '@/components/optimize-components/TopicCircle/EditorQuestion'
  import ShortVideoPlayback from '@/components/optimize-components/public/ShortVideoPlayback'
  import ImportVideo from '@/components/optimize-components/editor/ImportVideo'
  import {
    saveQa,
    getUserPublishAbility,
    getCommunityAnswerParent,
    updateQa,
    getQuestionAnswerDetail,
    getWebApiPersonalWebsite,
  } from '@/api/topic-circle'
  import { userInfo } from '@/api/user'

  // url参数描述
  // id : 问题id
  // quid : 重新编辑问题的id
  // anid : 二次编辑的回答的问题id
  // activity : 默认选中的活动id
  // topic : 默认选中的话题id
  // circle : 默认选中的圈子id
  export default {
    name: 'index',
    head() {
      return {
        title: this.$route.query.id?'回答-话题圈子-脑医汇':'提问-话题圈子-脑医汇',
        meta: [
          {
            hid: 'description',
            name: 'description',
            content: ''
          },
          {
            hid: 'keywords',
            name: 'keywords',
            content: '脑医汇,神外资讯,神介资讯,神内资讯,话题圈子,用户社区'
          }
        ],
        timer: null,
        script: [
          {src: '/js/aliyun-upload-sdk/lib/es6-promise.min.js'},
          {src: '/js/aliyun-upload-sdk/lib/aliyun-oss-sdk-6.17.1.min.js'},
          {src: '/js/aliyun-upload-sdk/aliyun-upload-sdk-1.5.6.min.js'}
        ]
      }
    },
    components: {
      VueUeditorWrap,
      AddTopic,
      AddCircle,
      Identification,
      Anonymity,
      Audit,
      ImgEditor,
      ImportFile,
      EditorQuestion,
      ShortVideoPlayback,
      ImportVideo
    },
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      const userAgent = req ? req.headers['user-agent'] : ""
      let TerminalType = null;
      if (userAgent.includes('BrainMedPC_Win')) {
        TerminalType = "BrainMedPC_Win"
      }
      if (userAgent.includes('BrainMedPC_Mac')) {
        TerminalType = "BrainMedPC_Mac"
      }

      store.commit('cs/setTerminalType', TerminalType)
    },
    computed: {
      getEditTitle_cpu() {
        if (this.isAnswer) {
          return window.sessionStorage.getItem('editTitle' + this.$route.query.id)
        }
      }
    },
    data() {
      return {
        contentTitle: '', // 富文本标题
        isReadonly: false, // 是否为创建问题
        content: '', // 富文本内容
        editorConfig: {
          // 后端服务
          serverUrl: '',
          // 配置UEditorPlus的静态资源
          UEDITOR_HOME_URL: '/ueditor/',
          initialContent: '<p class="initPlaceholder" style="color: #999;">请输入内容</p>',
          autoClearinitialContent: true
        },
        placeholder: '请输入标题 (2-50字)',
        isAnonymity: '0', // 是否启用匿名
        showTopicBox: false, // 添加话题弹窗开关
        showCircleBox: false, // 添加圈子弹窗开关
        isIdentificationAlert: false, // 认证弹窗 仍要发布
        isAnonymityAlert: false, // 认证弹窗 匿名
        auditAlert: false,
        topicList: [], // 选择的话题列表
        circleList: [], // 选择的圈子列表
        isAnswer: false, // 是否是回答
        isQuestion: false, // 是否是问题
        answerInfo: {}, // 二次编辑回答详情
        isAuth: null, // 认证状态
        isBackTop: false,
      }
    },
    watch:{
      content:{
        handler(n,o){
          if(/initPlaceholder/.test(n)) return;
          if(!this.$route.query.quid && !this.$route.query.anid){
            // 存本地草稿
            window.localStorage.setItem('brainmedUedit',this.content)
          }
        }
      },
      isAnonymity:{
        handler(n,o){
          if(n === '1'){
            this.isAnonymityAlert_fn();
          }
        },
        deep: true
      },
    },
    methods: {
      onBacktop_fn() {
        // this.$tool.scrollIntoTop()
        if(this.isBackTop){
          this.ScrollTop_fn(0,200);
        }else{
          let top = document.querySelector('#editor').getBoundingClientRect().height + 180;
          this.ScrollTop_fn(top,300);
        }
      },
      // 滚动动画
      ScrollTop_fn(number = 0, time){
        if (!time) {
          document.body.scrollTop = document.documentElement.scrollTop = number;
          return number;
        }
        const spacingTime = 10; // 设置循环的间隔时间  值越小消耗性能越高
        let spacingInex = time / spacingTime; // 计算循环的次数
        let nowTop = document.body.scrollTop + document.documentElement.scrollTop; // 获取当前滚动条位置
        let everTop = (number - nowTop) / spacingInex; // 计算每次滑动的距离
        let scrollTimer = setInterval(() => {
          if (spacingInex > 0) {
            spacingInex--;
            this.ScrollTop_fn(nowTop += everTop);
          } else {
            clearInterval(scrollTimer); // 清除计时器
          }
        }, spacingTime);
      },
      // 隐藏弹窗
      hideAlert_fn() {
        this.showTopicBox = false
        this.showCircleBox = false
      },
      // 请添加话题
      addTopic_fn() {
        this.showTopicBox = !this.showTopicBox
      },
      // 视频上传
      showImportVideo(fileDom){
        this.$store.commit('editor/setShowImportVideo', true) // 保存编辑图片的路径
      },
      // 请添加圈子
      addCircle_fn() {
        this.showCircleBox = !this.showCircleBox
      },
      // 富文本输入
      insertEditor(e) {
        if (e.key === 'editor') {
          window.UEditor.execCommand('insertHTML', JSON.parse(e.newValue).html, true)
          window.UEditorDialog.close(false)
        }
      },
      // 向话题列表添加
      removeTopicList_fn(item) {
        item.active = false
        this.topicList.map((v, i) => {
          if (item.id === v.id) {
            this.topicList.splice(i, 1)
            this.$toast('删除成功')
          }
        })
      },
      removeCircleList_fn(item) {
        item.active = false
        this.circleList.map((v, i) => {
          if (item.id === v.id) {
            this.circleList.splice(i, 1)
            this.$toast('删除成功')
          }
        })
      },
      // 发布
      isAuth_fn() {
        // 0：未认证；1：已认证；2：待认证 ； 3：认证未过
        // 问题
        if (this.isAuth === '1') {
          this.sendEdit_fn()
        } else {
          // 显示未认证弹窗
          this.isIdentificationAlert = true
        }
      },
      // 是否匿名 未认证
      isAnonymityAlert_fn() {
        if (this.isAuth === '1') {

        }else{
          this.isAnonymityAlert = true
        }
      },
      /**
       * 是否审核通过
       * 判断是否有上一篇发布，上一篇发布是否审核通过, 数据取最近的，所以取第一条数据判断是否审核通过
       * */
      getUserPublishQaPage_fn() {
        if (!this.$store.state.auth.user.id) {
          this.$router.push(`/signin?fallbackUrl=${this.$route.fullPath}`)
          return
        }
        this.$axios.$request(
          getUserPublishAbility({
            userId: this.$store.state.auth.user.id
          })
        ).then(res => {
          // console.log('审核', res)
          if (res.code === 1) {
            if (res.result) {
              this.sendEdit_fn()
            }else{
              this.auditAlert = true;
            }
          }
        })
      },
      // 发布到服务器
      sendEdit_fn() {
        if (!this.contentTitle) {
          this.$toast('请输入标题')
          return
        }else if(this.contentTitle.length<2){
          this.$toast('标题限制在2-50字');
          return
        }else if(this.contentTitle.length > 50){
          this.$toast('标题限制在2-50字');
          return
        }
        if(this.isAnswer){
          if (!this.content
            || /请输入详细内容\(选填\)/.test(this.content)
            || /<p class="initPlaceholder" style="color: #999;">/.test(this.content)) {
            this.$toast('请填写内容')
            return
          }
        }
        if (this.isQuestion) {
          if (!this.topicList.length) {
            this.$toast('请选择话题')
            return
          }
        }

        if(this.$route.query.anid || this.$route.query.quid){
          // 更新问答
          this.updateQa_fn();
        }else{
          // 创建问答
          this.createQa_fn();
        }
      },
      // 创建问答
      createQa_fn(){
        var communityIdStr = []
        // 话题
        this.topicList.map(v => {
          communityIdStr.push(v.id)
        })
        // 圈子
        this.circleList.map(v => {
          communityIdStr.push(v.id)
        })
        // 活动
        if(this.$route.query.activity){
          communityIdStr.push(this.$route.query.activity)
        }
        let data = {
          text: this.content
            // 去掉pc的特征，改成App认识的样式
            .replace(/"imageDiv contenteditable"/g,'"imageDiv"') // 替换图片类名，添加contenteditable
            .replace(/"video_box contenteditable"/g,'"video_box"') // 替换视频类名，添加contenteditable
            .replace(/\/ueditor\/themes\/default\/myImages\/icon_edit_img_delete\.png/g,'file:\/\/\/android_asset\/html_delete\.png')
            .replace(/\/ueditor\/themes\/default\/myImages\/icon_edit_img\.png/g,'file:\/\/\/android_asset\/html_edit\.png'),
          type: (this.isAnswer ? 'A' : 'Q'),
          questionId: this.$route.query.id?this.$route.query.id:this.$route.query.anid,
          isHideCreator: (this.isAnonymity==='1'?true:false),
          communityIdStr: communityIdStr.join(','),
          title: this.contentTitle,
        }
        const jsonData = qs.stringify(data)
        this.$axios.$request(
          saveQa(jsonData)
        ).then(res => {
          if (res.code === 1) {
            if (this.$route.query.id) {
              // 新建回答
              this.$analysys.publish({
                creator_id: this.$store.state.auth.user.id + '',
                creator_name: this.$store.state.auth.user.realName,
                publish_type : '回答',
                publish_title : this.contentTitle,
              })
              window.location.href = `/topic-circle/questionhome?id=${this.$route.query.id}`
            } else {
              // 新建问题
              this.$analysys.publish({
                creator_id: this.$store.state.auth.user.id + '',
                creator_name: this.$store.state.auth.user.realName,
                publish_type : '提问',
                publish_title : this.contentTitle,
              })
              window.location.href = `/topic-circle/questionhome?id=${res.result.id}`
            }
            if(!this.$route.query.quid && !this.$route.query.anid){
              // 删除本地草稿
              window.localStorage.removeItem('brainmedUedit');
            }
          } else {
            this.$toast(res.message)
          }
        })
      },
      // 更新问答
      updateQa_fn(){
        var communityIdStr = []
        this.topicList.map(v => {
          communityIdStr.push(v.id)
        })
        this.circleList.map(v => {
          communityIdStr.push(v.id)
        })
        let data = {
          qaId: this.$route.query.anid?this.$route.query.anid:this.$route.query.quid,
          userId: this.$store.state.auth.user.id,
          title: this.contentTitle,
          text: this.content
            // 去掉pc的特征，改成App认识的样式
            .replace(/"imageDiv contenteditable"/g,'"imageDiv"') // 替换图片类名，添加contenteditable
            .replace(/"video_box contenteditable"/g,'"video_box"') // 替换视频类名，添加contenteditable
            .replace(/\/ueditor\/themes\/default\/myImages\/icon_edit_img_delete\.png/g,'file:\/\/\/android_asset\/html_delete\.png')
            .replace(/\/ueditor\/themes\/default\/myImages\/icon_edit_img\.png/g,'file:\/\/\/android_asset\/html_edit\.png'),
          isHideCreator: (this.isAnonymity==='1'?true:false),
          communityIdStr: communityIdStr.join(','),
        }
        const jsonData = qs.stringify(data)
        this.$axios.$request(
          updateQa(jsonData)
        ).then(res => {
          if (res.code === 1) {
            let questionId;
            if(this.$route.query.anid){
              questionId = res.result.parentQuestionAnswer.id;
            }else if(this.$route.query.quid){
              questionId = res.result.id;
            }
            window.location.href = `/topic-circle/questionhome?id=${questionId}`
          } else {
            this.$toast(res.message)
          }
        })
      },
      // 获取问题内容
      getQuestionCon_fn(){
        this.$axios.$request(
          getQuestionAnswerDetail({
            qaId: this.$route.query.quid,
            userId: this.$store.state.auth.user.id
          })
        ).then(res=>{
          if(res.code === 1){
            // console.log('问题详情',res)
            this.contentTitle = res.result.title;
            this.content = res.result.text
              // pc的兼容
              .replace(/"imageDiv"/g,'"imageDiv contenteditable"') // 替换图片类名，添加contenteditable
              .replace(/"video_box"/g,'"video_box contenteditable"') // 替换视频类名，添加contenteditable
              // 安卓的兼容
              .replace(/file:\/\/\/android_asset\/html_delete\.png/g,'/ueditor/themes/default/myImages/icon_edit_img_delete.png')
              .replace(/file:\/\/\/android_asset\/html_edit\.png/g,'/ueditor/themes/default/myImages/icon_edit_img.png')
              // ios的兼容
              .replace(/editor_image_delete_icon\.png/g,'/ueditor/themes/default/myImages/icon_edit_img_delete.png')
              .replace(/2editor_image_edit_icon\.png/g,'/ueditor/themes/default/myImages/icon_edit_img.png')
            this.topicList = res.result.topicList;
            this.circleList = res.result.circleList;
            this.isAnonymity = res.result.isHideCreator?'1':'0';
          }
        })
      },
      // 获取回答内容
      getAnswerCon_fn() {
        this.$axios.$request(
          getCommunityAnswerParent({
            qaId: this.$route.query.anid,
          })
        ).then(res=>{
          if(res.code === 1){
            this.contentTitle = res.result.answer.title;
            this.content = res.result.answer.text
              // pc的兼容
              .replace(/"imageDiv"/g,'"imageDiv contenteditable"') // 替换图片类名，添加contenteditable
              .replace(/"video_box"/g,'"video_box contenteditable"') // 替换视频类名，添加contenteditable
              // 安卓的兼容
              .replace(/file:\/\/\/android_asset\/html_delete\.png/g,'/ueditor/themes/default/myImages/icon_edit_img_delete.png')
              .replace(/file:\/\/\/android_asset\/html_edit\.png/g,'/ueditor/themes/default/myImages/icon_edit_img.png')
              // ios的兼容
              .replace(/editor_image_delete_icon\.png/g,'/ueditor/themes/default/myImages/icon_edit_img_delete.png')
              .replace(/2editor_image_edit_icon\.png/g,'/ueditor/themes/default/myImages/icon_edit_img.png')
            this.isReadonly = true
          }
        })
      },
      // 查询认证 获取用户信息
      async getWebApiPersonalWebsite_fn(){
        await this.$axios.$request(
          getWebApiPersonalWebsite({
            userId: this.$store.state.auth.user.id,
            profileUserId: this.$store.state.auth.user.id,
          })
        ).then(res=>{
          // console.log('认证',res)
          if(res.code === 1){
            this.isAuth = res.result.isAuth;
          }
        })
      },
      // 图片编辑弹窗
      showImageEdit(url,originalUrl){
        this.$store.commit('editor/setShowImgEditUrl', url) // 保存编辑图片的路径
        this.$store.commit('editor/setOriginalUrl', originalUrl) // 原始图片路径
      },
      // 文件导入弹窗
      showImportFile(){
        this.$store.commit('editor/setShowImportFile', true) // 保存编辑图片的路径
      },
      // 显示问题内容 弹窗
      isShowEditorQuestion_fn(){
        this.$store.commit('topicCircle/setIsShowEditorQuestion', true) // 编辑回答页面是否显示问题内容
      },
      handleScroll(){
        if (window.outerHeight > document.querySelector('.setting').getBoundingClientRect().top + 160) {
          this.isBackTop = true;
        }else{
          this.isBackTop = false;
        }
      },
    },
    created() {
      if (this.$route.query.id || this.$route.query.anid) {
        // 回答
        this.isAnswer = true
        this.isQuestion = false
        this.editorConfig.initialContent = '<p class="initPlaceholder" style="color: #999;">请输入回答内容</p>'
      } else {
        // 提问
        this.isAnswer = false
        this.isQuestion = true
        this.placeholder = '输入问题并以问号结尾'
        this.editorConfig.initialContent = '<p class="initPlaceholder" style="color: #999;">对问题补充说明，可以更快获得解答（选填）</p>'
      }
      if(this.$route.query.circle){
        // 圈子
        this.circleList.push({
          id: this.$route.query.circle,
          title: this.$route.query.circletitle,
        })
      }
      if(this.$route.query.topic){
        // 话题
        this.topicList.push({
          id: Number(this.$route.query.topic),
          title: this.$route.query.topictitle,
          active: true,
        })
      }
    },
    async mounted() {
      window.addEventListener('scroll', this.handleScroll) // 导航切换
      // 监听图片编辑弹窗
      window.showImgEditor = (url,originalUrl)=>{
        this.showImageEdit(url,originalUrl);
      }
      // 监听引用文章
      window.showImportFile = () => {
        this.showImportFile();
      }
      // 监听视频上传事件
      window.showImportVideo = () => {
        return this.showImportVideo()
      }
      // 查询认证
      await this.getWebApiPersonalWebsite_fn();
      // 回答类型的时候有标题，设置为不能修改
      if (this.getEditTitle_cpu) {
        this.contentTitle = this.getEditTitle_cpu
        this.isReadonly = true
      }
      // 全局搜索跳来 自动添加标题
      if(window.sessionStorage.getItem('editTitleGlobal')){
        this.contentTitle = window.sessionStorage.getItem('editTitleGlobal');
        window.sessionStorage.removeItem('editTitleGlobal')
      }

      window.addEventListener('storage', this.insertEditor)

      // 草稿箱
      if(!this.$route.query.quid && !this.$route.query.anid){
        // 获取上一次的本地草稿
        if(window.localStorage.getItem('brainmedUedit') && !/<p style="color: #999;">请输入详细内容\(选填\)<\/p>/.test(window.localStorage.getItem('brainmedUedit'))){
          this.content = window.localStorage.getItem('brainmedUedit');
        }
      }
      if(this.$route.query.quid){
        // 获取问题内容
        this.getQuestionCon_fn();
      }
      if(this.$route.query.anid){
        // 获取回答内容
        this.getAnswerCon_fn();
      }
    },
    beforeDestroy() {
      window.removeEventListener('storage', this.insertEditor)
      window.removeEventListener('scroll', this.handleScroll)
    },
  }
</script>

<style scoped lang="less">
  #edit {
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
  }

  .write {
    max-width: 1200px;
    width: 1200px;
    box-sizing: border-box;
    margin: 20px auto;
    transition: all 0.3s;
    background: #fff;
    padding: 0 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
  }

  .editTitle {
    border: none;
    padding: 17px 0;
    font-size: 18px;
    width: 100%;
    border-bottom: 1px solid #F6F6F6;
    margin-bottom: 20px;

    &.answer{
      margin-bottom: 0;
      border-bottom-width: 0;
    }
  }

  .tag{
    display: flex;
    margin-bottom: 20px;
    cursor: pointer;
    li{
      background: #EFFAFF;
      color: #0581ce;
      padding: 0 4px 0 8px;
      line-height: 30px;
      font-size: 14px;
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      border-radius: 4px;
    }
  }

  .setting {
    border-top: 1px solid #F6F6F6;
    padding: 0 0 20px;

    ul {
      li {
        display: flex;
        align-items: center;
        margin-top: 20px;

        span {
          color: #222;
          font-size: 16px;
          line-height: 38px;
          width: 104px;
          margin-right: 10px;
          &.required::after{
            content: '*';
            color: #0581CE;
          }
        }

        .btn {
          background: #F6F6F6;
          color: #666;
          padding: 0 22px;
          line-height: 32px;
          cursor: pointer;
        }

        .msg {
          color: #999;
          font-size: 12px;
          margin-top: 10px;
        }
      }
    }
  }

  .bottomMenu {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    box-shadow: 0px -4px 8px rgba(0, 0, 0, 0.08);
    z-index: 999;

    .main {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .settingBtn {
        line-height: 68px;
        font-size: 18px;
        padding: 0 20px;
        cursor: pointer;
      }

      .sendBtn {
        width: 72px;
        line-height: 32px;
        background: rgba(5, 129, 206, 0.5);
        border-radius: 4px;
        color: #fff;
        margin-right: 20px;
        text-align: center;
        cursor: pointer;

        &.active {
          background: #0581ce;
        }
      }
    }
  }

  .topicListDom {
    display: flex;
    flex-wrap: wrap;
    padding: 4px 0;
    margin: 0;

    dd {
      line-height: 32px;
      font-size: 16px;
      background: #F6F6F6;
      border-radius: 4px;
      padding: 0 22px;
      position: relative;
      margin-right: 20px;
      font-weight: normal;
      margin-left: 0;

      img {
        width: 14px;
        height: 14px;
        position: absolute;
        top: -5px;
        right: -5px;
        object-fit: cover;
      }
    }
  }
</style>
