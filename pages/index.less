.home-content {
  //overflow-x: hidden;

  .el-main {
    padding: 0;
    overflow: unset;
  }

  .el-footer {
    height: auto !important;
    background: #F6F6F6;
    margin-top: 50px;
  }
}

html,
body,
#app {
  height: auto;
}

/*返回顶部*/
.backtop {
  z-index: 999;
  width: 48px;
  height: 48px;
  position: fixed;
  right: 2vw;
  text-align: center;
  list-style: none;
  line-height: 48px;
  border-radius: 6px !important;
  bottom: calc(225px + 20px + 15px) !important;
  background: #d0d7dc !important;

  .backtopSvg {
    color: #789db4;
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: #3a8ee6 !important;
    cursor: pointer;

    .backtopSvg {
      color: #789db4 !important;
    }
  }
}

/deep/ .el-footer {
  padding: 0 !important;
}
