<template>
  <div>
    <component :is="header"/>
    <!--    <Header v-if="!onlyMain"></Header>-->
    <!--    <SearchNav/>-->
    <main>
      <nuxt-child :key='key'></nuxt-child>
    </main>
    <Footer v-if="!onlyMain"></Footer>
    <!--侧边栏-->
    <Sidebar :is-app-show="!onlyMain"/>
    <!--用户感兴趣亚专业-->
    <PersonalizedPopup
      v-if="PersonalizedPopupVisible"
      @cancel="PersonalizedPopupVisible = false"
    />
    <!--身份认证弹框-->
    <identity-authentication
      :authentication-data-info="$store.state.authenticationDataInfo"
      :authentication-dialog.sync="$store.state.authenticationDialog"
      :identity-current-father="$store.state.auth.user.identity"
      :identity-flag="!!$store.state.auth.user.identity"
      :reselect-flag="false"
      @editFlag="authenticationDialogFun"
    ></identity-authentication>
  </div>
</template>

<script>
import {Header, Footer, Sidebar, SearchNav} from "../opt-components/template";
import PersonalizedPopup from "../components/optimize-components/page-components/signin/PersonalizedPopup/index.vue";
import {init} from "../assets/helpers/index-file-mounted";
import IdentityAuthentication from '@/components/IdentityAuthentication/IdentityAuthentication'

export default {
  name: "MainPage",
  components: {
    Header,
    SearchNav,
    Footer,
    Sidebar,
    PersonalizedPopup,
    IdentityAuthentication,
  },
  data() {
    return {
      timeAuth: null,
      PersonalizedPopupVisible: false,
    }
  },
  computed: {
    header() {
      if (this.onlyMain) {
        return false
      } else if (this.$route.path === '/search') {
        return 'SearchNav'
      } else if (this.$route.path === '/advanced-search') {
        return 'SearchNav'
      } else {
        return 'Header'
      }
    },
    onlyMain() {
      return !!(this.$route.query.onlyMain || this.$route.query.source === "en-brainmed" || this.$store.state.cs.TerminalType);
    },
    key() {
      /**
       * 从/page/a => /page/b，由于这两个路由的$route.path并不一样，所以组件被强制不复用，相关钩子加载顺序为beforeRouteUpdate => created => mounted
       * 从/page?id=a => /page?id=b，由于这两个路由的$route.path一样，所以和没设置key属性一样，会复用组件，相关钩子加载顺序为：beforeRouteUpdate
       */
      return this.$route.path   // + Math.random()
    }
  },
  mounted() {
    // 入口文件 初始化
    init(this)

    // 监听滚动条变化 (待优化)
    window.addEventListener('scroll', this.getScroll)
  },
  beforeDestroy() {
    // eslint-disable-next-line no-undef
    clearTimeout(this.timeAuth)
    window.removeEventListener('scroll', this.getScroll)
  },
  methods: {
    /**
     * 身份认证
     * @param data
     */
    authenticationDialogFun(data) {
      this.$store.commit('editAuthenticationDialog', data)
    },
    getScroll() {
      const scroll = document.documentElement.scrollTop || document.body.scrollTop
      this.$store.commit('global/setScrollHandler', scroll)

      // 窗口高度
      const windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 页面高度
      const documentHeight = document.documentElement.scrollHeight || document.body.scrollHeight

      if (windowHeight + scroll >= documentHeight) {
        // 页面触底
        this.$store.commit('global/setBottomLoadingHandler', parseFloat(Math.random() + 1, 10))
      }
    },
  }
}
</script>
