<template>
  <div id="preview" class='container-box details-con'>
    <div class="tip">此链接为临时连接，仅自己可见</div>
    <div v-if="isShow">
      <div class='details-con-left'>
        <!--text Start-->
        <el-row>
          <!--            <el-col :xl="3" :lg="3" :md="3" :sm="3" :xs="3" class="hidden-xs-only">-->
          <!--              <div class="zhanwei"></div>-->
          <!--            </el-col>-->
          <el-col :lg='24' :md='24' :sm='24' :xl='24' :xs='24'>
            <div class='title-box right-box'>
              <div class='line'>
                <el-skeleton :loading='skeleFlag' :rows='1' animated>
                  <template slot='template'>
                    <el-skeleton-item
                      style='width: 80%; margin-bottom: 32px'
                      variant='text'
                    />
                    <el-skeleton-item
                      style='width: 30%; margin-bottom: 10px'
                      variant='text'
                    />
                  </template>
                  <p v-if='infoData.title' class='title'>
                    <svg-icon
                      v-if="infoData.essences === 'T'"
                      class-name='poneIcon cursor'
                      icon-class='jinghua'
                    ></svg-icon>
                    {{ infoData.title }}
                  </p>
                </el-skeleton>
                <p ref='detailDom' :class="openFlag ? '' : 'text-limit-2'" class='con'>
                    <span v-if='infoData.publishTime' style='margin-right: 15px'>
                      {{ timeStamp.timestamp_13(infoData.publishTime, 'yyy-mm-d-h-m-s') }}
                    </span>
                  <span v-if='infoData.creator' style='margin-right: 10px'>
                      {{ infoData.creator.realName }}
                    </span>
                  <span
                    v-for='item in infoData.subspecialtys'
                    :key='item.id'
                    class='con_lable_sub'
                  >
                      <span
                        v-for='itemChildren in item.children'
                        :key='itemChildren.id'
                        class='con_lable_sub_item'
                      >{{ item.name + '-' + itemChildren.name }}</span>
                    </span>
                  <nuxt-link
                    v-for='item in infoData.productList'
                    :key='item.id'
                    target='_blank'
                    :to='`/bms/classify/${item.category.firstCategoryId || "-"}/product-details/${item.id}`'
                    class='con_lable_sub_productList'
                  >
                    <svg-icon className='productIcon' iconClass='product2'></svg-icon>
                    {{ item.name }}
                  </nuxt-link>
                </p>
                <p v-if='openBtn' class='open_sub cursor' @click='openFlag = !openFlag'>
                  {{ openFlag ? '收回' : '展开 >' }}
                </p>
              </div>
            </div>
          </el-col>
        </el-row>
        <no-ssr>
          <el-row class='article_box flex_start'>
            <div class='left-boxzhanwei'></div>
            <div class='right-box twocon'>
              <el-skeleton :loading='skeleFlag' :rows='30' animated>
                <div :class='!$store.state.auth.isLogged ? "hide-article" :""'>
                  <div
                    id='fuwenben'
                    v-html='content'></div>
                </div>
              </el-skeleton>
              <HideArticle v-if='!$store.state.auth.isLogged' />
              <div class='phone-fabulous'>
                <b class='cursor'>
                  <img alt='' class='first' src='~assets/images/fabulous.png' />
                  <span>点赞</span>
                </b>
                <span>分享至</span>
                <el-popover
                  placement='right-start'
                  title='扫码分享'
                  trigger='click'
                  width='50'
                >
                  <img :src='weixinUrl' alt='' style='width: 120px; text-align: center' />
                  <img
                    slot='reference'
                    alt=''
                    class='second'
                    src='~assets/images/weixin.png'
                    @click="shareFun('weixin')"
                  />
                </el-popover>
                <!--                  <img src="~assets/images/weixin.png" alt=""  @click="shareFun('weixin')">-->
                <img
                  alt=''
                  class='three'
                  src='~assets/images/qqkongjian.png'
                  @click="shareFun('qq')"
                />
                <img alt='' src='~assets/images/weibo.png' @click="shareFun('weibo')" />
              </div>
              <!-- End -->
            </div>
          </el-row>
        </no-ssr>
      </div>
    </div>
  </div>
</template>

<script>
  import SideNavigation from '@/components/page/detail/SideNavigation/SideNavigation'
  import ArticleContent from '@/components/page/detail/ArticleContent/ArticleContent'
  import { diggs } from '@/api/article'
  import { articleDigg, followerArticle, getArticleCommentsPage, getAssoInfos, getMpArticle } from '@/api/case'
  import { addCollect, isCollect, userInfo } from '@/api/user'
  import qrcade from 'qrcode'
  export default {
    name: 'preview',
    components: {
      SideNavigation,
      ArticleContent
    },
    async asyncData({app, params, error, store, query, req}) {
      /**
       * reuqest1 获取文章详情接口
       */
      const [request1] = await Promise.all([
        app.$axios.$request(getMpArticle({
          articleId: query.id,
          userId: store.state.auth.user.id,
          articleType: 'U'
        })),
      ])
      const request4 = store.state.auth.user.id ? await app.$axios.$request(isCollect({
        type: 3,
        contentId: query.id
      })) : { result: { isCollect: false } }

      app.head.title = request1.result.title
      const attrsNames = request1.result.subspecialtys ? request1.result.subspecialtys.map((item) => item.name + ',') : ''
      const authorNames = request1.result.creator.realName && request1.result.creator.realName !== '' ? request1.result.creator.realName + ',' : ''
      let keywordsName = request1.result.searchKeyWords || ''
      keywordsName = keywordsName !== '' ? keywordsName + ',' : ''

      return {
        infoData: request1.result,
        infoContent: request1.result.paragraphType === 'O' ? request1.result.content : JSON.parse(request1.result.content),
        isCollect: request4.result.isCollect,
        attrsNames,
        authorNames,
        keywordsName,
      }
    },
    head() {
      return {
        title: this.infoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content: `脑医汇, ${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
          }
        ]
      }
    },
    data(){
      return{
        openFlag: false, // 展开收回
        openBtn: false, // 展开收回是否显示
        skeleFlag: false, // 骨架屏开关
        weixinUrl: '', // 微信二维码
        isShow: false, // 页面是否可显示
        content: '',
      }
    },
    methods: {
      // 跳转评论
      discussJumpFun() {
        // document.querySelector('.participate').scrollIntoView({ behavior: 'smooth' }) // 跳到指定元素位置
        document.querySelector('#comment').focus() // 高亮
      },
      sharweixin() {
        var url = location.href
        qrcade
          .toDataURL(url)
          .then((img) => {
            this.weixinUrl = img
          })
          .catch((err) => {
            console.log(err)
          })
      },
      //分享事件
      shareFun(data) {
        this.$store.state.auth.isLogged ? this.$axios.$request(userInfo()).then((res) => {
          if (res.code === 1) {
            this.$analysys.share(
              res.result.realName,
              data === 'weixin' ? '微信' : data === 'weibo' ? '微博' : 'QQ',
              '',
              '',
              res.result.company,
              this.infoData.title,
              '',
              [],
              [],
              '病例',
              this.infoData.creator.realName,
              '病例',
              res.result.title,
              this.infoData.title,
              this.infoData.creator.company,
              ''
            )
          }
        }) : null
        switch (data) {
          case 'weixin': {
            this.sharweixin()
            break
          }
          case 'weibo': {
            this.shareweibo()
            break
          }
          case 'qq': {
            this.shareqq()
            break
          }
        }
      },
    },
    mounted() {
      if(this.infoData.creator.id === this.$store.state.auth.user.id){
        this.isShow = true;
        this.content = window.sessionStorage.getItem('editorContent');
        // this.content = window.localStorage.getItem('editorContent');
        // this.content = this.$store.state.editor.content;
        this.$nextTick(()=>{
          let textareaListDom = document.querySelectorAll('textarea');
          for(let i=0,len=textareaListDom.length;i<len;i++){
            if(!textareaListDom[i].value){
              textareaListDom[i].remove();
            }else{
              textareaListDom[i].readOnly = true;
            }
          }
        })
      }
      this.$nextTick(function() {
        this.openBtn = this.$refs.detailDom
          ? this.$refs.detailDom.clientHeight < this.$refs.detailDom.scrollHeight
          : false
        //  修改富文本样式
        const numbox = document.getElementById('fuwenben')
        const num = numbox !== null ? numbox.getElementsByTagName('textarea') : []
        for (let i = 0; i < num.length; i++) {
          if (!num[i].value || num[i].value === '') {
            num[i].style.cssText = 'display:none'
          }
        }
      })
    }
  }
</script>

<style scoped lang="less">
  @import "~@/pages/index/case/details.less";
  #preview{
    width: 1200px;
    margin: 0 auto;
    transition: all .3s;
  }
  .details-con .details-con-left .right-box{
    padding-bottom: 50px;
    padding-left: 0!important;
  }
  .details-con .details-con-left .title-box{
    padding-left: 0!important;
  }
  @media screen and (max-width: 1199px) {
    #preview {
      width: 950px;
    }
  }
  #preview /deep/ img{
    width: 100%;
  }

  .tip{
    background: #999EA4;
    color: #fff;
    text-align: center;
    font-size: 12px;
    line-height: 30px;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
  }
</style>
<style>
  /*新版编辑器标题*/
  #fuwenben {
    line-height: 1.8;
  }

  #fuwenben img {
    max-width: 100%;
    width: 100%;
    height: auto
  }

  #fuwenben ul li:before {
    display: inline-block;
    content: '•';
    width: 20px;
    font-size: inherit;
    text-align: center;
    text-indent: 0 !important;
  }

  #fuwenben ol {
    counter-reset: item;
  }

  #fuwenben ol li:before {
    display: inline-block;
    content: counter(item) ".";
    counter-increment: item;
    font-size: inherit;
    min-width: 20px;
    text-align: center;
  }

  #fuwenben h2 {
    margin: 1.2rem 0 .8rem;
    padding: 0;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.1rem;
    color: #333333;
  }

  #fuwenben h2.htwo {
    position: relative;
    padding-left: 0.6rem;
  }

  #fuwenben h2.htwo::before {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    content: '';
    width: 0.2rem;
    height: 1.1rem;
    background: #0581ce;
  }

  #fuwenben h2.hthree {
    position: relative;
    padding-bottom: 0.6rem;
    text-align: center;
  }

  #fuwenben h2.hthree::after {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    content: '';
    width: 1.1rem;
    height: 0.2rem;
    background: #0581ce;
  }

  #fuwenben h2.hone {
    position: relative;
    padding-left: 0.9rem;
  }

  #fuwenben h2.hone::before {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    content: '';
    width: 0.5rem;
    height: 0.6rem;
    background: url("/template/1/bluewise/_files/h5_images/triger.png") no-repeat center / cover;
  }

  #fuwenben h2.hfour {
    position: relative;
    padding: 0.3rem 0.2rem;
    background-color: #0581ce;
    color: #fff;
    /* display: inline-block; */
    display: inline-flex;
    display: table;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    left: 50%;
    transform: translateX(-50%);
    /* line-height: 30px; */
  }

  #fuwenben h2.hfour::before {
    position: absolute;
    left: 0.2rem;
    top: 0.2rem;
    content: '';
    width: 100%;
    height: 100%;
    border: 0.05rem solid #0581CE;
    box-sizing: border-box;
  }

  #fuwenben h2.hfive {
    position: relative;
    padding: 0.3rem 0.55rem;
    background-color: #0581ce;
    color: #fff;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 0.2rem;
    display: inline-flex;
    display: table;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
  }

  /*新病例*/
  /*图片描述*/
  #fuwenben p.tp_desc {
    margin-top: 0.6rem;
    margin-bottom: .6rem;
    text-align: center;
    font-size: 0.6rem;
    line-height: 1.2em;
    color: #999;
  }

  #fuwenben img {
    display: block;
  }

  #fuwenben {
    padding-top: 1rem;
    /*line-height: 1.5em;*/
  }

  /*兼容旧版段落标题*/
  .old_text_box {
    position: relative;
    margin: 0;
  }

  #fuwenben img:not(.imageDiv img) {
    margin-bottom: 1.2rem;
  }

  #fuwenben .imageDiv {
    margin-bottom: 1.2rem;
  }

  #fuwenben .noMarginBottom{
    margin-bottom: 0;
  }
  /*隐藏新编辑器操作按钮*/
  #fuwenben .imageDelete, .imageEdit {
    display: none;
    visibility: hidden;
  }
  #fuwenben li p,#fuwenben ol p{
    display: inline;
  }
  #fuwenben .quote_box,
  #fuwenben .link_box {
    position: relative;
    margin: 10px 0;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
    background: rgba(5, 129, 206, 0.04);
    border-radius: 4px;
    display: flex;
    align-items: center;
  }
  #fuwenben .link_box {
    line-height: 1;
    display: flex;
    align-items: center;
  }
  #fuwenben .link_box .link_icon {
    margin-right: 8px;
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/question/icon-lj.png') no-repeat center /
      cover;
  }
  #fuwenben .link_box i {
    font-style: normal;
    color: #279aee;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    text-decoration: underline;
  }
  #fuwenben textarea{
    font-size: 16px!important;
    line-height: normal!important;
    height: 20px!important;
    max-width: 100%;
    min-width: 100%;
    resize: none;
    white-space: nowrap;
    display: inline-block!important;
  }
  #fuwenben .imageDelete,
  #fuwenben .imageEdit,
  #fuwenben .close_btn,
  #fuwenben .progress_bar,
  #fuwenben .video_play_btn,
  #fuwenben .webModule,
  #fuwenben .close{
    display: none!important;
    visibility: hidden!important;
  }
</style>
