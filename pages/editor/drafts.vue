<template>
  <div id="Drafts">
    <!-- 顶部导航 -->
    <TopNavigation/>
    <div class="pageCon">
      <!-- 发布 -->
      <div class="issue">
        <dropdown>
          <div class="btn"
               @mouseenter="tabHover_fn(3)"
               @mouseleave="tabHover_fn(-1)">
            <svg-icon class-name='editor_issue' :icon-class='menuSvg === 3?"editor_issue_hover":"editor_issue"'/>
            <span>发布</span>
          </div>
          <dropdown-menu slot="dropdown">
            <dropdown-item>
              <div class="menuItem"
                   @click="toEditor_fn(0)"
                   @mouseenter="tabHover_fn(0)"
                   @mouseleave="tabHover_fn(-1)">
                <svg-icon :icon-class='menuSvg === 0?"editor_case_blue":"editor_case"' class-name='menuSvg'/>
                发布病例
              </div>
            </dropdown-item>
            <dropdown-item>
              <div class="menuItem"
                   @click="toEditor_fn(2)"
                   @mouseenter="tabHover_fn(1)"
                   @mouseleave="tabHover_fn(-1)">
                <svg-icon :icon-class='menuSvg === 1?"editor_article_blue":"editor_article"' class-name='menuSvg'/>
                发布文章
              </div>
            </dropdown-item>
            <dropdown-item>
              <div class="menuItem"
                   @click="toEditor_fn(3)"
                   @mouseenter="tabHover_fn(2)"
                   @mouseleave="tabHover_fn(-1)">
                <svg-icon :icon-class='menuSvg === 2?"editor_popular_blue":"editor_popular"' class-name='menuSvg'/>
                写科普
              </div>
            </dropdown-item>
          </dropdown-menu>
        </dropdown>
      </div>
      <!-- tab标签页 -->
      <!-- isShow主要在list文件控制加载更多 -->
      <tabs v-model="activeName" @tab-click="handleClick">
        <tab-pane v-for="(item,index) in tabs" :key="index" :label="item.name">
          <DraftsPage v-if="item.type === -1" :isShow="activeName === index.toString()"/>
          <Article v-else :type="item.type" :isShow="activeName === index.toString()"/>
        </tab-pane>
      </tabs>
    </div>
  </div>
</template>

<script>
  import { Tabs, TabPane, Dropdown,DropdownMenu,DropdownItem } from 'element-ui'
  import { userInfo } from '@/api/user'
  import { loginByToken } from '@/api/login'
  import Article from '@/components/optimize-components/editor/Drafts/Article'
  import DraftsPage from '@/components/optimize-components/editor/Drafts/DraftsPage'
  import TopNavigation from '@/components/optimize-components/editor/TopNavigation'
  export default {
    name: 'Drafts',
    components: {
      Tabs,
      TabPane,
      Article,
      DraftsPage,
      Dropdown,
      DropdownMenu,
      DropdownItem,
      TopNavigation
    },
    head() {
      return {
        title: '草稿箱',
        meta: [
          {
            hid: 'description',
            name: 'description',
            content: ''
          },
          {
            hid: 'keywords',
            name: 'keywords',
            content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
          }
        ],
        timer: null
      }
    },
    data() {
      return {
        activeName: '0',
        menuSvg: -1,
        tabs:[
          {
            name: '病例',
            type: 0,
          },
          // {
          //   name: '随笔',
          //   type: 1,
          // },
          {
            name: '文章',
            type: 2,
          },
          {
            name: '科普',
            type: 3,
          },
          {
            name: '草稿箱',
            type: -1,
          },
        ],
      }
    },
    methods: {
      handleClick(tab, event) {
        // tab点击
      },
      // 发布下拉框修改样式
      tabHover_fn(type){
        if(type === 3){
          this.$analysys.btn_click('发布', '草稿箱')
        }
        this.menuSvg = type;
      },
      // 发布下拉框点击
      toEditor_fn(type){
        const localToken = this.$cookies.get('medtion_token_only_sign')
        const isLogged = this.$store.state.auth.isLogged
        if (localToken || isLogged) {
          this.$axios.$request(loginByToken({
            token: localToken
          })).then(response => {
            this.$axios.$request(userInfo()).then(res => {
              if (res && res.code === 1) {
                // 判断身份
                if(res.result.identity === 3 || res.result.identity === 4){
                  this.$toast.fail('当前身份无法使用发布功能')
                  return;
                }
                if(type === 0){
                  this.$analysys.btn_click('发布病例', '草稿箱-发布')
                }else if(type === 2){
                  this.$analysys.btn_click('发布文章', '草稿箱-发布')
                }else if(type === 3){
                  this.$analysys.btn_click('写科普', '草稿箱-发布')
                }
                if (res.result.isAuth === '1') {
                  window.open(`/editor?type=${type}`)
                } else if (res.result.isAuth === '2') {
                  window.open(`/editor?type=${type}`)
                } else if (res.result.isAuth === '3') {
                  this.$toast.fail('身份认证失败')
                } else if (!res.result.identity || res.result.identity === 'undefined') {
                  const h = this.$createElement
                  this.$msgbox({
                    message: h('p', null, [
                      h('span', null, '部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！')
                    ]),
                    center: true,
                    showCancelButton: true,
                    cancelButtonText: '以后再说',
                    confirmButtonText: '去完善信息',
                    customClass: 'personaldata-messagebox',
                    beforeClose: (action, instance, done) => {
                      if (action === 'confirm') {
                        this.$store.commit('editAccountTypeFun', this.$store.state.auth.user.email ? this.$store.state.auth.user.email : 'tel')
                        this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                        this.$router.push({ name: 'register' })
                        done()
                      } else {
                        done()
                      }
                    }
                  })
                } else {
                  this.$toast.fail('请先认证')
                  this.$store.dispatch('authenticationHandler')
                }
              }
            })
          })
        } else {
          this.$emit('showHiddenFn', false)
          this.$toast('请先登录')
          this.$store.commit('editBackUrl', window.location.href)
          this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
        }
      },
    },
    mounted() {
      if(this.$route.query.type === '1'){
        // 禁止发布随笔
        window.location.replace(window.location.href.replace(/type=1/,'type=0'));
        return;
      }
      if(this.$route.query.type){
        if(this.$route.query.type === '1'){
          this.activeName = '1';
        }
        switch (this.$route.query.type) {
          case '0':
            this.activeName = '0';
            break;
          case '1':
            this.activeName = '0';
            break;
          case '2':
            this.activeName = '1';
            break;
          case '3':
            this.activeName = '2';
            break;
          default:
            this.activeName = '3';
        }
      }else{
        this.activeName = '3';
      }
    }
  }
</script>

<style scoped lang="less">
  #Drafts {
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
    min-height: 100.1vh;
    box-sizing: border-box;
  }

  .pageCon {
    max-width: 1200px;
    width: 1200px;
    min-height: 80vh;
    box-sizing: border-box;
    margin: 20px auto;
    transition: all 0.2s;
    background: #fff;
    padding: 0 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    position: relative;
  }

  .issue {
    position: absolute;
    right: 24px;
    top: 18px;
    z-index: 1;

    .btn{
      display: flex;
      align-items: center;
      cursor: pointer;

      &:hover span{
        color: #FFAD8F;
      }
      &:hover .editor_issue{
        color: #FFAD8F;
      }

      span{
        color: #E76333;
        font-size: 16px;
      }
      .editor_issue {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }
  }

  /deep/ .el-tabs__item {
    font-size: 16px;
  }

  /deep/ .el-tabs__nav-wrap {
    line-height: 57px;

    &::after {
      background-color: #F6F6F6;
    }
  }

  /deep/ .el-tabs__active-bar {
    /*width: 16px !important;*/
    /*margin-left: 10px;*/
  }

  /deep/ .el-dropdown-menu__item {
    padding: 0;
    &:hover{
      background: #EFFAFF;
      color: #0581CE;
    }
  }
  .menuItem{
    padding: 0 20px;
  }

  @media screen and (max-width: 1432px) {
    .pageCon {
      width: 1000px;
    }
  }
  @media screen and (max-width: 1230px) {
    .pageCon {
      width: 880px;
    }
  }
  @media screen and (max-width: 1110px) {
    .pageCon {
      width: 760px;
    }
  }
  @media screen and (max-width: 980px) {
    .pageCon {
      width: 640px;
    }
  }
</style>
