<template>
  <div id="index">
    <!-- 顶部导航 -->
    <TopNavigation/>
    <div id="edit">
      <div class="write">
        <input v-model="contentTitle" class="editTitle" type="text" :placeholder="placeholder" maxlength="50"
               :readonly="isReadonly">
        <vue-ueditor-wrap v-model="content" editor-id="editor" :config="editorConfig"
                          :editorDependencies="['ueditor.config.js', 'ueditor.all.js']" style="min-height: 80vh"/>
        <!-- 发布设置 -->
        <Setting/>
      </div>
      <!-- 底部菜单 -->
      <EditorFooter/>
    </div>
    <!-- 侧边导航 -->
    <RightNavigation/>
    <!-- 添加话题 -->
    <AddTopic v-if="$store.state.editor.showTopicBox"/>
    <!-- 添加圈子 -->
    <AddCircle v-if="$store.state.editor.showCircleBox"/>
    <!-- 添加频道 -->
    <AddChannel v-if="$store.state.editor.showChannelBox"/>
    <!-- 亚专业 -->
    <Submajor v-if="$store.state.editor.showSubmajorBox"/>
    <!-- 产品 -->
    <Product v-if="$store.state.editor.showProductBox"/>
    <!-- 专栏 -->
    <Special v-if="$store.state.editor.showSpecialBox"/>
    <!-- 病种 -->
    <Disease v-if="$store.state.editor.showDiseaseBox"/>
    <!-- app预览 -->
    <AppPreview v-if="$store.state.editor.showPreviewBox"/>
    <!-- 图片编辑 -->
    <ImgEditor v-if="$store.state.editor.showImgEditUrl"/>
    <!-- 文件导入 -->
    <ImportFile v-if="$store.state.editor.showImportFile"/>
    <!-- 视频导入 -->
    <ImportVideo v-if="$store.state.editor.showImportVideo"/>
  </div>
</template>

<script>
/*
* 未完成的功能
* 1. .md文件上传的图片路径没有转成阿里云
* 2. 光标在标题上时未禁用工具栏其他按钮
* 3. 个人主页的编辑按钮
* */
import VueUeditorWrap from 'vue-ueditor-wrap'
import Setting from '@/components/optimize-components/editor/Setting'
import EditorFooter from '@/components/optimize-components/editor/EditorFooter'
import RightNavigation from '@/components/optimize-components/editor/RightNavigation'
import AddTopic from '@/components/optimize-components/editor/AddTopic' // 添加话题
import AddCircle from '@/components/optimize-components/editor/AddCircle' // 添加圈子
import AddChannel from '@/components/optimize-components/editor/AddChannel' // 添加频道
import Submajor from '@/components/optimize-components/editor/Submajor' // 添加亚专业
import Product from '@/components/optimize-components/editor/Product'
import Special from '@/components/optimize-components/editor/Special'
import Disease from '@/components/optimize-components/editor/Disease'
import AppPreview from '@/components/optimize-components/editor/appPreview'
import TopNavigation from '@/components/optimize-components/editor/TopNavigation'
import ImgEditor from '@/components/optimize-components/editor/ImgEditor'
import ImportFile from '@/components/optimize-components/editor/ImportFile'
import ImportVideo from '@/components/optimize-components/editor/ImportVideo'
import {
  getMpArticle, // 获取亚专业
  getArticleSpecials, // 获取用户专栏
  getCommunityByArticleAndType, // 获取话题或圈子
  getArticleProducts, // 获取产品
  getMpChannelList, // 获取频道列表
  getTreeSpecialities, // 获取亚专业列表
  getCommonlyUsedSubspecialties // 获取常用亚专业
} from '@/api/editor'

export default {
  name: 'EditorIndex',
  components: {
    VueUeditorWrap,
    Setting,
    EditorFooter,
    RightNavigation,
    AddTopic,
    AddCircle,
    AddChannel,
    Submajor,
    Product,
    Special,
    Disease,
    AppPreview,
    TopNavigation,
    ImgEditor,
    ImportFile,
    ImportVideo
  },
  head() {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: ''
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ],
      script: [
        {src: '/js/aliyun-upload-sdk/lib/es6-promise.min.js'},
        {src: '/js/aliyun-upload-sdk/lib/aliyun-oss-sdk-6.17.1.min.js'},
        {src: '/js/aliyun-upload-sdk/aliyun-upload-sdk-1.5.6.min.js'}
      ]
    }
  },

  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    const userAgent = req ? req.headers['user-agent'] : ''
    let TerminalType = null
    if (userAgent.includes('BrainMedPC_Win')) {
      TerminalType = 'BrainMedPC_Win'
    }
    if (userAgent.includes('BrainMedPC_Mac')) {
      TerminalType = 'BrainMedPC_Mac'
    }

    store.commit('cs/setTerminalType', TerminalType)

    let title = '发病例'
    if (query.type === '0') {
      title = '病例编辑'
    } else if (query.type === '1') {
      title = '随笔编辑'
    } else if (query.type === '2') {
      title = '文章编辑'
    } else if (query.type === '3') {
      title = '科普患教编辑'
    }

    if (query.id) {
      const [request1, request2, request3, request4, request5, request6] = await Promise.all([
        app.$axios.$request(getMpArticle({
          id: query.id
        })),
        app.$axios.$request(getArticleSpecials({
          articleId: query.id,
          type: 'U',
          userId: store.state.auth.user.id
        })),
        app.$axios.$request(getCommunityByArticleAndType({
          articleId: query.id,
          articleType: 'U',
          userId: store.state.auth.user.id,
          communityType: 'T'
        })),
        app.$axios.$request(getCommunityByArticleAndType({
          articleId: query.id,
          articleType: 'U',
          userId: store.state.auth.user.id,
          communityType: 'C'
        })),
        app.$axios.$request(getCommunityByArticleAndType({
          articleId: query.id,
          articleType: 'U',
          userId: store.state.auth.user.id,
          communityType: 'A'
        })),
        app.$axios.$request(getArticleProducts({
          articleId: query.id
        }))
      ])
      // 二次编辑时 判断该文章作者是否是当前登录用户
      if (request1.result && (store.state.auth.user.id !== request1.result.creator.id)) {
        redirect(`/`)
        return
      }
      return {
        title,
        pageInfo: request1.result,
        specials: request2.list,
        topics: request3.list,
        circle: request4.list,
        activity: request5.list,
        products: request6.list
      }
    } else {
      return {
        title
      }
    }
  },
  watch: {
    contentTitle: {
      handler(n, o) {
        this.$store.commit('editor/setContentTitle', n) // 封面
      },
      deep: true
    },
    content: {
      handler(n, o) {
        this.$store.commit('editor/setContent', n) // 内容
        window.sessionStorage.setItem('editorContent', n) // 预览获取的内容
        // window.localStorage.setItem('editorContent',n); // 预览获取的内容 会话储存在客户端无法使用
      },
      deep: true
    }

  },
  data() {
    return {
      contentTitle: '', // 富文本标题
      isReadonly: false, // 标题是否可编辑
      content: '', // 富文本内容
      editorConfig: {
        // 后端服务
        serverUrl: '',
        // 配置UEditorPlus的静态资源
        UEDITOR_HOME_URL: '/ueditor/',
        initialContent: '<p style="color: #999;">请输入详细内容</p>',
        autoClearinitialContent: true
      },
      placeholder: '请输入标题 (2-50字)'
    }
  },
  methods: {
    getArticle() {
      if (this.$route.query.id) {
        this.contentTitle = this.pageInfo.title
        // contenteditable 防止格式被富文本转化
        this.content = this.pageInfo.content
          // pc的兼容
          .replace(/"imageDiv"/g, '"imageDiv contenteditable"') // 替换图片类名，添加contenteditable
          .replace(/"video_box"/g, '"video_box contenteditable"') // 替换视频类名，添加contenteditable
          // 安卓的兼容
          .replace(/file:\/\/\/android_asset\/html_delete\.png/g, '/ueditor/themes/default/myImages/icon_edit_img_delete.png')
          .replace(/file:\/\/\/android_asset\/html_edit\.png/g, '/ueditor/themes/default/myImages/icon_edit_img.png')
          // ios的兼容
          .replace(/editor_image_delete_icon\.png/g, '/ueditor/themes/default/myImages/icon_edit_img_delete.png')
          .replace(/2editor_image_edit_icon\.png/g, '/ueditor/themes/default/myImages/icon_edit_img.png')
        this.$store.commit('editor/setUpdateCoverUrl', this.pageInfo.cover) // 封面
        this.$store.commit('editor/setDescribe', this.pageInfo.description) // 描述
        this.$store.commit('editor/setActiveSpecialList', this.specials) // 专栏
        this.$store.commit('editor/setActiveTopicList', this.topics) // 话题
        this.$store.commit('editor/setActiveCircleList', this.circle) // 圈子
        this.$store.commit('editor/setActiveActivityList', this.activity) // 活动
        this.$store.commit('editor/setActiveProductList', this.products) // 产品
        this.$store.commit('editor/setActiveDiseaseList', this.pageInfo.diseases) // 病种
        this.$store.commit('editor/setPurviewSetting', this.pageInfo.permission) // 权限
        this.$store.commit('editor/setOriginal', this.pageInfo.original) // 原创
        // ** 频道 默认在this.getMpChannelList_fn()
        // ** 亚专业 默认在this.getTreeSpecialities_fn()
      }
    },
    // 图片编辑弹窗
    showImageEdit(url, originalUrl) {
      this.$store.commit('editor/setShowImgEditUrl', url) // 保存编辑图片的路径
      this.$store.commit('editor/setOriginalUrl', originalUrl) // 原始图片路径
    },
    // 视频上传
    showImportVideo(fileDom){
      this.$store.commit('editor/setShowImportVideo', true) // 保存编辑图片的路径
    },
    // 文件导入弹窗
    showImportFile() {
      this.$store.commit('editor/setShowImportFile', true) // 保存编辑图片的路径
    },
    // 获取频道列表（因为多个模块用到，所以在这里获取）
    getMpChannelList_fn() {
      this.$axios.$request(
        getMpChannelList({})
      ).then(res => {
        if (res.code === 1) {
          if (res.list.length) {
            // 将频道列表数据放在全局
            this.$store.commit('editor/setChannelList', JSON.parse(JSON.stringify(res.list)))
            // 获取频道默认选中设置
            let defaultActiveChannelList = []
            // this.pageInfo 文章数据
            if(this.$route.query.id && this.pageInfo && this.pageInfo.channelList.length){
              // 因为给的默认选中频道和频道数据列表格式不一致，在这里转换一下
              for (let i = 0, len = res.list.length; i < len; i++) {
                for (let defaultI = 0, len = this.pageInfo.channelList.length; defaultI < len; defaultI++) {
                  if (this.pageInfo.channelList[defaultI].id === res.list[i].id) {
                    res.list[i].avtive = true;
                    defaultActiveChannelList.push(res.list[i])
                  }
                }
              }
              // “最新”必选 状态设置
              for (let i = 0, len = defaultActiveChannelList.length; i < len; i++) {
                if(defaultActiveChannelList[i].name === '最新'){
                  defaultActiveChannelList[i].mustActive = true
                }
              }
              // 亚专业对应的必选状态设置
              defaultActiveChannelList.map(v => {
                this.$store.state.editor.activeSubmajorList.map(i => {
                  if (v.mpSubspecialityId === i.pId) {
                    v.mustActive = true
                  }
                })
              })
            }else{
              // 因为给的默认选中频道和频道列表格式不一致，在这里统一
              for (let i = 0, len = res.list.length; i < len; i++) {
                if(res.list[i].name === '最新'){
                  res.list[i].active = true;
                  res.list[i].mustActive = true;
                  defaultActiveChannelList.push(res.list[i])
                }
              }
            }

            this.$store.commit('editor/setActiveChannelList', defaultActiveChannelList)
          }
        }
      })
    },
    // 获取亚专业列表
    getTreeSpecialities_fn() {
      this.$axios.$request(getTreeSpecialities({
        siteType: 'F'
      }))
        .then((res) => {
          if (res.code === 1) {
            this.$store.commit('editor/setSubmajorList', JSON.parse(JSON.stringify(res.list)))
            // 因为给的默认选中亚专业和亚专业数据列表格式不一致，在这里转换一下
            if(this.pageInfo){
              let defaultActiveSubmajorList = res.list.filter(item1 =>
                this.pageInfo.dicts.some(item2 => item2.id === item1.id)
              );
              this.$store.commit('editor/setActiveSubmajorList', defaultActiveSubmajorList) // 亚专业
            }

            // 更具亚专业获取并设置必选频道
            if (this.$route.query.type === '2') {
              // 获取频道列表
              this.getMpChannelList_fn()
            }
          }
        })
    },
    // 常用亚专业
    getCommonlyUsedSubspecialties_fn() {
      this.$axios.$request(getCommonlyUsedSubspecialties({
        userId: this.$store.state.auth.user.id,
        title: '',
        limit: 10
      })).then(res => {
        if (res.code === 1) {
          this.$store.commit('editor/setCommonSubspecialty', JSON.parse(JSON.stringify(res.list)))
        }
      })
    }
  },
  mounted() {
    // 企业用户无法使用发布功能
    if (/^4/.test(this.$store.state.auth.user.identity)) {
      this.$toast.fail('当前身份无法发布功能')
      window.location.replace('/')
    }
    if (this.pageInfo && this.pageInfo.type !== this.$route.query.type) {
      // 若有文章id，判断原文章类型是否和当前页面类型一致
      window.location.replace(`/editor?type=${this.pageInfo.type}&id=${this.$route.query.id}`)
      return
    } else if (this.$route.query.type === '1') {
      // 禁止发布随笔
      window.location.replace(window.location.href.replace(/type=1/, 'type=0'))
      return
    }

    // 获取内容
    this.getArticle()

    if (this.$route.query.type === '0' || this.$route.query.type === '2') {
      // 获取亚专业列表
      this.getTreeSpecialities_fn()
      // 常用亚专业
      this.getCommonlyUsedSubspecialties_fn()
    }

    // 监听图片编辑弹窗
    window.showImgEditor = (url, originalUrl) => {
      this.showImageEdit(url, originalUrl)
    }
    // 监听引用文章
    window.showImportFile = () => {
      this.showImportFile()
    }
    // 监听视频上传事件
    window.showImportVideo = () => {
      return this.showImportVideo()
    }
  }
}
</script>

<style scoped lang="less">
#edit {
  background: #FBFBFB;
  overflow: hidden;
  padding-bottom: 60px;
}

.write {
  max-width: 1200px;
  width: 1200px;
  box-sizing: border-box;
  margin: 20px auto;
  transition: all 0.3s;
  background: #fff;
  padding: 0 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
}

.editTitle {
  border: none;
  padding: 17px 0;
  font-size: 18px;
  width: 100%;
  border-bottom: 1px solid #F6F6F6;
  margin-bottom: 20px;
}

@media screen and (max-width: 1432px) {
  .write {
    width: 1000px;
  }
}

@media screen and (max-width: 1230px) {
  .write {
    width: 880px;
  }
}

@media screen and (max-width: 1110px) {
  .write {
    width: 760px;
  }
}

@media screen and (max-width: 980px) {
  .write {
    width: 640px;
  }
}
</style>
