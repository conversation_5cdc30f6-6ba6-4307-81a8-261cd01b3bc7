<template>
  <div id="instructions">
    <TopNavigation />
    <div id="instructionsMain">
      <h3 class="title">
        <img :src="require('~/assets/images/editor/icon_instructions.png')" alt="">
        编辑器操作指南
      </h3>
      <ul class="list">
        <li v-for="(item,index) in data" :key="index">
          <span>{{item.msg}}</span>
          <img :src="item.src" alt="">
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  import TopNavigation from '@/components/optimize-components/editor/TopNavigation'
  export default {
    name: 'instructions',
    components: {
      TopNavigation
    },
    head() {
      return {
        title: '操作指南',
        meta: [
          {
            hid: 'description',
            name: 'description',
            content: ''
          },
          {
            hid: 'keywords',
            name: 'keywords',
            content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
          }
        ],
        timer: null
      }
    },
    data(){
      return{
        data:[
          {
            msg: '1.一个好的标题能吸引更多人点击打开',
            src: require('~/assets/images/editor/instructions_bg1.png')
          },
          {
            msg: '2.自定义标题可以让文章的内容模块更加简洁明了',
            src: require('~/assets/images/editor/instructions_bg2.png')
          },
          {
            msg: '3.有序列表可以让观点/结论更加一目了然',
            src: require('~/assets/images/editor/instructions_bg3.png')
          },
          {
            msg: '4.这里可以上传图片和视频(可支持裁剪、马赛克等功能)',
            src: require('~/assets/images/editor/instructions_bg4.png')
          },
          {
            msg: '5.关联的越精细，所得到的曝光也越多!',
            src: require('~/assets/images/editor/instructions_bg5.png')
          },
        ]
      }
    },
    methods: {

    },
    mounted() {

    }
  }
</script>

<style scoped lang="less">
  #instructions{
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
  }
  #instructionsMain{
    max-width: 1200px;
    width: 1200px;
    box-sizing: border-box;
    margin: 20px auto;
    transition: all 0.3s;
    background: #fff;
    padding: 20px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
  }

  .title{
    display: flex;
    align-items: center;
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 20px;
    img{
      width: 40px;
      height: 40px;
      margin-right: 16px;
    }
  }

  .list{
    display: flex;
    flex-direction: column;
    li{
      display: flex;
      flex-direction: column;
      margin: 20px 0;
      span{
        font-size: 20px;
        margin-bottom: 24px;
      }
      img{
        width: 100%;
        border-radius: 8px;
      }
    }
  }

  @media screen and (max-width: 1432px) {
    #instructionsMain {
      width: 1000px;
    }
  }
  @media screen and (max-width: 1230px) {
    #instructionsMain {
      width: 880px;
    }
  }
  @media screen and (max-width: 1110px) {
    #instructionsMain {
      width: 760px;
    }
  }
  @media screen and (max-width: 980px) {
    #instructionsMain {
      width: 640px;
    }
  }
</style>
