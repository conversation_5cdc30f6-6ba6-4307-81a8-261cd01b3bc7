body, #__nuxt, #__layout, #__layout {
  height: 100%;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

.appcodeMask {
  position: relative;

  &::after {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: black;
  }
}

::v-deep input[type='number'] {
  -moz-appearance: textfield !important;
}

.app-code-container {
  width: 230px;
}

/deep/ .el-form-item {
  margin-bottom: 15px;
}

/deep/ .el-input__inner {
  width: 100%;
  height: 48px;
  line-height: 48px;
  background: #F5FBFE;
  border: none;
}

/deep/ .el-input__suffix-inner {
  line-height: 54px;
}

/deep/ .el-form-item__content {
  line-height: 0;
}


/deep/ .el-checkbox__inner {
  border-radius: 4px;
}

/deep/ .el-radio__input.is-checked + .el-radio__label {
  color: #666666;
}

/deep/ .el-form-item__error {
  display: none;
}

.lable_tong {
  margin-bottom: 25px;
  text-align: center;
}

.button_item {
  width: 100%;
  border-radius: 12px !important;
  height: 47px;
  text-align: center;
  line-height: 47px;
  padding: 0;
}

.weixinLogin {
  margin-top: 25px;
}

.remember_box {
  margin-bottom: 15px;

  .closepassword {
    font-size: 14px;
    color: #FF922D;
  }
}

.passwordflag {
  width: 20px !important;
  height: 20px !important;
  margin-right: 19px;
}

/deep/ .el-input-group__prepend {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 0 0 24px;
  color: #759BB2;
  font-size: 16px;
  line-height: 48px;
}

/deep/ .el-input-group__append {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 24px;

  .verificationcode {
    font-size: 16px;
    color: #0581CE;
    line-height: 19px;

    span {
      color: #9FCFED;
      margin-right: 3px;
    }
  }
}

.signin-bg-box {
  height: 100vh;
  background-image: url("~assets/images/bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  //background-size: 100% 100%;
  background-position: center;

  .login-header-logo {
    margin-top: 10px;
    width: 302px;

    img {
      width: 100%;
    }
  }

  .forgetPasswordBox {
    height: auto !important;
  }

  .signin-center-box {
    position: absolute;
    left: 50%;
    top: calc(50% - 34px);
    width: 488px;
    //height: 591px;
    transform: translate(-50%, -50%);
    background: #FFFFFF;
    border-radius: 30px;
    padding: 36px 47px;
    box-sizing: border-box;
    box-shadow: 0px 10px 15px 1px rgba(0, 0, 0, 0.1);

    .guide_code_box {
      position: absolute;
      left: calc(100% + 20px);
      top: 100px;
      width: 298px;
      height: 316px;
    }

    .app_login_code_img {
      z-index: 99;
      cursor: pointer;
      position: absolute;
      left: 16px;
      top: 16px;
    }

    .close {
      width: 22px;
      height: 22px;
      text-align: center;
      line-height: 20px;
      background: #EBEBEB;
      position: absolute;
      top: 30px;
      right: 30px;

      &:hover i {
        color: #B1B1B1;
      }

      i {
        font-size: 13px;
        color: #949494;
      }
    }

    .weixin-login-box {
      margin-bottom: 37px;
      text-align: center;

      .weixin-tip {
        color: #666666;
      }

      .weixin-box {
        width: 208px;
        height: 208px;
        border: 1px solid #F2F2F2;
        box-sizing: border-box;
        margin: 0 auto 29px;

        #weixinbox {
          height: 100%;

          .title {
            display: none !important;
          }
        }

        #appcode {
          width: 100%;
          height: 100%;
          position: relative;

          .refresh-code {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            color: white;
            display: flex;
            flex-flow: column;
            align-items: center;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.6);

            i {
              font-size: 60px;
              line-height: 100%;
            }

            span {
              margin-top: 10px;
              font-size: 12px;
            }


          }

          img {
            width: 100%;
            height: 100%;
          }

          .seize {
            i {
              font-size: 20px;
              color: white;
            }

            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            width: calc(100% - 20px);
            height: calc(100% - 20px);
            background-color: rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .login-switching-list {
      margin-bottom: 40px;
      align-items: end;
      position: relative;

      .verificationcode-tip {
        position: absolute;
        left: 0;
        top: calc(100% + 6px);
        color: #666666;
      }

      .login-switching-item {
        margin-right: 15px;
        font-size: 20px;
        font-weight: 550;
        color: #888888;

        &:last-child {
          margin-right: 0;
        }
      }

      .is_active {
        font-size: 24px;
        color: #000000;
      }
    }

    .login-button_list {
      margin-bottom: 30px;

      & .button_item:nth-child(1) {
        background: #EEF8FF;
        color: #0581CE;
        margin-bottom: 15px;

        &:hover {
          background: #0581CE;
          color: #EEF8FF;
        }
      }

      & .button_item:nth-child(2) {
        background: #EDFFEF;
        color: #00B432;
        margin-bottom: 15px;

        &:hover {
          background: #00B432;
          color: #EDFFEF;
        }
      }

      & .button_item:nth-child(3) {
        background: #FFF9ED;
        color: #F86E0A;

        &:hover {
          background: #F86E0A;
          color: #FFF9ED;
        }
      }
    }

    .tip-buttom {
      line-height: 16px;

      .tip-register {
        font-size: 14px;
        color: #92A3AE;

        .register {

        }
      }

      .tip-Overseas {

      }
    }
  }

  .sign-codeoraccount-box {
    /deep/ .el-input__inner {
      width: 100%;
      height: 46px;
      line-height: 46px;
      background: #F5FBFE;
      border: none;
    }

    width: 870px !important;
    min-height: 550px !important;
    padding: 36px 66px !important;

    .weixin-login-box {
      margin-bottom: 0;

      .title {
        font-size: 24px;
        color: #333333;
        font-weight: 500;
        line-height: 28px;
        margin-bottom: 30px;
      }

      .weixin-tip {
        font-size: 16px;

        .tips {
          display: block;
          font-weight: 500;
          color: #333333;
          line-height: 20px;
          font-size: 16px;
          margin-bottom: 15px;
        }
      }
    }

    .line {
      width: 1px;
      height: 300px;
      background-color: #E8E8E8;;
    }

    .codeoraccount-container {
      width: 53.3875%;

      .lable_tong {
        margin-bottom: 15px;
      }

      .other-methods-tips {
        text-align: center;
        font-size: 12px;
        color: #B0B0B0;
        line-height: 16px;
        margin-bottom: 15px;
      }

      .login-button_list {
        .login-button_item {
          width: 47.9695%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 0;

          &:hover {
            .svg-icon {
              color: white;
            }
          }

          span {
            line-height: 16px;
          }

          .svg-icon {
            width: 20px;
            height: 20px;
            margin-right: 6px;
          }
        }
      }
    }
  }
}
