<template>
  <div class='signin-bg-box'>
    <div class='container-box'>
      <div class='login-header-logo'>
        <nuxt-link to='/'>
          <img alt='' class='img_render' src='~assets/images/login-logo.png'>
        </nuxt-link>
      </div>
    </div>
    <!-- 验证码登录/账户登录/扫码登录 Start -->
    <transition name='el-zoom-in-top'>
      <div v-if='loginType === "account" || loginType==="verification"'
           class='signin-center-box sign-codeoraccount-box flex_between flex_align_center'>
        <div class='app-code-container'>
          <!-- app扫码登录 -->
          <div class='weixin-login-box'>
            <p class='title'>
              扫码登录
            </p>
            <div class='weixin-box'>
              <div id='appcode'>
                <img v-if='appbase64Code' :src='appbase64Code'>
                <div v-else class='seize'>
                  <i class='el-icon-loading'></i>
                </div>
                <div v-show='refreshCodeFlag' class='refresh-code' @click='refreshCodeHandler'>
                  <i class='el-icon-refresh'></i>
                  <span>二维码已失效,请刷新</span>
                </div>
              </div>
            </div>
            <div class='weixin-tip fontSize14'>
          <span class='tips'>
            打开脑医汇App
          </span>
              <span v-if='!apploginMessage'> 点击 <span class='themeFontColor'>「我的」</span>右上角打开<span
                class='themeFontColor'>扫一扫</span></span>
              <span v-else>{{ apploginMessage }}</span>
            </div>
          </div>
        </div>
        <div class='line'></div>
        <div class='codeoraccount-container'>
          <!-- 登录切换 -->
          <ul
            class='login-switching-list flex_start'>
            <li v-for='(item,index) in loginSwitching' :key='index'
                :class='{is_active:loginSwitchCurrent===item.id}' class='login-switching-item cursor'
                @click='loginSwitchFun(item.id,item.name)'>
              {{ item.name }}
            </li>
            <li v-if='loginType==="verification"'
                class='verificationcode-tip fontSize12'>
              未注册手机号验证后点击登录即可前往注册
            </li>
          </ul>
          <!-- 登录表单 -->
          <el-form ref='loginForm' :model='loginForm' :rules='loginRules'>
            <!-- 账号密码登录 -->
            <el-form-item v-if='loginType==="account"' key='username' prop='username'>
              <el-input v-model='loginForm.username' autocomplete='on'>
                <p slot='prepend' class='input-p'>账号:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="account"' key='password' prop='password'>
              <el-input v-model='loginForm.password' :type="passwordflag?'text':'password'" autocomplete='on'
                        spellcheck='“false”'>
                <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                          class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
                <p slot='prepend' class='input-p'>密码:</p>
              </el-input>
            </el-form-item>
            <!-- 手机验证码登录 -->
            <el-form-item v-if='loginType==="verification"' key='usernamephone1' prop='usernamephone'>
              <el-input v-model='loginForm.usernamephone' autocomplete='off' type='number'>
                <p slot='prepend' class='input-p'>+86 :</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="verification"'>
              <SliderVerification
                :reload="reloadSlider"
                @finishSlide="finishSlide"
              />
            </el-form-item>

            <el-form-item v-if='loginType==="verification"' key='captcha2' prop='captcha'>
              <el-input v-model='loginForm.captcha' autocomplete='off' type='number'>
                <p slot='prepend' class='input-p'>验证码 :</p>
                <template slot='append'>
                  <GetPhoneVerification
                    :sliderVerificationCode="sliderVerificationCode"
                    :account-number='loginForm.usernamephone'
                    @getValidationFun='getValidationFun'
                    @finishCode="reloadSlider = true"
                  />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item
              prop='remember'>
              <div class='remember_box flex_between flex_align_center'>
                <checkbox v-model='loginForm.remember' class='themeFontColor'>记住我</checkbox>
                <span v-if='loginType==="account"' class='closepassword cursor'
                      @click='loginType = "forgetPassword",analysysFun("忘记密码")'>忘记密码?</span>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button id='themeButton' class='button_item' type='primary' @click='signinFun("loginForm")'>登录
              </el-button>
            </el-form-item>
            <el-form-item key='agreement1' prop='agreement'>
              <p class='lable_tong'>
                <radio v-model='loginForm.agreement' label='1'>
                  <agreement></agreement>
                </radio>
              </p>
            </el-form-item>
          </el-form>
          <p class='other-methods-tips'>
            ——其他登录方式——
          </p>
          <!-- 微信登录/邮箱登录 -->
          <ul v-if='loginType !== "forgetPassword" && loginType !== "binding"' class='login-button_list flex_between'>
            <li v-show='loginType === "email" || loginType === "weixin" || loginType === "appcode"'
                class='login-button_item button_item cursor'
                @click='loginType = "account",loginSwitchCurrent = 1,analysysFun("账号登录")'>账号登录
            </li>
            <li v-show='loginType !== "weixin"' class='login-button_item button_item cursor' @click='weixinLoginFun()'>
              <SvgIcon class-name='siginWechat' icon-class='siginWechat'/>
              <span>微信登录</span>
            </li>
            <li v-show='loginType!=="email" && loginType!=="appcode"' class='login-button_item button_item cursor'
                @click='loginType = "email",analysysFun("邮箱登录")'>
              <SvgIcon class-name='siginEmail' icon-class='siginEmail2'/>
              <span>邮箱登录</span>
            </li>
          </ul>
          <!-- 微信登录/邮箱登录 -->
          <!-- tips buttom -->
          <div v-if='loginType !== "forgetPassword" && loginType !== "binding"' class='tip-buttom flex_between'>
            <p class='tip-register flex_start'>
              <span>没有脑医汇账号？</span>
              <nuxt-link
                :to='{name:"register",query:{fallbackUrl:$route.query.fallbackUrl,source:$route.query.source}}'>
              <span class='register fontSize16 themeFontColor fontWeight'
                    @click='$store.commit("editIdentityInformationFun","Register"),analysysFun("注册")'>注册</span>
              </nuxt-link>
            </p>
            <nuxt-link :to='{name:"overseaslogin"}'>
              <p class='tip-Overseas fontSize14 cursor' @click="analysysFun('Overseas user login')">
                Overseas user login>
              </p>
            </nuxt-link>
          </div>
          <!-- close -->
        </div>
        <div class='close img_radius cursor' @click='closeFun'>
          <i class='el-icon-close'></i>
        </div>
      </div>
    </transition>
    <!-- 验证码登录/账户登录/扫码登录 End -->

    <transition name='el-zoom-in-bottom'>
      <div
        v-if='loginType !== "account" && loginType!=="verification"'
        :class='{forgetPasswordBox:loginType==="forgetPassword"||loginType==="binding"}'
        class='signin-center-box'>
        <!-- 引导APP登录二维码扫码-->
        <div v-if="loginType === 'appcode'" class='guide_code_box'>
          <img alt='' class='img_cover' src='~assets/images/guide.png'>
        </div>
        <!--APP登录二维码-->
        <div v-if='isAppLogin' class='app_login_code_img' @click='appLoginFun()'>
          <tooltip class='item' content='App客户端登录' effect='light' placement='top-start'>
            <img src='~assets/images/qrcode.png'>
          </tooltip>
        </div>
        <div class='china-user-box'>
          <!-- 登录切换 -->
          <ul v-if='loginType === "email"' :style="isAppLogin?'justify-content: center':''"
              class='login-switching-list flex_start'>
            <li class='login-switching-item cursor is_active'>
              邮箱登录
            </li>
          </ul>
          <ul v-if='loginType === "weixin"' :style="isAppLogin?'justify-content: center':''"
              class='login-switching-list flex_start'>
            <li class='login-switching-item cursor is_active'>
              微信登录
            </li>
          </ul>
          <ul v-if='loginType === "binding"' :style="isAppLogin?'justify-content: center':''"
              class='login-switching-list flex_start'>
            <li class='login-switching-item cursor is_active'>
              绑定手机
            </li>
          </ul>
          <ul v-if='loginType === "appcode"' class='login-switching-list flex_start' style='justify-content: center'>
            <li class='login-switching-item cursor is_active'>
              扫码登录
            </li>
          </ul>
          <ul v-if='loginType === "forgetPassword"' :style="isAppLogin?'justify-content: center':''"
              class='login-switching-list flex_start'>
            <li class='login-switching-item cursor is_active'>
              修改密码
            </li>
          </ul>
          <!-- 微信登录 -->
          <div v-show='loginType === "weixin"' class='weixin-login-box'>
            <div class='weixin-box'>
              <div id='weixinbox'></div>
            </div>
            <p class='weixin-tip fontSize14'>
              请使用微信扫描二维码登录“脑医汇”
            </p>
          </div>
          <!-- app扫码登录 -->
          <div v-show='loginType === "appcode"' class='weixin-login-box'>
            <div class='weixin-box'>
              <div id='appcode'>
                <div v-show='refreshCodeFlag' class='refresh-code' @click='refreshCodeHandler'>
                  <i class='el-icon-refresh'></i>
                  <span>二维码已失效,请刷新</span>
                </div>
                <img v-if='appbase64Code' :src='appbase64Code'>
                <div v-else class='seize'>
                  <i class='el-icon-loading'></i>
                </div>
              </div>
            </div>
            <p class='weixin-tip fontSize14'>
              <span v-if='!apploginMessage'> 请使用 <span
                class='themeFontColor'>脑医汇客户端</span> 扫描二维码登录</span>
              <span v-else>{{ apploginMessage }}</span>
            </p>
          </div>
          <!-- 登录表单 -->
          <el-form ref='loginForm' :model='loginForm' :rules='loginRules'>
            <!-- 微信登录绑定手机密码 -->
            <el-form-item v-if='loginType==="binding"' prop='weixinphone'>
              <el-input v-model='loginForm.weixinphone' autocomplete='off' type='number'>
                <p slot='prepend' class='input-p'>手机号:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="binding"' prop='captcha'>
              <el-input v-model='loginForm.captcha' autocomplete='off' spellcheck='“false”' type='number'>
                <p slot='prepend' class='input-p'>验证码:</p>
                <template slot='append'>
                  <get-verification :account-number='loginForm.weixinphone'
                                    @getValidationFun='getWeChatCodeFun'></get-verification>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="binding" && false' prop='weixinpassword'>
              <el-input v-model='loginForm.weixinpassword' :type="passwordflag?'text':'password'" autocomplete='off'>
                <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                          class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
                <p slot='prepend' class='input-p'>设置密码:</p>
              </el-input>
            </el-form-item>
            <!-- 邮箱密码登录 -->
            <el-form-item v-if='loginType==="email"' prop='email'>
              <el-input v-model='loginForm.email' autocomplete='on'>
                <p slot='prepend' class='input-p'>邮箱:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="email"' prop='emailpassword'>
              <el-input v-model='loginForm.emailpassword' :type="passwordflag?'text':'password'" autocomplete='off'>
                <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                          class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
                <p slot='prepend' class='input-p'>密码:</p>
              </el-input>
            </el-form-item>
            <el-form-item
              v-if='loginType !== "weixin" &&  loginType!=="forgetPassword" && loginType !== "binding" && loginType!=="appcode"'
              prop='remember'>
              <div class='remember_box flex_between flex_align_center'>
                <checkbox v-model='loginForm.remember' class='themeFontColor'>记住我</checkbox>
                <span v-if='loginType==="account"' class='closepassword cursor'
                      @click='loginType = "forgetPassword",analysysFun("忘记密码")'>忘记密码?</span>
              </div>
            </el-form-item>
            <!-- 忘记密码 -->
            <el-form-item v-if='loginType==="forgetPassword"' prop='phoneAndEmail'>
              <el-input v-model='loginForm.phoneAndEmail' autocomplete='off'>
                <p slot='prepend' class='input-p'>手机号/邮箱:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="forgetPassword"'>
              <SliderVerification
                :reload="reloadSlider"
                @finishSlide="finishSlide"
              />
            </el-form-item>
            <el-form-item v-if='loginType==="forgetPassword"' key='captcha' prop='captcha'>
              <el-input v-model='loginForm.captcha' autocomplete='off' type='number'>
                <p slot='prepend' class='input-p'>验证码:</p>
                <template slot='append'>
                  <GetPhoneVerification
                    :sliderVerificationCode="sliderVerificationCode"
                    :account-number='loginForm.phoneAndEmail'
                    @getValidationFun='getValidationFun'
                    @finishCode="reloadSlider = true"
                  />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="forgetPassword"' key='passwordForget' prop='passwordForget'>
              <el-input v-model='loginForm.passwordForget' :type="passwordflag?'text':'password'" autocomplete='off'>
                <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                          class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
                <p slot='prepend' class='input-p'>设置密码:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType==="forgetPassword"' key='confirmpassword' prop='confirmpassword'>
              <el-input v-model='loginForm.confirmpassword' :type="newpasswordflag?'text':'password'"
                        autocomplete='off'>
                <svg-icon slot='suffix' :icon-class="!newpasswordflag?'hidepassword':'showpassword'"
                          class-name='passwordflag cursor' @click='newpasswordflag=!newpasswordflag'></svg-icon>
                <p slot='prepend' class='input-p'>确认密码:</p>
              </el-input>
            </el-form-item>
            <el-form-item v-if='loginType === "forgetPassword"'>
              <el-button id='themeButton' class='button_item' type='primary' @click='signinFun("loginForm")'>修改密码
              </el-button>
            </el-form-item>
            <!-- 忘记密码 -->
            <el-form-item
              v-if='loginType !== "weixin" && loginType !== "forgetPassword" && loginType !== "binding"  && loginType !== "appcode"'>
              <el-button id='themeButton' class='button_item' type='primary' @click='signinFun("loginForm")'>登录
              </el-button>
            </el-form-item>
            <!-- 微信登录绑定手机 -->
            <el-form-item v-if='loginType === "binding"'>
              <el-button id='themeButton' class='button_item weixinLogin' type='primary'
                         @click='signinFun("loginForm")'>
                绑定
              </el-button>
            </el-form-item>
            <el-form-item v-if='loginType === "binding"' key='agreement1' prop='agreement'>
              <p class='lable_tong' style='margin-bottom: 0'>
                <radio v-model='loginForm.agreement' label='1'>
                  <agreement></agreement>
                </radio>
              </p>
            </el-form-item>
            <el-form-item v-if='loginType === "account"' key='agreement1' prop='agreement'>
              <p class='lable_tong'>
                <radio v-model='loginForm.agreement' label='1'>
                  <agreement></agreement>
                </radio>
              </p>
            </el-form-item>
            <el-form-item v-if='loginType === "verification"' key='agreement2' prop='agreement'>
              <p class='lable_tong'>
                <radio v-model='loginForm.agreement' label='1'>
                  <agreement></agreement>
                </radio>
              </p>
            </el-form-item>
            <el-form-item v-if='loginType === "email"' key='agreement3' prop='agreement'>
              <p class='lable_tong'>
                <radio v-model='loginForm.agreement' label='1'>
                  <agreement></agreement>
                </radio>
              </p>
            </el-form-item>
          </el-form>
          <!-- 微信登录/邮箱登录 -->
          <ul v-if='loginType !== "forgetPassword" && loginType !== "binding"' class='login-button_list'>
            <li v-show='loginType === "email" || loginType === "weixin" || loginType === "appcode"'
                class='login-button_item button_item cursor'
                @click='loginType = "account",loginSwitchCurrent = 1,analysysFun("账号登录")'>账号登录
            </li>
            <li v-show='loginType !== "weixin"' class='login-button_item button_item cursor' @click='weixinLoginFun()'>
              微信登录
            </li>
            <li v-show='loginType!=="email" && loginType!=="appcode"' class='login-button_item button_item cursor'
                @click='loginType = "email",analysysFun("邮箱登录")'>
              邮箱登录
            </li>
          </ul>
          <!-- 微信登录/邮箱登录 -->
          <!-- tips buttom -->
          <div v-if='loginType !== "forgetPassword" && loginType !== "binding"' class='tip-buttom flex_between'>
            <p class='tip-register'>
              <span>没有脑医汇账号？</span>
              <nuxt-link :to='{name:"register"}'>
              <span class='register fontSize16 themeFontColor'
                    @click='$store.commit("editIdentityInformationFun","Register"),analysysFun("注册")'>注册</span>
              </nuxt-link>
            </p>
            <nuxt-link :to='{name:"overseaslogin"}'>
              <p class='tip-Overseas fontSize14 cursor' @click="analysysFun('Overseas user login')">
                Overseas user login>
              </p>
            </nuxt-link>
          </div>
          <div v-if='loginType === "forgetPassword"' class='tip-buttom flex_center'>
            <p class='tip-register'>
            <span class='register fontSize14 fontWeight cursor'
                  @click='loginType="account",analysysFun("去登录")'>去登录~</span>
            </p>
          </div>
          <!-- 微信登录/邮箱登录 -->
          <!-- close -->
          <div class='close img_radius cursor' @click='closeFun'>
            <i class='el-icon-close'></i>
          </div>
        </div>
      </div>
    </transition>
    <!-- 邮箱登录/微信登录/忘记密码 Start-->

    <!-- 邮箱登录/微信登录/忘记密码 End -->
  </div>
</template>

<script>
import env from '/env-module'
import {Checkbox, Radio, Tooltip} from 'element-ui'
import Agreement from '@/components/PageComponents/Agreement/Agreement'
import {
  bindWeChatUserPhone,
  captchaLogin,
  forgetEmailPassword,
  forgetPassword,
  getEmailForgetPasswordCaptcha,
  getPhoneCaptcha,
  getPhoneForgetPasswordCaptcha,
  getQrCode,
  login
} from '@/api/login'
import validate from '@/validate/form-validate'
import GetVerification from '@/components/GetVerification/GetVerification'
import GetPhoneVerification from '@/components/GetVerification/GetPhoneVerification'
import {userInfo} from '@/api/user'
import SliderVerification from "../../components/GetVerification/SliderVerification.vue";
// 验证码组件

export default {
  head() {
    return {
      title: '登录',
    }
  },
  components: {
    SliderVerification,
    Checkbox,
    Radio,
    GetVerification,
    GetPhoneVerification,
    Agreement,
    Tooltip
  },
  mounted() {
    var _self = this
    this.appLoginFun()
    document.onkeydown = function (e) {
      var key = window.event.keyCode
      if (key === 13) {
        _self.signinFun('loginForm')
      }
    }
    if (this.$route.query.loginType === 'binding') {
      this.loginType = this.$route.query.loginType
    } else {
      this.loginType = 'verification'
    }
  },
  computed: {
    /**
     * app显示二维码
     */
    isAppLogin() {
      return this.loginType === 'account' || this.loginType === 'verification' || this.loginType === 'weixin' || this.loginType === 'email'
    }
  },
  name: 'index',
  watch: {
    // 切换账号情况表单
    loginType() {
      // 初始化滑块
      this.sliderVerificationCode = {}

      this.loginForm = {
        remember: true
      }
    }
  },
  watchQuery: ['code'],
  data() {
    var confirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback({tip: '请再次输入密码', isEnable: false})
      } else if (value !== this.loginForm.passwordForget) {
        callback({tip: '两次密码不一致', isEnable: false})
      } else {
        callback({isEnable: true})
      }
    }
    return {
      reloadSlider: false,
      sliderVerificationCode: {},
      loginType: 'verification',// 登录状态  account账号密码登录 verification验证码登录 email邮箱密码登录 forgetPassword忘记密码 binding绑定手机 weixin微信登录
      passwordflag: false,// 密码查看
      newpasswordflag: false,// 密码查看
      loginAccountType: '',// 用户是手机登录还是邮箱登录
      loginSwitching: [
        {name: '验证码登录', id: 2},
        {name: '账号登录', id: 1}
      ],// 登录选择
      loginSwitchCurrent: 2,// 登录选择下标
      loginForm: {
        remember: true
      }, // 登录表单,
      loginRules: {
        username: [
          {validator: validate.elformValidate().PhoneAndEmail, trigger: 'change'}
        ],
        password: [
          {validator: validate.elformValidate().Paswword, trigger: 'change'}
        ],
        agreement: [
          {validator: validate.elformValidate().Agreement, trigger: 'change'}
        ],
        captcha: [
          {validator: validate.elformValidate().Captcha, trigger: 'change'}
        ],
        usernamephone: [
          {validator: validate.elformValidate().Phone, trigger: 'change'}
        ],
        phoneAndEmail: [
          {validator: validate.elformValidate().PhoneAndEmail, trigger: 'change'}
        ],
        confirmpassword: [
          {validator: confirmPassword, trigger: 'change'}
        ],
        passwordForget: [
          {validator: validate.elformValidate().RegisterPaswword, trigger: 'change'}
        ],
        email: [
          {validator: validate.loginformValidate().Email, trigger: 'change'}
        ],
        emailpassword: [
          {validator: validate.elformValidate().Paswword, trigger: 'change'}
        ],
        weixinpassword: [
          {validator: validate.elformValidate().RegisterPaswword, trigger: 'change'}
        ],
        weixinphone: [
          {validator: validate.loginformValidate().Tel, trigger: 'change'}
        ]
      },// 表单验证
      appbase64Code: '', // app二维码
      appsessionId: '',  // app标识id
      apploginMessage: '', // app登录提示语
      refreshCodeFlag: false, // app二维码是否失效
      timer: null
    }
  },
  methods: {
    finishSlide(data) {
      this.sliderVerificationCode = {
        sessionId: data.sessionId,
        sig: data.sig,
        token: data.token,
      }
      this.reloadSlider = false
    },
    // 长轮询监听app扫码状态
    startQrCodeListener(sessionId) {
      // eslint-disable-next-line no-undef
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.refreshCodeFlag = true
      }, 3 * 60 * 1000)
      let timestamp
      let sseObj
      //发送Http长连接
      sseObj = new EventSource(`/wapi/qr/getSessionStatus.do?sessionId=` + sessionId)
      //回调方法
      sseObj.onmessage = (msg) => {
        let resultJson = JSON.parse(msg.data)
        //session状态
        let sessionStatus = resultJson.status
        //等于-1表示sessionId过期
        if (sessionStatus === -1) {
          this.$toast('二维码过期,请刷新页面')
          this.apploginMessage = '二维码过期,请刷新页面'
          sseObj.close()
        } else if (sessionStatus === 2) {
          let realName = resultJson.realName
          let token = resultJson.token
          /**
           * 保存token
           */

          this.$cookies.set('medtion_token_only_sign', token, {sameSite: 'lax'})
          this.$axios.$request(userInfo()).then(res => {
            /**
             * 如果通过token获取到用户信息 就代表有
             */
            if (res && res.code === 1) {
              // 已完善信息 获取 为完善信息 但是有身份 都可以进行登录
              this.$store.commit('auth/login', {
                user: res.result,
                remember: true,
                this: this,
                token: this.$cookies.get('medtion_token_only_sign')
              })
              this.apploginMessage = realName + '已登录'
            }
          })
          sseObj.close()
        } else if (sessionStatus === 1) {
          let realName = resultJson.realName
          this.apploginMessage = realName + '已扫码'
        }

      }

      sseObj.onerror = (evt) => {
        console.log('sse错误, 连接时间:' + (new Date().getTime() - timestamp))
        sseObj.close()
        this.$axios.$request(getQrCode()).then(res => {
          this.appbase64Code = res.result.base64Code
          this.appsessionId = res.result.sessionId
          this.startQrCodeListener(res.result.sessionId)
        })
      }

      sseObj.onopen = (evt) => {
        timestamp = new Date().getTime()
        console.log('sse连接成功')
      }
    },
    // app扫码登录
    appLoginFun() {
      this.loginType = 'appcode'
      this.$axios.$request(getQrCode()).then(res => {
        this.appbase64Code = res.result.base64Code
        this.appsessionId = res.result.sessionId
        this.startQrCodeListener(res.result.sessionId)
      })
    },
    /**
     * app二维码刷新
     * @param name
     */
    refreshCodeHandler() {
      this.$axios.$request(getQrCode()).then(res => {
        this.appbase64Code = res.result.base64Code
        this.appsessionId = res.result.sessionId
        this.refreshCodeFlag = false
        this.startQrCodeListener(res.result.sessionId)
      })
    },
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    },
    // 关闭登录
    closeFun() {
      this.$analysys.btn_click('关闭登录', document.title)
      const forcelLogin = this.$route.query.hasOwnProperty("ForceLogin")

      if (forcelLogin && !this.$store.state.auth.token) {
        window.location.replace('/')
        return
      }

      if (this.$route.query.thirdParty) {
        window.history.back()
      } else if (this.$route.query.fallbackUrl || this.$route.query.backurl) {
        window.location.replace(this.$route.query.fallbackUrl || this.$route.query.backurl)
      } else {
        window.location.replace('/')
      }
    },
    // 获取微信绑定手机验证码
    getWeChatCodeFun(item, callback) {
      this.$axios.$request(getPhoneCaptcha({
        phone: this.loginForm.weixinphone
      })).then(res => {
        return callback(true)
      })
    },
    // 获取验证码
    getValidationFun(item, callback) {
      this.reloadSlider = false;
      this.$analysys.btn_click('获取验证码', document.title)
      this.loginAccountType = item
      if (item === 'tel') {
        // 并且登录类型为微信登录
        this.$axios.$request(getPhoneForgetPasswordCaptcha({
          phone: this.loginType === 'verification' ? this.loginForm.usernamephone : this.loginForm.phoneAndEmail,
          sessionId: this.sliderVerificationCode.sessionId,
          sig: this.sliderVerificationCode.sig,
          token: this.sliderVerificationCode.token
        })).then(res => {
          if (res && res.code === 1) {
            return callback(true)
          }
        })
      } else if (item === 'email') {
        this.$axios.$request(getEmailForgetPasswordCaptcha({
          email: this.loginType === 'verification' ? this.loginForm.usernamephone : this.loginForm.phoneAndEmail
        })).then(res => {
          if (res && res.code === 1) {
            console.log('获取验证码成功')
            return callback(true)
          }
        })
      }
    },
    // 登录选择切换
    loginSwitchFun(index, name) {
      this.$analysys.btn_click(name, document.title)
      this.loginSwitchCurrent = index
      switch (index) {
        case 1: {
          this.loginType = 'account'
          break
        }
        default: {
          this.loginType = 'verification'
        }
      }
    },
    // 登录
    signinFun(formName) {
      this.$analysys.btn_click('登录', document.title)
      // 为了按顺序验证
      const storage = []
      let flag = true
      this.$refs[formName].validateField(['username', 'password', 'usernamephone', 'phoneAndEmail', 'passwordForget', 'confirmpassword', 'email', 'emailpassword', 'weixinphone', 'weixinpassword', 'captcha', 'agreement'], (valid) => {
        storage.push(valid)
      })
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false
          this.$toast.fail(storage[i].tip)
          return
        }
      }
      if (flag) {
        switch (this.loginType) {
          // 账号密码登录
          case 'account': {
            this.$axios.$request(login({
              username: this.loginForm.username,
              password: this.loginForm.password,
              loginType: 'T'
            })).then(res => {
              if (res && res.code === 1) {
                // 已完善信息 获取 为完善信息 但是有身份 都可以进行登录
                this.$store.commit('auth/login', {
                  token: res.result.token,
                  user: res.result.user,
                  remember: this.loginForm.remember,
                  this: this
                })


              }
            })
            break
          }
          // 验证码登录
          case 'verification': {
            this.$axios.$request(captchaLogin({
              username: this.loginForm.usernamephone,
              captcha: this.loginForm.captcha
            })).then(res => {
              if (res && res.code === 1) {
                // 已完善信息 获取 为完善信息 但是有身份 都可以进行登录
                this.$store.commit('auth/login', {
                  token: res.result.token,
                  user: res.result.user,
                  remember: this.loginForm.remember,
                  this: this
                })


              }
            })
            break
          }
          // 邮箱密码登录
          case 'email': {
            this.$axios.$request(login({
              username: this.loginForm.email,
              password: this.loginForm.emailpassword
            })).then(res => {
              if (res && res.code === 1) {

                // 已完善信息 获取 为完善信息 但是有身份 都可以进行登录
                this.$store.commit('auth/login', {
                  token: res.result.token,
                  user: res.result.user,
                  remember: this.loginForm.remember,
                  this: this
                })


              }
            })
            break
          }
          // 忘记密码
          case 'forgetPassword': {
            if (this.loginAccountType === 'tel') {
              this.$axios.$request(forgetPassword({
                phone: this.loginForm.phoneAndEmail,
                captcha: this.loginForm.captcha,
                newPassword: this.loginForm.passwordForget
              })).then(res => {
                if (res && res.code === 1) {
                  this.$toast({
                    message: '密码修改成功',
                    type: 'success',
                    duration: 1000,
                    onClose: () => {
                      this.loginType = 'account'
                    }
                  })
                }
              })
            } else if (this.loginAccountType === 'email') {
              this.$axios.$request(forgetEmailPassword({
                email: this.loginForm.phoneAndEmail,
                captcha: this.loginForm.captcha,
                newPassword: this.loginForm.passwordForget
              })).then(res => {
                if (res && res.code === 1) {
                  this.$toast({
                    message: '密码修改成功',
                    type: 'success',
                    duration: 1000,
                    onClose: () => {
                      this.loginType = 'account'
                    }
                  })
                }
              })
            }
            break
          }
          // 微信登录绑定手机
          case 'binding': {
            this.$axios.$request(bindWeChatUserPhone({
              unionid: this.$store.state.auth.weChatInfo.unionid,
              username: this.loginForm.weixinphone,
              captcha: this.loginForm.captcha,
              nickname: this.$store.state.auth.weChatInfo.nickname
            })).then(res => {
              if (res && res.code === 1 && res.result) {

                // 已完善信息 获取 为完善信息 但是有身份 都可以进行登录
                this.$store.commit('auth/login', {
                  token: res.result.token,
                  user: res.result.user,
                  remember: this.loginForm.remember,
                  this: this
                })
              } else {
                // 如果该手机号未注册 就跳到注册页面
                this.$toast(res.message)
                if (res.code === 1 && !res.result) {
                  this.$store.commit('editAccountTypeFun', 'tel')
                  this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                  this.$router.push({
                    name: 'register',
                    query: {wechat_perfect: 'T', phone: this.loginForm.weixinphone}
                  })
                }
              }
            })
            break
          }
        }
      }
    },
    // 微信登录
    weixinLoginFun() {
      this.$analysys.btn_click('微信登录', document.title)
      this.loginType = 'weixin'
      // const callBackUrlname = encodeURIComponent(encodeURIComponent(this.$store.state.back_url))
      const callBackUrlname = encodeURIComponent(encodeURIComponent(`//${window.location.host}/transfer?${this.$route.query.thirdParty ? '&thirdParty=true&' : ''}fallbackUrl=${this.$route.query.fallbackUrl || this.$route.query.backurl}`))
      const DDNS_URL = encodeURIComponent('http://www.brainmed.com/wxweb/redirect.jspx')
      // eslint-disable-next-line no-undef
      setTimeout(() => {
        window.WxLogin({
          self_redirect: false, // 	true：手机点击确认登录后可以在 iframe 内跳转到 redirect_uri，false：手机点击确认登录后可以在 top window 跳转到 redirect_uri。默认为 false。
          id: 'weixinbox', // 第三方页面显示二维码的容器id
          appid: 'wx204e9888a42c9142', // 应用唯一标识，在微信开放平台提交应用审核通过后获得
          scope: 'snsapi_login', // 应用授权作用域，拥有多个作用域用逗号（,）分隔，网页应用目前仅填写snsapi_login即可
          redirect_uri: DDNS_URL, // 重定向地址，需要进行UrlEncode
          state: callBackUrlname, // 用于保持请求和回调的状态，授权请求后原样带回给第三方。该参数可用于防止csrf攻击（跨站请求伪造攻击），建议第三方带上该参数，可设置为简单的随机数加session进行校验
          style: 'black', // 提供"black"、"white"可选，默认为黑色文字描述。详见文档底部FAQ
          href: 'data:text/css;base64,Ly8g6Ieq5a6a5LmJ5LqM57u056CB5qC35byPCiN0cGxfZm9yX2lmcmFtZXsKd2lkdGg6MTAwJTsKaGVpZ2h0OjEwMCUKfQojdHBsX2Zvcl9pZnJhbWUgLnFyY29kZSB7CndpZHRoOjEwMCU7CmhlaWdodDoxMDAlCn0KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciB7CmRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnFyY29kZSB7CmJvcmRlcjogbm9uZTsKbWFyZ2luOjA7CndpZHRoOiAxMDAlOwpoZWlnaHQ6IDEwMCU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1c3sKZGlzcGxheTogbm9uZQp9Ci5pbXBvd2VyQm94IC50aXRsZXsKZGlzcGxheTpub25lCn0'
        })
        document.getElementsByTagName('iframe')[0].setAttribute('sandbox', 'allow-scripts allow-top-navigation')
        document.getElementsByTagName('iframe')[0].style.cssText = 'width:100%;height:100%'
      }, 0)
    }
  }
}
</script>
<style lang='less'>
/* message box */
.personaldata-messagebox {
  width: 372px;
  padding: 36px 0 40px;
  text-align: center;
  border-radius: 30px;
  box-sizing: border-box;

  p {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
    padding: 0 50px;
  }
}

.personaldata-messagebox > .el-message-box__content {
  padding: 0;
  text-align: center;
}

.personaldata-messagebox > .el-message-box__btns {
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  margin-top: 30px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button {
  float: left;
  width: 120px;
  height: 38px;
  line-height: 38px;
  padding: 0 !important;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--small {
  background: #EFEFEF;
  border-radius: 12px;
  border: 0;
  color: #80A4B9;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--primary {
  background: #0581CE;
  color: #FFFFFF;
}
</style>
<style lang='less' scoped>
@import "~@/pages/signin/index.less";
</style>
