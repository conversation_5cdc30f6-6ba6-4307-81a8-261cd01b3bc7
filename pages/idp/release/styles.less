
.release_page {
  min-height: calc(100vh - 24px - 32px);
  background: #EEF2F3;
  padding: 0 0 160px;

  .release_header_wrapper {
    background: #FFF;
    margin-bottom: 24px;

    .header_container {
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header_left {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      font-weight: 600;

      img {
        width: 72px;
        margin-right: 24px;
      }
    }

    .header_right {
      /deep/ span {
        color: #333 !important;
      }
    }
  }

  .release_success_mask_wrapper {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.30);

    .release_success_container {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border-radius: 16px;
      background: #FFF;
      padding: 24px;
      box-sizing: border-box;

      min-width: 357px;
      min-height: 260px;
      display: flex;
      flex-flow: column;
      align-items: center;
      justify-content: space-between;

      .container_top {
        display: flex;
        flex-flow: column;
        align-items: center;

        img {
          width: 80px;
          height: 80px;
        }

        p {
          font-size: 14px;
          line-height: 1.5;
        }

        .name {
          text-align: center;
          width: 150px;
          color: #333;
          font-size: 20px;
          font-weight: 600;
          margin: 20px 0 8px;
          padding-left: 15px;
        }
      }

      .view_btn {
        border-radius: 100px;
        background: linear-gradient(270deg, #0581CE -14.56%, #32B7FA 100%);
        width: 205px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: #FFF;
      }
    }
  }

  .release_bottom_wrapper {
    width: 100%;
    background: #FFF;;
    border-top: 1px solid #EEE;
    position: fixed;
    bottom: 0;

    .release_bottom_container {
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: end;

      .release_btn {
        width: 80px;
        height: 36px;
        border-radius: 4px;
        background: #0581CE;
        color: #FFF;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
        user-select: none;
      }

      .release_btn:hover {
        background-color: #66b1ff;
        cursor: pointer;
      }

      .release_btn:active {
        background: #0581CE;
      }

      .not_release_btn {
        background: #82C0E7 !important;
        cursor: not-allowed !important;
      }
    }

  }


  .input_label {
    min-width: 100px;
    color: #333;
    font-size: 14px;
    line-height: 28px;
    position: relative;
    flex-shrink: 0;
    margin-right: 15px;
  }

  .input__label {
    min-width: 100px;
    color: #333;
    font-size: 14px;
    line-height: 28px;
    position: relative;
    flex-shrink: 0;
    margin-right: 15px;

    &::before {
      content: '*';
      color: #F23C17;
      position: absolute;
      left: -8px;
      top: 0;
    }
  }


  .release_content_wrapper {
    border-radius: 8px;
    background: #FFF;

    .release_title_container {
      border-bottom: 1px solid #EEE;

      /deep/ .el-input__inner {
        height: 72px;
        border: none;
        font-size: 24px;
        outline: none;
        padding: 0 24px;

        ::placeholder {
          font-size: 24px;
          color: #999;
        }
      }
    }

    .release_type_container {
      padding: 24px 24px;
      border-bottom: 1px solid #EEE;
      display: grid;
      gap: 10px;

      .type_wrapper {
        display: flex;
        align-items: start;

        .type_list {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .type_item {
            min-width: 92px;
            padding: 0 10px;
            height: 28px;
            border-radius: 18px;
            background: #F5FCFF;
            font-size: 13px;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            user-select: none;
            cursor: pointer;
          }

          .type_item_active {
            background: #0581CE;
            color: #FFF;
          }
        }
      }
    }

    .demand_container {
      padding: 24px 20px;
      border-bottom: 1px solid #EEE;


      /deep/ .el-textarea__inner {
        border: none;
        outline: none;
        min-height: 160px !important;
        padding: 10px 0 14px;
        font-size: 16px;
        color: #333;

        ::placeholder {
          font-size: 16px;
        }
      }

      .upload_list_wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 14px;

        /deep/ .el-progress-circle {
          width: 120px !important;
          height: 120px !important;
        }

        /deep/ .el-icon-plus {
          font-size: 28px;
          color: #8c939d;
          width: 120px;
          height: 120px;
          line-height: 120px;
          text-align: center;
          background: #EEEEEE;
          border-radius: 4px;
        }

        .upload_item {
          width: 120px;
          height: 120px;
          border-radius: 4px;
          overflow: hidden;
          background: #EEEEEE;
        }

        .image_wrapper {
          position: relative;

          .delete_btn {
            position: absolute;
            right: 3px;
            top: 3px;
            background: rgba(51, 51, 51, 0.9);
            border-radius: 50%;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            .delete_btn_icon {
              width: 10px;
              height: 10px;
            }
          }

          .image {
            width: 100%;
            height: 100%;
            display: block;
            object-fit: cover;
          }
        }


        .video_wrapper {
          background: rgba(0, 0, 0, .3);
          position: relative;
          cursor: pointer;

          .delete_btn {
            position: absolute;
            right: 3px;
            top: 3px;
            background: rgba(51, 51, 51, 0.9);
            border-radius: 50%;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;

            .delete_btn_icon {
              width: 10px;
              height: 10px;
            }
          }

          .icons {
            width: 40px;
            height: 40px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }

          video {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .release_module_wrapper {
      padding: 24px 20px;
      border-bottom: 1px solid #EEE;
      display: grid;
      gap: 14px;

      .module_item {
        display: flex;
        align-items: center;
      }
    }
  }
}
