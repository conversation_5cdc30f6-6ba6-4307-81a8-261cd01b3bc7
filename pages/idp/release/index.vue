<template>
  <div class="release_page">
    <div class="release_header_wrapper">
      <Container>
        <div class="header_container">
          <div class="header_left">
            <a target="_self" href="/idp"> <img alt='LOGO' src='~assets/images/logo2.jpg'></a>
            <span>产业对接</span>
          </div>
          <div class="header_right">
            <UserLoginStatus/>
          </div>
        </div>
      </Container>

    </div>
    <Container>
      <div class="release_content_wrapper">
        <div class="release_title_container">
          <el-input :maxlength="100" v-model="title" placeholder="请输入需求标题，最多100字"></el-input>
        </div>

        <div class="release_type_container">
          <div class="type_wrapper">
            <div class="input__label">类型（可多选）</div>
            <div class="type_list">
              <template v-for="item in typeList">
                <div
                  v-if="item.children.length === 0"
                  :key="item.id"
                  class="type_item"
                  :class="typeIdsMap[item.id] ?'type_item_active' :''"
                  @click="typeIdsMap[item.id] ? $delete(typeIdsMap, item.id) :$set(typeIdsMap, item.id, 1) "
                >{{ item.name }}
                </div>

                <div
                  v-else
                  v-for="itemChild in item.children"
                  :key="itemChild.id"
                  class="type_item"
                  :class="typeIdsMap[itemChild.id] ?'type_item_active' :''"
                  @click="childTypeHandler(itemChild.id,item.children)"
                >{{ itemChild.name }}
                </div>
              </template>

            </div>
          </div>

          <div class="type_wrapper">
            <div class="input__label">分类（单选）</div>
            <div class="type_list">
              <div
                v-for="item in classificationList"
                :key="item.id"
                :class="classificationId === item.id ? 'type_item_active' : ''"
                class="type_item"
                @click="classificationId = item.id">{{ item.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="demand_container">
          <div class="input__label">需求详情</div>
          <el-input
            type="textarea"
            placeholder="请详细描述需求详情，详细的需求描述可以帮助用户更好地获取信息"
            v-model="requirementDetail"
            resize="none"
          >
          </el-input>

          <div class="input_label" style="margin-bottom: 14px">相关图片/视频：</div>

          <div class="upload_list_wrapper">
            <template v-for="(item,index) in fileJsonPreview">
              <div v-if="item['type'] === 0" class="upload_item image_wrapper">
                <a target="_blank" :href="item.fileUrl">
                  <img :src="item.fileUrl" alt="" class="image">
                </a>
                <div class="delete_btn" @click="deleteFile(index)">
                  <svg-icon icon-class="delete_img" class-name="delete_btn_icon"/>
                </div>
              </div>
              <div v-else-if="item['type'] === 1" class="upload_item video_wrapper">
                <a v-if="item.play" target="_blank" :href="item.fileUrl">
                  <video :src="item.fileUrl"></video>
                  <svg-icon icon-class="icon_play_n" class-name="icons"/>
                </a>
                <div style="width: 100%;height: 100%" v-else @click="$toast('发布成功后即可预览')">
                  <video :src="item.fileUrl"></video>
                  <svg-icon icon-class="icon_play_n" class-name="icons"/>
                </div>
                <div class="delete_btn" @click="deleteFile(index)">
                  <svg-icon icon-class="delete_img" class-name="delete_btn_icon"/>
                </div>
              </div>
            </template>

            <Upload
              v-if="fileJson.length<9"
              :http-request="httpRequest"
              :show-file-list="false"
              action="string"
              class="avatar-uploader"
              accept="image/*,video/*"
              name="file">
              <i v-if="percentage >= 100" slot="default" class="el-icon-plus"></i>
              <Progress :width="120" v-else slot="default" type="circle" :percentage="percentage"></Progress>
            </Upload>
          </div>
        </div>

        <div class="release_module_wrapper">
          <div class="module_item">
            <div class="input_label">有效期</div>
            <DatePicker
              style="width: 400px"
              v-model="periodOfValidity"
              align="right"
              type="date"
              placeholder="请选择供需有效期，如不选择默认长期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
            >
            </DatePicker>
          </div>
          <div class="module_item">
            <div class="input_label">地区</div>
            <Cascader
              style="width: 400px"
              v-model="areaArr"
              :options="mapArea"
              :props="{ checkStrictly: true }"
              @change="changeArea"
              clearable></Cascader>
          </div>
        </div>

        <div class="release_module_wrapper">
          <div class="module_item">
            <div class="input__label">联系方式</div>
            <el-input style="width: 400px" v-model="contactInformation" placeholder="请输入您的联系方式"></el-input>
          </div>
          <div class="module_item">
            <div class="input__label">联系方式是否对其他注册用户可见：</div>
            <el-switch
              style="margin-left: 230px"
              v-model="contactInformationVisible"
              active-color="#0581CE"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </div>
        </div>

        <div class="release_type_container" style="gap: 20px">
          <div class="type_wrapper">
            <div class="input__label">发布主体</div>
            <div class="type_list">
              <div
                class="type_item"
                :class="publishSubject === 0 ?'type_item_active' :''"
                @click="publishSubject = 0"
              >单位
              </div>
              <div
                class="type_item"
                :class="publishSubject === 1 ?'type_item_active' :''"
                @click="publishSubject = 1"
              >个人
              </div>
            </div>
          </div>

          <div v-if="publishSubject === 1" class="type_wrapper" style="align-items: center">
            <div class="input__label">发布人姓名</div>
            <el-input style="width: 400px" :maxlength="30" v-model="publishName"
                      placeholder="请输入发布人姓名"></el-input>
          </div>
        </div>
      </div>
    </Container>

    <div class="release_bottom_wrapper">
      <Container>
        <div class="release_bottom_container">
          <Tooltip popper-class="release_error_tooltip" effect="light" :disabled="errorMsg === ''" :content="errorMsg"
                   placement="top-end">
            <div :class="checkStatus === 0 ? 'not_release_btn' : ''"
                 class="release_btn"
                 @click="releaseHandler">{{ infoDetail ? '修改' : '发布' }}
            </div>
          </Tooltip>

        </div>
      </Container>
    </div>

    <div v-if="detailId" class="release_success_mask_wrapper">
      <div class="release_success_container">
        <div class="container_top">
          <img src="~assets/images/industrial/sub_sucess.png" alt="">
          <p class="name">提交成功！</p>

          <!--          <p>对接信息已经提交，请等待审核。</p>-->
          <!--          <p>审核通过后，您的对接信息将会对其他用户可见。</p>-->
        </div>

        <a target="_self" :href="`/idp/detail/${detailId}`" class="view_btn">查看详情</a>
      </div>
    </div>


    <identity-authentication
      :authentication-data-info="$store.state.authenticationDataInfo"
      :authentication-dialog.sync="$store.state.authenticationDialog"
      :identity-current-father="$store.state.auth.user.identity"
      :identity-flag="!!$store.state.auth.user.identity"
      :reselect-flag="false"
      @editFlag="authenticationDialogFun"
    ></identity-authentication>
  </div>
</template>

<script>
import {DatePicker, Cascader, Switch, Tooltip, Upload, Progress} from "element-ui";
import {Container} from "../../../opt-components/template/index.js";
import {
  getAreaList,
  getClassificationList,
  getTypeList,
  publishDockingInformation,
  getIdpInformationDetail, updateDockingInformation
} from "../../../api/docking.js";
import UserLoginStatus from "../../../opt-components/template/Header/module/UserLoginStatus/index.vue";
import IdentityAuthentication from '@/components/IdentityAuthentication/IdentityAuthentication'
import {uploadOssImage} from "../../../api/upload.js";
import aliyunUpload from "../../../assets/js/aliyunUpload.js";
import {userInfo} from "../../../api/user.js";

export default {
  name: "IndustryDockingRelease",
  components: {
    Container,
    DatePicker,
    Cascader,
    'el-switch': Switch,
    Tooltip,
    UserLoginStatus,
    IdentityAuthentication,
    Upload,
    Progress
  },
  async asyncData({app, params, error, store, query, req, redirect, route}) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [TypeListData, classificationData, AreaList, UserInfo] = await Promise.all([
      app.$axios.$request(getTypeList()),
      app.$axios.$request(getClassificationList()),
      app.$axios.$request(getAreaList()),
      app.$axios.$request(userInfo())
    ])

    const mapArea = AreaList.list.map(item => {
      return {
        value: item.name,
        label: item.name,
        children: item.children.map(itemChild => {
          return {
            value: itemChild.name,
            label: itemChild.name,
          }
        })
      }
    })

    let infoDetail = null
    if (query.informationId) {
      infoDetail = await app.$axios.$request(getIdpInformationDetail({
        informationId: query.informationId,
        loginUserId: store.state.auth.user.id,
      }))

      if (infoDetail.result.publisher.id !== store.state.auth.user.id) {
        redirect("/idp")
      }
    }

    return {
      typeList: TypeListData.list,
      classificationList: classificationData.list,
      mapArea,
      infoDetail,
      userInfo: UserInfo.result
    }
  },
  head() {
    return {
      title: "产业对接-发布",
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '产业对接-发布'
        }],
      script: [
        {src: '/js/aliyun-upload-sdk/lib/es6-promise.min.js'},
        {src: '/js/aliyun-upload-sdk/lib/aliyun-oss-sdk-6.17.1.min.js'},
        {src: '/js/aliyun-upload-sdk/aliyun-upload-sdk-1.5.6.min.js'}
      ]
    }
  },
  mounted() {

    if (this.$route.query.informationId && this.infoDetail) {
      const res = this.infoDetail.result

      this.title = res.title

      res.typeList.forEach(item => {
        this.$set(this.typeIdsMap, item.id, 1)
      })
      this.typeIdsStr = res.typeList.map(item => item.id).join()
      this.classificationId = res.classification.id
      this.requirementDetail = res.requirementDetail
      this.fileJson = res.fileList.map(item => {
        if (item.type === 0) {
          return {"type": item.type, "fileUrl": item.fileUrl}
        } else {
          return {"type": 1, "vid": item.vid}
        }
      })
      this.fileJsonPreview = res.fileList.map(item => {
        if (item.type === 0) {
          return {"type": item.type, "fileUrl": item.fileUrl}
        } else {
          return {"type": 1, "vid": item.vid, "fileUrl": item.fileUrl, "play": true}
        }
      })
      this.periodOfValidity = res.periodOfValidity
      this.province = res.province
      this.city = res.city
      this.areaArr = res.city ? JSON.parse(JSON.stringify([res.province, res.city])) : JSON.parse(JSON.stringify([res.province]))
      this.contactInformation = res.contactInformation
      this.contactInformationVisible = res.contactInformationVisible
      this.publishSubject = res.publishSubject
      this.publishName = res.publishName
    } else {
      console.log(this.userInfo)


      if (this.userInfo.identity === 4 && this.userInfo.typesOfEnterprisePersonnel === 0) {
        this.publishSubject = 0
      }

      this.contactInformation = this.userInfo.mobile || this.userInfo.email || ""
      this.publishName = this.userInfo.realName
    }


    this.checkParams()
  },
  watch: {
    typeIdsMap(newValue) {
      let ids = ''
      Object.keys(newValue).forEach(key => {
        ids += key.toString() + ',';
      });
      this.typeIdsStr = ids.substring(0, ids.length - 1)
    },

    title() {
      this.checkParams()
    },
    typeIdsStr() {
      this.checkParams()
    },
    classificationId() {
      this.checkParams()
    },
    requirementDetail() {
      this.checkParams()
    },
    contactInformation() {
      this.checkParams()
    },
    publishSubject() {
      this.checkParams()
    },
    publishName() {
      this.checkParams()
    },
  },
  data() {
    return {
      releaseLock: true,
      percentage: 100,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
        shortcuts: [{
          text: '明天',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24);
            picker.$emit('pick', date);
          }
        }, {
          text: '一周后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
            picker.$emit('pick', date);
          }
        }, {
          text: '一月后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', date);
          }
        }, {
          text: '一年后',
          onClick(picker) {
            const date = new Date();
            date.setTime(date.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', date);
          }
        }]
      },
      // 标题
      title: "",
      // hu lue
      typeIdsMap: {},
      // 类型ID字符串（英文逗号分隔）（多选）
      typeIdsStr: "",
      // 分类id
      classificationId: "",
      // 需求详情
      requirementDetail: "",
      // 文件 文件json串，例：[{"type":0,"vid":"xxx","fileUrl":"xxx"},{"type":0,"vid":"xxx","fileUrl":"xxx"}]
      fileJson: [],
      fileJsonPreview: [],
      // 有效期
      periodOfValidity: "",
      areaArr: [],
      province: "",
      city: "",
      // 联系方式
      contactInformation: "",
      // 联系方式是否可见
      contactInformationVisible: 1,
      // 发布主题
      publishSubject: 1,
      // 发布人姓名
      publishName: "",
      // 查看详情ID
      detailId: null,
      // 检查状态  0未通过 1通过
      checkStatus: 0,
      errorMsg: ""
    }
  },
  methods: {
    childTypeHandler(id, childArr) {
      childArr.forEach(item => {
        if (item.id !== id) {
          this.$delete(this.typeIdsMap, item.id)
        }
      })

      if (this.typeIdsMap[id]) {
        this.$delete(this.typeIdsMap, id)
      } else {
        this.$set(this.typeIdsMap, id, 1)
      }

    },
    deleteFile(index) {
      this.fileJson.splice(index, 1)
      this.fileJsonPreview.splice(index, 1)
    },
    httpRequest(item) {
      const _this = this;
      // 验证图片格式大小信息
      const isJPG = item.file.type === "image/jpeg" || item.file.type === "image/png";
      const isMp4 = item.file.type === 'video/mp4' || item.file.type === 'video/*';

      const isLt2M = item.file.size / 1024 / 1024 < 2;

      if (!isJPG && !isMp4) {
        this.$message.error("请上传图片或者视频!");
      }

      const fileObj = item.file;

      // 图片格式大小信息没问题 执行上传图片的方法
      if (isJPG) {
        // 定义FormData对象 存储文件
        const mf = new FormData();
        // 将图片文件放入mf
        mf.append("file", fileObj);
        mf.append("module", "idp");
        _this.percentage = 0
        _this.checkParams()
        const timer = setInterval(() => {
          const randomNum = Math.floor(Math.random() * 10)
          if (_this.percentage < 89 && _this.percentage + randomNum < 100) {
            _this.percentage += randomNum
          } else {
            clearInterval(timer)
          }
        }, 200)

        this.$axios.$request(uploadOssImage(mf)).then((res) => {
          if (res && res.code === 1) {
            const str = {"type": 0, "fileUrl": res.result.filePath}
            this.fileJson.push(str)
            this.fileJsonPreview.push(str)
            this.percentage = 100
            this.checkParams()
          }
          clearInterval(timer)
        });
      } else if (isMp4) {
        _this.percentage = 0
        _this.checkParams()
        aliyunUpload(fileObj, this.$store.state.auth.user.id, _this).then(res => {
          const reader = new FileReader()
          reader.onload = function (e) { // 文件读取成功完成后的处理
            console.log(e, "ew")
            const str = {"type": 1, "vid": res.data.videoId}
            const strPreview = {"type": 1, "vid": res.data.videoId, "fileUrl": e.target.result, "play": false}
            _this.fileJson.push(str)
            _this.fileJsonPreview.push(strPreview)
            _this.percentage = 100
            _this.checkParams()
          };

          reader.onerror = function (e) { // 文件读取出错的处理
            console.error("File could not be read! Code " + e.target.error.code);
          };

          reader.readAsDataURL(fileObj); // 读取文件为DataURL


        })
      }


      console.log(this.fileJson)
    },
    /**
     * 身份认证
     * @param data
     */
    authenticationDialogFun(data) {
      this.$store.commit('editAuthenticationDialog', data)
    },
    changeArea(areaArr) {
      this.province = areaArr[0] || ""
      this.city = areaArr[1] || ""
    },

    checkParams(req) {
      let errorMsg = ''

      if (!this.title) {
        errorMsg = "请输入需求标题"
      } else if (!this.typeIdsStr) {
        errorMsg = "请选择类型"
      } else if (!this.classificationId) {
        errorMsg = "请选择分类"
      } else if (!this.requirementDetail) {
        errorMsg = "请输入需求详情"
      } else if (!this.contactInformation) {
        errorMsg = "请输入联系方式"
      } else if (this.publishSubject === 1 && !this.publishName) {
        errorMsg = "请输入发布人姓名"
      } else if (this.percentage !== 100) {
        errorMsg = "请等待上传文件完成"
      }


      if (errorMsg) {
        if (req) {
          // this.$message({
          //   message: errorMsg,
          //   type: 'warning'
          // });
        }
        console.log("验证没有通过")
        this.checkStatus = 0
        this.errorMsg = errorMsg
        return false
      }

      this.checkStatus = 1
      this.errorMsg = ''
      console.log("验证通过")
      return true;
    },


    releaseHandler() {
      if (this.checkParams(true) && this.releaseLock) {

        this.releaseLock = false
        const params = {
          title: this.title,
          typeIdsStr: this.typeIdsStr,
          classificationId: this.classificationId,
          requirementDetail: this.requirementDetail,
          fileJson: JSON.stringify(this.fileJson),
          periodOfValidity: this.periodOfValidity,
          province: this.province,
          city: this.city,
          contactInformation: this.contactInformation,
          contactInformationVisible: this.contactInformationVisible,
          publishSubject: this.publishSubject,
          publishName: this.publishName
        }

        if (this.infoDetail) {
          this.$analysys.btn_click('修改', '产业对接发布')
          params.informationId = this.$route.query.informationId
          this.$axios.$request(updateDockingInformation(params)).then(res => {
            if (res.code === 1) {
              this.detailId = res.result.id;
            }
          })
        } else {
          this.$analysys.btn_click('发布', '产业对接发布')
          this.$axios.$request(publishDockingInformation(params)).then(res => {
            if (res.code === 1) {
              this.detailId = res.result.id;
            }
          })
        }

      }

    },
  }
}
</script>

<style>
.release_error_tooltip {
  font-size: 22px;
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
