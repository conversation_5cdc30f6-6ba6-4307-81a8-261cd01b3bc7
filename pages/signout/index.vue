<template>

</template>

<script>
export default {
  name: 'index',
  mounted() {
    this.$store.commit('auth/logout', { this: this })
    if (this.$route.query.fallbackUrl || this.$route.query.backurl) {
      // window.location.replace(`//dev.medtion.com/logout.jspx?fallbackUrl=${this.$route.query.fallbackUrl || this.$route.query.backurl}`)
      window.location.replace(this.$route.query.fallbackUrl || this.$route.query.backurl)
    } else {
      window.location.replace('/')
    }
  }
}
</script>

<style scoped>

</style>
