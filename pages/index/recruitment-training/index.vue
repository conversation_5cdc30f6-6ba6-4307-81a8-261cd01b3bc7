<template>
  <PageContainer>
    <section slot='page-left'>
      <div style='min-height: 70vh'>
        <RecruitmentTrainingTabs @changeTabHandler='changeTabHandler' />
        <SecondLevelTabs
          @changeOnlineStatusHandler='changeOnlineStatusHandler'
          @filterCitiesHandler='filterCitiesHandler'
          @filterKeywordsHandler='filterKeywordsHandler'
        />
        <RecruitmentTrainingPostsGrid :recruitment-training-list='articleListSet.list' />
        <Empty :loading='loading' :no-more='!loading && articleListSet.list.length===0' height='30vh' />
        <el-pagination
          v-if='!loading'
          :current-page.sync='currentPage'
          :hide-on-single-page='$store.state.hideOnSinglePage'
          :layout='$store.state.layout'
          :page-size='$store.state.article_count'
          :pager-count='$store.state.pager_count'
          :total='articleListSet.page.totalCount'
          background
          small
          style='text-align: center'
          @current-change='handleCurrentChange'
        >
        </el-pagination>
      </div>
    </section>
    <section slot='page-right'>
      <SideAdvertisement
        v-for='(item) in bannerList.list'
        :key='item.adId'
        :data='item' />

      <MyAttentionRT
v-if='$store.state.auth.token && followNum>0' title-font-size='20px'
                     :is-more-flag='true'
                     icons='team_'
                     title='我关注的科室'
                     @followNumHandler='followNumHandler'
      />
      <div v-else class='focus-departments'>
        <nuxt-link to='/department/all' target='_blank'>
          <img alt='' class='img_cover cursor' src='~assets/images/department/focus_departments.png'>
        </nuxt-link>

      </div>
    </section>
  </PageContainer>
</template>

<script>
import MyAttentionRT
  from '../../../components/optimize-components/page-components/recruitment-training/MyAttentionRT/index.vue'
import {
  getWebApiTrainRecruitPage
} from '../../../api/recruitment-training'
import Empty from '../../../components/UI/Empty/Empty.vue'
import {getSlotContent} from "../../../api/banner/banner";
import RecruitmentTrainingTabs
  from '@/components/page/recruitment-training/RecruitmentTrainingTabs/RecruitmentTrainingTabs'
import SecondLevelTabs from '@/components/page/recruitment-training/SecondLevelTabs/SecondLevelTabs'
import SideAdvertisement from '@/components/page/SideAdvertisement/SideAdvertisement'
import PageContainer from '@/components/page/PageContainer/PageContainer'
import RecruitmentTrainingPostsGrid
  from '@/components/page/recruitment-training/RecruitmentTrainingPostsGrid/RecruitmentTrainingPostsGrid'

export default {
  name: 'RecruitmentTrainingIndex',
  components: {
    Empty,
    RecruitmentTrainingTabs,
    SecondLevelTabs,
    SideAdvertisement,
    PageContainer,
    RecruitmentTrainingPostsGrid,
    MyAttentionRT
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [articleListSet, bannerList] = await Promise.all([
      app.$axios.$request(getWebApiTrainRecruitPage({
        // 科室id
        departmentId: null,
        // T:培训，R:招聘，A:全部
        trainOrRecruitment: 'A',
        // O:线上，D：线下，A:全部
        onlineStatus: 'A',
        // 城市名字
        cityStr: null,
        // 关键字
        labelStr: null,
        pageNo: 1,
        pageSize: store.state.article_count
      })),
      app.$axios.$request(
        getSlotContent({
          loginUserId:store.state.auth.user.id,
          detailIdStr:'',
          adCode:'webapi_recruitment_training_right_banner',
        })
      )
    ])

    return {
      articleListSet,
      bannerList
    }
  },
  data() {
    return {
      loading: false,
      currentPage: 1,
      trainOrRecruitment: 'A',
      onlineStatus: 'A',
      cityStr: null,
      labelStr: null,
      followNum: 1
    }
  },
  head() {
    return {
      title: '招聘培训',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '招聘培训'
        }
      ]
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-01 17:44
     * @description: 关注个数
     * ------------------------------------------------------------------------------
     */
    followNumHandler(num) {
      this.followNum = num
    },
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 13:11
     * @Description: 获取文章列表
     */
    getWebApiTrainRecruitPage(params = {}) {
      this.loading = true
      this.articleListSet = []
      this.$axios.$request(getWebApiTrainRecruitPage({
        // 科室id
        departmentId: null,
        // T:培训，R:招聘，A:全部
        trainOrRecruitment: this.trainOrRecruitment,
        // O:线上，D：线下，A:全部
        onlineStatus: this.onlineStatus,
        // 城市名字
        cityStr: this.cityStr,
        // 关键字
        labelStr: this.labelStr,
        pageNo: this.currentPage,
        pageSize: this.$store.state.article_count
      })).then(response => {
        if (response && response.code === 1) {
          this.articleListSet = response
        }
        this.loading = false
      })
    },

    /**
     * 切换tab
     */
    changeTabHandler(id) {
      this.currentPage = 1
      this.trainOrRecruitment = id
      this.getWebApiTrainRecruitPage()
    },
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 13:43
     * @Description: 切换线上线下
     */
    changeOnlineStatusHandler(code) {
      this.currentPage = 1
      this.onlineStatus = code
      this.getWebApiTrainRecruitPage()
    },
    /**
     * 翻页
     */
    handleCurrentChange(pageNo) {
      this.currentPage = pageNo
      this.$tool.scrollIntoTop()
      this.getWebApiTrainRecruitPage()
    },
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 14:59
     * @Description: 筛选城市
     */
    filterCitiesHandler(city) {
      this.cityStr = city === '全国' ? null : city
      this.getWebApiTrainRecruitPage()
    },
    /**
     * <AUTHOR> (Rick)
     * @date 2022-12-08 14:59
     * @Description: 筛选关键词
     */
    filterKeywordsHandler(keywords) {
      this.labelStr = keywords
      this.getWebApiTrainRecruitPage()
    }
  }
}
</script>

<style lang='less' scoped>
@import './index';
</style>
