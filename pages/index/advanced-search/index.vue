<template>
  <SearchPageContainer :styles="{padding:'16px 0 50px'}">
    <template #left>
      <AdvancedSearchForm/>
    </template>

    <template #right>
      <Instructions/>
    </template>

  </SearchPageContainer>
</template>

<script>
import SearchPageContainer from "../../../components/optimize-components/UI/SearchPageContainer/index.vue";
import {Instructions, AdvancedSearchForm} from "../../../components/optimize-components/page-components/search";

export default {
  name: "AdvancedSearch",
  components: {SearchPageContainer, Instructions, AdvancedSearchForm},
  head() {
    return {
      title: '高级搜索'
    }
  },
}
</script>

<style scoped>

</style>
