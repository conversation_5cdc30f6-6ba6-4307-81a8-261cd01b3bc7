<!--/**
 *  @author:<PERSON>  @date:2022/10/11 17:15
 *  指南文章详情页
 */-->
<template>
  <PageContainer>
    <!--Navigation Start-->
    <div slot='page-top' class='pageNav'>
      <luckArticleTips/>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/guidance' }">指南共识</el-breadcrumb-item>
        <el-breadcrumb-item>正文</el-breadcrumb-item>
      </bm-breadcrumb>
    </div>

    <section slot='page-left' class='left-page'>
      <div class='article-container'>
        <SideNavigation
          :info-data='infoData'
          @articleCollectionHandler='articleCollectionHandler'
          @articleFabulousHandler='articleFabulousHandler'
        />
        <section>
          <ArticleContent :info-data='infoData'/>
          <Comment
            :comment-list='commentListSet'
            :see-more-flag='seeMoreFlag'
            @commentLikeHandler='commentLikeHandler'
            @submitCommentsHandler='submitCommentsHandler'
            @submitReplyCommentsHandler='submitReplyCommentsHandler'
            @viewMoreDataSetHandler='viewMoreDataSetHandler'
          />
        </section>
      </div>
    </section>

    <section ref='SideBar' slot='page-right' class='right-page'>
      <DownloadPDF v-if='infoData.info.pdfUrl' :article-title='infoData.title' :pdf-url='infoData.info.pdfUrl'/>
      <Affix :offset-height='SideBarHeight' offset-top='15'>
        <Catalogue/>
      </Affix>

      <SideArticle v-for='(item,index) in infoData.guideAdditionInfoList' :key='index'
                   :data-set='item'
      />
      <div class="article_ai_container" v-if="aiList.length > 0">
        <div class="ai_box_header bg">
          <div class="ai_header_title bg"></div>
          <div class="ai_header_sub_title">由AI智能生成</div>
        </div>
        <ul class="ai_box_list" v-if="aiList.length > 0">
          <li class="ai_item" v-for="(item, i) in aiList" :key="i" @click="toai(item)">
            <div class="ai_item_container">
              <i class="ai_item_index">{{ i + 1 }}.&nbsp;</i>
              <div class="ai_item_text">{{ item }}</div>
            </div>
            <div class="ai-item_icon">
              <svg class="item_icon_arrow item_icon_white" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                <g clip-path="url(#clip0_9431_13222)">
                  <path d="M4.74831 13.1469C4.99845 13.397 5.404 13.397 5.65414 13.1469L11.2009 7.60008C11.5323 7.26872 11.5323 6.73149 11.2009 6.40014L5.6865 0.885714C5.41849 0.617707 4.98396 0.617708 4.71596 0.885715C4.44795 1.15372 4.44795 1.58825 4.71596 1.85625L9.65646 6.79676C9.8045 6.9448 9.8045 7.18482 9.65646 7.33286L4.74831 12.241C4.49817 12.4912 4.49817 12.8967 4.74831 13.1469Z" fill="#333333"/>
                </g>
                <defs>
                  <clipPath id="clip0_9431_13222">
                    <rect width="14" height="14" fill="white"/>
                  </clipPath>
                </defs>
              </svg>
              <svg class="item_icon_arrow item_icon_dark" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                <g clip-path="url(#clip0_9431_13269)">
                  <path d="M4.74831 13.1469C4.99845 13.397 5.404 13.397 5.65414 13.1469L11.2009 7.60008C11.5323 7.26872 11.5323 6.73149 11.2009 6.40014L5.6865 0.885714C5.41849 0.617707 4.98396 0.617708 4.71596 0.885715C4.44795 1.15372 4.44795 1.58825 4.71596 1.85625L9.65646 6.79676C9.8045 6.9448 9.8045 7.18482 9.65646 7.33286L4.74831 12.241C4.49817 12.4912 4.49817 12.8967 4.74831 13.1469Z" fill="white"/>
                </g>
                <defs>
                  <clipPath id="clip0_9431_13269">
                    <rect width="14" height="14" fill="white"/>
                  </clipPath>
                </defs>
              </svg>
            </div>
          </li>
        </ul>
        <div class="ai_box_footer">
          <div class="ai_box_toai go_to_app" extinfo="chat" @click="toai()">
            <div>
              <div>
                <img src="~assets/images/ai/article_ai_button_ai.png" alt="">
              </div>
            </div>
          </div>
          <div class="ai_box_toauthor" @click="toauth">向作者追问</div>
        </div>
      </div>

      <RecommendArticle v-if='guideRelatedContentList.length>0' :data-set='guideRelatedContentList'/>
    </section>
  </PageContainer>
</template>

<script>
import PageContainer from '@/components/page/PageContainer/PageContainer'
import SideNavigation from '@/components/page/detail/SideNavigation/SideNavigation'
import ArticleContent from '@/components/page/detail/ArticleContent/ArticleContent'
import DownloadPDF from '@/components/page/detail/guidance/DownloadPDF/DownloadPDF'
import Catalogue from '@/components/page/detail/guidance/Catalogue/Catalogue'
import SideArticle from '@/components/page/detail/guidance/SideArticle/SideArticle'
import RecommendArticle from '@/components/page/detail/RecommendArticle/RecommendArticle'
import Affix from '@/components/UI/Affix/Affix'
import {getGuideInfo, getGuideRelatedContentList} from '@/api/guidance'
import Comment from '@/components/page/detail/Comment/Comment'
import luckArticleTips from '@/components/LuckyDrawPop/luckArticleTips' // 幸运中奖弹窗
import {comment, commentDiggs, diggs, getInfoCommentsPageTwo} from '@/api/article'
import {infoCollect} from '@/api/user'
import { getArticleAiList } from '../../../../../api/ai.js'

export default {
  name: 'DetailPage',
  components: {
    PageContainer,
    SideNavigation,
    ArticleContent,
    DownloadPDF,
    Catalogue,
    SideArticle,
    RecommendArticle,
    Affix,
    Comment,
    luckArticleTips
  },
  async asyncData({app, params, error, store, query, req, redirect}) {
    redirect(`/info/detail?id=${params.id}`)
    const [guideRelatedContentList, infoData] = await Promise.all([
      app.$axios.$request(getGuideRelatedContentList({
        contentId: params.id,
        contentSource: 2
      })),
      app.$axios.$request(getGuideInfo({
        infoId: params.id,
        userId: store.state.auth.user.id
      }))
    ])

    const authorNames = infoData.result.authorNames && infoData.result.authorNames !== '' ? infoData.result.authorNames + ',' : ''
    let keywordsName = infoData.result.searchKeyWords || ''
    keywordsName = keywordsName !== '' ? keywordsName + ',' : ''

    return {
      guideRelatedContentList: guideRelatedContentList.list,
      infoData: infoData.result,
      authorNames,
      keywordsName
    }
  },
  data() {
    return {
      SideBarHeight: 0,
      seeMoreFlag: null,              // 查看更多loading开关
      commentData: {},           // 评论数据
      commentListSet: [],      // 评论列表
      pageSize: 5 ,             // 评论数量
      aiList: []
    }
  },
  head() {
    return {
      title: this.infoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.infoData.info.metaDescription
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇, ${this.keywordsName}${this.authorNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }
      ]
    }
  },
  mounted() {
    this.getInfoCommentsPageHandler()
    // eslint-disable-next-line no-undef
    setTimeout(() => {
      this.SideBarHeight = this.$refs.SideBar.offsetHeight
    }, 0)
    this.getAi()

  },
  methods: {
    /**
     * 文章点赞
     */
    articleFabulousHandler(item, callback) {
      this.$analysys.like('', '', '', '点赞原文', '', '', '', '', [], [], '指南共识', '', '', String(this.$route.params.id))

      this.$axios.$request(diggs({
        articleId: this.$route.params.id,
        userId: this.$store.state.auth.user.id
      })).then(response => {
        if (response && !response.result) {
          callback(true)
        } else {
          callback(false)

        }
      })

    },
    /**
     * 文章收藏
     */
    articleCollectionHandler(item, callback) {
      this.$analysys.like('', '', '', '收藏原文', '', '', '', '', [], [], '指南共识', '', '', String(this.$route.query.id))

      this.$axios.$request(infoCollect({
        articleId: this.$route.params.id
      })).then(response => {
        if (response && response.code === 1) {
          callback(true)
        }
      })
    },

    /**
     *  @author:Rick  @date:2022/10/20 14:33
     *  获取评论列表
     */
    getInfoCommentsPageHandler(params = {
      pageSize: 5
    }) {
      this.$axios.$request(getInfoCommentsPageTwo({
        infoId: this.$route.params.id,
        userId: this.$store.state.auth.user.id,
        order: 0,
        pageNo: 1,
        pageSize: params.pageSize
      })).then(response => {
        if (response && response.code === 1) {
          this.commentListSet = response.result.list

          const newArray = []

          this.commentListSet.forEach(item => {
            item.childComments.forEach(itemChildren => {
              itemChildren.commentTime = itemChildren.creationDate
            })
            newArray.push({
              childComments: item.childComments,
              commentTime: item.creationDate,
              commentator: item.creator,
              diggs: item.diggs,
              id: item.id,
              diggStatus: item.isDigg === 'true' ? 'T' : 'F',
              status: item.status,
              text: item.text
            })
            this.commentListSet = newArray
          })

          this.commentData = response
          params.callback && params.callback(true)

          const count = this.commentData.result.page.totalCount

          if (count <= this.pageSize) {
            this.seeMoreFlag = null
          } else {
            this.seeMoreFlag = false
          }
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/20 13:45
     *  查看更多
     */
    viewMoreDataSetHandler() {
      const count = this.commentData.result.page.totalCount
      this.seeMoreFlag = true
      if (this.pageSize + 5 < (count + 5)) {
        this.pageSize = this.pageSize + 5
        this.getInfoCommentsPageHandler({pageSize: this.pageSize})
      }
    },

    /**
     *  @author:Rick  @date:2022/10/19 17:54
     *  评论点赞
     */
    commentLikeHandler(id) {
      this.$axios.$request(commentDiggs({
        commentId: id,
        userId: this.$store.state.auth.user.id
      })).then(response => {
        if (response && response.code === 1) {
          if (!response.result) {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'T'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs + 1
              }
            })
          } else {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'F'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs - 1
              }
            })
          }
        }
      })
    },

    /**
     *  @author:Rick  @date:2022/10/20 9:43
     *  提交评论
     */
    submitCommentsHandler({commentInfo, parentId = null}, callback) {

      if (commentInfo === '' || !commentInfo) {
        this.$toast('请输入评论内容')
        return
      }

      this.$axios.$request(comment({
        text: commentInfo,
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.params.id,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getInfoCommentsPageHandler({callback, pageSize: this.pageSize})
        }
      })

    },

    /**
     *  @author:Rick  @date:2022/10/20 10:02
     *  提交回复评论
     */
    submitReplyCommentsHandler({info, parentId}, callback) {

      this.$axios.$request(comment({
        text: info,
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.params.id,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getInfoCommentsPageHandler({callback, pageSize: this.pageSize})
          // eslint-disable-next-line node/no-callback-literal
        }
      })
    },
    async getAi () {
      const res = await this.$axios.$request(getArticleAiList({
        infoId: this.$route.query.id,
        resultCount: 3
      }))
      if (res && res.code === 1 && res.list) {
        this.aiList = [...res.list]
        this.$nextTick(() => {
            this.SideBarHeight = this.$refs.SideBar.offsetHeight
        })
      }
    },
    toai (i) {
      if (i) {
        this.$analysys.btn_click('启发追问-' + i, this.infoData.title)
        window.open('/askAI/char/home?question=' + i)
      } else {
        this.$analysys.btn_click('向AI自由提问', this.infoData.title)
        window.open('/askAI/char/home')
      }
    },
    toauth () {
      this.$analysys.btn_click('向作者追问', this.infoData.title)
      document.querySelector('#PostInput').focus()
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
.bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.article_ai_container {
  margin-top: 24px;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  border: 0.5px solid #E7E7FF;
  background: linear-gradient(180deg, #EDF3FF 0%, #FFF 71.81%);
  box-sizing: border-box;
  & * {
    box-sizing: border-box;
  }
  .ai_box_header {
    padding: 11px 17px 0 14px;
    width: 100%;
    height: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-image: url("~assets/images/ai/article_ai_header_bg.png");
  }
  .ai_header_title {
    width: 93px;
    height: 23px;
    background-image: url("~assets/images/ai/article_ai_title.png");
  }
  .ai_header_sub_title {
    color: #8C9CA7;
    font-size: 13px;
  }
  .ai_box_list {
    width: 100%;
    padding: 0 15px;
  }
  .ai_item {
    padding: 15px 0;
    border-bottom: 1px dashed #e0e9fb;
    display: flex;
    justify-content: space-between;
  }
  .ai_item_container {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    color: #333;
    font-size: 15px;
    font-weight: 500;
  }
  .ai_item_index {
    font-style: normal;
    white-space: nowrap;
    flex-shrink: 0;
  }
  .ai_item_text {
    max-width: 216px;
    /*display: -webkit-box;*/
    /*-webkit-box-orient: vertical;*/
    /*-webkit-line-clamp: 2;*/
    /*overflow: hidden;*/
    /*text-overflow: ellipsis;*/
  }
  .ai-item_icon {
    width: 14px;
    height: 14px;
  }

  .ai_box_footer {
    padding: 14px 20px 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .ai_box_footer div {
    flex: 1;
    height: 40px;
  }
  .ai_box_toai {
    margin-right: 15px;
    height: 100%;
    position: relative;
    border-radius: 24px;
    overflow: hidden;
  }
  .ai_box_toai::after{
    width: 100%;
    height: 100%;
    content: '';
    position: absolute;
    transform: translate(72px, 22px) rotate(-20deg) scaleX(3) scaleY(7);
    background: radial-gradient(circle at top left,#FF98E2 6.5%, #9F53FF 37.5%, #52C5FF 65%, #6B6EFA 89.5%);

  }
  .ai_box_toai > div {
    z-index: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    padding: 2px;
    box-sizing: border-box;
    border-radius: 24px;
  }
  .ai_box_toai > div > div {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .ai_box_toai img {
    width: 83px;
  }
  .ai_box_toauthor {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFF;
    font-size: 14px;
    border-radius: 44px;
    background: linear-gradient(90deg, #2DAEFF 0%, #008CE2 100%), linear-gradient(93deg, #308CFF 2.76%, #0581CE 97.24%);
  }
  .item_icon_dark {
    display: none;
  }
}

</style>
