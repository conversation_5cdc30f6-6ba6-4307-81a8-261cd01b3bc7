<!--/**
 *  @author:<PERSON>  @date:2022/10/11 17:15
 *  指南视频详情页
 */-->
<template>
  <ContainerVideoPage :styles="{padding:'24px 0 32px;'}">
    <luckArticleTips/>
    <div class='vide-title'>
      {{ videoDetail.videoName }}
    </div>
    <section slot='left' class='left-page'>

      <div class='player-container-wrapper'>
        <div class='video-container'>
          <div class='container'>
            <ali-player
              v-if='videoDetail.vid && playAuth'
              :auto-play-delay='0'
              :autoplay='true'
              :cover='$tool.compressImg(videoDetail.cover,840,448)'
              :is-live='false'
              :playauth='playAuth'
              :use-h5-prism='true'
              :vid='videoDetail.vid'
              control-bar-visibility='hover'
              height='100%'
              show-bar-time='3000'
              width='100%'
            >
            </ali-player>
            <img v-else :src='$tool.compressImg(videoDetail.cover,840,448)' alt='视频封面' class='img_cover'>
          </div>
        </div>
        <PlayerFunctionBar
          :diggs='videoDetail.diggs'
          :is-collection='videoDetail.collectStatus === "T"'
          :digg-status="videoDetail.diggStatus === 'T'"
          :pv='videoDetail.views'
          @collectionHandler='collectionHandler'
          @diggsHandler='diggsHandler'/>
      </div>

      <Comment
        :comment-list='commentListSet'
        :see-more-flag='seeMoreFlag'
        @commentLikeHandler='commentLikeHandler'
        @submitCommentsHandler='submitCommentsHandler'
        @submitReplyCommentsHandler='submitReplyCommentsHandler'
        @viewMoreDataSetHandler='viewMoreDataSetHandler'
      />

    </section>

    <section slot='right' class='right-page' style="position: sticky;top: 84px">
      <SpeakerList
        v-if="videoDetail.authorList && videoDetail.authorList.length>0"
        :author-list='videoDetail.authorList'/>
        <RecommendArticle v-if='guideRelatedContentList.length>0' :data-set='guideRelatedContentList'/>
    </section>
  </ContainerVideoPage>
</template>

<script>
import {ContainerVideoPage} from "../../../../../opt-components/template";
import Aliplayer from '@/components/AliPlayer/AliPlayer'
import SpeakerList from '@/components/page/detail/SpeakerList/SpeakerList'
import RecommendArticle from '@/components/page/detail/RecommendArticle/RecommendArticle'
import PlayerFunctionBar from '@/components/page/detail/PlayerFunctionBar/PlayerFunctionBar'
import Affix from '@/components/UI/Affix/Affix'
import {
  getGuideRelatedContentList,
  getInterpretingVideoDetail,
  getPlayAuth,
  webApiBmsVideoCommentDigg,
  webApiBmsVideoDigg,
  webApiCollectBmsVideo,
  webApiGetBmsVideoComments,
  webApiSaveBmsVideoComment
} from '@/api/guidance'
import Comment from '@/components/page/detail/Comment/Comment'
// 幸运中奖弹窗
import luckArticleTips from '@/components/LuckyDrawPop/luckArticleTips'

export default {
  name: 'DetailPage',
  components: {
    SpeakerList,
    RecommendArticle,
    PlayerFunctionBar,
    Affix,
    Comment,
    'ali-player': Aliplayer,
    luckArticleTips,
    ContainerVideoPage
  },
  async asyncData({app, params, error, store, query, req}) {
    const [guideRelatedContentList, interpretingVideoDetail] = await Promise.all([
      app.$axios.$request(getGuideRelatedContentList({
        contentId: params.id,
        contentSource: 3
      })),
      app.$axios.$request(getInterpretingVideoDetail({
        videoId: params.id,
        loginUserId: store.state.auth.user.id
      })),
    ])

    const playAuth = await app.$axios.$request(getPlayAuth({
      vid: interpretingVideoDetail.result.vid
    }))

    return {
      guideRelatedContentList: guideRelatedContentList.list,
      videoDetail: interpretingVideoDetail.result,
      playAuth: playAuth.result.authinfo
    }
  },
  data() {
    return {
      seeMoreFlag: null,              // 查看更多loading开关
      commentData: {},           // 评论数据
      commentListSet: [],      // 评论列表
      pageSize: 5              // 评论数量
    }
  },
  head() {
    return {
      title: this.videoDetail.videoName,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.videoDetail.description || "聚焦神经疾病，融汇中外进展；着眼诊疗规范，聆听专业解读。"
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.videoDetail.videoName
        }
      ]
    }
  },
  beforeMount() {
    this.getwebApiGetBmsVideoCommentsHandler() // 获取视频评论列表
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/19 16:48
     *  获取视频评论
     */
    getwebApiGetBmsVideoCommentsHandler(params = {
      pageSize: 5
    }) {
      this.$axios.$request(webApiGetBmsVideoComments({
        videoId: this.$route.params.id,
        loginUserId: this.$store.state.auth.user.id,
        pageNo: 1,
        pageSize: params.pageSize
      })).then(response => {
        if (response && response.code === 1) {
          this.commentListSet = response.list
          this.commentData = response
          params.callback && params.callback(true)

          const count = this.commentData.page.totalCount

          if (count <= this.pageSize) {
            this.seeMoreFlag = null
          } else {
            this.seeMoreFlag = false
          }

        }
      })
    },

    /**
     *  @author:Rick  @date:2022/10/20 13:45
     *  查看更多
     */
    viewMoreDataSetHandler() {
      const count = this.commentData.page.totalCount
      this.seeMoreFlag = true
      if (this.pageSize + 5 < (count + 5)) {
        this.pageSize = this.pageSize + 5
        this.getwebApiGetBmsVideoCommentsHandler({pageSize: this.pageSize})
      }

    },

    /**
     *  @author:Rick  @date:2022/10/19 17:54
     *  评论点赞
     */
    commentLikeHandler(id) {
      this.$axios.$request(webApiBmsVideoCommentDigg({
        commentId: id
      })).then(response => {
        if (response && response.code === 1) {
          if (response.result.diggStatus === 'T') {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'T'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs + 1
              }
            })
          } else {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'F'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs - 1
              }
            })
          }
        }
      })
    },

    /**
     *  @author:Rick  @date:2022/10/20 9:43
     *  提交评论
     */
    submitCommentsHandler({commentInfo, parentId = null}, callback) {

      if (commentInfo === '' || !commentInfo) {
        this.$toast('请输入评论内容')
        return
      }

      this.$axios.$request(webApiSaveBmsVideoComment({
        videoId: this.$route.params.id,
        text: commentInfo,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getwebApiGetBmsVideoCommentsHandler({callback, pageSize: this.pageSize})
        }
      })

    },

    /**
     *  @author:Rick  @date:2022/10/20 10:02
     *  提交回复评论
     */
    submitReplyCommentsHandler({info, parentId}, callback) {

      this.$axios.$request(webApiSaveBmsVideoComment({
        videoId: this.$route.params.id,
        text: info,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getwebApiGetBmsVideoCommentsHandler({callback, pageSize: this.pageSize})
          // eslint-disable-next-line node/no-callback-literal
        }
      })
    },

    /**
     *  @author:Rick  @date:2022/10/19 16:05
     *  收藏
     */
    collectionHandler(params, callback) {
      this.$axios.$request(webApiCollectBmsVideo({
        videoId: this.$route.params.id
      })).then(response => {
        if (response && response.code === 1) {
          if (response.result.followStatus === 'T') {
            callback(true)
          } else {
            callback(false)
          }
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/19 16:17
     *  视频点赞
     */
    diggsHandler(params, callback) {
      this.$axios.$request(webApiBmsVideoDigg({
        loginUserId: this.$store.state.auth.user.id,
        videoId: this.$route.params.id
      })).then(response => {
        if (response && response.code === 1) {
          if (response.result.diggStatus === 'T') {
            callback(true)
          } else {
            callback(false)
          }
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
@import "styles";
</style>
