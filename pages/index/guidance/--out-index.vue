<template>
  <PageContainer>
    <section slot='page-left' class='left-page'>
      <SubMajorTab :channel-list='channelList.list' @changeChannelHandler='changeChannelHandler' />
      <SubMajorFilter
        @changeChannelTimeHandler='changeChannelTimeHandler'
        @changeChannelTypeHandler='changeChannelTypeHandler' />
      <PostsGrid :guide-data-set='guideDataSet.list' :loading='loading' />
      <el-pagination
        v-if='!loading'
        :current-page.sync='currentPage'
        :hide-on-single-page='$store.state.hideOnSinglePage'
        :layout='$store.state.layout'
        :page-size='$store.state.article_count'
        :pager-count='$store.state.pager_count'
        :total='guideDataSet.page.totalCount'
        background
        small
        style='text-align: center'
        @current-change='handleCurrentChange'
      >
      </el-pagination>
    </section>
    <section slot='page-right' class='right-page'>
      <SideAdvertisement
        v-for='item in guideBannerDataSet'
        :key='item.adId'
        :data='item'
      />
      <SideHotContent :data-set='guideHotContentList' title='热门内容' />
    </section>
  </PageContainer>
</template>

<script>
/**
 *  @author:Rick  @date:2022/10/11 10:50
 */
import {getSlotContent} from "../../../api/banner/banner";
import PageContainer from '@/components/page/PageContainer/PageContainer'
import SubMajorTab from '@/components/page/SubMajorTab/SubMajorTab'
import SubMajorFilter from '@/components/page/SubMajorTab/SubMajorFilter/SubMajorFilter'
import PostsGrid from '@/components/page/PostsGrid/PostsGrid'
import SideAdvertisement from '@/components/page/SideAdvertisement/SideAdvertisement'
import SideHotContent from '@/components/page/SideHotContent/SideHotContent'
import { getByGuideChannelList, getGuideContentList, getGuideHotContentList } from '@/api/guidance'


export default {
  name: 'GuidancePage',
  components: {
    PageContainer,
    SubMajorTab,
    SubMajorFilter,
    PostsGrid,
    SideAdvertisement,
    SideHotContent
  },
  async asyncData({ app, params, error, store, query, req }) {
    app.head.title = '指南共识'
    const [channelData, guideData, guideBannerData, guideHotContent] = await Promise.all([
      app.$axios.$request(getByGuideChannelList({})),
      app.$axios.$request(getGuideContentList({
        pageNo: 1,
        pageSize: store.state.article_count,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(
        getSlotContent({
          loginUserId:store.state.auth.user.id,
          detailIdStr:'',
          adCode:'webAPi_guide_banner',
        })
      ),
      app.$axios.$request(getGuideHotContentList({
        limit: 10
      }))
    ])
    return {
      channelList: channelData,
      guideDataSet: guideData,
      guideBannerDataSet: guideBannerData.list,
      guideHotContentList: guideHotContent.list
    }
  },
  data() {
    return {
      loading: false,    // 数据加载动画
      currentPage: 1,
      channelId: 0,    // 频道ID
      guideTypeId: 0,  // 频道类型ID
      guideTimeId: 0   // 频道时间ID
    }
  },
  head() {
    return {
      title: '指南共识',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '指南共识'
        }
      ]
    }
  },
  methods: {
    /**
     *  @author:Rick  @date:2022/10/14 15:44
     *  获取频道列表
     */
    getGuideContentListHandler(params = {}) {
      this.loading = true
      this.guideDataSet.list = []
      this.$axios.$request(getGuideContentList({
        pageNo: this.currentPage,
        pageSize: this.$store.state.article_count,

        channelId: this.channelId !== 0 ? this.channelId : null,
        guideTypeId: this.guideTypeId !== 0 ? this.guideTypeId : null,
        year: this.guideTimeId === 0 ? null : this.guideTimeId === 'isBefore' ? 2016 : this.guideTimeId,
        isBefore: this.guideTimeId === 'isBefore' ? 'T' : null,

        loginUserId: this.$store.state.auth.user.id
      })).then(response => {
        if (response && response.code === 1) {
          this.guideDataSet = response
        }
        this.loading = false
      })
    },
    /**
     *  @author:Rick  @date:2022/10/14 15:38
     *  切换指南共识频道频道
     */
    changeChannelHandler(channel) {
      if (channel.channelId !== this.channelId) {
        this.channelId = channel.channelId
        this.currentPage = 1
        this.getGuideContentListHandler()
      }
    },
    /**
     *  @author:Rick  @date:2022/10/14 16:39
     *  切换频道类型
     */
    changeChannelTypeHandler(item) {
      if (item.id !== this.guideTypeId) {
        this.guideTypeId = item.id
        this.currentPage = 1
        this.getGuideContentListHandler()
      }
    },
    /**
     *  @author:Rick  @date:2022/10/18 9:59
     *  切换频道时间
     */
    changeChannelTimeHandler(item) {
      if (item.id !== this.guideTimeId) {
        this.guideTimeId = item.id
        this.currentPage = 1
        this.getGuideContentListHandler()
      }
    },
    /**
     *  @author:Rick  @date:2022/10/14 16:50
     *  指南共识列表分页
     */
    handleCurrentChange(current) {
      this.$tool.scrollIntoTop()
      this.currentPage = current
      this.getGuideContentListHandler()
    }
  }
}
</script>

<style lang='less' scoped>
@import "./styles";
</style>
