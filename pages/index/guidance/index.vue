<template>
  <Container :styles="{display:'flex',padding:'16px 0 32px'}">
    <div class="guidance_left">
      <SideNav :link-index="linkIndex" :data="formatChannelList" @changeIndex="changeNavIndex"/>
    </div>
    <div class="guidance_right">
      <div
        style="position: sticky;top: 60px;z-index: 10;background: white;padding:14px 0 14px;border-bottom: 1px solid #E6E6E6;margin-top: -16px">
        <SubMajorFilter
          @changeChannelTimeHandler='changeChannelTimeHandler'
          @changeChannelTypeHandler='changeChannelTypeHandler'
        />
      </div>

      <div style="margin-top: 10px">
        <RollingLoad
          :loading="loading"
          :empty="guideData.list.length===0"
          :no-more="currentPageNo >= guideData.page.totalPage"
          empty-height="50vh"
          @hit-bottom="hitBottomChangeHandler"
        >
          <template #loading>
            <InfoSkeleton :limit="16" :loading="loading"/>
          </template>
          <div class="skeleton_container">
            <GuidanceDefaultItem
              v-for="(item,index) in guideData.list" :key="index"
              :guidance-data="item"
            />
          </div>
        </RollingLoad>
      </div>
    </div>
  </Container>
</template>

<script>
import {SideNav} from "../../../opt-components/page/info";
import {Container} from "../../../opt-components/template";
import {getByGuideChannelList, getGuideContentList} from "../../../api/guidance";
import {InfoSkeleton} from "../../../opt-components/ui/skeleton";
import {GuidanceDefaultItem} from "../../../opt-components/data-list";
import RollingLoad from "../../../opt-components/component/RollingLoad/index.vue";
import SubMajorFilter from "../../../opt-components/page/guidance/SubMajorFilter/SubMajorFilter.vue";
import {userInfo} from "../../../api/user";

export default {
  name: "GuidanceList",
  components: {
    Container,
    SideNav,
    InfoSkeleton,
    GuidanceDefaultItem,
    RollingLoad,
    SubMajorFilter
  },
  async asyncData({app, params, error, store, query, req}) {
    const [ChannelData] = await Promise.all([
      app.$axios.$request(getByGuideChannelList())
    ])

    let userData = null
    if (store.state.auth.token) {
      userData = await app.$axios.$request(userInfo())
    }


    return {
      channelList: ChannelData.list,
      userData:userData?.result
    }
  },
  data() {
    return {
      loading: true,
      guideData: {
        list: [],
        page: {}
      },
      currentPageNo: 1,
      linkIndex:0,
      channelId: null,
      guideTypeId: null,
      year: null
    }
  },
  head() {
    return {
      title: '指南共识',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '指南共识'
        }
      ]
    }
  },
  computed: {
    formatChannelList() {
      if (this.channelList && this.channelList.length > 0) {
        const newList = this.channelList.map(item => {
          return {
            id: item.id,
            mpSubspecialityId:item.mpSubspecialityId,
            name: item.name
          }
        })

        newList.unshift({
          id: null,
          name: "全部"
        })


        return newList

      }
      return [];
    }
  },
  mounted() {
    const specialityId = this.userData && this.userData.specialityList.length > 0 ? this.userData.specialityList[0].id : null
    if (specialityId) {
      this.formatChannelList.forEach((item, index) => {
        if (item.mpSubspecialityId === specialityId) {
          this.channelId = item.id
          this.linkIndex = index
        }
      })
    }

    this.getDataHandler({
      pageNo: this.currentPageNo,
      channelId: this.channelId,
      guideTypeId: this.guideTypeId,
      year: this.year,
    })
  },
  methods: {
    async getDataHandler({pageNo, channelId, guideTypeId, year, pageUp} = {pageNo: 1}) {
      const GuideData = await this.$axios.$request(getGuideContentList({
        pageNo,
        pageSize: 16,

        channelId,
        guideTypeId,
        year: year === 0 ? null : year === 'isBefore' ? 2016 : year,
        isBefore: year === 'isBefore' ? "T" : null,

        loginUserId: this.$store.state.auth.user.id
      }))
      if (GuideData.code === 1) {

        if (pageUp) {
          this.$set(this.guideData, "list", [...this.guideData.list, ...this.formatList(GuideData.list)])
          this.$set(this.guideData, "page", GuideData.page)
        } else {
          this.$set(this.guideData, "list", this.formatList(GuideData.list))
          this.$set(this.guideData, "page", GuideData.page)
        }

      }
      this.loading = false;
    },
    changeNavIndex(index) {
      if (this.channelId !== this.formatChannelList[index].id) {
        this.$tool.scrollIntoTop()
        this.channelId = this.formatChannelList[index].id

        this.guideData = {
          list: [],
          page: {}
        }
        this.currentPageNo = 1;
        this.loading = true;

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          guideTypeId: this.guideTypeId,
          year: this.year,
        })
      }
    },
    changeChannelTimeHandler(item) {
      if (this.year !== item.id) {
        this.$tool.scrollIntoTop()
        this.year = item.id
        this.guideData = {
          list: [],
          page: {}
        }
        this.currentPageNo = 1;
        this.loading = true;

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          guideTypeId: this.guideTypeId,
          year: this.year,
        })
      }
    },
    changeChannelTypeHandler(item) {
      if (this.guideTypeId !== item.id) {
        this.$tool.scrollIntoTop()
        this.guideTypeId = item.id === 0 ? null : item.id
        this.guideData = {
          list: [],
          page: {}
        }
        this.currentPageNo = 1;
        this.loading = true;

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          guideTypeId: this.guideTypeId,
          year: this.year,
        })
      }
    },

    hitBottomChangeHandler(flag) {
      if (flag) {
        this.loading = true;
        this.currentPageNo += 1;


        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          guideTypeId: this.guideTypeId,
          year: this.year,
          pageUp: true
        })

      }
    },
    formatList(list) {
      if (list && list.length > 0) {
        return list.map(item => {
          switch (item.type) {
            case "info": {
              return {
                url: `/info/detail?id=${item.info.infoId}`,
                id: item.info.infoId,
                image: item.info.infoImg,
                title: item.info.infoTitle,
                authorNames: item.info.authorNames,
                guideTypeList: item.info.guideTypeList,
                publishDate: this.timeStamp.timestampFormat(item.info.publishDate / 1000),
                isPlay: false,
                bmsAuth:item.info.bmsAuth
              }
            }
            case "mp_article": {
              return {
                url: `/case/detail-ugc?id=${item.mp_article.id}`,
                id: item.mp_article.id,
                image: item.mp_article.cover,
                title: item.mp_article.title,
                authorNames: item.mp_article.creator.realName,
                guideTypeList: item.mp_article.guideTypeList,
                publishDate: this.timeStamp.timestampFormat(item.mp_article.publishTime / 1000),
                isPlay: false,
                bmsAuth:item.mp_article.bmsAuth
              }
            }
            case "meeting": {
              return {
                url: `/meeting/detail?id=${item.meeting.id}`,
                id: item.meeting.id,
                image: item.meeting.appMainPic || item.meeting.playerCover || item.meeting.titlePic,
                title: item.meeting.meetingName,
                authorNames: '',
                guideTypeList: item.meeting.guideTypeList,
                publishDate: item.meeting.meetingDateStr,
                isPlay: true,
              }
            }
            case "meetingFields": {
              return {
                url: `/meeting/detail?id=${item.meetingFields.meetingId}`,
                id: item.meetingFields.id,
                image: item.meetingFields.fieldsCover,
                title: item.meetingFields.subject,
                authorNames: '',
                guideTypeList: item.meetingFields.guideTypeList,
                publishDate: this.timeStamp.timestamp_13(item.meetingFields.startTime,'y-m-d--') + '-' + this.timeStamp.timestamp_13(item.meetingFields.endTime,'y-m-d--'),
                isPlay: true,
              }
            }
            case "interpreting_video": {
              return {
                url: `/guidance/detail/video/${item.interpreting_video.id}`,
                id: item.interpreting_video.id,
                image: item.interpreting_video.cover,
                title: item.interpreting_video.videoName,
                authorNames: item.interpreting_video.authors,
                guideTypeList: item.interpreting_video.guideTypeList,
                publishDate: this.timeStamp.timestampFormat(item.interpreting_video.publishTime / 1000),
                isPlay: true,
              }
            }
            default: {
              return {}
            }
          }
        })
      } else {
        return []
      }

    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
