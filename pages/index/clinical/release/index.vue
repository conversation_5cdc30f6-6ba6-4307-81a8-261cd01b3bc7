<template>
  <div class="container-box release_container">
    <aside class="release_page_left">
      <div class="release_content">
        <div class="title_container">
          <img src="~assets/images/clinical/release-title-hight.png" alt="">
        </div>
        <p class='tips'>脑医汇，专注为脑部和脊髓脊柱领域医生与患者搭建医患交流数字化平台</p>
        <div class="contact_container">
          <div class="contact">
            <svg-icon icon-class="c_phone" class-name="icons"></svg-icon>
            <span class="tips_info">电话联系：</span>
            <span class="phone">13162309792</span>
          </div>
          <div class="contact wechat_contact">
            <svg-icon icon-class="c_wechat" class-name="icons"></svg-icon>
            <span class="tips_info">微信联系：</span>
            <el-popover
              placement="bottom-end"
              width="260"
              trigger="click">
              <div style="display: flex;flex-flow: column;align-items: center;padding: 13px 4px">
                <div style="font-size: 20px; color: #333; font-weight: 600;margin-bottom: 12px">临床招募代发布</div>
                <div style="font-size: 14px; margin-bottom: 16px;color: #676C74; ">扫描下方二维码，添加客服微信沟通详情
                </div>
                <img style="width: 160px;height: 160px;" src="~assets/images/clinical/customer.png" alt="">
              </div>
              <div slot="reference" class="add_btn">+加好友</div>
            </el-popover>
          </div>
        </div>

        <div v-for="(item,index) in list" :key="index" class="list_wrapper" style="margin-bottom: 16px">
          <div class="title">
            <svg-icon :icon-class="item.icon" class-name="icons"></svg-icon>
            <span>{{ item.name }}</span>
          </div>
          <ul>
            <li v-for="(itemC,indexC) in item.list" :key="indexC">{{ itemC.name }}</li>
          </ul>
        </div>


      </div>
    </aside>
    <aside class="release_page_right">
      <div class="bg_container">
        <div class="bg_share">
          <el-popover
            placement="bottom-end"
            width="260"
            trigger="click">
            <div style="display: flex;flex-flow: column;align-items: center;padding: 28px 4px">
              <div style="font-size: 20px; color: #333; font-weight: 600;margin-bottom: 12px">微信扫码分享</div>
              <img style="width: 160px;height: 160px;" :src="wechatUrl" alt="">
            </div>
            <div slot="reference" class="share_btn" @click="shareHandler">
              <svg-icon icon-class="c_share" class-name="icons"></svg-icon>
              <div style="color:#333333;font-size: 12px">分享</div>
            </div>
          </el-popover>
        </div>
        <img class="release_bg" src="~assets/images/clinical/release-bg.jpg" alt="release"/>
      </div>
    </aside>
  </div>
</template>

<script>
import qrcade from 'qrcode'

export default {
  name: "ReleasePage",
  data() {
    return {
      wechatUrl: "",
      list: [
        {
          name: "我们的优势",
          icon: "c_youshi",
          list: [
            {name: "已为30,000+精准患者提供全病程服务，可推荐合适患者快速入组试验"},
            {name: "平台患者均已建立数字化电子病历，可线上集中批量筛选处理，提高入组效率"},
            {name: "平台宣传团队深耕临床神经科学领域多年，可为临床试验设计全场景、多渠道宣传方案"},
          ]
        },
        {
          name: "目前已合作的项目",
          icon: "c_hezuo",
          list: [
            {name: "一项在复发性胶质母细胞瘤患者中评估SNC-109 CAR-T细胞治疗的安全性、耐受性、药代动力学和初步抗肿瘤活性的I期临床研究"},
            {name: "一项单臂、多中心评价ACT001联合放疗治疗初诊弥漫内生型桥脑胶质瘤患者安全性和有效性的II期临床研究"},
            {name: "溶瘤病毒JL15003注射液治疗复发胶质母细胞瘤患者的安全性和耐受性临床研究"},
            {name: "硼中子俘获治疗（AB-BNCT）示范中心--招募高级别脑胶质瘤患者"},
            {name: "......"},
          ]
        },
      ]
    }
  },
  head() {
    return {
      title: '临床试验招募患者，欢迎联系我们！'
    }
  },
  mounted() {
    const url = window.location.href
    qrcade.toDataURL(url).then((img) => {
      this.wechatUrl = img
    }).catch((err) => {
      console.log(err)
    })
  },
  methods:{
    shareHandler(){
      this.$analysys.new_btn_click({
        userId: this.$store.state.auth.user.id,
        btnName: "分享",
        pageName: "临床试验招募患者，欢迎联系我们！"
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
