<template>
  <PageContainer>
    <section slot='page-left'>
      <DataLoad loading-height="80vh" :loading="loading" :no-more="infoData && infoData.list.length === 0">
        <ul class="list_wrapper">
          <InfoItemDefault
            v-for="item in infoData.list"
            :id="item.infoId"
            :key="item.infoId"
            :essences="item.essences"
            :title="item.infoTitle"
            :article-date="Date.parse(item.publishDate)"
            :author-names="item.authorNames"
            :img="item.infoImg"
            :meta-description="item.metaDescription"
          />
        </ul>
        <div style="margin-top: 20px">
          <el-pagination
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='$store.state.article_count'
            :pager-count='$store.state.pager_count'
            :total='infoData.page.totalCount'
            background
            small
            style='text-align: center'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </DataLoad>

    </section>
    <section slot='page-right'>
      <div v-for='item in brandAdList' :key='item.id' style="margin-bottom: 20px">
        <Advertisement
          :img-url='item.image'
          :name='item.name'
          :module='item.module'
          :extras='item.extras'
        />
      </div>
      <ContactUs/>
    </section>
  </PageContainer>
</template>

<script>
import InfoItemDefault
  from "../../../components/optimize-components/public/article-types-list/info-item/InfoItemDefault/index.vue";
import PageContainer from "../../../components/optimize-components/UI/PageContainer/PageContainer.vue";
import Advertisement from "../../../components/optimize-components/page-components/bms/Advertisement/index.vue";
import ContactUs from "../../../components/optimize-components/page-components/clinical/ContactUs/index.vue";
import {getClinicalRecruitmentArticlePage} from "../../../api/clinial/default";
import {getSlotContent} from "../../../api/banner/banner";
import DataLoad from "../../../components/optimize-components/public/DataLoad/index.vue";

export default {
  name: "ClinicalPage",
  components: {
    DataLoad,
    InfoItemDefault,
    PageContainer,
    Advertisement,
    ContactUs
  },
  async asyncData({app, params, error, store, query, req}) {
    const [infoData, slotData] = await Promise.all([
      app.$axios.$request(getClinicalRecruitmentArticlePage({
        userId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 9
      })),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: `webapi_Clinical_recruitment`,
        })
      )
    ])

    return {
      infoData,
      brandAdList: slotData.list
    }
  },
  head() {
    return {
      title: '临床招募',
    }
  },
  data() {
    return {
      loading: false,
      currentPage: 1
    }
  },
  mounted() {

  },
  methods: {
    handleCurrentChange(current) {
      this.$tool.scrollIntoTop()
      this.currentPage = current;
      this.loading = true;

      this.$axios.$request(getClinicalRecruitmentArticlePage({
        userId: this.$store.state.auth.user.id,
        pageNo: current,
        pageSize: 9
      })).then(res => {
        if (res && res.code === 1) {
          this.infoData = res;
        }

        this.loading = false;
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
