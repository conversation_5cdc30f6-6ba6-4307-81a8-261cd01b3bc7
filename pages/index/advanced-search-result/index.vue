<template>
  <SearchPageContainer :styles="{padding:'16px 0 50px'}">
    <template #left>
      <HighSearchTab/>
      <ShortVideoPlayback
        :video-id='$store.state.bms.bmsHomeShortVideoId'
        :visible='$store.state.bms.bmsHomeShortVisible'
        @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
      />
    </template>

    <template #right>
      <div ref="search_right_wrapper" class="search_right_wrapper" :style="{position: 'sticky'}">
        <HotSearch :list="hotSearchList"/>
        <RenownedExperts/>
        <HotAnswer/>
        <HotTalk/>
      </div>
    </template>
  </SearchPageContainer>
</template>

<script>
import {
  HotSearch,
  RenownedExperts,
  HotAnswer,
  HotTalk,
  HighSearchTab
} from "../../../components/optimize-components/page-components/search";
import SearchPageContainer from "../../../components/optimize-components/UI/SearchPageContainer/index.vue";
import {getLeaderBoard} from "../../../api/search";
import ShortVideoPlayback from "../../../components/optimize-components/public/ShortVideoPlayback/index.vue";

export default {
  name: "AdvancedSearchResult",
  components: {
    SearchPageContainer,
    HotSearch,
    RenownedExperts,
    HotAnswer,
    HotTalk,
    HighSearchTab,
    ShortVideoPlayback
  },
  async asyncData({app, params, error, store, query, req}) {
    const [hotSearchData] = await Promise.all([
      app.$axios.$request(getLeaderBoard())
    ])

    return {
      hotSearchList: hotSearchData.list,
    }
  },
  head() {
    return {
      title: '高级搜索'
    }
  },
  mounted() {
    // eslint-disable-next-line no-undef
    setTimeout(() => {
      const rightWrapper = this.$refs.search_right_wrapper
      if (rightWrapper) {
        this.rightTop = (rightWrapper.clientHeight - window.innerHeight) + 50

        rightWrapper.style.top = "-" + this.rightTop + "px"
      }
    }, 1000)
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
