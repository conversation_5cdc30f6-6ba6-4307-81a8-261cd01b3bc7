<template>
  <div style="padding: 0 0 36px">
    <InfoTemplate
      :nav-name='infoData.isClinicalRecruitment === "T" ? "临床招募" : "资讯"'
      :nav-path='infoData.isClinicalRecruitment === "T" ? "/clinical" : "/info"'
      :info-data='infoData'
      :related-articles='relatedArticles'
      :news-info='newsInfo'
      :recommend-list='recommendList'
      :related-articles-is-show='false'
    />
    <transition name='el-fade-in-linear'>
      <RecommendDialog
        v-if='isRecommendDialog'
        :recommend-list='todayRecommendList'
        :info-title="infoData.title"
        @cancel='isRecommendDialog = false'
      />
    </transition>
  </div>
</template>

<script>

import {getInfoRelatedContentList, getRandomInfoRelatedContent} from '../../../../api/article'
import RecommendDialog from '../../../../components/optimize-components/page-components/info/RecommendDialog/index.vue'
import {assocInfos, getInfo, newestInfos} from '@/api/article'
import InfoTemplate from '@/components/page/detail/InfoTemplate/InfoTemplate.vue'
import {getClinicalRecruitmentArticleCorrelation} from "../../../../api/clinial/default";

export default {
  name: 'DetailIndexPage',
  components: {
    RecommendDialog,
    InfoTemplate
  },

  async asyncData({app, params, error, store, query, req}) {

    /**
     * reuqest1 获取当前文章的相关文章
     * reuqest2 获取文章详情接口
     * newsInfo 最新发布
     */
    const [request1, request2] = await Promise.all([
      app.$axios.$request(assocInfos({
        articleId: query.id,
        limit: store.state.datalist_count
      })),
      app.$axios.$request(getInfo({
        infoId: query.id,
        userId: store.state.auth.user.id
      }))
    ])
    app.head.title = request2.result.title
    let idArray = ''// 作者ID集
    request2.result && request2.result.info.authorList ? request2.result.info.authorList.forEach((item) => {
      idArray += item.id + ','
    }) : idArray = ''

    const attrsNames = request2.result.attrs ? request2.result.attrs.map((item) => item.name + ',') : ''
    const authorNames = request2.result.authorNames && request2.result.authorNames !== '' ? request2.result.authorNames + ',' : ''
    let keywordsName = request2.result.searchKeyWords || ''
    keywordsName = keywordsName !== '' ? keywordsName + ',' : ''

    const newsInfo = await app.$axios.$request(newestInfos({
      infoId: query.id,
      authorIds: idArray.slice(0, idArray.length - 1),
      pageNo: 1,
      pageSize: store.state.datalist_count
    }))

    let recommendList = []

    if (request2.result.isClinicalRecruitment === 'F') {
      const recommendData = await app.$axios.$request(getInfoRelatedContentList({
        infoId: query.id,
        limit: 2
      }))

      recommendList = recommendData.list
    } else if (request2.result.isClinicalRecruitment === 'T') {
      const recommendData = await app.$axios.$request(getClinicalRecruitmentArticleCorrelation({
        userId: store.state.auth.user.id,
        infoId: query.id,
        limit: 5
      }))

      recommendList = recommendData.list.map(item => {
        return {
          type: "info",
          info: {
            infoId: item.infoId,
            infoImg: item.infoImg,
            infoTitle: item.infoTitle,
            publishDate: item.publishDate,
          }
        }
      })
    }


    return {
      relatedArticles: request1.list,
      infoData: request2.result,
      newsInfo: newsInfo.list,
      attrsNames,
      authorNames,
      keywordsName,
      recommendList
    }
  },
  head() {
    return {
      title: this.infoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.infoData.info.metaDescription
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇, ${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }]
    }
  },
  data() {
    return {
      isRecommendDialog: false,
      todayRecommendList: []
    }
  },
  mounted() {
    this.getRandomInfoRelatedContentHandler()
    this.$analysys.Browse_article({
      userid: String(this.$store.state.auth.user.id),
      articleid: String(this.$route.query.id),
      type: 'info',
      articletitle: String(this.infoData.title)
    })
  },
  methods: {
    // 获取今日推荐
    getRandomInfoRelatedContentHandler() {
      const time = new Date().getMonth() + 1 + '-' + new Date().getDate()
      const obj = JSON.parse(JSON.stringify(window.localStorage.getItem('RandomInfoRelatedContent')))
      if (!obj || time + '' !== obj + '') {
        this.$axios.$request(getRandomInfoRelatedContent({
          infoId: this.$route.query.id,
          meetingLimit: 10,
          courseLimit: 30
        })).then(res => {
          if (res.code === 1) {
            this.todayRecommendList = res.list
            this.isRecommendDialog = true
            window.localStorage.setItem('RandomInfoRelatedContent', time)
          }
        })
      }
    }
  }
}
</script>

<style lang='less' scoped>
//@import "./index";
</style>
