<template>
  <div class="page_wrapper">
    <ContainerPage>
      <template #left>
        <div class="page_left_wrapper">
          <Sidebar
            :comments="InfoData.comments"
            article-type="pgc"
            :show-diggs="InfoData.showDiggs"
            :show-collects="InfoData.showCollects"
            :is-article-digg="InfoData.isArticleDigg"
            :is-article-collect="InfoData.isArticleCollect"
            :meta-description="InfoData.metaDescription"
            @articleLikeHandler="articleLikeHandler"
            @articleCollectHandler="articleCollectHandler"
          />
          <InfoTopTitle
            article-type="pgc"
            :template="Template"
            :title="InfoData.title"
            :publish-date="timeStamp.timestamp_13(InfoData.publishDate, 'yyyy-mm-d')"
            :show-views="InfoData.showViews"
            :attrs="InfoData.attrs"
            :author-list="InfoData.authorList"
            :article-honer-info="articleHonerInfo"
            :guide-source="InfoData.guideSource"
            :bms-auth="InfoData.bmsAuth"
          />
          <InfoContent :content="InfoData.text"/>
          <BottomToolbars
            :comments="InfoData.comments"
            article-type="pgc"
            :show-diggs="InfoData.showDiggs"
            :show-collects="InfoData.showCollects"
            :is-article-digg="InfoData.isArticleDigg"
            :is-article-collect="InfoData.isArticleCollect"
            :meta-description="InfoData.metaDescription"
            @articleLikeHandler="articleLikeHandler"
            @articleCollectHandler="articleCollectHandler"
          />
          <RelatedArticles
            v-if="relatedArticleData.list.length>0"
            :info-title="InfoData.title"
            :list="relatedArticleData.list || []"
            article-type="pgc"
          />
          <InfoComments
            :comment-data="commentData"
            @likeHandler="likeHandler"
            @pushHandler="pushHandler"
            @pageUp="(current) => articleCommentListHandler({pageNo : current})"
          />
        </div>
      </template>
      <template #right>
        <div ref="page_right_wrapper" class="page_right_wrapper">
          <!--招聘与培训-->
          <RecruitmentTrainingEntrance
            v-if="isInfoTrainRecruit === 'A' || isInfoTrainRecruit === 'T' || isInfoTrainRecruit === 'R'"/>
          <!--商务模板-->
          <ActivityModule
            v-if="Template === 'business' && brandActivityList.length>0"
            :activity-list="brandActivityList"/>
          <!--临床招募-->
          <ClinicalRecruitment v-if="isClinicalRecruitment === 'T'"/>
          <!-- 精选编译-->
          <CompilationEntry v-if="Template === 'compile'"/>
          <!-- 指南共识-->
          <InfoCatalogue v-if="Template === 'guide'"/>
          <GuidanceEntrance v-if="Template === 'guide'"/>
          <PDFDownload
            v-if="Template === 'guide' && InfoData.pdfUrl"
            :pdf-url="InfoData.pdfUrl"/>
          <PDFTranslateDownload
            v-if="Template === 'guide' && InfoData.pdfTranslateUrl"
            :pdf-translate-url="InfoData.pdfTranslateUrl"/>

          <Progress
            v-if="Template === 'guide' && InfoData.guideAdditionInfoList && InfoData.guideAdditionInfoList.length>0"
            :guide-addition-info-list="InfoData.guideAdditionInfoList"
          />

          <!--普通模板-->
          <CircleEntrance
            v-if="circleList.length>0 && !Template"
            :circle-list="circleList"
          />
          <ColumnEntrance
            v-if="articleSpecials.length>0 && (!Template || Template === 'clinicalRecruitment')"
            :article-specials="articleSpecials"
          />
          <ContactUs v-if="Template === 'clinicalRecruitment'"/>
          <RelatedCategories
            v-if="(infoMiniTool.length>0 || (articleSpecials.length>0 && template) || (circleList.length>0 && template) || talkList.length>0 || infoProducts.length>0) && isClinicalRecruitment === 'F'"
            :template="Template"
            :info-mini-tool="infoMiniTool"
            :article-specials="articleSpecials"
            :circle-list="circleList"
            :talk-list="talkList"
            :info-products="infoProducts"
          />
          <SearchKeyword v-if="searchKeyWordList.length > 0" :search-key-word-list="searchKeyWordList"/>
          <div class="article_ai_container" v-if="aiList.length > 0">
            <div class="ai_box_header bg">
              <div class="ai_header_title bg"></div>
              <div class="ai_header_sub_title">由AI智能生成</div>
            </div>
            <ul class="ai_box_list" v-if="aiList.length > 0">
                    <li class="ai_item" v-for="(item, i) in aiList" :key="i" @click="toai(item)">
                      <div class="ai_item_container">
                        <i class="ai_item_index">{{ i + 1 }}.&nbsp;</i>
                        <div class="ai_item_text">{{ item }}</div>
                      </div>
                      <div class="ai-item_icon">
                        <svg class="item_icon_arrow item_icon_white" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                          <g clip-path="url(#clip0_9431_13222)">
                            <path d="M4.74831 13.1469C4.99845 13.397 5.404 13.397 5.65414 13.1469L11.2009 7.60008C11.5323 7.26872 11.5323 6.73149 11.2009 6.40014L5.6865 0.885714C5.41849 0.617707 4.98396 0.617708 4.71596 0.885715C4.44795 1.15372 4.44795 1.58825 4.71596 1.85625L9.65646 6.79676C9.8045 6.9448 9.8045 7.18482 9.65646 7.33286L4.74831 12.241C4.49817 12.4912 4.49817 12.8967 4.74831 13.1469Z" fill="#333333"/>
                          </g>
                          <defs>
                            <clipPath id="clip0_9431_13222">
                              <rect width="14" height="14" fill="white"/>
                            </clipPath>
                          </defs>
                        </svg>
                        <svg class="item_icon_arrow item_icon_dark" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                          <g clip-path="url(#clip0_9431_13269)">
                            <path d="M4.74831 13.1469C4.99845 13.397 5.404 13.397 5.65414 13.1469L11.2009 7.60008C11.5323 7.26872 11.5323 6.73149 11.2009 6.40014L5.6865 0.885714C5.41849 0.617707 4.98396 0.617708 4.71596 0.885715C4.44795 1.15372 4.44795 1.58825 4.71596 1.85625L9.65646 6.79676C9.8045 6.9448 9.8045 7.18482 9.65646 7.33286L4.74831 12.241C4.49817 12.4912 4.49817 12.8967 4.74831 13.1469Z" fill="white"/>
                          </g>
                          <defs>
                            <clipPath id="clip0_9431_13269">
                              <rect width="14" height="14" fill="white"/>
                            </clipPath>
                          </defs>
                        </svg>
                      </div>
                    </li>
            </ul>
            <div class="ai_box_footer">
              <div class="ai_box_toai go_to_app" extinfo="chat" @click="toai()">
                <div>
                  <div>
                    <img src="~assets/images/ai/article_ai_button_ai.png" alt="">
                  </div>
                </div>
              </div>
              <div class="ai_box_toauthor" @click="toauth">向作者追问</div>
            </div>
          </div>
        </div>
      </template>
    </ContainerPage>
  </div>
</template>

<script>
import {ContainerPage} from "../../../../opt-components/template";
import {
  InfoTopTitle,
  InfoContent,
  RelatedArticles,
  InfoComments,
  Sidebar,
  CircleEntrance,
  ColumnEntrance,
  SearchKeyword,
  RelatedCategories,
  GuidanceEntrance,
  PDFDownload,
  Progress,
  InfoCatalogue,
  CompilationEntry,
  ContactUs,
  ClinicalRecruitment,
  ActivityModule,
  RecruitmentTrainingEntrance,
  PDFTranslateDownload,
  BottomToolbars
} from "../../../../opt-components/page/info-detail";
import {
  comment,
  commentDiggs,
  diggs, getArticleHonerInfo, getArticleSpecials, getBrandActivityList, getCommunityByArticleAndType,
  getInfo,
  getInfoCommentsPage, getInfoMiniTool, getInfoProducts,
  getInfoSearchKeyWord,
  getRelatedArticles
} from "../../../../api/article";
import {infoCollect} from "../../../../api/user";
import {saveBrowsingHistory} from "../../../../api/browsing-history";
import { getArticleAiList } from '../../../../api/ai.js'

export default {
  name: "InfoDetailPage",
  components: {
    BottomToolbars,
    ContainerPage,
    InfoTopTitle,
    InfoContent,
    RelatedArticles,
    InfoComments,
    Sidebar,
    CircleEntrance,
    ColumnEntrance,
    SearchKeyword,
    RelatedCategories,
    GuidanceEntrance,
    PDFDownload,
    Progress,
    InfoCatalogue,
    CompilationEntry,
    ContactUs,
    ClinicalRecruitment,
    ActivityModule,
    RecruitmentTrainingEntrance,
    PDFTranslateDownload
  },
  /**
   * 目前文章有 五种模板
   * 五种模板的业务逻辑高度相似 但个别的接口不同
   * 所以我采用 一个文件判断不同模板 使用不同接口的方式 来做文章详情
   *
   * (但现在 没有办法区分  后台是按什么判断的 不同接口)
   * @param app
   * @param params
   * @param error
   * @param store
   * @param query
   * @param req
   * @returns {Promise<void>}
   */
  async asyncData({app, params, error, store, query, req}) {
    const [Request1, Request2, ArticleHonerInfo, InfoMiniTool, ArticleSpecials, CircleData, TalkData, InfoProducts, BrandActivityData] = await Promise.all([
      // 文章详情
      app.$axios.$request(getInfo({
        infoId: query.id,
        userId: store.state.auth.user.id
      })),
      // 关键字
      app.$axios.$request(getInfoSearchKeyWord({
        infoId: query.id,
      })),
      // 荣誉信息
      app.$axios.$request(getArticleHonerInfo({
        articleId: query.id,
        articleType: 'I' // 文章类型 A (UGC)| I (PGC)
      })),
      // 评分小工具
      app.$axios.$request(getInfoMiniTool({
        infoId: query.id,
      })),
      // 所属专栏
      app.$axios.$request(getArticleSpecials({
        articleId: query.id,
        type: 'P',
        loginUserId: store.state.auth.user.id
      })),
      // 所属圈子
      app.$axios.$request(getCommunityByArticleAndType({
        articleId: query.id,
        userId: store.state.auth.user.id,
        articleType: 'P',
        communityType: 'C'   // 话题：T ,圈子：C 活动：A
      })),
      // 所属话题
      app.$axios.$request(getCommunityByArticleAndType({
        articleId: query.id,
        userId: store.state.auth.user.id,
        articleType: 'P',
        communityType: 'T'   // 话题：T ,圈子：C 活动：A
      })),
      // 相关产品
      app.$axios.$request(getInfoProducts({
        infoId: query.id,
      })),
      // 活动专区
      app.$axios.$request(getBrandActivityList({
        infoId: query.id,
        loginUserId: store.state.auth.user.id
      }))
    ])
    /**
     * BUSINESS("business", "商务企业模板"),
     * GUIDE("guide", "指南共识模板"),
     * COMPILE("compile", "编译模板");
     * 普通模板为空
     */
    let Template = Request1.result.info.style
    // 是否为临床招募 "T" || "F"
    const isClinicalRecruitment = Request1.result.isClinicalRecruitment
    // 是否为招聘与培训
    const isInfoTrainRecruit = Request1.result.isInfoTrainRecruit
    if (isInfoTrainRecruit === 'A' || isInfoTrainRecruit === 'T' || isInfoTrainRecruit === 'R') {
      Template = "InfoTrainRecruit"
    }

    // 是否为病例
    const isCase = Request1.result.info.isCase

    let attrArr = []
    if (isCase === 'T' && Request1.result.info.subspecialtys && Request1.result.info.subspecialtys.length > 0) {
      Request1.result.info.subspecialtys.forEach(item => {
        let newName = item.name
        if (item.children && item.children.length > 0) {
          item.children.forEach(itemChild => {
            newName = `${item.name}-${itemChild.name}`
            attrArr.push({
              id: item.id,
              name: newName
            })
          })
        } else {
          attrArr.push({
            id: item.id,
            name: newName
          })
        }
      })
    } else if (Template === 'compile' && Request1.result.attrs && Request1.result.attrs.length > 0) {
      attrArr = Request1.result.attrs
    } else if ((Template === 'InfoTrainRecruit' || isClinicalRecruitment === 'T') && Request1.result.info.trainRecruitLabels && Request1.result.info.trainRecruitLabels.length > 0) {
      attrArr = Request1.result.info.trainRecruitLabels.map(item => {
        return {
          id: item.id,
          name: item.labelName
        }
      })
    }


    const InfoData = {
      infoId: Request1.result.info.id,
      bmsAuth: Request1.result.info.bmsAuth,
      attrs: attrArr,
      publishDate: Request1.result.info.publishDate,
      comments: Request1.result.info.comments,
      showViews: Request1.result.info.showViews,
      showDiggs: Request1.result.info.showDiggs,
      showCollects: Request1.result.info.showCollects,
      metaDescription: Request1.result.info.metaDescription,
      summary: Request1.result.info.summary,
      guideSource: Request1.result.info.guideSource,
      title: Request1.result.info.title,
      authorList: Request1.result.authorList,
      authorNames: Request1.result.info.authorNames,
      text: Request1.result.text,
      isArticleDigg: Request1.result.isArticleDigg,
      isArticleCollect: Request1.result.isArticleCollect,
      pdfUrl: Request1.result.info.pdfUrl,
      pdfTranslateUrl: Request1.result.info.pdfTranslateUrl,
      guideAdditionInfoList: Request1.result.guideAdditionInfoList,
      isCase: Request1.result.info.isCase
    }


    // 关键字列表
    const searchKeyWordList = Request2.list || []

    // tdk
    let keywordsName = ""
    if (searchKeyWordList.length > 0) {
      searchKeyWordList.forEach(item => {
        keywordsName += item + ","
      })
    }

    let authorNames = ""
    let authorNameAndCompany = ""
    if (InfoData.authorList && InfoData.authorList.length > 0) {
      InfoData.authorList.forEach(item => {
        authorNames += item.authorName + ","
        authorNameAndCompany += `${item.company ? item.company + "_" : ""}${item.authorName}、`
      })
    }
    authorNameAndCompany = authorNameAndCompany.slice(0, authorNameAndCompany.length - 1)

    let attrsNames = ""

    if (InfoData.attrs && InfoData.attrs.length > 0) {
      InfoData.attrs.forEach(item => {
        attrsNames += item.name + ","
      })
    }


    return {
      InfoData,
      Template,
      isClinicalRecruitment,
      isInfoTrainRecruit,
      searchKeyWordList,
      articleHonerInfo: ArticleHonerInfo.result || {},
      infoMiniTool: InfoMiniTool.list || [],
      articleSpecials: ArticleSpecials.list || [],
      circleList: CircleData.list || [],
      talkList: TalkData.list || [],
      infoProducts: InfoProducts.list || [],
      brandActivityList: BrandActivityData.list || [],
      keywordsName,
      authorNames,
      authorNameAndCompany,
      attrsNames
    }
  },
  data() {
    return {
      commentData: {
        list: [],
        page: {}
      },
      relatedArticleData: {
        list: [],
        page: {}
      },
      aiList: []
    }
  },
  head() {
    return {
      title: this.InfoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.InfoData.summary || this.InfoData.metaDescription || `${this.authorNameAndCompany}，文章分享：${this.InfoData.title}）`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }]
    }
  },
  mounted() {
    // 客户端请求接口
    this.getDataHandler()
    // 评论列表
    this.articleCommentListHandler({pageNo: 1});

    this.$nextTick(() => {
      // 计算右侧栏目高度
      window.setTimeout(() => {
        const rightWrapper = this.$refs.page_right_wrapper
        if (rightWrapper) {
          const rightTop = (rightWrapper.offsetHeight - window.innerHeight) + 32

          if (rightWrapper.offsetHeight > (window.innerHeight - 84)) {
            rightWrapper.style.top = rightTop > 0 ? "-" + rightTop + "px" : rightTop + "px"
          }

        }
      }, 10)
    })

    // 采集历史记录
    this.saveBrowsingHistoryHandler()
    this.getAi()

  },
  methods: {
    // 监听滚动条是上还是下
    addScollorHandler() {

    },
    // 采集浏览记录
    saveBrowsingHistoryHandler() {
      if (this.$store.state.auth.token) {
        this.$axios.$request(saveBrowsingHistory({
          loginUserId: this.$store.state.auth.user.id,
          contentSource: 1,
          contentId: this.$route.query.id,
          courseId: null,
          playDuration: null
        }))
      }

      // 顺便埋个点
      let company = ''
      let name = '' // 作者名字
      if (this.InfoData.authorList && this.InfoData.authorList.length > 0) {
        this.InfoData.authorList.forEach(item => {
          company += item.company + ','
          name += item.authorName + ','
        })
      }

      this.$analysys.view_info_page(this.InfoData.title, company, [], '', '图文', [], String(this.InfoData.infoId), name, 0)
    },

    // 获取相关文章
    async getDataHandler() {
      const [relatedArticleData] = await Promise.all([
        this.$axios.$request(getRelatedArticles({
          articleId: this.$route.query.id,
          count: 4,
          type: 'pgc',
          userId: this.$store.state.auth.user.id
        }))
      ])
      if (relatedArticleData.code === 1) {
        this.$set(this.relatedArticleData, 'list', relatedArticleData.list);
      }
    },

    // 文章点赞
    articleLikeHandler() {
      this.$set(this.InfoData, "isArticleDigg", !this.InfoData.isArticleDigg)
      // 埋点
      this.$analysys.like('', '', '', '点赞原文', '', '', '', '', [], [], '资讯', '', '', String(this.$route.query.id))
      // 点赞接口
      this.$axios.$request(diggs({
        articleId: this.$route.query.id,
        userId: this.$store.state.auth.user.id
      }))
    },

    // 文章收藏
    articleCollectHandler() {
      this.$set(this.InfoData, "isArticleCollect", !this.InfoData.isArticleCollect)
      // 埋点
      this.$analysys.add_to_favorite('', '', '', [], '', '', '', [], '', '', String(this.$route.query.id), '', '收藏原文', this.InfoData.title)
      // 点赞接口
      this.$axios.$request(infoCollect({
        articleId: this.$route.query.id
      }))
    },

    // 文章评论列表
    async articleCommentListHandler({pageNo = 1}) {
      const data = await this.$axios.$request(getInfoCommentsPage({
        infoId: this.$route.query.id,
        userId: this.$store.state.auth.user.id,
        order: 0,
        pageNo,
        pageSize: 10
      }))

      if (data.code === 1) {
        const list = data.result.list.map(item => {
          return item.parent
        })

        this.$set(this.commentData, 'list', this.commentData.list.concat(list));
        this.$set(this.commentData, 'page', data.result.page);
      }
    },

    // 评论点赞
    likeHandler(id, backFn) {
      this.$analysys.like('', '', '', '点赞留言', '', '', '', '', [], [], 'pgc', '', '', String(id))
      this.$axios.$request(commentDiggs({
        commentId: id,
        userId: this.$store.state.auth.user.id
      })).then((response) => {
        !response.result && response.code === 1 ? backFn(true) : backFn(false)
      })
    },

    // 评论发送
    pushHandler({info, parentId}, backFn) {
      this.$analysys.comment('评论留言', '', '', '', [], '', '', '', '', String(this.replyId), '', '评论留言', [], String(document.title), '')
      this.$axios.$request(comment({
        text: info,
        parentId,
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.query.id
      })).then((response) => {
        if (response && response.code === 1) {
          backFn(response.result)
          this.$toast(response.message)
        } else {
          backFn(false)
          this.$toast(response.message)
        }
      })
    },

    async getAi () {
      const res = await this.$axios.$request(getArticleAiList({
        infoId: this.$route.query.id,
        resultCount: 3
      }))
      if (res && res.code === 1 && res.list) {
        this.aiList = [...res.list]
        this.$nextTick(() => {
          // 计算右侧栏目高度
          window.setTimeout(() => {
            const rightWrapper = this.$refs.page_right_wrapper
            if (rightWrapper) {
              const rightTop = (rightWrapper.offsetHeight - window.innerHeight) + 32

              if (rightWrapper.offsetHeight > (window.innerHeight - 84)) {
                rightWrapper.style.top = rightTop > 0 ? "-" + rightTop + "px" : rightTop + "px"
              }

            }
          }, 10)
        })
      }
    },
    toai (i) {
      if (i) {
        this.$analysys.btn_click('启发追问-' + i, this.InfoData.title)
        window.open('/askAI/char/home?question=' + i)
      } else {
        this.$analysys.btn_click('向AI自由提问', this.InfoData.title)
        window.open('/askAI/char/home')
      }
    },
    toauth () {
      this.$analysys.btn_click('向作者追问', this.InfoData.title)
      document.querySelector('#PostInput').focus()
    }
  }
}
</script>
<style>
@media screen and (max-width: 1280px) {
  #bottom_tool_bars_container_id {
    display: block;
  }

  #sidebar_wrapper_tool_bars_id {
    display: none !important;
  }
}

@media screen and (min-width: 1280px) {
  #bottom_tool_bars_container_id {
    display: none;
  }

  #sidebar_wrapper_tool_bars_id {
    display: block !important;
  }
}
</style>
<style scoped lang="less">
@import "./styles";
.bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.article_ai_container {
  margin-top: 24px;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  border: 0.5px solid #E7E7FF;
  background: linear-gradient(180deg, #EDF3FF 0%, #FFF 71.81%);
  box-sizing: border-box;
  & * {
    box-sizing: border-box;
  }
  .ai_box_header {
    padding: 11px 17px 0 14px;
    width: 100%;
    height: 35px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-image: url("~assets/images/ai/article_ai_header_bg.png");
  }
  .ai_header_title {
    width: 93px;
    height: 23px;
    background-image: url("~assets/images/ai/article_ai_title.png");
  }
  .ai_header_sub_title {
    color: #8C9CA7;
    font-size: 13px;
  }
  .ai_box_list {
    width: 100%;
    padding: 0 15px;
  }
  .ai_item {
    padding: 15px 0;
    border-bottom: 1px dashed #e0e9fb;
    display: flex;
    justify-content: space-between;
  }
  .ai_item_container {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    color: #333;
    font-size: 15px;
    font-weight: 500;
  }
  .ai_item_index {
    font-style: normal;
    white-space: nowrap;
    flex-shrink: 0;
  }
  .ai_item_text {
    max-width: 216px;
    /*display: -webkit-box;*/
    /*-webkit-box-orient: vertical;*/
    /*-webkit-line-clamp: 2;*/
    /*overflow: hidden;*/
    /*text-overflow: ellipsis;*/
  }
  .ai-item_icon {
    width: 14px;
    height: 14px;
  }

  .ai_box_footer {
    padding: 14px 20px 20px;
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .ai_box_footer div {
    flex: 1;
    height: 40px;
  }
  .ai_box_toai {
    margin-right: 15px;
    height: 100%;
    position: relative;
    border-radius: 24px;
    overflow: hidden;
  }
  .ai_box_toai::after{
    width: 100%;
    height: 100%;
    content: '';
    position: absolute;
    transform: translate(72px, 22px) rotate(-20deg) scaleX(3) scaleY(7);
    background: radial-gradient(circle at top left,#FF98E2 6.5%, #9F53FF 37.5%, #52C5FF 65%, #6B6EFA 89.5%);

  }
  .ai_box_toai > div {
    z-index: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    padding: 2px;
    box-sizing: border-box;
    border-radius: 24px;
  }
  .ai_box_toai > div > div {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .ai_box_toai img {
    width: 83px;
  }
  .ai_box_toauthor {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFF;
    font-size: 14px;
    border-radius: 44px;
    background: linear-gradient(90deg, #2DAEFF 0%, #008CE2 100%), linear-gradient(93deg, #308CFF 2.76%, #0581CE 97.24%);
  }
  .item_icon_dark {
    display: none;
  }
}


</style>
