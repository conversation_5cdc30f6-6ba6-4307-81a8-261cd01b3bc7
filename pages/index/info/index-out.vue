<template>
  <div class='container-box consulting-box'>
    <el-row>
      <div class='page-left'>
        <div class='consulting-left'>
          <NavTwo
            :channelType='channelType ? channelType : "F"'
            :subSpecialtyCurrent='subSpecialtyName'
            :subSpecialtyData='subSpecialtyData'
            @selectRecommendFun='selectRecommendFun'
            @selectsubSpecialtyId='selectsubSpecialtyId'
          ></NavTwo>

          <div class='NewConsulting'>
            <ul ref='consultingref' class='consulting'>
              <el-skeleton
                :count='$store.state.pager_count'
                :loading='false'
                :rows='60'
                animated
              >
                <template slot='template'>
                  <li class='consulting-item'>
                    <div class='item-left' style='height: 126px'>
                      <el-skeleton-item style='height: 100%' variant='image' />
                    </div>
                    <div class='item-right' style='width: 100%'>
                      <p class='pone text-limit-1'>
                        <el-skeleton-item
                          class='pone'
                          style='margin-right: 16px'
                          variant='text'
                        />
                      </p>
                      <p class='pthree'>
                        <el-skeleton-item style='width: 30%' variant='text' />
                        <el-skeleton-item
                          style='margin-left: 10%; width: 30%'
                          variant='text'
                        />
                      </p>
                      <p class='ptwo text-limit-2'>
                        <el-skeleton-item style='width: 100%' variant='text' />
                      </p>
                    </div>
                  </li>
                </template>
                <template>
                  <li
                    v-for='item in consultingData'
                    :key='item.infoId'
                    class='consulting-item'
                    @click='jumpDetailsPageFun(item.infoId)'
                  >
                    <div class='item-left'>
                      <img
                        v-if='item.infoImg'
                        id='articleImg'
                        :src='$tool.compressImg(item.infoImg,226,124)'
                        class='img_cover'
                      />
                      <img v-else class='img_cover' src='~assets/images/default16.png' />
                      <div
                        v-if='false'
                        v-show='item.infoImg !== null'
                        class='position_top'
                      >
                        文章
                      </div>
                    </div>
                    <div class='item-right'>
                      <p class='pone fontWeight text-limit-2'>
                        <svg-icon
                          v-if="item.essences === 'T'"
                          class-name='poneIcon cursor'
                          icon-class='jinghua'
                        ></svg-icon>
                        <span class='vertical_align_middle'> {{ item.infoTitle }}</span>
                      </p>
                      <p class='pthree text-limit-1'>
                        <i class='el-icon-time'></i>
                        <span style='margin-right: 22px'>{{
                            timeStamp.timestampFormat(
                              Date.parse(item.publishDate.replace(/-/g, '/')) / 1000
                            )
                          }}</span>
                        <span>
                          <svg-icon
                            class-name='pthreeIcon cursor'
                            icon-class='yuanchan'
                          ></svg-icon>
                          <span>{{
                              item.authorNames
                                ? item.authorNames
                                : '神外资讯原创'
                            }}</span>
                        </span>
                      </p>
                      <p class='ptwo text-limit-2'>
                        {{ item.description }}
                      </p>
                    </div>
                  </li>
                </template>
              </el-skeleton>
            </ul>
          </div>
          <Empty :loading='loading' :no-more='!loading && consultingData.length===0' height='70vh' />
          <el-pagination
            v-if='!loading'
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='$store.state.article_count'
            :pager-count='$store.state.pager_count'
            :total='total'
            background
            small
            style='text-align: center; margin-bottom: 10px'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </div>
      <div class='page-right'>
        <div class='consulting-right'>
          <div v-for='item in advertisementData' :key='item.adId' class='advertisement' :title="item.name">
            <img
              :class="item.extras ? 'cursor' : ''"
              :src='$tool.compressImg(item.image,387,132)'
              :alt='item.name'
              class='img_cover'
              @click='jumpBannerFun(item)'
            />
          </div>
          <!--hot channel Start-->
          <hot-channel v-if='hotChannelData.length > 0' :hotChannelData='hotChannelData' title='热门频道'></hot-channel>
          <!--End-->
          <literature :literatureNewsData.sync='literatureNewsData'></literature>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
import NavTwo from '@/components/NavigationTwo/NavigationTwo'
import literature from '@/components/PageComponents/Literature/Literature' // 文献速览
import { getCompileChannel, getInfoList, literatureNews } from '@/api/article'
import { getBanner } from '@/api/home'
import HotChannel from '@/components/PageComponents/HotChannel/HotChannel'
import { getWebApiHotChannel } from '@/api/channel'
import Empty from '@/components/UI/Empty/Empty'
import {getSlotContent} from "../../../api/banner/banner";
import brandAdJump from "../../../assets/helpers/brandAdJump";

export default {
  name: 'ConsultingPage',
  // eslint-disable-next-line require-await
  async asyncData({ app, params, error, store, query, req }) {
    app.head.title = '脑医汇 - 医学资讯 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台'
    const [request1, request2, request3, request4, hotChannelData] = await Promise.all([
      app.$axios.$request(
        getInfoList({
          channelId: query.subSpecialtyId ? query.subSpecialtyId : null,
          recommend: query.selectedFalg ? query.selectedFalg : null,
          pageNo: query.currentPage ? query.currentPage : Number(1),
          pageSize: store.state.article_count
        })
      ),
      app.$axios.$request(getCompileChannel()),
      app.$axios.$request(
        literatureNews({ pageNo: 1, pageSize: store.state.article_count })
      ),
      app.$axios.$request(
        getSlotContent({
          loginUserId:store.state.auth.user.id,
          detailIdStr:'',
          adCode:'webAPi_Information',
        })
      ),
      app.$axios.$request(
        getWebApiHotChannel()
      )
    ])
    let subSpecialtyData = request2.list
    // 给咨询筛选第一个加入全部
    if (typeof request2 !== 'string' && typeof subSpecialtyData !== 'string') {
      let msg = query.selectedFalg
        ? query.selectedFalg === 'T'
          ? '精华'
          : '全部'
        : '全部'
      subSpecialtyData.unshift({
        name: msg,
        id: 0,
        compileArticle: 'T'
      })
    }
    let subCurrent = subSpecialtyData.findIndex((item) => item.code === 'shenjingkexue')
    subCurrent !== -1 ? subSpecialtyData.splice(subCurrent, 1) : null // 删除神经科学
    return {
      consultingData: request1.list,
      total: request1.page.totalCount,
      subSpecialtyData,
      literatureNewsData: request3.list,
      advertisementData: request4.list,
      currentPage: query.currentPage ? Number(query.currentPage) : 1, // 分页页码
      channelIndex: query.subSpecialtyId ? query.subSpecialtyId : null, // 亚专业ID
      subSpecialtyName: query.subSpecialtyName ? query.subSpecialtyName : '全部', // 选中亚专业名称
      channelType: query.selectedFalg ? query.selectedFalg : null, // 是否为精选
      hotChannelData: hotChannelData.list// 热门频道数据
    }
  },
  head() {
    return {
      title: '脑医汇 - 医学资讯 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  data() {
    return {
      loading: false
    }
  },
  mounted() {
  },
  watch: {
    $route(res) {
      this.currentPage = Number(res.query.currentPage)
      this.channelIndex = res.query.subSpecialtyId
      this.channelType = res.query.selectedFalg
      this.subSpecialtyName = res.query.subSpecialtyName
    }
  },
  methods: {
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId : item.adId,
        adUrl : item.extras,
        adModule : item.module,
        unionid : this.$store.state.auth.unionid,
        adClickLocation : item.clickLocation,
        adCode : item.code,
        adExtras : item.extras,
        adName : item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    // 切换亚专业
    selectsubSpecialtyId(index) {
      if (!this.loading) {
        this.loading = true
        this.currentPage = 1
        this.consultingData = []
        this.channelIndex = index.channelId
        this.channelType = null
        this.channelType === 'T'
          ? (this.subSpecialtyData[0].name = '精华')
          : (this.subSpecialtyData[0].name = '全部')
        this.$analysys.btn_click(index.name, document.title)
        this.$axios
          .$request(
            getInfoList({
              channelId: index.channelId === 0 ? null : index.channelId,
              pageNo: 1,
              pageSize: this.$store.state.article_count
            })
          )
          .then((res) => {
            if (res && res.code === 1) {
              this.loading = false
              this.consultingData = res.list
              this.total = res.page.totalCount
            }
          })
      }
    },
    // 切换精华
    selectRecommendFun(type) {
      type === 'T'
        ? (this.subSpecialtyData[0].name = '精华')
        : (this.subSpecialtyData[0].name = '全部')
      this.channelType = type
      this.currentPage = 1
      this.$axios
        .$request(
          getInfoList({
            channelId: this.channelIndex === 0 ? null : this.channelIndex,
            pageNo: 1,
            pageSize: this.$store.state.article_count,
            recommend: type
          })
        )
        .then((res) => {
          if (res && res.code === 1) {
            this.consultingData = res.list
            this.total = res.page.totalCount
          }
        })
    },
    // 分页
    handleCurrentChange(item) {
      this.$tool.scrollIntoView()
      this.$axios
        .$request(
          getInfoList({
            pageNo: item,
            pageSize: this.$store.state.article_count,
            channelId: this.channelIndex === 0 ? null : this.channelIndex,
            recommend: this.channelType
          })
        )
        .then((res) => {
          this.consultingData = res.list
          this.total = res.page.totalCount
        })
    },
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const { href } = this.$router.resolve({
        name: 'index-info-detail',
        query: {
          id: infoid
        }
      })
      window.open(href, '_blank')
    }
  },
  components: {
    NavTwo,
    literature,
    HotChannel,
    Empty
  }
}
</script>

<style lang='less' scoped>
@import "~@/pages/index/info/index.less";
</style>
