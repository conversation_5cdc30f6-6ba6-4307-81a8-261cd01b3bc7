<template>
  <Container :styles="{display:'flex',padding:'16px 0 32px'}">
    <div class="sub_major_container">
      <SideNav
        :data="formatSubMajorList"
        :link-index="linkIndex"
        @changeIndex="changeSubMajorIndex"
      />
    </div>
    <div class="list_container">
      <div class="essence_list">
        <div class="essence_item" :class="recommend === null && bmsAuth === null ? 'active_essence_item' : ''"
             @click="changeEssence(false)">全部
        </div>
        <div class="essence_item" :class="recommend === 'T' ? 'active_essence_item' : ''" @click="changeEssence(true)">
          精华
        </div>
        <div class="essence_item" :class="bmsAuth === 1 ? 'active_essence_item' : ''" @click="changeBmsAuth(1)">
          品牌认证
        </div>
      </div>
      <RollingLoad
        :loading="loading"
        :empty="infoData.list.length===0"
        :no-more="currentPageNo >= infoData.page.totalPage"
        empty-height="50vh"
        @hit-bottom="hitBottomChangeHandler"
      >
        <template #loading>
          <InfoSkeleton :limit="16" :loading="loading"/>
        </template>
        <div class="skeleton_container">
          <template v-for="(item,index) in infoData.list">
            <InfoDefaultItem
              v-if="item.type === 'info'"
              :key="index"
              :info-id="item.id"
              :image="item.infoImg"
              :title="item.title"
              :author-names="authorNames(item.authorList)"
              :publish-date="timeStamp.timeFormat(Date.parse(item.publishDate.replace(/-/g, '/')) / 1000)"
              :product-list="item.productList"
              :essences="item.essences"
              :essences-flag="recommend!=='T'"
              :bms-auth="item.bmsAuth"
            />
            <ShortVideoDefaultItem
              v-else-if="item.type === 'sv'"
              :key="index"
              :info-id="item.id"
              :image="item.infoImg"
              :title="item.title"
              :author-names="authorNames(item.authorList)"
              :publish-date="timeStamp.timeFormat(Date.parse(item.publishDate.replace(/-/g, '/')) / 1000)"
              :product-list="item.productList"
              :essences="item.essences"
              :essences-flag="recommend!=='T'"
              :bms-auth="item.bmsAuth"
            />
          </template>

        </div>
      </RollingLoad>
    </div>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </Container>
</template>

<script>
import ShortVideoPlayback from "../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import RollingLoad from "../../../opt-components/component/RollingLoad/index.vue";
import {Container} from "../../../opt-components/template";
import {SideNav} from "../../../opt-components/page/info";
import {getCompileChannel, getInformation} from "../../../api/article";
import {InfoDefaultItem, ShortVideoDefaultItem} from "../../../opt-components/data-list";
import {InfoSkeleton} from "../../../opt-components/ui/skeleton";
import {userInfo} from "../../../api/user";

export default {
  name: "InfoListPage",
  components: {
    ShortVideoDefaultItem,
    Container,
    SideNav,
    InfoDefaultItem,
    InfoSkeleton,
    RollingLoad,
    ShortVideoPlayback
  },
  async asyncData({app, params, error, store, query, req}) {
    const [subMajorList] = await Promise.all([
      app.$axios.$request(getCompileChannel())
    ])

    let userData = null
    if (store.state.auth.token) {
      userData = await app.$axios.$request(userInfo())
    }

    return {
      subMajorList: subMajorList.list,
      userData: userData?.result
    }
  },
  data() {
    return {
      loading: true,
      infoData: {
        list: [],
        page: {}
      },
      currentPageNo: 1,
      linkIndex: 0,
      channelId: null,
      recommend: null,
      bmsAuth: null,
    }
  },
  head() {
    return {
      title: '脑医汇 - 医学资讯 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  computed: {
    formatSubMajorList() {
      if (this.subMajorList && this.subMajorList.length > 0) {
        const newList = this.subMajorList.map(item => {
          return {
            id: item.channelId,
            mpSubspecialityId: item.mpSubspecialityId,
            name: item.name
          }
        })

        newList.unshift({
          id: null,
          name: "全部"
        })

        return newList

      } else {
        return []
      }
    }
  },
  mounted() {
    const specialityId = this.userData && this.userData.specialityList.length > 0 ? this.userData.specialityList[0].id : null
    if (specialityId) {
      this.formatSubMajorList.forEach((item, index) => {
        if (item.mpSubspecialityId === specialityId) {
          this.channelId = item.id
          this.linkIndex = index
        }
      })
    }

    this.getDataHandler({
      pageNo: this.currentPageNo,
      channelId: this.channelId,
      recommend: this.recommend,
      bmsAuth: this.bmsAuth
    })
  },
  methods: {
    async getDataHandler({pageNo, channelId, recommend, bmsAuth, pageUp} = {pageNo: 1}) {
      const infoData = await this.$axios.$request(getInformation({
        pageNo,
        pageSize: 16,

        channelId,
        recommend,
        bmsAuth
      }))
      if (infoData.code === 1) {

        if (pageUp) {
          this.$set(this.infoData, "list", [...this.infoData.list, ...infoData.list])
          this.$set(this.infoData, "page", infoData.page)
        } else {
          this.$set(this.infoData, "list", infoData.list)
          this.$set(this.infoData, "page", infoData.page)
        }
      }

      this.loading = false;
    },

    changeSubMajorIndex(index) {
      const channelId = this.formatSubMajorList[index].id

      if (this.channelId !== channelId) {
        this.$tool.scrollIntoTop()
        this.channelId = channelId
        this.currentPageNo = 1;
        this.$set(this.infoData, "list", [])
        this.$set(this.infoData, "page", {})
        this.loading = true

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          recommend: this.recommend,
          bmsAuth: this.bmsAuth
        })
      }
    },

    changeEssence(isEssence) {
      this.bmsAuth = null;
      const essence = isEssence ? "T" : null
      if (this.recommend !== essence) {
        this.$tool.scrollIntoTop()
        this.recommend = essence
        this.currentPageNo = 1;
        this.$set(this.infoData, "list", [])
        this.$set(this.infoData, "page", {})
        this.loading = true

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          recommend: this.recommend,
          bmsAuth: this.bmsAuth
        })

      }
    },
    changeBmsAuth(type) {
      this.recommend = null;
      if (this.bmsAuth !== 1) {
        this.$tool.scrollIntoTop()
        this.bmsAuth = type
        this.currentPageNo = 1;
        this.$set(this.infoData, "list", [])
        this.$set(this.infoData, "page", {})
        this.loading = true

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          recommend: this.recommend,
          bmsAuth: this.bmsAuth
        })
      }
    },

    hitBottomChangeHandler(flag) {
      if (flag) {
        this.currentPageNo += 1;
        this.loading = true

        this.getDataHandler({
          pageNo: this.currentPageNo,
          channelId: this.channelId,
          recommend: this.recommend,
          bmsAuth: this.bmsAuth,
          pageUp: true
        })
      }
    },
    authorNames(list) {
      let names = ""
      if (list && list.length > 1) {
        names = list[0].authorName + "等3位作者 "
      } else if (list && list.length === 1) {
        names = list[0].authorName
      } else {
        names = ""
      }

      return names
    },
  }
}
</script>

<style scoped lang="less">
.sub_major_container {
  width: 72px;
  flex-shrink: 0;
}

.list_container {
  flex: 1;
  margin-left: 24px;

  .essence_list {
    padding: 16px 0;
    margin-top: -16px;
    position: sticky;
    top: 60px;
    background: white;
    z-index: 10;

    .essence_item {
      display: inline-block;
      padding: 8px;
      border-radius: 4px;
      background: #F8F8F8;
      color: #676C74;
      font-size: 14px;
      line-height: 18px;
      margin-right: 16px;
      cursor: pointer;
      user-select: none;

      &:active {
        background: #EFFAFF;
      }

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        background: #EFFAFF;
        color: #0581CE;
      }
    }

    .active_essence_item {
      background: #EFFAFF;
      color: #0581CE;
      cursor: pointer;
    }
  }

  /deep/ .el-skeleton, .skeleton_container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 24px;
  }
}
</style>
