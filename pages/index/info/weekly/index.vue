<template>
  <div class="weekly_container">
    <Container :styles="{padding:'24px 0 32px'}">
      <div class="page_container">
        <!--        <div class="page_left">-->
        <!--          <WeeklyDate/>-->
        <!--        </div>-->
        <div class="page_right">
          <div class="weekly_title">
            <svg-icon icon-class="weekly_title_icon" class-name="weekly_icon"/>
            <span>周刊 · 第 {{ weeklyDetail.number }} 期</span>
          </div>
          <WeeklyList :list="weeklyList"/>
        </div>
      </div>
    </Container>
  </div>
</template>

<script>
import WeeklyList from "../../../../opt-components/page/weekly/WeeklyList/index.vue";
import {Container} from "../../../../opt-components/template";
import {getWeekly, getWeeklyContentList} from "../../../../api/article";

export default {
  name: "WeeklyPage",
  components: {Container, WeeklyList},
  async asyncData({app, params, error, store, query, req}) {
    const [request1, request2] = await Promise.all([
      app.$axios.$request(getWeekly({
        id: query.weeklyId
      })),
      app.$axios.$request(getWeeklyContentList({
        weeklyId: query.weeklyId
      }))
    ])

    return {
      weeklyDetail: request1.result,
      weeklyList: request2.list
    }
  },
  head() {
    return {
      title: "脑医汇周刊",
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.weeklyDetail.name
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.weeklyDetail.name
        }]
    }
  },
  mounted() {

  }
}
</script>

<style scoped lang="less">
.weekly_container {
  background: #eef4f6
}

.page_container {
  display: flex;
  justify-content: space-between;

  .page_right {
    width: 936px;
    margin: 0 auto;

    .weekly_title {
      width: 198px;
      height: 50px;
      background: linear-gradient(263deg, #34A6FF -6.9%, #0890E4 159.61%);
      color: #FFF;
      font-size: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px 20px 0 0; /* 右上角的圆角 */

      .weekly_icon {
        width: 26px;
        height: 26px;
        margin-right: 8px;
        flex-shrink: 0;
      }
    }
  }
}

</style>
