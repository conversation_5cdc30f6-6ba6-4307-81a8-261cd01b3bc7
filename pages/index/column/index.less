.article-column{
  padding: 26px 0 36px;
}
.column-box-ul {
  background: #FBFBFB;
  padding: 0 22px;
  box-sizing: border-box;
  margin: 20px 0;

  .item-active {
    font-weight: bold;
    color: #0581CE !important;
    position: relative;

    &::before {
      display: block;
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background: #0581CE;
    }
  }

  .item-list {
    line-height: 54px;
    margin-right: 60px;
    color: #666666;
  }
}
// 专栏列表开始
.columnContentBox {

  .el-row {
    margin: 0 -9px;
  }

  .el-col {
    padding: 0 9px;
  }

  .columnContentList {
    border-radius: 6px;
    overflow: hidden;
    background: #fbfbfb;
    margin-bottom: 20px;
    height: calc(100% - 20px);
    transition: all .3s;

    &:hover {
      box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
    }

    .columnConImage {
      height: 127px;
      overflow: hidden;
      position: relative;

      img {
        width: 100%;
        min-height: 100%;
      }
    }

    .articleTitle {
      font-size: 16px;
      line-height: 24px;
      margin: 14px 12px 12px;
      font-weight: 500;
      height: 44px;
    }

    .articleInfomation {
      margin-top: auto;
      line-height: 12px;
      margin-bottom: 14px;

      span {
        color: #708aa2;
        margin: 0 10px;
      }
      .time{
        display: inline-block;
        margin-top: 12px;
      }
    }
  }
}
.el-col-lg-4-8 {
  @media screen and (min-width: 1200px) {
    width: 20%;
  }
}
//专栏列表右上角
.position_top {
  position: absolute;
  top: 4px;
  right: 4px;
  border-radius: 6px;
  padding: 1px 6px;
  color: #ffffff;
  line-height: 16px;
  background: #0581CE;

  span {
    display: block;
    font-size: 12px;
    line-height: 14px;
    transform: scale(0.8);
  }
}
