<template>
  <Container :styles="{paddingBottom:'32px'}">
    <div style="position: sticky;top: 60px;z-index: 10;background: white">
      <ColumnTab
        :list="columnTabList"
        :site-type="siteType"
        @changeTab="changeTab"
      />
    </div>

    <div>
      <RollingLoad
        :loading="loading"
        :empty="columnData.list.length===0"
        :no-more="currentPageNo >= columnData.page.totalPage"
        empty-height="70vh"
        @hit-bottom="hitBottomChangeHandler"
      >
        <template #loading>
          <InfoSkeleton :limit="16" :loading="loading"/>
        </template>

        <div class="skeleton_container">
          <ColumnDefaultItem
            v-for="item in columnData.list"
            :key="item.id"
            :web-api-link-url="item.webApiLinkUrl"
            :column-id="item.id"
            :small-image-url="item.smallImageUrl"
            :title="item.title"
            :views="$tool.formatterNum(item.views)"
            :refers="$tool.formatterNum(item.refers)"
            :update-time="timeStamp.timestamp_13(item.updateTime,'y-m-d')"
          />
        </div>
      </RollingLoad>
    </div>
  </Container>
</template>

<script>
import {Container} from "../../../opt-components/template";
import {ColumnTab} from "../../../opt-components/page/column";
import {specials} from "../../../api/article";
import RollingLoad from "../../../opt-components/component/RollingLoad/index.vue";
import {InfoSkeleton} from "../../../opt-components/ui/skeleton";
import {ColumnDefaultItem} from "../../../opt-components/data-list";
import {userInfo} from "../../../api/user";

export default {
  name: "ColumnPage",
  components: {
    Container,
    ColumnTab,
    RollingLoad,
    InfoSkeleton,
    ColumnDefaultItem
  },
  async asyncData({app, params, error, store, query, req}) {

    let userData = null
    if (store.state.auth.token) {
      userData = await app.$axios.$request(userInfo())
    }

    return {
      userData: userData?.result
    }
  },
  data() {
    return {
      columnTabList: [
        {id: 1, title: '综合', code: 'F',},
        {id: 2, title: '介入', code: 'T',},
        {id: 3, title: '神经科学', code: 'B',}
      ],
      columnData: {
        list: [],
        page: {}
      },
      loading: true,
      currentPageNo: 1,
      siteType: "F"
    }
  },
  head() {
    return {
      title: '文章专栏',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '文章专栏'
        }
      ]
    }
  },
  mounted() {
    const specialityName = this.userData && this.userData.specialityList.length > 0 ? this.userData.specialityList[0].name : null
    if (specialityName) {
      if (specialityName === "神经介入") {
        this.siteType = "T"
      } else if (specialityName === "神经科学") {
        this.siteType = "B"
      }
    }


    this.getDataHandler({
      pageNo: this.currentPageNo,
      siteType: this.siteType
    })
  },
  methods: {
    async getDataHandler({siteType, pageNo, pageUp} = {pageNo: 1, siteType: this.siteType}) {
      const data = await this.$axios.$request(specials({
        userId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 16,
        siteType,
      }))

      if (data.code === 1) {

        if (pageUp) {
          this.$set(this.columnData, "list", [...this.columnData.list, ...data.list])
          this.$set(this.columnData, "page", data.page)
        } else {
          this.$set(this.columnData, "list", data.list)
          this.$set(this.columnData, "page", data.page)
        }

      }

      this.loading = false
    },
    changeTab(code) {
      if (this.siteType !== code) {
        this.columnData = {
          list: [],
          page: {}
        }

        this.siteType = code;
        this.currentPageNo = 1;
        this.loading = true;
        this.$tool.scrollIntoTop()
        this.getDataHandler({
          pageNo: this.currentPageNo,
          siteType: this.siteType
        })
      }

    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.currentPageNo += 1;
        this.loading = true;

        this.getDataHandler({
          pageNo: this.currentPageNo,
          siteType: this.siteType,
          pageUp: true
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.skeleton_container {
  //margin-bottom: 24px;
}

/deep/ .el-skeleton, .skeleton_container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 24px;
}
</style>
