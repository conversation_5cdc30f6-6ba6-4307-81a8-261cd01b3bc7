 <!--  -->
<template>
  <div style='overflow: hidden'>
    <div class='article-column container-box'>
      <!--Navigation Start-->
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/column' }">文章专栏</el-breadcrumb-item>
      </bm-breadcrumb>
      <!--End-->
      <!--Tab  Start -->
      <ul id='column-box' class='column-box-ul flex_start flex_align_center themeBorderRadius'>
        <li v-for='item in columnList'
            :key='item.id'
            :class='{"item-active":item.code=== $store.state.articeColumnListState}'
            @click='selectColumnDataFun(item.code)'
            class='item-list fontSize16 cursor userselect_none'>
          {{ item.title }}
        </li>
      </ul>
      <!--Tab End-->
      <!--栏目内容  Start -->
      <div class='column-content-div'>
        <transition mode='out-in' name='slide'>
          <!-- 专栏列表 -->
          <ul class='columnContentBox'>
            <el-row class='flex_flex'>
              <el-col v-for='item in articleColumn' :key='item.id' :lg="{span:'4-8'}" :md='8' :sm='8' :xs='12'>
                <li class='columnContentList flex_column cursor' @click="columnItem(item.id,item.webApiLinkUrl)">
                  <div class='columnConImage'>
                    <img v-if='item.smallImageUrl'
                         :src='$tool.compressImg(item.smallImageUrl,226,127)'
                         class='img_cover'/>
                    <img v-else class='img_cover' src='~assets/images/default16.png' />
                  </div>
                  <p class='articleTitle text-limit-2'>
                    {{ item.title }}
                  </p>
                  <p class='articleInfomation text-limit-1'>
                      <span
                        class='fontSize12 flex_shrink'>{{ item.views > 10000 ? item.views = (item.views / 10000).toFixed(1) + '万' : item.views
                        }}阅读 {{ item.refers }}文章</span>
                    <span class='time fontSize12 text-limit-1'>
                        {{(item.updateTime)?(timeStamp.timestamp_13(item.updateTime,'y-m-d')+'更新'):''}}
                      </span>
                  </p>
                </li>
              </el-col>
            </el-row>
          </ul>
        </transition>
      </div>
      <!--栏目内容 End-->
      <el-pagination
        :current-page.sync='currentPage'
        :hide-on-single-page='$store.state.hideOnSinglePage'
        :layout='$store.state.layout'
        :page-size='$store.state.meeting_count'
        :pager-count='$store.state.pager_count'
        :total='articleTotal'
        background
        small
        style='text-align: center; margin-bottom: 10px'
        @current-change='handleCurrentChange'
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { specials } from '@/api/article'

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  async asyncData({ app, params, error, store, query, req }) {
    const columnList = [
      { id: 1, title: '综合', code: 'F',}
      , { id: 2, title: '介入', code: 'T',}
      , { id: 3, title: '神经科学', code: 'B',}
    ] // 文章专栏分类
    const [request1] = await Promise.all([
      app.$axios.$request(specials({
        userId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: store.state.articleColumn_count,
        siteType: store.state.articeColumnListState,
      }))
    ])
    return {
      columnList,
      articleColumn: request1.list,
      articleTotal: request1.page.totalCount
    }
  },
  head() {
    return {
      title: '文章专栏',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '文章专栏'
        }
      ]
    }
  },
  data() {
    // 这里存放数据
    return {
      currentPage: 1,// 分页
    }
  },
  // 监听属性 类似于data概念
  computed: {
  },
  // 监控data中的数据变化
  watch: {
  },
  // 方法集合
  methods: {
    //专栏首页列表 item跳转
    columnItem(id,url){
      if(url){
        window.open(url, '_self')
      }else{
        this.$router.push({ name: 'index-column-detail-id', query: { id: id,type: 'O' } })
      }
    },
    /**
     * 切换栏目
     */
    selectColumnDataFun(code) {
      this.$store.commit('articleEditColumnListState', code)
      this.handleCurrentChange()
    },
    // 会议分页
    handleCurrentChange(item) {
      this.$tool.scrollIntoView()
      this.currentPage = item?item:1
      this.$axios.$request(specials({
        pageNo: this.currentPage,
        pageSize: this.$store.state.articleColumn_count,
        userId:  this.$store.state.auth.user.id,
        siteType: this.$store.state.articeColumnListState
      })).then(res => {
        if (res && res.code === 1) {
          this.articleColumn = res.list
          this.articleTotal = res.page.totalCount
        }
      })
    },
  },
}
</script>
<style lang='less' scoped>
@import "~@/pages/index/column/index.less";
</style>
