<template>
  <div style='overflow: hidden'>
    <div class='container-box article-details-con'>
      <!--Navigation Start-->
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/column' }">文章专栏</el-breadcrumb-item>
        <el-breadcrumb-item>{{ titleData.title }}</el-breadcrumb-item>
      </bm-breadcrumb>
      <!--End-->
      <el-row>
        <el-col :lg='16' :md='16' :sm='18' :xl='16' :xs='24'>
          <div class='article-details-con-left'>
            <el-row>
              <el-col :lg='24' :md='24' :sm='24' :xl='24' :xs='24'>
                <div class='article-title-box'>
                  <ul class='article-title-box-top'>
                    <li class='consulting-item'>
                      <div class='top-title-left'>
                        <img
                          v-if='titleData.smallImageUrl'
                          id='articleImg'
                          :src='$tool.compressImg(titleData.smallImageUrl,228,128)'
                          class='img_cover'
                        />
                        <img v-else class='img_cover' src='~assets/images/default16.png' />
                        <!--不是自建的会带精品专栏 图标-->
                        <div v-if="this.type =='O'" class='position_top'>
                          <span>精品专栏</span>
                        </div>
                      </div>
                      <div class='item-right flex_column'>
                        <p class='pone fontWeight text-limit-2'>
                        <span class='vertical_align_middle'>
                          {{ titleData.title }}
                        </span>
                        </p>
                        <p class='pthree text-limit-1'>
                          <span>{{ convertHours(titleData.views ? titleData.views : titleData.showViews)
                            }}阅读｜{{ convertHours(titleData.refers) }}内容</span>
                        </p>
                        <client-only>
                          <div class='flex_between'>
                            <div>
                              <p class='ptwo text-limit-2'>
                              </p>
                            </div>
                            <div class='text-bottom-right flex'>
                            <span
                              :class="titleData.isSubscribe?'SubscribeActive':''"
                              class='attention-btn flex_center flex_align_center cursor'
                              @click.stop='subscribeClick(titleData.id)'>
                              <svg-icon v-if='!titleData.isSubscribe' className='tab_icon flex_shirk'
                                        iconClass='subscribe'></svg-icon>
                              {{ titleData.isSubscribe ? '已关注' : '关注' }}
                            </span>
                              <!--分享-->
                              <span class='share_button flex_align_center flex cursor' @click='shareClick'>
                            <img alt='' src='~assets/images/codeIcon.png' style='width: 14px'>分享</span>
                            </div>
                          </div>
                        </client-only>
                      </div>
                    </li>
                  </ul>
                  <!--二级分类 Tab  Start -->
                  <ul v-if='tabsList1.length>0' class='tab-box_two'>
                    <li
                      v-for='(item, index) in tabsList1'
                      :key='item.id'
                      :class='{ "active": item.id === $store.state.articeDetailListState }'
                      class='tab-box_two_li'
                      @click='selectColumnDataFun(item.id)'
                    >
                      <span>{{ item.title }}</span>
                      <!--三级分类-->
                      <div v-if='item.children?item.children.length>0:false' class='three_case'>
                        <ul class='nav_three_select_box'>
                          <li
                            v-for='(itemX,indexX) in item.children'
                            :key='indexX'
                            :class='{ "is_active": $store.state.articeDetailreclassifyState === itemX.id }'
                            class='nav_two_select_list fontSize14'
                            @click='selectColumnDataFun(item.id,itemX.id)'
                          >{{ itemX.title }}
                          </li>
                        </ul>
                      </div>
                    </li>
                  </ul>
                  <!--Tab  End-->
                  <!--栏目内容  Start -->
                  <div class='column-content-div'>
                    <transition mode='out-in' name='slide'>
                      <!-- 专栏列表-->
                      <ul v-if="this.type =='O'" class='consulting'>
                        <li v-for='(item) in compileData' :key='item.id' class='consulting-item column-content cursor'
                            @click='jumpDetailsPageFun(item.id)'>
                          <div class='item-left'>
                            <img v-if='item.smallImageUrl' :src='$tool.compressImg(item.smallImageUrl,220,124)' alt=''
                                 class='img_cover'>
                            <img v-else class='img_cover' src='~assets/images/default16.png' />
                          </div>
                          <div class='item-right flex_column'>
                            <p class='pone text-limit-2 fontWeight'>
                              <svg-icon
                                v-if='item.essences=="T"'
                                class-name='poneIcon cursor'
                                icon-class='jinghua'
                              ></svg-icon>
                              <span class='vertical_align_middle'>
                                {{ item.title }}
                              </span>
                            </p>
                            <p class='pthree flex_start flex_align_center'>
                              <i class='el-icon-time' style='margin-right: 2px'></i>
                              <span style='margin-right: 22px'>
                                {{ item.publishDate }}
                              </span>
                              <span v-if='item.authorList.length>0' class='text-limit-2' style='flex: 1'>
                                <svg-icon
                                  class-name='pthreeIcon cursor'
                                  icon-class='yuanchan'
                                ></svg-icon>
                                <span v-for='itemauthorList in item.authorList' v-if='item.authorList.length>0'
                                      :key='itemauthorList.id'
                                      class='authorList'>{{ itemauthorList.authorName }}</span>
                              </span>
                            </p>
                            <ul class='lable-box'>
                              <li v-for='item in item.attrs' :key='item.id' class='lable-item'>
                                {{ item.name }}
                              </li>
                            </ul>
                            <p class='ptwo text-limit-1'>
                              <!--                              {{ item.title }}-->
                            </p>
                          </div>
                        </li>
                      </ul>
                      <!-- 自建专栏列表-->
                      <div v-if="this.type =='N'">
                        <ul v-for='(item,index) in specialData' :key='index'>
                          <li class='column-content'>
                            <!-- "mp_article", "病例夹（UGC）" -->
                            <CaseItem v-if='item.type === "mp_article"' :data-list='item' />
                            <!-- "info","PGC"" -->
                            <InfoItem v-if='item.type === "info"' :data-list='item' :type='type' />
                            <!-- "ocs_course", "云课堂"-->
                            <div v-if='item.type === "ocs_course"'
                                 class='information-list-box cloud-classroom-component flex_start'
                                 @click='jumpCurriculumFun(item.ocs_course.id)'>
                              <div class='left_image themeBorderRadius overflow_hidden flex_shrink'>
                                <img v-if='item.ocs_course.cover'
                                     :src='$tool.compressImg(item.ocs_course.cover,220,124)' alt=''
                                     class='img_cover'>
                                <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
                                <div class='type cloud-classroom-component-type themeBorderRadius'>
                                  {{ type === 'usercenter' ? '课程' : '云课堂' }}
                                </div>
                              </div>
                              <div class='right_info'>
                                <p class='title themeBlackColor fontSize18 fontWeight text-limit-1'>
                                  <svg-icon
                                    v-if='false'
                                    class-name='title-icon cursor flex_shrink'
                                    icon-class='jinghua'
                                  ></svg-icon>
                                  <span>{{ item.ocs_course.name }}</span>
                                </p>
                                <p class='cloud-classroom-infomation fontSize12 '>
                                  <span v-if='item.ocs_course.speakerNames'
                                        class='author'>{{ item.ocs_course.speakerNames }}</span>
                                  <span class='play-num'>{{ item.ocs_course.showViews }}次播放</span>
                                  <span class='score'>{{ item.ocs_course.score }}分</span>
                                  <span
                                    class='price'>{{ item.ocs_course.money !== '' && item.ocs_course.money ? item.ocs_course.money + '¥' : '免费'
                                    }}</span>
                                </p>
                                <div class='label-list-box flex_start flex_warp text-limit-1'
                                     style='margin-bottom: 9px'>
                                  <div v-for='label in item.ocs_course.subspecialties' :key='label.id'
                                       class='label-box label'>
                                    {{ label.name }}
                                  </div>
                                  <div v-if='false' class='label-product-box label'>
                                    <svg-icon
                                      className='productIcon'
                                      iconClass='product2'
                                    ></svg-icon>
                                    产品标签
                                  </div>
                                </div>
                                <CourseItem :data-list='item'></CourseItem>
                              </div>
                            </div>
                            <!--"meeting","会议"-->
                            <div v-if='item.type === "meeting"'
                                 class='information-list-box meeting-component flex_start'
                                 @click.stop='meedingDetail(item.meeting.id)'>
                              <div class='left_image themeBorderRadius overflow_hidden flex_shrink'>
                                <img
                                  v-if='item.meeting.appMainPic || item.meeting.playerCover || item.meeting.titlePic'
                                  :src='item.meeting.appMainPic?$tool.compressImg(item.meeting.appMainPic,220,124):item.meeting.playerCover?$tool.compressImg(item.meeting.playerCover,220,124):item.meeting.titlePic?$tool.compressImg(item.meeting.titlePic,220,124):null'
                                  alt=''
                                  class='img_cover'>
                                <img v-else alt='' class='img_cover' src='~assets/images/default16.png' />
                                <div class='type meeting-component-type themeBorderRadius'>
                                  会议
                                </div>
                              </div>
                              <div class='right_info'>
                                <p class='title themeBlackColor fontSize18 fontWeight text-limit-2'>
                                  <svg-icon
                                    v-if='false'
                                    class-name='title-icon cursor flex_shrink'
                                    icon-class='jinghua'
                                  ></svg-icon>
                                  <span>{{ item.meeting.meetingName }}</span>
                                </p>
                                <p class='user-time fontSize12 flex_start flex_align_center'>
                                  <span class='time'>{{ item.meeting.meetingDateStr }}</span>
                                  <span v-if='true' class='user'>
                                      <svg-icon
                                        v-if='false'
                                        class-name='user-icon cursor'
                                        icon-class='yuanchan'
                                      ></svg-icon>
                                      <span>
                                        {{ item.meeting.province ? item.meeting.province + '-' : null
                                        }}{{ item.meeting.city }}
                                      </span>
                                    </span>
                                </p>
                                <p class='desc text-limit-2 fontSize12'>
                                  {{ type === 'usercenter' ? item.meeting.description : item.meeting.searchDescription
                                  }}
                                </p>
                                <MoreCorrelation :data-list='item'></MoreCorrelation>
                              </div>
                            </div>
                          </li>
                        </ul>
                      </div>
                    </transition>
                  </div>
                  <!--栏目内容  End-->
                </div>
              </el-col>
            </el-row>
            <Empty :loading='loading' :no-more='isEmptyStatus' />
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='$store.state.articleColumnclassify_count'
              :pager-count='$store.state.pager_count'
              :total='articleTotal'
              background
              small
              style='text-align: center; margin-bottom: 10px'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </el-col>
        <el-col :lg='8' :md='8' :sm='6' :xl='8' :xs='24'>
          <div v-if='relatedArticles && relatedArticles.length>0' class='article-details-con-right'>
            <!--Related Start-->
            <div class='Related_box'>
              <div class='title'>推荐专栏</div>
              <ul v-for='item in relatedArticles' :key='item.id'
                  class='Related-item'>
                <li @click='recommendItem(item.id,item.webApiLinkUrl)'>
                  <div class='left'>
                    <img
                      v-if="type == 'O' && item.smallImage"
                      :src='$tool.compressImg(item.smallImage,142,80)'
                      alt=''
                      class='img_cover'
                    />
                    <img
                      v-if="type == 'N' && item.image"
                      :src='$tool.compressImg(item.image,142,80)'
                      alt=''
                      class='img_cover'
                    />
                    <img v-else class='img_cover' src='~assets/images/default16.png' />
                  </div>
                  <div class='right flex_column flex_column_justify'>
                    <p class='item-title text-limit-2' style='color: #333333'>
                      {{ item.title }}
                    </p>
                    <p class='con'>
                      {{ (item.updateTime) ? (timeStamp.timestamp_13(((item.updateTime)), 'y-m-d')) : '' }}
                    </p>
                  </div>
                </li>
              </ul>
            </div>
            <!--End-->
          </div>
        </el-col>
      </el-row>
    </div>
    <client-only>
      <ColumnShare :share-data='titleData' :share-flag='dialogVisible' @editFlag='shareOpenFun'></ColumnShare>
    </client-only>
  </div>
</template>

<script>
import {
  cancelCollectSpecial,
  cancelSubscribeSpecial,
  getSpecialProduces,
  getUserSpecial,
  recommendSelfSpecial,
  recommendSpecial,
  selectChildSpecials,
  specialDetail,
  specialInfos,
  subCollectSpecial,
  subscribeSpecial
} from '@/api/article'
import InfoItem from '@/components/DataList/InfoItem/InfoItem'
import CaseItem from '@/components/DataList/CaseItem/CaseItem'
import CompileItem from '@/components/DataList/CompileItem/CompileItem'
import ColumnShare from '@/components/ArticleColumn/ColumnShare/index'
import MoreCorrelation from '@/components/ArticleColumn/MoreCorrelation'
import CourseItem from '@/components/ArticleColumn/CourseItem'
import Empty from '@/components/UI/Empty/Empty'

export default {
  name: 'detail_id',
  head() {
    return {
      title: this.titleData.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$route.query.id == 'O' ? this.titleData.metaDescription : this.titleData.description
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.titleData.title
        }
      ]
    }
  },
  components: {
    InfoItem,
    CaseItem,
    ColumnShare,
    CompileItem,
    MoreCorrelation,
    CourseItem,
    Empty
  },
  async asyncData({ app, params, error, store, query, req, $axios }) {
    /**
     * request1 新官网获取专栏详情
     * request2 文章专栏详情 一级分类
     * request3 新官网根据专栏编号分页获取关联文章列表
     * request4 文章专栏 推荐专栏
     *
     * request5 新官网获取单个自建专栏的信息
     * request6 新官网获取自建专栏内容列表
     * request7 新官网自建专栏推荐列表
     */
    if (query.type == 'O') {
      const [request1, request2, request3, request4] = await Promise.all([
        app.$axios.$request(
          specialDetail({
            specialId: query.id,
            userId: store.state.auth.user.id
          })
        ),
        app.$axios.$request(
          selectChildSpecials({
            specialId: query.id
          })
        ),
        app.$axios.$request(
          specialInfos({
            specialId: query.id,
            pageNo: 1,
            pageSize: store.state.articleColumnclassify_count
          })
        ),
        app.$axios.$request(
          recommendSpecial({
            specialId: query.id,
            siteType: 'T',
            limit: '5'
          })
        )
      ])
      if (request2.list.length > 0) {
        let all = { title: '全部', id: 'all' }
        request2.list.unshift(all)
      }
      app.head.title = request1.result.title
      return {
        articleTotal: request3.page.totalCount,
        detailMore: false,//文章专栏详情 展开更多开关
        titleData: request1.result, //专栏取request1 自建专栏取request5
        tabsList1: request2.list, //一级导航栏数据
        compileData: request3.list,//专栏列表
        relatedArticles: request4.list,  //相关推荐
        specialData: []  //专栏列表
      }
    } else {
      const [request5, request6, request7] = await Promise.all([
        app.$axios.$request(
          getUserSpecial({
            specialId: query.id,
            userId: store.state.auth.user.id
          })
        ),
        app.$axios.$request(
          getSpecialProduces({
            specialId: query.id,
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: store.state.articleColumnclassify_count
          })
        ),
        app.$axios.$request(
          recommendSelfSpecial({
            specialId: query.id,
            limit: 5
          })
        )
      ])
      app.head.title = request5.result.title
      return {
        articleTotal: request6.page.totalCount,
        detailMore: false,//文章专栏详情 展开更多开关
        titleData: request5.result, //专栏取request1 自建专栏取request5
        tabsList1: [], //一级导航栏数据
        compileData: [],//专栏列表
        relatedArticles: request7.list, //相关推荐
        specialData: request6.list //专栏列表
      }
    }
  },
  data() {
    return {
      loading: null,
      isEmptyStatus: false,// 加载开关
      dialogVisible: false,  //分享弹窗开关
      currentPage: 1, //分页
      chooseId: this.$route.query.id,  //选中导航id
      type: 'O' // N自建专栏
    }
  },
  methods: {
    meedingDetail(id) {
      const { href } = this.$router.resolve({
        path: `/meeting/detail`,
        query: {
          id: id
        }
      })
      window.open(href, '_blank')
    },
    // 跳转文章详情
    jumpDetailsPageFun(id) {
      const { href } = this.$router.resolve({
        name: 'index-info-detail',
        query: {
          id: id
        }
      })
      window.open(href, '_blank')
    },
    // 跳转云课堂
    jumpCurriculumFun(id) {
      this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: id } })
    },
    //推荐专栏跳转
    recommendItem(id, url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        const { href } = this.$router.resolve({
          name: 'index-column-detail-id',
          query: { id: id, type: this.$route.query.type == 'O' ? 'O' : 'N' }
        })
        window.open(href, '_blank')
      }
    },
    /**
     * 子专栏 切换专栏
     */
    selectColumnDataFun(id, navTwoId) {
      // id 二级分类id   navTwoId三级分类id
      this.$store.commit('articleDetailColumnListState', id)
      if (navTwoId) {
        this.$store.commit('articleDetailColumnreclassifyState', navTwoId)
      }
      this.chooseId = navTwoId ? navTwoId : id
      this.getclassify_fn()
    },
    handleCurrentChange(item) {
      this.$tool.scrollIntoView()
      this.currentPage = item ? item : 1
      this.getclassify_fn()
    },
    getclassify_fn() {
      if (this.type === 'O') {
        if (!this.loading) {
          this.loading = true
          this.isEmptyStatus = false
          this.$axios.$request(specialInfos({
            specialId: this.chooseId == 'all' ? this.$route.query.id : this.chooseId,
            pageNo: this.currentPage,
            pageSize: this.$store.state.articleColumnclassify_count
          })).then(res => {
            if (res.code === 1) {
              this.compileData = res.list
              this.articleTotal = res.page.totalCount
              res.list.length > 0 ? this.isEmptyStatus = false : this.isEmptyStatus = true
              this.loading = false
            }
          })
        }
      } else {
        this.$axios.$request(getSpecialProduces({
          specialId: this.chooseId == 'all' ? this.$route.query.id : this.chooseId,
          pageNo: this.currentPage,
          userId: this.$store.state.auth.user.id,
          pageSize: this.$store.state.articleColumnclassify_count
        })).then(res => {
          if (res.code === 1) {
            this.specialData = res.list
            this.articleTotal = res.page.totalCount
          }
        })
      }
    },
    //点击是否关注
    async subscribeClick(id) {
      // 未登录
      if (!this.$cookies.get('medtion_token_only_sign')) {
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
      } else {
        //自建和非自建 返回是否关注字段没有统一  都判断
        if (this.titleData.isSubscribe) {
          //已关注  取消关注
          if (this.type == 'O') {
            await this.$axios.$request(cancelSubscribeSpecial({
              specialId: id
            })).then((res) => {
              if (res.code == 1) {
                this.titleData.isSubscribe = res.result.isSubscribe

                this.$analysys.add_follow(
                  String(this.$store.state.auth.user.id),
                  String(id),
                  String(this.$store.state.auth.user.realName),
                  String(this.titleData.title),
                  '取消',
                  '文章专栏'
                )
              }
            })
          } else if (this.type == 'N') {
            await this.$axios.$request(cancelCollectSpecial({
              specialId: id
            })).then((res) => {
              if (res.code == 1) {
                //获取单个自建详情
                this.$axios.$request(
                  getUserSpecial({
                    specialId: this.$route.query.id,
                    userId: this.$store.state.auth.user.id
                  })).then((res) => {
                  if (res.code == 1) {
                    this.titleData.isSubscribe = res.result.isSubscribe

                    this.$analysys.add_follow(
                      String(this.$store.state.auth.user.id),
                      String(id),
                      String(this.$store.state.auth.user.realName),
                      String(this.titleData.title),
                      '取消',
                      '文章专栏'
                    )

                  }
                })
              }
            })
          }
        } else {
          //关注
          if (this.type == 'O') {
            await this.$axios.$request(subscribeSpecial({
              specialId: id
            })).then((res) => {
              if (res.code == 1) {
                this.titleData.isSubscribe = res.result.isSubscribe

                this.$analysys.add_follow(
                  String(this.$store.state.auth.user.id),
                  String(id),
                  String(this.$store.state.auth.user.realName),
                  String(this.titleData.title),
                  '关注',
                  '文章专栏'
                )
              }
            })
          } else if (this.type == 'N') {
            await this.$axios.$request(subCollectSpecial({
              specialId: id
            })).then((res) => {
              if (res.code == 1) {
                //获取单个自建详情
                this.$axios.$request(
                  getUserSpecial({
                    specialId: this.$route.query.id,
                    userId: this.$store.state.auth.user.id
                  })).then((res) => {
                  if (res.code == 1) {
                    this.titleData.isSubscribe = res.result.isSubscribe

                    this.$analysys.add_follow(
                      String(this.$store.state.auth.user.id),
                      String(id),
                      String(this.$store.state.auth.user.realName),
                      String(this.titleData.title),
                      '关注',
                      '文章专栏'
                    )

                  }
                })
              }
            })
          }
        }
      }
    },
    //分享
    shareClick() {
      this.dialogVisible = true
    },
    convertHours(n) {
      if (n > 9999) {
        return (n / 10000).toFixed(1) + '万'
      }
      return n
    },
    /**
     * 分享按钮弹框打开
     */
    shareOpenFun(data) {
      this.$analysys.btn_click('分享', document.title)
      this.dialogVisible = data
    }
  },
  mounted() {
    this.type = this.$route.query.type

    this.$analysys.Browse_special({
      specialtitle: String(this.titleData.title),
      userid: String(this.$store.state.auth.user.id),
      specialid: String(this.chooseId === 'all' ? this.$route.query.id : this.chooseId),
      type: this.type === 'O' ? 'PGC专栏' : '自建专栏'
    })
  },
  computed: {}
}
</script>

<style lang='less' scoped>
@import "~@/pages/index/column/detail/index.less";
</style>
