.column_detail_container {
  .bg_wrapper {
    width: 100%;
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    background-size: cover;
    background-repeat: no-repeat;

    .bg_wrapper_fixed {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      //filter: blur(25px);
      backdrop-filter: blur(25px);
      display: flex;
      align-items: center;
    }

    .column_detail_head {

      border-radius: 8px;
      background: #FFF;
      padding: 32px;
      display: flex;
      align-items: stretch;

      .image {
        width: 250px;
        height: 140.625px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        margin-right: 24px;
      }

      .detail_content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          color: #333;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          padding-bottom: 22px;
        }

        .detail_info {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .tips {
            color: #999EA4;
            font-size: 12px;
          }

          .button_wrapper {
            display: flex;
            gap: 24px;
            flex-shrink: 0;
            align-items: center;
          }
        }
      }
    }
  }

  .column_tabs {
    position: sticky;
    top: 60px;
    background: white;
    z-index: 10;
  }

  .tow_tabs_container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px 45px;
    border-bottom: 0.5px solid #E6E6E6;
    padding: 12px 22px 12px;
    margin-bottom: 24px;
    margin-top: 12px;

    .tow_tab_item {
      color: #676C74;
      font-size: 16px;
      height: 20px;
      line-height: 20px;
      cursor: pointer;

      &:hover {
        color: #333;
      }
    }

    .active_tow_tab_item {
      color: #333;
      font-weight: 600;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: -14px;
        width: 16px;
        height: 4px;
        border-radius: 2px;
        background: #0581CE;
      }
    }
  }

  .three_tabs_container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    padding-bottom: 12px;
    margin-bottom: 12px;

    .three_tab_item {
      padding: 8px 16px;
      border-radius: 4px;
      background: #F4F6F8;
      font-size: 14px;
      color: #333;
      line-height: 16px;
      cursor: pointer;

      &:hover {
        background: #EFFAFF;
      }
    }

    .active_three_tab_item {
      outline: 1px solid #0581CE;
      background: #EFFAFF;
      color: #0581CE;
    }
  }

  /deep/ .list_wrapper, /deep/ .el-skeleton {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 24px 32px;
  }
}
