.article-details-con{
  margin-top: 20px;
  .article-details-con-left{
    padding-right: 20px;
    .article-title-box{
      padding: 0 3px;
      margin: 20px 0 16px;
      font-weight: 540;
      .article-title-box-top{
        .consulting-item{
          background: #FBFBFB;
        }
      }
      .search_content {
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
        margin-bottom: 10px;
        line-height: 54px;
        margin-right: 40px;
        color: #666666;
        white-space:nowrap;
      }
    }
  }
  .column-content-div{
    transition: all 0.3s;
    ul>li{
      margin-bottom: 20px;
      padding: 10px;
      border-radius: 6px;
    }
  }
  .reclassify{
    transition: all 0.3s;
    margin-bottom: 6px!important;
    li{
      padding: 8px 16px;
      border: 1px solid #F4F4F4;
      background: #FBFBFB;
      font-size: 16px;
      line-height: 16px;
      color: #333333;
      border-radius: 100px;
      margin-right: 32px;
      margin-bottom: 15px;
    }
  }
  .article-details-con-right{
    margin-top: 20px;
  }
  .consulting-item {
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 30px;
    transition: all 0.3s;
    display: flex;
    justify-content: flex-start;
    padding: 10px 0 10px 10px;
    box-sizing: border-box;
    background: #ffffff;

    .top-title-left {
      margin-right: 10px;
      min-width: 228px;
      max-width: 228px;
      text-align: center;
      min-height: 100%;
      border-radius: 6px;
      height: 128px;
      position: relative;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        height: auto;
        vertical-align: middle;
        margin: 0 auto;
        transition: all .3s ease-in 0s;
      }

      .position_top {
        position: absolute;
        top: 8px;
        right: 8px;
        border-radius: 4px;
        padding: 1px 6px;
        color: #ffffff;
        line-height: 16px;
        background: #0581CE;

        span {
          display: block;
          font-size: 12px;
          line-height: 14px;
          transform: scale(0.8);
        }
      }
      .position_top_jp{
        font-size: 10px;
        position: absolute;
        top: 4px;
        right: 4px;
        border-radius: 4px;
        padding: 1px 6px;
        color: #ffffff;
        line-height: 16px;
        background: #0581CE;
      }
    }
    .item-left {
      margin-right: 10px;
      min-width: 220px;
      max-width: 220px;
      text-align: center;
      min-height: 100%;
      border-radius: 6px;
      height: 124px;
      position: relative;
      overflow: hidden;

      img {
        display: block;
        width: 100%;
        height: auto;
        vertical-align: middle;
        margin: 0 auto;
        transition: all .3s ease-in 0s;
      }

      .position_top {
        position: absolute;
        top: 8px;
        right: 8px;
        border-radius: 4px;
        padding: 1px 6px;
        color: #ffffff;
        line-height: 16px;
        background: #0581CE;

        span {
          display: block;
          font-size: 12px;
          line-height: 14px;
          transform: scale(0.8);
        }
      }
      .position_top_jp{
        font-size: 10px;
        position: absolute;
        top: 4px;
        right: 4px;
        border-radius: 4px;
        padding: 1px 6px;
        color: #ffffff;
        line-height: 16px;
        background: #0581CE;
      }
    }

    .item-right {
      padding-right: 20px;
      width: 100%;
      justify-content: space-around;
      .pone {
        font-size: 18px;
        line-height: 22px;

        .poneIcon {
          min-width: 16px;
          min-height: 16px;
        }
      }

      .ptwo {
        display: block;
        font-size: 12px;
        line-height: 18px;
        color: #999999;
      }

      .pthree {
        font-size: 12px;
        color: #999999;
        line-height: 12px;
        margin: 15px 0 16px;

        .pthreeIcon {
          width: 12px;
          height: 12px;
        }
        .authorList {
          margin-right: 10px;
        }

        i {
          margin-right: 2px;
        }
      }
      .lable-box {
        display: flex;
        justify-items: flex-start;
        flex-wrap: wrap;
        margin-top: 10px;

        .lable-item {
          border-radius: 6px;
          background: #F0F9FF;
          font-size: 12px;
          color: #0A83CE;
          line-height: 22px;
          padding: 0 11px;
          margin: 0 10px 10px 0;
        }
      }
      .text-bottom-right{
        .SubscribeActive{
          border: 1px solid #E6E6E6!important;
          color: #999999!important;
          background: #FBFBFB!important;
        }
        span{
          border-radius: 100px;
          font-size: 14px;
          svg,img{
            width: 10px;
            margin-right: 4px;
          }
        }

        .attention-btn{
          background: #0581CE;
          color: #ffffff;
          padding: 6px 16px;
          margin-right: 14px;
        }

        .share_button{
          padding: 6px 12px;
          border: 1px solid #0581CE;
          color: #0581CE;
        }
      }
    }
  }
}
.column-content:hover {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.15);
}
.Related_box {
  cursor: pointer;
  border-radius: 6px;
  background: #FBFBFB;
  padding: 17px 14px 0;
  overflow: hidden;

  .title {
    font-size: 18px;
    line-height: 20px;
    position: relative;
    padding-left: 14px;
    margin-bottom: 19px;
  }

  .title::before {
    content: "";
    display: block;
    width: 3px;
    height: 15px;
    background: #0581ceff;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -7.5px;
  }

  .Related-item {
    overflow: hidden;

    li {
      display: flex;
      justify-items: flex-start;
      margin-bottom: 16px;

      .left {
        min-width: 142px;
        max-width: 142px;
        height: 80px;
        margin-right: 10px;
        overflow: hidden;
        border-radius: 6px;
      }

      .right {
        .item-title {
          font-size: 14px;
          color: #000000ff;
          margin-bottom: 9px;
          line-height: 24px;
        }

        .con {
          color: #708aa2ff;
          font-size: 12px;
          line-height: 24px;
        }
      }
    }
  }
}
/* 隐藏滚动条 */
.select-wrap {
  /* 隐藏滚动条 */
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.select-wrap::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

/deep/.el-dialog__body{
  display: flex;
  align-items: center;
  flex-direction: column;
}
/deep/.el-dialog__header{
  display: none;
}
/deep/.el-dialog{
  border-radius: 6px!important;
}
/* 云课堂样式 */
.information-list-box {
  .left_image {
    width: 220px;
    height: 124px;
    margin-right: 10px;
    position: relative;

    .type {
      position: absolute;
      right: 4px;
      top: 4px;
      padding: 1px 8px;
      color: #FFFFFF;
      font-size: 12px;
    }

    .information-component-type {
      background: #0555CE;
    }

    .meeting-component-type {
      background: #0581CE;
    }

    .case-component-type {
      background: #0CA92E;
    }

    .cloud-classroom-component-type {
      background: #EE9222;
    }

    .compile-component-type {
      background: #AF2EFF;
    }
  }

  .right_info {
    width: calc(100% - 230px);

    .label-list-box {
      .label {
        display: inline-block;
        margin-right: 10px;
      }

      .label-box {
        border-radius: 11px;
        background: #E6F2FA;
        padding: 2px 8px;
        font-size: 14px;
        color: #0A83CE;
      }

      .label-product-box {
        display: inline-block;
        border-radius: 11px;
        background: #F6F6F6;
        padding: 2px 8px;
        font-size: 14px;
        color: #0CA92E;
      }
    }

    .title {
      line-height: 20px;
      margin-bottom: 15px;

      span {
        display: inline;
        vertical-align: middle
      }

      .title-icon {
        display: inline;
        width: 16px;
        height: 16px;
        vertical-align: middle;
      }
    }

    .user-time {
      color: #708AA2;
      line-height: 17px;
      margin-bottom: 15px;

      .time {
        line-height: 18px;
        margin-right: 10px;
      }

      .user {
        span {
          vertical-align: middle;
        }

        .user-icon {
          width: 10px;
          height: 10px;
          vertical-align: middle;
        }
      }
    }

    .desc {
      color: #708AA2;
      line-height: 15px;
    }

    .case-details {
      .details {
        span {
          margin-right: 10px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}

.cloud-classroom-component {
  .right_info {
    .label-list-box {
      margin-bottom: 15px;

      .label {
        margin-right: 10px;

        &:last-child {
          margin-right: 0;
        }
      }

      .label-box {
        border-radius: 11px;
        background: #E6F2FA;
        padding: 2px 8px;
        font-size: 14px;
        color: #0A83CE;
      }

      .label-product-box {
        border-radius: 11px;
        background: #F6F6F6;
        padding: 2px 8px;
        font-size: 14px;
        color: #0CA92E;
      }
    }

    .title {
      margin-bottom: 10px !important;
    }

    .cloud-classroom-infomation {
      color: #708AA2;
      line-height: 12px;
      margin-bottom: 12px;

      span {
        margin-right: 15px;
      }

      .price {
        color: #DD5A42;
        font-size: 14px;
      }
    }
  }
}
/* 云课堂样式 End */
.tab-box_two {
  flex-wrap: wrap;
  border-radius: 6px;
  background: #fbfbfbff;
  display: flex;
  //justify-content: space-between;
  line-height: 54px;
  padding: 0 27px;
  margin-bottom: 20px;

  .tab-box_two_li {
    cursor: pointer;
    color: #888888;
    position: relative;
    margin-right: 20px;
    >span{
      font-size: 16px;
    }
    &:hover {
      color: #202020;
    }

    .mixin_desktop{
      font-size: 12px;
    }

    .tab_icon {
      width: 8px;
      margin-left: 4px;
    }

    .nav_two_select_box {
      display: none;
      z-index: 10;
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      top: 80%;
      width: 60px;
      box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.15);
      border-radius: 4px 4px 4px 4px;
      background: #fff;

      .nav_two_select_list {
        line-height: 25px;
        text-align: center;
        color: #888888;
        font-weight: 400;
      }

      .is_active {
        background: #dbf1ff;
        color: #0581ce !important;
      }
    }

    .three_case {
      display: none;
      z-index: 10;
      position: absolute;
      left: calc(-27px);
      top: 100%;
      padding-top: 10px;
    }

    .nav_three_select_box {
      width: 322px;
      overflow: hidden;
      box-sizing: border-box;
      box-shadow: 0px 4px 4px 1px rgba(0, 0, 0, 0.15);
      border-radius: 6px;
      background: #fff;
      padding: 16px 16px 0;

      .nav_two_select_list {
        line-height: 18px;
        color: #708AA2;
        font-weight: 400;
        float: left;
        margin-right: 20px;
        margin-bottom: 18px;

        &:hover {
          color: #333333;
        }
      }

      .is_active {
        color: #0581ce!important;
        font-weight: 500;
      }
    }

    &:hover .nav_two_select_box {
      display: block;
    }

    &:hover .three_case {
      display: block;
    }
  }

  .active {
    color: #0581ce !important;
    font-weight: bold;
  }
}
