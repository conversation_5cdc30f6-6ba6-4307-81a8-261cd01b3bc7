<template>
  <div class="column_detail_container">
    <div
      class="bg_wrapper"
      :style="{background:`url(${DetailData.smallImageUrl})`, backgroundSize: 'cover',backgroundRepeat:'no-repeat'}">
      <div class="bg_wrapper_fixed"/>
      <Container :styles="{position:'relative',padding:'0 36px',boxSizing:'border-box'}">
        <div class="column_detail_head">
          <div class="image">
            <zip-img
              :width="250"
              :height="140"
              :src="DetailData.smallImageUrl"
              fill
            />
          </div>
          <div class="detail_content">
            <p class="title">
              {{ DetailData.title }}
            </p>
            <div class="detail_info">
              <div class="tips">
                {{ $tool.formatterNum(DetailData.views || DetailData.showViews) }}阅读
                {{ $tool.formatterNum(DetailData.refers) }}内容
              </div>
              <div class="button_wrapper">
                <SubscribeDefault
                  :is-follow="DetailData.isSubscribe"
                  @follow="followHandler"
                />
                <ShareDefaultBtn/>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>

    <Container :styles="{padding:'0 36px 32px',boxSizing:'border-box'}">
      <div v-if="TabList.length>0" class="column_tabs">
        <div class="tow_tabs_container">
          <div
            v-for="(item,index) in TabList"
            :key="item.id"
            class="tow_tab_item"
            :class="twoSpecial === index ? 'active_tow_tab_item' : '' "
            @click="changeTabHandler(item.id,index)"
          >
            {{ item.title }}
          </div>
        </div>

        <div v-if="threeTabList && threeTabList.length>0" class="three_tabs_container">
          <div
            v-for="item in threeTabList"
            :key="item.id"
            class="three_tab_item"
            :class="specialId === item.id ? 'active_three_tab_item' : '' "
            @click="changeTabHandler(item.id,true)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>

      <RollingLoad
        :loading="loading"
        :empty="columnData.list.length===0"
        :no-more="currentPageNo >= columnData.page.totalPage"
        empty-height="50vh"
        @hit-bottom="hitBottomChangeHandler"
      >
        <template #loading>
          <InfoSkeleton :limit="16" :loading="loading"/>
        </template>

        <template>
          <div class="list_wrapper" :style="{marginTop:'24px'}">
            <template v-if="type === 'O'">
              <!--type O 精品专栏-->
              <InfoDefaultItem
                v-for="item in columnData.list"
                :key="item.id"
                :info-id="item.id"
                :image="item.smallImageUrl"
                :title="item.title"
                :essences="item.essences"
                :author-names="authorNames(item.authorList)"
                :publish-date="item.publishDate"
                :product-list="item.productList"
                :bms-auth="item.bmsAuth"
              />
            </template>
            <template v-else>
              <template v-for="item in columnData.list">

                <CaseDefaultItem
                  v-if="item.type === 'mp_article'"
                  :key="item.mp_article.id"
                  :info-id="item.mp_article.id"
                  :image="item.mp_article.cover"
                  :title="item.mp_article.title"
                  :essences="item.mp_article.essences"
                  :author-image="item.mp_article.creator.avatarAddress"
                  :author-name="item.mp_article.creator.realName"
                  :publish-date="timeStamp.timestampFormat(item.mp_article.publishTime / 1000)"
                  :product-list="item.mp_article.productList"
                  :bms-auth="item.mp_article.bmsAuth"
                />
                <InfoDefaultItem
                  v-else-if="item.type === 'info'"
                  :key="item.info.infoId"
                  :info-id="item.info.infoId"
                  :image="item.info.infoImg"
                  :title="item.info.infoTitle"
                  :essences="item.info.essences"
                  :author-names="authorNames(item.info.authorList)"
                  :publish-date="timeStamp.timestampFormat(item.info.publishDate / 1000)"
                  :product-list="item.info.productList"
                  :bms-auth="item.info.bmsAuth"
                />

                <MeetingDefaultItem
                  v-else-if="item.type === 'meeting'"
                  :key="item.meeting.id"
                  :meeting-id="item.meeting.id"
                  :image="item.meeting.titlePic"
                  :title="item.meeting.meetingName"
                  :publish-date="item.meeting.meetingDateStr"
                />
                <ClassDefaultItem
                  v-else-if="item.type === 'ocs_course'"
                  :key="item.ocs_course.id"
                  :class-id="item.ocs_course.id"
                  :image="item.ocs_course.cover"
                  :title="item.ocs_course.name"
                  :show-views="item.ocs_course.showViews"
                />
              </template>
            </template>

          </div>
        </template>

      </RollingLoad>

    </Container>

  </div>
</template>

<script>
import RollingLoad from "../../../../opt-components/component/RollingLoad/index.vue";
import {Container} from "../../../../opt-components/template";
import {InfoSkeleton} from "../../../../opt-components/ui/skeleton";
import {
  InfoDefaultItem,
  CaseDefaultItem,
  MeetingDefaultItem,
  ClassDefaultItem
} from "../../../../opt-components/data-list";
import {
  cancelCollectSpecial,
  cancelSubscribeSpecial, getSpecialProduces, getUserSpecial,
  selectChildSpecials,
  specialDetail,
  specialInfos, subCollectSpecial,
  subscribeSpecial
} from "../../../../api/article";
import ZipImg from "../../../../opt-components/component/ZipImg/index.vue";
import {SubscribeDefault} from "../../../../opt-components/component/Subscribe";
import {ShareDefaultBtn} from "../../../../opt-components/component/ShareBtn";

export default {
  /**
   * type O 是精品专栏  有分离tab 只有资讯列表
   * type N 是自建专栏  没有分类tab 有各种列表
   */
  name: "ColumnDetailPage",
  components: {
    ZipImg,
    InfoSkeleton,
    Container,
    RollingLoad,
    InfoDefaultItem,
    SubscribeDefault,
    ShareDefaultBtn,
    CaseDefaultItem,
    MeetingDefaultItem,
    ClassDefaultItem
  },
  async asyncData({app, params, error, store, query, req, $axios,redirect}) {
    const type = params.type === "pgc" ? "O" : "N"
    if(query.specialId){
      redirect(`/column/detail/${params.type}?id=${query.specialId}`)
    }
    if (type === "O") {
      const [DetailData, TabList] = await Promise.all([
        app.$axios.$request(
          specialDetail({
            specialId: query.id,
            userId: store.state.auth.user.id
          }),
        ),
        app.$axios.$request(
          selectChildSpecials({
            specialId: query.id
          })
        ),
      ])

      if (TabList.list.length > 0) {
        TabList.list.unshift({
          id: query.id,
          title: "全部"
        })
      }

      return {
        type,
        DetailData: DetailData.result,
        TabList: TabList.list
      }
    } else if (type === "N") {
      const [DetailData] = await Promise.all([
        app.$axios.$request(
          getUserSpecial({
            specialId: query.id,
            userId: store.state.auth.user.id
          })
        )
      ])
      return {
        type,
        DetailData: DetailData.result,
        TabList: []
      }
    }

  },
  data() {
    return {
      columnData: {
        list: [],
        page: {}
      },
      loading: true,
      currentPageNo: 1,
      specialId: this.$route.query.id,
      twoSpecial: 0,
      threeTabList: []
    }
  },
  head() {
    return {
      title: this.DetailData.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.type === 'O' ? this.DetailData.metaDescription : this.DetailData.description
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.DetailData.title
        }
      ]
    }
  },
  mounted() {
    if (this.type === "O") {
      // 精品专栏
      this.getQualityDataHandler({pageNo: 1, specialId: this.$route.query.id})
    } else {
      this.getSpecialProduces()
      // 自建专栏
    }
  },
  methods: {
    // 精品专栏
    async getQualityDataHandler({pageNo, pageUp, specialId} = {pageNo: 1}) {
      const data = await this.$axios.$request(
        specialInfos({
          specialId,
          pageNo,
          pageSize: 12
        })
      )

      if (data.code === 1) {

        if (pageUp) {
          this.$set(this.columnData, "list", [...this.columnData.list, ...data.list])
          this.$set(this.columnData, "page", data.page)
        } else {
          this.$set(this.columnData, "list", data.list)
          this.$set(this.columnData, "page", data.page)
        }

      }
      this.loading = false;

    },
    // 切换tab
    changeTabHandler(id, isThree) {
      this.specialId = id;
      this.currentPageNo = 1;
      this.loading = true;
      this.$set(this.columnData, "list", [])
      this.$set(this.columnData, "page", {})
      this.$tool.scrollIntoTop()

      if (this.TabList.length > 0 && typeof isThree === 'number') {
        this.twoSpecial = isThree
        this.threeTabList = this.TabList.filter(item => item.id === this.specialId)?.[0]?.children
      }

      this.getQualityDataHandler({
        pageNo: this.currentPageNo,
        specialId: this.specialId
      })
    },

    // 自建专栏
    async getSpecialProduces({pageNo, pageUp} = {pageNo: 1}) {
      const data = await this.$axios.$request(getSpecialProduces({
          specialId: this.$route.query.id,
          userId: this.$store.state.auth.user.id,
          pageNo,
          pageSize: 12
        })
      )

      if (data.code === 1) {

        if (pageUp) {
          this.$set(this.columnData, "list", [...this.columnData.list, ...data.list])
          this.$set(this.columnData, "page", data.page)
        } else {
          this.$set(this.columnData, "list", data.list)
          this.$set(this.columnData, "page", data.page)
        }

      }
      this.loading = false;

    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.loading = true;
        this.currentPageNo += 1;

        if (this.type === "O") {
          this.getQualityDataHandler({
            pageNo: this.currentPageNo,
            pageUp: true,
            specialId: this.specialId
          })
        }else{
          this.getSpecialProduces({
            pageNo: this.currentPageNo,
            pageUp: true,
          })
        }
      }
    },
    authorNames(list) {
      let names = ""
      if (list.length > 1) {
        names = list[0].authorName + "等3位作者 "
      } else if (list.length === 1) {
        names = list[0].authorName
      } else {
        names = ""
      }

      return names
    },


    // 关注
    followHandler() {
      if (this.type === 'O') {
        if (this.DetailData.isSubscribe) {
          this.$set(this.DetailData, "isSubscribe", false)
          this.$axios.$request(cancelSubscribeSpecial({
            specialId: this.DetailData.id
          }))
        } else {
          this.$set(this.DetailData, "isSubscribe", true)
          this.$axios.$request(subscribeSpecial({
            specialId: this.DetailData.id
          }))
        }
      } else if (this.type === 'N') {
        if (this.DetailData.isSubscribe) {
          this.$set(this.DetailData, "isSubscribe", false)
          this.$axios.$request(cancelCollectSpecial({
            specialId: this.DetailData.id
          }))
        } else {
          this.$set(this.DetailData, "isSubscribe", true)
          this.$axios.$request(subCollectSpecial({
            specialId: this.DetailData.id
          }))
        }
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
