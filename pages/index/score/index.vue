<template>
  <div class="big-box">
    <div class="nav_list">
      <div @click="isOpenedShow" class="nav-right">
        <img class="score-icon" alt="" />
        <p>评分记录</p>
      </div>
      <img src="~/static/minitool/score_header.png" alt="" />
    </div>
    <!--    <div class="nav">-->
    <!--      <p class="nav-p">临床评分小工具</p>-->

    <!--      &lt;!&ndash; </nuxt-link> &ndash;&gt;-->
    <!--    </div>-->
    <!-- 评分 分类 -->
    <div class="classify-big">
      <!-- 通用工具 -->
      <div class="general-bool classify" style="width: 1128px;z-index: 2">
        <div class="general-bool-title title">
          <p style="color:#1879bf">通用工具</p>
        </div>
        <div class="general-bool-content content">
          <div class="gcs garde" v-for="(i, index) in generalBoolTitle" :key="index">
            <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
              name: `index-minitool-index-${generalName[index]}`,
              params: { id: index + 1, ind: 1, wordage: i },
            }" :ind="1">{{ i }}</nuxt-link>
          </div>
        </div>
      </div>

      <!-- 缺血 -->
      <div class="ischemia classify" style="width: 1128px">
        <div class="ischemia-title title" style="background:linear-gradient(90deg, #DCDBF2 -6.61%, #FFF 102.97%);">
          <p style="color:#404aab">缺血</p>
        </div>
        <div class="ischemia-content content">
          <template v-for="(i, index) in ischemia">
            <div class="ischemia" v-if="i !== '颅内血管动静脉畸形(AVM) Spetsler-Martin分级'" :key="index">
              <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
                name: `index-minitool-index-${ischemiaName[index]}`,
                params: { id: index + 6, ind: 2, wordage: i },
              }" :ind="2">{{ i }}</nuxt-link>
            </div>
          </template>
        </div>
      </div>

      <!-- 卒中再发风险评分 -->
      <div class="classify" style="width: 1128px;height: 140px">
        <div class="apoplexy-title title" style="background:linear-gradient(90deg, #D4F2E9 -6.61%, #FFF 102.97%);">
          <p style="color:#4d9236">卒中再发风险评分</p>
        </div>
        <div class="apoplexy-content content">
          <div class="ischemia-content content">
            <div v-for="(i, index) in apoplexy" :key="index" class="apoplexy">
              <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
                name: `index-minitool-index-${apoplexyName[index]}`,
                params: { id: index + 13, ind: 3, wordage: i },
              }" :ind="3">{{ i }}</nuxt-link>
            </div>
          </div>
        </div>
      </div>

      <div class="merge">
        <!-- 动脉瘤 -->
        <div class="classify" style="width: 1128px;padding-bottom: 1px;">
          <div class="apoplexy-title title" style="background:linear-gradient(90deg, #DBE9F4 -6.61%, #FFF 102.97%);">
            <p style="color:#1879bf">动脉瘤</p>
          </div>
          <div class="apoplexy-content content">
            <div v-for="(i, index) in artery" :key="index" class="aneurysm">
              <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
                name: `index-minitool-index-${arterialAneurysmName[index]}`,
                params: { id: index + 17, ind: 4, wordage: i },
              }" :ind="4">{{ i }}</nuxt-link>
            </div>
          </div>
        </div>

        <!-- 脑血管畸形 -->
        <div class="apoplexy classify" style="width: 1128px;height: 140px">
          <div class="apoplexy-title title" style="background:linear-gradient(90deg, #F6EBCC -6.61%, #FFF 102.97%);">
            <p style="color:#c68200">脑血管畸形</p>
          </div>
          <div class="apoplexy-content content">
            <div v-for="(i, index) in malformation" :key="index" class="cerebrovascular">
              <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
                name: `index-minitool-index-${cerebrovascularMalformation[index]}`,
                params: { id: index + 21, ind: 5, wordage: i },
              }" :ind="5">{{ i }}</nuxt-link>
            </div>
          </div>
        </div>

        <!-- 步态分析法 -->
        <div class="apoplexy classify" style="width: 1128px;height: 140px">
          <div class="apoplexy-title title" style="background:linear-gradient(90deg, #DCDBF2 -6.61%, #FFF 102.97%);">
            <p style="color:#4455b2">步态分析法</p>
          </div>
          <div class="apoplexy-content content">
            <div v-for="(i, index) in walkNametion" :key="index" class="gait">
              <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
                name: `index-minitool-index-${walkName[index]}`,
                params: { id: index + 25, ind: 8, wordage: i },
              }" :ind="8">{{ i }}</nuxt-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 帕金森 -->
      <div class="apoplexy classify" style="width: 1128px;height: 205px">
        <div class="apoplexy-title title" style="background:linear-gradient(90deg, #DBF1F2 -6.61%, #FFF 102.97%);">
          <p style="color:#4d9236">帕金森</p>
        </div>
        <div class="apoplexy-content content">
          <div v-for="(i, index) in parkinson" :key="index" class="parkin">
            <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
              name: `index-minitool-index-${parkinsonName[index]}`,
              params: { id: index + 25, ind: 9, wordage: i },
            }" :ind="9">{{ i }}</nuxt-link>
          </div>
        </div>
      </div>

      <!-- 其他 -->
      <div class="apoplexy classify" style="width: 1128px">
        <div class="apoplexy-title title" style="background:linear-gradient(90deg, #DBE9F4 -6.61%, #FFF 102.97%);">
          <p style="color:#1879bf">其他</p>
        </div>
        <div class="apoplexy-content content">
          <div v-for="(i, index) in other" :key="index" class="other">
            <nuxt-link @click.native="getInd($event)" class="aaa" :to="{
              name: `index-minitool-index-${otherName[index]}`,
              params: { id: index + 24, ind: 6, wordage: i },
            }" :ind="6">{{ i }}</nuxt-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'minitoolPage',
  head () {
    return {
      title: '临床评分小工具',
    }
  },
  data () {
    return {}
  },
  mounted () {

    // 删除本地存储的 scoringDetails 评分项数据
    // if (sessionStorage !== undefined) {
    //   localStorage.removeItem('scoringDetails')
    // }
    // 首次加载页面 判断登录状态
    if (this.$cookies.get('medtion_isLogged_only_sign') !== true) {
      this.$store.commit('minitool/setLoggingStatus', false)
    } else {
      this.$store.commit('minitool/setLoggingStatus', true)
    }
  },
  computed: {
    // 从 vuex 中解析出来的数据
    ...mapState('minitool', [
      'generalBoolTitle',
      'generalName',
      'ischemia',
      'apoplexy',
      'artery',
      'malformation',
      'walkNametion',
      'parkinson',
      'other',
      'ischemiaName',
      'apoplexyName',
      'arterialAneurysmName',
      'cerebrovascularMalformation',
      'walkName',
      'parkinsonName',
      'otherName',
    ]),
  },
  methods: {
    // 打开跳转之后的侧边栏
    getInd: function (e) {
      // localStorage.setItem('openeds', e.target.attributes.ind.value)
      // 首次进入评分页 侧边栏打开的序号
      this.$store.commit('minitool/setOpenedsHandler', [
        e.target.attributes.ind.value,
      ])
      console.log(e.target.attributes.ind.value)
      // 首次进入评分页 面包屑具体评分项的名称
      this.$store.commit('minitool/setWordLast', e.target.innerHTML)
      localStorage.setItem('wordLast', e.target.innerHTML)
      // 评分记录被选中的样式
      this.$store.commit('minitool/setIsOpenedShow', false)
    },
    // 评分记录页面导航条的隐藏
    isOpenedShow () {
      localStorage.setItem('className', '评分记录')
      this.$store.commit('minitool/setClassName', '评分记录')
      this.$store.commit('minitool/setIsOpenedShow', true)
      this.$store.commit('minitool/setOpenedsHandler', ['7'])
      // 判断是否登录

      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        // 登录状态
        // 将 vuex中的name title 置为空
        this.$store.commit('minitool/setName', '')
        this.$store.commit('minitool/setTitle', '')
        localStorage.setItem('name', '')
        localStorage.setItem('taxon', '全部')
        localStorage.setItem('currentPage', 1)
        // 发请求
        this.$store.dispatch('minitool/gradingRecords', {
          $axios: this.$axios.$request,
          userId: this.$cookies.get('medtion_user_only_sign').id,
          router: this.$router,
          page: 1,
          skip: true
        })
      } else {
        // 未登录状态 跳转到登录页面
        this.$router.push({
          name: 'signin',
          query: { fallbackUrl: this.$route.fullPath },
        })
      }
    },
  },

  beforeDestroy () {
    localStorage.removeItem('scoreAgain')
    localStorage.removeItem('associatedScore')
    localStorage.removeItem('conditionChange')
    localStorage.removeItem('submitData')
    localStorage.removeItem('submitDataSpecial')
    localStorage.removeItem('switchover')
    localStorage.removeItem('scoringDetails')
    localStorage.removeItem('illnessTotalScore')
    localStorage.removeItem('illnessListDetail')
    localStorage.removeItem('patient')
    localStorage.removeItem('associated')
  },
}
</script>

<style lang="less" scoped>
@import '~@/pages/index/score/index.less';
</style>
