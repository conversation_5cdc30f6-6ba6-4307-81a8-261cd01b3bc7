.nav {
  width: 1200px;
  height: 30px;
  margin: 30px auto;
  font-family: 'Microsoft YaHei';

  .nav-p {
    width: 1200px;
    font-weight: 700;
    font-size: 24px;
    // margin: 0 auto;
    text-align: center;
    line-height: 24px;
    color: #202020;
  }

  .nav-right {
    margin-top: -27px;
    float: right;
    display: flex;
    padding: 4px 10px;
    gap: 6px;
    width: 106px;
    height: 30px;
    background: rgba(5, 129, 206, 0.1);
    border-radius: 6px;
    box-sizing: border-box;
    cursor: pointer;
    overflow: hidden;

    .el-icon-s-order {
      color: #0581ce;
      width: 14.4px;
      height: 14.4px;
      margin-top: 3.8px;
    }

    // .white{
    //   position: relative;
    //   // top: 30px;
    // }

    p {
      font-family: 'PingFang SC' !important;
      font-weight: 400;
      font-size: 16px;
      color: #0581ce;
    }
  }
  .nav-right:hover {
    background-color: #0581ce;

    .el-icon-s-order {
      color: #ffffff;
    }

    p {
      color: #ffffff;
    }
  }
}

.classify-big {
  width: 1200px;
  margin: -30px auto 20px auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding-bottom: 10px;
  .merge {
    //height: 400px;
    //margin-top: 30px;
    //margin-bottom: 10px;

    .apoplexy {
      margin-top: 24px;
    }
  }

  .classify {
    //z-index: 2;
    //border: 1px solid rgba(200,200,200,0.5);
    box-sizing: border-box;
    background: #fbfbfb;
    //box-shadow: 0px 4px 4px 2px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 24px;
    color: #ffffff;
    // margin-bottom: 30px;
  }

  .classify:nth-of-type(4),
  .classify:nth-of-type(5),
  .classify:nth-of-type(6) {
    //margin-top: 100px;

  }

  //.classify:hover {
  //  background: #ffffff;
  //  border: 1px solid #0581ce;
  //  box-shadow: 0px 4px 4px 2px rgba(5, 129, 206, 0.1);
  //}

  .title {
    height: 55px;
    line-height: 55px;
    background: linear-gradient(90deg, #DBE9F4 -6.61%, #FFF 102.97%);
    border-radius: 10px 10px 0px 0px;

    p {
      padding-left: 24px;
      font-weight: 700;
      font-size: 20px;
      color: #ffffff;
    }
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    margin: 0 24px 24px 0px;
    .garde {
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: rgba(5, 129, 206, 0.05);
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .garde:hover {
      background-color: #0581ce;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }

    //缺血样式
    .ischemia{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #F3F3FF;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .ischemia:hover {
      background-color: #525AC9;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }

    //卒中再发风险评分
    .apoplexy{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #ECFCF8;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .apoplexy:hover {
      background-color: #62A83F;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }

    //动脉瘤
    .aneurysm{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #EFFAFF;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .aneurysm:hover {
      background-color: #0581CE;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }

    //脑血管畸形
    .cerebrovascular{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #FFFAED;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .cerebrovascular:hover {
      background-color: #E79E0C;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }


    //步态分析法
    .gait{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #F3F3FF;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .gait:hover {
      background-color: #525AC9;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }

    //步态分析法
    .parkin{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #ECFCF8;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .parkin:hover {
      background-color: #62A83F;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }


    //其他
    .other{
      box-sizing: border-box;
      font-weight: 700;
      height: 38px;
      color: #333333;
      font-size: 16px;
      margin-top: 24px;
      background: #EFFAFF;
      border-radius: 4px;
      margin-left: 24px;

      .aaa {
        display: block;
        padding: 9px 14px;
        color: #333;
        margin-bottom: 14px;
      }
    }

    .other:hover {
      background-color: #0581CE;
      .aaa {
        color: #ffffff !important;
        display: block;
      }
    }
  }
}

.nav_list{
  min-width: 1440px;
  min-height: 235px;
  //background: #0A83CE;
  margin: 0 auto;
  position: relative;
  img{
    width: 100%;
    height: 100%;;
  }

  .nav-right {
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.28);
    color: #FFF;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    position: absolute;
    right: 0;
    top: 25px;
    right: 155px;
    display: flex;
    padding: 8px;
    gap: 6px;
    width: 110px;
    height: 38px;
    box-sizing: border-box;
    cursor: pointer;
    overflow: hidden;
    .score-icon{
      content: url('~/static/minitool/score_icon.png');
      width: 22px;
      height: 22px;
    }
  }

  .nav-right:hover {
    background: #FFF;
    color: #0581CE;
    .score-icon{
      content: url('~/static/minitool/choose_score_icon.png');
      width: 22px;
      height: 22px;
    }
  }
}

.big-box{
  background: #EEF4F6;
  overflow-x: hidden;
  overflow-y: hidden;
}



