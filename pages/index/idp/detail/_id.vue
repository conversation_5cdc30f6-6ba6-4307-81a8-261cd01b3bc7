<template>
  <div class="page_wrapper">
    <Container>
      <DockingReleaseBtn
        :list-btn-show="false"
      />
      <ContainerPage>
        <template #left>
          <div class="page_left_wrapper">
            <Sidebar
              :comments="detailData.comments"
              :is-enable-like-button="false"
              article-type="pgc"
              :show-diggs="0"
              :show-collects="detailData.collects"
              :is-article-digg="false"
              :is-article-collect="detailData.collectStatus === 'T'"
              :meta-description="detailData.requirementDetail"
              @articleCollectHandler="articleCollectHandler"
            />

            <div class="docking_header_wrapper">
              <div class="header_left">
                <span class="push_time">{{ timeStamp.timestamp_13(detailData.createTime, 'y-m-d') }}发布</span>
                <span class="read_number">{{ $tool.formatterNum(detailData.views) }}阅读</span>
              </div>
              <div v-if="detailData.viewerAvatarList.length>0" class="header_right">
                <span style="margin-right: 12px">最近观看过：</span>

                <AvatarScrolling
                  :slider-head-img-list="detailData.viewerAvatarList"
                />
                <!--              <div class="user_images">-->
                <!--                <div-->
                <!--                  v-for="(item,index) in detailData.viewerAvatarList"-->
                <!--                  :key="index"-->
                <!--                  class="image">-->
                <!--                  <zip-img-->
                <!--                    fill-->
                <!--                    :width="24"-->
                <!--                    :height="24"-->
                <!--                    :src="item"-->
                <!--                    :type-user="true"-->
                <!--                  />-->
                <!--                </div>-->
                <!--              </div>-->
              </div>
            </div>


            <div class="detail_title">
              {{ detailData.title }}
            </div>

            <div class="docking_info_tip_wrapper">
              <div v-if="detailData.publishSubject === 1" class="docking_info_tip">
                由<a target="_blank" :href="`/user-center?profileUserId=${detailData.publisher.id}`"
                     class="brand">{{ detailData.publishName }}</a>发布
              </div>
              <div v-else-if="detailData.publishSubject !== 1 && detailData.publisher.identity === 4"
                   class="docking_info_tip">
                由<a target="_blank"
                     :style=" detailData.publisher.productLineId ? {}:{pointerEvents: 'none'}"
                     :href="`/bms/classify/3/product-line-details/${detailData.publisher.productLineId}`"
                     class="brand">{{
                  detailData.publisher.brandName ? `${detailData.publisher.brandName}·${detailData.publisher.productLineName}` : detailData.publisher.company
                }}</a>发布
              </div>
              <div v-else-if="detailData.publishSubject !== 1 && detailData.publisher.identity === 1"
                   class="docking_info_tip">
                由<a
                :style=" detailData.publisher.departmentId ? {}:{pointerEvents: 'none'}"
                target="_blank" :href="`/department/detail?departmentId=${detailData.publisher.departmentId}`"
                class="brand">{{
                  detailData.publisher.hospitalName ? `${detailData.publisher.hospitalName}·${detailData.publisher.departmentName}` : detailData.publisher.company
                }}</a>发布
              </div>

              <div v-else
                   class="docking_info_tip">
                由<a
                :style="{pointerEvents: 'none'}"
                target="_blank"
                class="brand">{{
                  detailData.publisher.company || detailData.publisher.realName
                }}</a>发布
              </div>

              <el-popover
                v-if="detailData.contactInformationVisible === 1"
                style="height: 25px"
                placement="bottom"
                popper-class="docking_businessQrCode_popover"
                trigger="click">
                <img slot="reference"
                     src="~assets/images/industrial/contact_information.png"
                     alt="联系方式"
                     class="contact_information">
                <div class="popover_contact_wrapper" style="display: flex">
                  <div v-if="detailData.publishSubject === 1" class="left" style="display: flex;align-items: center">
                    <img :src="detailData.publisher.avatarAddress || `${require('/assets/images/industrial/avatar.png')}`"
                         alt="" class="image">
                    <div class="info">
                      <p class="name">{{ detailData.publishName }}</p>
                    </div>
                  </div>

                  <div v-else class="left"
                       style="display: flex;align-items: center">
                    <img
                      :src="detailData.publisher.avatarAddress || `${require('/assets/images/industrial/avatar.png')}`"
                      alt=""
                      class="image">
                    <div class="info">
                      <p class="name">{{detailData.publisher.realName ? detailData.publisher.realName.split('')[0] + '**' : '**' }}</p>
                      <p class="company">{{ detailData.publisher.company }}</p>
                    </div>
                  </div>

                  <div class="right"><p>{{ detailData.contactInformation }}</p></div>
                </div>
              </el-popover>

              <el-popover
                v-else
                style="height: 25px"
                placement="bottom"
                width="200"
                popper-class="docking_businessQrCode_popover"
                trigger="click">
                <img slot="reference"
                     src="~assets/images/industrial/contact_information.png"
                     alt="联系方式"
                     class="contact_information">
                <div class="popover_wrapper">
                  <img :src="businessQrCode.businessQrCode" alt="">
                  <p>扫码添加脑医汇商务对接微信</p>
                  <p>帮您一键对接意向企业</p>
                </div>
              </el-popover>

            </div>

            <template>
              <InfoContent v-if="detailData.contentType === 1" :content="detailData.requirementDetail"/>
              <div v-else class="docking_txt">{{detailData.requirementDetail}}</div>
            </template>

            <template v-if="detailData.fileList && detailData.fileList.length>0">
              <div v-for="item in detailData.fileList" class="docking_file_wrapper">
                <div v-if="item.type === 0" class="docking_image">
                  <img :src="item.fileUrl" alt="" class="image">
                </div>
                <div v-if="item.type === 1" class="docking_video">
                  <video :src="item.fileUrl" controls :poster="item.cover">
                    Your browser does not support the video element.
                  </video>
                </div>
              </div>

            </template>


            <div class="docking_bottom_wrapper">
              <p class="tip">
                <span class="label">有效期：</span>
                <span>{{ detailData.periodOfValidity || "长期有效" }}</span>
              </p>
              <p v-if="detailData.area" class="tip">
                <span class="label">地区：</span>
                <span>{{ detailData.area }}</span>
              </p>
              <p class="tips">*请您注意核实对接需求信息及发布方真实性，如发现虚假或违法信息，请及时向脑医汇反馈</p>
            </div>

            <DockingBrandInfo
              v-if="detailData.publisher.productLineId"
              :publisher="detailData.publisher"
            />

            <PublicComments
              :comment-data="commentData"
              @likeHandler="likeHandler"
              @pushHandler="pushHandler"
              @getChildComment="getChildComment"
              @pageUp="(current) => getCommentsData(current)"
            />
          </div>
        </template>

        <template #right>
          <div ref="page_right_wrapper" class="page_right_wrapper">
            <div class="entrance_module">
              <div class="module_left">
                <img class="module_image" src="~assets/images/industrial/entrance.png" alt="产业对接">
                <span>产业对接</span>
              </div>
              <a target="_self" href="/idp" class="module_btn" @click="$analysys.btn_click('产业对接', detailData.title)">查看</a>
            </div>

            <div class="docking_info">
              <div class="title">更多对接信息</div>

              <template v-for="item in moreIdpInformationData">
                <DockingDefaultItem
                  :is-move-effect="false"
                  :key="item.id"
                  :infoId="item.id"
                  :title="item.title"
                  :typeList="item.typeList"
                  :classification="item.classification"
                  :publisher="item.publisher"
                  :periodOfValidity="item.periodOfValidity"
                  :publishName="item.publishName"
                  :links="item.links"
                  :publishSubject="item.publishSubject"
                />
                <div class="line"></div>
              </template>

            </div>


          </div>
        </template>
      </ContainerPage>
    </Container>


    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import {InfoContent} from "../../../../opt-components/page/info-detail/index.js";
import {Container} from "../../../../opt-components/template/index.js";
import {PublicComments} from "../../../../opt-components/component/index.js";
import {ContainerPage} from "../../../../opt-components/template/index.js";
import {DockingDefaultItem} from "../../../../opt-components/data-list/index.js";
import ZipImg from "../../../../opt-components/component/ZipImg/index.vue";
import {Sidebar} from "../../../../opt-components/page/info-detail/index.js";
import {
  addViews,
  collectIdpInformation, getBusinessQrCode, getChildIdpCommentList, getIdpCommentPage,
  getIdpInformationDetail,
  getMoreIdpInformationList, idpCommentDigg, saveIdpComment
} from "../../../../api/docking.js";
import DockingBrandInfo from "../../../../opt-components/page/docking/DockingBrandInfo.vue";
import ShortVideoPlayback from "../../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import AvatarScrolling from "../../../../opt-components/page/docking/AvatarScrolling.vue";
import DockingReleaseBtn from "../../../../opt-components/page/docking/DockingReleaseBtn.vue";
export default {
  name: "IndustrialDockingPage",
  components: {
    DockingReleaseBtn,
    PublicComments,
    ZipImg,
    ContainerPage,
    DockingDefaultItem,
    Sidebar,
    DockingBrandInfo,
    ShortVideoPlayback,
    AvatarScrolling,
    Container,
    InfoContent
  },
  async asyncData({app, params, error, store, query, req}) {
    const [MoreIdpInformationData, IdpInformationDetail, BusinessQrCode] = await Promise.all([
      app.$axios.$request(getMoreIdpInformationList({
        informationId: params.id,
        pageNo: 1,
        pageSize: 5
      })),
      app.$axios.$request(getIdpInformationDetail({
        informationId: params.id,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(getBusinessQrCode())
    ])

    return {
      moreIdpInformationData: MoreIdpInformationData.list,
      detailData: IdpInformationDetail.result,
      businessQrCode: BusinessQrCode.result
    }
  },
  head() {
    return {
      title: this.detailData.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.detailData.requirementDetail
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.detailData.title+',脑科产业对接'
        }]
    }
  },
  data() {
    return {
      commentData: {
        list: [],
        page: {}
      },
    }
  },
  mounted() {
    this.getCommentsData(1)
    // 增加浏览量
    this.$axios.$request(addViews({
      informationId: this.$route.params.id,
      loginUserId: this.$store.state.auth.user.id
    }))

    this.$nextTick(() => {
      // 计算右侧栏目高度
      window.setTimeout(() => {
        const rightWrapper = this.$refs.page_right_wrapper
        if (rightWrapper) {
          const rightTop = (rightWrapper.offsetHeight - window.innerHeight) + 32

          if (rightWrapper.offsetHeight > (window.innerHeight - 84)) {
            rightWrapper.style.top = rightTop > 0 ? "-" + rightTop + "px" : rightTop + "px"
          }

        }
      }, 10)
    })
  },
  methods: {
    articleCollectHandler() {
      this.$set(this.detailData, "collectStatus", this.detailData.collectStatus === 'F' ? 'T' : 'F')

      // 点赞接口
      this.$axios.$request(collectIdpInformation({
        informationId: this.$route.params.id
      }))
    },

    getCommentsData(pageNo = 1) {
      this.$axios.$request(getIdpCommentPage({
        informationId: this.$route.params.id,
        loginUserId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 10
      })).then(res => {
        if (res.code === 1) {
          // const list = res.list.map(item => {
          //   return item.parent
          // })

          this.$set(this.commentData, 'list', this.commentData.list.concat(res.list));
          this.$set(this.commentData, 'page', res.page);
        }
      })
    },
    likeHandler(id, backFn) {
      this.$axios.$request(idpCommentDigg({
        commentId: id,
      })).then(res => {
        if (res.result.diggStatus === 'T') {
          backFn(true)
        } else {
          backFn(false)
        }
      })
    },
    getChildComment({parentCommentId, pageNo}, backFn) {
      this.$axios.$request(getChildIdpCommentList({
        parentCommentId,
        loginUserId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 4
      })).then(res => {
        if (res.code === 1) {
          backFn({
            list: res.list,
            page: res.page
          })
        }
      })
    },
    pushHandler({info, parentId}, backFn) {
      this.$axios.$request(saveIdpComment({
        content: info,
        informationId: this.$route.params.id,
        parentId
      })).then((response) => {
        if (response && response.code === 1) {
          backFn(response.result)
          this.$toast(response.message || "评论成功")
        } else {
          backFn(false)
          this.$toast(response.message || "评论成功")
        }
      })
    },
  }
}
</script>

<style>
.docking_businessQrCode_popover {
  .popover_contact_wrapper {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between;
    padding: 5px 0;

    .left {
      flex-shrink: 0;
      display: flex !important;
      align-items: center;
      margin-right: 15px;

      .image {
        width: 32px;
        height: 32px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .info {
        .name {
          color: #333;
          font-size: 15px;
          font-weight: 600;
          line-height: 1.5;
        }

        .company {
          color: #666;
          font-size: 11px;
          line-height: 1.5;
        }
      }
    }

    .right {
      color: #333;
      font-size: 16px;
    }
  }

  .popover_wrapper {
    p {
      text-align: center;
      font-size: 12px;
      color: #333;
    }

    img {
      width: 100%;
    }
  }
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
