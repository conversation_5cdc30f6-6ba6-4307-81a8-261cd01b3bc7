.page_wrapper {
  background: #EEF2F3;
  padding: 24px 0 32px;

  .page_left_wrapper {
    padding: 27px 24px;
    border-radius: 8px;
    background: #FFF;
    position: relative;

    .docking_header_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header_left {
        color: #999;
        font-size: 13px;
        line-height: 1.5;
      }

      .header_right {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 13px;
        line-height: 1.5;

        .user_images {
          display: flex;
          align-items: center;

          .image {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
            margin-left: -10px;
          }
        }
      }
    }

    .detail_title {
      font-size: 20px;
      color: #333;
      line-height: 1.5;
      font-weight: 600;
      margin-bottom: 12px;
    }

    .docking_info_tip_wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 24px;

      .contact_information {
        width: 96px;
        margin-left: 8px;
        cursor: pointer;
      }
    }

    .docking_info_tip {
      color: #999;
      font-size: 12px;
      line-height: 20px;

      .brand {
        color: #0581CE;
        font-size: 14px;
        line-height: 20px;
        margin: 0 4px;
      }
    }

    .docking_txt {
      color: #333;
      font-size: 16px;
      line-height: 1.5;
      white-space: pre-wrap
    }

    .docking_file_wrapper {
      margin: 10px 0;

      .docking_image {
        width: 100%;

        .image {
          max-width: 100%;
        }
      }

      .docking_video {
        width: 100%;

        video {
          max-width: 100%;
        }
      }
    }


    .docking_bottom_wrapper {
      margin: 14px 0 30px;

      .tip {
        color: #333;
        font-size: 14px;
        margin-bottom: 8px;

        .label {
          display: inline-block;
          min-width: 56px;
          color: #666;
          font-size: 14px;
          margin-right: 8px;
        }
      }

      .tips {
        margin-top: 14px;
        color: #999;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }

  .page_right_wrapper {
    display: flex;
    flex-flow: column;
    gap: 24px 0;
    position: sticky;
    top: calc(60px + 24px);

    .entrance_module {
      border-radius: 4px;
      background: #FFF;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;

      .module_left {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #333;

        .module_image {
          width: 40px;
          height: 40px;
          flex-shrink: 0;
          margin-right: 12px;

        }
      }

      .module_btn {
        border-radius: 4px;
        border: 0.5px solid #0581CE;
        background: rgba(5, 129, 206, 0.05);
        width: 45px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #0581CE;
        cursor: pointer;

        &:hover {
          border: 0.5px solid #46B0F2;
          background: rgba(70, 176, 242, 0.05);
          color: #46B0F2;
        }
      }
    }

    .docking_info {
      border-radius: 8px;
      background: #FFF;

      .title {
        padding: 16px 18px 0;
        font-weight: 600;
        font-size: 16px;
        color: #333;
      }

      .line {
        width: 100%;
        height: 1px;
        background: #F4F4F4;
      }
    }
  }
}
