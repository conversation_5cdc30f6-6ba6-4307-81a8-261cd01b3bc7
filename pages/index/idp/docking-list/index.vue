<template>
  <div class="docking_list_page">
    <Container>
      <div class="docking_container">
        <DockingTabs :active-component="activeComponent" :tab-list="tabList" @changeTabHandler="changeTabHandler"/>


        <DropdownLoading
          v-show="activeComponent === '0'"
          :loading="loading1"
          :empty="publishInformationData.list.length===0"
          :no-more="current1 >= publishInformationData.page.totalPage"
          @hit-bottom="publishHitBottomChangeHandler"
        >
          <div class="docking_list_wrapper">
            <template v-for="(item,index) in publishInformationData.list">
              <div
                :key="item.id"
                @click="jumpDetail(item.id)"
                class="release_docking_item"
                :class="activeIndex === index ? 'release_docking_item_active' : ''"
                @mouseenter="activeIndex = index"
              >
                <div class="edit_delete_wrapper">
                  <a v-if="item.contentType === 0" @click.stop target="_blank " :href="`/idp/release?informationId=${item.id}`"
                     class="edit_btn">
                    <svg-icon icon-class="edit_docking" class-name="edit_icon"/>
                    <span>编辑</span>
                  </a>
                  <div v-if="item.contentType === 0"  class="line"></div>
                  <Popconfirm
                    confirm-button-text='删除'
                    cancel-button-text='取消'
                    icon="el-icon-info"
                    icon-color="red"
                    title="确认删除吗？"
                    placement="bottom-end"
                    @confirm="confirmDelete(item.id)"
                  >
                    <div slot="reference" class="edit_btn" @click.stop>
                      <svg-icon icon-class="delete_docking" class-name="edit_icon"/>
                      <span>删除</span>
                    </div>
                  </Popconfirm>
                </div>

                <div class="title text-limit-1">
                  {{ item.title }}
                </div>
                <div class="docking_info text-limit-2">
                  {{ item.requirementDetail }}
                </div>
                <div class="docking_tip">
                  <div class="time">{{
                      timeStamp.timestampFormat(
                        Date.parse(item.createTime.replace(/-/g, '/')) / 1000
                      )
                    }}
                  </div>
                  <div v-if="item.status === 0" class="verify">审核未过</div>
                  <div v-else-if="item.status === 2" class="verify">待审核</div>
                </div>
              </div>
            </template>
          </div>
        </DropdownLoading>

        <DropdownLoading
          v-show="activeComponent === '1'"
          :loading="loading2"
          :empty="collectInformationData.list.length===0"
          :no-more="current2 >= collectInformationData.page.totalPage"
          @hit-bottom="collectHitBottomChangeHandler"
        >
          <div class="docking_list_wrapper">
            <template v-for="item in collectInformationData.list">
              <a
                :key="item.id"
                target="_blank"
                :href="`/idp/detail/${item.id}`"
                class="release_docking_item">
                <div class="title text-limit-1">
                  {{ item.title }}
                </div>
                <div class="docking_info text-limit-2">
                  {{ item.requirementDetail }}
                </div>
                <div class="docking_tip">
                  <div class="time">收藏于 {{
                      timeStamp.timestampFormat(
                        Date.parse(item.createTime.replace(/-/g, '/')) / 1000
                      )
                    }}
                  </div>
                </div>
              </a>
            </template>
          </div>
        </DropdownLoading>
      </div>

    </Container>
  </div>
</template>

<script>
import DockingTabs from "../../../../opt-components/page/docking/DockingTabs.vue";
import {Container} from "../../../../opt-components/template/index.js";
import {
  deleteDockingInformation,
  getUserCollectInformationList,
  getUserPublishInformationList
} from "../../../../api/docking.js";
import DropdownLoading from "../../../../components/optimize-components/public/DropdownLoading/index.vue";
import {Popconfirm} from "element-ui";

export default {
  name: "DockingListPage",
  components: {
    Container,
    DockingTabs,
    DropdownLoading,
    Popconfirm
  },
  asyncData({app, params, error, store, query, req, redirect, route}) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
    }
  },
  data() {
    return {
      activeIndex: null,
      loading1: true,
      loading2: true,
      current1: 1,
      current2: 1,
      activeComponent: "0",
      tabList: [
        {id: "0", tabName: '发布的对接', isEnable: true},
        {id: "1", tabName: '收藏的对接', isEnable: true}
      ],
      publishInformationData: {
        list: [],
        page: {}
      },
      collectInformationData: {
        list: [],
        page: {}
      }
    }
  },
  head() {
    return {
      title: '对接列表',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '对接列表'
        }
      ]
    }
  },
  mounted() {
    this.getPublishData({pageNo: 1})
    this.getCollectData({pageNo: 1})
  },
  methods: {
    confirmDelete(informationId) {
      this.$axios.$request(deleteDockingInformation({
        informationId
      })).then(res => {
        if (res.code === 1) {
          this.$toast('删除完成')
          this.$set(this.publishInformationData, "list", this.publishInformationData.list.filter(item => item.id !== informationId))
        }
      })
    },
    jumpDetail(id) {
      const {href} = this.$router.resolve({
        path: `/idp/detail/${id}`
      })
      window.open(href, '_blank')

    },
    getPublishData({pageNo = 1}) {
      this.$axios.$request(getUserPublishInformationList({
        loginUserId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 16
      })).then(res => {
        if (res.code === 1) {
          this.$set(this.publishInformationData, "list", this.publishInformationData.list.concat(res.list))
          this.$set(this.publishInformationData, "page", res.page)
        }

        this.loading1 = false;
      })
    },

    getCollectData({pageNo = 1}) {
      this.$axios.$request(getUserCollectInformationList({
        loginUserId: this.$store.state.auth.user.id,
        pageNo,
        pageSize: 15
      })).then(res => {
        if (res.code === 1) {
          this.$set(this.collectInformationData, "list", this.collectInformationData.list.concat(res.list))
          this.$set(this.collectInformationData, "page", res.page)
        }

        this.loading2 = false;
      })
    },
    changeTabHandler(id) {
      this.activeComponent = id;
    },
    publishHitBottomChangeHandler(flag) {
      if (flag) {
        this.current1 += 1;
        this.loading1 = true

        this.getPublishData({
          pageNo: this.current1,
        })
      }
    },
    collectHitBottomChangeHandler(flag) {
      if (flag) {
        this.current2 += 1;
        this.loading2 = true

        this.getCollectData({
          pageNo: this.current2,
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.docking_list_page {
  background: #EEF4F6;
  padding: 24px 0 40px;

  .docking_container {
    border-radius: 8px;
    background: #FFF;
    padding: 0 30px 40px;
  }

  .docking_list_wrapper {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px 20px;

    .release_docking_item {
      border-radius: 8px;
      border: 1px solid #EAEEF0;
      padding: 16px 18px;
      position: relative;
      cursor: pointer;

      .edit_delete_wrapper {
        height: 24px;
        position: absolute;
        top: 16px;
        right: 16px;
        opacity: 0;
        transition: .3s;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        background: rgba(51, 51, 51, 0.75);


        .edit_btn {
          width: 70px;
          font-size: 14px;
          color: #FFF;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .edit_icon {
            width: 18px;
            height: 18px;
            margin-right: 4px;
          }
        }

        .line {
          width: 1px;
          height: 15px;
          border-radius: 2px;
          background: rgba(255, 255, 255, 0.50);
        }
      }


      .title {
        color: #333;
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .docking_info {
        color: #666;
        font-size: 15px;
        line-height: 1.5;
        height: 45px;
        margin-bottom: 12px;
      }

      .docking_tip {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .time {
          color: #999;
          font-size: 12px;
        }

        .verify {
          color: #FF9600;
          font-size: 12px;
        }
      }

      &:hover {
        .title {
          color: #0581CE;
        }
      }
    }

    .release_docking_item_active {
      .edit_delete_wrapper {
        opacity: 1;
      }
    }
  }
}
</style>
