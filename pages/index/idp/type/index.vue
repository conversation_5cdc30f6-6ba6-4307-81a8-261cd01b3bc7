<template>
  <div class="industry_screening_page">
    <Container>
      <DockingReleaseBtn
        :list-btn-show="false"
      />
      <div class="screening_container">

        <div class="filter_container">

          <div class="filter_item">
            <span class="filter_name">搜索</span>
            <div :style="{marginTop:'4px'}">
              <el-input placeholder="搜索标题/发布内容" v-model="searchValue" @keyup.enter.native='searchHandler'>
                <div slot="append" class="append">
                  <div :style="searchValue ?{opacity:1}:{opacity: 0}" class="close">
                    <svg-icon v-if="searchValue" @click="searchValue = ''" icon-class="close_icon"
                              class-name="close_icon"/>
                  </div>
                  <svg-icon icon-class="search" class-name="search_icon" @click="searchHandler"/>
                </div>

              </el-input>
            </div>
          </div>

          <div class="filter_item">
            <span class="filter_name">类型</span>
            <div class="filter_option_wrapper">

              <div
                v-for="item in typeList"
                :key="item.id"
                class="option_f_wrapper">
                <div
                  class="option_item"
                  :class="item.id === typeId ? 'option_item_active' : ''"
                  @click="changeType(item.id)"
                >{{ item.name }}
                </div>
                <div v-if="item.children.length > 0" class="child_option_item_wrapper">
                  <div
                    v-for="itemChild in item.children"
                    :key="itemChild.id"
                    class="child_option_item"
                    :class="itemChild.id === typeId ? 'child_option_item_active' : ''"
                    @click="changeType(itemChild.id)"
                  >
                    {{ itemChild.name}}
                  </div>
                </div>
              </div>


            </div>
          </div>

          <div class="filter_item">
            <span class="filter_name">分类</span>
            <div class="filter_option_wrapper">
              <div v-for="item in classificationList" :key="item.id" class="option_item"
                   :class="item.id === classificationId ? 'option_item_active' : ''"
                   @click="changeClassification(item.id)">{{ item.name }}
              </div>
            </div>
          </div>

          <div class="filter_item">
            <span class="filter_name">主体</span>
            <div class="filter_option_wrapper">
              <!--0-->
              <div class="option_item" :class="publishSubject === 0 ? 'option_item_active' :''"
                   @click="changePublishSubject(0)">单位
              </div>
              <!--1-->
              <div class="option_item" :class="publishSubject === 1 ? 'option_item_active' :''"
                   @click="changePublishSubject(1)">个人
              </div>
            </div>
          </div>


          <div class="filter_item">
            <span class="filter_name">区域</span>
            <div>
              <div class="filter_option_wrapper">
                <div v-for="item in areaList" :key="item.id" class="option_item"
                     :class="province === item.name ? 'option_item_active' :''" @click="changeProvince(item.name)">
                  {{ item.brief }}
                </div>
              </div>
              <div v-if="province" class="city_wrapper">
                <div v-for="item in areaList.filter( item => item.name === province)[0].children"
                     :key="item.id"
                     :class="item.name === city ? 'city_item_active' :''"
                     @click="changeCity(item.name)"
                     class="city_item">{{ item.name }}
                </div>
              </div>
            </div>
          </div>


        </div>


        <div class="industrial_list_wrapper">
          <div class="filter_new">
            <div class="filter_btn" :class="seqType === 0 ? 'filter_btn_active' : ''" @click="changeSeqType(0)">全部
            </div>
            <div class="filter_btn" :class="seqType === 1 ? 'filter_btn_active' : ''" @click="changeSeqType(1)">最热
            </div>
          </div>

          <RollingLoad
            :loading="loading"
            :empty="dpDataList.list.length===0"
            :no-more="currentPageNo >= dpDataList.page.totalPage"
            empty-height="35vh"
            @hit-bottom="hitBottomChangeHandler"
          >
            <div class="list_grid">
              <template v-for="(item,index) in dpDataList.list">
                <DockingDefaultItem
                  :key="index"
                  :infoId="item.id"
                  :title="item.title"
                  :typeList="item.typeList"
                  :classification="item.classification"
                  :publisher="item.publisher"
                  :periodOfValidity="item.periodOfValidity"
                  :publishName="item.publishName"
                  :links="item.links"
                  :publishSubject="item.publishSubject"
                />
              </template>
            </div>
          </RollingLoad>

        </div>
      </div>
    </Container>
  </div>
</template>

<script>
import {Container} from "../../../../opt-components/template/index.js";
import DockingDefaultItem from "../../../../opt-components/data-list/Docking/default/index.vue";
import {
  getAreaList,
  getClassificationList,
  getIdpInformationList,
  getTypeList,
  idpQuery
} from "../../../../api/docking.js";
import RollingLoad from "../../../../opt-components/component/RollingLoad/index.vue";
import DockingReleaseBtn from "../../../../opt-components/page/docking/DockingReleaseBtn.vue";

export default {
  name: "IndustryScreening",
  components: {
    DockingReleaseBtn,
    DockingDefaultItem,
    Container,
    RollingLoad
  },
  head() {
    return {
      title: '脑医汇 - 产业对接筛选 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇,神外资讯,神介资讯,神内资讯,脑科产业对接,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断'
        }
      ]
    }
  },
  async asyncData({app, params, error, store, query, req}) {
    const [IdpInformationData, TypeListData, classificationData, AreaList] = await Promise.all([
      app.$axios.$request(getIdpInformationList({
        typeId: query.typeId,
        seqType: 0,
        pageNo: 1,
        pageSize: 12
      })),
      app.$axios.$request(getTypeList()),
      app.$axios.$request(getClassificationList()),
      app.$axios.$request(getAreaList())
    ])

    return {
      typeId: query.typeId ? Number(query.typeId) : null,
      dpDataList: IdpInformationData,
      typeList: TypeListData.list,
      classificationList: classificationData.list,
      areaList: AreaList.list
    }
  },
  data() {
    return {
      loading: true,
      currentPageNo: 1,
      // 搜索内容
      searchValue: "",
      // 分类ID
      classificationId: null,
      // 主体
      publishSubject: null,
      // 省
      province: null,
      // 市
      city: null,
      // 排序方式
      seqType: 0,
      source: null,
      isSearch: false
    }
  },
  watch: {
    searchValue(newValue) {
      if (!newValue) {
        this.isSearch = false
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }
        this.getData()
      }
    }
  },
  mounted() {
    this.loading = false;
  },
  methods: {
    searchHandler() {
      if (this.searchValue) {
        this.isSearch = true;
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }
        this.getData()
      } else {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }
        this.getData()
      }

    },
    getData(pageUp) {
      if (!pageUp) {
        this.$set(this.dpDataList, "list", [])
        this.$set(this.dpDataList, "page", {})

        this.currentPageNo = 1;
        this.loading = true;
        const CancelToken = this.$axios.CancelToken;
        this.source = CancelToken.source();
      }

      if (this.isSearch) {
        this.$axios.$request(idpQuery({
          keywords: this.searchValue,
          orderRule: this.seqType === 0 ? 1 : 2,
          typeId: this.typeId,
          classificationId: this.classificationId,
          publishSubject: this.publishSubject,
          province: this.province,
          city: this.city === "全部" || this.province === this.city ? null : this.city,
          pageNo: this.currentPageNo,
          pageSize: 12
        }, pageUp ? null : this.source.token)).then(res => {
          if (res.code === 1) {

            if (pageUp) {
              this.$set(this.dpDataList, "list", [...this.dpDataList.list, ...res.list])
              this.$set(this.dpDataList, "page", res.page)
            } else {
              this.$set(this.dpDataList, "list", res.list)
              this.$set(this.dpDataList, "page", res.page)
            }

          }

          this.loading = false;
        })
      } else {
        this.$axios.$request(getIdpInformationList({
          typeId: this.typeId,
          classificationId: this.classificationId,
          publishSubject: this.publishSubject,
          areas: (this.province || '') + (this.city || ''),
          seqType: this.seqType,
          pageNo: this.currentPageNo,
          pageSize: 12
        }, pageUp ? null : this.source.token)).then(res => {
          if (res.code === 1) {

            if (pageUp) {
              this.$set(this.dpDataList, "list", [...this.dpDataList.list, ...res.list])
              this.$set(this.dpDataList, "page", res.page)
            } else {
              this.$set(this.dpDataList, "list", res.list)
              this.$set(this.dpDataList, "page", res.page)
            }

          }

          this.loading = false;
        })
      }


    },
    // 切换类型
    changeType(id) {
      if (this.typeId !== id) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }
        this.typeId = id;
        this.$router.push({
          path: `/idp/type?typeId=${id}`
        })
        this.getData()
      } else {
        this.typeId = null;
        this.$router.push({
          path: `/idp/type`
        })
        this.getData()
      }
    },
    // 切换分类
    changeClassification(id) {
      if (this.classificationId !== id) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }
        this.classificationId = id;
        this.getData()
      } else {
        this.classificationId = null;
        this.getData()
      }
    },
    // 切换主体
    changePublishSubject(id) {
      if (this.publishSubject !== id) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }

        this.publishSubject = id;
        this.getData()
      } else {
        this.publishSubject = null;
        this.getData()
      }
    },
    // 切换省
    changeProvince(name) {
      if (this.province !== name) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }

        this.province = name;
        const cityArr = this.areaList.filter(item => item.name === name)
        if (cityArr && cityArr.length > 0) {
          this.city = cityArr[0].children[0].name
        }
        this.getData()
      } else {
        this.province = null;
        this.city = null;
        this.getData()
      }
    },
    changeCity(name) {
      if (this.city !== name) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }

        this.city = name;
        this.getData()
      } else {
        this.city = null;
        this.getData()
      }
    },
    changeSeqType(id) {
      if (this.seqType !== id) {
        if (this.source) {
          this.source.cancel('Operation canceled by the user');
        }

        this.seqType = id;
        this.getData()
      }
    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.loading = true;
        this.currentPageNo += 1;


        this.getData(true)

      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
