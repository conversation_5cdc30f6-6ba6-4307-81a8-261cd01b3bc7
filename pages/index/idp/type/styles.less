
.industry_screening_page {
  background: #EEF4F6;
  padding: 24px 0 40px;

  .screening_container {
    border-radius: 8px;
    background: #FFF;
    padding: 0 0 30px;

    .filter_container {
      padding: 14px 0;
      border-bottom: 1px solid #EAEEF0;

      .filter_item {
        display: flex;
        align-items: start;
        padding-right: 32px;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        /deep/ .el-input {
          background: #F5FCFF;
          //background: red;
          border: none;
          border-radius: 18px;
          transition: all .3s;
        }

        /deep/ .el-input__inner {
          width: 210px;
          height: 28px;
          //padding-top:24px;
          border: none;
          background: unset;
          border-radius: 18px;
          font-size: 14px;

          ::placeholder {
            color: rgba(51, 51, 51, 0.45);
            font-size: 14px;
          }
        }

        /deep/ .append {
          display: flex;
          align-items: center;
          transition: all .3s;
          user-select: none;
        }

        /deep/ .close_icon {
          color: white;
          cursor: pointer;
        }

        /deep/ .close {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: rgba(51, 51, 51, 0.43);
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 8px;
        }

        /deep/ .search_icon {
          //width: 13px;
          color: #708AA2;
          cursor: pointer;
        }

        /deep/ .el-input-group__append {
          background: unset;
          border: none;
          line-height: 28px;
          left: -12px;
          padding: 0;
        }

        .filter_name {
          height: 36px;
          line-height: 36px;
          flex-shrink: 0;
          padding: 0 20px;
          margin-right: 12px;
        }

        .filter_option_wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 12px 10px;

          .option_f_wrapper {
            display: flex;
            align-items: center;
            margin-top: 4px;
            background: #F4F4F4;
            border-radius: 18px;

            .child_option_item_wrapper {
              display: flex;
              align-items: center;
              gap: 10px;
              height: 28px;
              padding: 0 8px;

              .child_option_item {
                border-radius: 10px;
                background: #FFF;
                height: 20px;
                line-height: 20px;
                padding: 0 20px;
                color: #333;
                font-size: 13px;
                cursor: pointer;
                user-select: none;
              }
              .child_option_item_active {
                background: #0581CE;
                color: #FFF;
              }
            }

          }

          .option_item {
            user-select: none;
            cursor: pointer;
            min-width: 92px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 18px;
            background: #F5FCFF;
            flex-shrink: 0;
            font-size: 13px;
            color: #333;
            padding: 0 20px;
            box-sizing: border-box;
          }

          .option_item_active {
            background: #0581CE;
            color: #FFF;
          }
        }

        .city_wrapper {
          border-radius: 8px;
          background: #F8F8F8;
          padding: 14px;
          display: flex;
          gap: 16px 10px;
          flex-wrap: wrap;
          margin-top: 10px;

          .city_item {
            user-select: none;
            cursor: pointer;
            border-radius: 6px;
            background: #FFF;
            min-width: 80px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 13px;
            color: #333;
            padding: 0 10px;
            box-sizing: border-box;
          }

          .city_item_active {
            background: #0581CE;
            color: #FFF;
          }
        }
      }
    }

    .industrial_list_wrapper {
      padding: 0 30px;

      .filter_new {
        display: flex;
        align-items: center;
        margin: 24px 0;

        .filter_btn {
          cursor: pointer;
          margin-right: 16px;
          border-radius: 4px;
          background: #F8F8F8;
          padding: 8px;
          color: #676C74;
          font-size: 14px;
          line-height: 18px;
        }

        .filter_btn_active {
          background: #EFFAFF;
          color: #0581CE;
        }
      }

      .list_grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 24px 20px;
      }
    }
  }
}
