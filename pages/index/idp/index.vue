<template>
  <div class="industrial_page">
    <Container>
      <DockingReleaseBtn/>
      <div class="industrial_nav">
        <div class="left_banner">
          <div id="index_banner_wrapper" ref="index_banner_wrapper" class="banner_item">
            <el-carousel
              indicator-position='outside'
              :interval="3000"
              :arrow="bannerList.length > 1 ? 'hover' : 'never'"
              @change="changeBannerHandler"
            >
              <el-carousel-item v-for="item in bannerList" :key="item.adId">
                <div class="carousel_image_wrapper" @click="jumpBannerFun(item)">
                  <ZipImg
                    fill
                    :src="item.image"
                    :width="744"
                    :height="248"
                    :alt="item.name"
                  />
                  <div class="carousel_image_mask">
                    <p class="text-limit-1 mask_p">{{ item.name }}</p>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>

        <div class="right_menu">
          <a target="_blank" :href="item.module === 'bms' ? `/bms/home/<USER>/idp/type?typeId=${item.id}`"
             v-for="(item,index) in typeList.slice(0,8)"
             :key="item.id" class="menu_item"
             @click="$analysys.btn_click(item.name, '产业对接')"
          >
            <img :src="item.logo" :alt="item.name" class="menu_img">
            <p class="menu_name">{{ item.name }}</p>
          </a>
        </div>
      </div>

      <div class="industrial_list_wrapper">
        <DropdownLoading
          :loading="loading"
          :empty="dpDataList.list.length===0"
          :no-more="current >= dpDataList.page.totalPage"
          @hit-bottom="hitBottomChangeHandler"
        >
          <div class="list_grid">
            <template v-for="(item,index) in dpDataList.list">
              <DockingDefaultItem
                :key="item.id"
                :infoId="item.id"
                :title="item.title"
                :typeList="item.typeList"
                :classification="item.classification"
                :publisher="item.publisher"
                :periodOfValidity="item.periodOfValidity"
                :publishName="item.publishName"
                :links="item.links"
                :publishSubject="item.publishSubject"
              />
            </template>
          </div>
        </DropdownLoading>

      </div>
    </Container>
  </div>
</template>

<script>
import {Container} from "../../../opt-components/template/index.js";
import ZipImg from "../../../opt-components/component/ZipImg/index.vue";
import brandAdJump from "../../../assets/helpers/brandAdJump.js";
import {DockingDefaultItem} from "../../../opt-components/data-list/index.js";
import {getSlotContent} from "../../../api/banner/banner.js";
import {getIdpInformationList, getTypeList} from "../../../api/docking.js";
import DropdownLoading from "../../../components/optimize-components/public/DropdownLoading/index.vue";
import DockingReleaseBtn from "../../../opt-components/page/docking/DockingReleaseBtn.vue";

export default {
  name: "index",
  head() {
    return {
      title: '脑医汇 - 产业对接 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇,神外资讯,神介资讯,神内资讯,脑科产业对接,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断'
        }
      ]
    }
  },
  components: {
    DockingReleaseBtn,
    ZipImg,
    Container,
    DockingDefaultItem,
    DropdownLoading
  },
  async asyncData({app, params, error, store, query, req}) {
    const [bannerData, TypeListData, IdpInformationData] = await Promise.all([
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webapi_idp_Information',
        })
      ),
      app.$axios.$request(getTypeList()),
      app.$axios.$request(getIdpInformationList({
        pageNo: 1,
        pageSize: 12
      }))
    ])


    return {
      bannerList: bannerData.list,
      typeList: TypeListData.list,
      dpDataList: IdpInformationData
    }
  },
  data() {
    return {
      loading: true,
      current: 1,
      menuList: [
        {name: "找产品", image: require('/assets/images/industrial/menu_1.png')},
        {name: "找渠道", image: require('/assets/images/industrial/menu_2.png')},
        {name: "找服务", image: require('/assets/images/industrial/menu_3.png')},
        {name: "招聘/应聘", image: require('/assets/images/industrial/menu_4.png')},
        {name: "成果转化", image: require('/assets/images/industrial/menu_5.png')},
        {name: "投融资", image: require('/assets/images/industrial/menu_6.png')},
        {name: "风向标", image: require('/assets/images/industrial/menu_7.png')},
        {name: "园区招商", image: require('/assets/images/industrial/menu_8.png')},
      ],
      bannerIndexNum: 0,
    }
  },
  mounted() {
    this.loading = false
  },
  methods: {
    changeBannerHandler(index) {
      const item = this.bannerList[index]
      if (this.bannerIndexNum < this.bannerList.length) {
        this.bannerIndexNum = this.bannerIndexNum + 1
        this.$analysys.ad_exposure({
          adExtras: item.extras,
          adModule: item.module,
          exposureLocation: item.clickLocation,
          adCode: item.code,
          adId: item.adId,
          adName: item.name,
          unionid: this.$store.state.auth.unionid,
          adUrl: item.extras,
        })
      }
    },
    getData({pageNo = 1}) {
      this.$axios.$request(getIdpInformationList({
        pageNo,
        pageSize: 12
      })).then(res => {
        if (res.code === 1) {
          this.$set(this.dpDataList, "list", this.dpDataList.list.concat(res.list))
          this.$set(this.dpDataList, "page", this.dpDataList.page)
        }
        this.loading = false
      })
    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.current += 1;
        this.loading = true

        this.getData({
          pageNo: this.current,
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
