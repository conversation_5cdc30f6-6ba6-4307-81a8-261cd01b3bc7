.industrial_page {
  background: #EEF4F6;
  padding: 24px 0 40px;


  .industrial_nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;

    .left_banner {
      min-width: 744px;

      .banner_item {
        border-radius: 8px;
        background: #D9D9D9;
        overflow: hidden;
        position: relative;

        /deep/ .el-carousel__container {
          height: unset;
          padding-bottom: 33.333%;
        }

        /deep/ .el-carousel__arrow {
          font-size: 18px;
          font-weight: bold;
        }

        /deep/ .carousel_image_wrapper {
          width: 100%;
          height: 100%;
          position: relative;
          cursor: pointer;
        }

        /deep/ .carousel_image_mask {
          z-index: 1;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.8));

          .mask_p {
            position: absolute;
            left: 0;
            bottom: 32px;
            font-size: 14px;
            color: #FFFFFF;
            padding: 0 16px;
          }
        }

        /deep/ .el-carousel__indicators--outside {
          position: absolute;
          left: 0;
          bottom: 10px;
          padding: 0 16px;

          .el-carousel__indicator--horizontal {
            transition: all .3s;
            padding: 0;
            width: 3px;
            height: 3px;
            border-radius: 50%;
            background: #808081;
            margin-right: 3px;

            &:last-child {
              margin-right: 0;
            }

            .el-carousel__button {
              width: 3px;
              height: 3px;
              border-radius: 50%;
              background: #808081;
            }
          }

          .is-active {
            width: 20px;
            border-radius: 8px;
            background: white;

            .el-carousel__button {
              width: 20px;
              border-radius: 2px;
              background: white;
            }
          }
        }
      }
    }

    .right_menu {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 8px;

      .menu_item {
        border-radius: 8px;
        background: #FFF;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-flow: column;
        height: 120px;
        transition: all .3s ease;

        .menu_img {
          width: 38px;
          height: 38px;
        }

        .menu_name {
          font-size: 14px;
          color: #333;
          margin-top: 10px;
        }

        &:hover {
          transform: translateY(-3px);
        }
      }
    }
  }

  .industrial_list_wrapper {
    border-radius: 8px;
    background: #FFF;
    padding: 24px 30px 30px;

    .list_grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 24px 20px;
    }
  }

}
