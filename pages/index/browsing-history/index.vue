<template>
  <div class='container-box browsing-history-box flex_between'>
    <div class='browsing-history-left'>
      <PersonalConLeft :avatarAddress='userInfomation.avatarAddress' :company='userInfomation.company'
                       :department='userInfomation.department' :isAuth='userInfomation.isAuth'
                       :realName='userInfomation.realName' :title='userInfomation.title' />
    </div>
    <div class='browsing-history-right'>
      <BrowseHistoryTab :dataLength='historyData.list.length' @changeTabHandler='changeTabHandler'
                        @deleteAllDataHandler='deleteAllDataHandler'
                        @selectAllHandler='selectAllHandler' />
      <div class='browsing-history-content'>
        <BrowseHistoryItem v-for='item in historyData.list' :key='item.id' :data='item'
                           :label-flag='historyDataType === 0'
                           @deleteDataHandler='deleteDataHandler' />
        <Empty :loading='loading' :no-more='!loading && historyData.list.length === 0' />
        <el-pagination
          v-if='!loading'
          :current-page.sync='currentPage'
          :hide-on-single-page='$store.state.hideOnSinglePage'
          :layout='$store.state.layout'
          :page-size='$store.state.history_count'
          :pager-count='$store.state.pager_count'
          :total='historyData.page ? historyData.page.totalCount : 0'
          background
          small
          style='text-align: center;margin-top: 8px'
          @current-change='handleCurrentChange'
        />
      </div>
    </div>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import ShortVideoPlayback from "../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import PersonalConLeft from '@/components/PageComponents/page/PersonalConLeft/PersonalConLeft'
import { userInfo } from '@/api/user'
import BrowseHistoryTab from '@/components/PageComponents/page/BrowseHistoryTab/BrowseHistoryTab'
import BrowseHistoryItem from '@/components/PageComponents/page/BrowseHistoryItem/BrowseHistoryItem'
import { deleteBrowsingHistory, getBrowsingHistory } from '@/api/browsing-history'
import Empty from '@/components/UI/Empty/Empty'

export default {
  name: 'BrowsingHistory',
  components: {
    ShortVideoPlayback,
    Empty,
    BrowseHistoryItem,
    BrowseHistoryTab,
    PersonalConLeft
  },
  async asyncData({ app, params, error, store, query, req }) {
    app.head.title = '浏览历史'
    const [request1, request2] = await Promise.all([
      /**
       * request1 用户信息
       * request2 获取浏览历史
       */
      app.$axios.$request(userInfo()),
      app.$axios.$request(getBrowsingHistory({
        userId: store.state.auth.user.id,
        contentType: 0,
        search: null,
        pageNo: 1,
        pageSize: store.state.history_count
      }))
    ])


    return {
      userInfomation: request1.result,
      historyData: request2
    }

  },
  head() {
    return {
      title: '浏览历史'
    }
  },
  watch: {
    historyData(newData) {
      const delMap = this.$store.state.history.delMap
      if (Object.keys(delMap).length > 0) {
        var compareObj = {}
        var num = 0
        newData.list.forEach(item => {
          for (let i in delMap) {
            if (parseInt(item.id) === parseInt(i)) {
              num++
            }
          }
        })
        if (num === newData.list.length) {
          this.$store.commit('history/setSelectAll', true)
        } else {
          this.$store.commit('history/setSelectAll', false)
        }
      }
    },
    '$store.state.history.delMap'(newData) {
      if (Object.keys(newData).length > 0) {
        var compareObj = {}
        var num = 0
        this.historyData.list.forEach(item => {
          for (let i in newData) {
            if (parseInt(item.id) === parseInt(i)) {
              num++
            }
          }
        })
        if (num === this.historyData.list.length) {
          this.$store.commit('history/setSelectAll', true)
        } else {
          this.$store.commit('history/setSelectAll', false)
        }
      }
    }
  },
  data() {
    return {
      loading: null,
      currentPage: 1,      // 分页页码
      userInfomation: {},
      historyData: {},
      historyDataType: 0, // 数据类型 默认0全部
      historyPageNo: 1     // 默认分页
    }
  },
  mounted() {
    this.$store.commit('history/setBatchIsEnableFn', false)
    this.$store.commit('history/collapse', { deleteAll: true })
  },
  methods: {
    /**
     * 全选历史记录
     * @param params
     */
    selectAllHandler(item) {
      if (item.selectAllFlag) {
        this.historyData?.list.map((item) => {
          this.$store.commit('history/expand', { that: this, data: item.id })
        })
      } else {
        this.historyData?.list.map((item) => {
          this.$store.commit('history/collapse', { that: this, data: item.id })
        })
      }

    },

    // 获取浏览历史数据
    getHistoryDataFn(params = { contentType: 0, pageNo: 1 }) {
      this.$axios.$request(getBrowsingHistory({
        userId: this.$store.state.auth.user.id,
        contentType: params.contentType,
        search: null,
        pageNo: params.pageNo,
        pageSize: this.$store.state.history_count
      })).then(res => {
        this.loading = false
        this.historyData = res
      })
    },
    // 删除浏览历史数据
    deleteHistoryDataFn(params = {}) {
      const deleteIdLength = typeof params.id === 'string' ? params.id.split(',').length : null
      if (this.historyPageNo !== 1) {
        deleteIdLength && deleteIdLength >= this.historyData.list.length ?
          this.historyPageNo = this.historyPageNo - 1
          :
          null
      }
      this.$axios.$request(deleteBrowsingHistory({
        userId: this.$store.state.auth.user.id,
        browsingHistoryId: params.id
      })).then(res => {
        this.getHistoryDataFn({ contentType: this.historyDataType, pageNo: this.historyPageNo })
        params.callback(true)
      })
    },
    // 切换浏览历史类型
    changeTabHandler(id) {
      this.loading = true
      this.historyDataType = id // 记录当前类型
      this.currentPage = 1   // 切换类型 分页为1
      this.historyData = { list: [] }
      this.getHistoryDataFn({ contentType: id, pageNo: 1 })
    },
    /**
     * 翻页
     * @param pageNo
     */
    handleCurrentChange(pageNo) {
      this.$store.commit('history/selectNumHandler', pageNo)
      this.$tool.scrollIntoTop()
      this.$store.commit('history/setSelectAll', false) // 翻页后 可以重新全选
      this.historyPageNo = pageNo // 保存当前分页
      this.getHistoryDataFn({ contentType: this.historyDataType, pageNo })
    },
    /**
     * 删除浏览记录
     */
    async deleteDataHandler(id, callback) {
      await this.deleteHistoryDataFn({ id, callback })
    },
    /**
     * 删除全部浏览记录
     */
    deleteAllDataHandler(param, callback) {
      let deleId = ''
      for (let key in this.$store.state.history.delMap) {
        deleId += ',' + key
      }
      deleId = deleId.slice(1) // 要删除的id

      this.deleteHistoryDataFn({ id: deleId, callback })

    }
  }
}
</script>

<style lang='less' scoped>
@import "./index";
</style>
