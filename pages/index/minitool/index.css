.miniToolMain {
  min-width: 1200px;
  background: linear-gradient(to right, white 0%, white calc((100% - 632px) / 2), #eef4f6 calc((100% - 1200px) / 2), #eef4f6 100%);
}
.maskLayer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  z-index: 10000;
  display: none;
  background: rgba(0, 0, 0, 0.25);
}
.maskLayer .promptMessage {
  width: 372px;
  height: 188px;
  background: #ffffff;
  border-radius: 6px;
  margin: 0 auto;
  box-sizing: border-box;
  margin-top: 355px;
  padding-top: 46px;
}
.maskLayer .promptMessage p {
  text-align: center;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}
.maskLayer .promptMessage .btnKnow {
  width: 120px;
  height: 38px;
  margin: 0 auto;
  background: #0581ce;
  border-radius: 6px;
  margin-top: 41px;
  cursor: pointer;
}
.maskLayer .promptMessage .btnKnow p {
  font-weight: 500;
  font-size: 14px;
  text-align: center;
  color: #ffffff;
  line-height: 38px;
}
.maskLayerShow {
  display: block;
}
.bread {
  width: 1200px;
  height: 20px;
  margin: 0 auto;
  margin-top: 20px;
}
.bread .el-breadcrumb ::v-deep .el-breadcrumb__inner {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #0581ce;
}
.big-box {
  width: 1200px;
  margin: 0 auto 0px auto;
  display: flex;
}
.big-box .left {
  width: 284px;
  height: 100%;
}
.big-box .left .el-col {
  width: 100%;
  /* 鼠标悬停时的样式 */
  /* 鼠标移入时显示滚动条 */
}
.big-box .left .el-col /deep/ .el-menu-vertical-demo {
  margin-top: 18px;
  z-index: 666;
  background: #fff !important;
  border-radius: 6px;
  border: none;
}
.big-box .left .el-col /deep/ .el-submenu__title:nth-of-type(1),
.big-box .left .el-col .el-submenu:nth-of-type(1) {
  border-radius: 6px 6px 0 0;
}
.big-box .left .el-col /deep/ .el-submenu__title {
  height: 50px;
  font-family: 'Microsoft YaHei';
  font-weight: 700;
  font-size: 18px;
  line-height: 50px;
}
.big-box .left .el-col /deep/ .el-submenu__title .el-icon-s-order {
  font-weight: 700;
  font-size: 18px;
  color: #202020;
}
.big-box .left .el-col /deep/ .el-submenu {
  border-radius: 6px 6px 0 0;
  background: #fff !important;
}
.big-box .left .el-col /deep/ .el-icon-arrow-down {
  font-size: 14px;
}
.big-box .left .el-col /deep/ .el-icon-arrow-down:before {
  content: '\e6e1' !important;
}
.big-box .left .el-col /deep/ .el-submenu__title:hover .el-icon-arrow-down:before {
  color: #ffffff !important;
  /* 鼠标悬停时的颜色 */
}
.big-box .left .el-col /deep/ .el-menu-item {
  padding-left: 20px !important;
  padding-right: 20px !important;
  font-weight: 400;
  font-size: 16px;
  height: auto;
  color: #333333;
  width: 100%;
  white-space: inherit;
}
.big-box .left .el-col /deep/ .el-menu-item p {
  width: 217px;
  white-space: normal;
}
.big-box .left .el-col /deep/ .el-menu-item-group__title {
  position: absolute;
}
.big-box .left .el-col /deep/ .el-submenu__icon-arrow {
  font-size: 18px;
}
.big-box .left .el-col /deep/ .is-opened {
  background-color: #50789c !important;
}
.big-box .left .el-col /deep/ .is-opened .el-submenu__icon-arrow,
.big-box .left .el-col /deep/ .is-opened .el-icon-s-order,
.big-box .left .el-col /deep/ .is-opened span {
  color: #ffffff !important;
}
.big-box .left .el-col .el-menu-item-group {
  padding: 0;
  max-height: 500px;
  overflow-y: auto;
  /* 隐藏垂直滚动条 */
  overflow-x: hidden;
  /* 显示水平滚动条 */
}
.big-box .left .el-col .el-menu-item-group::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
  transition: overflow-y 0.5s;
  /* 添加过渡效果 */
}
.big-box .left .el-col .el-menu-item-group:hover::-webkit-scrollbar {
  display: block;
  /* Chrome, Safari */
  transition: overflow-y 0.5s;
  /* 添加过渡效果 */
}
.big-box .left .el-col .el-menu-item-group:hover {
  overflow-y: auto;
  /* 显示垂直滚动条 */
  transition: overflow-y 0.5s;
  /* 添加过渡效果 */
}
.big-box .left .el-col .is-active,
.big-box .left .el-col .nuxt-link-exact-active {
  font-weight: 500;
  font-size: 16px;
  color: #0581ce !important;
  background: #effaff;
}
.big-box .left .el-col /deep/ .el-submenu:nth-of-type(9) {
  border-radius: 0 0 6px 6px;
}
.big-box .left .el-col /deep/ .el-submenu:nth-of-type(9) .el-submenu__icon-arrow {
  opacity: 0;
}
.big-box .left .el-col /deep/ .el-submenu:nth-of-type(9) .el-submenu__title {
  border-bottom: none !important;
  border-radius: 0 0 6px 6px;
}
.big-box .record {
  height: 52px;
}
.big-box .record span {
  display: flex;
  height: 24px;
}
.big-box .record span svg {
  margin-top: 18px;
  margin-right: 5px;
}
.big-box .right {
  width: 894px;
  padding-left: 22px;
  margin-left: 0;
}
.big-box .right .headline {
  width: 100%;
}
.big-box .right .headline P {
  font-family: 'Microsoft YaHei';
  font-size: 14px;
  margin-top: 16px;
  color: #999ea4;
}
 /deep/ .el-icon-arrow-right:nth-of-type(3) {
  display: none;
}
.scroll-transition {
  transition: all 0.5s ease;
  /* 这里设置了过渡效果的时间和缓动函数 */
}
 /deep/ .particulars {
  background: #fff;
  border-radius: 8px;
  padding: 0 24px 0 24px;
  margin-bottom: 60px;
}
 /deep/ .particulars .result {
  width: 100% !important;
  height: auto !important;
  margin-top: 32px !important;
}
 /deep/ .particulars .result .grade {
  display: flex;
  justify-content: right;
  color: #0581ce !important;
  text-align: right;
  font-size: 20px;
}
 /deep/ .particulars .result .grade span {
  max-width: 475px;
  text-align: start;
  color: #0581ce !important;
}
 /deep/ .particulars .result .grade span:nth-of-type(2) span {
  font-size: 20px !important;
}
 /deep/ .particulars .result .explain {
  display: flex;
  justify-content: right;
  font-size: 14px !important;
  margin-left: 280px;
}
 /deep/ .particulars .button {
  margin-top: 34px !important;
}
 /deep/ .particulars .save {
  margin-top: 16px !important;
  padding-bottom: 32px;
}
 /deep/ .particulars .save .btn {
  width: 120px !important;
  color: #999ea4 !important;
  font-size: 14px;
}
 /deep/ .particulars .save .btn:hover {
  color: #ffffff !important;
}
 /deep/ .particulars .save .scoreBtn {
  color: #ffffff !important;
}
 /deep/ .openEyes {
  background: #fff !important;
  border-bottom: 0.5px solid #efefef;
  width: 100% !important;
  margin-top: 18px !important;
  padding: 16px 0 16px 0 !important;
}
 /deep/ .openEyes .grade .label {
  margin-top: 16px;
  font-size: 14px !important;
  font-weight: 400;
  color: #676c74;
}
 /deep/ .openEyes .grade .label::after {
  content: '';
  border: 1px solid #cccccc;
  width: 14px;
  height: 14px;
  display: block;
  position: absolute;
  top: 1px;
  background-color: #fff !important;
  left: -19px;
  border-radius: 50px;
}
 /deep/ .openEyes .grade .label::before {
  content: '';
  background-color: #0581ce;
  border-radius: 50%;
  display: block;
  width: 8px;
  height: 8px;
  position: absolute;
  top: 5.32px;
  left: -14.3px;
  opacity: 0;
  z-index: 10;
}
 /deep/ .openEyes p {
  color: #333333 !important;
}
 /deep/ .openEyes .option label p {
  color: #676c74 !important;
  font-size: 14px !important;
}
 /deep/ .explainContent {
  width: 532px;
  display: flex;
}
 /deep/ .explainContent .explainContentResult {
  white-space: nowrap;
  color: #888888 !important;
}
.el-menu-item:hover {
  background-color: #effaff !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  color: #0581ce !important;
}
 /deep/ .el-submenu__title:hover {
  background-color: #50789c !important;
  color: #fff !important;
}
 /deep/ .el-submenu__title {
  color: #202020;
}
/* 定义滚动条样式 */
 /deep/ .big-box .left .el-col .el-menu-item-group::-webkit-scrollbar {
  width: 5px;
  /* 设置滚动条宽度 */
}
 /deep/ .big-box .left .el-col .el-menu-item-group::-webkit-scrollbar-track {
  background-color: #fff !important;
  /* 设置滚动条轨道背景色 */
}
 /deep/ .big-box .left .el-col .el-menu-item-group::-webkit-scrollbar-thumb {
  background-color: #e0e0e0;
  /* 设置滚动条手柄颜色 */
  border-radius: 6px;
  /* 设置滚动条手柄圆角 */
}
/* 鼠标悬停时滚动条手柄颜色 */
 /deep/ .big-box .left .el-col .el-menu-item-group::-webkit-scrollbar-thumb:hover {
  background-color: #fff;
}
.btnBox {
  width: 100%;
  display: flex;
  justify-content: space-around !important;
}
.btnBox .btn-know,
.btnBox .btn-save-score {
  width: 120px;
  height: 38px;
  background: #708aa2;
  border-radius: 6px;
  margin-top: 41px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  text-align: center;
  color: #ffffff !important;
  border: none;
  line-height: 38px;
}
.btnBox .btn-know {
  background: #0581ce;
}
