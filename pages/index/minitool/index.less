.miniToolMain {
  min-width: 1200px;
  background: linear-gradient(
    to right,
    white 0%,
    white calc((100% - 632px) / 2),
    #eef4f6 calc((100% - 1200px) / 2),
    #eef4f6 100%
  );
}

.maskLayer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  z-index: 10000;
  display: none;
  background: rgba(0, 0, 0, 0.25);

  .promptMessage {
    width: 372px;
    height: 188px;
    background: #ffffff;
    border-radius: 6px;
    margin: 0 auto;
    box-sizing: border-box;
    margin-top: 355px;
    padding-top: 46px;
    p {
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
    }
    .btnKnow {
      width: 120px;
      height: 38px;
      margin: 0 auto;
      background: #0581ce;
      border-radius: 6px;
      margin-top: 41px;
      cursor: pointer;
      p {
        font-weight: 500;
        font-size: 14px;
        text-align: center;
        color: #ffffff;
        line-height: 38px;
      }
    }
  }
}

.maskLayerShow {
  display: block;
}

.bread {
  width: 1200px;
  height: 20px;
  margin: 0 auto;
  margin-top: 20px;

  .el-breadcrumb ::v-deep .el-breadcrumb__inner {
    font-family: 'PingFang SC';
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #0581ce;
  }
}

.big-box {
  width: 1200px;
  margin: 0 auto 0px auto;
  display: flex;

  .left {
    width: 284px;
    height: 100%;

    .el-col {
      width: 100%;

      /deep/ .el-menu-vertical-demo {
        margin-top: 18px;
        z-index: 666;
        background: #fff !important;
        border-radius: 6px;
        border: none;
      }

      /deep/ .el-submenu__title:nth-of-type(1),
      .el-submenu:nth-of-type(1) {
        border-radius: 6px 6px 0 0;
      }

      /deep/ .el-submenu__title {
        height: 50px;
        font-family: 'Microsoft YaHei';
        font-weight: 700;
        font-size: 18px;
        line-height: 50px;
        //border-bottom: 2px solid #b7dbf1;
        //background: rgba(5, 129, 206, 0.05) !important;
        .el-icon-s-order {
          font-weight: 700;
          font-size: 18px;
          color: #202020;
        }
      }

      /deep/ span {
        //color: #202020;
      }
      /deep/ .el-submenu {
        border-radius: 6px 6px 0 0;
        background: #fff !important;
      }

      /deep/ .el-icon-arrow-down {
        //color: #676c74 !important;
        font-size: 14px;
      }

      /deep/ .el-icon-arrow-down:before {
        content: '\e6e1' !important;
      }

      /* 鼠标悬停时的样式 */
      /deep/ .el-submenu__title:hover .el-icon-arrow-down:before {
        color: #ffffff !important; /* 鼠标悬停时的颜色 */
      }

      /deep/ .el-menu-item {
        //background: #fbfbfb !important;
        padding-left: 20px !important;
        padding-right: 20px !important;
        font-weight: 400;
        font-size: 16px;
        height: auto;
        color: #333333;
        //border-bottom: 1px solid #eeeeee;
        width: 100%;
        white-space: inherit;
        p {
          width: 217px;
          white-space: normal;
        }
      }

      /deep/ .el-menu-item-group__title {
        position: absolute;
      }

      /deep/ .el-submenu__icon-arrow {
        font-size: 18px;
      }

      /deep/ .is-opened {
        background-color: #50789c !important;
        .el-submenu__icon-arrow,
        .el-icon-s-order,
        span {
          color: #ffffff !important;
        }
      }

      .el-menu-item-group {
        padding: 0;
        //background-color: #fbfbfb;
        max-height: 500px;
        overflow-y: auto; /* 隐藏垂直滚动条 */
        overflow-x: hidden; /* 显示水平滚动条 */
      }

      .el-menu-item-group::-webkit-scrollbar {
        display: none; /* Chrome, Safari */
        transition: overflow-y 0.5s; /* 添加过渡效果 */
      }

      /* 鼠标移入时显示滚动条 */
      .el-menu-item-group:hover::-webkit-scrollbar {
        display: block; /* Chrome, Safari */
        transition: overflow-y 0.5s; /* 添加过渡效果 */
      }

      .el-menu-item-group:hover {
        overflow-y: auto; /* 显示垂直滚动条 */
        transition: overflow-y 0.5s; /* 添加过渡效果 */
      }

      .is-active,
      .nuxt-link-exact-active {
        font-weight: 500;
        font-size: 16px;
        color: #0581ce !important;
        background: #effaff;
      }

      /deep/ .el-submenu:nth-of-type(9) {
        border-radius: 0 0 6px 6px;
        .el-submenu__icon-arrow {
          opacity: 0;
        }
        .el-submenu__title {
          border-bottom: none !important;
          border-radius: 0 0 6px 6px;
        }
      }
    }
  }

  .record {
    height: 52px;
    span {
      display: flex;
      height: 24px;
      svg {
        margin-top: 18px;
        margin-right: 5px;
      }
    }
  }

  .right {
    width: 894px;
    padding-left: 22px;
    //background: #EEF4F6;
    margin-left: 0;
    .headline {
      width: 100%;
      //height: 50px;
      //background: #fbfbfb;
      //border-radius: 6px 6px 0px 0px;

      P {
        font-family: 'Microsoft YaHei';
        font-size: 14px;
        margin-top: 16px;
        color: #999ea4;
        //font-weight: 700;
        //font-size: 20px;
        //color: #333333;
        //line-height: 50px;
        //margin-left: 16px;
      }
    }
  }
}

/deep/ .el-icon-arrow-right:nth-of-type(3) {
  display: none;
}

.scroll-transition {
  transition: all 0.5s ease; /* 这里设置了过渡效果的时间和缓动函数 */
}

/deep/ .particulars {
  background: #fff;
  border-radius: 8px;
  padding: 0 24px 0 24px;
  margin-bottom: 60px;
  .result {
    width: 100% !important;
    height: auto !important;
    margin-top: 32px !important;
    .grade {
      display: flex;
      justify-content: right;
      color: #0581ce !important;
      text-align: right;
      font-size: 20px;
      span {
        max-width: 475px;
        text-align: start;
        color: #0581ce !important;
      }
      span:nth-of-type(2) {
        span {
          font-size: 20px !important;
        }
      }
    }
    .explain {
      display: flex;
      justify-content: right;
      font-size: 14px !important;
      margin-left: 280px;
    }
  }
  .button {
    margin-top: 34px !important;
  }
  .save {
    margin-top: 16px !important;
    padding-bottom: 32px;
    .btn {
      width: 120px !important;
      color: #999ea4 !important;
      font-size: 14px;
    }
    .btn:hover {
      color: #ffffff !important;
    }
    .scoreBtn {
      color: #ffffff !important;
    }
  }
}

/deep/ .openEyes {
  background: #fff !important;
  border-bottom: 0.5px solid #efefef;
  width: 100% !important;
  margin-top: 18px !important;
  padding: 16px 0 16px 0 !important;
  .grade {
    .label {
      margin-top: 16px;
      font-size: 14px !important;
      font-weight: 400;
      color: #676c74;
    }
    .label::after {
      content: '';
      border: 1px solid #cccccc;
      width: 14px;
      height: 14px;
      display: block;
      position: absolute;
      top: 1px;
      background-color: #fff !important;
      left: -19px;
      border-radius: 50px;
    }
    .label::before {
      content: '';
      background-color: #0581ce;
      border-radius: 50%;
      display: block;
      width: 8px;
      height: 8px;
      position: absolute;
      top: 5.32px;
      left: -14.3px;
      opacity: 0;
      z-index: 10;
    }
  }
  p {
    color: #333333 !important;
  }

  .option {
    label {
      p {
        color: #676c74 !important;
        font-size: 14px !important;
      }
    }
  }
}

/deep/ .explainContent {
  width: 532px;
  display: flex;
  .explainContentResult {
    white-space: nowrap;
    color: #888888 !important;
  }
}

.el-menu-item:hover {
  background-color: #effaff !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  color: #0581ce !important;
}

/deep/ .el-submenu__title:hover {
  background-color: #50789c !important;
  color: #fff !important;
}
/deep/ .el-submenu__title {
  color: #202020;
}

/* 定义滚动条样式 */
/deep/.big-box .left .el-col .el-menu-item-group::-webkit-scrollbar {
  width: 5px; /* 设置滚动条宽度 */
}

/deep/.big-box .left .el-col .el-menu-item-group::-webkit-scrollbar-track {
  background-color: #fff !important; /* 设置滚动条轨道背景色 */
}

/deep/ .big-box .left .el-col .el-menu-item-group::-webkit-scrollbar-thumb {
  background-color: #e0e0e0; /* 设置滚动条手柄颜色 */
  border-radius: 6px; /* 设置滚动条手柄圆角 */
}

/* 鼠标悬停时滚动条手柄颜色 */
/deep/
  .big-box
  .left
  .el-col
  .el-menu-item-group::-webkit-scrollbar-thumb:hover {
  background-color: #fff;
}

.btnBox {
  width: 100%;
  display: flex;
  justify-content: space-around !important;

  .btn-know,
  .btn-save-score {
    width: 120px;
    height: 38px;
    background: #708aa2;
    border-radius: 6px;
    margin-top: 41px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
    color: #ffffff !important;
    border: none;
    line-height: 38px;
  }

  .btn-know {
    background: #0581ce;
  }
}
