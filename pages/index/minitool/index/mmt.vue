<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '徒手肌力评定法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '徒手肌力评定法是一种常用的评估肌肉力量的方法，它不需要任何仪器或设备，只需要使用评定者的手来进行测量。在评估过程中，评定者会运用不同的手法和技巧，通过对特定肌肉群的抵抗或推动来评估患者的肌力水平。徒手肌力评定法可以用来评估各个肌群的力量，包括上肢、下肢和核心肌群。评定者会根据被测肌肉的力量水平，将其分为不同的等级或使用定量得分来描述患者的肌力。这种评定法可以帮助评估者更全面地了解患者的肌力状况，为康复计划和治疗方案的制定提供重要参考。徒手肌力评定法简单易行，适用于临床和运动领域中评估肌力的需求。它可以帮助评估者检测肌肉力量的不平衡，监测康复进展和指导康复训练的安排。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Manual Muscle Testing (MMT),手动肌力测试,手动肌力评测,手动肌力测量,手动肌力量表,手动肌力评分法,徒手肌力测试量表,徒手肌力评分法'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 肌肉力量的等级',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5','1-6'],
        content: [
          '0级：完全无肌力，无肌肉收缩，不能活动;',
          '1级：可触及肌肉轻微收缩，但无关节活动;',
          '2级：在消除重力姿势下能做全关节活动范围的运动;',
          '3级：能抗重力做全关节活动范围的运动，但不能抗阻力;',
          '4级：能抗重力和一定的阻力运动;',
          '5级：能抗重力和充分阻力的运动。'
        ],
        grade: ['0级', '1级', '2级', '3级', '4级','5级'],
        show: 'false',
        simpleContent: [
          '0级：完全无肌力，无肌肉收缩，不能活动;',
          '1级：可触及肌肉轻微收缩，但无关节活动;',
          '2级：在消除重力姿势下能做全关节活动范围的运动;',
          '3级：能抗重力做全关节活动范围的运动，但不能抗阻力;',
          '4级：能抗重力和一定的阻力运动;',
          '5级：能抗重力和充分阻力的运动;',
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '徒手肌力评定法')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
