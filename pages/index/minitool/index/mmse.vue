<template>
  <div>
    <Patient></Patient>
    <div class="particulars">
      <!-- 评分详情 -->
      <ScoringDetails></ScoringDetails>
      <!-- 评分内容 -->
      <div @click="getTargetInput" ref="reference">
        <p class="first-title">定向力</p>
        <p class="second-title">
          一、现在是（星期几）（几号）（几月）（什么季节）（哪一年）？
        </p>
        <Options
          v-for="data in diFoOneData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">
          二、现在我们在哪里：（省市）（区或县）（街道或乡）（什么地方）（第几层楼）？
        </p>
        <Options
          v-for="data in diFoTwoData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="first-title">记忆力</p>
        <p class="second-title">
          一、现在我要说三样东西的名称，在我讲完以后，请您重复说一遍。（请仔细说清楚，每样东西一秒钟）。"皮球""国旗""树木"
          请您把这三样东西说一遍（以第一次的答案记分）
        </p>
        <Options
          v-for="data in memoryData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="first-title">注意力和计算力</p>
        <p class="second-title">
          一、请您算一算100减7，然后从所得的数目再减去7，如此一直计算下去，请您将每减一个7后的答案告诉我，直到我说停为止。（若某一答案错误，但下一答案正确，只记一次错误）
        </p>
        <Options
          v-for="data in atCaData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="first-title">回忆能力</p>
        <p class="second-title">
          一、现在请您说出刚才我让您记住的三样东西？"皮球""国旗""树木"
        </p>
        <Options
          v-for="data in reAbData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="first-title">语言能力</p>
        <p class="second-title">一、命名能力</p>
        <p class="second-title">1、（出示手表）这个东西叫什么？</p>
        <Options
          v-for="data in laCoOneData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">2、（出示钢笔）这个东西叫什么？</p>
        <Options
          v-for="data in laCoTwoData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">二、复述能力</p>
        <p class="second-title">
          1、现在我要说一句话，请您跟着我清楚的重复一遍。"四十四只石狮子"
        </p>
        <Options
          v-for="data in laCoThreeData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">三、阅读能力</p>
        <p class="second-title">
          1、请您念一念这句话，并且按它的意思去做。闭上你的眼睛
        </p>
        <Options
          v-for="data in laCoFourData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">四、执行命令能力</p>
        <p class="second-title">
          1、我给您一张纸请您按我说的去做，现在开始："用右手拿着这张纸，用两只手将它对折起来，放在您的大腿上"。（不要重复说明，也不要示范）
        </p>
        <Options
          v-for="data in laCoFiveData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">五、书写能力</p>
        <p class="second-title">1、您给我写一句完整的句子。</p>
        <Options
          v-for="data in laCoSixData"
          :key="data.title"
          :information="data"
        ></Options>
        <p class="second-title">六、结构能力</p>
        <p class="second-title">1、这是一张图，请您在纸上照样画出来</p>
        <Options
          v-for="data in laCoSevenData"
          :key="data.title"
          :information="data"
        ></Options>
      </div>
      <!-- 结果展示 -->
      <div class="result">
        <div class="grade">
          结果：
          <span v-if="selectedSet.size === 0">暂无</span>
          <span v-else>{{ totalPoints }}</span>
        </div>
        <div v-show="unselected !== 0" class="explain">
          已选择
          <span>{{ selectedSet.size }}</span>
          个评分项，尚有
          <span>{{ unselected }}</span>
          个评分项未选择
        </div>
        <div class="explain" style="white-space: pre-wrap">
          <div class="explainContent">
            <span class="explainContentResult">结果说明：</span>
            <p>{{ resultExplain }}</p>
          </div>
        </div>
      </div>
      <!-- 保存评分 -->
      <div class="save">
        <div
          :class="{ scoreBtn: unselected === 0 }"
          @click="saveScore"
          class="btn"
        >
          保存评分
        </div>
      </div>
    </div>
    <PromptPopup
      :type="popupType"
      :show="popupShow"
      @closePopup="closePopup"
      @directSave="directSave"
    ></PromptPopup>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { saveToolScore } from '@/api/minitool'
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import PromptPopup from '@/components/MiniTool/PromptPopup.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options, PromptPopup },
  // 配置TDK
  head() {
    return {
      title: '简易智能精神状态检查量表(MMSE) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            '为了进行脑梗塞病人的血液稀释治疗效果的研究，SSS评分分为预后评分和长期随访评分。最初预后评定项目包括意识水平，眼活动和瘫痪的严重性；随访评分项目包括上下肢和手的肌力，定向力，语言，面瘫和步态。',
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '面瘫, scandinavian stroke scale, 上肢肌力, 手的肌力, 眼球运动, 意识, SSS, SNSS',
        },
      ],
    }
  },
  computed: {
    ...mapState('minitool', ['PatientInformations']),
    // 结果说明
    resultExplain() {
      return '简易智能精神状态检查量表(MMSE)，又称为简易智力状况检查法(mini-mental state examination)，该量表简单易行，国内外广泛应用，是痴呆筛选的首选量表。共30题，每项正确回答得1分，回答错误或者不知道得0分，量表总分范围为0-30分。\n评分参考：\n27-30分：认知功能正常；\n＜27分：认知功能障碍；\n21-26分：轻度认知功能障碍；\n10-20分：中度认知功能障碍；\n0-9分：重度认知功能障碍。'
    },
    diFoOneData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '今天是星期几?',
          id: ['1-1', '1-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '今天是几号?',
          id: ['2-1', '2-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在是几月份?',
          id: ['3-1', '3-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在是什么季节?',
          id: ['4-1', '4-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '今年的年份?',
          id: ['5-1', '5-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    diFoTwoData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在我们在哪里(省、市)?',
          id: ['6-1', '6-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在我们在什么地方(区、县)?',
          id: ['7-1', '7-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在我们在什么街道(乡、村)?',
          id: ['8-1', '8-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '这里是什么地方(地址名称)?',
          id: ['9-1', '9-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '现在我们在第几层楼?',
          id: ['10-1', '10-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    memoryData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '复述：皮球',
          id: ['11-1', '11-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '复述：国旗',
          id: ['12-1', '12-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '复述：树木',
          id: ['13-1', '13-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    atCaData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '100-7=93',
          id: ['14-1', '14-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '93-7=86',
          id: ['15-1', '15-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '86-7=79',
          id: ['16-1', '16-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '79-7=72',
          id: ['17-1', '17-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '72-7=65',
          id: ['18-1', '18-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    reAbData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '回忆：皮球',
          id: ['19-1', '19-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '回忆：国旗',
          id: ['20-1', '20-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '回忆：树木',
          id: ['21-1', '21-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoOneData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '辨认：手表',
          id: ['22-1', '22-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoTwoData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '辨认：钢笔',
          id: ['23-1', '23-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoThreeData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '复述：四十四只石狮子',
          id: ['24-1', '24-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoFourData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '按纸上指令做动作：闭上你的眼睛',
          id: ['25-1', '25-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoFiveData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '按口头指令动作：用右手拿纸',
          id: ['26-1', '26-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '按口头指令动作：将纸对折',
          id: ['27-1', '27-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '按口头指令动作：将纸放在自己大腿上',
          id: ['28-1', '28-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoSixData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '能够写一句完整句子(含主语、谓语、宾语)',
          id: ['29-1', '29-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
    laCoSevenData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '这是一张图，请您在纸上照样画出来',
          id: ['30-1', '30-2'],
          content: ['1) 正确;', '2) 错误;'],
          grade: ['1', '0'],
          simpleContent: ['正确;', '错误;'],
        },
      ]
    },
  },
  data() {
    return {
      selectedSet: new Set(),
      selectDetails: {},
      totalPoints: 0,
      unselected: 30,
      resultString: '',
      popupType: '0',
      popupShow: false,
      selectOptions: {},
    }
  },

  beforeMount() {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '简易智能精神状态检查量表(MMSE)')
  },

  mounted() {
    const userSelected = JSON.parse(localStorage.getItem('userSelected'))
    if (userSelected) {
      Object.entries(userSelected).forEach(([key, { id }]) => {
        const selectedInput = document.getElementById(id)
        if (selectedInput) {
          selectedInput.checked = true
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
          })
          selectedInput.dispatchEvent(clickEvent)
        }
      })
      localStorage.removeItem('userSelected')
    }
  },

  methods: {
    ...mapMutations('minitool', ['setPatientInformations']),
    getTargetInput(event) {
      if (event.target.nodeName !== 'INPUT') return

      const { target } = event
      const { attributes } = target
      const title = target.name
      const id = target.id.split('-')[0]
      const grade = attributes.grade.value
      const option = attributes.title.value
      this.selectOptions[id] = { id: target.id, type: 'radio', value: true }

      this.selectedSet.add(id)
      this.selectDetails[id] = { title, option, grade }
      this.unselected = 30 - this.selectedSet.size
      if (this.unselected === 0) {
        this.calculateScore()
      }
    },
    calculateScore() {
      let allScore = 0
      this.totalPoints = 0
      this.resultString = ''
      for (const key in this.selectDetails) {
        const { title, option, grade } = this.selectDetails[key]
        let result = ''
        allScore += Number(grade)
        if (allScore >= 27) {
          result = '认知功能正常'
        } else if (allScore >= 21) {
          result = '轻度认知功能障碍'
        } else if (allScore >= 10) {
          result = '中度认知功能障碍'
        } else if (allScore >= 0) {
          result = '重度认知功能障碍'
        }
        this.totalPoints = result
        this.resultString += `{${title}:${option},分数${grade}}`
      }
    },
    // 保存评分
    saveScore(event) {
      if (!event.target.classList.contains('scoreBtn')) return
      const validationResult = this.validateInputs()
      if (validationResult) {
        this.popupType = validationResult
        this.popupShow = true
      }
      const {
        age,
        gender,
        diagnose: diagnosis,
        name,
      } = this.PatientInformations
      const params = {
        age: age !== '未知' ? age : '',
        gender: gender !== '未知' ? gender : '',
        diagnosis,
        name,
        scoreResult: this.totalPoints,
        scoreTitle: '简易智能精神状态检查量表(MMSE)',
        scoreDetail: this.resultString,
        scoreType: '',
        userId: this.$cookies.get('medtion_user_only_sign')?.id,
      }
      this.$axios.$request(saveToolScore(params)).then((res) => {
        if (res.code === 1) {
          this.$router.push('/minitool/page_save')
        }
      })
    },

    closePopup() {
      this.popupShow = false
    },

    directSave() {
      this.popupShow = false
      const { age, gender, diagnose, name } = this.PatientInformations
      this.setPatientInformations({
        age: age || '未知',
        name: name || '未知',
        gender: gender || '未知',
        diagnose: diagnose || '未知',
      })
    },

    // 统一验证用户信息
    validateInputs() {
      if (!this.checkPatientInformation()) return '0' // 信息不完整
      if (!this.checkAge()) return '1' // 年龄不正确
      if (!this.checkUnselected()) return '2' // 有未完成的评分项
      if (!this.checkLogin()) {
        this.savePatientInformation()
        return '3'
      } // 未登录
      return null // 所有检查通过
    },

    // 将用户信息暂时保存本地
    savePatientInformation() {
      localStorage.setItem('userSelected', JSON.stringify(this.selectOptions))
    },

    // 判断用户信息是否填写完整
    checkPatientInformation() {
      return Object.values(this.PatientInformations).every(
        (value) => value !== ''
      )
    },

    // 判断用户年龄是否正确
    checkAge() {
      const age = this.PatientInformations.age
      if (age === '未知') {
        return true
      } else {
        return !isNaN(Number(age)) && Number(age) > 0
      }
    },

    // 判断是否还有未完成的评分项
    checkUnselected() {
      return this.unselected === 0
    },

    // 判断是否登录
    checkLogin() {
      return this.$cookies.get('medtion_isLogged_only_sign') !== undefined
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.first-title {
  width: 100%;
  color: #333;
  margin-top: 30px;
  font-weight: 600;
  text-align: center;
}

.second-title {
  color: #333;
  margin-top: 20px;
  font-weight: 600;
}
</style>
