.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  background: white;
  .button {
    display: flex;
    width: 228px;
    height: 33px;
    margin-top: 18px;
    cursor: pointer;
    justify-content: space-between;

    div {
      width: 84px;
      height: 33px;
      line-height: 33px;
      text-align: center;
      background: #F4F6F8;
      border-radius: 6px;
      color: #666666;
    }

    .change-button {
      background: #EFFAFF;
      color: #0581CE;
    }
  }

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;
    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }
    input {
      border: none;
    }
  }
  .btn:hover{
    background-color: #0581CE;
    color: #FFFFFF;
  }
  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

// .maskLayer{
//   width: 100%;
//   height: 100px;
//   float: left;
//   background: rgba(0, 0, 0, 0.25);
// }
