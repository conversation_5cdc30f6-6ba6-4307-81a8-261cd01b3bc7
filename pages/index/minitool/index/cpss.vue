<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="FacialDroop"></Options>
          <Options :information="UpperLimbDrift"></Options>
          <Options :information="speech"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{scoreBtn: unselected === 0}" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue';
import Options from '@/components/MiniTool/Options.vue';
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue';

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '辛辛那提院前卒中量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '辛辛那提院前脑卒中识别评分量表(CPSS)在院前急救中对可疑脑卒中患者的快速筛选价值。使用 CPSS 对急诊可疑脑卒中患者进行筛选均有显著统计学意义(P0.05)，急诊医师在院前急救中使用 CPSS 对可疑脑卒中患者进行筛选有一定价值。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'CPSS, 心血管并发症, 取栓术后血流动力学, 神经系统检查, 脑卒中分类, 院前卒中评估工具, cincinnati prehospital stroke scale, 卒中评估量表, 识别疑似卒中患者'
        }
      ]
    }
  },
  data () {
    return {
      // 1.面部下垂
      FacialDroop: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 200px;',
        title: '1. 面部下垂',
        id: ['1-1', '1-2'],
        content: ['1) 正常：两侧面部运动对称;',
          '2) 异常：一侧面运动不如另一侧。'],
        grade: ['正常', '异常'],
        show: 'false',
        simpleContent: ['正常：两侧面部运动对称;',
          '异常：一侧面运动不如另一侧;']
      },
      // 2.上肢漂移
      UpperLimbDrift: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 200px;',
        title: '2. 上肢漂移',
        id: ['2-1', '2-2'],
        content: ['1) 正常：两上肢运动一致或无运动;',
          '2) 异常：与另一侧肢体相比，一侧上肢不活动或向下漂移。'],
        grade: ['正常', '异常'],
        show: 'false',
        simpleContent: ['正常：两上肢运动一致或无运动;',
          '异常：与另一侧肢体相比，一侧上肢不活动或向下漂移;']
      },
      // 3.言语
      speech: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 200px;',
        title: '3. 言语',
        id: ['3-1', '3-2'],
        content: ['1) 正常：用词正确，发音不含糊;',
          '2) 异常：用词错误，发音含糊或不能讲侧。'],
        grade: ['正常', '异常'],
        show: 'false',
        simpleContent: ['正常：用词正确，发音不含糊;',
          '异常：用词错误，发音含糊或不能讲侧;']
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选个数
      unselected: 3,
      choiceBox: [],
      result: [],
    }
  },

  beforeMount() {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', '辛辛那提院前卒中量表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (let j = 0; j < this.$refs.reference.children[i].children[0].children[3].children.length; j++) {
        this.choiceBox.push(this.$refs.reference.children[i].children[0].children[3].children[j].children[0])
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 登录之后 从本地拿去数据 勾选选项
    this.choiceBox.forEach(element => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
      }
      // 数组中有没有被选中元素的grade值
      if (element.checked === true && this.result.indexOf(element.attributes.grade.value) === -1) {
        // 没有 push
        this.result.push(element.attributes.grade.value);
        // 判断值
        if (this.result.indexOf('异常') !== -1) {
          // 赋值
          this.totalPoints = '强烈提示脑卒中';
        } else {
          this.totalPoints = '脑卒中可能性小';
        }
      } else if (this.result.indexOf('异常') !== -1) {
        this.totalPoints = '强烈提示脑卒中';
      }
    },
    getTatals (e) {
      // 每次点击先清空数组
      this.result = [];
      this.selected = 0;
      this.unselected = 3;
      // 选项的状态
      let optionMessage = []
      // 循环
      this.choiceBox.forEach(element => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }

    },
  }
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #FFFFFF;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>