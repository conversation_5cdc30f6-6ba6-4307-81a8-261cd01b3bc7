<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in DVT" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div style="white-space: pre-wrap" class="explain">{{ resultExplain }}</div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '手术风险度POSSUM评分标准：Wells评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '与DVT表现类似的其他疾病包括：肌肉伤、慢性水肿、浅静脉炎、血栓后综合征、关节类、静脉功能不全、蜂窝组织炎、脑窝囊肿、骨盆肿瘤、术后肿胀、多种湿杂因素。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '深静脉血栓, DVT, wells评分'
        }
      ]
    }
  },
  data () {
    return {
      DVT: [
        // 1.年龄
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 619px;',
          title: '1. 年龄',
          id: ['1-1', '1-2', '1-3', '1-4'],
          content: [
            '1) <=60;',
            '2) 61-70;',
            '3) 71-85;',
            '4) >=85。',
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['<=60;', '61-70;', '71-85;', '>=85;']
        },
        // 2.心脏征象
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '2. 心脏征象',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1) 正常的;',
            '2) 服用利尿剂、降压药、心绞痛药物;',
            '3) 周围性水肿、华法令治疗;',
            '4) 颈静脉压增高。',
          ],
          grade: ['1', '2', '4', '5'],
          simpleContent: ['正常的;', '服用利尿剂、降压药、心绞痛药物;', '周围性水肿、华法令治疗;', '颈静脉压增高;']
        },
        // 3.呼吸系统
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '3. 呼吸系统',
          id: ['3-1', '3-2', '3-3', '3-4'],
          content: [
            '1) 无气促;',
            '2) 运动时气促COPD轻度;',
            '3) 登高时气促，COPD中度;',
            '4) 休息时气促肺纤维化或实变。',
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['无气促;', '运动时气促COPD轻度;', '登高时气促，COPD中度;', '休息时气促肺纤维化或实变;']
        },
        // 4.收缩压
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '4. 收缩压',
          id: ['4-1', '4-2', '4-3', '4-4'],
          content: [
            '1) 110-130;',
            '2) 100-109或131-170;',
            '3) 90-99或>=170;',
            '4) <=89。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['110-130;', '100-109或131-170;', '90-99或>=170;', '<=89;']
        },
        // 5.胸部受伤
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '5. 胸部受伤',
          id: ['5-1', '5-2', '5-3', '5-4'],
          content: [
            '1) 50-80;',
            '2) 81-140或40-49;',
            '3) 101-120;',
            '4) >121或<=39。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['50-80;', '81-140或40-49;','101-120;','>121或<=39']
        },
        // 6.Glasgow昏迷评分
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '6. Glasgow昏迷评分',
          id: ['6-1', '6-2', '6-3', '6-4'],
          content: [
            '1) 15;',
            '2) 12.0-14.0;',
            '3) 9.0-11.0;',
            '4) <=8.0。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['15;', '12.0-14.0;','9.0-11.0','<=8.0']
        },
        // 7.血红蛋白
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '7. 血红蛋白',
          id: ['7-1', '7-2', '7-3', '7-4'],
          content: [
            '1) 13-16;',
            '2) 11.5-12.9或16.1-17.0;',
            '3) 10.1-11.4或17.1-18.0;',
            '4) <=9.9或>=18.1。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['13-16;', '11.5-12.9或16.1-17.0;', '10.1-11.4或17.1-18.0;', '<=9.9或>=18.1;']
        },
        // 8.白细胞
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '8. 白细胞',
          id: ['8-1', '8-2','8-3'],
          content: [
            '1) 4-10;',
            '2) 3.1-4.0或10.1-20.0;',
            '3) <=3.0或>=20.1。'
          ],
          grade: ['1', '2', '4'],
          simpleContent: ['4-10;', '3.1-4.0或10.1-20.0;','<=3.0或>=20.1']
        },
        // 9.血清尿素
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '9. 血清尿素',
          id: ['9-1', '9-2','9-3', '9-4'],
          content:[
            '1) 13-16;',
            '2) 11.5-12.9或16.1-17.0;',
            '3) 10.1-11.4或17.1-18.0;',
            '4) <=9.9或>=18.1。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['13-16;', '11.5-12.9或16.1-17.0;','10.1-11.4或17.1-18.0;','<=9.9或>=18.1;']
        },
        // 10.血清钠
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '10. 血清钠',
          id: ['10-1', '10-2','10-3', '10-4'],
          content: [
            '1) >136;',
            '2) 131-135;',
            '3) 126-130;',
            '4) <=125。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['>136;', '131-135;', '126-130;','<=125;']
        },
        // 11.血清钾
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '11. 血清钾',
          id: ['11-1', '11-2','11-3', '11-4'],
          content: [
            '1) 3.5-5.0;',
            '2) 3.2-3.4或5.1-5.3;',
            '3) 2.9-3.1或5.4-5.9;',
            '4) <=2.8或>=6.0。'
          ],
          grade: ['1', '2','4','8'],
          simpleContent: ['3.5-5.0;', '3.2-3.4或5.1-5.3;', '2.9-3.1或5.4-5.9;','<=2.8或>=6.0;']
        },
        // 12.心电图
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '12. 心电图',
          id: ['12-1', '12-2','12-3', '12-4'],
          content: [
            '1) 正常;',
            '2) 房颤，心率60－90>=5次/分，Q波ST/T波异常;',
            '3) 异常心律，早搏>=5次/分，Q波ST/T波异常。',
          ],
          grade: ['1', '4', '8'],
          simpleContent: ['正常;', '房颤，心率60－90>=5次/分，Q波ST/T波异常;', '异常心律，早搏>=5次/分，Q波ST/T波异常;']
        },
        // 13.手术范围
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '13. 手术范围',
          id: ['13-1', '13-2','13-3', '13-4'],
          content: [
            '1) 小手术;',
            '2) 中手术;',
            '3) 大手术;',
            '4) 特大手术。',
          ],
          grade: ['1', '2','4','8'],
          simpleContent: ['小手术;', '中手术;', '大手术;', '特大手术;']
        },
        // 14.30天内手术次数
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '14. 30天内手术次数',
          id: ['14-1', '14-2', '14-3', '14-4'],
          content: [
            '1) 1;',
            '2) 2;',
            '3) 3;',
            '4) 4。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['1;', '2;', '3;', '4;']
        },
        // 15.脑血管疾病
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '15. 脑血管疾病',
          id: ['15-1', '15-2', '15-3', '15-4'],
          content: [
            '1) <100;',
            '2) 101-500;',
            '3) 501-999;',
            '4) >=1000。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['<100;', '101-500;', '501-999;', '>=1000;']
        },
        // 16.腹腔污染
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '16. 腹腔污染',
          id: ['16-1', '16-2', '16-3', '16-4'],
          content: [
            '1) 无;',
            '2) 血清血(<250mL);',
            '3) 局部脓肿;',
            '4) 游离肠内容物、脓及血。'
          ],
          grade: ['1', '2', '4', '8'],
          simpleContent: ['无;', '血清血(<250mL);', '局部脓肿;', '游离肠内容物、脓及血']
        },
        // 17.恶性肿瘤
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '17. 恶性肿瘤',
          id: ['17-1', '17-2', '17-3', '17-4'],
          content: [
            '1) 无;',
            '2) 仅单发灶;',
            '3) >伴淋巴结转移;',
            '4) 伴远处转移。'
          ],
          grade: ['0', '2', '4', '8'],
          simpleContent: ['无;', '仅单发灶;', '>伴淋巴结转移;', '伴远处转移;']
        },
        // 18.手术类别
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '18. 手术类别',
          id: ['18-1', '18-2', '18-3'],
          content: [
            '1) 择期手术;',
            '2) 急症、可等待2小时以上;',
            '3) 需在2小时内手术。',
          ],
          grade: ['1', '4','8'],
          simpleContent: ['择期手术;', '急症、可等待2小时以上;','需在2小时内手术']
        },
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 18,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return '结果说明：①高风险人群入院24小时内，手术后患者即刻完成；\n                 ②≥15分者根据活动内容的改变及时评估（至少每三天一次）；\n                 ③＜14分者每周评估一次。'
    },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints <= 10) {
        return `${this.totalPoints}分，低风险`
      } else if (this.totalPoints > 10 && this.totalPoints <= 14) {
        return `${this.totalPoints}分，中风险`
      } else if (this.totalPoints >= 15) {
        return `${this.totalPoints}分，高风险`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用')
    localStorage.setItem('wordLast', '手术风险度POSSUM评分标准')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 18
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
