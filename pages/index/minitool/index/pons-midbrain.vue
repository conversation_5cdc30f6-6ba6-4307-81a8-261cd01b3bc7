<template>
  <div>
    <form action="">
      <Patient :parameter="rewritePatient"></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <div @click="getTatals($event)" ref="reference">
          <Options :information="firstTopic"></Options>
          <Options :information="secondTopic"></Options>
          <Options :information="thirdlyTopic"></Options>
          <Options :information="sixthTopicthlyTopic"></Options>

        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '桥脑中脑指数 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '桥脑中脑指数（MIDAS）是一种用于评估中脑功能的量表，主要用于判断脑干患者的预后和预测意识恢复的可能性。桥脑中脑指数通过评估瞳孔反应、眼球运动、脸部动作、四肢动作和呼吸模式来进行评分。评估中，医护人员会观察患者的瞳孔是否对光刺激有反应，眼球是否能在水平和垂直方向上自由运动，脸部是否能自主表达情绪和运动，四肢是否能做出适当的动作以及呼吸模式是否正常。每个项目都会根据患者的表现进行评分，最高总分为10分。MIDAS评分可以提供有关患者中脑功能的重要信息，帮助医护人员判断其意识恢复的可能性。较高的得分意味着患者中脑功能较好，可能更有机会恢复意识。这一量表在临床中有助于预测患者的预后，并指导医护人员进行个性化的康复计划。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑桥中脑指数量表,脑桥中脑灵活指动指量表,脑桥中脑指运动能力指标表,脑桥中脑指数量表,上中脑指数评估,上中脑指数量表,上下中脑指数评定表,上、下中脑量表指数，桥脑–中脑指数评价量表,上下脑干指数评分表'
        }
      ]
    }
  },
  data () {
    return {
      // 1.左侧半脑桥
      firstTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        leftOne: 'left: 108px',
        leftTwo: 'left: 120px',
        title: '1.左侧半脑桥',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '没有低密度：0分;',
          '低密度面积小于50%：1分',
          '低密度面积大于50%：2分。',
        ],
        grade: ['0', '1', '2'],
        explainShow: false,
        simpleContent: [
          '没有低密度;',
          '低密度面积小于50%;',
          '低密度面积大于50%;'
        ],
      },
      // 2.右侧半脑桥
      secondTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '2.语右侧半脑桥',
        id: ['2-1', '2-2', '2-3'],
        content: [
          '没有低密度：0分;',
          '低密度面积小于50%：1分;',
          '低密度面积大于50%：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 142px',
        leftTwo: 'left: 154px',
        explainShow: false,
        simpleContent: ['没有低密度;', '低密度面积小于50%;', '低密度面积大于50%;'],
      },
      // 3.左侧半中脑。
      thirdlyTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '3.左侧半中脑',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '没有低密度：0分;',
          '低密度面积小于50%：1分;',
          '低密度面积大于50%：2分。'
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 140px',
        leftTwo: 'left: 152px',
        explainShow: false,
        simpleContent: ['没有低密度;', '低密度面积小于50%;', '低密度面积大于50%;'],
      },
      // 4.右侧半中脑
      sixthTopicthlyTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '4. 右侧半中脑',
        id: ['4-1', '4-2', '4-3'],
        content: [
          '没有低密度：0分;',
          '低密度面积小于50%：1分;',
          '低密度面积大于50%：2分。'
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 71px',
        leftTwo: 'left: 79px',
        explainShow: false,
        simpleContent: [
          '没有低密度;',
          '低密度面积小于50%;',
          '低密度面积大于50%;'
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 4,
      choiceBox: [],
      // 去评分时患者信息
      rewritePatient: '',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '桥脑中脑指数')
  },

  mounted () {
    if (localStorage.getItem('associatedScore') === 'true') {
      this.$store.commit('minitool/setRelevanceSkip', true)
      this.rewritePatient = this.$store.state.minitool.PatientInformations
      this.$store.commit(
        'minitool/setAssociatedScore',
        localStorage.getItem('associatedScore')
      )
    }
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('relevanceScore') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('relevanceScore'))[
          index
          ].choice
      })
    }

    // 根据第一题选项选其他题目
    this.relevance()
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    relevance () {
      this.choiceBox.map((item) => {
        if (this.choiceBox[3].checked === true) {
          switch (item.id) {
            case '2-3':
              item.checked = true
              break
            case '3-3':
              item.checked = true
              break
            case '6-4':
              item.checked = true
              break
            case '7-5':
              item.checked = true
              break
            case '8-5':
              item.checked = true
              break
            case '9-5':
              item.checked = true
              break
            case '10-5':
              item.checked = true
              break
            case '11-1':
              item.checked = true
              break
            case '12-3':
              item.checked = true
              break
            case '13-4':
              item.checked = true
              break
            case '14-3':
              item.checked = true
              break
            case '15-3':
              item.checked = true
              break
            default:
              break
          }
        }
      })
    },
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 根据第一题选项选其他题目
      this.relevance()
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 4
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      // nihss 的选中状态 存入特殊的变量里 防止关联评分时混乱
      localStorage.setItem('relevanceScore', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
          score: this.totalPoints,
          path: this.$route.query.fromPath,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }

    .bin:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
