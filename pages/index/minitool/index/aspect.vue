<template>
  <div @click="exhibition">
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 切换按钮 -->
        <div class="button">
          <div @click="changeBtnOne" :class="{ 'change-button': btnClass === false }">
            前循环
          </div>
          <div @click="changeBtnTwo" :class="{ 'change-button': btnClass === true }">
            后循环
          </div>
        </div>
        <!-- 前循环 评分内容 -->
        <div @click="getTatalsOne($event)" class="matter" v-show="show === true" ref="referenceOne">
          <MultipleChoice :information="subcorticalStructures"></MultipleChoice>
          <MultipleChoice :information="MCA"></MultipleChoice>
        </div>
        <!-- 后循环 评分内容 -->
        <div @click="getTatalsTwo($event)" class="matter" v-show="show === false" ref="referenceTwo">
          <MultipleChoice :information="area"></MultipleChoice>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>ASPECTS评分=10-所有10个分区总分（早期缺血改变每累及一个区域评分减1分；最低分0分，最高分10分，得分越高，预后越好；10项评分总分为10分，0分提示弥漫性缺血累及整个大脑中动脉。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, MultipleChoice, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'ASPECTS评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'ASPECT评分，是指急性前循环卒中的标准CT评分系统。0分提示弥漫性缺血，累及了整个大脑中动脉，评分>7分时提示病人三个月后很有希望独立生活，而评分≤7时提示病人不能独立生活或死亡的可能性大。如果溶栓以后评分≤7分，则其出血的危险性是评分>7分患者的14倍，所以ASPECT的评分方法主要用来评测病人的预后。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'aspects评分, 脑卒中临床评估量表, 前循环ASPECT评分, 后循环ASPECT评分, 皮层下结构, 大脑中动脉皮层, EVT, AIS, 缺血'
        }
      ]
    }
  },
  data () {
    return {
      // 皮层下结构区域
      subcorticalStructures: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title: '1. 皮层下结构区域',
        id: ['1-1', '1-2', '1-3'],
        content: ['(1) 尾状核(C)', '(2) 豆状核(L)', '(3) 内囊(IC)'],
        grade: ['1', '1', '1'],
        simpleContent: ['尾状核(C);', '豆状核(L);', '内囊(IC);'],
        subTitle: '前循环',
      },
      // 大脑中动脉皮层
      MCA: {
        bgColor: 'background: #FBFBFB;',
        title: '2. 大脑中动脉皮层',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5', '2-6', '2-7'],
        content: [
          '(1) 大脑中动脉前皮质区(M1)',
          '(2) 岛叶皮质(I)',
          '(3) 大脑中动脉岛叶外侧皮质区(M2)',
          '(4) 大脑中动脉后皮层区(M3)',
          '(5) M1 上方的大脑中动脉皮层(M4)',
          '(6) M2 上方的大脑中动脉皮层(M5)',
          '(7) M3 上方的大脑中动脉皮层(M6)',
        ],
        simpleContent: [
          '大脑中动脉前皮质区(M1);',
          '岛叶皮质(I);',
          '大脑中动脉岛叶外侧皮质区(M2);',
          '大脑中动脉后皮层区(M3);',
          'M1 上方的大脑中动脉皮层(M4);',
          'M2 上方的大脑中动脉皮层(M5);',
          'M3 上方的大脑中动脉皮层(M6);',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '1'],
        subTitle: '前循环',
      },
      // 后循环 区域
      area: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title: '1. 区域',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5', '3-6', '3-7', '3-8'],
        content: [
          '脑桥任何部位',
          '中脑任何部位',
          '左侧小脑',
          '右侧小脑',
          '左侧丘脑',
          '右侧丘脑',
          '左侧大脑后动脉供血区',
          '右侧大脑后动脉供血区',
        ],
        simpleContent: [
          '脑桥任何部位;',
          '中脑任何部位;',
          '左侧小脑;',
          '右侧小脑;',
          '左侧丘脑;',
          '右侧丘脑;',
          '左侧大脑后动脉供血区;',
          '右侧大脑后动脉供血区;',
        ],
        grade: ['2', '2', '1', '1', '1', '1', '1', '1'],
        subTitle: '后循环',
      },
      explain: false,
      // 总分数
      totalPoints: 10,
      // 选中的个数
      selected: 0,
      // 存储获取到的 input 框
      anteriorCirculation: [],
      posteriorCirculation: [],
      // 未选中的个数
      unselected: 2,
      // 判断评分项选中的个数
      choiceNumber: [],
      // 按钮样式
      btnClass: false,
      // 前后循环 切换
      show: true,
      frontcirculation: [],
      backcirculation: []
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', 'ASPECTS评分')
  },

  mounted () {
    if (localStorage.getItem('switchover') === null) {
      localStorage.setItem('switchover', '前循环')
    }

    if (localStorage.getItem('switchover') === '前循环') {
      this.btnClass = false
      this.show = true
    } else {
      this.btnClass = true
      this.show = false
    }
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.referenceOne.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceOne.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.anteriorCirculation.push(
          this.$refs.referenceOne.children[i].children[0].children[1].children[
            j
          ].children[0]
        )
      }
    }
    // 获取到 后循环 所有的 input 框
    for (let i = 0; i < this.$refs.referenceTwo.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceTwo.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.posteriorCirculation.push(
          this.$refs.referenceTwo.children[i].children[0].children[1].children[
            j
          ].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.anteriorCirculation.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
      this.posteriorCirculation.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.anteriorCirculation.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.frontChecked(element)
    })
    this.posteriorCirculation.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.backChecked(element)
    })
  },
  methods: {
    // 前循环 公共逻辑
    frontChecked (element) {
      if (element.checked === true) {
        // 分数减减
        this.totalPoints -= element.attributes.grade.value * 1
        // 判断数组中是否含有某个字符，有 跳出，无 push 进去
        if (this.choiceNumber.indexOf(element.id.slice(0, 1)) === -1) {
          this.choiceNumber.push(element.id.slice(0, 1))
          // 选中的个数 等于 数组的长度
          this.selected = this.choiceNumber.length
          // 未选中的个数
          this.unselected = 2 - this.selected
        } else {
          this.selected = this.choiceNumber.length
          this.unselected = 2 - this.selected
        }
      }
    },
    // 后循环 公共逻辑
    backChecked (element) {
      if (element.checked === true) {
        // 分数减减
        this.totalPoints -= element.attributes.grade.value * 1
        // 判断数组中是否含有某个字符，有 跳出，无 push 进去
        if (this.choiceNumber.indexOf(element.id.slice(0, 1)) === -1) {
          this.choiceNumber.push(element.id.slice(0, 1))
          // 选中的个数 等于 数组的长度
          this.selected = this.choiceNumber.length
          // 未选中的个数
          this.unselected = 1 - this.selected
        } else {
          this.selected = this.choiceNumber.length
          this.unselected = 1 - this.selected
          return
        }
      }
    },
    explainShow () {
      this.explain = true
    },
    exhibition () {
      this.explain = false
    },
    // 点击事件 按钮样式
    changeBtnOne () {
      this.btnClass = false
      this.show = true
      this.totalPoints = 10
      this.unselected = 2
      this.selected = 0
      this.choiceNumber = []
      localStorage.setItem('switchover', '前循环')
      // 按钮切换后 根据选项的状态进行勾选
      this.anteriorCirculation.map((item, index) => {
        item.checked = this.frontcirculation[index]
      })
      // 循环数组 拿去分数和勾选个数
      this.anteriorCirculation.forEach((element) => {
        // 如果 处于选中状态 就处理以下逻辑
        this.frontChecked(element)
      })
      // 清空 multiSelect 中的数据
      this.$store.commit('minitool/setScoringDetails', null)
    },
    changeBtnTwo () {
      this.btnClass = true
      this.show = false
      this.totalPoints = 10
      this.unselected = 1
      this.selected = 0
      this.choiceNumber = []
      localStorage.setItem('switchover', '后循环')
      // 按钮切换后 根据选项的状态进行勾选
      this.posteriorCirculation.map((item, index) => {
        item.checked = this.backcirculation[index]
      })
      // 循环数组 拿去分数和勾选个数
      this.posteriorCirculation.forEach((element) => {
        // 如果 处于选中状态 就处理以下逻辑
        this.backChecked(element)
      })
      // 清空 multiSelect 中的数据
      this.$store.commit('minitool/setScoringDetails', null)
    },
    // 点击事件
    getTatalsOne (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 10
      this.selected = 0
      this.unselected = 2
      this.choiceNumber = []
      // 选项的状态
      let optionMessage = []
      this.frontcirculation = []
      // 遍历 arr 数组
      this.anteriorCirculation.forEach((element) => {
        this.frontcirculation.push(element.checked)
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.frontChecked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 点击事件
    getTatalsTwo (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 10
      this.selected = 0
      this.unselected = 1
      this.choiceNumber = []
      this.backcirculation = []
      let optionMessage = []
      // 遍历 arr 数组
      this.posteriorCirculation.forEach((element) => {
        this.backcirculation.push(element.checked)
        // 如果 处于选中状态 就处理以下逻辑
        this.backChecked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          choice: true,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .button {
    display: flex;
    width: 178px;
    height: 33px;
    margin-top: 18px;
    cursor: pointer;
    justify-content: space-between;

    div {
      width: 84px;
      height: 33px;
      line-height: 33px;
      text-align: center;
      background: #F4F6F8;
      border-radius: 6px;
      color: #666666;
    }

    .change-button {
      background: #EFFAFF;
      color: #0581CE;
    }
  }

  .result {
    width: 450px;
    height: 180px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
