<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <MultipleChoice :information="oneTopic"></MultipleChoice>
          <MultipleChoice :information="twoTopic"></MultipleChoice>
          <MultipleChoice :information="threeTopic"></MultipleChoice>
          <MultipleChoice :information="fourTopic"></MultipleChoice>
          <MultipleChoice :information="fiveTopic"></MultipleChoice>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>正常与不正常的分界值与教育程度有关；文盲组≤17分，小学组≤20 分，中学或以上组≤24分。分界值以下为有认知功能缺陷，以上为正常。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, MultipleChoice, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'MMSE简易精神状态检查量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '简易精神状态量表或称简易精神状态检查表(Mini-Mental State Examination，MMSE)是最具影响的标准化智力状态检查工具之一，其作为认知障碍检查方法，可以用于阿尔茨海默病的筛查，简单易行。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'MMSE简易精神状态检查量表, 简易智力状态检查量表, 简易精神状态评价量表, mmse'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '1. 定向力（对患者进行提问，如果患者回答正确则勾选对应的选项，计1分）',
        id: [
          '1-1',
          '1-2',
          '1-3',
          '1-4',
          '1-5',
          '1-6',
          '1-7',
          '1-8',
          '1-9',
          '1-10',
        ],
        content: [
          '现在是哪一年？',
          '现在是什么季节？',
          '现在是几月份？',
          '今天是几号？',
          '今天是星期几？',
          '我们现在在什么城市（城市名）？',
          '我们现在在什么区（城区名）？（如为外地患者，则可问患者家在当地的哪个区）',
          '我们现在在什么街道？（如为外地患者，则可问患者家在当地的哪个街道）',
          '我们现在在第几层楼？',
          '我们现在在什么地方？（地址名称）',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '1', '1', '1', '1'],
        simpleContent: [
          '现在是哪一年？;',
          '现在是什么季节？;',
          '现在是几月份？;',
          '今天是几号？;',
          '今天是星期几？;',
          '我们现在在什么城市（城市名）？;',
          '我们现在在什么区（城区名）？（如为外地患者，则可问患者家在当地的哪个区）？;',
          '我们现在在什么街道？（如为外地患者，则可问患者家在当地的哪个街道）？;',
          '我们现在在第几层楼？;',
          '我们现在在什么地方？（地址名称）;',
        ],
        option: false,
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        title:
          '2. 记忆力（说出下列三样东西的名称，并请患者复述一遍，能正确复述则勾选对应的选项，计1分）',
        id: ['2-1', '2-2', '2-3'],
        content: ['复述“皮球”', '复述“国旗”', '复述“树木”'],
        grade: ['1', '1', '1'],
        simpleContent: ['复述“皮球”;', '复述“国旗”;', '复述“树木”;'],
        option: false,
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '3. 注意力和计算力（请患者计算100减7，然后从所得的数目再减去7，如此循环计算5次，计算正确则勾选对应的选项，计1分，若某一答案错误，但下一答案正确，只记一次错误）',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: ['100-7=93', '93-7=86', '86-7=79', '79-7=72', '72-7=65'],
        grade: ['1', '1', '1', '1', '1'],
        simpleContent: [
          '100-7=93;',
          '93-7=86;',
          '86-7=79;',
          '79-7=72;',
          '72-7=65;',
        ],
        option: false,
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        title:
          '4. 回忆能力（请患者回忆并回答出第二题时提示的三样东西，回忆正确则勾选对应的选项，计1分）',
        id: ['4-1', '4-2', '4-3'],
        content: ['回答出“皮球”', '回答出“国旗”', '回答出“树木”'],
        grade: ['1', '1', '1'],
        simpleContent: ['回答出“皮球”;', '回答出“国旗”;', '回答出“树木”;'],
        option: false,
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '5. 语言能力（出示语言指令并考查患者的命名、复述、阅读、执行能力，如果患者能够正确完成指令动作则勾选对应的选项，计1分）',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5', '5-6', '5-7', '5-8', '5-9'],
        content: [
          '（出示手表）这个东西叫什么？',
          '（出示铅笔）这个东西叫什么？',
          '复述“四十四只石狮子”',
          '阅读文字指令并照做：“请闭上您的眼睛”',
          '听取口头指令并照做：“右手拿起纸”',
          '听取口头指令并照做：“将纸对折”',
          '听取口头指令并照做：“将纸放在左腿上”',
          '请患者写一个完整的句子（句子要有主语、谓语、宾语，能表达一定的意思）',
          '按样画图（提供一个简单的图形，并请患者照样画出）',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '1', '1', '1'],
        simpleContent: [
          '（出示手表）这个东西叫什么？;',
          '（出示铅笔）这个东西叫什么？;',
          '复述“四十四只石狮子”;',
          '阅读文字指令并照做：“请闭上您的眼睛”;',
          '听取口头指令并照做：“右手拿起纸”;',
          '听取口头指令并照做：“将纸对折”;',
          '听取口头指令并照做：“将纸放在左腿上”;',
          '请患者写一个完整的句子（句子要有主语、谓语、宾语，能表达一定的意思）;',
          '按样画图（提供一个简单的图形，并请患者照样画出）;',
        ],
        option: false,
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 5,
      explain: 0,
      // 判断评分项选中的个数
      choiceNumber: [],
      explain: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', 'MMSE简易精神状态检查量表')
  },

  mounted () {
    // 获取到所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[1].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.result()
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        // 分数加加
        this.explain += element.attributes.grade.value * 1
        // 判断数组中是否含有某个字符，有 跳出，无 push 进去
        if (this.choiceNumber.indexOf(element.id.slice(0, 1)) === -1) {
          this.choiceNumber.push(element.id.slice(0, 1))
          // 选中的个数 等于 数组的长度
          this.selected = this.choiceNumber.length
          // 未选中的个数
          this.unselected = 5 - this.selected
        } else {
          this.selected = this.choiceNumber.length
          this.unselected = 5 - this.selected
          return
        }
      }
    },
    result () {
      if (this.explain >= 0 && this.explain <= 9) {
        this.totalPoints = `${this.explain}分，重度认知功能障碍`
      } else if (this.explain >= 10 && this.explain <= 20) {
        this.totalPoints = `${this.explain}分，中度认知功能障碍`
      } else if (this.explain >= 21 && this.explain <= 26) {
        this.totalPoints = `${this.explain}分，轻度认知功能障碍`
      } else if (this.explain >= 27 && this.explain <= 30) {
        this.totalPoints = `${this.explain}分，认知功能正常`
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 5
      this.choiceNumber = []
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
