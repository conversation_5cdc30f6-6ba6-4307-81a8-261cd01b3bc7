<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '机体反应水平分级(RLS) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '机体反应水平分级(RLS)是一种用于评估个体对刺激的感知和反应的方法。该方法将个体的反应水平分为三个级别：高反应水平、中反应水平和低反应水平。高反应水平指个体对刺激非常敏感，反应迅速而强烈。中反应水平表示个体对刺激的反应适中，能够在适当的时机做出反应。低反应水平则指个体对刺激不太敏感，反应较为迟钝或较弱。通过RLS评估，我们可以了解个体的感知和反应能力，进而为个体提供适宜的应对方法和环境调整，以促进其适应力和功能改善。此外，RLS也可用于研究人们在不同环境和任务中的反应变化，有助于探索个体反应水平的影响因素和调节机制。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'RLS评分量表（RLS Rating Scale）,RLS严重程度量表（RLS Severity Scale）,RLS症状评定量表（RLS Symptom Assessment Scale）,RLS自评量表（RLS Self-report Scale）,RLS临床评价量表（RLS Clinical Evaluation Scale）,RLS病情评估量表（RLS Disease Assessment Scale）,RLS症状严重度量表（RLS Symptom Severity Scale）,RLS影响程度评估量表（RLS Impact Assessment Scale）,RLS疼痛程度量表（RLS Pain Intensity Scale）,RLS休息质量评估量表（RLS Rest Quality Assessment Scale）'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. RLS分为8个不同级别，每个级别描述了患者的认知功能、行为表现和反应水平。下面是每个级别的简要描述：',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7','1-8'],
        content: [
          '无反应（No Response）：患者没有可观察到的反应，不能被刺激唤醒;',
          '异常反应（Generalized Response）：患者对刺激有广泛的非特定反应，但缺乏特定性和目的性;',
          '局限反应（Localized Response）：患者对刺激有局限的特定反应，如回避疼痛刺激、眨眼或者握紧拳头;',
          '混乱-激动（Confused-Agitated）：患者表现出紊乱和兴奋的行为，常伴有攻击性和冲动行为;',
          '混乱-非协调（Confused-Inappropriate）：患者表现出非协调且非目的性的行为，对外界刺激反应异常',
          '混乱-适应性（Confused-Appropriate）：患者的行为逐渐适应了他们的环境，但仍然表现出认知功能障碍',
          '自主-石化（Automatic-Appropriate）：患者的行为逐渐变得自主和目的性，能够有限地执行各种日常活动',
          '执行-完全恢复（Purposeful-Appropriate）：患者基本上恢复了正常的认知和行为能力，能够自主、灵活地执行各种任务。',
        ],
        grade: ['无反应（No Response）：患者没有可观察到的反应，不能被刺激唤醒',
                '异常反应（Generalized Response）：患者对刺激有广泛的非特定反应，但缺乏特定性和目的性',
                '局限反应（Localized Response）：患者对刺激有局限的特定反应，如回避疼痛刺激、眨眼或者握紧拳头',
                '混乱-激动（Confused-Agitated）：患者表现出紊乱和兴奋的行为，常伴有攻击性和冲动行为',
                '混乱-非协调（Confused-Inappropriate）：患者表现出非协调且非目的性的行为，对外界刺激反应异常',
                '混乱-适应性（Confused-Appropriate）：患者的行为逐渐适应了他们的环境，但仍然表现出认知功能障碍',
                '自主-石化（Automatic-Appropriate）：患者的行为逐渐变得自主和目的性，能够有限地执行各种日常活动',
                '执行-完全恢复（Purposeful-Appropriate）：患者基本上恢复了正常的认知和行为能力，能够自主、灵活地执行各种任务'
        ],
        show: 'false',
        simpleContent: [
          '无反应（No Response）：患者没有可观察到的反应，不能被刺激唤醒;',
          '异常反应（Generalized Response）：患者对刺激有广泛的非特定反应，但缺乏特定性和目的性;',
          '局限反应（Localized Response）：患者对刺激有局限的特定反应，如回避疼痛刺激、眨眼或者握紧拳头;',
          '混乱-激动（Confused-Agitated）：患者表现出紊乱和兴奋的行为，常伴有攻击性和冲动行为;',
          '混乱-非协调（Confused-Inappropriate）：患者表现出非协调且非目的性的行为，对外界刺激反应异常;',
          '混乱-适应性（Confused-Appropriate）：患者的行为逐渐适应了他们的环境，但仍然表现出认知功能障碍;',
          '自主-石化（Automatic-Appropriate）：患者的行为逐渐变得自主和目的性，能够有限地执行各种日常活动;',
          '执行-完全恢复（Purposeful-Appropriate）：患者基本上恢复了正常的认知和行为能力，能够自主、灵活地执行各种任务'
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '机体反应水平分级(RLS)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    //width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
