<template>
  <div>
    <div class="successfull">
      <img src="~/static/minitool/saveSuccessfully.jpg" alt="" class="icon" />
      <p>保存成功</p>
      <div class="btn">
        <div @click="particulars" class="particulars">查看详情</div>
        <div @click="share" class="shareBtn">分享详情</div>
        <div @click="homepage" class="returnBtn">返回首页</div>
      </div>
    </div>
    <ScoreShare :share-data="information" :share-flag="shareFlag" @editFlag="shareOpenFun" :detailsId="id"></ScoreShare>
  </div>
</template>

<script>
import ScoreShare from '@/components/MiniTool/ScoreShare'
import { selectScoreList } from '@/api/minitool'

export default {
  components: {
    ScoreShare,
  },
  data () {
    return {
      shareFlag: false, // 分享弹框
      information: {
        scoreType: '',
        scoreTitle: '',
      },
      id: '',
    }
  },

  mounted () {
    // 使用 querySelector 选择 class 为 miniToolMain 的元素
    var miniToolMain = document.querySelector('.miniToolMain');
    // 将元素的背景样式设置为空字符串，清除背景
    if(miniToolMain){
      miniToolMain.style.background = '';
    }

    // 选择 class 为 headline 下的所有 p 元素
    const paragraphs = document.querySelectorAll('.headline p');

     // 遍历所有选中的 p 元素，并修改其样式
    paragraphs.forEach(paragraph => {
      paragraph.style.fontWeight = '700';
      paragraph.style.fontSize = '20px';
      paragraph.style.color = '#333';
      paragraph.style.lineHeight = '50px';
      paragraph.style.marginLeft = '16px';
    });

    // 选择 class 为 headline 的元素
    const headlineElement = document.querySelector('.headline');

    // 修改背景颜色
    headlineElement.style.backgroundColor = '#fbfbfb';
    // this.shareFlag = true
    this.information.scoreType = localStorage.getItem('className')
    this.information.scoreTitle = localStorage.getItem('wordLast')
    this.$axios
      .$request(
        selectScoreList({
          name: this.$store.state.minitool.name,
          title: this.$store.state.minitool.title,
          userId: this.$cookies.get('medtion_user_only_sign').id,
          pageNo: 1,
          pageSize: 10,
        })
      )
      .then((res) => {
        this.id = res.list[0].id
      })
  },

  methods: {
    // 查看详情
    particulars () {
      localStorage.setItem('openeds', 7)
      this.$store.commit('minitool/setRightnav', false)
      this.$store.commit('minitool/setIsOpenedShow', true)
      this.$store.commit('minitool/setOpenedsHandler', [7])
      this.$axios
        .$request(
          selectScoreList({
            name: this.$store.state.minitool.name,
            title: this.$store.state.minitool.title,
            userId: this.$cookies.get('medtion_user_only_sign').id,
            pageNo: 1,
            pageSize: 10,
          })
        )
        .then((res) => {
          this.$store.dispatch('minitool/viewdetails', {
            id: res.list[0].id,
            router: this.$router,
            $axios: this.$axios.$request,
          })
        })
    },

    // 返回首页
    homepage () {
      // 清空 vuex 中 单选和多选 的内容
      this.$store.commit('minitool/setScoringDetails', null),
        this.$store.commit('minitool/setMultiSelect', 'null'),
        // 返回上一页
        this.$router.push('/score')
    },
    // 分享
    share () {
      this.shareFlag = true
      this.information.scoreType = localStorage.getItem('className')
      this.information.scoreTitle = localStorage.getItem('wordLast')
      this.$axios
        .$request(
          selectScoreList({
            name: this.$store.state.minitool.name,
            title: this.$store.state.minitool.title,
            userId: this.$cookies.get('medtion_user_only_sign').id,
            pageNo: 1,
            pageSize: 10,
          })
        )
        .then((res) => {
          this.id = res.list[0].id
        })
    },
    /**
     * 分享按钮弹框打开
     */
    shareOpenFun (data) {
      this.$analysys.btn_click('分享', document.title)
      this.shareFlag = data
    },
  },
}
</script>

<style lang="less" scoped>
.successfull {
  margin-top: 20px;
  width: 894px;
  height: 500px;

  .icon {
    font-size: 68px;
    margin: 65px 413px 20px 413px;
  }

  p {
    text-align: center;
    font-weight: 700;
    font-size: 22px;
    color: #333333;
  }

  .btn {
    margin-top: 80px;
    width: 100%;
    display: flex;
    padding: 0 100px;
    box-sizing: border-box;
    justify-content: space-around;

    // 查看详情
    .particulars {
      cursor: pointer;
      width: 180px;
      height: 48px;
      background: #0581ce;
      border-radius: 6px;
      text-align: center;
      line-height: 48px;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }

    // 返回首页
    .returnBtn {
      cursor: pointer;
      width: 180px;
      height: 48px;
      background: #ffffff;
      border-radius: 6px;
      text-align: center;
      line-height: 48px;
      font-weight: 400;
      font-size: 16px;
      color: #0581ce;
      border: 1px solid #0581ce;
    }

    // 分享详情
    .shareBtn {
      cursor: pointer;
      width: 180px;
      height: 48px;
      background: #0eb2f8;
      border-radius: 6px;
      text-align: center;
      line-height: 48px;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
    }
  }
}
</style>
