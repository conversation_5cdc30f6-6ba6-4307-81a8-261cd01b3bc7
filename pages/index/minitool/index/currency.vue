<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <SheetChange :information="oneTopic"></SheetChange>
          <SheetChange :information="twoTopic" v-show="twoTopicShow"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span style="white-space: pre-wrap" v-show="selected !== 0">{{
              totalPoints
            }}</span>
          </div>
          <div v-show="selected !== 0" style="white-space: pre-wrap" class="explain">
            <p>{{ describe }}</p>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ explain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, SheetChange },
  // tdk
  head () {
    return {
      title: '洼田饮水试验评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '洼田饮水试验是评定吞咽障碍的实验方法，分级明确清楚，操作简单，利于选择有治疗适应症的患者。局限性在于：该检查根据患者主观感觉，与临床和实验室检查结果不一致的很多，并要求患者意识清楚并能够按照指令完成试验。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '洼田饮水试验, 洼田吞咽能力评定法'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 700px;',
        title: '1. 患者端坐，喝下30毫升温开水，观察所需时间和呛咳情况',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '能顺利地1次将水咽下',
          '分2次以上，能不呛咳地咽下',
          '能1次咽下，但有呛咳',
          '分2次以上咽下，但有呛咳',
          '频繁呛咳，不能全部咽下',
        ],
        grade: ['Ⅰ级', 'Ⅱ级', 'Ⅲ级', 'Ⅳ级', 'Ⅴ级'],
        describe: [
          '',
          '分级：Ⅱ级 吞咽障碍的判断：可疑有吞咽障碍 误吸风险的判断：Ⅰ~Ⅱ不伴有呛咳，可以认为不存在显性误吸，为洼田饮水试验阴性',
          '分级：Ⅲ级 吞咽障碍的判断：确定有吞咽障碍 误吸风险的判断：Ⅲ~Ⅴ级伴有呛咳，判定为可能存在误吸，为洼田饮水试验阳性',
          '分级：Ⅳ级 吞咽障碍的判断：确定有吞咽障碍 误吸风险的判断：Ⅲ~Ⅴ级伴有呛咳，判定为可能存在误吸，为洼田饮水试验阳性',
          '分级：Ⅴ级 吞咽障碍的判断：确定有吞咽障碍 误吸风险的判断：Ⅲ~Ⅴ级伴有呛咳，判定为可能存在误吸，为洼田饮水试验阳性',
        ],
        show: 'false',
        simpleContent: [
          '能顺利地1次将水咽下;',
          '分2次以上，能不呛咳地咽下;',
          '能1次咽下，但有呛咳;',
          '分2次以上咽下，但有呛咳;',
          '频繁呛咳，不能全部咽下;',
        ],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 700px;',
        title: '2. 患者是否在5秒内咽下',
        id: ['1-1', '1-2'],
        content: ['是', '否'],
        grade: ['Ⅰ级', 'Ⅰ级'],
        describe: [
          '分级：Ⅰ级 吞咽障碍的判断：正常 误吸风险的判断：Ⅰ~Ⅱ不伴有呛咳，可以认为不存在显性误吸，为洼田饮水试验阴性',
          '分级：Ⅰ级 吞咽障碍的判断：可疑有吞咽障碍 误吸风险的判断：Ⅰ~Ⅱ不伴有呛咳，可以认为不存在显性误吸，为洼田饮水试验阴性',
        ],
        show: 'false',
        simpleContent: ['是;', '否;'],
      },
      explain:
        '该评分表也可用于疗效判断，标准如下：治愈：吞咽障碍消失，饮水试验评定1级 有效：吞咽障碍明显改善，饮水试验评定2级 无效：吞咽障碍改善不显著，饮水试验评定≥3级',
      describe: '',
      // 总分数
      totalPoints: '',
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 1,
      choiceBox: [],
      twoTopicShow: false,
      firstQuestion: [],
      secondQuestion: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '洼田饮水试验评分')
  },


  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    this.firstQuestion = this.choiceBox.slice(0, 5)
    this.secondQuestion = this.choiceBox.slice(5, 7)

    // 遍历 choiceBox 数组
    this.firstQuestion.map((item, index) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.firstChecked(item, index)
    })
    if (this.choiceBox[0].checked === true) {
      this.secondQuestion.map((item, index) => {
        // 如果 处于选中状态 就处理以下逻辑
        this.secondChecked(item, index)
      })
    }
  },
  methods: {
    // 第一题的公共逻辑
    firstChecked (item, index) {
      if (item.checked === true) {
        if (index === 0) {
          this.selected += 1
          this.unselected = 2
          this.unselected -= 1
          this.twoTopicShow = true
        } else {
          this.secondQuestion.map((it) => {
            if (it.checked === true) {
              this.$store.commit('minitool/setDelete', 1)
            }
            it.checked = false
          })
          this.twoTopicShow = false
          this.unselected = 1
          this.selected += 1
          this.unselected -= 1
          this.totalPoints = item.attributes.describe.value
        }
      }
    },
    // 第二题的公共逻辑
    secondChecked (item, index) {
      if (item.checked === true) {
        this.selected += 1
        this.unselected = 1
        this.unselected -= 1
        this.totalPoints = item.attributes.describe.value
      }
    },
    // 点击事件
    getTatals (e) {
      this.firstQuestion = this.choiceBox.slice(0, 5)
      this.secondQuestion = this.choiceBox.slice(5, 7)
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = ''
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.firstQuestion.map((item, index) => {
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.firstChecked(item, index)
      })
      if (this.choiceBox[0].checked === true) {
        this.secondQuestion.map((item, index) => {
          optionMessage.push({
            choice: item.checked,
          })
          // 如果 处于选中状态 就处理以下逻辑
          this.secondChecked(item, index)
        })
      }
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 500px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 210px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
