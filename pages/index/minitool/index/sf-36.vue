<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="motorFunction" class="oneTopic">
            <p class="subTitle">1.总体来讲，您的健康状况是</p>
            <Options v-for="i in health" :key="i.title" :information="i"></Options>
            <p class="subTitle">2.跟一年前相比，您觉得您现在的健康状况是</p>
            <Options v-for="i in healthStatus" :key="i.title" :information="i"></Options>
            <p class="subTitle">健康和日常活动</p>
            <p class="subTitle">3.以下这些问题都与日常活动有关。请您想一想，您的健康状况是否限制这些活动？如果有限制，程度如何？</p>
            <Options v-for="i in dailyActivities" :key="i.title" :information="i"></Options>
            <p class="subTitle">4.在过去 4 个星期里，您的工作和日常活动有无因为身体健康的原因而出现以下这些问题？</p>
            <Options v-for="i in healthReason" :key="i.title" :information="i"></Options>
          </div>
          <div ref="cognitiveFunction" class="cognitive-function">
            <p class="subTitle">5.在过去 4 个星期里，您的工作和日常活动有无因为情绪的原因(如抑郁或焦虑)而出现以下问题？</p>
            <Options v-for="i in emotionalReasons" :key="i.title" :information="i"></Options>
            <p class="subTitle">6.在过去 4 个星期里，您的健康或情绪不好在多大程度上影响了您与家人、朋友、邻居或集体的正常社会交往?</p>
            <Options v-for="i in socialCognition" :key="i.title" :information="i"></Options>
            <p class="subTitle">7.在过去 4 个星期里，您有身体疼痛吗?</p>
            <Options v-for="i in physicalPain" :key="i.title" :information="i"></Options>
            <p class="subTitle">8.过去 4 个星期里，身体疼痛影响您的工作和家务事吗?</p>
            <p class="subTitle">1）如果条目 7 选择“完全没有”，则得分依次为：</p>
            <Options v-for="i in housekeeping" :key="i.key" :information="i"></Options>
            <p class="subTitle">2）如果条目 7 选择除“完全没有”外的其他选项，则得分依次为：</p>
            <Options v-for="i in housekeepingTwo" :key="i.key" :information="i"></Options>
            <p class="subTitle">9.以下这些问题是关于过去 4 个星期里您自己的感觉，对每一条问题所说的事情，您的情况是什么样的?</p>
            <Options v-for="i in monthFeeling" :key="i.title" :information="i"></Options>
            <p class="subTitle">10.身体健康或情绪问题影响了您的社会活动(如走亲访友等)</p>
            <Options v-for="i in socialActivities" :key="i.title" :information="i"></Options>
            <p class="subTitle">11.请看下列每一条问题，哪一种答案最符合您的情况？</p>
            <Options v-for="i in allHealthReason" :key="i.title" :information="i"></Options>
          </div>
        </div>

        <div class="diseaseType">
          <div class="disease">{{ physicalFunction.name }}:<span>{{ physicalFunction.result }}分</span></div>
          <div class="disease">{{ rolePhysical.name }}:<span>{{ rolePhysical.result }}分</span></div>
          <div class="disease">{{ musclePain.name }}:<span>{{ musclePain.result }}分</span></div>
          <div class="disease">{{ healthCondition.name }}:<span>{{ healthCondition.result }}分</span></div>
          <div class="disease">{{ vitality.name }}:<span>{{ vitality.result }}分</span></div>
          <div class="disease">{{ socialFunction.name }}:<span>{{ socialFunction.result }}分</span></div>
          <div class="disease">{{ emotionalRoles.name }}:<span>{{ emotionalRoles.result }}分</span></div>
          <div class="disease">{{ mentalHealth.name }}:<span>{{ mentalHealth.result }}分</span></div>
          <div class="disease">{{ mentalHealthHT.name }}:<span>{{ mentalHealthHT.result }}分</span></div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected !== 36">暂无</span>
            <span v-show="selected == 36">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import Options from '@/components/MiniTool/Options.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options },
  // tdk
  head () {
    return {
      title: '健康调查简表SF36(生存质量简表) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '功能独立性评定（Functional Independence Measure，FIM）是一种用于评估病人康复程度的工具，它主要评估病人在日常活动中的功能独立性。被广泛应用于各种康复领域，包括神经康复、骨科康复、心脏康复、肿瘤康复等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '功能独立性评定, FIM, 认知功能测定子量表, Functional Independence Measure'
        }
      ]
    }
  },
  data () {
    return {
      // 健康状况
      health: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '1. 总体来讲，您的健康状况是',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
          content: [
            '1) 极好：5.0分;',
            '2) 很好：4.4分;',
            '3) 好：3.4分;',
            '4) 一般：2.0分;',
            '5) 差：1.0分。'
          ],
          grade: ['5.0', '4.4', '3.4', '2.0', '1.0'],
          simpleContent: ['极好;', '很好;', '好;', '一般;', '差;'],
          titleShow: true
        }
      ],
      // 健康情况
      healthStatus: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '2. 跟一年前相比，您觉得您现在的健康状况是',
          id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
          content: [
            '1) 比1年前好多了：5分;',
            '2) 比1年前好一些 ：4分;',
            '3) 跟1年前差不多：3分;',
            '4) 比1年前差一些：2分;',
            '5) 比1年前差多了：1分。'
          ],
          grade: ['5', '4', '3', '2', '1'],
          simpleContent: ['比1年前好多了;', '比1年前好一些;', '跟1年前差不多;', '比1年前差一些;', '比1年前差多了;'],
          titleShow: true
        }
      ],
      // 健康和日常管理
      dailyActivities: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(1). 重体力活动，如：跑步、举重物、参加剧烈运动等',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 380px;',
          title: '(2). 适度的活动，如：移动一张桌子、推动吸尘器、扫地、打太极拳、做简单体操、玩保龄球等',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(3). 手提日用品，如买菜、购日常用品等',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(4). 上几层楼梯',
          id: ['6-1', '6-2', '6-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(5). 上一层楼梯',
          id: ['7-1', '7-2', '7-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(6). 弯腰、屈膝、下蹲',
          id: ['8-1', '8-2', '8-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(7). 步行 1600 米以上的路程（3 里地，公交车 1 站地）',
          id: ['9-1', '9-2', '9-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(8). 步行 800 米的路程',
          id: ['10-1', '10-2', '10-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(9). 步行 100 米的路程',
          id: ['11-1', '11-2', '11-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 380px;',
          title: '(10). 自己洗澡、穿衣',
          id: ['12-1', '12-2', '12-3'],
          content: [
            '1) 限制很大：1分;',
            '2) 有些限制：2分;',
            '3) 毫无限制：3分。'
          ],
          grade: ['1', '2', '3'],
          simpleContent: ['限制很大;', '有些限制;', '毫无限制;']
        }
      ],
      healthReason: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 210px;',
          title: '(1). 减少了工作或其他活动时间',
          id: ['13-1', '13-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 210px;',
          title: '(2). 比想要做的事情完成少',
          id: ['14-1', '14-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 210px;',
          title: '(3). 想要干的工作和活动的种类受到限制',
          id: ['15-1', '15-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '不是;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 210px;',
          title: '(4). 完成工作或其他活动困难增多(比如需要额外的努力)',
          id: ['16-1', '16-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }
      ],

      emotionalReasons: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 210px;',
          title: '(1). 减少了工作或活动的时间',
          id: ['17-1', '17-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 210px;',
          title: '(2). 本来想要做的事情只能完成一部分',
          id: ['18-1', '18-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 210px;',
          title: '(3). 干事情不如平时仔细了',
          id: ['19-1', '19-2'],
          content: [
            '1) 有：1分;',
            '2) 没有：2分。'
          ],
          grade: ['1', '2'],
          simpleContent: ['有;', '没有;']
        }
      ],
      socialCognition: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '6. 在过去 4 个星期里，您的健康或情绪不好在多大程度上影响了您与家人、朋友、邻居或集体的正常社会交往?',
          id: ['20-1', '20-2', '20-3', '20-4', '20-5'],
          content: [
            '1) 完全没有影响：5分;',
            '2) 有一点影响：4分;',
            '3) 中等影响：3分;',
            '4) 影响较大：2分;',
            '5) 影响极大：1分。'
          ],
          grade: ['5', '4', '3', '2', '1'],
          simpleContent: ['完全没有影响;', '有一点影响;', '中等影响;', '影响较大;', '影响极大;'],
          titleShow: true
        }
      ],
      physicalPain: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '7. 在过去 4 个星期里，您有身体疼痛吗?',
          id: ['21-1', '21-2', '21-3', '21-4', '21-5', '21-6'],
          content: [
            '1) 完全没有：6.0分;',
            '2) 稍微有一点：5.4分;',
            '3) 有一点：4.2分;',
            '4) 中等：3.1分;',
            '5) 严重：2.2分;',
            '6) 很严重：1.0分。'
          ],
          grade: ['6.0', '5.4', '4.2', '3.1', '2.2', '1.0'],
          simpleContent: ['完全没有;', '稍微有一点;', '有一点', '中等;', '严重;', '很严重疼痛;'],
          titleShow: true
        }
      ],
      housekeeping: [
        {
          key: 0,
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '8. 过去 4 个星期里，身体疼痛影响您的工作和家务事吗?',
          id: ['22-1', '22-2', '22-3', '22-4', '22-5'],
          content: [
            '1) 完全没影响：6.0分;',
            '2) 有一点影响：4.75分;',
            '3) 中等影响：3.5分;',
            '4) 影响很大 ：2.25分;',
            '5) 影响非常大：1.0分。'
          ],
          grade: ['6.0', '4.75', '3.5', '2.25', '1.0'],
          simpleContent: ['完全没影响;', '有一点影响;', '中等影响;', '影响很大;', '影响非常大;'],
          titleShow: true
        }
      ],
      housekeepingTwo: [
        {
          key: 1,
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '8. 过去 4 个星期里，身体疼痛影响您的工作和家务事吗?',
          id: ['37-1', '37-2', '37-3', '37-4', '37-5'],
          content: [
            '1) 完全没影响：5分;',
            '2) 有一点影响：4.分;',
            '3) 中等影响：3分;',
            '4) 影响很大 ：2分;',
            '5) 影响非常大：1分。'
          ],
          grade: ['5', '4', '3', '2', '1'],
          simpleContent: ['完全没影响;', '有一点影响;', '中等影响;', '影响很大;', '影响非常大;'],
          titleShow: true
        }
      ],
      monthFeeling: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(1). 您觉得生活充实吗？',
          id: ['23-1', '23-2', '23-3', '23-4', '23-5', '23-6'],
          content: [
            '1) 所有的时间：6分;',
            '2) 大部分时间：5分;',
            '3) 比较多时间：4分;',
            '4) 一部分时间：3分;',
            '5) 一小部分时间：2分;',
            '6) 没有这种感觉：1分。'
          ],
          grade: ['6', '5', '4', '3', '2', '1'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(2). 您是一个敏感的人吗？',
          id: ['24-1', '24-2', '24-3', '24-4', '24-5', '24-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间：4分;',
            '5) 一小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(3). 您有没有情绪非常不好，什么事都不能使您高兴？',
          id: ['25-1', '25-2', '25-3', '25-4', '25-5', '25-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间：4分;',
            '5) 一小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(4). 您的心理很平静、平和吗？',
          id: ['26-1', '26-2', '26-3', '26-4', '26-5', '26-6'],
          content: [
            '1) 所有的时间：6分;',
            '2) 大部分时间：5分;',
            '3) 比较多时间：4分;',
            '4) 一部分时间：3分;',
            '5) 一小部分时间：2分;',
            '6) 没有这种感觉：1分。'
          ],
          grade: ['6', '5', '4', '3', '2', '1'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(5). 您做事感到精力充沛吗？',
          id: ['27-1', '27-2', '27-3', '27-4', '27-5', '27-6'],
          content: [
            '1) 所有的时间：6分;',
            '2) 大部分时间：5分;',
            '3) 比较多时间：4分;',
            '4) 一部分时间：3分;',
            '5) 一小部分时间：2分;',
            '6) 没有这种感觉：1分。'
          ],
          grade: ['6', '5', '4', '3', '2', '1'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(6). 您有没有情绪低落呢？',
          id: ['28-1', '28-2', '28-3', '28-4', '28-5', '28-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间：4分;',
            '5) 一小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(7). 您有没有觉得筋疲力尽呢？',
          id: ['29-1', '29-2', '29-3', '29-4', '29-5', '29-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间：4分;',
            '5) 一小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(8). 您是一个快乐的人吗？',
          id: ['30-1', '30-2', '30-3', '30-4', '30-5', '30-6'],
          content: [
            '1) 所有的时间：6分;',
            '2) 大部分时间：5分;',
            '3) 比较多时间：4分;',
            '4) 一部分时间：3分;',
            '5) 一小部分时间：2分;',
            '6) 没有这种感觉：1分。'
          ],
          grade: ['6', '5', '4', '3', '2', '1'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(9). 您感觉厌烦吗？',
          id: ['31-1', '31-2', '31-3', '31-4', '31-5', '31-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间：4分;',
            '5) 一小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '一小部分时间;', '没有这种感觉;']
        }
      ],
      socialActivities: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '10. 身体健康或情绪问题影响了您的社会活动(如走亲访友等)',
          id: ['32-1', '32-2', '32-3', '32-4', '32-5', '32-6'],
          content: [
            '1) 所有的时间：1分;',
            '2) 大部分时间：2分;',
            '3) 比较多时间：3分;',
            '4) 一部分时间 ：4分;',
            '5) 小部分时间：5分;',
            '6) 没有这种感觉：6分。'
          ],
          grade: ['1', '2', '3', '4', '5', '6'],
          simpleContent: ['所有的时间;', '大部分时间;', '比较多时间;', '一部分时间;', '小部分时间;', '没有这种感觉;'],
          titleShow: true
        }
      ],
      allHealthReason: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '(1). 我好像比别人容易生病',
          id: ['33-1', '33-2', '33-3', '33-4', '33-5'],
          content: [
            '1) 完全对：1分;',
            '2) 大部分对：2分;',
            '3) 不能肯定：3分;',
            '4) 大部分不对：4分;',
            '5) 完全不对：5分。'
          ],
          grade: ['1', '2', '3', '4', '5'],
          simpleContent: ['完全对;', '大部分对;', '不能肯定;', '大部分不对;', '完全不对;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '(2). 我跟周围人一样健康',
          id: ['34-1', '34-2', '34-3', '34-4', '34-5'],
          content: [
            '1) 完全对：5分;',
            '2) 大部分对：4分;',
            '3) 不能肯定：3分;',
            '4) 大部分不对：2分;',
            '5) 完全不对：1分。'
          ],
          grade: ['5', '4', '3', '2', '1'],
          simpleContent: ['完全对;', '大部分对;', '不能肯定;', '大部分不对;', '完全不对;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '(3). 我认为我的健康状况在变坏',
          id: ['35-1', '35-2', '35-3', '35-4', '35-5'],
          content: [
            '1) 完全对：1分;',
            '2) 大部分对：2分;',
            '3) 不能肯定：3分;',
            '4) 大部分不对：4分;',
            '5) 完全不对：5分。'
          ],
          grade: ['1', '2', '3', '4', '5'],
          simpleContent: ['完全对;', '大部分对;', '不能肯定;', '大部分不对;', '完全不对;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '(4). 我的健康状况非常好',
          id: ['36-1', '36-2', '36-3', '36-4', '36-5'],
          content: [
            '1) 完全对：5分;',
            '2) 大部分对：4分;',
            '3) 不能肯定：3分;',
            '4) 大部分不对：2分;',
            '5) 完全不对：1分。'
          ],
          grade: ['5', '4', '3', '2', '1'],
          simpleContent: ['完全对;', '大部分对;', '不能肯定;', '大部分不对;', '完全不对;']
        }
      ],
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 36,
      // 总分数
      totalPoints: 0,
      choiceBoxs: [[], []],
      score: {
        // 运动功能 分数
        motorFunctionScore: 0,
        // 认知功能 分数
        cognitiveFunctionScore: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: [],
      physicalFunction: {
        name: '生理功能PF初得分',
        finallyName: '生理功能PF终得分',
        result: 0,
        finalScore: 0
      },
      rolePhysical: {
        name: '生理职能RP初得分',
        finallyName: '生理职能RP终得分',
        result: 0,
        finalScore: 0
      },
      musclePain: {
        name: '躯体疼痛BP初得分',
        finallyName: '躯体疼痛BP终得分',
        result: 0,
        finalScore: 0
      },
      healthCondition: {
        name: '总体健康感GH初得分',
        finallyName: '总体健康感GH终得分',
        result: 0,
        finalScore: 0
      },
      vitality: {
        name: '生命活力VT初得分',
        finallyName: '生命活力VT终得分',
        result: 0,
        finalScore: 0
      },
      socialFunction: {
        name: '社交功能SF初得分',
        finallyName: '社交功能SF终得分',
        result: 0,
        finalScore: 0
      },
      emotionalRoles: {
        name: '情感职能RE初得分',
        finallyName: '情感职能RE终得分',
        result: 0,
        finalScore: 0
      },
      mentalHealth: {
        name: '精神健康MH初得分',
        finallyName: '精神健康MH终得分',
        result: 0,
        finalScore: 0
      },
      mentalHealthHT: {
        name: '健康变化HT初得分',
        finallyName: '健康变化HT终得分',
        result: 0,
        finalScore: 0
      },
      score7: 0,
      score8: 0
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      return this.physicalFunction.finallyName + ":" + this.physicalFunction.finalScore + ";" + this.rolePhysical.finallyName + ":" + this.rolePhysical.finalScore + ";"
        + this.musclePain.finallyName + ":" + this.musclePain.finalScore + ";" + this.healthCondition.finallyName + ":" + this.healthCondition.finalScore + ";"
        + this.vitality.finallyName + ":" + this.vitality.finalScore + ";" + this.socialFunction.finallyName + ":" + this.socialFunction.finalScore + ";"
        + this.emotionalRoles.finallyName + ":" + this.emotionalRoles.finalScore + ";" + this.mentalHealth.finallyName + ":" + this.mentalHealth.finalScore + this.mentalHealthHT.finallyName + ":" + this.mentalHealthHT.finalScore + ";"
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '健康调查简表SF36(生存质量简表)')
  },

  mounted () {
    this.motorFn()
    this.cognitiveFn()
    this.loginEnd()
  },

  methods: {
    // 未登录状态下 做题 登录之后的逻辑
    loginEnd () {
      // 登录之后 从本地拿去数据 勾选选项
      if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
        this.allInput.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
          ].choice
        })
      }
      // 勾选完，循环数组，拿去分数和选中个数
      this.allInput.map((item) => {
        // 如果 处于选中状态 就处理以下逻辑
        if (item.checked === true) {
          if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
            this.choiceBox.push(item.id.slice(0, 2))
          }
          this.selected = this.choiceBox.length
          this.unselected = 36 - this.selected
          this.totalPoints += item.attributes.grade.value * 1
        }
      })
    },
    // 获取 运动功能 中的所有input框
    motorFn () {
      for (let i = 0; i < this.$refs.motorFunction.children.length; i++) {
        let index = this.$refs.motorFunction.children[i]
        if (index.children.length != 0) {
          if (index.children[0].children[3]) {
            let length = index.children[0].children[3].children.length
            for (let j = 0; j < length; j++) {
              this.choiceBoxs[0].push(
                index.children[0].children[3].children[j]
                  .children[0]
              )
            }
          }
        }
      }
    },
    // 获取 认知功能 中的所有input框
    cognitiveFn () {
      for (let i = 0; i < this.$refs.cognitiveFunction.children.length; i++) {
        let index = this.$refs.cognitiveFunction.children[i]
        if (index.children.length != 0) {
          if (index.children[0].children[3]) {
            let length = index.children[0].children[3].children.length
            for (let j = 0; j < length; j++) {
              this.choiceBoxs[1].push(
                index.children[0].children[3].children[j]
                  .children[0]
              )
            }
          }

        }
      }
      // 清空 allInput
      this.allInput = []
      // 合并两个数组
      this.allInput = [...this.allInput, ...this.choiceBoxs[0], ...this.choiceBoxs[1]]
    },
    checkedMotor (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 36 - this.selected
        // 获取 grade 值并转换为整数
        const gradeValue = parseFloat(item.attributes.grade.value);
        const nameValue = item.attributes.id.value;

        // 1.躯体功能 计算第三大题 第一道题到第10道题的总分：3a+3b+3c+3d+3e+3f+3g+3h+3h+3i
        // 匹配序号（假设序号是位于名字字符串开头的数字）
        const match = nameValue.match(/^\d+/);
        if (match) {
          const sequenceNumber = parseFloat(match[0]);

          // 健康变化HT初得分 2
          if (sequenceNumber === 2) {
            this.mentalHealthHT.result = gradeValue;
            this.mentalHealthHT.finalScore = ((this.mentalHealthHT.result - 1) / 4 * 100).toFixed(2);
          }

          // 生理功能PF初得分: 3(1)+3(2)+······+3(10)
          if (sequenceNumber >= 3 && sequenceNumber <= 12) {
            this.physicalFunction.result += gradeValue;
            this.physicalFunction.finalScore = ((this.physicalFunction.result - 10) / 20 * 100).toFixed(2);
          }

          // 生理职能RP初得分: 4(1)+4(2)+ 4(3)+4(4)
          if (sequenceNumber >= 13 && sequenceNumber <= 16) {
            this.rolePhysical.result += gradeValue;
            this.rolePhysical.finalScore = ((this.rolePhysical.result - 4) / 4 * 100).toFixed(2);
          }

          // 总体健康感GH初得分: 1+11(1)+11(2)+11(3)+11(4)
          if (sequenceNumber === 1) {
            this.healthCondition.result = this.healthCondition.result + gradeValue;
            this.healthCondition.finalScore = ((this.healthCondition.result - 5) / 20 * 100).toFixed(2);
          }
        }
      }
    },
    checkedCognitive (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 36 - this.selected

        // 获取 grade 值并转换为整数
        const gradeValue = parseFloat(item.attributes.grade.value);
        const nameValue = item.attributes.id.value;

        // 匹配序号（假设序号是位于名字字符串开头的数字）
        const match = nameValue.match(/^\d+/);
        if (match) {
          const sequenceNumber = parseFloat(match[0]);
          // 躯体疼痛BP初得分: 7+8
          if (sequenceNumber === 21) {
            this.score7 = gradeValue
          }

          if (sequenceNumber === 22 || sequenceNumber === 37) {
            this.score8 = gradeValue
          }

          this.musclePain.result = this.score7 + this.score8
          this.musclePain.finalScore = ((this.musclePain.result - 2) / 10 * 100).toFixed(2);


          // 总体健康感GH初得分: 1+11(1)+11(2)+11(3)+11(4)
          if (sequenceNumber >= 33 && sequenceNumber <= 36) {
            this.healthCondition.result += gradeValue;
            this.healthCondition.finalScore = ((this.healthCondition.result - 5) / 20 * 100).toFixed(2);

          }

          // 生命活力VT初得分: 9(1)+9(5)+ 9(7)+9(9)
          if (sequenceNumber === 23 || sequenceNumber === 27 || sequenceNumber === 29 || sequenceNumber === 31) {
            this.vitality.result += gradeValue;
            this.vitality.finalScore = ((this.vitality.result - 4) / 20 * 100).toFixed(2);
          }

          // 社交功能SF初得分: 6+10
          if (sequenceNumber === 20 || sequenceNumber === 32) {
            this.socialFunction.result += gradeValue;
            this.socialFunction.finalScore = ((this.socialFunction.result - 2) / 9 * 100).toFixed(2);
          }

          // 情感职能RE初得分: 5(1)+5(2)+5(3)
          if (sequenceNumber >= 17 && sequenceNumber <= 19) {
            this.emotionalRoles.result += gradeValue;
            this.emotionalRoles.finalScore = ((this.emotionalRoles.result - 3) / 3 * 100).toFixed(2);
          }

          // 精神健康MH初得分: 9(2)+9(3)+ 9(6)+9(4) +9(8)
          if ((sequenceNumber >= 24 && sequenceNumber <= 26) || sequenceNumber === 28 || sequenceNumber === 30) {
            this.mentalHealth.result += gradeValue;
            this.mentalHealth.finalScore = ((this.mentalHealth.result - 5) / 25 * 100).toFixed(2);
          }
        }
      }
    },
    // 选项的点击事件
    getTatals () {
      this.physicalFunction.result = 0
      this.rolePhysical.result = 0
      this.healthCondition.result = 0
      this.vitality.result = 0
      this.socialFunction.result = 0
      this.emotionalRoles.result = 0
      this.mentalHealth.result = 0
      // this.musclePain.result=0
      // 初始化数据
      this.selected = 0
      this.choiceBox = []
      this.unselected = 36
      this.totalPoints = 0
      this.optionMessage = []
      this.score.motorFunctionScore = 0
      this.score.cognitiveFunctionScore = 0
      // 运动功能
      this.choiceBoxs[0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedMotor(item)
      })
      // 认知功能
      this.choiceBoxs[1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedCognitive(item)
      })
      // 计算总分
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
        console.log(6666, keepArguments);

      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    .title {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      text-align: center;
      margin: 18px 0;
    }

    .subTitle {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 17px;
      color: #202020;
      margin: 20px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    .threeTopic {
      div {
        width: 877px;
        margin-top: 16px;
        padding: 3px 16px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(5, 129, 206, 0.03);

        p:nth-of-type(1) {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }

        p:nth-of-type(2) {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.oneTopic {
  margin-top: 35px;
}

/deep/.openEyes {
  padding: 0 0 16px 0 !important;
}

.diseaseType {
  margin-top: 30px;
}

.disease {
  margin-bottom: 18px;
}
</style>
