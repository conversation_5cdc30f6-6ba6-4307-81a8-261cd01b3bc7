<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in FLACC" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div style="white-space: pre-wrap" class="explain">{{ resultExplain }}</div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script type="text/javascript">
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'FLACC 行为评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'FLACC 行为评分是一种评估老年人能否独立生活的工具。FLACC 行为评分由6个项目组成，每个项目被评为能力（独立完成）或无能力（需要帮助），总分为6分。该评估工具被广泛用于医疗、社会保障和养老机构等领域，用于评估老年人的自理能力水平。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'FLACC, 日常生活功能指数评价, Katz指数, 日常生活能力评估'
        }
      ]
    }
  },
  data () {
    return {
      FLACC: [
        // 1.面部表情
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 面部表情',
          id: ['1-1', '1-2', '1-3'],
          content: [
            '1) 无特定表情或微笑：0分;',
            '2) 偶尔面部扭曲或皱眉：1分;',
            '3) 持续颤抖下巴，紧缩下颚，紧皱眉头：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['无特定表情或微笑;', '偶尔面部扭曲或皱眉;', '持续颤抖下巴，紧缩下颚，紧皱眉头;']
        },
        // 2.腿部活动
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 腿部活动',
          id: ['2-1', '2-2', '2-3'],
          content: [
            '1) 正常体位或放松状态：0分;',
            '2) 不适，无法休息，肌肉或神经紧张，肌体间断弯曲/伸展：1分;',
            '3) 踢或拉直腰，高张力，扩大肌体弯曲/伸展，发抖：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['正常体位或放松状态;', '不适，无法休息，肌肉或神经紧张，肌体间断弯曲/伸展;', '踢或拉直腰，高张力，扩大肌体弯曲/伸展，发抖;']
        },
        // 3.活动度
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 活动度',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 安静平躺，正常体位，可顺利移动：0分;',
            '2) 急促不安，来回移动，紧张，移动犹豫：1分;',
            '3) 卷曲或痉挛，来回摆动，头部左右摇动，揉搓身体某部分：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['安静平躺，正常体位，可顺利移动;', '急促不安，来回移动，紧张，移动犹豫;', '卷曲或痉挛，来回摆动，头部左右摇动，揉搓身体某部分;']
        },
        // 4.哭闹
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 哭闹',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 不哭不闹：0分;',
            '2) 呻吟或啜泣，偶尔哭泣，叹息：1分;',
            '3) 不断哭泣，尖叫或抽泣，呻吟：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不哭不闹;', '呻吟或啜泣，偶尔哭泣，叹息;', '不断哭泣，尖叫或抽泣，呻吟']
        },
        // 5.可安慰度
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '5. 可安慰度',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 无须帮助，或能借助辅助器具进出厕所：0分;',
            '2) 需帮助进出厕所，便后清洁或整理衣裤：1分;',
            '3) 不能自行进出厕所完成排泄过程：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['平静的，满足的，放松，不要求安慰;', '可通过偶尔身体接触消除疑虑，分散注意;', '安慰有困难;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 5,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return '结果说明：5 项指标的总和，最低为0分，最高为10 分，得分越高，不适和疼痛越明显。\n ' +
             'FLACC 评分(FLACC Score)包括:面部表情(facialexpression)，腿部活动(legs)、活动度(activity)、哭闹(cry)、是否易安(consolability)。\n ' +
             'FIACC 行为评分法适用:术后疼痛及不适，选择性外科手术术后，不能精确表达的0岁~7岁儿童。'
              },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      return `${this.totalPoints}分`
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', 'FLACC 行为评分')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 5
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
