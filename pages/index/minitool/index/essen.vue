<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="age"></Options>
          <MultipleChoice :information="symptom"></MultipleChoice>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails, MultipleChoice },

  // tdk
  head () {
    return {
      title: 'ESSEN评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Essen卒中风险分层量表 (Essen stroke risk score，ESRS) 是一个简便、易于临床操作的9分量表，主要用于预测缺血性脑卒中后1年内脑卒中复发风险，指导二级预防。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'ESSEN, 卒中风险评分量表, 卒中评分, ESRS, 高血压, 糖尿病, 识别卒中高危患者'
        }
      ]
    }
  },

  data () {
    return {
      // 患者年龄
      age: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '1. 患者年龄',
        id: ['1-1', '1-2', '1-3'],
        content: ['1) <65岁：0分;', '2) 65~75岁：1分;', '3) >75岁：2分。'],
        grade: ['0', '1', '2'],
        simpleContent: ['<65岁;', '65~75岁;', '>75岁;'],
      },
      // 患者情况/症状
      symptom: {
        bgColor: 'background: #FBFBFB;',
        title: '2. 患者情况/症状',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5', '2-6', '2-7', '2-8'],
        content: [
          '1) 高血压：1分',
          '2) 糖尿病：1分',
          '3) 吸烟：1分',
          '4) 既往心肌梗死：1分',
          '5) 其他心脏病（除外心梗或房颤）：1分',
          '6) 既往TIA或缺血性卒中病史：1分',
          '7) 外周动脉疾病：1分',
          '8) 没有以上情况或症状：0分',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '1', '0'],
        simpleContent: [
          '高血压;',
          '糖尿病;',
          '吸烟;',
          '既往心肌梗死;',
          '其他心脏病（除外心梗或房颤）;',
          '既往TIA或缺血性卒中病史;',
          '外周动脉疾病;',
          '没有以上情况或症状;',
        ],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 2,
      // 总分数
      totalPoints: 0,
      totalPointsSingle: 0,
      totalPointsMore: 0,
      // 单选
      multipleChoice: [],
      // 多选
      checkbox: [],
      // 选择个数
      selectNumber: [],
      allInput: []
    }
  },

  beforeMount () {
    localStorage.setItem('className', '卒中再发风险评分')
    localStorage.setItem('wordLast', 'ESSEN评分')
  },

  mounted () {
    // 获取到单选的 input 元素，并存入到 arr 中
    for (
      let j = 0;
      j <
      this.$refs.reference.children[0].children[0].children[3].children.length;
      j++
    ) {
      this.multipleChoice.push(
        this.$refs.reference.children[0].children[0].children[3].children[j]
          .children[0]
      )
    }
    // 获取到 多选 的 input 框
    for (
      let j = 0;
      j <
      this.$refs.reference.children[1].children[0].children[1].children.length;
      j++
    ) {
      this.checkbox.push(
        this.$refs.reference.children[1].children[0].children[1].children[j]
          .children[0]
      )
    }

    this.allInput = this.multipleChoice.concat(this.checkbox)

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.allInput.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.allInput.map((item) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.aloneChoice(item)
    })

    // 总分
    this.totalPoints = this.totalPointsSingle + this.totalPointsMore
  },
  methods: {
    // 公共逻辑
    aloneChoice (item) {
      if (item.checked === true) {
        if (this.selectNumber.indexOf(item.id.slice(0, 1)) === -1) {
          this.selectNumber.push(item.id.slice(0, 1))
        }
        this.selected = this.selectNumber.length
        this.unselected = 2 - this.selected
        this.totalPointsSingle += item.attributes.grade.value * 1
      }
    },
    moreChoice (item) {
      if (item.checked === true) {
        this.totalPointsMore += item.attributes.grade.value * 1
        if (this.selectNumber.indexOf(item.id.slice(0, 1)) === -1) {
          this.selectNumber.push(item.id.slice(0, 1))
        } else if (item.id === '2-9') {
          this.totalPointsMore = 0
        }
        this.selected = this.selectNumber.length
        this.unselected = 2 - this.selected
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 2
      this.totalPointsSingle = 0
      this.totalPointsMore = 0
      this.selected = 0
      this.selectNumber = []
      // 选项的状态
      let optionMessage = []
      // 遍历 multipleChoice 数组
      this.multipleChoice.map((item) => {
        // 存储选项的状态
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.aloneChoice(item)
      })

      // 多选逻辑
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '2-8' &&
        e.target.attributes.id.value.substring(0, 1) === '2'
      ) {
        this.checkbox[7].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '2-8' &&
        e.target.attributes.id.value.substring(0, 1) === '2'
      ) {
        for (let i = 0; i < this.checkbox.length - 1; i++) {
          this.checkbox[i].checked = false
        }
      }

      // 遍历 checkbox 数组
      this.checkbox.map((item) => {
        // 存储选项的状态
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.moreChoice(item)
      })
      // 总分
      this.totalPoints = this.totalPointsSingle + this.totalPointsMore
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
