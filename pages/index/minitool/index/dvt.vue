<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in DVT" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ resultExplain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '深静脉血栓(DVT)Autar评估表：Wells评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '与DVT表现类似的其他疾病包括：肌肉伤、慢性水肿、浅静脉炎、血栓后综合征、关节类、静脉功能不全、蜂窝组织炎、脑窝囊肿、骨盆肿瘤、术后肿胀、多种湿杂因素。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '深静脉血栓, DVT, wells评分'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      DVT: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 619px;',
          title: '1. 年龄',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6'],
          content: [
            '1) 10-30岁：0分;',
            '2) 31-40岁：1分;',
            '3) 41-50岁：2分;',
            '4) 51-60岁：3分;',
            '5) 61-70岁：4分;',
            '6) >70岁：5分。'
          ],
          grade: ['0', '1', '2', '3', '4', '5'],
          simpleContent: ['10-30岁;', '31-40岁;', '41-50岁;', '51-60岁;', '61-70岁;', '>70岁;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '2. 体重指数（BMI）',
          id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
          content: [
            '1) 低体重 BMI<18.5：0分;',
            '2) 正常体重 BMI 18.5-22.9：1分;',
            '3) 超重 BMI 23-24.9：2分;',
            '4) 肥胖 BMI 25-29.9：3分;',
            '5) 过度肥胖 BMI >=30：4分。'
          ],
          grade: ['0', '1', '2', '3', '4'],
          simpleContent: ['低体重 BMI<18.5;', '正常体重 BMI 18.5-22.9;', '超重 BMI 23-24.9;', '肥胖 BMI 25-29.9;', '过度肥胖 BMI >=30;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '3. 运动能力',
          id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
          content: [
            '1) 自由活动：0分;',
            '2) 运动受限（需要辅助工具）：1分;',
            '3) 运动严重受限（需他人协助）：2分;',
            '4) 使用轮椅：3分;',
            '5) 绝对卧床：4分。'
          ],
          grade: ['0', '1', '2', '3', '4'],
          simpleContent: ['自由活动;', '运动受限（需要辅助工具）;', '运动严重受限（需他人协助）;', '使用轮椅;', '绝对卧床;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '4. 头部受伤',
          id: ['4-1', '4-2'],
          content: [
            '1) 有：1分;',
            '2) 无：0分。'
          ],
          grade: ['1', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '5. 胸部受伤',
          id: ['5-1', '5-2'],
          content: [
            '1) 有：1分;',
            '2) 无：0分。'
          ],
          grade: ['1', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '6. 脊柱受伤',
          id: ['6-1', '6-2'],
          content: [
            '1) 有：2分;',
            '2) 无：0分。'
          ],
          grade: ['2', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '7. 骨盆受伤',
          id: ['7-1', '7-2'],
          content: [
            '1) 有：3分;',
            '2) 无：0分。'
          ],
          grade: ['3', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '8. 下肢受伤',
          id: ['8-1', '8-2'],
          content: [
            '1) 有：4分;',
            '2) 无：0分。'
          ],
          grade: ['4', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '9. 溃疡性结肠炎',
          id: ['9-1', '9-2'],
          content: [
            '1) 有：1分;',
            '2) 无：0分。'
          ],
          grade: ['1', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '10. 红细胞增多症',
          id: ['10-1', '10-2'],
          content: [
            '1) 有：2分;',
            '2) 无：0分。'
          ],
          grade: ['2', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '11. 静脉曲张',
          id: ['11-1', '11-2'],
          content: [
            '1) 有：3分;',
            '2) 无：0分。'
          ],
          grade: ['3', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '12. 慢性心脏病',
          id: ['12-1', '12-2'],
          content: [
            '1) 有：3分;',
            '2) 无：0分。'
          ],
          grade: ['3', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '13. 急性心肌梗死',
          id: ['13-1', '13-2'],
          content: [
            '1) 有：4分;',
            '2) 无：0分。'
          ],
          grade: ['4', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '14. 恶性肿瘤',
          id: ['14-1', '14-2'],
          content: [
            '1) 有：5分;',
            '2) 无：0分。'
          ],
          grade: ['5', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '15. 脑血管疾病',
          id: ['15-1', '15-2'],
          content: [
            '1) 有：6分;',
            '2) 无：0分。'
          ],
          grade: ['6', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '16. 静脉栓塞病史',
          id: ['16-1', '16-2'],
          content: [
            '1) 有：7分;',
            '2) 无：0分。'
          ],
          grade: ['7', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '17. 如果有口服避孕药，现在年龄',
          id: ['17-1', '17-2', '17-3'],
          content: [
            '1) 未使用口服避孕药：0分;',
            '2) 20-35岁：1分;',
            '3) >35岁：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['未使用口服避孕药;', '20-35岁;', '>35岁;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '18. 激素治疗',
          id: ['18-1', '18-2'],
          content: [
            '1) 有：2分;',
            '2) 无：0分。'
          ],
          grade: ['2', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '19. 怀孕/产褥期',
          id: ['19-1', '19-2'],
          content: [
            '1) 有：3分;',
            '2) 无：0分。'
          ],
          grade: ['3', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '20. 血栓形成',
          id: ['20-1', '20-2'],
          content: [
            '1) 有：4分;',
            '2) 无：0分。'
          ],
          grade: ['4', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 819px;',
          title: '21. 外科手术(只选择其中一项)',
          id: ['21-1', '21-2', '21-3', '21-4', '21-5', '21-6', '21-7', '21-8', '21-9', '21-10'],
          content: [
            '1) 无手术：0分;',
            '2) 小手术<=30分钟：1分;',
            '3) 择期大手术：2分;',
            '4) 急诊大手术：3分;',
            '5) 胸部手术：3分;',
            '6) 腹部手术：3分;',
            '7) 泌尿系手术：3分;',
            '8) 神经系统手术：3分;',
            '9) 妇科手术：3分;',
            '10) 骨科（腰部以下）手术：4分。'
          ],
          grade: ['0', '1', '2', '3', '3', '3', '3', '3', '3', '4'],
          simpleContent: ['无手术;', '小手术<=30分钟;', '择期大手术;', '急诊大手术;', '胸部手术;', '腹部手术;', '泌尿系手术;', '神经系统手术;', '妇科手术;', '骨科（腰部以下）手术;']
        },
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 21,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return '①高风险人群入院24小时内，手术后患者即刻完成；\n②≥15分者根据活动内容的改变及时评估（至少每三天一次）；\n③＜14分者每周评估一次。'
    },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints <= 10) {
        return `${this.totalPoints}分，低风险`
      } else if (this.totalPoints > 10 && this.totalPoints <= 14) {
        return `${this.totalPoints}分，中风险`
      } else if (this.totalPoints >= 15) {
        return `${this.totalPoints}分，高风险`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '深静脉血栓(DVT)Autar评估表')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 21
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
