<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
          <Options :information="fourteenTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '汉密顿焦虑量表（HAMA) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '汉密尔顿焦虑量表（Hamilton Anxiety Scale，HAMA）是精神科临床中常用的量表之一，包括14个项目。《CCMD-3中国精神疾病诊断标准》将其列为焦虑症的重要诊断工具，临床上常将其用于焦虑症的诊断及程度划分的依据。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '汉密顿焦虑量表, 汉密尔顿焦虑量表, hama'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 焦虑心境：担心、担忧，感到有最坏的事将要发生，容易激惹',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '2. 紧张：紧张感、易疲劳、不能放松、情绪反应，易哭、颤抖、感到不安',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '3. 害怕：害怕黑暗、陌生人、一人独处、动物、乘车或旅行及人多的场合',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '4. 失眠：难以入睡、易醒、睡得不深、多梦、夜惊、醒后感疲倦',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '5. 认知功能：或称记忆、注意障碍，注意力不能集中，记忆力差',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '6. 抑郁心境：丧失兴趣、对以往爱好缺乏快感、抑郁、早醒、昼重夜轻',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '7. 躯体性焦虑-肌肉系统：肌肉酸痛、活动不灵活、肌肉抽动、肢体抽动、牙齿打颤、声音发抖',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '8. 躯体性焦虑-感觉系统：视物模糊、发冷发热、软弱无力感、浑身刺痛',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '9. 心血管系统症状：心动过速、心悸、胸痛、心管跳动感、昏倒感、心搏脱漏',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '10.呼吸系统症状：胸闷、窒息感、叹息、呼吸困难',
        id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '11.胃肠道症：吞咽困难、嗳气、消化不良（进食后腹痛、腹胀、恶心、胃部饱感）、肠动感、肠鸣、腹泻、体重减轻、便秘',
        id: ['11-1', '11-2', '11-3', '11-4', '11-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '12.生殖泌尿神经系统症状：尿意频数、尿急、停经、性冷淡、早泄、阳萎',
        id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '13.植物神经系统症状：口干、潮红、苍白、易出汗、起鸡皮疙瘩、紧张性头痛、毛发竖起',
        id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      fourteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '14.会谈时行为表现：（1）一般表现：紧张、不能松驰、忐忑不安，咬手指、紧紧握拳、摸弄手帕，面肌抽动、不宁顿足、手发抖、皱眉、表情僵硬、肌张力高，叹气样呼吸、面色苍白。（2）生理表现：吞咽、打呃、安静时心率快、呼吸快（20次/分以上）、腱反射亢进、震颤、瞳孔放大、眼睑跳动、易出汗、眼球突出',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5'],
        content: [
          '1) 无：0分;',
          '2) 轻：1分;',
          '3) 中等：2分;',
          '4) 重：3分;',
          '5) 极重：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: ['无;', '轻;', '中等;', '重;', '极重;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 14,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '汉密顿焦虑量表(HAMA)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.result()
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explain = this.explain + element.attributes.grade.value * 1
      }
    },
    // 结果展示
    result () {
      if (this.explain < 7) {
        this.totalPoints = `${this.explain}分，没有焦虑症状`
      } else if (this.explain >= 7 && this.explain <= 14) {
        this.totalPoints = `${this.explain}分，可能有焦虑`
      } else if (this.explain >= 15 && this.explain <= 21) {
        this.totalPoints = `${this.explain}分，肯定有焦虑`
      } else if (this.explain >= 22 && this.explain <= 29) {
        this.totalPoints = `${this.explain}分，肯定有明显焦虑`
      } else if (this.explain > 29) {
        this.totalPoints = `${this.explain}分，可能为严重焦虑`
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 14
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
