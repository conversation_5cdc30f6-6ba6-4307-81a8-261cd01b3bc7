<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient @update-data="ageData"></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails ></ScoringDetails>
        <!-- 评分内容详情 -->

        <div @click="getTatals($event)" class="matter" ref="reference">
          <div  class="subTitleOne">一.危险因素</div>
          <Options :information="oneTopic"></Options>
          <li  class="apache">
            <label  for="apache">2.APACHE II的生理评分(不含GCS评分):
            <input v-model="apacheVal" @input="changeApacheVal" name="APACHE II的生理评分(不含GCS评分):" type="number" id="apache" placeholder="请输入APACHE II的生理评分(不含GCS评分)"></label>
          </li>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
<!--          <div class="explain" style="white-space: pre-wrap">-->
<!--            <div class="explainContent">-->
<!--              <span class="explainContentResult">说明：</span> <p> 静态分=总分*5,随意运动分=总分*4,联动分=总分,最后得分=随意运动分-静态分-联动分。</p>-->
<!--            </div>-->
<!--          </div>-->
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import locale from "element-ui/lib/locale/lang/en";

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '蛛网膜下腔出血短期和长期结局评估(FRESH评分) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '汉密尔顿焦虑量表（Hamilton Anxiety Scale，HAMA）是精神科临床中常用的量表之一，包括14个项目。《CCMD-3中国精神疾病诊断标准》将其列为焦虑症的重要诊断工具，临床上常将其用于焦虑症的诊断及程度划分的依据。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '蛛网膜下腔出血短期和长期结局评估(FRESH评分), 汉密尔顿焦虑量表, hama'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 700px;',
        title: '1. Hunt&Hess分级',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6'],
        content: [
          '1) 未破裂动脉瘤;',
          '2) 无症状或轻微头痛;',
          '3) 中至重度头痛、脑膜刺激征、脑神经麻痹;',
          '4) 嗜睡、意识混浊、轻度局灶神经体征;',
          '5) 昏迷、中或重度偏瘫、有早期去脑强直或自主神经功能紊乱;',
          '6) 深昏迷、去大脑强直、濒死状态。',
        ],
        grade: ['1', '1.2', '1.8', '2.8', '4.2', '6'],
        simpleContent: ['未破裂动脉瘤;', '无症状或轻微头痛;', '中至重度头痛、脑膜刺激征、脑神经麻痹;', '嗜睡、意识混浊、轻度局灶神经体征;','昏迷、中或重度偏瘫、有早期去脑强直或自主神经功能紊乱','深昏迷、去大脑强直、濒死状态'],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 170px;',
        title:
          '3. 动脉瘤再出血',
        id: ['3-1', '3-2'],
        content: [
          '1) 否;',
          '2) 是。'
        ],
        grade: ['0', '0'],
        simpleContent: ['否;', '是;'],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title:
          '4. 蛛网膜下腔出血 WFNS分级',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: [
          '1) GCS评分 15分。无神经功能障碍，偏瘫和（或）失语;',
          '2) GCS评分 13-14分。无神经功能障碍，偏瘫和（或）失语;',
          '3) GCS评分 13-14分。有神经功能障碍，偏瘫和（或）失语;',
          '4) GCS评分 8-12分。有或无神经功能障碍，偏瘫和（或）失语;',
          '5) GCS评分 3-7分。有或无神经功能障碍，偏瘫和（或）失语。',
        ],
        grade: ['1.2', '1.8', '2.8', '4.2', '6'],
        simpleContent: ['GCS评分 15分。无神经功能障碍，偏瘫和（或）失语;', 'GCS评分 13-14分。无神经功能障碍，偏瘫和（或）失语;',
          'GCS评分 13-14分。有神经功能障碍，偏瘫和（或）失语;','GCS评分 8-12分。有或无神经功能障碍，偏瘫和（或）失语',
          'GCS评分 3-7分。有或无神经功能障碍，偏瘫和（或）失语'],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 170px;',
        title: '5. 入院时脑室内出血',
        id: ['5-1', '5-2'],
        content: [
          '1) 无;',
          '2) 有。',
        ],
        grade: ['0', '0'],
        simpleContent: ['无;', '有;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 4,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
      oneScore : 0,
      fourScore : 0,
      allThreeMonthScore:0,
      oneYearScore:0,
      age:0,
      apacheVal:"",
      ageValue: ''
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '蛛网膜下腔出血短期和长期结局评估(FRESH评分)')
  },

  mounted () {

    // console.log('apache')
    // 初始化 choiceBox 数组
    this.choiceBox = [];

    // 获取到所有的 input 元素，并存入到 choiceBox 中
    if (this.$refs.reference && this.$refs.reference.children) {
      for (let i = 0; i < this.$refs.reference.children.length; i++) {
        const child = this.$refs.reference.children[i];
        if (child.children && child.children[0] && child.children[0].children[3]) {
          const optionsContainer = child.children[0].children[3];
          for (let j = 0; j < optionsContainer.children.length; j++) {
            const optionElement = optionsContainer.children[j].children[0];
            if (optionElement) {
              this.choiceBox.push(optionElement);
            }
          }
        }
      }
    }

    // 登录之后从本地获取数据并勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null) {
      const savedDetails = JSON.parse(localStorage.getItem('scoringDetails'));
      this.choiceBox.forEach((item, index) => {
        if (savedDetails[index] && savedDetails[index].choice) {
          item.checked = savedDetails[index].choice;
        }
      });
    }

    // 遍历 choiceBox 数组，更新选中状态和分数
    this.choiceBox.forEach(element => {
      this.checked(element);
    });

    // 获取APACHE II的生理评分
    const apache=localStorage.getItem('apache')
    if(apache){
        document.getElementById('apache').value = apache;
        this.apacheVal=apache;
        this.allThreeMonthScore = this.calculateScore(this.oneScore, "age", "apache", 70);
        this.oneYearScore = this.calculateScore(this.fourScore, "age", "apache", 65);
        localStorage.removeItem('apache');
        // console.log(this.apacheVal,"this.apacheVal")
    }

    // 更新结果展示
    this.result();
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
        // this.explain += Number(element.attributes.grade.value);
        // console.log(this.explain,element.attributes.grade.value,"element.attributes.grade.value",element.attributes.name.value)
        // 获取 grade 值并转换为整数
        const gradeValue = element.attributes.grade.value;
        // 获取对应的标题部分题号
        const nameValue = element.attributes.name.value;

        // 在您的代码中调用优化的函数
        const match = nameValue.match(/^\d+/);
        if (match) {
          const sequenceNumber = parseInt(match[0]);

          if (sequenceNumber === 1) {
            this.oneScore = gradeValue;
            this.allThreeMonthScore = this.calculateScore(this.oneScore, "age", "apache", 70);
            console.log(this.allThreeMonthScore, "allThreeMonthScore");
          } else if (sequenceNumber === 4) {
            this.fourScore = gradeValue;
            this.oneYearScore = this.calculateScore(this.fourScore, "age", "apache", 65);
            console.log(this.oneYearScore, "oneYearScore");
          }
        }
      }
    },

    changeApacheVal(){
      this.allThreeMonthScore = this.calculateScore(this.oneScore, "age", "apache", 70);
      this.oneYearScore = this.calculateScore(this.fourScore, "age", "apache", 65);
      // console.log(this.apacheVal,"this.apacheVal")
       this.result()
    },

    ageData(data) {
      // 处理接收到的数据
      this.ageValue = data;
      this.allThreeMonthScore = this.calculateScore(this.oneScore, "age", "apache", 70);
      this.oneYearScore = this.calculateScore(this.fourScore, "age", "apache", 65);
      // console.log(this.apacheVal,"this.apacheVal")
      this.result()
    },


    // 结果展示
    result () {
      this.totalPoints = "3个月结局FRESH评分" +`${this.allThreeMonthScore}` + "分" + ",1年结局FRESH评分" +`${this.oneYearScore}`  + "分"
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      // this.explain = 0
      this.oneScore= 0
      this.fourScore=0
      this.allThreeMonthScore=0
      this.oneYearScore=0
      this.selected = 0
      this.unselected = 4
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })

      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    calculateScore(grade, ageElementId, apacheElementId, threshold) {
      let ageElement = this.ageValue;
      let apacheElement = this.apacheVal;
      if (!ageElement) { ageElement = 0; }
      if (!apacheElement) { apacheElement = 0; }

      const ageNumber = parseInt(ageElement) > threshold ? 1.8 : 0;
      const apaNumber = parseInt(apacheElement) / 5 * 0.1;
      const apaNumbers = Number(apaNumber.toFixed(1));

      let score = (Number(grade) + ageNumber + apaNumbers).toFixed(1);
      if (Number.isInteger(Number(score))) {
      score = Math.floor(score);
      }
      return score;
      },

    // 保存评分
    save () {
      // return
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        const ageElement = document.getElementById("apache").value;
        if(!ageElement){
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '评分项')
          return;
        }else{
          localStorage.setItem('apache',ageElement)
        }
        // console.log(ageElement,"ageElement")

        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        console.log(keepArguments,"keepArguments")
        // return

        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
        const ageElement = document.getElementById("apache").value;
        if(ageElement){
          localStorage.setItem('apache',ageElement)
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0 -10px 0;
}

.subTitleOne{
  padding-top: 20px;
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0;
}

.apache label{
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 32px;
  color: #333333 !important;
  font-weight: 700;
}
.apache label input{
  width: 323px;
  height: 22px;
  font-size: 14px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 6px;
  border: none;
  color: #333333;
  margin-left: 10px;
  padding-left: 10px;
  font-weight: 500;
}

/* 隐藏输入框类型为number时的右侧箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 兼容Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

</style>
