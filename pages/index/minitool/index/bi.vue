<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in changeBarthel" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">说明：</span> <p>1.该量表用来评估患者日常生活活动能力，可用于患者治疗前后的功能恢复评价。依据患者日常实际表现，不以患者可能具有的能力判断。2.总分为100分,得分越高，独立性越好，依赖性越小。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '日常生活活动能力量表(Barthel Index，BI) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '日常生活活动能力量表(Barthel Index，BI)是一种评估病人日常生活自理能力的量表，它是在Barthel指数评定量表的基础上进行改良得来的。原始的Barthel指数评定量表存在一些问题，例如对于行动能力和认知能力的评估不够全面，对于辅助工具的使用也没有考虑等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Barthel日常生活活动评分, Barthel日常生活活动指数, Barthel指数, ADL评分, 患者的日常生活功能, 日常生活活动'
        }
      ]
    }
  },
  data () {
    return {
      // 吃饭
      changeBarthel: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 吃饭',
          id: ['1-1', '1-2', '1-3'],
          content: [
            '1) 依赖;',
            '2) 需部分帮助;',
            '3) 自理。'
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['依赖;', '需部分帮助;', '自理;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 170px;',
          title: '2. 洗澡',
          id: ['2-1', '2-2'],
          content: [
            '1) 依赖;',
            '2) 自理。'
          ],
          grade: ['0', '5'],
          simpleContent: ['依赖;', '自理;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 170px;',
          title: '3. 修饰（洗脸、梳头、刷牙、剃须）',
          id: ['3-1', '3-2'],
          content: [
            '1) 需帮助;',
            '2) 自理。'
          ],
          grade: ['0', '5'],
          simpleContent: ['需帮助;', '自理;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 穿衣（解系纽扣、拉链、穿鞋凳）',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 依赖;',
            '2) 需部分帮助;',
            '3) 自理。'
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['依赖;', '需部分帮助;', '自理;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '5. 大便',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 失禁或需灌肠;',
            '2) 偶有失禁;',
            '3) 能控制。'
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['失禁或需灌肠;', '偶有失禁;', '能控制;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '6. 小便',
          id: ['6-1', '6-2', '6-3'],
          content: [
            '1) 失禁或插尿管和不能自理;',
            '2) 偶有失禁;',
            '3) 能控制。'
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['失禁或插尿管和不能自理;', '偶有失禁;', '能控制;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '7. 用厕（包括拭净、整理衣裤、冲水）',
          id: ['7-1', '7-2', '7-3'],
          content: [
            '1) 依赖;',
            '2) 需部分帮助;',
            '3) 自理。'
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['依赖;', '需部分帮助;', '自理;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 477px;',
          title: '8. 床椅转移',
          id: ['8-1', '8-2', '8-3', '8-4'],
          content: [
            '1) 完全依赖，不能坐;',
            '2) 需大量帮助（2人），能坐;',
            '3) 需少量帮助（1人）或指导;',
            '4) 自理。'
          ],
          grade: ['0', '5', '10', '15'],
          simpleContent: ['完全依赖，不能坐;', '需大量帮助（2人），能坐;', '需少量帮助（1人）或指导;', '自理;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 477px;',
          title: '9. 平地移动',
          id: ['9-1', '9-2', '9-3', '9-4'],
          content: [
            '1) 不能移动，或移动少于45米;',
            '2) 独自操纵轮椅移动超过45米，包括转弯;',
            '3) 需1人帮助步行超过45米（体力或言语指导）;',
            '4) 独立步行超过45米（可用辅助器）。'
          ],
          grade: ['0', '5', '10', '15'],
          simpleContent: ['不能移动，或移动少于45米;', '独自操纵轮椅移动超过45米，包括转弯;', '需1人帮助步行超过45米（体力或言语指导）;', '独立步行超过45米（可用辅助器）;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '10. 上楼梯',
          id: ['10-1', '10-2', '10-3'],
          content: [
            '1) 不能;',
            '2) 需帮助（体力、言语指导、辅助器）;',
            '3) 自理。',
          ],
          grade: ['0', '5', '10'],
          simpleContent: ['不能;', '需帮助（体力、言语指导、辅助器）;', '自理;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 10,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints >= 65 && this.totalPoints <=100) {
        return `${this.totalPoints}分，轻度功能障碍，能基本完成日常活动，需部分帮助。`
      } else if (this.totalPoints >= 45 && this.totalPoints <65) {
        return `${this.totalPoints}分，中度功能障碍，需很大帮助方能完成日常活动。`
      } else if (this.totalPoints >= 0 && this.totalPoints < 45) {
        return `${this.totalPoints}分，重度功能障碍，大部分日常活动不能完成或需他人服侍。`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '日常生活活动能力量表(Barthel Index，BI)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 10
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
