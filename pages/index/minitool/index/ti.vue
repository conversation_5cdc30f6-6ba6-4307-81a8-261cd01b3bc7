<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in changeBarthel" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '创伤指数(TI) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '创伤指数(TI)是一种评估病人日常生活自理能力的量表，它是在Barthel指数评定量表的基础上进行改良得来的。原始的Barthel指数评定量表存在一些问题，例如对于行动能力和认知能力的评估不够全面，对于辅助工具的使用也没有考虑等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Barthel日常生活活动评分, Barthel日常生活活动指数, Barthel指数, ADL评分, 患者的日常生活功能, 日常生活活动'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      changeBarthel: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '1. 受伤部位',
          id: ['1-1', '1-2', '1-3', '1-4'],
          content: [
            '1)四肢;',
            '2)背;',
            '3)胸;',
            '4)头、颈、腹部。'
          ],
          grade: ['1', '3', '5', '6'],
          simpleContent: ['四肢;', '背;', '胸;', '头、颈、腹部;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '2. 损伤类型',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1)撕裂伤;',
            '2)挫伤;',
            '3)刀刺伤;',
            '4)钝器或子弹、弹片伤。'
          ],
          grade: ['1', '3', '5', '6'],
          simpleContent: ['撕裂伤;', '挫伤;', '刀刺伤;', '钝器或子弹、弹片伤;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '3. 循环状态',
          id: ['3-1', '3-2', '3-3', '3-4'],
          content: [
            '1)外出血;',
            '2)血压8.0~13.3kPa，脉搏100~140次/分;',
            '3)血压8.0kPa，脉搏>140次/分;',
            '4)脉搏<55次/分。'
          ],
          grade: ['1', '3', '5', '6'],
          simpleContent: ['外出血;', '血压8.0~13.3kPa，脉搏100~140次/分;', '血压8.0kPa，脉搏>140次/分;', '脉搏<55次/分;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '4. 呼吸状态',
          id: ['4-1', '4-2', '4-3', '4-4'],
          content: [
            '1)胸痛;',
            '2)呼吸困难;',
            '3)发绀;',
            '4)呼吸停止。'
          ],
          grade: ['1', '3', '5', '6'],
          simpleContent: ['胸痛;', '呼吸困难;', '发绀;', '呼吸停止;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '5. 意识状态',
          id: ['5-1', '5-2', '5-3', '5-4'],
          content: [
            '1)倦睡',
            '2)昏呆;',
            '3)半昏迷;',
            '4)深昏迷。'
          ],
          grade: ['1', '3', '5', '6'],
          simpleContent: ['倦睡;', '昏呆;', '半昏迷;', '深昏迷;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 5,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
     if (this.totalPoints >= 17) {
        return `${this.totalPoints}分，危重`
      } else if (this.totalPoints >= 10 && this.totalPoints <= 16) {
        return `${this.totalPoints}分，重度`
      } else if (this.totalPoints >= 0 && this.totalPoints <= 9) {
        return `${this.totalPoints}分，轻度或中度损伤`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '创伤指数(TI)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 5
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
