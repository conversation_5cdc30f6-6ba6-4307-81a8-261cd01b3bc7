<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient :information="relevance"></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="AVM"></Options>
          <Options :information="hematoncus"></Options>
          <Options :information="ventricle"></Options>
        </div>
        <!-- 关联评分 -->
        <!--        <div class="relevance">-->
        <!--          <p>不知道NIHSS评分？</p>-->
        <!--          <p @click="routeSkit">去评分</p>-->
        <!--        </div>-->
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ finalScore }}</span>
          </div>
          <div class="explain" v-show="fatalitiesShow === false">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span>
              <p>{{ explain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '颅内血管动静脉畸形(AVM) Spetsler-Martin分级 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'ICH评分是常见的评价颅内出血的预后。该量表总分0~6分，根据总分值评估临床患者30天病死率，评分越高预后越差。评价指标包括入院时格拉斯哥昏迷指数（Glasgow Coma Scale，GCS）评分、血肿量、患者年龄、血肿是否破入脑室和血肿是否来自幕下这5项。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '颅内血管动静脉畸形(AVM) Spetsler-Martin分级, 非创伤性脑出血, 脑出血, PICH, 脑疝, 血肿, 脑积水, mPICH评分, 儿童脑出血预后评分, 出血破入脑室'
        }
      ]
    }
  },
  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      return this.resultsMap[this.totalPoints]
    }
  },
  data () {
    return {
      relevance: true,
      resultsMap: {
        1: '1分，I级，强烈建议实施手术治疗',
        2: '2分，II',
        3: '3分，III',
        4: '4分，IV',
        5: '5分，V级，不建议行外科手术治疗'
      },
      // AVM直径
      AVM: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '1. AVM直径',
        id: ['1-1', '1-2', '1-3'],
        content: ['1) <3cm：1分;', '2) 3-6cm：2分;', '3) >6cm：3分。'],
        grade: ['1', '2', '3'],
        simpleContent: ['<3cm;', '3-6cm;', '>6cm;'],
      },
      // AVM位置
      hematoncus: {
        bgColor: 'background: #FBFBFB;',
        width: 'width:219px;',
        title: '2. AVM位置',
        id: ['2-1', '2-2'],
        content: ['1) 非功能区：0分;', '2) 功能区：1分。'],
        grade: ['0', '1'],
        simpleContent: ['非功能区;', '功能区;'],
      },
      // AVM引流
      ventricle: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title: '3. AVM引流',
        id: ['3-1', '3-2'],
        content: ['1) 表浅静脉引流：0分;', '2) 深部静脉引流：1分。'],
        grade: ['0', '1'],
        simpleContent: ['表浅静脉引流;', '深部静脉引流;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 3,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
      // 死亡率
      fatalities: '',
      // 病亡率显示与隐藏
      fatalitiesShow: false,
      nihssScore: null,
      optionMessage: [],
      // 评分详情
      scoringDetails: [[], [], [], [], []],
      queryId: "",
      explain: "根据AVM大小、是否在功能区、有无深部静脉引流三项得分相加，评分范围为1~5，分别对应Ⅰ~Ⅴ级，分级越高，外科手术难度越大，预后越差。完全位于功能区的巨大AVM或累及下丘脑和脑干的AVM视为6级，任何方法治疗危险性都极大。\n" +
        "Spetzler-Martin分级为Ⅰ到Ⅱ级的病人被强烈建议实施手术治疗，Ⅲ级的病人推荐使用血管内栓塞术后紧随显微外科手术的方案。而Ⅳ或Ⅴ级的病人行外科切除术的并发症和死亡率显著升高，因此，不建议Ⅳ或Ⅴ级的病人行外科手术治疗。"
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', '颅内血管动静脉畸形(AVM) Spetsler-Martin分级')
  },

  mounted () {
    // 监听 popstate 事件 解决nihss然后不评分再次跳转回来 评分失败的bug
    window.addEventListener('popstate', function (event) {
      // 执行页面刷新操作
      location.reload();
    });
    // 修复返回gbm没有关联到ICH 传的问题
    if (localStorage.getItem('queryId')) {
      this.queryId = localStorage.getItem('queryId')
      localStorage.removeItem('queryId')
    }

    // 传过来的 nihss 的分数
    this.nihssScore = this.$route.query.fromPath
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }
    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 评分之后 从本地拿去数据 勾选选项
    if (
      JSON.parse(localStorage.getItem('scoringDetails')) !== null &&
      JSON.parse(localStorage.getItem('scoringDetails')).length !== 0
    ) {
      if (this.$route.query.fromPath !== undefined) {
        this.choiceBox.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
          ].choice
        })
      }
    }

    // 关联评分 跳转之后 第一题选项
    if (this.nihssScore <= 3) {
      this.choiceBox[0].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['<=3;'],
            id: '',
            scores: '0',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    } else if (this.nihssScore >= 4 && this.nihssScore <= 10) {
      this.choiceBox[1].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['4-10;'],
            id: '',
            scores: '1',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    } else if (this.nihssScore >= 11) {
      this.choiceBox[2].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['NIHSS≥11;'],
            id: '',
            scores: '2',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    }


    // 遍历 choiceBox 数组
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    // 结果展示
    this.result()
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },

    // 结果展示
    result () {
      if (this.unselected === 0) {
        this.fatalitiesShow = true
        if (this.totalPoints === 1) {
          this.fatalities = '13%'
        } else if (this.totalPoints === 2) {
          this.fatalities = '26%'
        } else if (this.totalPoints === 3) {
          this.fatalities = '72%'
        } else if (this.totalPoints === 4) {
          this.fatalities = '97%'
        } else if (this.totalPoints === 5) {
          this.fatalities = '100%'
        } else {
          this.fatalities = '0%'
        }
      }
    },
    // 去评分点击事件
    routeSkit () {
      this.$router.push({
        path: '/minitool/nihss',
        query: {
          fromPath: '/minitool/select',
        },
      })
      localStorage.setItem('className', '缺血')
      localStorage.setItem('wordLast', 'NIHSS评分')

      if (this.$route.query.id) {
        localStorage.setItem('queryId', this.$route.query.id)
      }
      this.$store.commit('minitool/setClassName', '缺血')
      this.$store.commit('minitool/setWordLast', 'NIHSS评分')
      localStorage.setItem('associatedScore', true)
      if (JSON.stringify(this.optionMessage) === null) {
        localStorage.setItem('associated', JSON.stringify(this.optionMessage))
      }
      // 去评分的状态
      localStorage.setItem('storagesubmitData', true)
    },

    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      this.optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        this.optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 判断如果是从GBM跳转过来有queryId就使用储存的Id 没有就使用原来的id
      const queryIdSign = this.queryId ? this.queryId : this.$route.query.id

      // this.$store.commit('minitool/gradeReturn', 'nihss评分')
      localStorage.removeItem('associatedScore')
      localStorage.removeItem('conditionChange')
      // 改变是否去跳转评分的状态
      this.$store.commit('minitool/setAssociatedScore', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: queryIdSign,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  position: relative;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .relevance {
    position: absolute;
    left: 125px;
    top: 66px;
    width: 168px;
    display: flex;
    font-size: 12px;
    font-weight: 400;
    font-family: 'PingFang SC';

    p:nth-of-type(1) {
      color: #888888;
    }

    p:nth-of-type(2) {
      color: #0581ce;
      cursor: pointer;
      border-bottom: 1px solid #0581ce;
    }
  }

  .result {
    width: 100%;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
