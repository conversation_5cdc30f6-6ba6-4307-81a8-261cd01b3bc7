<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Brunnstrom偏瘫功能评价法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Brunnstrom偏瘫功能评价法是一种用于评估中枢神经系统损伤（如中风）后患者的肢体功能恢复程度的常用方法。该方法根据中风患者肌肉的不同恢复过程，将功能恢复分为6个阶段。首先，评估者观察受损肢体的各个关节的运动情况。随后，通过对肌肉的被动运动来评估肌肉的张力和协调性恢复情况。然后，评估者会指导患者尝试主动运动，以评估患者的主动运动能力。此外，评估中还包括评估患者的手指、手掌和脚底反射恢复情况。通过Brunnstrom偏瘫功能评价法，可以评估中风患者不同阶段的肢体功能恢复情况，帮助康复团队量化患者的恢复进程，并制定个体化的康复计划。此方法广泛应用于康复领域，可有效促进中风患者的康复进程，提高生活能力和生活质量。'

        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Brunnstrom'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 中风患者的肢体恢复',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7'],
        content: [
          '麻痹期（Flaccid stage）：在此阶段，患者的肌肉完全无力，没有主动运动;',
          '基础阶段（Basic synergy stage）：在此阶段，患者开始有一些运动出现，但主要是通过不同肌肉群的协同运动来实现;',
          '增加主动运动阶段（Increased voluntary control stage）：在此阶段，患者开始逐渐恢复更多的主动运动，能够脱离肌肉群的协同运动;',
          '增加动作独立性阶段（Increased motion independence stage）：在此阶段，患者能够进行更多独立的运动，不再受限于特定的肌肉群协同;',
          '减少肌肉群的使用阶段（Decreased reliance on basic synergies stage）：在此阶段，患者开始减少对基本肌肉协同模式的依赖，运动能力更加自由和灵活',
          '恢复协调动作阶段（Selective control stage）：在此阶段，患者能够进行更具选择性的运动，能够在不同肌肉群之间进行精细和协调的运动',
          '功能恢复阶段（Skillful movement stage）：在此阶段，患者肌肉恢复到接近正常水平，可以实现精细、协调和复杂的运动。',
        ],
        grade: ['麻痹期（Flaccid stage）：在此阶段，患者的肌肉完全无力，没有主动运动',
                '基础阶段（Basic synergy stage）：在此阶段，患者开始有一些运动出现，但主要是通过不同肌肉群的协同运动来实现',
                '增加主动运动阶段（Increased voluntary control stage）：在此阶段，患者开始逐渐恢复更多的主动运动，能够脱离肌肉群的协同运动',
                '增加动作独立性阶段（Increased motion independence stage）：在此阶段，患者能够进行更多独立的运动，不再受限于特定的肌肉群协同',
                '减少肌肉群的使用阶段（Decreased reliance on basic synergies stage）：在此阶段，患者开始减少对基本肌肉协同模式的依赖，运动能力更加自由和灵活',
                '恢复协调动作阶段（Selective control stage）：在此阶段，患者能够进行更具选择性的运动，能够在不同肌肉群之间进行精细和协调的运动',
                '功能恢复阶段（Skillful movement stage）：在此阶段，患者肌肉恢复到接近正常水平，可以实现精细、协调和复杂的运动'],
        show: 'false',
        simpleContent: [
          '麻痹期（Flaccid stage）：在此阶段，患者的肌肉完全无力，没有主动运动;',
          '基础阶段（Basic synergy stage）：在此阶段，患者开始有一些运动出现，但主要是通过不同肌肉群的协同运动来实现B级：感觉完整，但主动运动受限，不能对躯干大肌肉进行完全控制;',
          '增加主动运动阶段（Increased voluntary control stage）：在此阶段，患者开始逐渐恢复更多的主动运动，能够脱离肌肉群的协同运动;',
          '增加动作独立性阶段（Increased motion independence stage）：在此阶段，患者能够进行更多独立的运动，不再受限于特定的肌肉群协同;',
          '减少肌肉群的使用阶段（Decreased reliance on basic synergies stage）：在此阶段，患者开始减少对基本肌肉协同模式的依赖，运动能力更加自由和灵活;',
          '恢复协调动作阶段（Selective control stage）：在此阶段，患者能够进行更具选择性的运动，能够在不同肌肉群之间进行精细和协调的运动;',
          '功能恢复阶段（Skillful movement stage）：在此阶段，患者肌肉恢复到接近正常水平，可以实现精细、协调和复杂的运动;',
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'Brunnstrom偏瘫功能评价法')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    //width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
