<template>
  <div>
    <form action="">
      <Patient :parameter="rewritePatient"></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <div @click="getTatals($event)" ref="reference">
          <Options :information="firstTopic"></Options>
          <Options :information="secondTopic"></Options>
          <Options :information="thirdlyTopic"></Options>
          <Options :information="sixthTopicthlyTopic"></Options>

        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '全面无反应量表FOUR评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '全面无反应量表（FOUR）是一种常用于评估昏迷患者意识状态的量表。它涵盖了四个方面：眼动反应、语言反应、肢体运动和呼吸模式。每一个方面都根据患者的表现进行评分，最高总分为16分。在眼动反应方面，评估者会观察患者的眼睛动态，包括自发性眨眼、注视对象和眼球活动情况。语言反应评分取决于患者的语音产出、有无发音困难和语意理解。肢体运动方面评分与患者的肌张力、肌肉活动和肢体运动的自主性有关。呼吸模式方面评分则根据患者的呼吸深度、节奏和有无呼吸困难来进行评估。FOUR评分可以提供全面、客观的昏迷患者意识状态评估。较高的得分表示患者的意识状态更好，而较低的得分可能意味着较深度的昏迷。通过FOUR评分，医护人员可以更好地了解患者的病情，判断治疗效果，并为患者制定个性化的护理计划。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '四级无反应评分量表,完全无反应评分量表,完全失去反应评分量表,四项评分量表,四标准评分量表,四项无反应评分表,四级诊断评分量表,四级昏迷评分量表,四项神经反应评分量表,全面无意识评分量表'
        }
      ]
    }
  },
  data () {
    return {
      // 1.眼反应：评估患者的眼睛运动和眼神定向，包括自发性眼睛开合、对声音和触觉刺激的眼球运动反应
      firstTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        leftOne: 'left: 108px',
        leftTwo: 'left: 120px',
        title: '1.眼反应：评估患者的眼睛运动和眼神定向，包括自发性眼睛开合、对声音和触觉刺激的眼球运动反应',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）;',
          '1分（无反应）。',
        ],
        grade: ['4', '3', '2', '1'],
        explainShow: false,
        simpleContent: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）;',
          '1分（无反应）;',
        ],
      },
      // 2.语言反应：观察患者的语言能力和反应，包括触摸和声音刺激下的语言反应，以及患者是否有交流能力和口腔活动
      secondTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '2.语言反应：观察患者的语言能力和反应，包括触摸和声音刺激下的语言反应，以及患者是否有交流能力和口腔活动',
        id: ['2-1', '2-2', '2-3','2-4'],
        content: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）;',
          '1分（无反应）。'
        ],
        grade: ['4', '3', '2','1'],
        leftOne: 'left: 142px',
        leftTwo: 'left: 154px',
        explainShow: false,
        simpleContent: ['4分（正常反应）;', '3分（受限反应）;', '2分（异常反应）;', '1分（无反应）;'],
      },
      // 3.运动反应：评估患者的肌肉张力和运动能力，包括观察患者对疼痛刺激的肢体反应、主动运动和定向运动能力。
      thirdlyTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '3.运动反应：评估患者的肌肉张力和运动能力，包括观察患者对疼痛刺激的肢体反应、主动运动和定向运动能力。',
        id: ['3-1', '3-2', '3-3', '3-4'],
        content: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）;',
          '1分（无反应）。',
        ],
        grade: ['4', '3', '2','1'],
        leftOne: 'left: 140px',
        leftTwo: 'left: 152px',
        explainShow: false,
        simpleContent: ['4分（正常反应）;', '3分（受限反应）;', '2分（异常反应）;','1分（无反应）;'],
      },
      // 4.脑干反应：评估患者的脑干功能，包括对于呼吸和眼球运动的反应
      sixthTopicthlyTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '4. 脑干反应：评估患者的脑干功能，包括对于呼吸和眼球运动的反应',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）',
          '1分（无反应）。',
        ],
        grade: ['4', '3', '2','1'],
        leftOne: 'left: 71px',
        leftTwo: 'left: 79px',
        explainShow: false,
        simpleContent: [
          '4分（正常反应）;',
          '3分（受限反应）;',
          '2分（异常反应）;',
          '1分（无反应）;'
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 4,
      choiceBox: [],
      // 去评分时患者信息
      rewritePatient: '',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '全面无反应量表FOUR评分')
  },

  mounted () {
    if (localStorage.getItem('associatedScore') === 'true') {
      this.$store.commit('minitool/setRelevanceSkip', true)
      this.rewritePatient = this.$store.state.minitool.PatientInformations
      this.$store.commit(
        'minitool/setAssociatedScore',
        localStorage.getItem('associatedScore')
      )
    }
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('relevanceScore') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('relevanceScore'))[
          index
          ].choice
      })
    }

    // 根据第一题选项选其他题目
    // this.relevance()
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    relevance () {
      this.choiceBox.map((item) => {
        if (this.choiceBox[3].checked === true) {
          switch (item.id) {
            case '2-3':
              item.checked = true
              break
            case '3-3':
              item.checked = true
              break
            case '6-4':
              item.checked = true
              break
            case '7-5':
              item.checked = true
              break
            case '8-5':
              item.checked = true
              break
            case '9-5':
              item.checked = true
              break
            case '10-5':
              item.checked = true
              break
            case '11-1':
              item.checked = true
              break
            case '12-3':
              item.checked = true
              break
            case '13-4':
              item.checked = true
              break
            case '14-3':
              item.checked = true
              break
            case '15-3':
              item.checked = true
              break
            default:
              break
          }
        }
      })
    },
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 根据第一题选项选其他题目
      // this.relevance()
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 4
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      // nihss 的选中状态 存入特殊的变量里 防止关联评分时混乱
      localStorage.setItem('relevanceScore', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
          score: this.totalPoints,
          path: this.$route.query.fromPath,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }

    .bin:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
