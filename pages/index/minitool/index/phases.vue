<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in phases" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '脑动脉瘤破裂风险评分(PHASES评分) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '适用于社区老人的独立性和轻型老年痴呆。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑动脉瘤破裂风险评分(PHASES评分), FAQ'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      phases: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 200px;',
          title: '1. 以前有蛛网膜下腔出血吗？',
          id: ['1-1', '1-2'],
          content: [
            '1) 有;',
            '2) 无。'
          ],
          grade: ['1', '0'],
          simpleContent: ['有;', '无;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 500px;',
          title: '2. 动脉瘤的位置',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1) 颈内动脉(ICA);',
            '2) 大脑前动脉/前交通动脉(ACA/ACOM);',
            '3) 大脑中动脉(MCA);',
            '4) 后交通动脉/后循环(PCOM/Posterior circulation)。'
          ],
          grade: ['0', '4', '2', '4'],
          simpleContent: ['颈内动脉(ICA);', '大脑前动脉/前交通动脉(ACA/ACOM);', '大脑中动脉(MCA);', '后交通动脉/后循环(PCOM/Posterior circulation);']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 200px;',
          title: '3. 年龄(岁)？',
          id: ['3-1', '3-2'],
          content: [
            '1) <70;',
            '2) >=70。'
          ],
          grade: ['0', '1'],
          simpleContent: ['<70;','>=70;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 350px;',
          title: '4. 所在国家',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 北美，中国，欧洲(除芬兰外);',
            '2) 日本;',
            '3) 芬兰。'
          ],
          grade: ['0', '3', '5'],
          simpleContent: ['北美，中国，欧洲(除芬兰外);', '日本;', '芬兰;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 500px;',
          title: '5. 动脉瘤大小(mm)',
          id: ['5-1', '5-2', '5-3', '5-4'],
          content: [
            '1) <6.9;',
            '2) 7.0–9.9;',
            '3) 10.0–19.9;',
            '4) ≥20.0。'
          ],
          grade: ['0', '3', '6', '10'],
          simpleContent: ['<6.9;', '7.0–9.9;', '10.0–19.9;', '≥20.0;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 200px;',
          title: '6. 是否有高血压',
          id: ['6-1', '6-2', '6-3', '6-4'],
          content: [
            '1) 是;',
            '2) 否。'
          ],
          grade: ['1', '0'],
          simpleContent: ['是;', '否;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 6,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      const riskLevels = {
        0: { points: 0, risk: 0.4, level: '低风险' },
        1: { points: 0, risk: 0.4, level: '低风险' },
        2: { points: 0, risk: 0.4, level: '低风险' },
        3: { points: 3, risk: 0.7, level: '低风险' },
        4: { points: 4, risk: 0.9, level: '高风险' },
        5: { points: 5, risk: 1.3, level: '高风险' },
        6: { points: 6, risk: 1.7, level: '高风险' },
        7: { points: 7, risk: 2.4, level: '高风险' },
        8: { points: 8, risk: 3.2, level: '高风险' },
        9: { points: 9, risk: 4.3, level: '高风险' },
        10: { points: 10, risk: 5.3, level: '高风险' },
        11: { points: 11, risk: 7.2, level: '高风险' },
        12: { points: 12, risk: 17.8, level: '高风险' },
      };

      if (this.totalPoints >= 0 && this.totalPoints <= 12) {
        const { points, risk, level } = riskLevels[this.totalPoints];
        return `${points}分，${level}，动脉瘤不稳定，5年动脉瘤破裂的风险为${risk}%。`;
      } else {
        return `${this.totalPoints}分，高风险，动脉瘤不稳定，5年动脉瘤破裂的风险为17.8%。`;
      }

    }
  },

  beforeMount () {
    localStorage.setItem('className', '动脉瘤')
    localStorage.setItem('wordLast', '脑动脉瘤破裂风险评分(PHASES评分)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 6
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
