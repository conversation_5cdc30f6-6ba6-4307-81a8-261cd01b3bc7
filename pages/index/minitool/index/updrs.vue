<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <!--          <p>一、自理能力</p>-->
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
          <Options :information="fourteenTopic"></Options>
          <Options :information="fifteenTopic"></Options>
          <Options :information="sixteenTopic"></Options>
          <Options :information="seventeenTopic"></Options>
          <Options :information="eighteenTopic"></Options>
          <Options :information="nineteenTopic"></Options>
          <Options :information="twentyATopic"></Options>
          <Options :information="twentyBTopic"></Options>
          <Options :information="twentyCTopic"></Options>
          <Options :information="twentyDTopic"></Options>
          <Options :information="twentyETopic"></Options>
          <Options :information="twentyOneATopic"></Options>
          <Options :information="twentyOneBTopic"></Options>
          <Options :information="twentyTwoATopic"></Options>
          <Options :information="twentyTwoBTopic"></Options>
          <Options :information="twentyTwoCTopic"></Options>
          <Options :information="twentyTwoDTopic"></Options>
          <Options :information="twentyTwoETopic"></Options>
          <Options :information="twentyThreeATopic"></Options>
          <Options :information="twentyThreeBTopic"></Options>
          <Options :information="twentyFourATopic"></Options>
          <Options :information="twentyFourBTopic"></Options>
          <Options :information="twentyFiveATopic"></Options>
          <Options :information="twentyFiveBTopic"></Options>
          <Options :information="twentySixATopic"></Options>
          <Options :information="twentySixBTopic"></Options>
          <Options :information="twentySevenTopic"></Options>
          <Options :information="twentyEightTopic"></Options>
          <Options :information="twentyNineTopic"></Options>
          <Options :information="thirtyTopic"></Options>
          <Options :information="thirtyOneTopic"></Options>
          <Options :information="thirtyTwoTopic"></Options>
          <Options :information="thirtyThreeTopic"></Options>
          <Options :information="thirtyFourTopic"></Options>
          <Options :information="thirtyFiveTopic"></Options>
          <Options :information="thirtySixTopic"></Options>
          <Options :information="thirtySevenTopic"></Options>
          <Options :information="thirtyEightTopic"></Options>
          <Options :information="thirtyNineTopic"></Options>
          <Options :information="fortyTopic"></Options>
          <Options :information="fortyOneTopic"></Options>
          <Options :information="fortyTwoTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '统一帕金森病评定量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '统一帕金森病评定量表（Unified Parkinson’s Disease Rating Scale, UPDRS）是一个广泛应用于临床实践和研究中的标准化工具，用于评估帕金森病（Parkinson‘s Disease, PD）患者的症状严重程度、疾病进展以及治疗效果。该量表设计全面且详细，可以量化多种与帕金森病相关的运动和非运动症状。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'UPDRS,统一帕金森评分量表,帕金森病统一评估量表'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 智力损害',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '0=无;',
          '1=轻微智力损害，持续健忘，能部分回忆过去的事件，无其他困难;',
          '2=中等记忆损害，有定向障碍，解决复杂问题有中等程度的困难，在家中生活功能有轻度但肯定的损害，有时需要鼓励;',
          '3=严重记忆损害伴时间及（经常有）地点定向障碍，解决问题有严重困难;',
          '4＝严重记忆损害，仅保留人物定向，不能作出判断或解决问题，生活需要更多的他人帮助。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻微智力损害，持续健忘，能部分回忆过去的事件，无其他困难;',
          '2=中等记忆损害，有定向障碍，解决复杂问题有中等程度的困难，在家中生活功能有轻度但肯定的损害，有时需要鼓励;',
          '3=严重记忆损害伴时间及（经常有）地点定向障碍，解决问题有严重困难;',
          '4＝严重记忆损害，仅保留人物定向，不能作出判断或解决问题，生活需要更多的他人帮助;',
        ],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '2. 思维障碍(痴呆或药物中毒)',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: [
          '0＝无;',
          '1＝生动的梦境;',
          '2＝“良性”幻觉，自知力良好;',
          '3＝偶然或经常的幻觉或妄想, 无自知力, 可能影响日常活动;',
          '4＝持续的幻觉、妄想或富于色彩的精神病, 不能自我照料。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0＝无;',
          '1＝生动的梦境;',
          '2＝“良性”幻觉，自知力良好;',
          '3＝偶然或经常的幻觉或妄想, 无自知力, 可能影响日常活动;',
          '4＝持续的幻觉、妄想或富于色彩的精神病, 不能自我照料;',
        ],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '3. 抑郁',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '0＝无;',
          '1＝悲观和内疚时间比正常多，持续时间不超过1周;',
          '2＝持续抑郁(1周或以上);',
          '3＝持续抑郁伴自主神经症状(失眠、食欲减退、体重下降、兴趣降低);',
          '4＝持续抑郁伴自主神经症状和自杀念头或意愿。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0＝无;',
          '1＝悲观和内疚时间比正常多，持续时间不超过1周;',
          '2＝持续抑郁(1周或以上);',
          '3＝持续抑郁伴自主神经症状(失眠、食欲减退、体重下降、兴趣降低);',
          '4＝持续抑郁伴自主神经症状和自杀念头或意愿;',
        ],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '4. 动力或始动力',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: [
          '0=正常;',
          '1＝比通常缺少决断力(assertive),较被动;',
          '2＝持续抑郁(1周或以上);',
          '3＝持续抑郁伴自主神经症状(失眠、食欲减退、体重下降、兴趣降低);',
          '4＝持续抑郁伴自主神经症状和自杀念头或意愿。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1＝比通常缺少决断力(assertive),较被动;',
          '2＝持续抑郁(1周或以上);',
          '3＝持续抑郁伴自主神经症状(失眠、食欲减退、体重下降、兴趣降低);',
          '4＝持续抑郁伴自主神经症状和自杀念头或意愿;',
        ],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '5. 言语(接受)',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: [
          '0=正常;',
          '1=轻微受影响，无听懂困难;',
          '2=中度受影响，有时要求重复才听懂;',
          '3=严重受影响，经常要求重复才听懂;',
          '4=经常不能理解。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻微受影响，无听懂困难;',
          '2=中度受影响，有时要求重复才听懂;',
          '3=严重受影响，经常要求重复才听懂;',
          '4=经常不能理解;',
        ],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '6. 唾液分泌',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: [
          '0=正常;',
          '1=口腔内唾液分泌轻微但肯定增多，可能有夜间流涎;',
          '2=中等程度的唾液分泌过多，可能有轻微流涎;',
          '3=明显过多的唾液伴流涎;',
          '4=明显流涎,需持续用纸巾或手帕擦拭。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=口腔内唾液分泌轻微但肯定增多，可能有夜间流涎;',
          '2=中等程度的唾液分泌过多，可能有轻微流涎;',
          '3=明显过多的唾液伴流涎;',
          '4=明显流涎,需持续用纸巾或手帕擦拭;',
        ],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '7. 吞咽',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '0=正常;',
          '1=极少呛咳;',
          '2=偶然呛咳;',
          '3=需进软食;',
          '4=需要鼻饲或胃造瘘进食。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=极少呛咳;',
          '2=偶然呛咳;',
          '3=需进软食;',
          '4=需要鼻饲或胃造瘘进食;',
        ],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '8. 书写',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '0=正常;',
          '1=轻微缓慢或字变小;',
          '2=中度缓慢或字变小，所有字迹均清楚;',
          '3=严重受影响，不是所有字迹均清楚;',
          '4=大多数字迹不清楚。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻微缓慢或字变小;',
          '2=中度缓慢或字变小，所有字迹均清楚;',
          '3=严重受影响，不是所有字迹均清楚;',
          '4=大多数字迹不清楚;',
        ],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '9. 切割食物和使用餐具',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '0=正常;',
          '1=稍慢和笨拙，但不需要帮助;',
          '2=尽管慢和笨拙，但能切割多数食物，需要某种程度的帮助;',
          '3=需要他人帮助切割食物，但能自己缓慢进食;',
          '4=需要喂食。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=稍慢和笨拙，但不需要帮助;',
          '2=尽管慢和笨拙，但能切割多数食物，需要某种程度的帮助;',
          '3=需要他人帮助切割食物，但能自己缓慢进食;',
          '4=需要喂食;',
        ],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '10.着装',
        id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
        content: [
          '0=正常;',
          '1=略慢，不需帮助;',
          '2=偶尔需要帮助扣扣及将手臂放进袖里;',
          '3=需要相当多的帮助,但还能独立做某些事情;',
          '4=完全需要帮助。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=略慢，不需帮助;',
          '2=偶尔需要帮助扣扣及将手臂放进袖里;',
          '3=需要相当多的帮助,但还能独立做某些事情;',
          '4=完全需要帮助;',
        ],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:'11.个人卫生',
        id: ['11-1', '11-2', '11-3', '11-4', '11-5'],
        content: [
          '0=正常;',
          '1=稍慢，但不需要帮助;',
          '2=需要帮助淋浴或盆浴，或做个人卫生很慢;',
          '3=洗脸、刷牙、梳头及洗澡均需帮助;',
          '4=保留导尿或其他机械帮助。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=稍慢，但不需要帮助;',
          '2=需要帮助淋浴或盆浴，或做个人卫生很慢;',
          '3=洗脸、刷牙、梳头及洗澡均需帮助;',
          '4=保留导尿或其他机械帮助;',
        ],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '12.翻身和整理床单',
        id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
        content: [
          '0=正常;',
          '1=稍慢且笨拙，但无需帮助;',
          '2=能独立翻身或整理床单，但很困难;',
          '3=能起始，但不能完成翻身或整理床单;',
          '4=完全需要帮助。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=稍慢且笨拙，但无需帮助;',
          '2=能独立翻身或整理床单，但很困难;',
          '3=能起始，但不能完成翻身或整理床单;',
          '4=完全需要帮助;',
        ],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '13.跌跤(与冻结“freezing”无关者)后',
        id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
        content: [
          '0=无;',
          '1=偶有;',
          '2=有时有，少于每天1次;',
          '3=平均每天1次;',
          '4=多于每天1次。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=偶有;',
          '2=有时有，少于每天1次;',
          '3=平均每天1次;',
          '4=多于每天1次;',
        ],
      },
      fourteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '14.行走中冻结',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5'],
        content: [
          '0=无;',
          '1=少见，可有启动困难;',
          '2=有时有冻结;',
          '3=经常有，偶有因冻结跌跤;',
          '4=经常因冻结跌交。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=少见，可有启动困难;',
          '2=有时有冻结;',
          '3=经常有，偶有因冻结跌跤;',
          '4=经常因冻结跌交;',
        ],
      },
      fifteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '15.行走',
        id: ['15-1', '15-2', '15-3', '15-4', '15-5'],
        content: [
          '0=正常;',
          '1=轻微困难，可能上肢不摆动或倾向于拖步;',
          '2=中度困难，但稍需或不需帮助;',
          '3=严重行走困难，需要帮助;',
          '4=即使给予帮助也不能行走。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻微困难，可能上肢不摆动或倾向于拖步;',
          '2=中度困难，但稍需或不需帮助;',
          '3=严重行走困难，需要帮助;',
          '4=即使给予帮助也不能行走;',
        ],
      },
      sixteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '16.震颤',
        id: ['16-1', '16-2', '16-3','16-4', '16-5'],
        content: [
          '0=无;',
          '1=轻微，不常有;',
          '2=中度，感觉烦恼;',
          '3=严重，许多活动受影响;',
          '4=明显，大多数活动受影响。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻微，不常有;',
          '2=中度，感觉烦恼;',
          '3=严重，许多活动受影响;',
          '4=明显，大多数活动受影响;',
        ],
      },
      seventeenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '17.与帕金森病有关的感觉主诉',
        id: ['17-1', '17-2', '17-3', '17-4', '17-5'],
        content: [
          '0=无;',
          '1=偶然有麻木、麻刺感或轻微疼痛',
          '2=经常有麻木、麻刺感或轻微疼痛，不痛苦,',
          '3=经常的痛苦感;',
          '4=极度的痛苦感。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=偶然有麻木、麻刺感或轻微疼痛;',
          '2=经常有麻木、麻刺感或轻微疼痛，不痛苦;',
          '3=经常的痛苦感;',
          '4=极度的痛苦感;',
        ],
      },
      eighteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '18.言语(表达)',
        id: ['18-1', '18-2', '18-3','18-4', '18-5'],
        content: [
          '0=正常;',
          '1=表达、理解和(或)音量轻度下降;',
          '2=单音调，含糊但可听懂，中度受损;',
          '3=明显损害，难以听懂;',
          '4=无法听懂。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=表达、理解和(或)音量轻度下降;',
          '2=单音调，含糊但可听懂，中度受损;',
          '3=明显损害，难以听懂;',
          '4=无法听懂;',
        ],
      },
      nineteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '19.面部表情',
        id: ['19-1', '19-2', '19-3', '19-4', '19-5'],
        content: [
          '0=正常;',
          '1=略呆板，可能是正常的“面无表情”',
          '2=轻度但肯定是面部表情差,',
          '3=中度表情呆板，有时张口;',
          '4=面具脸，几乎完全没有表情，口张开在1/4英寸( 0. 6 cm)或以上。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=略呆板，可能是正常的“面无表情”;',
          '2=轻度但肯定是面部表情差;',
          '3=中度表情呆板，有时张口;',
          '4=面具脸，几乎完全没有表情，口张开在1/4英寸( 0. 6 cm)或以上;',
        ],
      },
      twentyATopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '20a.静止性震颤(面部、嘴唇、下颌)',
        id: ['20-1', '20-2', '20-3','20-4', '20-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '4=幅度大，多数时间出现。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '44=幅度大，多数时间出现;',
        ],
      },

      twentyBTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '20b.静止性震颤(右上肢)',
        id: ['21-1', '21-2', '21-3', '21-4', '21-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '4=幅度大，多数时间出现。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '44=幅度大，多数时间出现;',
        ],
      },
      twentyCTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '20c.静止性震颤(左上肢)',
        id: ['22-1', '22-2', '22-3', '22-4', '22-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '4=幅度大，多数时间出现。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '44=幅度大，多数时间出现;',
        ],
      },
      twentyDTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '20d.静止性震颤(右下肢)',
        id: ['23-1', '23-2', '23-3', '23-4', '23-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '4=幅度大，多数时间出现。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '44=幅度大，多数时间出现;',
        ],
      },
      twentyETopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '20e.静止性震颤(左下肢)',
        id: ['24-1', '24-2', '24-3', '24-4', '24-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '4=幅度大，多数时间出现。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度小而持续，或中等幅度间断出现;',
          '3=幅度中等，多数时间出现;',
          '44=幅度大，多数时间出现;',
        ],
      },
      twentyOneATopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '21a.手部动作性或姿势性震颤(右上肢)',
        id: ['25-1', '25-2', '25-3', '25-4', '25-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度中等，活动时出现;',
          '3=幅度中等，持物或活动时出现;',
          '4=幅度大，影响进食。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度中等，活动时出现;',
          '3=幅度中等，持物或活动时出现;',
          '4=幅度大，影响进食;',
        ],
      },
      twentyOneBTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '21b.手部动作性或姿势性震颤(左上肢)',
        id: ['26-1', '26-2', '26-3', '26-4', '26-5'],
        content: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度中等，活动时出现;',
          '3=幅度中等，持物或活动时出现;',
          '4=幅度大，影响进食。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度,有时出现;',
          '2=幅度中等，活动时出现;',
          '3=幅度中等，持物或活动时出现;',
          '4=幅度大，影响进食;',
        ],
      },
      twentyTwoATopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '22a.强直(患者取坐位，放松，以大关节的被动活动来判断，可以忽略“齿轮样感觉”；颈部)',
        id: ['27-1', '27-2', '27-3', '27-4', '27-5'],
        content: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出',
          '2=轻到中度,',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出;',
          '2=轻到中度;',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限;',
        ],
      },
      twentyTwoBTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '22b.强直(患者取坐位，放松，以大关节的被动活动来判断，可以忽略“齿轮样感觉”；右上肢)',
        id: ['28-1', '28-2', '28-3', '28-4', '28-5'],
        content: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出',
          '2=轻到中度,',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出;',
          '2=轻到中度;',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限;',
        ],
      },
      twentyTwoCTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '22c.强直(患者取坐位，放松，以大关节的被动活动来判断，可以忽略“齿轮样感觉”；左上肢)',
        id: ['29-1', '29-2', '29-3', '29-4', '29-5'],
        content: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出',
          '2=轻到中度,',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出;',
          '2=轻到中度;',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限;',
        ],
      },
      twentyTwoDTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '22d.强直(患者取坐位，放松，以大关节的被动活动来判断，可以忽略“齿轮样感觉”；右下肢)',
        id: ['30-1', '30-2', '30-3', '30-4', '30-5'],
        content: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出',
          '2=轻到中度,',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出;',
          '2=轻到中度;',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限;',
        ],
      },
      twentyTwoETopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '22e.强直(患者取坐位，放松，以大关节的被动活动来判断，可以忽略“齿轮样感觉”；左下肢',
        id: ['31-1', '31-2', '31-3', '31-4', '31-5'],
        content: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出',
          '2=轻到中度,',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=轻度，或仅在镜像运动及加强试验时可查出;',
          '2=轻到中度;',
          '3=明显，但活动范围不受限;',
          '4=严重，活动范围受限;',
        ],
      },
      twentyThreeATopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '23a.手指拍打试验(拇食指尽可能大幅度、快速地做连续对掌动作；右手)',
        id: ['32-1', '32-2', '32-3','32-4', '32-5'],
        content: [
          '0=正常(≥15次/5秒);',
          '1=轻度减慢和(或)幅度减小( 11～ 14次/5秒);',
          '2=中等障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿( 7～10次/秒);',
          '3=严重障碍，动作起始困难或运动中有停顿(3～6次/5秒);',
          '4=几乎不能执行动作( 0～2次/5秒)。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常(≥15次/5秒);',
          '1=轻度减慢和(或)幅度减小(11～ 14次/5秒);',
          '2=中等障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿( 7～10次/秒);',
          '3=严重障碍，动作起始困难或运动中有停顿(3～6次/5秒);',
          '4=几乎不能执行动作(0～2次/5秒);',
        ],
      },
      twentyThreeBTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '23b.手指拍打试验(拇食指尽可能大幅度、快速地做连续对掌动作；左手)',
        id: ['32-1', '32-2', '32-3','32-4', '32-5'],
        content: [
          '0=正常(≥15次/5秒);',
          '1=轻度减慢和(或)幅度减小( 11～ 14次/5秒);',
          '2=中等障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿( 7～10次/秒);',
          '3=严重障碍，动作起始困难或运动中有停顿(3～6次/5秒);',
          '4=几乎不能执行动作( 0～2次/5秒)。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常(≥15次/5秒);',
          '1=轻度减慢和(或)幅度减小(11～ 14次/5秒);',
          '2=中等障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿( 7～10次/秒);',
          '3=严重障碍，动作起始困难或运动中有停顿(3～6次/5秒);',
          '4=几乎不能执行动作(0～2次/5秒);',
        ],
      },
      twentyFourATopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '24a.手运动(尽可能大幅度地做快速连续的伸掌握拳动作，两手分别做；右手)',
        id: ['34-1', '34-2', '34-3','34-4', '34-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },
      twentyFourBTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '24b.手运动(尽可能大幅度地做快速连续的伸掌握拳动作，两手分别做；左手)',
        id: ['34-1', '34-2', '34-3','34-4', '34-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },
      twentyFiveATopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '25a.轮替动作(两手垂直或水平作最大幅度的旋前和旋后动作，双手同时动作；右手)',
        id: ['36-1', '36-2', '36-3','36-4', '36-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },
      twentyFiveBTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '25b.轮替动作(两手垂直或水平作最大幅度的旋前和旋后动作，双手同时动作；左手)',
        id: ['36-1', '36-2', '36-3','36-4', '36-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },
      twentySixATopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '26a.腿部灵活性(连续快速地脚后跟踏地，腿完全抬高，幅度约为3英寸；右下肢)',
        id: ['38-1', '38-2', '38-3','38-4', '38-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },

      twentySixBTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '26b.腿部灵活性(连续快速地脚后跟踏地，腿完全抬高，幅度约为3英寸；左下肢)',
        id: ['38-1', '38-2', '38-3','38-4', '38-5'],
        content: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=轻度减慢或幅度减小;',
          '2=中度障碍，有肯定的早期疲劳现象，运动中可以有偶尔的停顿;',
          '3=严重障碍，动作起始时经常犹豫或运动中有停顿;',
          '4=几乎不能执行动作;',
        ],
      },

      twentySevenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '27.起立(患者双手臂抱胸从直背木或金属椅子站起)',
        id: ['40-1', '40-2', '40-3','40-4', '40-5'],
        content: [
          '0=正常;',
          '1=缓慢，或可能需要试1次以上;',
          '2=需扶扶手站起;',
          '3=向后倒的倾向，必须试几次才能站起，但不需帮助;',
          '4=没有帮助不能站起。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=缓慢，或可能需要试1次以上;',
          '2=需扶扶手站起;',
          '3=向后倒的倾向，必须试几次才能站起，但不需帮助;',
          '4=没有帮助不能站起;',
        ],
      },
      twentyEightTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '28.姿势',
        id: ['41-1', '41-2', '41-3','41-4', '41-5'],
        content: [
          '0=正常直立;',
          '1=不很直，轻度前倾，可能是正常老年人的姿势;',
          '2=中度前倾，肯定是不正常，可能有轻度的向一侧倾斜;',
          '3=严重前倾伴脊柱后突，可能有中度的向一侧倾斜;',
          '4=显著屈曲，姿势极度异常。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常直立;',
          '1=不很直，轻度前倾，可能是正常老年人的姿势;',
          '2=中度前倾，肯定是不正常，可能有轻度的向一侧倾斜;',
          '3=严重前倾伴脊柱后突，可能有中度的向一侧倾斜;',
          '4=显著屈曲，姿势极度异常;',
        ],
      },
      twentyNineTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '29.步态',
        id: ['42-1', '42-2', '42-3','42-4', '42-5'],
        content: [
          '0=正常;',
          '1=行走缓慢，可有曳步，步距小，但无慌张步态或前冲步态;',
          '2=行走困难，但还不需要帮助，可有某种程度的慌张步态、小步或前冲;',
          '3=严重异常步态，行走需帮助;',
          '4=不借助外界帮助不能站立。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=行走缓慢，可有曳步，步距小，但无慌张步态或前冲步态;',
          '2=行走困难，但还不需要帮助，可有某种程度的慌张步态、小步或前冲;',
          '3=严重异常步态，行走需帮助;',
          '4=不借助外界帮助不能站立;',
        ],
      },
      thirtyTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '30.姿势的稳定性(突然向后拉双肩时所引起姿势反应，患者应睁眼直立，双脚略分开并做好准备)',
        id: ['43-1', '43-2', '43-3','43-4', '43-5'],
        content: [
          '0=正常;',
          '1=后倾，无需帮助可自行恢复;',
          '2=无姿势反应，如果不扶可能摔倒;',
          '3=非常不，有自发的失去平衡现象;',
          '4=不借助外界帮助不能站立。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=正常;',
          '1=后倾，无需帮助可自行恢复;',
          '2=无姿势反应，如果不扶可能摔倒;',
          '3=非常不，有自发的失去平衡现象;',
          '4=不借助外界帮助不能站立;',
        ],
      },
      thirtyOneTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '31.躯体少动(梳头缓慢，手臂摆动减少，幅度减小，整体活动减少)',
        id: ['44-1', '44-2', '42-3','42-4', '42-5'],
        content: [
          '0=无;',
          '1=略慢，似乎是故意的，在某些人可能是正常的，幅度可能减小;',
          '2=运动呈轻度缓慢和减少，肯定不正常，或幅度减小;',
          '3=中度缓慢，运动缺乏或幅度小;',
          '4=明显缓慢，运动缺乏或幅度小。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1=略慢，似乎是故意的，在某些人可能是正常的，幅度可能减小;',
          '2=运动呈轻度缓慢和减少，肯定不正常，或幅度减小;',
          '3=中度缓慢，运动缺乏或幅度小;',
          '4=明显缓慢，运动缺乏或幅度小;',
        ],
      },
      thirtyTwoTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '32.持续时间：（异动症存在时间所占1天觉醒状态时间的比例-病史信息）',
        id: ['45-1', '45-2', '45-3','45-4', '45-5'],
        content: [
          '0＝无;',
          '1＝1%～25％;',
          '2＝26%～50％;',
          '3＝51%～75％;',
          '4＝76%～100％。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0＝无;',
          '1＝1%～25％;',
          '2＝26%～50％;',
          '3＝51%～75％;',
          '4＝76%～100％;',
        ],
      },
      thirtyThreeTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '33.残疾（异动症所致残疾的程度——病史信息，可经诊室检查修正）',
        id: ['46-1', '46-2', '46-3','46-4', '46-5'],
        content: [
          '0＝无残疾;',
          '1＝轻度残疾;',
          '2＝中度残疾;',
          '3＝严重残疾;',
          '4＝完全残疾。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0＝无残疾;',
          '1＝轻度残疾;',
          '2＝中度残疾;',
          '3＝严重残疾;',
          '4＝完全残疾;',
        ],
      },
      thirtyFourTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '34.痛性异动症所致疼痛的程度',
        id: ['47-1', '47-2', '47-3','47-4', '47-5'],
        content: [
          '0＝无痛性异动症;',
          '1＝轻微;',
          '2＝中度;',
          '3＝严重;',
          '4＝极度。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0＝无痛性异动症;',
          '1＝轻微;',
          '2＝中度;',
          '3＝严重;',
          '4＝极度;',
        ],
      },
      thirtyFiveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 275px;',
        title: '35.清晨肌张力障碍',
        id: ['48-1', '48-2'],
        content: [
          '0＝无;',
          '1＝有。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0＝无;',
          '1＝有;',
        ],
      },
      thirtySixTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 275px;',
        title: '36.“关”是否能根据服药时间预测',
        id: ['49-1', '49-2'],
        content: [
          '0＝不能;',
          '1＝能。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0＝不能;',
          '1＝能;',
        ],
      },
      thirtySevenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 275px;',
        title: '37.“关”是否不能根据服药时间预测',
        id: ['50-1', '50-2'],
        content: [
          '0＝不是;',
          '1＝是。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0＝不是;',
          '1＝能;',
        ],
      },
      thirtyEightTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 275px;',
        title: '38.“关”是否会突然出现（几秒钟内）',
        id: ['51-1', '5-2'],
        content: [
          '0=不会;',
          '1=会。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0=不会;',
          '1=会;',
        ],
      },
      thirtyNineTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '39.“关”平均所占每天觉醒状态时间的比例',
        id: ['52-1', '52-2', '52-3','52-4', '52-5'],
        content: [
          '0＝无;',
          '1＝1%～25％;',
          '2＝26%～50％;',
          '3＝51%～75％;',
          '4＝76%～100％。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '0=无;',
          '1＝1%～25％;',
          '2＝26%～50％;',
          '3＝51%～75％;',
          '4＝76%～100％;',
        ],
      },
      fortyTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 275px;',
        title: '40.患者有无食欲减退、恶心或呕吐',
        id: ['53-1', '53-2'],
        content: [
          '0=无;',
          '1=有。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0=无;',
          '1=有;',
        ],
      },
      fortyOneTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 275px;',
        title: '41.患者是否有睡眠障碍（如失眠或睡眠过多）',
        id: ['54-1', '54-2'],
        content: [
          '0＝无;',
          '1=有。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0=无;',
          '1=有;',
        ],
      },
      fortyTwoTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 275px;',
        title: '42.站立时是否有低血压或感觉头晕',
        id: ['55-1', '55-2'],
        content: [
          '0=无;',
          '1=有。'
        ],
        grade: ['0', '1'],
        simpleContent: [
          '0=无;',
          '1=有;',
        ],
      },
      // reference: '',
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 55,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', '统一帕金森病评定量表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.result()
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explain =
          this.explain + element.attributes.grade.value * 1
      }
    },
    // 结果展示
    result () {
      this.totalPoints = `${this.explain}分`
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 55
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0;
}
</style>
