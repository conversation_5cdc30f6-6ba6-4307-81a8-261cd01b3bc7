<template>
  <div>
    <form action=''>
      <Patient></Patient>
      <div class='particulars'>
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div ref='reference' @click='getTatals($event)'>
          <Options :information='LevelDescription'></Options>
        </div>
        <!-- 结果展示 -->
        <div class='result'>
          <div class='grade'>
            结果：<span v-show='selected === 0'>暂无</span>
            <span v-show='selected !== 0'>{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show='unselected !== 0' class='explain'>
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class='save'>
          <div :class='{ scoreBtn: unselected === 0 }' class='btn' @click='save'>
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'GOS评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '评分等级描述：5恢复良好恢复正常生活，尽管有轻度缺陷；4轻度残疾残疾但可独立生活；能在保护下工作；3重度残疾清醒、残疾，日常生活需要照料；2植物生存仅有最小反应(如随着睡眠/清醒周期，眼睛能睁开）；1死亡。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Glasgow Outcome Scale, 格拉斯哥预后评分, gos, 疼痛刺激定位反应, 继发性血肿, 继发性脑梗塞, 缺血性脑卒中, 创伤性脑损伤, tbi, 颅脑损伤, 神经功能预后'
        }
      ]
    }
  },
  data () {
    return {
      // 评分等级描述
      LevelDescription: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title: '1. 评分等级描述',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '1) 恢复良好恢复正常生活，尽管有轻度缺陷：5分;',
          '2) 轻度残疾残疾但可独立生活；能在保护下工作：4分;',
          '3) 重度残疾清醒、残疾，日常生活需要照料：3分;',
          '4) 植物生存仅有最小反应(如随着睡眠/清醒周期，眼睛能睁开）：2分;',
          '5) 死亡：1分。'
        ],
        grade: ['5', '4', '3', '2', '1'],
        simpleContent: [
          '恢复良好恢复正常生活，尽管有轻度缺陷;',
          '轻度残疾残疾但可独立生活；能在保护下工作;',
          '重度残疾清醒、残疾，日常生活需要照料;',
          '植物生存仅有最小反应(如随着睡眠/清醒周期，眼睛能睁开）;',
          '死亡;'
        ]
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 1,
      choiceBox: []
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'GOS评分')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 1
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        optionMessage.push({
          choice: element.checked
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
          $analysys: this.$analysys
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    }
  }
}
</script>

<style lang='less' scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      color: #ffffff;
      background: #0581ce;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
