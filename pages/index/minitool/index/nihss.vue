<template>
  <div>
    <form action="">
      <Patient :parameter="rewritePatient"></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <div @click="getTatals($event)" ref="reference">
          <Options :information="firstTopic"></Options>
          <Options :information="secondTopic"></Options>
          <Options :information="thirdlyTopic"></Options>
          <Options :information="sixthTopicthlyTopic"></Options>
          <Options :information="fifthTopic"></Options>
          <Options :information="sixthTopic"></Options>
          <Options :information="seventhTopic"></Options>
          <Options :information="eighthTopic"></Options>
          <Options :information="ninthTopic"></Options>
          <Options :information="tenthTopic"></Options>
          <Options :information="eleventhTopic"></Options>
          <Options :information="theTwelfthTopic"></Options>
          <Options :information="language"></Options>
          <Options :information="dysarthria"></Options>
          <Options :information="neglect"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'NIHSS评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'NIHSS是美国国立卫生研究院卒中量表(National Institute of Health stroke scale )的简称。它是从三个量表（Toronto Stroke Scale，Oxbury Initial Severity Scale，Cincinnati Stroke Scale）中选取有意义的项目组成一个量表，包含每个主要脑动脉病变可能出现的神经系统检查项目，增加了从Edin-burg-2昏迷量表中选取的两个项目来补充精神状态检查。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'NIHSS, 脑梗死, mca, AIS-LVO患者, 血管闭塞, BAO, 梗死, 出血性卒中, 卒中患者'
        }
      ]
    }
  },
  data () {
    return {
      // 1a.意识水平
      firstTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        leftOne: 'left: 92px',
        leftTwo: 'left: 120px',
        title: '1a.意识水平',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '1) 清醒，反应灵敏：0分;',
          '2) 嗜睡，轻微刺激能唤醒，可回答问题，执行指令：1分;',
          '3) 昏睡或反应迟钝，需反复刺激、强烈或疼痛刺激才有非刻板的反应：2分;',
          '4) 昏迷，仅有反射性活动或自发性反应或完全无反应、软瘫、无反射：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        explain:
          '即使不能全面评价（如气管插管、语言障碍、气管创伤及绷带包扎等），检查者也必须选择1个反应。只在患者对有害刺激无反应时（不是反射）才能记录3分。',
        explainShow: false,
        simpleContent: [
          '清醒，反应灵敏;',
          '嗜睡，轻微刺激能唤醒，可回答问题，执行指令;',
          '昏睡或反应迟钝，需反复刺激、强烈或疼痛刺激才有非刻板的反应;',
          '昏迷，仅有反射性活动或自发性反应或完全无反应、软瘫、无反射;',
        ],
      },
      // 1b.意识水平提问
      secondTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '1b.意识水平提问',
        id: ['2-1', '2-2', '2-3'],
        content: [
          '1) 两项均正确：0分;',
          '2) 一项正确：1分;',
          '3) 两项均不正确：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 124px',
        leftTwo: 'left: 154px',
        explain:
          '月份、年龄。仅对初次回答评分。失语和昏迷者不能理解问题记2分，因气管插管、气管创伤、严重构音障碍、语言障碍或其他任何原因不能完成者（非失语所致）记1分。可书面回答。',
        explainShow: false,
        simpleContent: ['两项均正确;', '一项正确;', '两项均不正确;'],
      },
      // 1c.意识水平指令
      thirdlyTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '1c.意识水平指令',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '1) 两项均正确：0分;',
          '2) 一项正确：1分;',
          '3) 两项均不正确：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 124px',
        leftTwo: 'left: 152px',
        explain:
          '睁闭眼；非瘫痪侧握拳松开。仅对最初反应评分，有明确努力但未完成的也给分。若对指令无反应，用动作示意，然后记录评分。对创伤、截肢或其他生理缺陷者，应予适当的指令。',
        explainShow: false,
        simpleContent: ['两项均正确;', '一项正确;', '两项均不正确;'],
      },
      // 2.凝视
      sixthTopicthlyTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '2. 凝视',
        id: ['4-1', '4-2', '4-3'],
        content: [
          '1) 正常：0分;',
          '2) 部分凝视麻痹（单眼或双眼凝视异常，但无强迫凝视或完全凝视麻痹）：1分;',
          '3) 强迫凝视或完全凝视麻痹（不能被头眼反射克服）：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 54px',
        leftTwo: 'left: 79px',
        explain:
          '只测试水平眼球运动。对随意或反射性眼球运动记分。若眼球偏斜能被随意或反射性活动纠正，记1分。若为孤立的周围性眼肌麻痹记1分。对失语者，凝视是可以测试的。对眼球创伤、绷带包扎、盲人或有其他视力、视野障碍者，由检查者选择一种反射性运动来测试，确定眼球的联系，然后从一侧向另一侧运动，偶尔能发现部分性凝视麻痹。',
        explainShow: false,
        simpleContent: [
          '正常;',
          '部分凝视麻痹（单眼或双眼凝视异常，但无强迫凝视或完全凝视麻痹）;',
          '强迫凝视或完全凝视麻痹（不能被头眼反射克服）;',
        ],
      },
      // 3.视野
      fifthTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '3. 视野',
        id: ['5-1', '5-2', '5-3', '5-4'],
        content: [
          '1) 无视野缺损：0分;',
          '2) 部分偏盲：1分;',
          '3) 完全偏盲：2分;',
          '4) 双侧偏盲（包括皮质盲）：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        leftOne: 'left: 54px',
        leftTwo: 'left: 79px',
        explain:
          '若能看到侧面的手指，记录正常，若单眼盲或眼球摘除，检查另一只眼。明确的非对称盲（包括象限盲），记1分。若全盲（任何原因）记3分。若频临死亡记1分，结果用于回答问题11。',
        explainShow: false,
        simpleContent: [
          '无视野缺损;',
          '部分偏盲;',
          '完全偏盲;',
          '双侧偏盲（包括皮质盲）;',
        ],
      },
      // 4.面瘫
      sixthTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '4. 面瘫',
        id: ['6-1', '6-2', '6-3', '6-4'],
        content: [
          '1) 正常：0分;',
          '2) 轻微（微笑时鼻唇沟变平、不对称）：1分;',
          '3) 部分（下面部完全或几乎完全瘫痪）：2分;',
          '4) 完全（单或双侧瘫痪，上下面部缺乏运动）：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: [
          '正常;',
          '轻微（微笑时鼻唇沟变平、不对称）;',
          '部分（下面部完全或几乎完全瘫痪）;',
          '完全（单或双侧瘫痪，上下面部缺乏运动）;',
        ],
      },
      // 5a.左上肢运动
      seventhTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 600px;',
        title: '5a.左上肢运动',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5', '7-6'],
        content: [
          '1) 无下落，置肢体于90°（或45°坚持10 秒）：0分;',
          '2) 能抬起但不能坚持10秒，下落时不撞击床或其他支持物：1分;',
          '3) 试图抵抗重力，但不能维持坐位90°或仰位45°：2分;',
          '4) 不能抵抗重力，肢体快速下落：3分;',
          '5) 无运动/昏迷：4分;',
          '6) 截肢或关节融合：9分。',
        ],
        grade: ['0', '1', '2', '3', '4', '9'],
        leftOne: 'left: 106px',
        leftTwo: 'left: 137px',
        explain:
          '置左上肢于合适的位置：坐位时左上肢平举90°，仰卧时上抬45°，掌心向下，若上肢在10秒内下落，记1～4分。对失语者用语言或动作鼓励，不用有害刺激。',
        explainShow: false,
        simpleContent: [
          '无下落，置肢体于90°（或45°坚持10 秒）;',
          '能抬起但不能坚持10秒，下落时不撞击床或其他支持物;',
          '试图抵抗重力，但不能维持坐位90°或仰位45°;',
          '不能抵抗重力，肢体快速下落;',
          '无运动/昏迷;',
          '截肢或关节融合;',
        ],
      },
      // 5b.右上肢运动
      eighthTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 600px;',
        title: '5b.右上肢运动',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5', '8-6'],
        content: [
          '1) 无下落，置肢体于90°（或45°坚持10 秒）：0分;',
          '2) 能抬起但不能坚持10秒，下落时不撞击床或其他支持物：1分;',
          '3) 试图抵抗重力，但不能维持坐位90°或仰位45°：2分;',
          '4) 不能抵抗重力，肢体快速下落：3分;',
          '5) 无运动/昏迷：4分;',
          '6) 截肢或关节融合：9分。',
        ],
        grade: ['0', '1', '2', '3', '4', '9'],
        leftOne: 'left: 106px',
        leftTwo: 'left: 137px',
        explain:
          '置右上肢于合适的位置：坐位时右上肢平举90°，仰卧时上抬45°，掌心向下，若上肢在10秒内下落，记1～4分。对失语者用语言或动作鼓励，不用有害刺激。',
        explainShow: false,
        simpleContent: [
          '无下落，置肢体于90°（或45°坚持10 秒）;',
          '能抬起但不能坚持10秒，下落时不撞击床或其他支持物;',
          '试图抵抗重力，但不能维持坐位90°或仰位45°;',
          '不能抵抗重力，肢体快速下落;',
          '无运动/昏迷;',
          '截肢或关节融合;',
        ],
      },
      // 6a.左下肢运动
      ninthTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 600px;',
        title: '6a.左下肢运动',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5', '9-6'],
        content: [
          '1) 无下落，于要求位置坚持5秒：0分;',
          '2) 5秒末下落，不撞击床：1分;',
          '3) 5秒内下落到床上，可部分抵抗重力：2分;',
          '4) 立即下落到床上，不能抵抗重力：3分;',
          '5) 无运动/昏迷：4分;',
          '6) 截肢或关节融合：9分。',
        ],
        grade: ['0', '1', '2', '3', '4', '9'],
        leftOne: 'left: 106px',
        leftTwo: 'left: 137px',
        explain:
          '置左下肢于合适的位置：左下肢卧位抬高30°，左下肢在5秒内下落，记1～4分。对失语者用语言或动作鼓励，不用有害刺激。',
        explainShow: false,
        simpleContent: [
          '无下落，于要求位置坚持5秒;',
          '5秒末下落，不撞击床;',
          '5秒内下落到床上，可部分抵抗重力;',
          '立即下落到床上，不能抵抗重力;',
          '无运动/昏迷;',
          '截肢或关节融合;',
        ],
      },
      // 6b.右下肢运动
      tenthTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 600px;',
        title: '6b.右下肢运动',
        id: ['10-1', '10-2', '10-3', '10-4', '10-5', '10-6'],
        content: [
          '1) 无下落，于要求位置坚持5秒：0分;',
          '2) 5秒末下落，不撞击床：1分;',
          '3) 5秒内下落到床上，可部分抵抗重力：2分;',
          '4) 立即下落到床上，不能抵抗重力：3分;',
          '5) 无运动/昏迷：4分;',
          '6) 截肢或关节融合：9分。',
        ],
        grade: ['0', '1', '2', '3', '4', '9'],
        leftOne: 'left: 106px',
        leftTwo: 'left: 137px',
        explain:
          '置右下肢于合适的位置：右下肢卧位抬高30°，右下肢在5秒内下落，记1～4分。对失语者用语言或动作鼓励，不用有害刺激。',
        explainShow: false,
        simpleContent: [
          '无下落，于要求位置坚持5秒;',
          '5秒末下落，不撞击床;',
          '5秒内下落到床上，可部分抵抗重力;',
          '立即下落到床上，不能抵抗重力;',
          '无运动/昏迷;',
          '截肢或关节融合;',
        ],
      },
      // 7.肢体共济失调
      eleventhTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '7. 肢体共济失调',
        id: ['11-1', '11-2', '11-3', '11-4'],
        content: [
          '1) 无共济失调：0分;',
          '2) 一个肢体有：1分;',
          '3) 两个肢体有，共济失调在：一侧肢体动作僵硬或不准确：2分;',
          '4) 截肢或关节融合：9分。',
        ],
        grade: ['0', '1', '2', '9'],
        leftOne: 'left: 118px',
        leftTwo: 'left: 152px',
        explain:
          '目的是发现一侧小脑病变。检查时睁眼，若有视力障碍，应确保检查在无视野缺损中进行。进行双侧指鼻试验、跟膝径试验，共济失调与无力明显不呈比例时记分。若患者不能理解或肢体瘫痪不记分。盲人用伸展的上肢摸鼻。若为截肢或关节融合记9分，并解释。',
        explainShow: false,
        simpleContent: [
          '无共济失调;',
          '一个肢体有;',
          '两个肢体有，共济失调在：一侧肢体动作僵硬或不准确;',
          '截肢或关节融合;',
        ],
      },
      // 8.感觉
      theTwelfthTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '8. 感觉',
        id: ['12-1', '12-2', '12-3'],
        content: [
          '1) 正常：0分;',
          '2) 轻-中度感觉障碍（患者感觉针刺不尖锐或迟钝，或针刺感缺失但有触觉）：1分;',
          '3) 重度-完全感觉缺失（面、上肢、下肢无触觉）：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 53px',
        leftTwo: 'left: 79px',
        explain:
          '检查对针刺的感觉和表情，或意识障碍及失语者对有害刺激的躲避。只对与脑卒中有关的感觉缺失评分。偏身感觉丧失者需要精确检查，应测试身体多处[上肢（不包括手）、下肢、躯干、面部]确定有无偏身感觉缺失。严重或完全的感觉缺失记2分。昏睡或失语者记1或0分。脑干卒中双侧感觉缺失记2分。无反应或四肢瘫痪者记2分。昏迷患者（1a=3）记2分。',
        explainShow: false,
        simpleContent: [
          '正常;',
          '轻-中度感觉障碍（患者感觉针刺不尖锐或迟钝，或针刺感缺失但有触觉）;',
          '重度-完全感觉缺失（面、上肢、下肢无触觉）;',
        ],
      },
      // 9.语言
      language: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '9. 语言',
        id: ['13-1', '13-2', '13-3', '13-4'],
        content: [
          '1) 正常：0分;',
          '2) 轻-中度失语：流利程度和理解能力部分下降，但表达无明显受限：1分;',
          '3) 严重失语，交流是通过患者破碎的语言表达，听者须推理、询问、猜测，交流困难：2分;',
          '4) 不能说话或者完全失语，无言语或听力理解能力：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        leftOne: 'left: 53px',
        leftTwo: 'left: 79px',
        explain:
          '命名、阅读测试。若视觉缺损干扰测试，可让患者识别放在手上的物品，重复和发音。气管插管者手写回答。昏迷者记3分。给恍惚或不合作者选择一个记分，但3分仅给不能说话且不能执行任何指令者。',
        explainShow: false,
        simpleContent: [
          '正常;',
          '轻-中度失语：流利程度和理解能力部分下降，但表达无明显受限;',
          '严重失语，交流是通过患者破碎的语言表达，听者须推理、询问、猜测，交流困难;',
          '不能说话或者完全失语，无言语或听力理解能力;',
        ],
      },
      // 10.构音障碍
      dysarthria: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '10.构音障碍',
        id: ['14-1', '14-2', '14-3', '14-4'],
        content: [
          '1) 正常：0分;',
          '2) 轻-中度，至少有些发音不清，虽有困难但能被理解：1分;',
          '3) 言语不清，不能被理解，但无失语或与失语不成比例，或失音：2分;',
          '4) 气管插管或其他物理障碍：9分。',
        ],
        grade: ['0', '1', '2', '9'],
        leftOne: 'left: 90px',
        leftTwo: 'left: 121px',
        explain:
          '读或重复表上的单词。若有严重的失语，评估自发语言时发音的清晰度。若因气管插管或其他物理障碍不能讲话，记9分。同时注明原因。不要告诉患者为什么做测试。',
        explainShow: false,
        simpleContent: [
          '正常;',
          '轻-中度，至少有些发音不清，虽有困难但能被理解;',
          '言语不清，不能被理解，但无失语或与失语不成比例，或失音;',
          '气管插管或其他物理障碍;',
        ],
      },
      // 11.忽视
      neglect: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '11.忽视',
        id: ['15-1', '15-2', '15-3'],
        content: [
          '1) 正常：0分;',
          '2) 视、触、听、空间觉或个人的忽视；或对一种感觉的双侧同时刺激忽视：1分;',
          '3) 严重的偏侧忽视或一种以上的偏侧忽视；不认识自己的手；只能对一侧空间定位：2分。',
        ],
        grade: ['0', '1', '2'],
        leftOne: 'left: 56px',
        leftTwo: 'left: 90px',
        explain:
          '若患者严重视觉缺失影响双侧视觉的同时检查，皮肤刺激正常，记为正常。若失语，但确实表现为对双侧的注意，记分正常。视空间忽视或疾病失认也可认为是异常的证据。',
        explainShow: false,
        simpleContent: [
          '正常;',
          '视、触、听、空间觉或个人的忽视；或对一种感觉的双侧同时刺激忽视;',
          '严重的偏侧忽视或一种以上的偏侧忽视；不认识自己的手；只能对一侧空间定位;',
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 15,
      choiceBox: [],
      // 去评分时患者信息
      rewritePatient: '',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', 'NIHSS评分')
  },

  mounted () {
    if (localStorage.getItem('associatedScore') === 'true') {
      this.$store.commit('minitool/setRelevanceSkip', true)
      this.rewritePatient = this.$store.state.minitool.PatientInformations
      this.$store.commit(
        'minitool/setAssociatedScore',
        localStorage.getItem('associatedScore')
      )
    }
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('relevanceScore') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('relevanceScore'))[
          index
        ].choice
      })
    }

    // 根据第一题选项选其他题目
    this.relevance()
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    relevance () {
      this.choiceBox.map((item) => {
        if (this.choiceBox[3].checked === true) {
          switch (item.id) {
            case '2-3':
              item.checked = true
              break
            case '3-3':
              item.checked = true
              break
            case '6-4':
              item.checked = true
              break
            case '7-5':
              item.checked = true
              break
            case '8-5':
              item.checked = true
              break
            case '9-5':
              item.checked = true
              break
            case '10-5':
              item.checked = true
              break
            case '11-1':
              item.checked = true
              break
            case '12-3':
              item.checked = true
              break
            case '13-4':
              item.checked = true
              break
            case '14-3':
              item.checked = true
              break
            case '15-3':
              item.checked = true
              break
            default:
              break
          }
        }
      })
    },
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 根据第一题选项选其他题目
      this.relevance()
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 15
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      // nihss 的选中状态 存入特殊的变量里 防止关联评分时混乱
      localStorage.setItem('relevanceScore', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
          score: this.totalPoints,
          path: this.$route.query.fromPath,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }

    .bin:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
