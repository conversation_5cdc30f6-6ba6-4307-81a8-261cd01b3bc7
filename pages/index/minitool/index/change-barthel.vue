<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in changeBarthel" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '改良Barthel指数评定量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '改良Barthel指数评定量表是一种评估病人日常生活自理能力的量表，它是在Barthel指数评定量表的基础上进行改良得来的。原始的Barthel指数评定量表存在一些问题，例如对于行动能力和认知能力的评估不够全面，对于辅助工具的使用也没有考虑等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Barthel日常生活活动评分, Barthel日常生活活动指数, Barthel指数, ADL评分, 患者的日常生活功能, 日常生活活动'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      changeBarthel: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 619px;',
          title: '1. 洗澡',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：1分;',
            '3) 中等帮助：3分;',
            '4) 最小帮助：4分;',
            '5) 完全独立：5分;'
          ],
          grade: ['0', '1', '3', '4', '5'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '2. 进食',
          id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '3. 用厕',
          id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '4. 穿衣',
          id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '5. 大便控制',
          id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '6. 小便控制',
          id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '7. 上下楼梯',
          id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：2分;',
            '3) 中等帮助：5分;',
            '4) 最小帮助：8分;',
            '5) 完全独立：10分。'
          ],
          grade: ['0', '2', '5', '8', '10'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '8. 床椅转移',
          id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：3分;',
            '3) 中等帮助：8分;',
            '4) 最小帮助：12分;',
            '5) 完全独立：15分。'
          ],
          grade: ['0', '3', '8', '12', '15'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '9. 平地行走',
          id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：3分;',
            '3) 中等帮助：8分;',
            '4) 最小帮助：12分;',
            '5) 完全独立：15分。'
          ],
          grade: ['0', '3', '8', '12', '15'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '10. 坐轮椅（仅在不能行走时才评定此项）',
          id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
          content: [
            '1) 完全依赖：0分;',
            '2) 最大帮助：1分;',
            '3) 中等帮助：3分;',
            '4) 最小帮助：4分;',
            '5) 完全独立：5分。'
          ],
          grade: ['0', '1', '3', '4', '5'],
          simpleContent: ['完全依赖;', '最大帮助;', '中等帮助;', '最小帮助;', '完全独立;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 10,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints == 100) {
        return `${this.totalPoints}分，正常`
      } else if (this.totalPoints >= 60) {
        return `${this.totalPoints}分，生活基本自理`
      } else if (this.totalPoints >= 41 && this.totalPoints <= 59) {
        return `${this.totalPoints}分，中度功能障碍，生活需要帮助`
      } else if (this.totalPoints >= 21 && this.totalPoints <= 40) {
        return `${this.totalPoints}分，重度功能障碍，生活依赖明显`
      } else if (this.totalPoints <= 20) {
        return `${this.totalPoints}分，生活完全依赖`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '改良Barthel指数评定量表')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 10
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
