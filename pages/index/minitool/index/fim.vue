<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="motorFunction" class="oneTopic">
            <p class="title">运动功能</p>
            <p class="subTitle">一、自理能力</p>
            <Options v-for="i in selfCareAbility" :key="i.title" :information="i"></Options>
            <p class="subTitle">二、括约肌控制</p>
            <Options v-for="i in sphincterControl" :key="i.title" :information="i"></Options>
            <p class="subTitle">三、转移</p>
            <Options v-for="i in transfer" :key="i.title" :information="i"></Options>
            <p class="subTitle">四、行走</p>
            <Options v-for="i in walk" :key="i.title" :information="i"></Options>
          </div>
          <div ref="cognitiveFunction" class="cognitive-function">
            <p class="title">认知功能</p>
            <p class="subTitle">一、交流</p>
            <Options v-for="i in exchange" :key="i.title" :information="i"></Options>
            <p class="subTitle">二、社会认知</p>
            <Options v-for="i in socialCognition" :key="i.title" :information="i"></Options>
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected !== 18">暂无</span>
            <span v-show="selected == 18">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import Options from '@/components/MiniTool/Options.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options },
  // tdk
  head () {
    return {
      title: '功能独立性评定(FIM) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '功能独立性评定（Functional Independence Measure，FIM）是一种用于评估病人康复程度的工具，它主要评估病人在日常活动中的功能独立性。被广泛应用于各种康复领域，包括神经康复、骨科康复、心脏康复、肿瘤康复等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '功能独立性评定, FIM, 认知功能测定子量表, Functional Independence Measure'
        }
      ]
    }
  },
  data () {
    return {
      // 自理能力
      selfCareAbility: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '1. 进食',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '2. 梳洗修饰',
          id: ['2-1', '2-2', '2-3', '2-4', '2-5', '2-6', '2-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '3. 洗澡',
          id: ['3-1', '3-2', '3-3', '3-4', '3-5', '3-6', '3-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '4. 穿裤子',
          id: ['4-1', '4-2', '4-3', '4-4', '4-5', '4-6', '4-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '5. 穿上衣',
          id: ['5-1', '5-2', '5-3', '5-4', '5-5', '5-6', '5-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '6. 上厕所',
          id: ['6-1', '6-2', '6-3', '6-4', '6-5', '6-6', '6-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        },
      ],
      // 括约肌控制
      sphincterControl: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '1. 膀胱控制',
          id: ['7-1', '7-2', '7-3', '7-4', '7-5', '7-6', '7-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '2. 直肠控制',
          id: ['8-1', '8-2', '8-3', '8-4', '8-5', '8-6', '8-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        },
      ],
      // 转移
      transfer: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '1. 床、椅、轮椅转移',
          id: ['9-1', '9-2', '9-3', '9-4', '9-5', '9-6', '9-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '2. 入厕',
          id: ['10-1', '10-2', '10-3', '10-4', '10-5', '10-6', '10-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '3. 盆浴或淋浴',
          id: ['11-1', '11-2', '11-3', '11-4', '11-5', '11-6', '11-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }
      ],
      // 行走
      walk: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '1. 步行/轮椅',
          id: ['12-1', '12-2', '12-3', '12-4', '12-5', '12-6', '12-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '2. 上下楼梯',
          id: ['13-1', '13-2', '13-3', '13-4', '13-5', '13-6', '13-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }
      ],
      // 交流
      exchange: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '1. 理解',
          id: ['14-1', '14-2', '14-3', '14-4', '14-5', '14-6', '14-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '2. 表达',
          id: ['15-1', '15-2', '15-3', '15-4', '15-5', '15-6', '15-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }
      ],
      // 社会认知
      socialCognition: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '1. 社会交往',
          id: ['16-1', '16-2', '16-3', '16-4', '16-5', '16-6', '16-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 719px;',
          title: '2. 解决问题',
          id: ['17-1', '17-2', '17-3', '17-4', '17-5', '17-6', '17-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 719px;',
          title: '3. 记忆',
          id: ['18-1', '18-2', '18-3', '18-4', '18-5', '18-6', '18-7'],
          content: [
            '1) 完全独立：7分;',
            '2) 辅助独立：6分;',
            '3) 监护或准备：5分;',
            '4) 最低接触性帮助：4分;',
            '5) 中等接触性帮助：3分;',
            '6) 最大帮助：2分;',
            '7) 完全依赖：1分。'
          ],
          grade: ['7', '6', '5', '4', '3', '2', '1'],
          simpleContent: ['完全独立;', '辅助独立;', '监护或准备;', '最低接触性帮助;', '中等接触性帮助;', '最大帮助;', '完全依赖;']
        }
      ],
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 18,
      // 总分数
      totalPoints: 0,
      choiceBoxs: [[], []],
      score: {
        // 运动功能 分数
        motorFunctionScore: 0,
        // 认知功能 分数
        cognitiveFunctionScore: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: []
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      let _this = this.totalPoints
      if (_this == 126) {
        return `${_this}分，完全独立`
      } else if (_this <= 125 && _this >= 108) {
        return `${_this}分，基本上独立`
      } else if (_this <= 107 && _this >= 90) {
        return `${_this}分，有条件的独立`
      } else if (_this <= 89 && _this >= 72) {
        return `${_this}分，轻度依赖`
      } else if (_this <= 71 && _this >= 54) {
        return `${_this}分，中度依赖`
      } else if (_this <= 53 && _this >= 36) {
        return `${_this}分，重度依赖`
      } else if (_this <= 35 && _this >= 19) {
        return `${_this}分，极重度依赖`
      } else if (_this == 18) {
        return `${_this}分，完全依赖`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '功能独立性评定(FIM)')
  },

  mounted () {
    this.motorFn()
    this.cognitiveFn()
    this.loginEnd()
  },

  methods: {
    // 未登录状态下 做题 登录之后的逻辑
    loginEnd () {
      // 登录之后 从本地拿去数据 勾选选项
      if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
        this.allInput.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
          ].choice
        })
      }
      // 勾选完，循环数组，拿去分数和选中个数
      this.allInput.map((item) => {
        // 如果 处于选中状态 就处理以下逻辑
        if (item.checked === true) {
          if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
            this.choiceBox.push(item.id.slice(0, 2))
          }
          this.selected = this.choiceBox.length
          this.unselected = 15 - this.selected
          this.totalPoints += item.attributes.grade.value * 1
        }
      })
    },
    // 获取 运动功能 中的所有input框
    motorFn () {
      for (let i = 0; i < this.$refs.motorFunction.children.length; i++) {
        let index = this.$refs.motorFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[0].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
    },
    // 获取 认知功能 中的所有input框
    cognitiveFn () {
      for (let i = 0; i < this.$refs.cognitiveFunction.children.length; i++) {
        let index = this.$refs.cognitiveFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[1].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
      // 清空 allInput
      this.allInput = []
      // 合并两个数组
      this.allInput = [...this.allInput, ...this.choiceBoxs[0], ...this.choiceBoxs[1]]
    },
    // 总分
    result () {
      this.totalPoints = this.score.motorFunctionScore + this.score.cognitiveFunctionScore
    },
    checkedMotor (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 18 - this.selected
        this.score.motorFunctionScore += item.attributes.grade.value * 1
      }
    },
    checkedCognitive (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 18 - this.selected
        this.score.cognitiveFunctionScore += item.attributes.grade.value * 1
      }
    },
    // 选项的点击事件
    getTatals () {
      // 初始化数据
      this.selected = 0
      this.choiceBox = []
      this.unselected = 18
      this.totalPoints = 0
      this.optionMessage = []
      this.score.motorFunctionScore = 0
      this.score.cognitiveFunctionScore = 0
      // 运动功能
      this.choiceBoxs[0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedMotor(item)
      })
      // 认知功能
      this.choiceBoxs[1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedCognitive(item)
      })
      // 计算总分
      this.result()
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    .title {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      text-align: center;
      margin: 18px 0;
    }

    .subTitle {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 18px;
      color: #202020;
      margin: 20px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    .threeTopic {
      div {
        width: 877px;
        margin-top: 16px;
        padding: 3px 16px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(5, 129, 206, 0.03);

        p:nth-of-type(1) {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }

        p:nth-of-type(2) {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
