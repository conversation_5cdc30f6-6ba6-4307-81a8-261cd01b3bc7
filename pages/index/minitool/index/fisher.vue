<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue';
import SheetChange from '@/components/MiniTool/SheetChange.vue';
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue';


export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '改良Fisher分级 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '蛛网膜下腔出血Fisher分级是基于影像学检查结果的分级，该分级主要是对患者的迟发性脑梗死及血管痉挛风险进行评估。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Fisher分级, logistic回归分析, 脑室内出血, 评估aSAH, mFS, DCI, 动脉瘤性蛛网膜下腔出血, 颅内血管痉挛, 动脉瘤特点, 宽颈动脉瘤破裂出血, 动脉瘤'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 850px;',
        title: '1. 表现',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: ['未见出血或仅脑室内出血或脑实质内出血',
          '仅见基底池出血',
          '仅见周边脑池或侧裂池出血',
          '广泛蛛网膜下腔出血拌脑实质内血肿',
          '基底池和周边脑池、侧裂池较厚积血 57%'],
        grade: ['0级，CVS可能性3%', 'Ⅰ级，CVS可能性14%', 'Ⅱ级，CVS可能性38%', 'Ⅲ级，CVS可能性57%', 'Ⅳ级，CVS可能性57%'],
        show: 'false',
        simpleContent: ['未见出血或仅脑室内出血或脑实质内出血;',
          '仅见基底池出血;',
          '仅见周边脑池或侧裂池出血;',
          '广泛蛛网膜下腔出血拌脑实质内血肿;',
          '基底池和周边脑池、侧裂池较厚积血 57%;'],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '动脉瘤')
    localStorage.setItem('wordLast', '改良Fisher分级')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (let j = 0; j < this.$refs.reference.children[i].children[0].children[0].children[1].children.length; j++) {
        this.choiceBox.push(this.$refs.reference.children[i].children[0].children[0].children[1].children[j].children[0])
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.unselected = 1;
      this.selected = 0;
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach(element => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      });
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }

    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #FFFFFF;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>