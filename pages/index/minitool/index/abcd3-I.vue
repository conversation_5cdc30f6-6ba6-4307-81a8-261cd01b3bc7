<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="age"></Options>
          <Options :information="bloodPressure"></Options>
          <Options :information="clinicalFeature"></Options>
          <Options :information="symptom"></Options>
          <Options :information="diabetes"></Options>
          <Options :information="cerebralIschemia"></Options>
          <Options :information="DWI"></Options>
          <Options :information="carotidArtery"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div class="grade">
            卒中风险：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ risk }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'ABCD³-I - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '用于评估TIA后早期卒中风险。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '一过性脑缺血发作, 评估TIA后早期卒中风险, TIA 后卒中风险, 短暂性脑缺血发作, 卒中的风险, ABCD³-I, 轻型缺血性脑卒中, 非致残性缺血性脑卒中, 脑卒中复发风险, TIA脑卒中风险评分, 脑卒中发生预测评分'
        }
      ]
    }
  },
  data () {
    return {
      // 年龄
      age: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title: '1. 年龄',
        id: ['1-1', '1-2'],
        content: ['1) <60岁：0分;', '2) ≥60岁：1分。'],
        grade: ['0', '1'],
        simpleContent: ['<60岁;', '≥60岁;'],
      },
      // 血压
      bloodPressure: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '2. 血压',
        id: ['2-1', '2-2'],
        content: ['1) 正常：0分;', '2) ≥140/90mmHg：1分。'],
        grade: ['0', '1'],
        simpleContent: ['正常;', '≥140/90mmHg;'],
      },
      // 临床表现
      clinicalFeature: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '3. 临床表现',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '1) 无语言障碍无肢体无力：0分;',
          '2) 有语言障碍而无肢体无力：1分;',
          '3) 单侧肢体无力：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无语言障碍无肢体无力;',
          '有语言障碍而无肢体无力;',
          '单侧肢体无力;',
        ],
      },
      // 症状持续时间
      symptom: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '4. 症状持续时间',
        id: ['4-1', '4-2', '4-3'],
        content: ['1) <10分钟：0分;', '2) 10-59分钟：1分;', '3) ≥60分钟：2分。'],
        grade: ['0', '1', '2'],
        simpleContent: ['<10分钟;', '10-59分钟;', '≥60分钟;'],
      },
      // 糖尿病（口服降糖药或应用胰岛素治疗）
      diabetes: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title: '5. 糖尿病（口服降糖药或应用胰岛素治疗）',
        id: ['5-1', '5-2'],
        content: ['1) 无：0分;', '2) 有：1分。'],
        grade: ['0', '1'],
        simpleContent: ['无;', '有;'],
      },
      // 双重短暂性脑缺血发作病史
      cerebralIschemia: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '6. 双重短暂性脑缺血发作病史',
        id: ['6-1', '6-2'],
        content: ['1) 无：0分;', '2) 有：2分。'],
        grade: ['0', '2'],
        simpleContent: ['无;', '有;'],
      },
      // DWI高信号
      DWI: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title: '7. DWI高信号',
        id: ['7-1', '7-2'],
        content: ['1) 无：0分;', '2) 有：2分。'],
        grade: ['0', '2'],
        simpleContent: ['无;', '有;'],
      },
      // 颈动脉狭窄≥50%
      carotidArtery: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '8. 颈动脉狭窄≥50%',
        id: ['8-1', '8-2'],
        content: ['1) 无：0分;', '2) 有：2分。'],
        grade: ['0', '2'],
        simpleContent: ['无;', '有;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 8,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
      // 风险说明
      risk: '',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '卒中再发风险评分')
    localStorage.setItem('wordLast', 'ABCD³-l')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })

    // 结果展示
    this.result()
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 结果展示
    result () {
      if (this.totalPoints <= 3) {
        this.risk = '低危'
      } else if (this.totalPoints >= 4 && this.totalPoints <= 7) {
        this.risk = '中危'
      } else {
        this.risk = '高危'
      }
    },

    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 8
      this.selected = 0
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()

      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
