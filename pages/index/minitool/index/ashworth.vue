<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '改良Ashworth分级评定法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '改良的Ashworth分级评定法是一种用于评估肌肉痉挛和肌张力的工具。与传统的Ashworth分级评定法相比，改良版增加了对各个肌群的定量评估，提供了更精确和详细的测量。改良的Ashworth分级评定法使用一个0-4级的分级系统来描述肌肉痉挛的严重程度，级别越高意味着肌张力越高。在评估过程中，评定者通过触摸患者的相关肌群，在被动关节运动中观察肌张力的变化，并记录相应的分级。该评定法的改良版还加入了额外的评估项目，如运动速度、开始和结束时的抵抗等，以提供更准确的评估结果。通过这种改良，评估者可以更好地了解患者的肌肉痉挛情况，从而更好地制定个体化的康复计划和治疗策略。改良的Ashworth分级评定法在康复领域中被广泛使用，帮助评估患者的肌肉痉挛程度，监测病情进展，并指导康复过程中的治疗方案。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '改良Modified Ashworth Scale,Ashworth修订版评定法,修订版Ashworth分级评定法,修订版Ashworth量表,Ashworth修订版评分方法,改进版Ashworth分级评定法'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 平衡状态',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5','1-6'],
        content: [
          '无肌张力的增加;',
          '肌张力轻度增加: 受累部分被动屈伸时，在持续被动运动 (ROM) 之末时呈现最小的阻力或出现突然卡住和释放;',
          '肌张力轻度增加: 在ROM<50%范围内出现突然卡住，或呈现最小的阻力;',
          '肌张力较明显地增加: 在>50%ROM范围，肌张力较明显地增加，但受累部分仍能较易地被移动;',
          '肌张力严重增高:全ROM被动运动困难;',
          '僵硬，受累部分被动屈伸时呈现僵硬状态而不能动。'
        ],
        grade: ['0级', 'I级','I+级', 'II级', 'Ⅲ级', 'IV级'],
        show: 'false',
        simpleContent: [
          '无肌张力的增加;',
          '肌张力轻度增加：受累部分被动屈伸时，在持续被动运动 (ROM) 之末时呈现最小的阻力或出现突然卡住和释放;',
          '肌张力轻度增加：在ROM<50%范围内出现突然卡住，或呈现最小的阻力;',
          '肌张力较明显地增加： 在>50%ROM范围，肌张力较明显地增加，但受累部分仍能较易地被移动;',
          '肌张力严重增高：全ROM被动运动困难;',
          '僵硬，受累部分被动屈伸时呈现僵硬状态而不能动;',
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '改良Ashworth分级评定法')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
