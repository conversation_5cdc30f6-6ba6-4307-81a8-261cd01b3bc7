<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 1.动脉瘤体积计算 -->
        <div class="Framingham">
          <p>1.Framingham风险评分</p>
          <div class="describe">
            <ul>
              <li>
                <p>年龄：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.age === '请选择年龄' ? 'selectBefore' : 'selectAfter',
                  ]"
                  v-model="obj.age"
                >
                  <option selected disabled hidden>请选择年龄</option>
                  <option>20-34岁</option>
                  <option>35-39岁</option>
                  <option>40-44岁</option>
                  <option>45-49岁</option>
                  <option>50-54岁</option>
                  <option>55-59岁</option>
                  <option>60-64岁</option>
                  <option>65-69岁</option>
                  <option>70-74岁</option>
                  <option>75-79岁</option>
                </select>
              </li>
              <li>
                <p>性别：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.sex === '请选择性别' ? 'selectBefore' : 'selectAfter',
                  ]"
                  v-model="obj.sex"
                >
                  <option selected disabled hidden>请选择性别</option>
                  <option>男性</option>
                  <option>女性</option>
                </select>
              </li>
              <li>
                <p>吸烟状况：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.smoking === '请选择吸烟状况'
                      ? 'selectBefore'
                      : 'selectAfter',
                  ]"
                  v-model="obj.smoking"
                >
                  <option selected disabled hidden>请选择吸烟状况</option>
                  <option>不吸烟</option>
                  <option>吸烟</option>
                </select>
              </li>
              <li>
                <p>整体胆固醇水平：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.overall === '请选择整体胆固醇水平'
                      ? 'selectBefore'
                      : 'selectAfter',
                  ]"
                  v-model="obj.overall"
                >
                  <option selected disabled hidden>请选择整体胆固醇水平</option>
                  <option>≤4.14 mmol/L</option>
                  <option>4.15-5.19 mmol/L</option>
                  <option>5.2-6.19 mmol/L</option>
                  <option>6.2-7.2 mmol/L</option>
                  <option>≥7.21 mmol/L</option>
                </select>
              </li>
              <li>
                <p>好的胆固醇水平：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.good === '请选择好的胆固醇水平'
                      ? 'selectBefore'
                      : 'selectAfter',
                  ]"
                  v-model="obj.good"
                >
                  <option selected disabled hidden>请选择好的胆固醇水平</option>
                  <option>≥1.55 mmol/L</option>
                  <option>1.30-1.54 mmol/L</option>
                  <option>1.04-1.29 mmol/L</option>
                  <option>&lt;1.04 mmol/L</option>
                </select>
              </li>
              <li>
                <p>收缩压：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.systoliPressure === '请选择收缩压'
                      ? 'selectBefore'
                      : 'selectAfter',
                  ]"
                  v-model="obj.systoliPressure"
                >
                  <option selected disabled hidden>请选择收缩压</option>
                  <option>&lt;120 mmHg</option>
                  <option>120-129 mmHg</option>
                  <option>130-139 mmHg</option>
                  <option>140-159 mmHg</option>
                  <option>160+ mmHg</option>
                </select>
              </li>
              <li>
                <p>血压是否被治疗：</p>
                <i class="el-icon-caret-bottom"></i>
                <select
                  :class="[
                    'selectBefore',
                    obj.bloodPressure === '请选择血压是否被治疗'
                      ? 'selectBefore'
                      : 'selectAfter',
                  ]"
                  v-model="obj.bloodPressure"
                >
                  <option selected disabled hidden>请选择血压是否被治疗</option>
                  <option>无治疗</option>
                  <option>有治疗</option>
                </select>
              </li>
            </ul>
          </div>
          <!-- 结果展示 -->
          <div class="result">
            <p>FPS总分:</p>
            <span v-show="result === 0">暂无</span>
            <span v-show="result !== 0">{{ result }}分</span>
          </div>
          <div v-show="risk !== 0">
            <p>
              10年内风险: <span>{{ risk }}</span>
            </p>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import { saveToolScore } from '@/api/minitool'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head() {
    return {
      title: 'Framingham风险评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            'Framingham 危险评分是世界上第一个冠心病危险评分，该评分已成为公认的预测个体未来冠心病事件风险及制订预防管理决策的基础。如今，Framingham 危险评分在临床和科研中得到了广泛使用。',
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            'framingham, 心脏研究, 心梗, 冠心病, 心血管风险, 肢端肥大症, 糖尿病患者, 心血管疾病, 糖耐量受损',
        },
      ],
    }
  },
  data() {
    return {
      obj: {
        age: '请选择年龄',
        sex: '请选择性别',
        smoking: '请选择吸烟状况',
        overall: '请选择整体胆固醇水平',
        good: '请选择好的胆固醇水平',
        systoliPressure: '请选择收缩压',
        bloodPressure: '请选择血压是否被治疗',
        path: '',
        id: '',
      },
      // 总分
      result: 0,
      // 风险概率
      risk: 0,
      // 年龄 性别 的 综合得分情况
      scoreAgeSex: 0,
      // 性别 年龄 吸烟状况 综合得分情况
      scoreAgeSexSmoker: 0,
      // 性别 年龄 整体胆固醇水平 综合得分情况
      scoreAgeSexOverall: 0,
      // 好的胆固醇
      scoreGood: 0,
      // 性别 收缩压 有无治疗
      scoreSystoliPressureBloodPressure: 0,
      // 评分详情
      submitInformation: {
        name: '',
        gender: '',
        age: '',
        diagnosis: '',
        userId: '',
        scoreType: '',
        scoreTitle: 'Framingham风险评分',
        scoreDetail: '',
        scoreResult: '',
        relationScoreId: '',
      },
      // scoringDetails: [[], [], [], []],
    }
  },

  beforeMount() {
    localStorage.setItem('className', '卒中再发风险评分')
    localStorage.setItem('wordLast', 'Framingham风险评分')
  },

  watch: {
    obj: {
      deep: true,
      handler() {
        if (
          this.obj.age !== '请选择年龄' &&
          this.obj.sex !== '请选择性别' &&
          this.obj.smoking !== '请选择吸烟状况' &&
          this.obj.overall !== '请选择整体胆固醇水平' &&
          this.obj.good !== '请选择好的胆固醇水平' &&
          this.obj.systoliPressure !== '请选择收缩压' &&
          this.obj.bloodPressure !== '请选择血压是否被治疗'
        ) {
          // // 年龄 性别 的 综合得分情况
          if (this.obj.sex === '男性' && this.obj.age === '20-34岁') {
            this.scoreAgeSex = -9
          } else if (this.obj.sex === '男性' && this.obj.age === '35-39岁') {
            this.scoreAgeSex = -4
          } else if (this.obj.sex === '男性' && this.obj.age === '40-44岁') {
            this.scoreAgeSex = 0
          } else if (this.obj.sex === '男性' && this.obj.age === '45-49岁') {
            this.scoreAgeSex = 3
          } else if (this.obj.sex === '男性' && this.obj.age === '50-54岁') {
            this.scoreAgeSex = 6
          } else if (this.obj.sex === '男性' && this.obj.age === '55-59岁') {
            this.scoreAgeSex = 8
          } else if (this.obj.sex === '男性' && this.obj.age === '60-64岁') {
            this.scoreAgeSex = 10
          } else if (this.obj.sex === '男性' && this.obj.age === '65-69岁') {
            this.scoreAgeSex = 11
          } else if (this.obj.sex === '男性' && this.obj.age === '70-74岁') {
            this.scoreAgeSex = 12
          } else if (this.obj.sex === '男性' && this.obj.age === '75-79岁') {
            this.scoreAgeSex = 13
          } else if (this.obj.sex === '女性' && this.obj.age === '20-34岁') {
            this.scoreAgeSex = -7
          } else if (this.obj.sex === '女性' && this.obj.age === '35-39岁') {
            this.scoreAgeSex = -3
          } else if (this.obj.sex === '女性' && this.obj.age === '40-44岁') {
            this.scoreAgeSex = 0
          } else if (this.obj.sex === '女性' && this.obj.age === '45-49岁') {
            this.scoreAgeSex = 3
          } else if (this.obj.sex === '女性' && this.obj.age === '50-54岁') {
            this.scoreAgeSex = 6
          } else if (this.obj.sex === '女性' && this.obj.age === '55-59岁') {
            this.scoreAgeSex = 8
          } else if (this.obj.sex === '女性' && this.obj.age === '60-64岁') {
            this.scoreAgeSex = 10
          } else if (this.obj.sex === '女性' && this.obj.age === '65-69岁') {
            this.scoreAgeSex = 12
          } else if (this.obj.sex === '女性' && this.obj.age === '70-74岁') {
            this.scoreAgeSex = 14
          } else if (this.obj.sex === '女性' && this.obj.age === '75-79岁') {
            this.scoreAgeSex = 16
          }
          // 性别 年龄 吸烟状况 综合得分情况
          if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 8
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 8
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 9
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 9
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 7
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 7
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.smoking === '不吸烟'
          ) {
            this.scoreAgeSexSmoker = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.smoking === '吸烟'
          ) {
            this.scoreAgeSexSmoker = 1
          }
          // 性别 年龄 整体胆固醇水平 综合得分情况
          if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 7
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 9
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 11
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 7
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 9
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 11
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 6
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 6
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 11
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '20-34岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 13
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 11
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '35-39岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 13
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 6
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '40-44岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 10
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 6
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 8
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '45-49岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 10
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '50-54岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 7
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 5
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '55-59岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 7
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '60-64岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '65-69岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '70-74岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '≤4.14 mmol/L'
          ) {
            this.scoreAgeSexOverall = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '4.15-5.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '5.2-6.19 mmol/L'
          ) {
            this.scoreAgeSexOverall = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '6.2-7.2 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.age === '75-79岁' &&
            this.obj.overall === '≥7.21 mmol/L'
          ) {
            this.scoreAgeSexOverall = 2
          }
          // 好的胆固醇
          if (this.obj.good === '≥1.55 mmol/L') {
            this.scoreGood = -1
          } else if (this.obj.good === '1.30-1.54 mmol/L') {
            this.scoreGood = 0
          } else if (this.obj.good === '1.04-1.29 mmol/L') {
            this.scoreGood = 1
          } else if (this.obj.good === '<1.04 mmol/L') {
            this.scoreGood = 2
          }
          // 性别 收缩压 有无治疗
          if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '<120 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '<120 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '120-129 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 0
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '120-129 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '130-139 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '130-139 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '140-159 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 1
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '140-159 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '160+ mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 2
          } else if (
            this.obj.sex === '男性' &&
            this.obj.systoliPressure === '160+ mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '<120 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '<120 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 0
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '120-129 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 1
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '120-129 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '130-139 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 2
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '130-139 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '140-159 mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 3
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '140-159 mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 5
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '160+ mmHg' &&
            this.obj.bloodPressure === '无治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 4
          } else if (
            this.obj.sex === '女性' &&
            this.obj.systoliPressure === '160+ mmHg' &&
            this.obj.bloodPressure === '有治疗'
          ) {
            this.scoreSystoliPressureBloodPressure = 6
          }
          this.result =
            this.scoreAgeSex +
            this.scoreAgeSexSmoker +
            this.scoreAgeSexOverall +
            this.scoreGood +
            this.scoreSystoliPressureBloodPressure
          // 风险判断
          if (this.obj.sex === '男性') {
            if (this.result < 0) {
              this.risk = '<1%'
            } else if (
              this.result === 0 ||
              this.result === 1 ||
              this.result === 2 ||
              this.result === 3 ||
              this.result === 4
            ) {
              this.risk = '1%'
            } else if (this.result === 5 || this.result === 6) {
              this.risk = '2%'
            } else if (this.result === 7) {
              this.risk = '3%'
            } else if (this.result === 8) {
              this.risk = '4%'
            } else if (this.result === 9) {
              this.risk = '5%'
            } else if (this.result === 10) {
              this.risk = '6%'
            } else if (this.result === 11) {
              this.risk = '8%'
            } else if (this.result === 12) {
              this.risk = '10%'
            } else if (this.result === 13) {
              this.risk = '12%'
            } else if (this.result === 14) {
              this.risk = '16%'
            } else if (this.result === 15) {
              this.risk = '20%'
            } else if (this.result === 16) {
              this.risk = '25%'
            } else {
              this.risk = '30%'
            }
          } else if (this.obj.sex === '女性') {
            if (this.result < 9) {
              this.risk = '<1%'
            } else if (
              this.result === 9 ||
              this.result === 10 ||
              this.result === 11 ||
              this.result === 12
            ) {
              this.risk = '1%'
            } else if (this.result === 13 || this.result === 14) {
              this.risk = '2%'
            } else if (this.result === 15) {
              this.risk = '3%'
            } else if (this.result === 16) {
              this.risk = '4%'
            } else if (this.result === 17) {
              this.risk = '5%'
            } else if (this.result === 18) {
              this.risk = '6%'
            } else if (this.result === 19) {
              this.risk = '8%'
            } else if (this.result === 20) {
              this.risk = '11%'
            } else if (this.result === 21) {
              this.risk = '14%'
            } else if (this.result === 22) {
              this.risk = '17%'
            } else if (this.result === 23) {
              this.risk = '22%'
            } else if (this.result === 24) {
              this.risk = '27%'
            } else {
              this.risk = '30%'
            }
          }
        }
      },
    },
    'obj.age'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '年龄'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('1')
      // 触发 vuex 修改提交信息

      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.sex'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '性别'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('2')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.smoking'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '吸烟状况'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('3')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.overall'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '整体胆固醇水平'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('4')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.good'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '好的胆固醇水平'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('5')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.systoliPressure'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '收缩压'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('6')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
    'obj.bloodPressure'(n) {
      let scoringDetails = [[], [], [], [], []]
      // scoringDetails[0] 二级标题
      scoringDetails[0] = []
      scoringDetails[0].push(null)
      // scoringDetails[1] 再次评分 路径
      scoringDetails[1] = []
      scoringDetails[1].push(
        window.location.href.substring(window.location.href.indexOf('info') - 1)
      )
      // scoringDetails[2] 评分选项信息
      scoringDetails[2] = []
      let obj = {
        title: '',
        content: [],
        score: '',
        id: '',
      }
      obj.title = '血压是否被治疗'
      obj.content.push(n)
      obj.score = null
      scoringDetails[2].push(obj)
      // scoringDetails[3] 分数是否显示
      scoringDetails[3] = []
      scoringDetails[3].push(true)
      // scoringDetails[4] 选项是否显示
      scoringDetails[4].push('7')
      // 触发 vuex 修改提交信息
      this.$store.commit('minitool/setScoringDetails', scoringDetails)
    },
  },
  methods: {
    // 保存评分
    save() {
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        this.obj.path = window.location.href.substring(
          window.location.href.indexOf('info') - 1
        )
        // 姓名
        this.submitInformation.name =
          this.$store.state.minitool.PatientInformations.name
        // 性别
        this.submitInformation.gender =
          this.$store.state.minitool.PatientInformations.gender
        // 年龄
        this.submitInformation.age =
          this.$store.state.minitool.PatientInformations.age
        // 诊断结果
        this.submitInformation.diagnosis =
          this.$store.state.minitool.PatientInformations.diagnose
        // 评分详情
        this.submitInformation.scoreDetail = `${JSON.stringify(
          this.$store.state.minitool.scoringDetails
        )}`
        // 评分结果
        this.submitInformation.scoreResult = this.result * 1
        // 关联 id
        this.submitInformation.relationScoreId = this.$route.query.id
        // userId
        if (this.$cookies.get('medtion_user_only_sign') !== undefined) {
          this.submitInformation.userId = this.$cookies.get(
            'medtion_user_only_sign'
          ).id
        }

        // 患者信息是否完整
        if (this.$store.state.minitool.PatientInformationFull !== 'true') {
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '患者信息')
          // 评分选项是否全选
        } else if (
          this.obj.age === '请选择年龄' ||
          this.obj.sex === '请选择性别' ||
          this.obj.smoking === '请选择吸烟状况' ||
          this.obj.overall === '请选择整体胆固醇水平' ||
          this.obj.good === '请选择好的胆固醇水平' ||
          this.obj.systoliPressure === '请选择收缩压' ||
          this.obj.bloodPressure === '请选择血压是否被治疗'
        ) {
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '评分项')
          // 是否登录
        } else {
          // 普遍类型的处理
          // 将发送数据转换成数组类型
          const ScoreDetail = JSON.parse(this.submitInformation.scoreDetail)
          let sendScoreDetail = ''
          for (let i = 0; i < ScoreDetail.length; i++) {
            // 删除字符串末尾的符号：ScoreDetail[i][2][0].content[0].slice(0,ScoreDetail[i][2][0].content[0].length-1)
            sendScoreDetail +=
              '{' +
              (i + 1) +
              '.' +
              ScoreDetail[i][2][0].title +
              ':' +
              ScoreDetail[i][2][0].content[0].slice(
                0,
                ScoreDetail[i][2][0].content[0].length
              ) +
              '}'
          }
          this.submitInformation.scoreDetail = sendScoreDetail
          // 发请求 将信息通过参数传过去
          this.$axios.$request(
            saveToolScore(JSON.stringify(this.submitInformation))
          )
          this.$router.push('/minitool/page_save')
        }
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .Framingham {
    width: 877px;
    box-sizing: border-box;
    //background-color: rgba(5, 129, 206, 0.03);
    border-radius: 6px;
    margin-top: 18px;
    padding: 16px;

    p {
      font-weight: 700;
      font-size: 16px;
      color: #333333;
      width: 845px;
      height: 21px;
      line-height: 21px;
    }

    .describe {
      width: 500px;
      margin: 14px 0;
      box-sizing: border-box;
    }

    ul {
      li {
        display: flex;
        position: relative;

        .el-icon-caret-bottom {
          position: absolute;
          top: 5px;
          left: 315px;
          //color: #0581ce;
          font-size: 22px;
          transform: rotate(180deg);
        }

        .selectBefore {
          color: #888888;
        }

        .selectAfter {
          color: #333333;
        }

        p {
          width: 150px;
          height: 32px;
          line-height: 32px;
          font-weight: 400;
          font-size: 16px;
          color: #404040;
        }

        select {
          //background-color: #f7fbfb;
          border: none;
          width: 185px;
          outline: none;

          option {
            font-weight: 400;
            font-size: 16px;
            width: 128px;
            height: 32px;
            line-height: 32px;
            color: #333333;
          }
        }

        select:focus-visible {
          outline: none;
        }

        select::-ms-expand {
          display: none;
        }

        select {
          appearance: none;
          -moz-appearance: none;
          /* Firefox */
          -webkit-appearance: none;
          /* Safari 和 Chrome */
        }
      }

      .discolor {
        color: #0581ce;
        height: 32px;
        line-height: 32px;
      }
    }

    .result {
      display: flex;
      width: 200px;
      height: 21px;
      margin-bottom: 14px;

      p {
        width: 75px;
        height: 21px;
        color: #404040;
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
      }

      span:nth-of-type(1) {
        font-weight: 400;
        font-size: 16px;
        color: #888888;
      }

      span:nth-of-type(2) {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        color: #333333;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }
}

.el-icon-caret-bottom:before {
  content: '\e6e1' !important;
  //font-weight: bold;
}
</style>
