<template>
  <div>
    <form action="" method="post" target="targetIfr">
      <!-- 患者信息 -->
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="stool"></Options>
          <Options :information="urination"></Options>
          <Options :information="modification"></Options>
          <Options :information="defcation"></Options>
          <Options :information="eat"></Options>
          <Options :information="diversion"></Options>
          <Options :information="doings"></Options>
          <Options :information="dressing"></Options>
          <Options :information="staircase"></Options>
          <Options :information="bathe"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">说明：</span> <p>日常生活能力量表(Activity of Daily Living Scale，ADL)，由美国的Lawton氏和Brody制定于1969年。由躯体生活自理量表(Physical Self－maintenance Scale,PSMS)和工具性日常生活活动量表(Instrumental Activities of Daily Living Scale IADL)组成。主要用于评定被试的日常生活能力。也称为Barthel 指数。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '日常生活能力量表(ADL) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '巴氏量表（Barthel Index），是一个由医师团队来评估老年患者日常生活的体能，所做的日常生活功能之评估量表。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'the barthelindex of adl, ADL, Barthel'
        }
      ]
    }
  },
  data () {
    return {
      // 大便
      stool: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '1. 大便',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '1)失禁或昏迷;',
          '2)偶尔失禁（每周<1次）;',
          '3)能控制。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '失禁或昏迷;',
          '偶尔失禁（每周<1次）;',
          '能控制;',
        ],
      },
      // 小便
      urination: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '2. 小便',
        id: ['2-1', '2-2'],
        content: ['1)失禁活昏迷或需要人导尿;', '2)偶尔失禁(每24小时<1次，每周>1次);', '3)能控制。'],
        grade: ['0', '5','10'],
        simpleContent: ['失禁活昏迷或需要人导尿;', '偶尔失禁(每24小时<1次，每周>1次);', '能控制;'],
      },
      // 修饰
      modification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 165px;',
        title: '3. 修饰',
        id: ['3-1', '3-2'],
        content: [
          '1)需帮助;',
          '2)独立洗脸、梳头、刷牙、剃须。'
        ],
        grade: ['0', '5'],
        simpleContent: [
          '需帮助;',
          '独立洗脸、梳头、刷牙、剃须;'
        ],
      },
      // 用厕
      defcation: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '4. 用厕',
        id: ['4-1', '4-2', '4-3'],
        content: ['1)依赖别人;', '2)需部分帮助;', '3)自理。'],
        grade: ['0', '5', '10'],
        simpleContent: ['依赖别人;', '需部分帮助;', '自理;'],
      },
      // 吃饭
      eat: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '5. 吃饭',
        id: ['5-1', '5-2', '5-3'],
        content: [
          '1)依赖别人;',
          '2)需部分帮助(夹饭、盛饭、切面包);',
          '3)全面自理。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '依赖别人;',
          '需部分帮助(夹饭、盛饭、切面包);',
          '全面自理;',
        ],
      },
      // 转移 (床← →椅)
      diversion: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 430px;',
        title: '6. 转移 (床← →椅)',
        id: ['6-1', '6-2', '6-3', '6-4',],
        content: [
          '1)完全依赖别人，不能坐;',
          '2)需大量帮助(2人)，能坐;',
          '3)需少量帮助(1人)或指导;',
          '4)自理。'
        ],
        grade: ['0', '5', '10','15'],
        simpleContent: [
          '完全依赖别人，不能坐;',
          '需大量帮助(2人)，能坐;',
          '需少量帮助(1人)或指导;',
          '自理;'
        ],
      },
      // 活动(主要指步行，即在病房及其周围，不包括走远路
      doings: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 430px;',
        title: '7. 活动(主要指步行，即在病房及其周围，不包括走远路)',
        id: ['7-1', '7-2', '7-3', '7-4'],
        content: ['1)不能动;', '2)在轮椅上独立行动;', '3)在轮椅上独立行动;','4)独立步行(可用辅助器)。'],
        grade: ['0', '5', '10', '15'],
        simpleContent: ['不能动;', '在轮椅上独立行动;','在轮椅上独立行动;', '独立步行(可用辅助器);'],
      },
      // 转移（床、椅）
      dressing: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '8. 穿衣',
        id: ['8-1', '8-2', '8-3'],
        content: [
          '1)依赖;',
          '2)需一半帮助;',
          '3)自理(系开钮扣、关、开拉锁和穿鞋)。'
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '依赖;',
          '需一半帮助;',
          '自理(系开钮扣、关、开拉锁和穿鞋);'
        ],
      },
      // 上楼梯(上下一段楼梯，用手杖也算独立)
      staircase: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '9. 上楼梯(上下一段楼梯，用手杖也算独立)',
        id: ['9-1', '9-2', '9-3'],
        content: [
          '1)不能;',
          '2)需帮助(体力或语言指导);',
          '3)自理。'
        ],
        grade: ['0', '5', '10'],
        simpleContent: ['不能;', '需帮助(体力或语言指导);', '自理;'],
      },
      // 10.洗澡
      bathe: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 165px;',
        title: '10.洗澡',
        id: ['10-1', '10-2'],
        content: [
          '1)依赖;',
          '2)自理。'
        ],
        grade: ['0', '5'],
        simpleContent: ['依赖;', '自理;'],
      },
      // 选中的个数
      selected: 0,
      // 总分数
      totalPoints: 0,
      // 未选个数
      unselected: 10,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同结果)
    finalScore () {
      if (this.totalPoints ===100) {
        return `${this.totalPoints}分，独立`
      }else if (this.totalPoints >= 75 && this.totalPoints <=95) {
        return `${this.totalPoints}分，轻度依赖`
      } else if (this.totalPoints >= 50 && this.totalPoints <=70) {
        return `${this.totalPoints}分，中度依赖`
      }  else if (this.totalPoints >= 25 && this.totalPoints <=45) {
        return `${this.totalPoints}分，重度依赖`
      } else if (this.totalPoints >= 0 && this.totalPoints <= 20) {
        return `${this.totalPoints}分，完全依赖`
      }
    }
  },


  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '日常生活能力量表(ADL)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 10
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 100%;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
