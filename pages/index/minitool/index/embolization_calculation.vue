<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 1.动脉瘤体积计算 -->
        <div class="UIAvolume">
          <p>1.动脉瘤体积计算</p>
          <div class="describe">
            <ul>
              <li>
                <p>形态：</p>
                <i class="el-icon-caret-bottom"></i>
                <select class="caret-wrap" v-model="UIAform">
                  <option selected>椭圆型</option>
                  <option>分叶型</option>
                </select>
              </li>
              <div class="copy" v-for="(i, index) in UIAcopyNum" :key="i">
                <li class="discolor" v-show="UIAform === '分叶型'">
                  <span><span class="dot"></span>椭圆{{ i }}</span>
                </li>
                <li>
                  <label>
                    长度(mm):
                    <input
                      v-model.lazy="objUIA.lang[index]"
                      type="text"
                      placeholder="请输入动脉瘤长度"
                    />
                  </label>
                </li>
                <li>
                  <label>
                    宽度(mm):
                    <input
                      v-model.lazy="objUIA.wide[index]"
                      type="text"
                      placeholder="请输入动脉瘤宽度"
                    />
                  </label>
                </li>
                <li>
                  <label>
                    高度(mm):
                    <input
                      v-model.lazy="objUIA.hight[index]"
                      type="text"
                      placeholder="请输入动脉瘤高度"
                    />
                  </label>
                </li>
              </div>
            </ul>
          </div>
          <!-- 结果展示 -->
          <div class="result">
            <p>动脉瘤体积:</p>
            <span v-show="volumeResult === 0">暂无</span>
            <span v-show="volumeResult !== 0">{{ volumeResult }}mm&sup3;</span>
          </div>
        </div>
        <!-- 2.弹簧圈体积计算 -->
        <div class="springCoil">
          <p>2.弹簧圈体积计算</p>
          <div class="content">
            <ul>
              <li>
                <p>个数：</p>
                <select
                  :class="[
                    'selectBefore',
                    springForm === '1' ? 'selectBefore' : 'selectAfter',
                  ]"
                  v-model="springForm"
                >
                  <option selected>1</option>
                  <option v-for="i in 19" :key="i">{{ i + 1 }}</option>
                </select>
              </li>
              <div
                @click="serial(index)"
                v-for="(i, index) in springForm * 1"
                :key="i"
              >
                <li class="discolor">
                  <span><span class="dot"></span>弹簧圈{{ i }}</span>
                </li>
                <li>
                  <p>品牌：</p>
                  <div class="springCoilBgc">
                    <i class="el-icon-caret-bottom"></i>
                    <select
                      class="triangle"
                      :class="[
                        'selectBefore',
                        springBrand[index] === '请选择弹簧圈的品牌'
                          ? 'selectBefore'
                          : 'selectAfter',
                      ]"
                      v-model="springBrand[index]"
                    >
                      <option disabled hidden>请选择弹簧圈的品牌</option>
                      <option v-for="i in brands" :key="i.name">
                        {{ i.name }}
                      </option>
                      <option>手动输入</option>
                    </select>
                  </div>
                </li>
                <li v-show="handMovement[index] === true">
                  <p>产品：</p>
                  <div class="springCoilBgc">
                    <i class="el-icon-caret-bottom"></i>
                    <select
                      class="triangle"
                      :class="[
                        'selectBefore',
                        springProduct[index] === '请选择弹簧圈的产品'
                          ? 'selectBefore'
                          : 'selectAfter',
                      ]"
                      v-model="springProduct[index]"
                    >
                      <option selected disabled hidden>
                        请选择弹簧圈的产品
                      </option>
                      <option disabled v-show="product[indexId].length === 0">
                        请先选择弹簧圈的品牌
                      </option>
                      <option v-for="(i, ind) in product[index][0]" :key="ind">
                        {{ i.name }}
                      </option>
                    </select>
                  </div>
                </li>
                <!-- {{product[index]}} -->
                <li v-show="seriesShow[index] && handMovement[index] === true">
                  <p>系列：</p>
                  <div class="springCoilBgc">
                    <i class="el-icon-caret-bottom"></i>
                    <select
                      class="triangle"
                      :class="[
                        'selectBefore',
                        springSeries[index] === '请选择弹簧圈的系列'
                          ? 'selectBefore'
                          : 'selectAfter',
                      ]"
                      v-model="springSeries[index]"
                    >
                      <option selected disabled hidden>
                        请选择弹簧圈的系列
                      </option>
                      <option disabled v-show="series[indexId].length === 0">
                        请先选择弹簧圈的产品
                      </option>
                      <option v-for="(i, ind) in series[index][0]" :key="ind">
                        {{ i.name }}
                      </option>
                    </select>
                  </div>
                </li>
                <li v-show="handMovement[index] === true">
                  <p>型号：</p>
                  <div class="springCoilBgc">
                    <i class="el-icon-caret-bottom"></i>
                    <select
                      class="triangle"
                      :class="[
                        'selectBefore',
                        springModel[index] === '请选择弹簧圈的型号'
                          ? 'selectBefore'
                          : 'selectAfter',
                      ]"
                      v-model="springModel[index]"
                    >
                      <option selected disabled hidden>
                        请选择弹簧圈的型号
                      </option>
                      <option disabled v-show="model[indexId].length === 0">
                        请先选择弹簧圈的系列
                      </option>
                      <option v-for="(i, ind) in model[index][0]" :key="ind">
                        {{ i.name }}
                      </option>
                    </select>
                  </div>
                </li>
                <li class="diameter">
                  <label for="in">
                    <p>一级圈直径(in):</p>
                    <input
                      id="in"
                      type="text"
                      :placeholder="diameterHint[index]"
                      v-model.lazy="springData.diameter[index]"
                      :disabled="diameterImport[index]"
                    />
                  </label>
                </li>
                <li class="diameter">
                  <label for="cm">
                    <p>长度(cm):</p>
                    <input
                      id="cm"
                      type="text"
                      :placeholder="langHint[index]"
                      v-model.lazy="springData.lang[index]"
                      :disabled="langImport[index]"
                    />
                  </label>
                </li>
              </div>
            </ul>
          </div>
          <!-- 弹簧圈体积计算结果 -->
          <div class="result">
            <p>弹簧圈体积:</p>
            <span v-show="springResult === 0">暂无</span>
            <span v-show="springResult !== 0">{{ springResult }}mm&sup3;</span>
          </div>
          <!-- 弹簧圈体积计算结果 -->
          <div class="result">
            <p>栓塞率:</p>
            <span v-show="embolismResult === 0">暂无</span>
            <span v-show="embolismResult !== 0">{{ embolismResult }}%</span>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import { selectBrandProductModel, saveToolScore } from '@/api/minitool'

export default {
  // 注册组件
  components: { Patient, ScoringDetails },
  // tdk
  head() {
    return {
      title: '弹簧圈栓塞密度计算 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            '栓塞密度 (packing density) 是指填塞弹簧圈体积与动脉瘤体积的百分比。颅内动脉瘤栓塞密度计算小工具提供给医生一个在线的、实用的，便利的计算手段，用以指导临床工作。栓塞密度的主要误差体现在动脉瘤体积测量上，因此目前手工测量的动脉瘤长宽高，应尽可能地准确，或多次计算后取平均值，如果有体积自动化测量手段将更准确。',
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '微创临床, 动脉瘤, 弹簧圈, 栓塞密度, 血管内治疗, 颅内动脉瘤, 动脉瘤体积测量',
        },
      ],
    }
  },
  data() {
    return {
      // 动脉瘤体积计算 双向绑定的数据
      UIAform: '椭圆型',
      // 循环次数
      UIAcopyNum: 1,
      objUIA: {
        lang: [[]],
        wide: [[]],
        hight: [[]],
      },
      // 品牌
      brands: '',
      // 动脉瘤体积计算结果
      score: 0,
      volumeResult: 0,
      // 弹簧圈体积计算 双向绑定的数据
      springForm: 1,
      springBrand: ['请选择弹簧圈的品牌'],
      springProduct: ['请选择弹簧圈的产品'],
      springSeries: ['请选择弹簧圈的系列'],
      springModel: ['请选择弹簧圈的型号'],
      // 弹簧圈计算结果
      springResult: 0,
      // 栓塞率计算结果
      embolismResult: 0,
      changeSpringForm: false,
      brand: [],
      product: [[]],
      series: [[]],
      model: [[]],
      diameterHint: ['请先选择弹簧圈相关信息'],
      langHint: ['请先选择弹簧圈相关信息'],
      // 系列隐藏
      seriesShow: [true],
      indexId: 0,
      springData: {
        diameter: [[]],
        lang: [[]],
      },
      // 四舍五入之前的体积
      springScore: 0,
      roundUIA: 0,
      // input框正常使用
      diameterImport: [true],
      langImport: [true],
      // 手动输入
      handMovement: [true],
      // 系列 显示与隐藏
      continue: true,
      // 详情数据
      detailData: [],
      // 评分详情
      submitInformation: {
        name: '',
        gender: '',
        age: '',
        diagnosis: '',
        userId: '',
        scoreType: '',
        scoreTitle: '弹簧圈栓塞密度计算',
        scoreDetail: '',
        scoreResult: '',
        relationScoreId: '',
      },
    }
  },

  beforeMount() {
    localStorage.setItem('className', '动脉瘤')
    localStorage.setItem('wordLast', '弹簧圈栓塞密度计算')
  },

  mounted() {
    this.UIAform = '分叶型'
    // 初次加载页面拿取数据
    this.$axios
      .$request(
        selectBrandProductModel({
          typeId: 1,
        })
      )
      .then((res) => {
        // 品牌 数据
        this.brands = res.data
      })
    this.UIAform = '椭圆型'
  },
  watch: {
    // 深度监听
    objUIA: {
      deep: true,
      handler() {
        if (
          this.UIAcopyNum === 1 &&
          this.objUIA.lang[0].length !== 0 &&
          this.objUIA.wide[0].length !== 0 &&
          this.objUIA.hight[0].length !== 0
        ) {
          this.roundUIA =
            (4 / 3) *
            Math.PI *
            Math.pow(
              (parseFloat(this.objUIA.lang[0][0]) +
                parseFloat(this.objUIA.wide[0][0]) +
                parseFloat(this.objUIA.hight[0][0])) /
                6,
              3
            )
          this.volumeResult = this.roundUIA.toFixed(2)
        } else if (
          this.UIAcopyNum === 2 &&
          this.objUIA.lang[1].length !== 0 &&
          this.objUIA.wide[1].length !== 0 &&
          this.objUIA.hight[1].length !== 0 &&
          this.objUIA.lang[0].length !== 0 &&
          this.objUIA.wide[0].length !== 0 &&
          this.objUIA.hight[0].length !== 0
        ) {
          for (let i = 0; i < this.UIAcopyNum; i++) {
            this.roundUIA =
              (4 / 3) *
                Math.PI *
                Math.pow(
                  (parseFloat(this.objUIA.lang[0][0]) +
                    parseFloat(this.objUIA.wide[0][0]) +
                    parseFloat(this.objUIA.hight[0][0])) /
                    6,
                  3
                ) +
              (4 / 3) *
                Math.PI *
                Math.pow(
                  (parseFloat(this.objUIA.lang[1][0]) +
                    parseFloat(this.objUIA.wide[1][0]) +
                    parseFloat(this.objUIA.hight[1][0])) /
                    6,
                  3
                )
            this.volumeResult = this.roundUIA.toFixed(2)
          }
        }
        // 栓塞率
        if (this.volumeResult !== 0 && this.springResult !== 0) {
          this.embolismResult = ((this.springScore / this.roundUIA) * 1000) / 10
        }
      },
    },
    UIAform() {
      this.objUIA.lang = [[]]
      this.objUIA.hight = [[]]
      this.objUIA.wide = [[]]
      this.volumeResult = 0
      if (this.UIAform === '椭圆型') {
        this.UIAcopyNum = 1
      } else {
        this.UIAcopyNum = 2
        this.objUIA.lang.push([])
        this.objUIA.hight.push([])
        this.objUIA.wide.push([])
      }
    },

    // 监听 弹簧圈体积计算 的数据改变
    // 产品请求
    springBrand(n, o) {
      this.springResult = 0
      if (n[this.indexId] === '手动输入') {
        // 输入框正常输入
        this.handMovement[this.indexId] = false
        this.langImport[this.indexId] = false
        this.diameterImport[this.indexId] = false
        this.diameterHint[this.indexId] = '请输入弹簧圈一级圈直径'
        this.langHint[this.indexId] = '请输入弹簧圈长度'
        this.springProduct[this.indexId] = '请选择弹簧圈的产品'
        this.springSeries[this.indexId] = '请选择弹簧圈的系列'
        this.springModel[this.indexId] = '请选择弹簧圈的型号'
        if (this.changeSpringForm === true) {
          this.changeSpringForm = false
        } else {
          this.springData.diameter[this.indexId] = []
          this.springData.lang[this.indexId] = []
          this.changeSpringForm = false
        }
      } else {
        // 输入框不能正常输入
        this.langImport[this.indexId] = true
        this.diameterImport[this.indexId] = true
        this.handMovement[this.indexId] = true
        this.diameterHint[this.indexId] = '请先选择弹簧圈相关信息'
        this.langHint[this.indexId] = '请先选择弹簧圈相关信息'
        this.springProduct[this.indexId] = '请选择弹簧圈的产品'
        this.springSeries[this.indexId] = '请选择弹簧圈的系列'
        this.springModel[this.indexId] = '请选择弹簧圈的型号'
        if (this.changeSpringForm === true) {
          this.changeSpringForm = false
        } else {
          this.springData.diameter[this.indexId] = []
          this.springData.lang[this.indexId] = []
          this.changeSpringForm = false
        }
        // 输入的品牌名
        let a = n[this.indexId]
        // 循环品牌名
        this.brands.map((item) => {
          // 输入的值和品牌名相同
          if (a === item.name) {
            this.$axios
              .$request(
                selectBrandProductModel({
                  typeId: 2,
                  brandId: item.id,
                })
              )
              .then((res) => {
                if (this.product[this.indexId].length === 0) {
                  this.product[this.indexId].push(res.data)
                } else {
                  this.product[this.indexId].splice(0, 1)
                  this.product[this.indexId].push(res.data)
                }
                this.continue = true
              })
          }
        })
      }
    },
    // 系列请求
    springProduct(n) {
      this.embolismResult = 0
      this.springResult = 0
      if (this.continue === true) {
        let a = n[this.indexId]
        this.product[this.indexId][0].map((item) => {
          if (a === item.name) {
            this.$axios
              .$request(
                selectBrandProductModel({
                  typeId: 3,
                  productId: item.id,
                })
              )
              .then((res) => {
                if (res.data.length === 0) {
                  this.seriesShow[this.indexId] = false
                  // 没有系列 直接请求型号的数据
                  this.$axios
                    .$request(
                      selectBrandProductModel({
                        typeId: 4,
                        productId: item.id,
                      })
                    )
                    .then((res) => {
                      this.springModel[this.indexId] = '请选择弹簧圈的型号'
                      this.springSeries[this.indexId] = '请选择弹簧圈的系列'
                      this.springData.diameter[this.indexId] = ''
                      this.springData.lang[this.indexId] = ''
                      this.model[this.indexId].splice(0, 1)
                      if (this.model[this.indexId] !== undefined) {
                        this.model[this.indexId].push(res.data)
                      }
                    })
                } else {
                  // this.series = []
                  this.springSeries[this.indexId] = '请选择弹簧圈的系列'
                  this.springModel[this.indexId] = '请选择弹簧圈的型号'
                  this.springData.diameter[this.indexId] = ''
                  this.springData.lang[this.indexId] = ''
                  this.seriesShow[this.indexId] = true
                  this.series[this.indexId].splice(0, 1)
                  if (this.model[this.indexId] !== undefined) {
                    this.series[this.indexId].push(res.data)
                  }
                }
              })
          }
        })
      }
    },
    // 型号请求
    springSeries(n) {
      if (this.continue === true) {
        let a = n[this.indexId]
        this.series[this.indexId][0].map((item) => {
          if (a === item.name) {
            this.$axios
              .$request(
                selectBrandProductModel({
                  typeId: 4,
                  bmsProductModelId: item.id,
                })
              )
              .then((res) => {
                this.model[this.indexId].splice(0, 1)
                this.springModel[this.indexId] = '请选择弹簧圈的型号'
                if (this.model[this.indexId] !== undefined) {
                  this.model[this.indexId].push(res.data)
                }
              })
          }
        })
      }
    },
    // 直径 长度请求
    springModel(n) {
      this.springScore = 0
      if (this.continue === true) {
        let str1 = ''
        let str2 = ''
        let a = n[this.indexId]
        this.model[this.indexId][0].map((item) => {
          if (a === item.name) {
            for (let i = 0; i < item.modelCustomMap.length; i++) {
              if (item.modelCustomMap[i].name === '外径') {
                str1 = item.modelCustomMap[i].value
              }
              if (item.modelCustomMap[i].name === '长度') {
                str2 = item.modelCustomMap[i].value
              }
            }
            let length1 = str1.length
            let length2 = str2.length
            // 直径
            if (isNaN(str1.substring(length1 - 2)) === true) {
              this.springData.diameter[this.indexId] = str1.substring(
                0,
                length1 - 2
              )
            } else {
              this.springData.diameter[this.indexId] = str1
            }
            if (this.springData.diameter[this.indexId].length === 0) {
              this.langHint[this.indexId] = '请输入弹簧圈一级圈直径'
            }
            // 长度
            if (isNaN(str2.substring(length2 - 2)) === true) {
              this.springData.lang[this.indexId] = str2.substring(
                0,
                length2 - 2
              )
            } else {
              this.springData.lang[this.indexId] = str2
            }
            if (this.springData.lang[this.indexId].length === 0) {
              this.langHint[this.indexId] = '请输入弹簧圈长度'
              this.langImport[this.indexId] = false
            } else {
              this.langImport[this.indexId] = false
            }
          }
        })
      }
      // 弹簧圈体积
      if (
        this.indexId === this.springForm - 1 &&
        this.springData.diameter.length === this.springForm * 1 &&
        this.springData.lang.length === this.springForm * 1
      ) {
        for (let i = 0; i < this.springForm; i++) {
          this.springScore +=
            (Math.PI *
              Math.pow(parseFloat(this.springData.diameter[i]) * 25.4, 2) *
              parseFloat(this.springData.lang[i]) *
              10) /
            4
          if (
            isNaN(this.springScore) === true ||
            this.springData.diameter[this.indexId].length === 0 ||
            this.springData.lang[this.indexId].length === 0
          ) {
            this.springResult = 0
          } else {
            this.springResult = this.springScore.toFixed(2)
          }
        }
      }
      // 栓塞率
      if (this.volumeResult > 0 && this.springResult > 0) {
        this.embolismResult = ((this.springScore / this.roundUIA) * 1000) / 10
      } else {
        this.embolismResult = 0
      }
    },
    // 计算
    springData: {
      deep: true,
      handler() {
        this.springScore = 0
        // 弹簧圈体积
        for (let i = 0; i < this.springForm; i++) {
          this.springScore +=
            (Math.PI *
              Math.pow(parseFloat(this.springData.diameter[i]) * 25.4, 2) *
              parseFloat(this.springData.lang[i]) *
              10) /
            4
          if (
            isNaN(this.springScore) === true ||
            this.springData.diameter[this.indexId].length === 0 ||
            this.springData.lang[this.indexId].length === 0
          ) {
            this.springResult = 0
          } else {
            this.springResult = this.springScore.toFixed(2)
          }
        }
        // 栓塞率
        if (this.volumeResult !== 0 && this.springResult !== 0) {
          this.embolismResult = ((this.springScore / this.roundUIA) * 1000) / 10
        }
      },
    },
    springForm(n, o) {
      this.changeSpringForm = true
      if (n - o > 0) {
        for (let i = 0; i < n - o; i++) {
          this.springData.diameter.push([])
          this.springData.lang.push([])
          this.product.push([])
          this.series.push([])
          this.model.push([])
          this.diameterHint.push('请先选择弹簧圈相关信息')
          this.langHint.push('请先选择弹簧圈相关信息')
          this.diameterImport.push(true)
          this.langImport.push(true)
        }
      }
      this.continue = false
      this.springResult = 0
      this.springScore = 0

      for (let i = 0; i < n - 1; i++) {
        this.springBrand.push('请选择弹簧圈的品牌')
        this.springProduct.push('请选择弹簧圈的产品')
        this.springSeries.push('请选择弹簧圈的系列')
        this.springModel.push('请选择弹簧圈的型号')
        this.handMovement.push(true)
        this.seriesShow.push(true)
      }
      for (let i = 0; i < this.springForm; i++) {
        this.springScore +=
          (Math.PI *
            Math.pow(parseFloat(this.springData.diameter[i]) * 25.4, 2) *
            parseFloat(this.springData.lang[i]) *
            10) /
          4
        if (
          isNaN(this.springScore) === true ||
          this.springData.diameter[this.indexId].length === 0 ||
          this.springData.lang[this.indexId].length === 0
        ) {
          this.springResult = 0
        } else {
          this.springResult = this.springScore.toFixed(2)
        }
      }
    },
  },
  methods: {
    serial(e) {
      this.indexId = e
    },
    // 保存评分
    save() {
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        // 评分详情是否填写完整
        let integrity = true
        // 动脉瘤体积是否完整
        for (let i = 0; i < this.UIAcopyNum; i++) {
          if (this.objUIA.lang[i].length === 0) {
            integrity = false
          }
          if (this.objUIA.wide[i].length === 0) {
            integrity = false
          }
          if (this.objUIA.hight[i].length === 0) {
            integrity = false
          }
        }
        // 弹簧圈体积是否完整
        for (let i = 0; i < this.springForm; i++) {
          if (this.springBrand[i] === '请选择弹簧圈的品牌') {
            integrity = false
          } else if (
            this.springBrand[i] !== '手动输入' &&
            this.springBrand[i] !== '请选择弹簧圈的品牌'
          ) {
            if (this.springProduct[i] === '请选择弹簧圈的产品') {
              integrity = false
            }
            if (this.springSeries[i] === '请选择弹簧圈的系列') {
              integrity = false
            }
            if (this.springModel[i] === '请选择弹簧圈的型号') {
              integrity = false
            }
          }

          if (this.springData.diameter[i].length === 0) {
            integrity = false
          }
          if (this.springData.lang[i].length === 0) {
            integrity = false
          }
        }

        let objSpring = {
          springForm: this.springForm,
          springBrand: this.springBrand,
          springProduct: this.springProduct,
          springSeries: this.springSeries,
          springModel: this.springModel,
          springData: this.springData,
        }
        this.detailData = []
        this.detailData.push({
          // 动脉瘤体积计算
          title: '动脉瘤体积计算',
          content: this.objUIA,
          score: this.volumeResult,
        })
        this.detailData.push({
          // 弹簧圈体积计算
          title: '动脉瘤弹簧圈体积计算体积计算',
          content: objSpring,
          score: this.springResult,
        })
        this.detailData.push({
          path: window.location.href.substring(
            window.location.href.indexOf('info') - 1
          ),
        })
        // 姓名
        this.submitInformation.name =
          this.$store.state.minitool.PatientInformations.name
        // 性别
        this.submitInformation.gender =
          this.$store.state.minitool.PatientInformations.gender
        // 年龄
        this.submitInformation.age =
          this.$store.state.minitool.PatientInformations.age
        // 诊断结果
        this.submitInformation.diagnosis =
          this.$store.state.minitool.PatientInformations.diagnose
        // 评分详情
        this.submitInformation.scoreDetail = `${JSON.stringify(
          this.detailData
        )}`
        // 评分结果
        this.submitInformation.scoreResult = `栓塞率：${this.embolismResult}%`
        // 关联 id
        if (this.$route.query.id === undefined) {
          this.submitInformation.relationScoreId = ''
        } else {
          this.submitInformation.relationScoreId = this.$route.query.id
        }
        if (this.$cookies.get('medtion_user_only_sign') !== undefined) {
          // userId
          this.submitInformation.userId = this.$cookies.get(
            'medtion_user_only_sign'
          ).id
        }

        // 患者信息是否完整
        if (this.$store.state.minitool.PatientInformationFull !== 'true') {
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '患者信息')
          // 评分选项是否全选
        } else if (integrity !== true) {
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '评分项')
        } else {
          //  弹簧圈部分特殊处理发送部分
          if (this.submitInformation.scoreTitle == '弹簧圈栓塞密度计算') {
            // 特殊处理弹簧圈部分
            const ScoreDetail = JSON.parse(this.submitInformation.scoreDetail)
            //  初始化定义弹簧圈详情
            let CoilCoreDetail = {
              aneurysm: { shapes: [{}], type: '', volume: '' },
              springcoil: { coils: [] },
              embolization: '',
              number: 0,
              voilume: '',
            }
            /**
             * 1.动脉瘤体积部分的赋予
             */
            //1.赋予动脉瘤体积的长宽高
            // let shapes = [{ depth: '', width: '', height: '' }]
            let shapes = []
            for (let i = 0; i < this.UIAcopyNum; i++) {
              shapes.push({ depth: '', width: '', height: '' })
              shapes[i].depth = ScoreDetail[0].content.lang[i]
              shapes[i].width = ScoreDetail[0].content.wide[i]
              shapes[i].height = ScoreDetail[0].content.hight[i]
            }
            CoilCoreDetail.aneurysm.shapes = shapes
            //1.2赋予动脉瘤体积的类型
            CoilCoreDetail.aneurysm.type = ScoreDetail[0].title
            //1.3赋予动脉瘤体积的体积
            CoilCoreDetail.aneurysm.volume = ScoreDetail[0].score + 'mm'

            /**
             * 2.弹簧圈体积计算部分的赋予
             */
            // 2.1根据品牌做出判断个数 进行赋予
            let coils = []
            for (let i = 0; i < this.springForm; i++) {
              coils.push({
                // 弹簧圈品牌
                brand: this.springBrand[i],
                // 弹簧圈品牌id
                brandId: '11',
                // 弹簧圈一级圈直径
                diameter: '',
                // 弹簧圈长度
                length: '',
                // 弹簧圈型号
                model: '',
                // 弹簧圈型号id
                modelId: '1',
                // 弹簧圈产品
                product: '',
                // 弹簧圈系列
                series: '',
                // 弹簧圈系列id
                seriesId: '12',
              })
            }

            for (let i = 0; i < coils.length; i++) {
              // 一级圈直径
              coils[i].diameter = this.springData.diameter[i]
              // 长度
              coils[i].length = this.springData.lang[i]
              // 型号
              coils[i].model = this.springModel[i]
              // 产品
              coils[i].product = this.springProduct[i]
              // 系列
              coils[i].series = this.springSeries[i]
              // 这边最好将全部id赋值
            }
            //  弹簧圈体积赋值
            CoilCoreDetail.springcoil.volume = ScoreDetail[1].score
            // 弹簧圈栓塞率赋值
            CoilCoreDetail.springcoil.embolization = this.embolismResult + '%'

            // 弹簧圈体积计算coils赋予
            CoilCoreDetail.springcoil.coils = coils
            this.submitInformation.scoreDetail = JSON.stringify(CoilCoreDetail)
          }
          // 发请求 将信息通过参数传过去
          // return
          this.$axios.$request(
            saveToolScore(JSON.stringify(this.submitInformation))
          )
          this.$router.push('/minitool/page_save')
          localStorage.removeItem('springGoin')
        }
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
@import '~@/pages/index/minitool/index/embolization_calculation.less';
</style>
