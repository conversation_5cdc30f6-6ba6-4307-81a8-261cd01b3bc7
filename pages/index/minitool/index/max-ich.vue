<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <p class="subTitle">max-ICH评分较ICH评分可能更好地评价颅内出血的预后</p>
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in hemorrhage" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '颅内出血预后评分(max-ICH评分) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '适用于社区老人的独立性和轻型老年痴呆。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '功能活动问卷, FAQ'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      hemorrhage: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '1. 年龄(岁)',
          id: ['1-1', '1-2', '1-3', '1-4'],
          content: [
            '1) ≤69;',
            '2) 70-74;',
            '3) 75-79;',
            '4) ≥80。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['≤69;', '70-74;', '75-79;', '≥80;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '2. NIHSS',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1) 0–6;',
            '2) 7–13;',
            '3) 14–20;',
            '4) ≥ 21。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 170px;',
          title: '3. 颅内脑叶出血量(mL)',
          id: ['3-1', '3-2'],
          content: [
            '1) 脑叶出血 ≥30mL;',
            '2) 脑叶出血 <30mL。'
          ],
          grade: ['1', '0'],
          simpleContent: ['脑叶出血 ≥30mL;', '脑叶出血 <30mL;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 170px;',
          title: '4. 颅内非脑叶出血量(mL)',
          id: ['4-1', '4-2'],
          content: [
            '1) 非脑叶出血 ≥10mL;',
            '2) 非脑叶出血 <10mL。'
          ],
          grade: ['1', '0'],
          simpleContent: ['非脑叶出血 ≥10mL;', '非脑叶出血 <10mL;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 170px;',
          title: '5. 血肿破入脑室',
          id: ['5-1', '5-2'],
          content: [
            '1) 是;',
            '2) 否。'
          ],
          grade: ['1', '0'],
          simpleContent: ['是;', '否;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 170px;',
          title: '6. 口服抗凝药物',
          id: ['6-1', '6-2'],
          content: [
            '1) 是;',
            '2) 否。'
          ],
          grade: ['1', '0'],
          simpleContent: ['是;', '否;']
        },
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 6,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      const prognosis = {
        0: "0分，良好预后为91-100%。",
        1: "1分，良好预后为77-97%，死亡率为0-10%。",
        2: "2分，良好预后为56-83%，死亡率为4-24%。",
        3: "3分，良好预后为40-62%，死亡率为13-31%。",
        4: "4分，良好预后为31-51%，死亡率为19-38%。",
        5: "5分，良好预后为13-30%，死亡率为33-54%。",
        6: "6分，良好预后为4-29%，死亡率为34-68%。",
        7: "7分，良好预后为0-16%，死亡率为53-87%。",
        8: "8分，良好预后为0%，死亡率为50-100%。",
        9: "9分，接近100%死亡。",
        10: "10分，接近100%死亡。"
      };

      if (prognosis[this.totalPoints]) {
        return prognosis[this.totalPoints];
      } else {
        return "未知预后情况";
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '颅内出血预后评分(max-ICH评分)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 6
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 35px 0 -10px 0;
}
</style>
