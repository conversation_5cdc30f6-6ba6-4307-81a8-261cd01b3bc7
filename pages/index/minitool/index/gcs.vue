<template>
  <div>
    <form action="#" method="post" target="targetIfr">
      <!-- 患者信息组件 -->
      <Patient :parameter="rewritePatient"></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 切换按钮 -->
        <div class="button">
          <div @click="changeBtnadult" :class="{ 'change-button': btnClass === false }">
            成人
          </div>
          <div @click="changeBtnChild" style="width: 120px" :class="{ 'change-button': btnClass === true }">
            儿童(&lt4岁)
          </div>
        </div>
        <!-- 成人 评分内容详情 -->
        <div @click="getTatalsadult($event)" class="matter" v-show="show === true" ref="referenceOne">
          <!-- 睁眼 -->
          <Options :information="adultOpenEyes"></Options>
          <!-- 语言 -->
          <Options :information="adultLanguage"></Options>
          <!-- 运动 -->
          <Options :information="adultSport"></Options>
        </div>
        <!-- 儿童 评分内容详情 -->
        <div @click="getTatalschild($event)" class="matter" v-show="show === false" ref="referenceTwo">
          <!-- 睁眼 -->
          <Options :information="childOpenEyes"></Options>
          <!-- 语言 -->
          <Options :information="childLanguage"></Options>
          <!-- 运动 -->
          <Options :information="childSport"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'GCS评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '格拉斯哥昏迷评分法（GCS, Glasgow Coma Scale）是医学上评估病人昏迷程度的方法。昏迷程度以三者分数相加来评估，得分值越高，提示意识状态越好，格拉斯哥昏迷评分法（GCS）来判断病人的意识情况，比较客观。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'GCS, glasgow coma scale, 格拉斯哥昏迷评分, 睁眼反应, 语言反应, 肢体运动, 意识障碍'
        }
      ]
    }
  },
  data () {
    return {
      // element ui
      radio: 3,
      // 按钮样式
      btnClass: false,
      // 成人和儿童的切换
      show: true,
      // 成人 睁眼
      adultOpenEyes: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '1. 睁眼',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '1) 自发睁眼：4分;',
          '2) 语言吩咐睁眼：3分;',
          '3) 疼痛刺激睁眼：2分;',
          '4) 无睁眼：1分。',
        ],
        grade: ['4', '3', '2', '1'],
        simpleContent: [
          '自发睁眼;',
          '语言吩咐睁眼;',
          '疼痛刺激睁眼;',
          '无睁眼;',
        ],
        subTitle: '成人',
      },
      // 成人 语言
      adultLanguage: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 543px;',
        title: '2. 言语',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: [
          '1) 正常交谈：5分;',
          '2) 回答错误：4分;',
          '3) 胡言乱语：3分;',
          '4) 只能发音：2分;',
          '5) 无发音：1分。',
        ],
        grade: ['5', '4', '3', '2', '1'],
        simpleContent: [
          '正常交谈;',
          '回答错误;',
          '胡言乱语;',
          '只能发音;',
          '无发音;',
        ],
        subTitle: '成人',
      },
      // 成人 运动
      adultSport: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 664px;',
        title: '3. 运动',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5', '3-6'],
        content: [
          '1) 按吩咐动作：6分;',
          '2) 对疼痛刺激定位反应：5分;',
          '3) 逃避行为：4分;',
          '4) 异常屈曲（去皮层状态）：3分;',
          '5) 异常伸展（去脑状态）：2分;',
          '6) 无反应：1分。',
        ],
        grade: ['6', '5', '4', '3', '2', '1'],
        simpleContent: [
          '按吩咐动作;',
          '对疼痛刺激定位反应;',
          '逃避行为;',
          '异常屈曲（去皮层状态）;',
          '异常伸展;',
          '无反应;',
        ],
        subTitle: '成人',
      },
      // 儿童 睁眼
      childOpenEyes: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '1. 睁眼',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: [
          '1) 自发睁眼：4分;',
          '2) 语言吩咐睁眼：3分;',
          '3) 疼痛刺激睁眼：2分;',
          '4) 无睁眼：1分。',
        ],
        grade: ['4', '3', '2', '1'],
        simpleContent: [
          '自发睁眼;',
          '语言吩咐睁眼;',
          '疼痛刺激睁眼;',
          '无睁眼;',
        ],
        subTitle: '儿童（＜4岁）',
      },
      // 儿童 语言
      childLanguage: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 543px;',
        title: '2. 言语',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: [
          '1) 微笑，声音定位，注视物体，互动：5分;',
          '2) 哭闹，但可以安慰；不正确的互动：4分;',
          '3) 对安慰异常反应，呻吟：3分;',
          '4) 无法安慰：2分;',
          '5) 无语言反应：1分。',
        ],
        grade: ['5', '4', '3', '2', '1'],
        simpleContent: [
          '微笑，声音定位，注视物体，互动;',
          '哭闹，但可以安慰；不正确的互动;',
          '对安慰异常反应，呻吟;',
          '无法安慰;',
          '无语言反应;',
        ],
        subTitle: '儿童（＜4岁）',
      },
      // 儿童 运动
      childSport: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 664px;',
        title: '3. 运动',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5', '6-6'],
        content: [
          '1) 正常（服从命令）：6分;',
          '2) 对疼痛刺激定位反应：5分;',
          '3) 逃避行为：4分;',
          '4) 异常屈曲（去皮层状态）：3分;',
          '5) 异常伸展（去脑状态）：2分;',
          '6) 无反应：1分。',
        ],
        grade: ['6', '5', '4', '3', '2', '1'],
        simpleContent: [
          '正常（服从命令）;',
          '对疼痛刺激定位反应;',
          '逃避行为;',
          '异常屈曲（去皮层状态）;',
          '异常伸展（去脑状态）;',
          '无反应;',
        ],
        subTitle: '儿童（＜4岁）',
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 3,
      // 总分数
      totalPoints: 0,
      // 创建空数组，用来存放所有 input
      adultArr: [],
      childArr: [],
      rewritePatient: '',
      childStatus: [],
      adultStatus: [],
      // 切换按钮 并点击，清空vuex中需要提交的数据
    }
  },
  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'GCS评分')
  },

  mounted () {
    // 成人儿童转换
    if (localStorage.getItem('switchover') === null) {
      localStorage.setItem('switchover', '成人')
    }

    if (localStorage.getItem('switchover') === '成人') {
      this.btnClass = false
      this.show = true
    } else {
      this.btnClass = true
      this.show = false
    }

    if (localStorage.getItem('associatedScore') === 'true') {
      this.$store.commit('minitool/setRelevanceSkip', true)
      this.$store.commit(
        'minitool/setAssociatedScore',
        localStorage.getItem('associatedScore')
      )
    }
    // 获取到成人所有的input框
    for (let i = 0; i < this.$refs.referenceOne.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceOne.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.adultArr.push(
          this.$refs.referenceOne.children[i].children[0].children[3].children[
            j
          ].children[0]
        )
      }
    }
    // 获取到儿童所有的input框
    for (let i = 0; i < this.$refs.referenceTwo.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceTwo.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.childArr.push(
          this.$refs.referenceTwo.children[i].children[0].children[3].children[
            j
          ].children[0]
        )
      }
    }
    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('relevanceScore') !== null
    ) {
      // 成人
      if (localStorage.getItem('switchover') === '成人') {
        this.adultArr.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('relevanceScore'))[
            index
          ].choice
        })
      } else {
        // 儿童
        this.childArr.map((item, index) => {
          item.checked = JSON.parse(
            localStorage.getItem('scoringDetailsChild')
          )[index].choice
        })
      }
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.adultArr.forEach((element) => {
      this.adultStatus.push(element.checked)
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.childArr.forEach((element) => {
      this.adultStatus.push(element.checked)
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 成人 儿童 按钮点击事件
    changeBtnadult () {
      this.btnClass = false
      this.show = true
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      localStorage.setItem('switchover', '成人')
      // 按钮切换后 根据选项的状态进行勾选
      this.adultArr.map((item, index) => {
        item.checked = this.adultStatus[index]
      })
      // 循环数组 拿去分数和勾选个数
      this.adultArr.forEach((element) => {
        this.adultStatus.push(element.checked)
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
    },
    changeBtnChild () {
      this.btnClass = true
      this.show = false
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      localStorage.setItem('switchover', '儿童(<4岁)')
      // 按钮切换后 根据选项的状态进行勾选
      this.childArr.map((item, index) => {
        item.checked = this.childStatus[index]
      })
      // 循环数组 拿去分数和勾选个数
      this.childArr.forEach((element) => {
        this.childStatus.push(element.checked)
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
    },
    // 选项 点击事件
    getTatalsadult (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      let optionMessage = []
      this.adultStatus = []
      // 遍历 arr 数组
      this.adultArr.forEach((element) => {
        this.adultStatus.push(element.checked)
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      // gcs的选中状态 存入特殊的变量里 防止关联评分时混乱
      localStorage.setItem('relevanceScore', JSON.stringify(optionMessage))
    },
    getTatalschild (e) {
      // 选择之后 改变状态
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      this.childStatus = []
      let optionMessage = []
      // 遍历 arr 数组
      this.childArr.forEach((element) => {
        this.childStatus.push(element.checked)
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetailsChild', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          score: this.totalPoints,
          path: this.$route.query.fromPath,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },

  beforeDestroy () {
    // localStorage.removeItem('switchover')
  },
}
</script>

<style lang="less" scoped>
@import '~@/pages/index/minitool/index/GCS.less';
</style>
