<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in faq" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '功能活动问卷（FAQ） - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '适用于社区老人的独立性和轻型老年痴呆。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '功能活动问卷, FAQ'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      faq: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '1. 每月平衡收支的能力，算账的能力',
          id: ['1-1', '1-2', '1-3', '1-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '2. 患者的工作能力',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '3. 能否到商店买衣服、杂货和家庭用品',
          id: ['3-1', '3-2', '3-3', '3-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '4. 有无爱好？会不会下棋和打扑克',
          id: ['4-1', '4-2', '4-3', '4-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '5. 会不会做简单的事情、如点炉子、泡茶等',
          id: ['5-1', '5-2', '5-3', '5-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '6. 会不会准备饭菜',
          id: ['6-1', '6-2', '6-3', '6-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '7. 能否了解最近发生的事件',
          id: ['7-1', '7-2', '7-3', '7-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '8. 能否参加讨论和了解电视、书和杂志的内容',
          id: ['8-1', '8-2', '8-3', '8-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '9. 能否记住约会时间、家庭节日和吃药',
          id: ['9-1', '9-2', '9-3', '9-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '10. 能否拜访邻居、自己乘公共汽车',
          id: ['10-1', '10-2', '10-3', '10-4'],
          content: [
            '1) 正常或从未做过，但能做：0分;',
            '2) 困难但可单独完成或从未做过：1分;',
            '3) 需要帮助：2分;',
            '4) 完全依赖他人：3分。'
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['正常或从未做过，但能做;', '困难但可单独完成或从未做过;', '需要帮助;', '完全依赖他人;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 10,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints > 5) {
        return `${this.totalPoints}分，异常，该患者在家庭和社区中不可能独立。`
      }else {
        return `${this.totalPoints}分，正常。`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '功能活动问卷(FAQ)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 10
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
