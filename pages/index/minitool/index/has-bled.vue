<template>
  <div @click="exhibition">
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <MultipleChoice :information="hazard"></MultipleChoice>
        </div>
        <!-- 附加说明 -->
        <div>
          <div
            @click.stop="showOne"
            class="icon"
            style="left: 308px; top: 100px"
          >
            i
          </div>
          <div
            v-show="showOnes === true"
            class="content"
            style="left: 325px; top: 9px"
          >
            异常的肝功能指慢性肝病(如肝硬化)或显著的生化指标紊乱(如胆红素>正常值上限的2倍，并且谷丙转氨酶/谷草转氨酶/碱性磷酸酶>正常值上限的3倍等)
          </div>

          <div
            @click.stop="showTwo"
            class="icon"
            style="left: 498px; top: 100px"
          >
            i
          </div>
          <div
            v-show="showTwos === true"
            class="content"
            style="left:510px; top: 9px"
          >
            肾功能异常定义为慢性透析或肾移植或血清肌酐≥200微摩尔/升
          </div>

          <div
            @click.stop="showThree"
            class="icon"
            style="left: 790px; top: 100px"
          >
            i
          </div>
          <div
            v-show="showThrees === true"
            class="content"
            style="left: 628px; top: 7px"
          >
            出血指既往有出血病史和（或）出血的诱因如出血体质、贫血等
          </div>

          <div
            @click.stop="showFour"
            class="icon"
            style="left: 156px; top: 137px"
          >
            i
          </div>
          <div
            v-show="showFours === true"
            class="content"
            style="left: 170px; top: 45px"
          >
            INR值不稳定指INR值易变/偏高或达不到治疗范围(如&lt60％)
          </div>

          <div
            @click.stop="showFive"
            class="icon"
            style="left: 488px; top: 137px"
          >
            i
          </div>
          <div
            v-show="showFives === true"
            class="content"
            style="left: 500px; top: 45px"
          >
            如抗血小板药，非甾体类抗炎药等
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0"
              >{{ totalPoints }}<span>分</span></span
            >
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span
            >个评分项，尚有<span>{{ unselected }}</span
            >个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>积分≥3分时提示“高危”，出血高危患者无论接受华法林还是阿司匹林治疗，均应谨慎，并在开始抗栓治疗之后定期复查。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div
            :class="{ scoreBtn: unselected === 0 }"
            @click="save"
            class="btn"
          >
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, MultipleChoice, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'HAS-BLED评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'HAS-BLED评分标准，主要是针对心房颤动患者的出血风险来进行量化评分。如果通过评分发现存在着出血高风险，在这一部分患者应用抗凝药物过程中就需要特别的慎重，需要关注常见的可能性出血，像消化道出血，要进行便常规的检测，要密切监测血常规中的血红蛋白变化。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '房颤血栓, 脑卒中, HAS-BLED, 高血压, 心房颤动, 脑小血管疾病, 颅内出血, TIA, 脑微出血, 出血'
        }
      ]
    }
  },
  data() {
    return {
      // 危险因素
      hazard: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title: '1. 危险因素',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7', '1-8', '1-9'],
        content: [
          '高血压：1分',
          '肝功能异常：1分',
          '肾功能异常：1分',
          '卒中：1分',
          '出血：1分',
          'INR值不稳定：1分',
          '老年>65岁：1分',
          '药物：1分',
          '饮酒，嗜酒：1分',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '1', '1', '1'],
        simpleContent: [
          '高血压;',
          '肝功能异常;',
          '肾功能异常;',
          '卒中;',
          '出血;',
          'INR值不稳定;',
          '老年>65岁;',
          '药物;',
          '饮酒，嗜酒;',
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 1,
      choiceBox: [],
      // 附加说明的显示与隐藏
      showOnes: false,
      showTwos: false,
      showThrees: false,
      showFours: false,
      showFives: false,
    }
  },

  beforeMount() {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', 'HAS-BLED评分')
  },

  mounted() {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[1].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked(element) {
      if (element.checked === true) {
        this.selected = 1
        this.totalPoints += 1
        this.unselected = 0
      }
    },
    getTatals(e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 1
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    showOne() {
      this.showOnes = true
      this.showTwos = false
      this.showThrees = false
      this.showFours = false
      this.showFives = false
    },
    showTwo() {
      this.showTwos = true
      this.showOnes = false
      this.showThrees = false
      this.showFours = false
      this.showFives = false
    },
    showThree() {
      this.showThrees = true
      this.showOnes = false
      this.showTwos = false
      this.showFours = false
      this.showFives = false
    },
    showFour() {
      this.showFours = true
      this.showOnes = false
      this.showTwos = false
      this.showThrees = false
      this.showFives = false
    },
    showFive() {
      this.showFives = true
      this.showOnes = false
      this.showTwos = false
      this.showThrees = false
      this.showFours = false
    },
    exhibition() {
      this.showOnes = false
      this.showTwos = false
      this.showThrees = false
      this.showFours = false
      this.showFives = false
    },
    // 保存评分
    save() {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
