<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in rass" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ resultExplain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Richmond躁动-镇静评分(RASS) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Richmond躁动-镇静评分(RASS)是临床上用于评估重症监护患者镇静程度和躁动状态的工具。该评分标准是一种简单、客观和可靠的评估工具，可以帮助医生确定何时应增加或减少镇静剂的使用。 RASS评分可以帮助医生调整镇静剂的剂量，从而达到有效的镇静和控制躁动的目的，同时避免过度镇静和呼吸抑制等不良反应的发生。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'RASS, 躁动-镇静评分, 躁动镇静评分, Richmond Agitation–Sedation Scale'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      rass: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 819px;',
          title: '1. 术语/描述',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7', '1-8', '1-9', '1-10'],
          content: [
            '1) 有攻击性：明显的暴力行为，对工作人员有威胁;',
            '2) 非常躁动：试着拔出呼吸管，胃管或静脉点滴;',
            '3) 躁动焦虑：身体无意义的频繁移动，无法配合呼吸机;',
            '4) 不安焦虑：焦虑紧张但身体只有轻微的移动;',
            '5) 清醒平静：清醒自然状态;',
            '6) 昏昏欲睡：没有完全清醒，但可声音唤醒并维持清醒（睁眼且有眼神交流），>10s;',
            '7) 轻度镇静：声音唤醒后短暂维持清醒，<10s;',
            '8) 中度镇静：对声音有反应或睁眼（但无眼神交流）;',
            '9) 重度镇静：对物理刺激有反应或睁眼;',
            '10) 昏迷：对声音和物理刺激均无反应。'
          ],
          grade: ['+4', '+3', '+2', '+1', '0', '-1', '-2', '-3', '-4', '-5'],
          simpleContent: ['有攻击性：明显的暴力行为，对工作人员有威胁;', '非常躁动：试着拔出呼吸管，胃管或静脉点滴;', '躁动焦虑：身体无意义的频繁移动，无法配合呼吸机;', '不安焦虑：焦虑紧张但身体只有轻微的移动;', '清醒平静：清醒自然状态;', '昏昏欲睡：没有完全清醒，但可声音唤醒并维持清醒（睁眼且有眼神交流），>10s;', '轻度镇静：声音唤醒后短暂维持清醒，<10s;', '中度镇静：对声音有反应或睁眼（但无眼神交流）;', '重度镇静：对物理刺激有反应或睁眼;', '昏迷：对声音和物理刺激均无反应;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 1,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return 'RASS评分的操作方法：\n第1步：观察病人\n●病人清醒、不安焦虑、或躁动——> 评分0~+4\n第2步：若病人不清醒，用名字唤醒病人并令其睁眼看着说话人\n●病人可睁眼，有眼神交流，并维持该状态——> 评分 - 1\n●病人可睁眼，有眼神交流，但无法维持——> 评分 -2 \n第3步：若病人对声音无反应，摇晃肩膀或抚摸胸口唤醒病人\n●病人对物理刺激有反应或睁眼——> 评分 - 4\n●病人对所有刺激均无反应——> 评分 - 5'
    },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints > 0) {
        return `+${this.totalPoints}分`
      } else {
        return `${this.totalPoints}分`
      }
    },
    finalScoreSave () {
      if (this.totalPoints > 0) {
        return `+${this.totalPoints}`
      } else {
        return `${this.totalPoints}`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'Richmond躁动-镇静评分(RASS)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 1
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScoreSave,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 150px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
