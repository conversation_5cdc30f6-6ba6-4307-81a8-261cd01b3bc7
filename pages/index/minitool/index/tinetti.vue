<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="motorFunction" class="oneTopic">
            <p class="title">平衡测试</p>
            <Options v-for="i in selfCareAbility" :key="i.title" :information="i"></Options>
          </div>
          <div ref="cognitiveFunction" class="cognitive-function">
            <p class="title">步态测试</p>
            <Options v-for="i in sphincterControl" :key="i.title" :information="i"></Options>
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected !== 19">暂无</span>
            <span v-show="selected == 19">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import Options from '@/components/MiniTool/Options.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options },
  // tdk
  head () {
    return {
      title: 'Tinetti步态和平衡量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Tinetti步态和平衡量表是一种用于评估老年人步态和平衡功能的常用工具。该量表包括16个项目，涵盖了步态、平衡能力和危险因素的评估。通过对患者的观察和测试，可以评估出他们的步行能力、姿势控制、平衡感以及躯干和四肢的功能。每个项目都根据患者的表现进行打分，最终得分的总和可反映患者的步态和平衡能力。该量表可以帮助医生识别老年人中存在的步态和平衡问题，评估其日常生活能力和跌倒风险，并制定相应的康复计划。通过使用Tinetti步态和平衡量表，可以提供有效的步态和平衡评估，以保障老年人的安全和健康生活。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Tinetti步态和平衡评定量表,Tinetti尺度,Tinetti失衡评估,Tinetti步态评估量表,Tinetti平衡评估工具,Tinetti平衡分析量表,Tinetti姿势评估量表,Tinetti步态和平衡测量工具'
        }
      ]
    }
  },
  data () {
    return {
      // 自理能力
      selfCareAbility: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '1. 坐位平衡',
          id: ['1-1', '1-2'],
          content: [
            '1) 斜靠或从椅子上滑下：0分;',
            '2) 稳定：1分。',
          ],
          grade: ['0', '1'],
          simpleContent: ['斜靠或从椅子上滑下;', '稳定;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 起身',
          id: ['2-1', '2-2'],
          content: [
            '1) 没有帮助就无法完成：0分;',
            '2) 用胳膊帮助才能完成：1分;',
            '3) 不用胳膊就能完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有帮助就无法完成;', '用胳膊帮助才能完成;', '不用胳膊就能完成;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 试图起身',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 没有帮助就无法完成：0分;',
            '2) 需要尝试1次以上才能完成：1分;',
            '3) 1次尝试就能完成：2分。',
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有帮助就无法完成;', '需要尝试1次以上才能完成;', '1次尝试就能完成;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 立即站起来时平衡功能（站起的头5秒）',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 不稳（摇晃，移动脚步，明显躯干摆动）：0分;',
            '2) 稳定，但是需要助行器或手杖，或抓住其他物体支撑：1分;',
            '3) 稳定，不需要助行器或手杖，或抓住其他物体支撑：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不稳（摇晃，移动脚步，明显躯干摆动）;', '稳定，但是需要助行器或手杖，或抓住其他物体支撑;', '稳定，不需要助行器或手杖，或抓住其他物体支撑;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '5. 坐下时平衡',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 不稳：0分;',
            '2) 稳定，但两脚距离较宽（足跟中点间距离大于4英寸，或使用手杖、助行器或其他支撑）：1分;',
            '3) 稳定，两脚距离较窄，且不需要支撑：2分。',
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不稳;', '稳定，但两脚距离较宽（足跟中点间距离大于4英寸，或使用手杖、助行器或其他支撑）;', '稳定，两脚距离较窄，且不需要支撑;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '6. 轻推（患者双脚尽可能靠拢站立，用手轻推3次）',
          id: ['6-1', '6-2', '6-3'],
          content: [
            '1) 开始就会摔倒：0分;',
            '2) 摇晃并要抓东西，但是只抓自己：1分;',
            '3) 稳定：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['开始就会摔倒;', '摇晃并要抓东西，但是只抓自己;', '稳定;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '7. 闭眼（患者双脚尽可能靠拢站立，用手轻推3次）',
          id: ['7-1', '7-2'],
          content: [
            '1) 不稳：0分;',
            '2) 稳定：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['不稳;', '稳定;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '8. 转身360°',
          id: ['8-1', '8-2', '8-3'],
          content: [
            '1) 不连续的步骤：0分;',
            '2) 不稳定（手臂及身体摇晃）：1分;',
            '3) 稳定：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不连续的步骤;', '不稳定（手臂及身体摇晃）;', '稳定;']
        },{
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '9. 坐下°',
          id: ['9-1', '9-2', '9-3'],
          content: [
            '1) 不安全：0分;',
            '2) 用胳膊或动作不连贯：1分;',
            '3) 安全且动作连贯：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不安全;', '用胳膊或动作不连贯;', '安全且动作连贯;']
        },
      ],
      // 括约肌控制
      sphincterControl: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '1. 起步',
          id: ['10-1','10-2'],
          content: [
            '1) 有迟疑，或须尝试多次方能启动：0分;',
            '2) 正常启动：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['有迟疑，或须尝试多次方能启动;', '正常启动;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '2. 抬脚高度(a.左脚跨步)',
          id: ['11-1', '11-2'],
          content: [
            '1) 脚拖地，或抬高大于1~2英寸：0分;',
            '2) 脚完全离地，但不超过1~2英寸：2分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['脚拖地，或抬高大于1~2英寸;', '脚完全离地，但不超过1~2英寸;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '3. 抬脚高度(b.右脚跨步)',
          id: ['12-1', '12-2'],
          content: [
            '1) 脚拖地，或抬高大于1~2英寸：0分;',
            '2) 脚完全离地，但不超过1~2英寸：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['脚拖地，或抬高大于1~2英寸;', '脚完全离地，但不超过1~2英寸;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '4. 步长(a.左脚跨步)',
          id: ['13-1', '13-2'],
          content: [
            '1) 跨步的脚未超过站立的对侧脚：0分;',
            '2) 有超过站立的对侧脚：1分;',
          ],
          grade: ['0', '1'],
          simpleContent: ['跨步的脚未超过站立的对侧脚;', '有超过站立的对侧脚;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '5. 步长(b.右脚跨步)',
          id: ['14-1', '14-2'],
          content: [
            '1) 脚拖地，或抬高大于1~2英寸：0分;',
            '2) 脚完全离地，但不超过1~2英寸：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['脚拖地，或抬高大于1~2英寸;', '脚完全离地，但不超过1~2英寸;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '6. 步态对称性',
          id: ['15-1', '15-2'],
          content: [
            '1) 两脚步长不等：0分;',
            '2) 两脚步长相等：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['两脚步长不等;', '两脚步长相等;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '7. 步伐连续性',
          id: ['16-1', '16-2'],
          content: [
            '1) 步伐与步伐之间不连续或中断：0分;',
            '2) 步伐连续：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['步伐与步伐之间不连续或中断;', '步伐连续;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '8. 走路路径（行走大约三公尺长）',
          id: ['17-1', '17-2', '17-3'],
          content: [
            '1) 明显偏移到某一边：0分;',
            '2) 轻微/中度偏移或使用步行辅具：1分;',
            '3) 走直线，且不需辅具：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['明显偏移到某一边;', '轻微/中度偏移或使用步行辅具;', '走直线，且不需辅具;']
        },{
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '9. 躯干稳定',
          id: ['18-1', '18-2', '18-3'],
          content: [
            '1) 身体有明显摇晃或需使用步行辅具：0分;',
            '2) 身体不晃，但需屈膝或有背痛或张开双臂以维持平衡：1分;',
            '3) 身体不晃，无屈膝，不需张开双臂或使用辅具：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['身体有明显摇晃或需使用步行辅具;', '身体不晃，但需屈膝或有背痛或张开双臂以维持平衡;','身体不晃，无屈膝，不需张开双臂或使用辅具;']
        },{
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '10. 步宽（脚跟距离）',
          id: ['19-1', '19-2'],
          content: [
            '1) 脚跟分开（步宽大）：0分;',
            '2) 走路时两脚跟几乎靠在一起：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['脚跟分开（步宽大）;', '走路时两脚跟几乎靠在一起;']
        },
      ],
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 19,
      // 总分数
      totalPoints: 0,
      choiceBoxs: [[], []],
      score: {
        // 运动功能 分数
        motorFunctionScore: 0,
        // 认知功能 分数
        cognitiveFunctionScore: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: []
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      let _this = this.totalPoints
      console.log(_this,"_this")
      if (_this >= 25) {
        return `${_this}分`
      } else if (_this>=15&&_this<= 24) {
        return `${_this}分，有平衡功能障碍`
      }else if (_this<15) {
        return `${_this}分，有跌倒的危险`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '步态分析法')
    localStorage.setItem('wordLast', 'Tinetti步态和平衡量表')
  },

  mounted () {
    this.motorFn()
    this.cognitiveFn()
    this.loginEnd()
  },

  methods: {
    // 未登录状态下 做题 登录之后的逻辑
    loginEnd () {
      // 登录之后 从本地拿去数据 勾选选项
      if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
        this.allInput.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
            ].choice
        })
      }
      // 勾选完，循环数组，拿去分数和选中个数
      this.allInput.map((item) => {
        // 如果 处于选中状态 就处理以下逻辑
        if (item.checked === true) {
          if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
            this.choiceBox.push(item.id.slice(0, 2))
          }
          this.selected = this.choiceBox.length
          this.unselected = 19 - this.selected
          this.totalPoints += item.attributes.grade.value * 1
        }
      })
    },
    // 获取 运动功能 中的所有input框
    motorFn () {
      for (let i = 0; i < this.$refs.motorFunction.children.length; i++) {
        let index = this.$refs.motorFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[0].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
    },
    // 获取 认知功能 中的所有input框
    cognitiveFn () {
      for (let i = 0; i < this.$refs.cognitiveFunction.children.length; i++) {
        let index = this.$refs.cognitiveFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[1].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
      // 清空 allInput
      this.allInput = []
      // 合并两个数组
      this.allInput = [...this.allInput, ...this.choiceBoxs[0], ...this.choiceBoxs[1]]
    },
    // 总分
    result () {
      this.totalPoints = this.score.motorFunctionScore + this.score.cognitiveFunctionScore
    },
    checkedMotor (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 19 - this.selected
        this.score.motorFunctionScore += item.attributes.grade.value * 1
      }
    },
    checkedCognitive (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 19 - this.selected
        this.score.cognitiveFunctionScore += item.attributes.grade.value * 1
      }
    },
    // 选项的点击事件
    getTatals () {
      // 初始化数据
      this.selected = 0
      this.choiceBox = []
      this.unselected = 19
      this.totalPoints = 0
      this.optionMessage = []
      this.score.motorFunctionScore = 0
      this.score.cognitiveFunctionScore = 0
      // 运动功能
      this.choiceBoxs[0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedMotor(item)
      })
      // 认知功能
      this.choiceBoxs[1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedCognitive(item)
      })
      // 计算总分
      this.result()
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    .title {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      text-align: center;
      margin: 18px 0;
    }

    .subTitle {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 18px;
      color: #202020;
      margin: 20px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    .threeTopic {
      div {
        width: 877px;
        margin-top: 16px;
        padding: 3px 16px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(5, 129, 206, 0.03);

        p:nth-of-type(1) {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }

        p:nth-of-type(2) {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
