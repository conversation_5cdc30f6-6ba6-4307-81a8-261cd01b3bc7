<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails ></ScoringDetails>
        <!-- 评分内容详情 -->

        <div @click="getTatals($event)" class="matter" ref="reference">
<!--          <div  class="subTitleOne">一.危险因素</div>-->
          <li  class="apache">
            <label  for="apache">1.中性粒细胞计数(x109)
            <input v-model="apacheVal" @input="updateResult('neutrophils')" name="1.中性粒细胞计数(x109):" type="number" id="apache" placeholder="请输入中性粒细胞计数(x109)"></label>
          </li>

          <li  class="apache">
            <label  for="apache">2.淋巴细胞计数(x109)
              <input v-model="lymphocyteVal" @input="updateResult('lymphocyte')" name="2.淋巴细胞计数(x109):" type="number" id="lymphocyteVal" placeholder="请输入淋巴细胞计数(x109)"></label>
          </li>

          <li  class="apache">
            <label  for="apache">3.血清白蛋白水平(g/L)
              <input v-model="serumAlbuminVal" @input="updateResult('serumAlbumin')" name="3.血清白蛋白水平(g/L):" type="number" id="serumAlbuminVal" placeholder="请输入血清白蛋白水平(g/L)"></label>
          </li>


          <li  class="apache">
            <label  for="apache">4.血清球蛋白水平(g/L)
              <input v-model="serumGlobulin" @input="updateResult('serumGlobulin')" name="4.血清球蛋白水平(g/L):" type="number" id="serumGlobulin" placeholder="请输入血清球蛋白水平(g/L)"></label>
          </li>

          <li  class="apache">
            <label  for="apache">5.血清纤维蛋白原水平(g/L)
              <input v-model="serumFibrinogen" @input="updateResult('serumFibrinogen')" name="5.血清纤维蛋白原水平(g/L):" type="number" id="serumFibrinogen" placeholder="请输入血清纤维蛋白原水平(g/L)"></label>
          </li>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="5-filledInputCount !== 0">暂无</span>
            <span v-show="5-filledInputCount=== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="5-filledInputCount!== 0" class="explain">
            已选择<span>{{ filledInputCount }}</span>个评分项，尚有<span>{{ 5-filledInputCount }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: 5-filledInputCount === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import locale from "element-ui/lib/locale/lang/en";

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '预测胶质瘤分级及预后的评分系统(F-NLR-AGR得分) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '汉密尔顿焦虑量表（Hamilton Anxiety Scale，HAMA）是精神科临床中常用的量表之一，包括14个项目。《CCMD-3中国精神疾病诊断标准》将其列为焦虑症的重要诊断工具，临床上常将其用于焦虑症的诊断及程度划分的依据。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '预测胶质瘤分级及预后的评分系统(F-NLR-AGR得分), 汉密尔顿焦虑量表, hama'
        }
      ]
    }
  },
  data () {
    return {
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 4,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
      oneScore : 0,
      fourScore : 0,
      allThreeMonthScore:0,
      oneYearScore:0,
      age:0,
      apacheVal:"",
      lymphocyteVal:'',
      serumAlbuminVal:'',
      serumGlobulin:'',
      serumFibrinogen:'',
      ageValue: '',
      inputCount: 0
    }
  },

  computed: {
    filledInputCount: function() {
      let count = 0;
      if (this.apacheVal !== '') count++;
      if (this.lymphocyteVal !== '') count++;
      if (this.serumAlbuminVal !== '') count++;
      if (this.serumGlobulin !== '') count++;
      if (this.serumFibrinogen !== '') count++;
      return count;
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '预测胶质瘤分级及预后的评分系统(F-NLR-AGR得分)')
  },

  mounted () {
    // console.log('apache')
    // 初始化 choiceBox 数组
    this.choiceBox = [];

    // 获取到所有的 input 元素，并存入到 choiceBox 中
    if (this.$refs.reference && this.$refs.reference.children) {
      for (let i = 0; i < this.$refs.reference.children.length; i++) {
        const child = this.$refs.reference.children[i];
        if (child.children && child.children[0] && child.children[0].children[3]) {
          const optionsContainer = child.children[0].children[3];
          for (let j = 0; j < optionsContainer.children.length; j++) {
            const optionElement = optionsContainer.children[j].children[0];
            if (optionElement) {
              this.choiceBox.push(optionElement);
            }
          }
        }
      }
    }

    // 登录之后从本地获取数据并勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null) {
      const savedDetails = JSON.parse(localStorage.getItem('scoringDetails'));
      this.choiceBox.forEach((item, index) => {
        if (savedDetails[index] && savedDetails[index].choice) {
          item.checked = savedDetails[index].choice;
        }
      });
    }

    // 遍历 choiceBox 数组，更新选中状态和分数
    this.choiceBox.forEach(element => {
      this.checked(element);
    });

    // 1.中性粒细胞计数(x109)
    const apache=localStorage.getItem('apache')
    if(apache){
        document.getElementById('apache').value = apache;
        this.apacheVal=apache;
        localStorage.removeItem('apache');
        // console.log(this.apacheVal,"this.apacheVal")
    }
    // 2.淋巴细胞计数(x109)
    const lymphocyteVal=localStorage.getItem('lymphocyteVal')
    if(lymphocyteVal){
      document.getElementById('lymphocyteVal').value = lymphocyteVal;
      this.lymphocyteVal=lymphocyteVal;
      localStorage.removeItem('lymphocyteVal');
      // console.log(this.apacheVal,"this.apacheVal")
    }

    // 3.血清白蛋白水平(g/L)
    const serumAlbuminVal=localStorage.getItem('serumAlbuminVal')
    if(serumAlbuminVal){
      document.getElementById('serumAlbuminVal').value = serumAlbuminVal;
      this.serumAlbuminVal=serumAlbuminVal;
      localStorage.removeItem('serumAlbuminVal');
      // console.log(this.apacheVal,"this.apacheVal")
    }

    // 4.血清球蛋白水平(g/L)
    const serumGlobulin=localStorage.getItem('serumGlobulin')
    if(serumGlobulin){
      document.getElementById('serumGlobulin').value = serumGlobulin;
      this.serumGlobulin=serumGlobulin;
      localStorage.removeItem('serumGlobulin');
      // console.log(this.apacheVal,"this.apacheVal")
    }

    // 5.血清纤维蛋白原水平(g/L)
    const serumFibrinogen=localStorage.getItem('serumFibrinogen')
    if(serumFibrinogen){
      document.getElementById('serumFibrinogen').value = serumFibrinogen;
      this.serumFibrinogen=serumFibrinogen;
      localStorage.removeItem('serumFibrinogen');
      // console.log(this.apacheVal,"this.apacheVal")
    }
    // 更新结果展示
    this.result();
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
        // this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },

    updateResult(fieldName) {
      if (this[fieldName] !== '') {
        // this.inputCount++;
      }
      console.log(this.apacheVal,this.lymphocyteVal,this.serumAlbuminVal,this.serumGlobulin,this.serumFibrinogen,this.inputCount)
    },
    // 结果展示
    result () {
      this.totalPoints = "总得分:" +`${this.apacheVal}` + "分" + ";风险分级" +`${this.lymphocyteVal}`  + ";3年生存率" +`${this.serumAlbuminVal}`+ ";中位OS（月）:" +`${this.serumGlobulin}`
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      // this.explain = 0
      this.oneScore= 0
      this.fourScore=0
      this.allThreeMonthScore=0
      this.oneYearScore=0
      this.selected = 0
      this.unselected = 4
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })

      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)

      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        const ageElement = document.getElementById("apache").value;
        const lymphocyteValElement = document.getElementById("lymphocyteVal").value;
        const serumAlbuminValElement = document.getElementById("serumAlbuminVal").value;
        const serumGlobulinElement = document.getElementById("serumGlobulin").value;
        const serumFibrinogenElement = document.getElementById("serumFibrinogen").value;

        if(!ageElement&&!lymphocyteValElement&&!serumAlbuminValElement&&!serumGlobulinElement&&!serumFibrinogenElement){
          this.$store.commit('minitool/setShowMaskLayer', 'true')
          this.$store.commit('minitool/setGradeOrHind', '评分项')
          return;
        }else{
          // 使用对象来存储每个值，可以添加键名以便于区分不同的值
          const inputValues = {
            apacheElementV: ageElement,
            lymphocyteValElementV: lymphocyteValElement,
            serumAlbuminValElementV: serumAlbuminValElement,
            serumGlobulinElementV: serumGlobulinElement,
            serumFibrinogenElementV: serumFibrinogenElement
          };

          // 将对象转换为 JSON 字符串，并存储到 localStorage
          localStorage.setItem('inputValues', JSON.stringify(inputValues));
        }
        console.log(ageElement,"ageElement")

        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.inputCount,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        console.log(keepArguments,"keepArguments")
        // return

        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')

        const ageElement = document.getElementById("apache").value;
        const lymphocyteValElement = document.getElementById("lymphocyteVal").value;
        const serumAlbuminValElement = document.getElementById("serumAlbuminVal").value;
        const serumGlobulinElement = document.getElementById("serumGlobulin").value;
        const serumFibrinogenElement = document.getElementById("serumFibrinogen").value;

        if(ageElement||lymphocyteValElement||serumAlbuminValElement||serumGlobulinElement||serumFibrinogenElement){
          localStorage.setItem('apache',ageElement)
          localStorage.setItem('lymphocyteVal',lymphocyteValElement)
          localStorage.setItem('serumAlbuminVal',serumAlbuminValElement)
          localStorage.setItem('serumGlobulin',serumGlobulinElement)
          localStorage.setItem('serumFibrinogen',serumFibrinogenElement)
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0 -10px 0;
}

.subTitleOne{
  padding-top: 20px;
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0;
}

.apache label{
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 48px;
  color: #333333 !important;
  font-weight: 700;
}
.apache label input{
  width: 323px;
  height: 22px;
  font-size: 14px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 6px;
  border: none;
  color: #333333;
  margin-left: 10px;
  padding-left: 10px;
  font-weight: 500;
}

/* 隐藏输入框类型为number时的右侧箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 兼容Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

</style>
