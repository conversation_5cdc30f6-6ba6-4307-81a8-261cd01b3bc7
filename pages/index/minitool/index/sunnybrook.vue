<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->

        <div @click="getTatals($event)" class="matter" ref="reference">
          <div  class="subTitleOne">一.静态时与健侧比较</div>
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <div class="subTitle">二.与健侧比较随意运动的对称性</div>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <div class="subTitle">三.联动分级</div>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">说明：</span> <p> 静态分=总分*5,随意运动分=总分*4,联动分=总分,最后得分=随意运动分-静态分-联动分。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Sunnybrook面神经评定量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '汉密尔顿焦虑量表（Hamilton Anxiety Scale，HAMA）是精神科临床中常用的量表之一，包括14个项目。《CCMD-3中国精神疾病诊断标准》将其列为焦虑症的重要诊断工具，临床上常将其用于焦虑症的诊断及程度划分的依据。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Sunnybrook面神经评定量表, 汉密尔顿焦虑量表, hama'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '1. 眼（脸裂）',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '1) 正常;',
          '2) 缩窄;',
          '3) 增宽;',
          '4) 做过眼脸整形手术。'
        ],
        grade: ['0', '1', '1', '1'],
        simpleContent: ['正常;', '缩窄;', '增宽;', '做过眼脸整形手术;'],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title:
          '2. 颊（鼻唇沟）',
        id: ['2-1', '2-2', '2-3', '2-4'],
        content: [
          '1)正常;',
          '2)消失;',
          '3)不明显;',
          '4)过于明显。'
        ],
        grade: ['0', '2', '1', '1'],
        simpleContent: ['正常;', '消失;', '不明显;', '过于明显;'],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title:
          '3. 嘴',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '1)正常;',
          '2)口角下垂;',
          '3)口角上提。',
        ],
        grade: ['0', '1', '1'],
        simpleContent: ['正常;', '口角下垂;', '口角上提;'],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '4. 抬额头',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: [
          '1) 无运动（完全不对称）;',
          '2) 轻度运动;',
          '3) 有运动但有错乱的表情;',
          '4) 运动接近对称;',
          '5) 运动完全对称。',
        ],
        grade: ['1', '2', '3', '4', '5'],
        simpleContent: ['无运动（完全不对称）;', '轻度运动;', '有运动但有错乱的表情;', '有运动但有错乱的表情;', '运动完全对称;'],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '5. 轻轻闭眼 ',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: [
          '1) 无运动（完全不对称）;',
          '2) 轻度运动;',
          '3) 有运动但有错乱的表情;',
          '4) 运动接近对称;',
          '5) 运动完全对称。',
        ],
        grade: ['1', '2', '3', '4','5'],
        simpleContent: ['无运动（完全不对称）;', '轻度运动;', '有运动但有错乱的表情;', '运动接近对称', '运动完全对称;'],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '6. 张嘴微笑 ',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: [
          '1) 无运动（完全不对称）;',
          '2) 轻度运动;',
          '3) 有运动但有错乱的表情;',
          '4) 运动接近对称;',
          '5) 运动完全对称。',
        ],
        grade: [ '1', '2', '3', '4','5'],
        simpleContent: ['无运动（完全不对称）;', '轻度运动;', '有运动但有错乱的表情;', '运动接近对称;', '运动完全对称;'],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '7. 耸鼻 ',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '1) 无运动（完全不对称）;',
          '2) 轻度运动;',
          '3) 有运动但有错乱的表情;',
          '4) 运动接近对称;',
          '5) 运动完全对称。',
        ],
        grade: [ '1', '2', '3', '4', '5'],
        simpleContent: ['无运动（完全不对称）;', '轻度运动;', '有运动但有错乱的表情;', '运动接近对称;', '运动完全对称;'],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title:
          '8. 唇吸吮 ',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '1) 无运动（完全不对称）;',
          '2) 轻度运动;',
          '3) 有运动但有错乱的表情;',
          '4) 运动接近对称;',
          '5) 运动完全对称。',
        ],
        grade: [ '1', '2', '3', '4', '5'],
        simpleContent: ['无运动（完全不对称）;', '轻度运动;', '有运动但有错乱的表情;', '运动接近对称;', '运动完全对称;'],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title:
          '9. 抬额头 ',
        id: ['9-1', '9-2', '9-3', '9-4'],
        content: [
          '1) 没有联动;',
          '2) 轻度联动;',
          '3) 明显联动但无毁容;',
          '4) 严重的毁容性联动。'
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['没有联动;', '轻度联动;', '明显联动但无毁容;', '严重的毁容性联动;'],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '10.轻轻闭眼',
        id: ['10-1', '10-2', '10-3', '10-4'],
        content: [
          '1) 没有联动;',
          '2) 轻度联动;',
          '3) 明显联动但无毁容;',
          '4) 明显联动但无毁容。'
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['没有联动;', '轻度联动;', '明显联动但无毁容;', '明显联动但无毁容;'],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title:
          '11.张嘴微笑',
        id: ['11-1', '11-2', '11-3', '11-4'],
        content: [
          '1) 坚持5秒;',
          '2) 轻度联动;',
          '3) 明显联动但无毁容;',
          '4) 严重的毁容性联动。'
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['坚持5秒;', '轻度联动;', '明显联动但无毁容;', '严重的毁容性联动;'],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title:'12.耸鼻',
        id: ['12-1', '12-2', '12-3', '12-4'],
        content: [
          '1) 没有联动;',
          '2) 轻度联动;',
          '3) 明显联动但无毁容;',
          '4) 严重的毁容性联动。'
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['没有联动;', '轻度联动;', '明显联动但无毁容;', '严重的毁容性联动;'],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title:'13.唇吸吮',
        id: ['13-1', '13-2', '13-3', '13-4'],
        content: [
          '1) 没有联动;',
          '2) 轻度联动;',
          '3) 明显联动但无毁容;',
          '4) 严重的毁容性联动。'
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['没有联动;', '轻度联动;', '明显联动但无毁容;', '严重的毁容性联动;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 13,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
      explain1to3 : 0,
      explain4to8 : 0,
      explain9to13 : 0,
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', 'Sunnybrook面神经评定量表')
  },

  mounted () {
    // 初始化 choiceBox 数组
    this.choiceBox = [];

    // 获取到所有的 input 元素，并存入到 choiceBox 中
    if (this.$refs.reference && this.$refs.reference.children) {
      for (let i = 0; i < this.$refs.reference.children.length; i++) {
        const child = this.$refs.reference.children[i];
        if (child.children && child.children[0] && child.children[0].children[3]) {
          const optionsContainer = child.children[0].children[3];
          for (let j = 0; j < optionsContainer.children.length; j++) {
            const optionElement = optionsContainer.children[j].children[0];
            if (optionElement) {
              this.choiceBox.push(optionElement);
            }
          }
        }
      }
    }

    // 登录之后从本地获取数据并勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null) {
      const savedDetails = JSON.parse(localStorage.getItem('scoringDetails'));
      this.choiceBox.forEach((item, index) => {
        if (savedDetails[index] && savedDetails[index].choice) {
          item.checked = savedDetails[index].choice;
        }
      });
    }

    // 遍历 choiceBox 数组，更新选中状态和分数
    this.choiceBox.forEach(element => {
      this.checked(element);
    });

    // 更新结果展示
    this.result();
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
        this.explain += parseInt(element.attributes.grade.value);
        console.log(this.explain,element.attributes.grade.value,"element.attributes.grade.value",element.attributes.name.value)

        // 获取 grade 值并转换为整数
        const gradeValue = parseInt(element.attributes.grade.value);
        const nameValue = element.attributes.name.value;

        // 匹配序号（假设序号是位于名字字符串开头的数字）
        const match = nameValue.match(/^\d+/);
        if (match) {
          const sequenceNumber = parseInt(match[0]);

          // 根据序号累加到相应的 explain 变量
          if (sequenceNumber >= 1 && sequenceNumber <= 3) {
            this.explain1to3 += gradeValue;
          } else if (sequenceNumber >= 4 && sequenceNumber <= 8) {
            this.explain4to8 += gradeValue;
          } else if (sequenceNumber >= 9 && sequenceNumber <= 13) {
            this.explain9to13 += gradeValue;
          }
        }
        console.log(this.explain1to3,this.explain4to8,this.explain9to13);
      }
    },
    // 结果展示
    result () {
      this.totalPoints =this.explain4to8*4-this.explain1to3*5-this.explain9to13

      if(this.totalPoints<0){
        this.totalPoints=0+'分'
      }else{
        this.totalPoints=this.totalPoints+'分'
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.explain1to3= 0
      this.explain4to8=0
      this.explain9to13= 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 13
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })

      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },


    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0 -10px 0;
}

.subTitleOne{
  padding-top: 20px;
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0;
}
</style>
