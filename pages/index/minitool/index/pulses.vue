<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in pulses" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ resultExplain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'PULSES评分量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'PULSES评分量表是一种用于评估病人痛苦和不适程度的工具。PULSES是一个缩写，代表着6个评估指标：Pain、Urgency、Lethargy、Sleeplessness、Eating、Severity。这些指标被用来描述一个病人的疼痛、紧迫性、衰弱、失眠、进食和疼痛或不适的严重程度。它被广泛应用于疼痛管理和病人舒适护理的领域。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'PULSES评分, PULSES评定'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      pulses: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '1. 躯体状况（Physical condition,P），包括内脏疾病如心血管、呼吸、消化、泌尿、内分泌和神经系统疾患情况',
          id: ['1-1', '1-2', '1-3', '1-4'],
          content: [
            '1) 正常，与同年龄组健康者相比无明显异常：1分;',
            '2) 轻度异常，偶尔需要治疗和护理：2分;',
            '3) 中度异常，需要经常得到治疗和护理，可让患者活动：3分;',
            '4) 重度异常，需要长期得到医疗和护理，活动明显受损，只能卧床或坐轮椅：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，与同年龄组健康者相比无明显异常;', '轻度异常，偶尔需要治疗和护理;', '中度异常，需要经常得到治疗和护理，可让患者活动;', '重度异常，需要长期得到医疗和护理，活动明显受损，只能卧床或坐轮椅']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '2. 上肢功能（Upper limb function,U），包括颈部、肩胛带和上背部脊柱',
          id: ['2-1', '2-2', '2-3', '2-4'],
          content: [
            '1) 正常，与同年龄组健康者相比无明显异常：1分;',
            '2) 轻度异常，活动稍受限，功能良好：2分;',
            '3) 中度异常，在一定范围内可活动：3分;',
            '4) 重度异常，功能严重受限，需要长期护理：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，与同年龄组健康者相比无明显异常;', '轻度异常，活动稍受限，功能良好;', '中度异常，在一定范围内可活动;', '重度异常，功能严重受限，需要长期护理']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '3. 肢功能（Lower limb function,L），包括骨盆、下背部和腰骶部脊柱',
          id: ['3-1', '3-2', '3-3', '3-4'],
          content: [
            '1) 正常，与同年龄组健康者相比无明显异常：1分;',
            '2) 轻度异常，活动稍受限：2分;',
            '3) 中度异常，在一定范围内可活动：3分;',
            '4) 重度异常，功能严重受限，只能卧床或坐轮椅：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，与同年龄组健康者相比无明显异常;', '轻度异常，活动稍受限;', '中度异常，在一定范围内可活动;', '重度异常，功能严重受限，只能卧床或坐轮椅']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '4. 感觉功能（Sensory component,S），包括语言、听觉和视觉',
          id: ['4-1', '4-2', '4-3', '4-4'],
          content: [
            '1) 正常，与同年龄组健康者相比无明显异常：1分;',
            '2) 轻度异常，无明显功能障碍：2分;',
            '3) 中度异常，有明显功能障碍：3分;',
            '4) 重度异常，语言、听觉和视觉完全丧失：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，与同年龄组健康者相比无明显异常;', '轻度异常，无明显功能障碍;', '中度异常，有明显功能障碍', '重度异常，语言、听觉和视觉完全丧失']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 419px;',
          title: '5. 排泄功能（Excretory function,E），即大小便控制',
          id: ['5-1', '5-2', '5-3', '5-4'],
          content: [
            '1) 正常，能完全控制：1分;',
            '2) 轻度异常，偶尔发生大小便失禁或夜尿：2分;',
            '3) 中度异常，周期性的大小便失禁或潴留交替出现：3分;',
            '4) 重度异常，大小便完全失禁：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，能完全控制;', '轻度异常，偶尔发生大小便失禁或夜尿;', '中度异常，周期性的大小便失禁或潴留交替出现;', '重度异常，大小便完全失禁;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 419px;',
          title: '6. 社会心理状况（Mental and emotion status,S）',
          id: ['6-1', '6-2', '6-3', '6-4'],
          content: [
            '1) 正常，与同年龄组健康者相比无明显异常：1分;',
            '2) 轻度异常，表现在情绪、脾气和个性方面，但整个精神调节未受损害：2分;',
            '3) 中度异常，需要一定的监护：3分;',
            '4) 重度异常，需要完全监护：4分。'
          ],
          grade: ['1', '2', '3', '4'],
          simpleContent: ['正常，与同年龄组健康者相比无明显异常;', '轻度异常，表现在情绪、脾气和个性方面，但整个精神调节未受损害;', '中度异常，需要一定的监护;', '重度异常，需要完全监护;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 6,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return '总分积6分功能最佳；＞12分表示独立自理生活严重受限；＞16分表示有严重残疾；24分功能最差。'
    },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      return `${this.totalPoints}分`
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'PULSES评分量表')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 6
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
