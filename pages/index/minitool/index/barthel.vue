<template>
  <div>
    <form action="" method="post" target="targetIfr">
      <!-- 患者信息 -->
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="dress"></Options>
          <Options :information="embellish"></Options>
          <Options :information="eat"></Options>
          <Options :information="defcation"></Options>
          <Options :information="excrement"></Options>
          <Options :information="pee"></Options>
          <Options :information="shower"></Options>
          <Options :information="transfer"></Options>
          <Options :information="activity"></Options>
          <Options :information="stairs"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Barthel量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '巴氏量表（Barthel Index），是一个由医师团队来评估老年患者日常生活的体能，所做的日常生活功能之评估量表。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'the barthelindex of adl, ADL, Barthel'
        }
      ]
    }
  },
  data () {
    return {
      // 穿衣
      dress: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '1. 穿衣',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '1) 依赖：0分;',
          '2) 需一半帮助：5分;',
          '3) 自理（系、开纽扣、关、开拉锁和鞋带）：10分。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '依赖;',
          '需一半帮助;',
          '自理（系、开纽扣、关、开拉锁和鞋带）;',
        ],
      },
      // 修饰
      embellish: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 200px;',
        title: '2. 修饰',
        id: ['2-1', '2-2'],
        content: ['1) 需帮助：0分;', '2) 独立梳头、刷牙、洗脸、剃须：5分。'],
        grade: ['0', '5'],
        simpleContent: ['需帮助;', '独立梳头、刷牙、洗脸、剃须;'],
      },
      // 吃饭
      eat: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '3. 吃饭',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '1) 依赖别人：0分;',
          '2) 需部分帮助（夹菜、盛饭；切面包、抹黄油）：5分;',
          '3) 全面自理：10分。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '依赖别人;',
          '需部分帮助（夹菜、盛饭；切面包、抹黄油）;',
          '全面自理;',
        ],
      },
      // 如厕
      defcation: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '4. 如厕',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: ['1) 依赖别人：0分;', '2) 需部分帮助：5分;', '3) 自理：10分。'],
        grade: ['0', '5', '10'],
        simpleContent: ['依赖别人;', '需部分帮助;', '自理;'],
      },
      // 大便
      excrement: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '5. 大便',
        id: ['5-1', '5-2', '5-3'],
        content: [
          '1) 失禁或昏迷：0分;',
          '2) 偶尔失禁（每周<1次）：5分;',
          '3) 能控制：10分。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '失禁或昏迷;',
          '需部偶尔失禁（每周<1次）分帮助;',
          '能控制;',
        ],
      },
      // 小便
      pee: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '6. 小便',
        id: ['6-1', '6-2', '6-3'],
        content: [
          '1) 失禁或昏迷或需由他人导尿：0分;',
          '2) 偶尔失禁（每24小时<1次，每周>1次）：5分;',
          '3) 能控制：10分。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: [
          '失禁或昏迷或需由他人导尿;',
          '偶尔失禁（每24小时<1次，每周>1次）;',
          '自理;',
        ],
      },
      // 洗澡
      shower: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 200px;',
        title: '7. 洗澡',
        id: ['7-1', '7-2'],
        content: ['1) 依赖：0分;', '2) 自理：5分。'],
        grade: ['0', '5'],
        simpleContent: ['依赖;', '自理;'],
      },
      // 转移（床、椅）
      transfer: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '8. 转移（床、椅）',
        id: ['8-1', '8-2', '8-3', '8-4'],
        content: [
          '1) 完全依赖别人；不能坐：0分;',
          '2) 需大量帮助（2人）；能坐：5分;',
          '3) 需少量帮助（1人）；能坐：10分;',
          '4) 自理：15分。',
        ],
        grade: ['0', '5', '10', '15'],
        simpleContent: [
          '完全依赖别人；不能坐;',
          '需大量帮助（1人）；能坐;',
          '需少量帮助（1人）；能坐;',
          '自理;',
        ],
      },
      // 活动（步行）（在病房及其周围，不包括走远路；平地45米）
      activity: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '9. 活动（步行）（在病房及其周围，不包括走远路；平地45米）',
        id: ['9-1', '9-2', '9-3', '9-4'],
        content: [
          '1) 不能步行：0分;',
          '2) 在轮椅上独立行动，较大依赖：5分;',
          '3) 需1人帮助步行（体力或语言指导）：10分;',
          '4) 独立步行（可用辅助器）：15分。',
        ],
        grade: ['0', '5', '10', '15'],
        simpleContent: [
          '不能步行;',
          '在轮椅上独立行动，较大依赖;',
          '需1人帮助步行（体力或语言指导）;',
          '独立步行（可用辅助器）;',
        ],
      },
      // 上楼梯（上下一段楼梯，用手杖也算独立）
      stairs: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '10.上楼梯（上下一段楼梯，用手杖也算独立）',
        id: ['10-1', '10-2', '10-3'],
        content: [
          '1) 不能：0分;',
          '2) 需帮助（体力或语言指导）：5分;',
          '3) 自理：10分。',
        ],
        grade: ['0', '5', '10'],
        simpleContent: ['不能;', '需帮助（体力或语言指导）;', '自理;'],
      },
      // 选中的个数
      selected: 0,
      // 总分数
      totalPoints: 0,
      // 未选个数
      unselected: 10,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'Barthel量表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 10
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
