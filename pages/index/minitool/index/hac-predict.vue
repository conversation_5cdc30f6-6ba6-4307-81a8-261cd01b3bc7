<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in changeBarthel" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '高血压性脑出血1年再复发预测 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '高血压性脑出血1年再复发预测是一种评估病人日常生活自理能力的量表，它是在Barthel指数评定量表的基础上进行改良得来的。原始的Barthel指数评定量表存在一些问题，例如对于行动能力和认知能力的评估不够全面，对于辅助工具的使用也没有考虑等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Barthel日常生活活动评分, Barthel日常生活活动指数, Barthel指数, ADL评分, 患者的日常生活功能, 日常生活活动'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      changeBarthel: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '1. 年龄',
          id: ['1-1', '1-2'],
          content: [
            '1)>=60岁;',
            '2)<60岁。',
          ],
          grade: ['8', '4'],
          simpleContent: ['>=60岁;', '<60岁;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '2. 入院时血压的级别',
          id: ['2-1', '2-2'],
          content: [
            '1)三级高血压;',
            '2)三级高血压以下。',
          ],
          grade: ['6', '0'],
          simpleContent: ['三级高血压;', '三级高血压以下;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '3. 入院时GCS评分',
          id: ['3-1', '3-2'],
          content: [
            '1)9-12分;',
            '2)9分以下。'
          ],
          grade: ['1', '0'],
          simpleContent: ['9-12分;', '9分以下;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 219px;',
          title: '4. 出院时GCS评分',
          id: ['4-1', '4-2'],
          content: [
            '1)3-8分;',
            '2)3分以下。'
          ],
          grade: ['10', '0'],
          simpleContent: ['3-8分;', '3分以下;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '5. 缺血性卒中史',
          id: ['5-1', '5-2'],
          content: [
            '1)有',
            '2)无。',
          ],
          grade: ['5', '0'],
          simpleContent: ['有;', '无;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '6. 吸烟史',
          id: ['6-1', '6-2'],
          content: [
            '1)有',
            '2)无。',
          ],
          grade: ['5', '0'],
          simpleContent: ['有;', '无;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '7. 酗酒史',
          id: ['7-1', '7-2'],
          content: [
            '1)有',
            '2)无。',
          ],
          grade: ['5', '0'],
          simpleContent: ['有;', '无;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 219px;',
          title: '8. 血清同型半胱氨酸水平',
          id: ['8-1', '8-2'],
          content: [
            '1)有',
            '2)无。',
          ],
          grade: ['4', '0'],
          simpleContent: ['有;', '无;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 8,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      if (this.totalPoints>=30) {
        return `${this.totalPoints}分，高风险，1年复发风险为57.14%`
      } else if (this.totalPoints>10 && this.totalPoints <= 29) {
        return `${this.totalPoints}分，中等风险，1年复发风险为6.11%`
      } else if ( this.totalPoints>=0 && this.totalPoints <= 10) {
        return `${this.totalPoints}分，低风险，1年内复发风险为1.73%"`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '高血压性脑出血1年再复发预测')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 8
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
