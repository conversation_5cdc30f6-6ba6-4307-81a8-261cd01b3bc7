.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  // 动脉瘤体积计算
  .UIAvolume {
    width: 877px;
    box-sizing: border-box;
    //background-color: rgba(5, 129, 206, 0.03);
    border-radius: 6px;
    margin-top: 18px;
    padding: 16px 0 16px 0;
    p {
      font-weight: 700;
      font-size: 16px;
      color: #333333;
      width: 845px;
      height: 21px;
      line-height: 21px;
    }

    .describe {
      width: 100%;
      margin: 14px 0;
      box-sizing: border-box;

      ul {
        li {
          display: flex;
          position: relative;
          top: 0;
          left: 0;
          margin: 16px 0 16px 0;
          color: #676C74;
          font-size: 14px;
          // 小箭头
          .el-icon-caret-bottom {
            position: absolute;
            left: 12px;
            top: 5px;
            margin-top: 5px;
            margin-left: 140px;
            color: #676c74;
            font-size: 16px;
            transform: rotate(180deg);
          }

          p {
            width: 72px;
            height: 32px;
            line-height: 32px;
            font-weight: 400;
            font-size: 14px;
            color: #676C74;
          }

          // 下拉框
          select {
            //background-color: #f7fbfe;
            border: none;
            // 去掉下拉箭头
            appearance: none;
            outline: none;

            option {
              font-weight: 400;
              font-size: 16px;
              width: 128px;
              height: 32px;
              line-height: 32px;
              color: #333333;
            }
          }

          select::-ms-expand {
            display: none;
          }

          // 去除下拉框选中后的样式
          select:focus-visible {
            outline: none;
          }

          select {
            appearance: none;
            -moz-appearance: none; /* Firefox */
            -webkit-appearance: none; /* Safari 和 Chrome */
          }

          // 输入框
          label {
            display: flex;
            line-height: 32px;

            input {
              width: 166px;
              height: 32px;
              border: none;
              text-align: center;
              color: #333333;
              font-weight: 500;
              margin-left: 10px;
              background: #F4F4F4;
              border-radius: 4px;
              font-size: 14px;
            }

            input::-webkit-input-placeholder {
              color: #888888 !important;
            }
          }
        }

        .discolor {
          color: #50789C;
          height: 32px;
          line-height: 32px;
          font-size:16px
        }
      }
    }

    // 结果
    .result {
      display: flex;
      width: 400px;
      height: 21px;

      p {
        width: 96px;
        height: 21px;
        color: #404040;
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
      }

      span:nth-of-type(1) {
        width: 300px;
        font-size: 16px;
        color: #888888;
      }

      span:nth-of-type(2) {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        color: #333333;
      }
    }
  }

  // 弹簧圈体积计算
  .springCoil {
    width: 877px;
    // height: 393px;
    //background: #fbfbfb;
    border-radius: 6px;
    box-sizing: border-box;
    margin: 16px 0;
    padding: 16px 0 16px 0;

    p {
      font-weight: 700;
      font-size: 16px;
      color: #333333;
      width: 845px;
      height: 21px;
      line-height: 21px;
    }

    .content {
      width: 307px;
      // height: 256px;
      margin: 14px 0;
      box-sizing: border-box;

      ul {
        li {
          display: flex;
          margin-top: 16px;
          height: 32px;
          position: relative;

          .selectBefore {
            color: #888888;
            width: auto;
          }

          .selectAfter {
            color: #333333;
            font-weight: 500;
          }

          .el-icon-caret-bottom {
            position: absolute;
            top: 10px;
            left: 200px;
            //color: #0581ce;
            font-size: 14px;
            color: #676c74;
            transform: rotate(180deg);
          }

          p {
            width: 51px;
            height: 32px;
            line-height: 32px;
            font-weight: 400;
            font-size: 14px;
            color: #676C74;
          }

          span {
            line-height: 32px;
            color: #50789C;
            font-size: 16px;
          }

          // 下拉框
          select {
            //background: #fbfbfb;
            border: none;
            color: #888888;
            overflow: hidden;
            outline: none;

            option {
              font-weight: 400;
              font-size: 16px;
              width: 128px;
              height: 32px;
              line-height: 32px;
              color: #333333;
            }
          }

          .triangle {
            // 去掉下拉箭头
            appearance: none;
            width: 149px;
            font-size: 14px;
            text-align: center;
            background: none;
          }

          // 去除下拉框选中后的样式
          select:focus-visible {
            outline: none;
          }

          // 输入框
          label {
            display: flex;

            input {
              width: 176px;
              height: 32px;
              font-weight: 500;
              border: none;
              background: none;
              color: #333;
              font-size: 14px;
              //text-align: center;
            }

            input::-webkit-input-placeholder {
              color: #888888 !important;
            }
          }
        }
        .diameter {
          p {
            width: 121px;
          }
        }

        .discolor {
          color: #0581ce;
        }
      }
    }

    // 结果
    .result {
      display: flex;
      width: 400px;
      height: 21px;
      margin-bottom: 14px;

      p {
        width: 96px;
        height: 21px;
        color: #404040;
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
      }

      span:nth-of-type(1) {
        font-weight: 400;
        font-size: 16px;
        color: #888888;
      }

      span:nth-of-type(2) {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        color: #333333;
      }
    }
  }
  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;
    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }
    input {
      border: none;
    }
  }
  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }
}

.dot {
  position: relative;
  width: 5px;
  height: 5px;
  margin-right: 6px;
  background-color: #50789C;
  border-radius: 50%;
}

.dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background-color: #50789C;
  border-radius: 50%;
}

.el-icon-caret-bottom:before {
  content: "\e6e1" !important;
  //font-weight: bold;
}
.springCoilBgc{
  display: flex;
  width: 173px;
  border-radius: 4px;
  background: #F4F4F4;
}

/* 更改select元素的背景颜色 */
.caret-wrap {
  width: 108px;
  text-align: center;
  background: #F4F4F4;
}

.caret-wrap{
  background-image: none !important;
  filter: none !important;
  border: 1px solid #e5e5e5;
}
