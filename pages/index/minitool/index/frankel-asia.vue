<template>
  <div>
    <form action="">
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div class="content" ref="reference">
          <table>
            <tr>
              <th colspan="3">Frankel功能分级</th>
            </tr>
            <tr>
              <th>级别</th>
              <th>功能</th>
            </tr>
            <tr>
              <td>A</td>
              <td>完全瘫痪</td>
            </tr>
            <tr>
              <td>B</td>
              <td>感觉功能不完全丧失，无运动功能</td>
            </tr>
            <tr>
              <td>C</td>
              <td>感觉功能不完全丧失，有非功能性运动</td>
            </tr>
            <tr>
              <td>D</td>
              <td>感觉功能不完全丧失，有功能性运动</td>
            </tr>
            <tr>
              <td>E</td>
              <td>感觉、运动功能正常</td>
            </tr>
          </table>
          <div class="lineOne"></div>
          <div class="lineTwo"></div>
        </div>

        <div class="content" ref="reference">
          <table>
            <tr>
              <th colspan="3">ASIA分级</th>
            </tr>
            <tr>
              <th class="w-10">级别</th>
              <th>功能</th>
              <th>脊髓损伤类型</th>
            </tr>
            <tr>
              <td>A</td>
              <td>在骶段(S4~S5)无任何感觉和运动功能</td>
              <td>完全性损害</td>
            </tr>
            <tr>
              <td>B</td>
              <td>在神经损伤平面以下，包括骶段(S4~S5)存在感觉功能，但无运动功能</td>
              <td>不完全性损害</td>
            </tr>
            <tr>
              <td>C</td>
              <td>在神经损伤平面以下，存在运动功能，大部分关键肌的肌力小于3级</td>
              <td>不完全性损害</td>
            </tr>
            <tr>
              <td>D</td>
              <td>在神经损伤平面以下，存在运动功能，大部分关键肌的肌力大于或等于3级</td>
              <td>不完全性损害</td>
            </tr>
            <tr>
              <td>E</td>
              <td>感觉和运动功能正常</td>
              <td>正常</td>
            </tr>
          </table>
          <div class="lineThree"></div>
          <div class="lineFour"></div>
        </div>

        <div class="content" ref="reference">
          <table>
            <tr>
              <td class="w-10">结果解读</td>
              <td>可作为脊髓损伤的自然转归和治疗前后对照的观察指标。</td>
            </tr>
            <tr>
              <td>相关解释</td>
              <td>根据脊髓损伤的临床表现进行分级，目前较常用的是国际Frankel分级和美国脊髓损伤学会(ASIA)分级。</td>
            </tr>
            <tr>
              <td>参考来源</td>
              <td>陈孝平主编. 《外科学》(八年制)[M]. 人民卫生出版社.2010年</td>
            </tr>
          </table>
          <div class="lineFive"></div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
export default {
  // 注册组件
  components: { ScoringDetails },
  // tdk
  head () {
    return {
      title: '脊髓损伤严重程度Frankel功能分级和ASIA分级 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '刘建民教授团队根据现行指南及文献提出了LAST, CHANCE (最后机会)的评估方法，以帮助筛选适合行血管内治疗的大血管闭塞患者。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '急性缺血性卒中, 血管内治疗评估, LAST2CHANCE, LAST₂ CH₂ANCE'
        }
      ]
    }
  },
  data () {
    return {}
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '脊髓损伤严重程度Frankel功能分级和ASIA分级')
  },
}
</script>

<style lang="less" scoped>
.w-10{
  width: 10%;
}
.particulars {
  width: 980px;
  padding-left: 46px;
  margin-top: 25px;
  padding-bottom: 40px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .content {
    width: 877px;
    position: relative;
    border-radius: 6px 6px 0px 0px;
    margin-top: 18px;
    box-sizing: border-box;
    // border: 1px solid red;

    table {
      margin-top: 40px;
      width: 100%;
      overflow: hidden;
      border-radius: 6px 6px 6px 6px;

      tr {
        height: 50px;
        background: rgba(5, 129, 206, 0.05);
      }

      tr:nth-of-type(1) {
        font-weight: 700;
        font-size: 16px;
        color: #333333;
        background: rgba(5, 129, 206, 0.15);
      }

      tr:nth-of-type(even) {
        background: #fbfbfb;
      }

      td {
        text-align: center;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
      }
    }

    .lineOne {
      position: absolute;
      width: 1px;
      height: 86%;
      top: 14%;
      left: 80px;
      background: rgba(5, 129, 206, 0.3);
    }

    .lineTwo {
      position: absolute;
      //width: 1px;
      height: 100%;
      top: 0;
      left: 566px;
      background: rgba(5, 129, 206, 0.3);
    }

    .lineThree{
      position: absolute;
      width: 1px;
      height: 86%;
      top: 14%;
      left: 80px;
      background: rgba(5, 129, 206, 0.3);
    }
    .lineFour{
      position: absolute;
      width: 1px;
      height: 86%;
      top: 14%;
      left: 750px;
      background: rgba(5, 129, 206, 0.3);
    }

    .lineFive {
      position: absolute;
      width: 1px;
      height: 99%;
      top: 1%;
      left: 80px;
      background: rgba(5, 129, 206, 0.3);
    }

  }
}
</style>
