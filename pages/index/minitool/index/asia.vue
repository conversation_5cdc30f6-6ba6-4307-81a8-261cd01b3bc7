<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <SheetChange :information="LevelDescription"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue';
import SheetChange from '@/components/MiniTool/SheetChange.vue';
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue';


export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'ASIA损伤分级 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '目前肌力评估方法大致分两种：手法肌力检查、器械肌力检查手法。肌力检查目前有3种标准：Lovett分级、M.R.C.分级、Kendall百分比器械肌力检查下又分：等长肌力测试、等张肌力检查、等速肌力测定。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑动静脉畸形, avm患者, 畸形血管, 肌力评定量表, mrcs评分, roc分析, mrc分级, 重力检查, 肌肉收缩检查'
        }
      ]
    }
  },
  data () {
    return {
      // 体征/症状
      LevelDescription: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 700px;',
        title: '体征/症状',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '完全：骶段脊髓S4-S5无运动或感觉功能保留',
          '不完全：损伤平面以下仅留有感觉而无运动功能，包括骶段脊髓S4-S5',
          '不完全：损伤平面以下运动功能保留，且超过一半的主要肌群肌力小于3级',
          '不完全：损伤平面以下运动功能保留，且至少一半的主要肌群肌力为3级或以上',
          '正常：运动及感觉功能正常'
        ],
        grade: ['A', 'B', 'C', 'D', 'E'],
        show: 'false',
        describe: [],
        simpleContent: [
          '完全：骶段脊髓S4-S5无运动或感觉功能保留;',
          '不完全：损伤平面以下仅留有感觉而无运动功能，包括骶段脊髓S4-S5;',
          '不完全：损伤平面以下运动功能保留，且超过一半的主要肌群肌力小于3级;',
          '不完全：损伤平面以下运动功能保留，且至少一半的主要肌群肌力为3级或以上;',
          '正常：运动及感觉功能正常;']
      },
      // 总分数
      totalPoints: '',
      // 选中的个数
      selected: 0,
      // 未选中个数
      unselected: 1,
      choiceBox: [],
      show: 'false',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'ASIA损伤分级')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (let j = 0; j < this.$refs.reference.children[i].children[0].children[0].children[1].children.length; j++) {
        this.choiceBox.push(this.$refs.reference.children[i].children[0].children[0].children[1].children[j].children[0])
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          element.attributes.grade.value;
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = '';
      this.selected = 0;
      this.unselected = 1;
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach(element => {
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      });
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }

    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background: #0581CE;
      color: #FFFFFF;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
