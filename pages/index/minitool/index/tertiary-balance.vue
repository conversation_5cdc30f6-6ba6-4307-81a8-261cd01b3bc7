<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="classification"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '三级平衡检测法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '三级平衡检测法是一种用于评估个体平衡能力的常用方法。此方法通过一系列平衡测试来检查身体在静止和动态状态下的稳定性。首先，个体进行静止平衡测试，如单脚站立或闭眼站立。测试者会记录个体的平衡时间和姿势稳定性。接着，个体进行动态平衡测试，如行走或跑步。测试者会观察个体的步态、协调性和稳定性。最后，个体进行挑战性平衡测试，如斜坡行走或平衡板测试。这些测试会更加复杂和具有挑战性，以考察个体在非常态环境下的平衡控制能力。三级平衡检测法通过综合评估个体的静态和动态平衡能力，能够提供全面的平衡功能评估。这种方法不仅适用于康复治疗中对平衡障碍的评估，还可以用于体育训练和老年人健康管理中。通过三级平衡检测法，我们可以更好地了解和改善个体平衡能力，从而提高生活质量和避免潜在的跌倒风险。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Berg Balance Scale（伯格平衡量表）,Tinetti平衡量表,托尼约翰逊平衡量表,布鲁姆贝克平衡检测,功能平衡测评工具,平衡能力评估量表,走路平衡检测法,转身平衡评估表,三点触地法平衡检测,平衡功能评估测试（Berg平衡测试）'
        }
      ]
    }
  },
  data () {
    return {
      // 分级描述
      classification: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 平衡状态',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '一级平衡：静平衡。受试者可以在没有帮助的情况下保持所需的体位;',
          '二次平衡：自动动平衡。受试者在一定范围内主动移动身体重心后，可以保持所需的体位，并保持原来的体位;',
          '三级平衡：其他动平衡。受试者受到外力干扰，移动身体重心后，仍能恢复并保持原来的体位。',
        ],
        grade: ['一级平衡', '二次平衡','三级平衡'],
        show: 'false',
        simpleContent: [
          '一级平衡：静平衡。受试者可以在没有帮助的情况下保持所需的体位;',
          '二次平衡：自动动平衡。受试者在一定范围内主动移动身体重心后，可以保持所需的体位，并保持原来的体位;',
          '三级平衡：其他动平衡。受试者受到外力干扰，移动身体重心后，仍能恢复并保持原来的体位;'
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '三级平衡检测法')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
