<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
          <Options :information="fourteenTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span style="white-space: pre-wrap" v-show="unselected === 0">{{
              totalPoints
            }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>本表包括焦虑和抑郁2个亚量表，分别针对焦虑（A）和抑郁（D）问题各7题。</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '医院焦虑抑郁量表(HAD) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '英国精神医学家齐格蒙德(A. S. Zigmond)和斯奈思(R. P. Snaith)1989年编制的自评量表。主要应用于综合医院中受检者的焦虑和抑郁情绪的筛查。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '医院焦虑抑郁量表, had'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '1. 【A】我感到紧张（或痛苦）',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '1) 几乎所有时候：3分;',
          '2) 大多时候：2分;',
          '3) 有时候：1分;',
          '4) 根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['几乎所有时候;', '大多时候;', '有时候;', '根本没有;'],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '2. 【D】我对以往感兴趣的事情还是有兴趣',
        id: ['2-1', '2-2', '2-3', '2-4'],
        content: [
          '1) 肯定一样：0分;',
          '2) 不像以前那样多：1分;',
          '3) 只有一点：2分;',
          '4) 认为目前的疾病是对自己错误的惩罚，或有罪恶妄想：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: [
          '肯定一样;',
          '不像以前那样多;',
          '只有一点;',
          '基本上没有了;',
        ],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '3. 【A】我感到有些害怕，好像预感到有什么可怕的事情要发生',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '1) 非常肯定和十分严重：3分;',
          '2) 是的，但并不太严重：2分;',
          '3) 有一点，但并不使我苦恼：1分;',
          '4) 根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: [
          '非常肯定和十分严重;',
          '是的，但并不太严重;',
          '有一点，但并不使我苦恼;',
          '根本没有;',
        ],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '4. 【D】我能够哈哈大笑，并看到事物有趣的一面',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: [
          '1) 我经常这样：0分;',
          '2) 我现在已经不大这样了：1分;',
          '3) 现在肯定是不太多了：2分;',
          '4) 根本没有：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: [
          '我经常这样;',
          '我现在已经不大这样了;',
          '现在肯定是不太多了;',
          '根本没有;',
        ],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '5. 【A】我心中充满烦恼',
        id: ['5-1', '5-2', '5-3', '5-4'],
        content: [
          '1) 大多数时间：3分;',
          '2) 常常如此：2分;',
          '3) 时时，但并不经常：1分;',
          '4) 偶然如此：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: [
          '大多数时间;',
          '常常如此;',
          '时时，但并不经常;',
          '偶然如此;',
        ],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '6. 【D】我感到愉快',
        id: ['6-1', '6-2', '6-3', '6-4'],
        content: [
          '1) 根本没有：3分;',
          '2) 并不经常这样：2分;',
          '3) 有时：1分;',
          '4) 大多数时间：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['根本没有;', '并不经常这样;', '有时;', '大多数时间;'],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '7. 【A】我能够安闲而轻松地坐着',
        id: ['7-1', '7-2', '7-3', '7-4'],
        content: [
          '1) 肯定：0分;',
          '2) 经常：1分;',
          '3) 并不经常：2分;',
          '4) 根本没有：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['肯定;', '经常;', '并不经常;', '根本没有;'],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '8. 【D】我感到人好像变迟钝了',
        id: ['8-1', '8-2', '8-3', '8-4'],
        content: [
          '1) 几乎所有时间：3分;',
          '2) 很经常：2分;',
          '3) 有时：1分;',
          '4)根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['几乎所有时间;', '很经常;', '有时;', '根本没有;'],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '9. 【A】我感到一种令人发抖的恐惧',
        id: ['9-1', '9-2', '9-3', '9-4'],
        content: [
          '1) 根本没有：0分;',
          '2) 有时：1分;',
          '3) 很经常：2分;',
          '4) 非常经常：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['根本没有;', '有时;', '很经常;', '非常经常;'],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '10.【D】我对自己的外表（打扮自己）失去兴趣',
        id: ['10-1', '10-2', '10-3', '10-4'],
        content: [
          '1) 肯定：3分;',
          '2) 经常：2分;',
          '3) 并不经常：1分;',
          '4) 根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['肯定;', '经常;', '并不经常;', '根本没有;'],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '11.【A】我有点坐立不安，好像感到非要活动不可',
        id: ['11-1', '11-2', '11-3', '11-4'],
        content: [
          '1) 确实非常多：3分;',
          '2) 是不少：2分;',
          '3) 并不多：1分;',
          '4) 根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['确实非常多;', '是不少;', '并不多;', '根本没有;'],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '12.【D】我怀着愉快的心情憧憬未来',
        id: ['12-1', '12-2', '12-3', '12-4'],
        content: [
          '1) 差不多是这样做：0分;',
          '2) 并不完全是这样做的：1分;',
          '3) 很少这样做：2分;',
          '3) 几乎从来不这样做：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: [
          '差不多是这样做;',
          '并不完全是这样做的;',
          '很少这样做;',
          '几乎从来不这样做;',
        ],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title: '13.【A】我突然有恐惧感',
        id: ['13-1', '13-2', '13-3', '13-4'],
        content: [
          '1) 确实很经常：3分;',
          '2) 时常：2分;',
          '3) 并非经常：1分;',
          '4) 根本没有：0分。',
        ],
        grade: ['3', '2', '1', '0'],
        simpleContent: ['确实很经常;', '时常;', '并非经常;', '根本没有;'],
      },
      fourteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title: '14.【D】我能欣赏一本好书或一项好的广播或电视节目',
        id: ['14-1', '14-2', '14-3', '14-4'],
        content: [
          '1) 常常：0分;',
          '2) 有时：1分;',
          '3) 并非经常：2分;',
          '3) 很少：3分。',
        ],
        grade: ['0', '1', '2', '3'],
        simpleContent: ['常常;', '有时;', '并非经常;', '很少;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 14,
      // 总分数
      totalPoints: 0,
      totalPointsA: 0,
      totalPointsD: 0,
      explainA: 0,
      explainD: 0,
      choiceBox: [],
      choiceA: [],
      choiceD: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '医院焦虑抑郁量表(HAD)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }
    this.choiceBox.map((item) => {
      if (item.attributes.name.value.substring(4, 5) === 'A') {
        this.choiceA.push(item)
      } else {
        this.choiceD.push(item)
      }
    })

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceA.map((item) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checkedA(item)
    })
    this.choiceD.map((item) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checkedD(item)
    })

    // 结果展示
    this.result()
    this.totalPoints = `${this.totalPointsA}\n${this.totalPointsD}`
  },

  methods: {
    // 公共逻辑
    checkedA (item) {
      if (item.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explainA += item.attributes.grade.value * 1
      }
    },
    checkedD (item) {
      if (item.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explainD = this.explainD + item.attributes.grade.value * 1
      }
    },
    result () {
      if (this.explainA >= 0 && this.explainA <= 7) {
        this.totalPointsA = `焦虑（A）评分：${this.explainA}，无焦虑;`
      } else if (this.explainA >= 8 && this.explainA <= 10) {
        this.totalPointsA = `焦虑（A）评分：${this.explainA}，可能或“临界”焦虑;`
      } else if (this.explainA >= 11 && this.explainA <= 21) {
        this.totalPointsA = `焦虑（A）评分：${this.explainA}，可能有明显焦虑;`
      }
      if (this.explainD >= 0 && this.explainD <= 7) {
        this.totalPointsD = `抑郁（D）评分：${this.explainD}，无抑郁;`
      } else if (this.explainD >= 8 && this.explainD <= 10) {
        this.totalPointsD = `抑郁（D）评分：${this.explainD}，可能或“临界”抑郁;`
      } else if (this.explainD >= 11 && this.explainD <= 21) {
        this.totalPointsD = `抑郁（D）评分：${this.explainD}，可能有明显抑郁;`
      }
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explainA = 0
      this.explainD = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 14
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceA.map((item) => {
        // 存储选项的状态
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checkedA(item)
      })
      this.choiceD.map((item) => {
        // 存储选项的状态
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checkedD(item)
      })
      // 结果展示
      this.result()
      this.totalPoints = `${this.totalPointsA}\n${this.totalPointsD}`
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 450px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .explain {
    font-weight: 400;
    font-size: 16px;
    margin-top: 10px;
    color: #888888;

    span {
      color: #0581ce;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
