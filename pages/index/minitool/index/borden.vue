<template>
  <div @click.stop="exhibition">
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="type"></SheetChange>
        </div>
        <!-- 附加说明 -->
        <div>
          <div @click.stop="showOne" class="icon" style="left: 206px; top: 112px">
            i
          </div>
          <div v-show="showOnes === true" class="content" style="left: 223px; top: 15px">
            DAVM直接引流至静脉窦和硬脑膜静脉。病变由一处或多处动静脉瘘组成，介于硬膜动脉和静脉窦或硬膜静脉之间，临床症状轻微，大多无症状，部分病人可有颅内杂音或颅神经损害症状。预后良好，部分病人可自愈。
          </div>

          <div @click.stop="showTwo" class="icon" style="top: 158px; left: 290px">
            i
          </div>
          <div v-show="showTwos === true" class="content" style="left: 310px; top: 66px">
            DAVM静脉既向静脉窦回流，也反向回流至软脑膜静脉。通常病灶为高流量的单个或多个动静脉瘘。临床上可因静脉高压和颅内出血引起颅内压增高、神经功能障碍、癫痫、耳鸣、颅内杂音。
          </div>

          <div @click.stop="showThree" class="icon" style="top: 204px; left: 164px">
            i
          </div>
          <div v-show="showThrees === true" class="content" style="left: 182px; top: 205px">
            DAVM只向静脉窦附近的软脑膜静脉反向回流，引流静脉动脉化，迂曲扩张、病变由大静脉窦壁上的动静脉瘘组成。临床上可因皮质静脉、深静脉内压力增高致使血管破裂，引起颅内出血或颅内高压，症状常进行性恶化。
            亚型A：单纯动静脉瘘，静脉回流至静脉窦或硬膜静脉（IA型），兼有软脑膜回流（IIA型）。或只向软脑膜静脉回流（IIIA型）。
            亚型B：多发动静脉瘘，具有多处动脉供应的DAVM，静脉回流至静脉窦或硬膜静脉（IB型），兼有软脑膜回流（IIB型），或只向软脑膜静脉回流（IIIB型）。
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Borden分型 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Borden 分类型系统根据血流方向和皮质静脉引流情况将病变描述为以下类型：I型:顺行流入硬膜静脉窦或脑膜静脉。通常有良性的自然病史。Il型:顺行流入硬膜静脉窦。然而，他们也有逆行性皮质静脉回流。39%的患者被认为是具有侵袭性行为的高级别病变。IlI型: 从痿管直接逆行流入皮质静脉，从而引起静脉高压。79%的人有侵袭性行为。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Borden, dAVF的破裂风险, 引流静脉, 硬脑膜动静脉瘘, 颅内动静脉畸形, 自发性出血, Cognard, 良性静脉引流, 侵袭性静脉造影, 硬脑膜动静脉畸形, 脊髓静脉引流'
        }
      ]
    }
  },
  data () {
    return {
      // 类型描述
      type: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '1. 类型描述',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '只有静脉窦和硬膜静脉回流',
          '兼有静脉窦、硬膜静脉和软脑膜静脉回流',
          '只有软脑膜静脉回流',
        ],
        grade: ['Ⅰ型', 'Ⅱ型', 'Ⅲ型'],
        show: 'false',
        simpleContent: [
          '只有静脉窦和硬膜静脉回流;',
          '兼有静脉窦、硬膜静脉和软脑膜静脉回流;',
          '只有软脑膜静脉回流;',
        ],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 1,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
      showOnes: false,
      showTwos: false,
      showThrees: false,
    }
  },

  beforeMount () {
    localStorage.setItem('className', '脑血管畸形')
    localStorage.setItem('wordLast', 'Borden分型')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[0].children[1]
            .children[j].children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = element.attributes.grade.value
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.unselected = 1
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    showOne () {
      this.showOnes = true
      this.showTwos = false
      this.showThrees = false
    },
    showTwo () {
      this.showTwos = true
      this.showThrees = false
      this.showOnes = false
    },
    showThree () {
      this.showThrees = true
      this.showOnes = false
      this.showTwos = false
    },
    exhibition () {
      this.showOnes = false
      this.showTwos = false
      this.showThrees = false
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';
  position: relative;

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 104px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
