<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="awareness"></Options>
          <Options :information="directiveForce"></Options>
          <Options :information="eyeMovement"></Options>
          <Options :information="language"></Options>
          <Options :information="facioplegia"></Options>
          <Options :information="upperLimb"></Options>
          <Options :information="hand"></Options>
          <Options :information="lowerLimbs"></Options>
          <Options :information="walk"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import { keepScore } from '@/api/minitool'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '斯堪的那维亚卒中量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '为了进行脑梗塞病人的血液稀释治疗效果的研究，SSS评分分为预后评分和长期随访评分。最初预后评定项目包括意识水平，眼活动和瘫痪的严重性；随访评分项目包括上下肢和手的肌力，定向力，语言，面瘫和步态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '面瘫, scandinavian stroke scale, 上肢肌力, 手的肌力, 眼球运动, 意识, SSS, SNSS'
        }
      ]
    }
  },
  data () {
    return {
      // 意识
      awareness: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '1. 意识',
        id: ['1-1', '1-2', '1-3', '1-4'],
        content: [
          '1) 完全清醒：6分;',
          '2) 嗜睡（唤醒后意识完全清醒）：4分;',
          '3) 昏睡（对语言刺激有反应，但不完全清醒）：2分;',
          '4) 昏迷：0分。',
        ],
        grade: ['6', '4', '2', '0'],
        simpleContent: [
          '完全清醒;',
          '嗜睡（唤醒后意识完全清醒）;',
          '昏睡（对语言刺激有反应，但不完全清醒）;',
          '昏迷;',
        ],
      },
      // 定向力（时间、地点、人物）
      directiveForce: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '2. 定向力（时间、地点、人物）',
        id: ['2-1', '2-2', '2-3', '2-4'],
        content: [
          '1) 三项均正常：6分;',
          '2) 两项正常：4分;',
          '3) 一项正常：2分;',
          '4) 所有定向力丧失：0分。',
        ],
        grade: ['6', '4', '2', '0'],
        simpleContent: [
          '三项均正常;',
          '两项正常;',
          '一项正常;',
          '所有定向力丧失;',
        ],
      },
      // 眼球运动
      eyeMovement: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 300px;',
        title: '3. 眼球运动',
        id: ['3-1', '3-2', '3-3'],
        content: [
          '1) 无凝视麻痹：4分;',
          '2) 有凝视麻痹：2分;',
          '3) 眼球分离：0分。',
        ],
        grade: ['4', '2', '0'],
        simpleContent: ['无凝视麻痹;', '有凝视麻痹;', '眼球分离;'],
      },
      // 语言
      language: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 400px;',
        title: '4. 语言',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: [
          '1) 无失语：10分;',
          '2) 词汇减少，语言不连贯：6分;',
          '3) 语句短缩，不能说长句：3分;',
          '4) 仅能说是或不，或不能言语：0分。',
        ],
        grade: ['10', '6', '3', '0'],
        simpleContent: [
          '无失语：;',
          '词汇减少，语言不连贯;',
          '语句短缩，不能说长句;',
          '仅能说是或不，或不能言语;',
        ],
      },
      // 面瘫
      facioplegia: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 200px;',
        title: '5. 面瘫',
        id: ['5-1', '5-2'],
        content: ['1) 无面瘫或不肯定：2分;', '2) 有面瘫：0分。'],
        grade: ['2', '0'],
        simpleContent: ['无面瘫或不肯定;', '有面瘫;'],
      },
      // 上肢肌力（瘫痪侧）
      upperLimb: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 500px;',
        title: '6. 上肢肌力（瘫痪侧）',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: [
          '1) 抬臂肌力正：6分;',
          '2) 抬臂肌力减弱：5分;',
          '3) 抬臂时肘部屈曲：4分;',
          '4) 能运动，但不能对抗重力：2分;',
          '5) 完全瘫痪：0分。',
        ],
        grade: ['6', '5', '4', '2', '0'],
        simpleContent: [
          '抬臂肌力正;',
          '抬臂肌力减弱;',
          '抬臂时肘部屈曲;',
          '能运动，但不能对抗重力;',
          '完全瘫痪;',
        ],
      },
      // 手的肌力（瘫痪侧）
      hand: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '7. 手的肌力（瘫痪侧）',
        id: ['7-1', '7-2', '7-3', '7-4'],
        content: [
          '1) 正常：6分;',
          '2) 减弱：4分;',
          '3) 指尖不能触到手掌：2分;',
          '4) 完全瘫痪：0分。',
        ],
        grade: ['6', '4', '2', '0'],
        simpleContent: ['正常;', '减弱;', '指尖不能触到手掌;', '完全瘫痪;'],
      },
      // 下肢肌力（瘫痪侧）
      lowerLimbs: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 500px;',
        title: '8. 下肢肌力（瘫痪侧）',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '1) 正常：6分;',
          '2) 伸膝抬腿时肌力减弱：5分;',
          '3) 抬腿时膝部屈曲：4分;',
          '4) 能运动，但不能对抗重力：2分;',
          '5) 完全瘫痪：0分。',
        ],
        grade: ['6', '5', '4', '2', '0'],
        simpleContent: [
          '正常;',
          '伸膝抬腿时肌力减弱;',
          '抬腿时膝部屈曲;',
          '能运动，但不能对抗重力;',
          '完全瘫痪;',
        ],
      },
      // 步行能力
      walk: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title: '9. 步行能力',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '1) 独立行走5米以上：12分;',
          '2) 独立行走，需扶杖：9分;',
          '3) 有人扶持下可以行走：6分;',
          '4) 独自坐立，不需支持：3分;',
          '5) 卧床或坐轮椅：0分。',
        ],
        grade: ['12', '9', '6', '3', '0'],
        simpleContent: [
          '独立行走5米以上：;',
          '独立行走，需扶杖;',
          '有人扶持下可以行走;',
          '独自坐立，不需支持;',
          '卧床或坐轮椅;',
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选个数
      unselected: 9,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', '斯堪的那维亚卒中量表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 9
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
