<template>
  <div>
    <form action="" method="post" target="targetIfr">
      <!-- 患者信息 -->
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="dress"></Options>
          <Options :information="embellish"></Options>
          <Options :information="eat"></Options>
          <Options :information="defcation"></Options>
          <Options :information="excrement"></Options>
          <Options :information="pee"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'crs-r评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '意识恢复标准修订版（Coma Recovery Scale-Revised，简称CRS-R）是一种用于评估意识障碍患者的评分量表。CRS-R结合了行为观察和问卷调查的方式，通过评估患者的视觉、听觉、言语、动作和意识特征等方面的表现，对患者的意识恢复程度进行评估。评估过程中，医护人员会观察患者的反应，包括触觉、视觉和听觉刺激的反应，以及身体动作的自主性。同时，还会通过与患者进行交流，观察他们的言语、回应和意识表现。CRS-R评分可以帮助医护人员判断患者的意识状态，包括昏迷、浅昏迷、意识恢复等级等。这一评估工具对于患者的康复和治疗规划至关重要，可以帮助医疗团队制定个性化的康复计划，提供更准确的治疗指导，并预测患者的康复潜力和康复过程。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '响应级别评分量表复查意识恢复水平修订版,CRS-R评分,康复周期评估评分修订版,康复状态评估修订版,意识恢复评估修订版,修订版康复响应评分量表,版本修订的意识恢复评估量表,修订版康复状态评估量表'
        }
      ]
    }
  },
  data () {
    return {
      // 听觉
      dress: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 550px;',
        title: '1. 听觉',
        id: ['1-1', '1-2', '1-3','1-4', '1-5'],
        content: [
          '1) 对指令有稳定的反应：4分;',
          '2) 可重复执行指令：3分;',
          '3) 声源定位：2分;',
          '4) 对声音有眨眼反应（惊吓反应）：1分;',
          '5) 无：0分。',
        ],
        grade: ['4', '3', '2', '1', '0'],
        simpleContent: [
          '对指令有稳定的反应;',
          '可重复执行指令;',
          '声源定位;',
          '对声音有眨眼反应（惊吓反应）;',
          '无;',
        ],
      },
      // 视觉
      embellish: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 660px;',
        title: '2. 视觉',
        id: ['2-1', '2-2','2-3', '2-4','2-5','2-6'],
        content: [
          '1) 识别物体：5分;',
          '2) 物体定位，够向物体：4分;',
          '3) 眼球追踪性移动：3分;',
          '4) 视觉对象定位（>2秒）：2分;',
          '5) 对威胁有眨眼反应（惊吓反应）：1分;',
          '6) 无：0分。',
        ],
        grade: [ '5','4','3','2','1','0'],
        simpleContent: ['识别物体;', '物体定位，够向物体;', '视觉对象定位（>2秒）;', '视觉对象定位（>2秒）;', '对威胁有眨眼反应（惊吓反应）;','无'],
      },
      // 运动
      eat: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 770px;',
        title: '3. 运动',
        id: ['3-1', '3-2', '3-3','3-4', '3-5', '3-6', '3-7'],
        content: [
          '1) 会使用对象：6分;',
          '2) 自主性运动反应：5分;',
          '3) 能摆弄物体：4分;',
          '4) 对伤害性刺激定位：3分;',
          '5) 回撤屈曲：2分;',
          '6) 异常姿势（屈曲/伸展）：1分',
          '7) 无：0分。',
        ],
        grade: ['6','5','4', '3', '2', '1','0'],
        simpleContent: [
          '会使用对象;',
          '自主性运动反应;',
          '能摆弄物体;',
          '对伤害性刺激定位;',
          '回撤屈曲;',
          '异常姿势（屈曲/伸展）;',
          '无;',
        ],
      },
      // 言语反应
      defcation: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 440px;',
        title: '4. 言语反应',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: ['1) 表达可理解：3分;', '2) 发声/发生动作：2分;', '3) 反射性发声动作：1分;', '4) 无：0分'],
        grade: ['3', '2', '1','0'],
        simpleContent: ['表达可理解;', '发声/发生动作;', '反射性发声动作;', '无;'],
      },
      // 交流
      excrement: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 330px;',
        title: '5. 交流',
        id: ['5-1', '5-2', '5-3'],
        content: [
          '1) 功能性（准确的）：2分;',
          '2) 非功能性（意向性的）：1分;',
          '3) 无：0分。',
        ],
        grade: ['2', '1', '0'],
        simpleContent: [
          '功能性（准确的）;',
          '非功能性（意向性的）;',
          '无;',
        ],
      },
      // 唤醒度
      pee: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 440px;',
        title: '6. 唤醒度',
        id: ['6-1', '6-2', '6-3', '6-4'],
        content: [
          '1) 能注意：3分;',
          '2) 睁眼：2分;',
          '3) 刺激下睁眼：1分;',
          '3) 无：0分。',
        ],
        grade: ['3', '2', '1','0'],
        simpleContent: [
          '能注意;',
          '睁眼;',
          '刺激下睁眼;',
          '无;',
        ],
      },
      // 选中的个数
      selected: 0,
      // 总分数
      totalPoints: 0,
      // 未选个数
      unselected: 6,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', 'crs-r评分')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 6
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
