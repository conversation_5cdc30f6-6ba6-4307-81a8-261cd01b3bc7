<template>
  <div>
    <Patient></Patient>
    <div class="particulars">
      <!-- 评分详情 -->
      <ScoringDetails></ScoringDetails>
      <!-- 评分内容 -->
      <div @click="getTargetInput" ref="reference">
        <Options
          v-for="data in raceData"
          :key="data.title"
          :information="data"
        ></Options>
      </div>
      <!-- 结果展示 -->
      <div class="result">
        <div class="grade">
          结果：
          <span v-if="selectedSet.size === 0">暂无</span>
          <span v-else>{{ totalPoints }}</span>
        </div>
        <div v-show="unselected !== 0" class="explain">
          已选择
          <span>{{ selectedSet.size }}</span>
          个评分项，尚有
          <span>{{ unselected }}</span>
          个评分项未选择
        </div>
      </div>
      <!-- 保存评分 -->
      <div class="save">
        <div
          :class="{ scoreBtn: unselected === 0 }"
          @click="saveScore"
          class="btn"
        >
          保存评分
        </div>
      </div>
    </div>
    <PromptPopup
      :type="popupType"
      :show="popupShow"
      @closePopup="closePopup"
      @directSave="directSave"
    ></PromptPopup>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { saveToolScore } from '@/api/minitool'
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import PromptPopup from '@/components/MiniTool/PromptPopup.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options, PromptPopup },
  // 配置TDK
  head() {
    return {
      title: '动脉闭塞快速评价量表（RACE） - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            '为了进行脑梗塞病人的血液稀释治疗效果的研究，SSS评分分为预后评分和长期随访评分。最初预后评定项目包括意识水平，眼活动和瘫痪的严重性；随访评分项目包括上下肢和手的肌力，定向力，语言，面瘫和步态。',
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '面瘫, scandinavian stroke scale, 上肢肌力, 手的肌力, 眼球运动, 意识, SSS, SNSS',
        },
      ],
    }
  },
  computed: {
    ...mapState('minitool', ['PatientInformations']),
    raceData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '1.面瘫',
          id: ['1-1', '1-2', '1-3'],
          content: ['1) 无:0分;', '2) 轻度:1分;', '3) 中重度:2分;'],
          grade: ['0', '1', '2'],
          simpleContent: ['无;', '轻度;', '中重度;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '2.上肢运动功能',
          id: ['2-1', '2-2', '2-3'],
          content: [
            '1) 正常或轻度受损:0分;',
            '2) 中度受损:1分;',
            '3) 重度受损:2分;',
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['正常或轻度受损;', '中度受损;', '重度受损;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '3.下肢运动功能',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 正常或轻度受损:0分;',
            '2) 中度受损:1分;',
            '3) 重度受损:2分;',
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['正常或轻度受损;', '中度受损;', '重度受损;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '4.凝视',
          id: ['4-1', '4-2'],
          content: ['1) 无:0分;', '2) 有:1分;'],
          grade: ['0', '1'],
          simpleContent: ['无;', '有;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          name: 'inputName-5',
          title: '5-1.失语(右侧偏瘫时)',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 两项指令均正确执行(闭眼、握拳):0分;',
            '2) 需帮助进出厕所，便后清洁或整理衣裤:1分;',
            '3) 两项执行均不正确:2分;',
          ],
          grade: ['0', '1', '2'],
          simpleContent: [
            '两项指令均正确执行(闭眼、握拳);',
            '需帮助进出厕所，便后清洁或整理衣裤;',
            '两项执行均不正确;',
          ],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5-2.失认(左侧偏瘫时)',
          name: 'inputName-5',
          id: ['6-1', '6-2', '6-3'],
          content: [
            '1) 可识别自己的胳膊和功能受损:0分;',
            '2) 只能识别二者其中之一:1分',
            '3) 二者均不能识别:2分',
          ],
          grade: ['0', '1', '2'],
          simpleContent: [
            '可识别自己的胳膊和功能受损;',
            '只能识别二者其中之一;',
            '二者均不能识别;',
          ],
        },
      ]
    },
  },
  data() {
    return {
      selectedSet: new Set(),
      selectDetails: {},
      totalPoints: 0,
      unselected: 5,
      resultString: '',
      popupType: '0',
      popupShow: false,
      selectOptions: {},
    }
  },

  beforeMount() {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '动脉闭塞快速评价量表（RACE）')
  },

  mounted() {
    const userSelected = JSON.parse(localStorage.getItem('userSelected'))

    if (userSelected) {
      Object.entries(userSelected).forEach(([key, { id }]) => {
        const selectedInput = document.getElementById(id)
        if (selectedInput) {
          selectedInput.checked = true
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
          })
          selectedInput.dispatchEvent(clickEvent)
        }
      })
      localStorage.removeItem('userSelected')
    }
  },

  methods: {
    ...mapMutations('minitool', ['setPatientInformations']),
    getTargetInput(event) {
      if (event.target.nodeName !== 'INPUT') return

      const { target } = event
      const { attributes } = target
      const realId = target.id.split('-')[0]
      const title = target.name.includes('inputName')
        ? this.raceData[realId - 1].title
        : target.name
      const id = realId === '6' ? '5' : realId
      const grade = attributes.grade.value
      const option = attributes.title.value
      this.selectOptions[id] = { id: target.id, type: 'radio', value: true }
      this.selectedSet.add(id)
      console.log(2222, this.selectedSet)

      this.selectDetails[id] = { title, option, grade }
      this.unselected = 5 - this.selectedSet.size
      if (this.unselected === 0) {
        this.calculateScore()
      }
    },
    calculateScore() {
      this.totalPoints = 0
      this.resultString = ''
      let allScore = 0
      for (const key in this.selectDetails) {
        const { title, option, grade } = this.selectDetails[key]
        allScore += Number(grade)
        this.totalPoints = allScore
        this.resultString += `{${title}:${option},分数${grade}}`
      }
    },
    // 保存评分
    saveScore(event) {
      if (!event.target.classList.contains('scoreBtn')) return
      const validationResult = this.validateInputs()
      if (validationResult) {
        this.popupType = validationResult
        this.popupShow = true
      }
      const {
        age,
        gender,
        diagnose: diagnosis,
        name,
      } = this.PatientInformations
      const params = {
        age: age !== '未知' ? age : '',
        gender: gender !== '未知' ? gender : '',
        diagnosis,
        name,
        scoreResult: this.totalPoints,
        scoreTitle: '动脉闭塞快速评价量表（RACE）',
        scoreDetail: this.resultString,
        scoreType: '',
        userId: this.$cookies.get('medtion_user_only_sign')?.id,
      }
      this.$axios.$request(saveToolScore(params)).then((res) => {
        if (res.code === 1) {
          this.$router.push('/minitool/page_save')
        }
      })
    },

    closePopup() {
      this.popupShow = false
    },

    directSave() {
      this.popupShow = false
      const { age, gender, diagnose, name } = this.PatientInformations
      this.setPatientInformations({
        age: age || '未知',
        name: name || '未知',
        gender: gender || '未知',
        diagnose: diagnose || '未知',
      })
    },

    // 统一验证用户信息
    validateInputs() {
      if (!this.checkPatientInformation()) return '0' // 信息不完整
      if (!this.checkAge()) return '1' // 年龄不正确
      if (!this.checkUnselected()) return '2' // 有未完成的评分项
      if (!this.checkLogin()) {
        this.savePatientInformation()
        return '3'
      } // 未登录
      return null // 所有检查通过
    },

    // 将用户信息暂时保存本地
    savePatientInformation() {
      localStorage.setItem('userSelected', JSON.stringify(this.selectOptions))
    },

    // 判断用户信息是否填写完整
    checkPatientInformation() {
      return Object.values(this.PatientInformations).every(
        (value) => value !== ''
      )
    },

    // 判断用户年龄是否正确
    checkAge() {
      const age = this.PatientInformations.age
      if (age === '未知') {
        return true
      } else {
        return !isNaN(Number(age)) && Number(age) > 0
      }
    },

    // 判断是否还有未完成的评分项
    checkUnselected() {
      return this.unselected === 0
    },

    // 判断是否登录
    checkLogin() {
      return this.$cookies.get('medtion_isLogged_only_sign') !== undefined
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
