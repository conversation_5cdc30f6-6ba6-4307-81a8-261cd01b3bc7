<template>
  <div class="bigBox" @click="spinner">
    <div class="search">
      <input type="text" v-model="name" placeholder="请输入患者姓名" />
      <div @click.stop="optionsShow" class="slect">
        <i class="el-icon-caret-bottom"></i>
        <div :class="{ checkeds: changeClass }" class="checkbox">
          {{ taxon }}
        </div>
        <div @click.stop="getInnerhtml($event)" v-show="options" class="options">
          <ul>
            <li :class="{ checked: taxon === '全部' }">全部</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '通用工具' }">通用工具</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '缺血' }">缺血</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '卒中再发风险评分' }">
              卒中再发风险评分
            </li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '动脉瘤' }">动脉瘤</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '脑血管畸形' }">脑血管畸形</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '步态分析法' }">步态分析法</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '帕金森' }">帕金森</li>
            <div class="line"></div>
            <li :class="{ checked: taxon === '其他' }" class="other">其他</li>
          </ul>
        </div>
      </div>
      <button @click="search">搜索</button>
      <button @click="reset">重置</button>
    </div>
    <!-- 记录内容 -->
    <div class="historylist">
      <HaveRecord v-show="content === 'true'"></HaveRecord>
      <NoRecord v-show="content === 'false'"></NoRecord>
    </div>
  </div>
</template>

<script>
import HaveRecord from '@/components/MiniTool/HaveRecord.vue'
import NoRecord from '@/components/MiniTool/NoRecord.vue'
import { mapState } from 'vuex'
export default {
  // 注册组件
  components: { HaveRecord, NoRecord },
  data () {
    return {
      name: '',
      taxon: '全部',
      // 下拉框显示与隐藏
      options: false,
      checkeds: false,
      title: '',
      // alter: false,
      changeClass: false,
      // 根据有没有评分记录来显示那个组件
      content: '',
    }
  },
  watch: {
    // 监听 患者姓名的变化
    name (n) {
      this.$store.commit('minitool/setName', n)
      localStorage.setItem('name', n)
      this.alter = true
    },
    // 监听 分类的变化
    taxon (n) {
      localStorage.setItem('taxon', n)
      this.alter = true
      this.changeClass = true
      // 根据 类名 查找评分
      if (n === '全部') {
        this.title = ''
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '通用工具') {
        this.title =
          'GCS评分,mRS评分,Barthel量表,GOS评分,肌力评分,洼田饮水试验评分,改良Barthel指数评定量表,功能独立性评定(FIM),Katz日常生活功能指数评价,PULSES评分量表,功能活动问卷(FAQ),Richmond躁动-镇静评分(RASS),oa颈髓损伤评分,Brunnstrom偏瘫功能评价法,徒手肌力评定法,全面无反应量表FOUR评分,桥脑中脑指数,crs-r评分,改良Ashworth分级评定法,三级平衡检测法,运动功能评定法,日常生活能力量表(ADL),创伤CRAMS评分法,日常生活活动能力量表(Barthel Index，BI),创伤指数(TI),ASIA损伤分级,ICH评分量表'
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '缺血') {
        this.title =
          'ASPECTS评分,NIHSS评分,长海LAST₂ CH₂ANCE评分,CHA₂DS₂-VASc,HAS-BLED评分,斯堪的那维亚卒中量表,欧洲卒中量表,辛辛那提院前卒中量表,洛杉矶院前中风筛检表,颅内血管动静脉畸形(AVM) Spetsler-Martin分级'
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '卒中再发风险评分') {
        this.title = 'Framingham风险评分,ESSNE评分,ABCD²,ABCD³-l'
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '动脉瘤') {
        this.title = '弹簧圈栓塞密度计算,Hunt-Hess评分,WFNS评分,改良Fisher分级,脑动脉瘤破裂风险评分(PHASES评分),脑动脉瘤生长风险评分(ELAPSS评分),卒中后迟发性痫性发作的预测评分(SeLECT评分)'
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '脑血管畸形') {
        this.title = 'Spetzler Martin分级,Borden分型,Cognard分级'
        this.$store.commit('minitool/setTitle', this.title)
      }else if (n === '步态分析法') {
        this.title = 'Berg平衡评定法,Tinetti步态和平衡量表,6分钟步行测试'
        this.$store.commit('minitool/setTitle', this.title)
      }else if (n === '帕金森') {
        this.title = '改良Hoehn-Yahr(H-Y分期法),帕金森病生活质量评分量表(PDQ-39),统一帕金森病评定量表,帕金森病睡眠量表(PDSS),MMSE简易精神状态检查量表,帕金森病非运动症状评价量表(NMSS),帕金森氏病患者生活质量量表简化版本(PDQ-8)'
        this.$store.commit('minitool/setTitle', this.title)
      } else if (n === '其他') {
        this.title =
          'CAM-ICU,FRANKEL脊髓损伤分级,MGS-GCS评分系统,汉密顿抑郁量表(HAMD),汉密顿焦虑量表(HAMA),SICH评分,深静脉血栓(DVT)Autar评估表,MMSE简易精神状态检查量表,医院焦虑抑郁量表(HAD),蒙特利尔认知评估,脑室出血的Graeb评分,机体反应水平分级(RLS),FLACC 行为评分,匹兹堡脑干评分(PBSS),颅内出血预后评分(max-ICH评分),蛛网膜下腔出血短期和长期结局评估(FRESH评分),高血压性脑出血1年再复发预测,蛛网膜下腔出血后不良事件和功能结局预测(HATCH得分),Sunnybrook面神经评定量表,健康调查简表SF36(生存质量简表)'
        this.$store.commit('minitool/setTitle', this.title)
      }
    },
  },
  beforeMount () {
    this.deletes = false
    localStorage.setItem('className', '评分记录')
    this.$store.commit('minitool/setRightnav', false)
    this.$store.commit('minitool/setIsOpenedShow', true)
  },
  computed: {
    // 从 vuex 中解析出来的数据
    ...mapState('minitool', ['gradingRecords', 'pageRecords']),
  },
  mounted () {
    // 使用 querySelector 选择 class 为 miniToolMain 的元素
    var miniToolMain = document.querySelector('.miniToolMain');
    // 将元素的背景样式设置为空字符串，清除背景
    if(miniToolMain){
      miniToolMain.style.background = '';
    }
    // 页面刷新 拿去评分记录最新数据
    if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
      this.$store.dispatch('minitool/gradingRecords', {
        $axios: this.$axios.$request,
        userId: this.$cookies.get('medtion_user_only_sign').id,
        router: this.$router,
        page: 1,
        skip: false,
      })
    }
    this.$store.commit('minitool/setPage', localStorage.getItem('currentPage'))
    this.name = localStorage.getItem('name')
    this.taxon = localStorage.getItem('taxon')
    // 初次渲染 如果vuex中没有数据 将本地存储的数据拿出
    if (this.$store.state.minitool.gradingRecords === '') {
      this.$store.commit('minitool/setGradingRecords', {
        gradingRecords: JSON.parse(localStorage.getItem('gradingRecords')),
        pageRecords: JSON.parse(localStorage.getItem('pageRecords')),
      })
    }
    // 判断哪个页面显示
    if (this.gradingRecords.length === 0) {
      this.content = 'false'
    } else {
      this.content = 'true'
    }
  },

  methods: {
    spinner () {
      this.options = false
    },
    getInnerhtml (e) {
      this.taxon = e.target.innerText
      this.options = false
    },
    optionsShow () {
      this.options = true
      this.checkeds = true
    },
    // 搜索按钮 点击事件
    search () {
      localStorage.setItem('currentPage', 1)
      this.$store.commit('minitool/setPage', 1)
      this.$store.dispatch('minitool/gradingRecords', {
        $axios: this.$axios.$request,
        userId: this.$cookies.get('medtion_user_only_sign').id,
        router: this.$router,
        page: 1,
      })
    },
    // 重置按钮
    reset () {
      localStorage.setItem('currentPage', 1)
      this.$store.commit('minitool/setPage', 1)
      this.$store.commit('minitool/setName', '')
      this.$store.commit('minitool/setTitle', '')
      localStorage.setItem('name', '')
      localStorage.setItem('taxon', '全部')
      this.name = ''
      this.taxon = '全部'
      this.$store.dispatch('minitool/gradingRecords', {
        $axios: this.$axios.$request,
        userId: this.$cookies.get('medtion_user_only_sign').id,
        router: this.$router,
        page: 1,
      })
    },
  },
}
</script>
<style lang="less" scoped>

.bigBox {
  font-family: 'Microsoft YaHei';
  width: 894px;

  .search {
    display: flex;
    margin-top: 16px;
    input {
      width: 240px;
      height: 36px;
      padding-left: 14px;
      box-sizing: border-box;
      margin-right: 18px;
      background: #ffffff;
      border: 1px solid #dedede;
      border-radius: 6px;
      line-height: 36px;
      color: #333333;
      font-size: 14px;
      font-weight: 400;
    }

    // input框 选中时的样式
    input:focus {
      border-color: #0581ce;
    }

    // input框 提示文字的颜色
    /deep/ input::-webkit-input-placeholder {
      -webkit-text-fill-color: #b0b0b0;
    }

    /deep/ input::-moz-input-placeholder {
      -webkit-text-fill-color: #b0b0b0;
    }

    /deep/ input::-ms-input-placeholder {
      -webkit-text-fill-color: #b0b0b0;
    }

    // 下拉框
    .slect {
      margin-right: 18px;
      position: relative;

      .el-icon-caret-bottom {
        position: absolute;
        color: #0581ce;
        font-size: 20px;
        top: 6.5px;
        left: 205px;
      }

      .checkbox {
        box-sizing: border-box;
        width: 240px;
        height: 36px;
        background: #ffffff;
        border: 1px solid #dedede;
        border-radius: 6px;
        font-weight: 400;
        font-size: 14px;
        color: #b0b0b0;
        line-height: 36px;
        padding-left: 14px;
      }

      .checkeds {
        color: #333333;
        font-weight: 400;
      }

      .options {
        // display: none;
        box-sizing: border-box;
        position: absolute;
        width: 240px;
        //height: 330px;
        left: 0px;
        top: 42px;
        background: #ffffff;
        border: 1px solid #dedede;
        box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        padding: 10px 0;

        ul {
          li {
            box-sizing: border-box;
            width: 238px;
            height: 38px;
            line-height: 38px;
            padding: 0 14px;
            font-weight: 400;
            font-size: 14px;
            color: #404040;
            cursor: pointer;
          }

          li:hover {
            color: #0581ce;
            background: rgba(5, 129, 206, 0.1);
          }

          .other {
            border-radius: 0 0 8px 8px;
          }

          .line {
            width: 212px;
            height: 1px;
            background: #f0f0f0;
            margin-left: 14px;
            box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.1);
          }

          .checked {
            font-weight: 700;
            color: #333333;
          }
        }
      }
    }

    // 搜索按钮
    button:nth-of-type(1) {
      width: 80px;
      height: 36px;
      background: #0581ce;
      border-radius: 6px;
      border: none;
      font-weight: 400;
      font-size: 14px;
      margin-right: 16px;
      color: #ffffff;
    }

    // 重置按钮
    button:nth-of-type(2) {
      background: #fb9537;
      border-radius: 6px;
      width: 80px;
      height: 36px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      border: none;
    }
  }
}

.particulars{
   background: none!important;
}
</style>
