<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="motorFunction" class="oneTopic">
            <p class="title">上肢（坐位）</p>
            <p class="subTitle">一、有无反射活动</p>
            <Options v-for="i in selfCareAbility" :key="i.title" :information="i"></Options>
            <p class="subTitle">二、屈肌协同运动</p>
            <Options v-for="i in sphincterControl" :key="i.title" :information="i"></Options>
            <p class="subTitle">三、伸肌协同运动</p>
            <Options v-for="i in transfer" :key="i.title" :information="i"></Options>
            <p class="subTitle">四、伴协同运动的活动</p>
            <Options v-for="i in walk" :key="i.title" :information="i"></Options>
            <p class="subTitle">五、脱离协同运动的活动</p>
            <Options v-for="i in exchange" :key="i.title" :information="i"></Options>
            <p class="subTitle">六、反射亢进</p>
            <Options v-for="i in socialCognition" :key="i.title" :information="i"></Options>
            <p class="subTitle">七、腕稳定性</p>
            <Options v-for="i in WristStability" :key="i.title" :information="i"></Options>
            <p class="subTitle">八、肘伸直，肩前屈30°时</p>
            <Options v-for="i in elbowExtension" :key="i.title" :information="i"></Options>
            <p class="subTitle">九、手指</p>
            <Options v-for="i in finger" :key="i.title" :information="i"></Options>
            <p class="subTitle">十、协同能力与速度（手指指鼻试验连续5次）</p>
            <Options v-for="i in Synergy" :key="i.title" :information="i"></Options>
          </div>
          <div ref="cognitiveFunction" class="cognitive-function">
            <p class="title">下肢</p>
            <p class="subTitle">一、有无反射活动（仰卧位）</p>
            <Options v-for="i in SupinePosition" :key="i.title" :information="i"></Options>
            <p class="subTitle">二、屈肌协同运动（仰卧位）</p>
            <Options v-for="i in flexorSynergy" :key="i.title" :information="i"></Options>
            <p class="subTitle">三、伸肌协同运动（仰卧位）</p>
            <Options v-for="i in extensorSynergy" :key="i.title" :information="i"></Options>
            <p class="subTitle">四、伴协同运动的活动（坐位）</p>
            <Options v-for="i in companionCollaborative" :key="i.title" :information="i"></Options>
            <p class="subTitle">五、脱离协同运动的活动（站位）</p>
            <Options v-for="i in disengagingCollaboration" :key="i.title" :information="i"></Options>
            <p class="subTitle">六、反射亢进（坐位）</p>
            <Options v-for="i in hyperreflexia" :key="i.title" :information="i"></Options>
            <p class="subTitle">七、协调能力与速度（跟-膝-胫试验，快速连续作5次）</p>
            <Options v-for="i in coordinationAbility" :key="i.title" :information="i"></Options>
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected !== 50">暂无</span>
            <span v-show="selected == 50">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import Options from '@/components/MiniTool/Options.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options },
  // tdk
  head () {
    return {
      title: '运动功能评定法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '运动功能评定法'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '运动功能评定法'
        }
      ]
    }
  },
  data () {
    return {
      // 有无反射活动
      selfCareAbility: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 220px;',
          title: '1. 肱二头肌',
          id: ['1-1', '1-2'],
          content: [
            '1) 不能引起反射活动：0分;',
            '2) 能引起反射活动：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['不能引起反射活动;', '能引起反射活动;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 220px;',
          title: '2. 肱三头肌',
          id: ['2-1', '2-2'],
          content: [
            '1) 完全独立：0分;',
            '2) 辅助独立：1。'
          ],
          grade: ['0', '1'],
          simpleContent: ['不能引起反射活动;', '能引起反射活动;']
        }
      ],
      // 屈肌协同运动
      sphincterControl: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 肩上提',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 肩后缩',
          id: ['4-1', '4-2', '4-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        },{
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 肩外展≥90°',
          id: ['5-1', '5-2', '5-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        },{
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 肩外旋',
          id: ['6-1', '6-2', '6-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        },{
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '5. 肘屈曲',
          id: ['7-1', '7-2', '7-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        },{
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '6. 前臂旋后',
          id: ['8-1', '8-2', '8-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        },
      ],
      // 伸肌协同运动
      transfer: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 肩内收、内旋',
          id: ['9-1', '9-2', '9-3'],
          content: [
            '1) 完全不能进行：0分;',
            '2) 部分完成：1分;',
            '3) 无停顿地充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 肘伸展',
          id: ['10-1', '10-2', '10-3'],
          content: [
            '1) 完全独立：0分;',
            '2) 辅助独立：1分;',
            '3) 监护或准备：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 前臂旋前',
          id: ['11-1', '11-2', '11-3'],
          content: [
            '1) 完全独立：0分;',
            '2) 辅助独立：1分;',
            '3) 监护或准备：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能进行;', '部分完成;', '无停顿地充分完成;']
        }
      ],
      // 行走
      walk: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '1. 手触腰椎',
          id: ['12-1', '12-2', '12-3'],
          content: [
            '1) 没有明显活动：0分;',
            '2) 手仅可向后越过骼前上棘：1分;',
            '3) 能顺利完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有明显活动;', '手仅可向后越过骼前上棘;', '能顺利完成;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '2. 肩关节屈曲90°，前臂旋前、旋后',
          id: ['13-1', '13-2', '13-3'],
          content: [
            '1) 开始时手臂立即外展或肘关节屈曲：0分;',
            '2) 在接近规定位置时肩关节外展或肘关节屈曲：1分;',
            '3) 能顺利充分完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['开始时手臂立即外展或肘关节屈曲;', '在接近规定位置时肩关节外展或肘关节屈曲;', '能顺利充分完成;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '3. 肩0°，屈肘90°，前臂旋前、旋后',
          id: ['14-1', '14-2', '14-3'],
          content: [
            '1) 不能屈肘或前臂，不能旋前：0分;',
            '2) 肩、肘位正确，基本能旋前、旋后：1分;',
            '3) 顺利完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能屈肘或前臂，不能旋前;', '肩、肘位正确，基本能旋前、旋后;', '顺利完成;']
        }
      ],
      // 脱离协同运动的活动
      exchange: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 肩关节外展90°，肘伸直，前臂旋前',
          id: ['15-1', '15-2', '15-3'],
          content: [
            '1) 开始时肘就屈曲，前臂偏离方向不能旋前：0分;',
            '2) 部分完成动作或肘关节屈曲或前臂不能旋前：1分;',
            '3) 顺利完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['开始时肘就屈曲，前臂偏离方向不能旋前;', '部分完成动作或肘关节屈曲或前臂不能旋前;', '顺利完成;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 肩关节前屈举臂过头肘伸直前臂中立位',
          id: ['16-1', '16-2', '16-3'],
          content: [
            '1) 开始时肘关节屈曲或肩关节外展：0分;',
            '2) 肩屈曲中途，肘关节屈曲，肩关节外展：1分;',
            '3) 顺利完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['开始时肘关节屈曲或肩关节外展;', '肩屈曲中途，肘关节屈曲，肩关节外展;', '顺利完成;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 肩屈曲30°~90°肘伸直前臂旋前旋后',
          id: ['17-1', '17-2', '17-3'],
          content: [
            '1) 前臂旋前旋后完全不能或肩肘位不正确：0分;',
            '2) 肩肘位置正确基本能完成旋前旋后：1分;',
            '3) 顺利完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['前臂旋前旋后完全不能或肩肘位不正确;', '肩肘位置正确基本能完成旋前旋后;', '顺利完成;']
        }
      ],
      // 反射亢进
      socialCognition: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '1. 查肱二头肌、肱三头肌、指屈肌3反射',
          id: ['18-1', '18-2', '18-3'],
          content: [
            '1) 至少2~3个反射明显亢进：0分;',
            '2) 1个反射明显亢进或至少2个反射活跃：1分;',
            '3) 活跃反射≤1个且无反射亢进：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['至少2~3个反射明显亢进;', '1个反射明显亢进或至少2个反射活跃;', '活跃反射≤1个且无反射亢进;']
        }
      ],
      // 反射亢进
      WristStability: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 肩0°，肘屈90°腕背屈',
          id: ['19-1', '19-2', '19-3'],
          content: [
            '1) 不能背屈腕关节达15°：0分;',
            '2) 可完成腕背屈，但不能抗拒阻力：1分;',
            '3) 施加轻微阻力可保持腕背屈：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能背屈腕关节达15°;', '可完成腕背屈，但不能抗拒阻力;', '施加轻微阻力可保持腕背屈;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 肩0°，肘屈90°腕屈伸',
          id: ['20-1', '20-2', '20-3'],
          content: [
            '1) 不能随意屈伸：0分;',
            '2) 不能在全关节范围内主动活动腕关节：1分;',
            '3) 不停顿进行：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能随意屈伸;', '不能在全关节范围内主动活动腕关节;', '不停顿进行;']
        }
      ],
      // 肘伸直，肩前屈30°时
      elbowExtension: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 腕背屈',
          id: ['21-1', '21-2', '21-3'],
          content: [
            '1) 不能背屈腕关节达15°：0分;',
            '2) 可完成腕背屈，但不能抗拒阻力：1分;',
            '3) 施加轻微阻力可保持腕背屈：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能背屈腕关节达15°;', '可完成腕背屈，但不能抗拒阻力;', '施加轻微阻力可保持腕背屈;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 腕屈伸',
          id: ['22-1', '22-2', '22-3'],
          content: [
            '1) 不能随意屈伸：0分;',
            '2) 不能在全关节范围内主动活动腕关节：1分;',
            '3) 能平滑不停顿的进行：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能随意屈伸;', '不能在全关节范围内主动活动腕关节;', '能平滑不停顿的进行;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 腕环行运动',
          id: ['23-1', '23-2', '23-3'],
          content: [
            '1) 不能进行：0分;',
            '2) 活动费力或不完全：1分;',
            '3) 正常完成：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能进行;', '活动费力或不完全;', '正常完成;']
        }
      ],
      // 手指
      finger: [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '1. 集团屈曲',
          id: ['24-1', '24-2', '24-3'],
          content: [
            '1) 不能屈曲：0分;',
            '2) 能屈曲但不充分能放松主动屈曲的手指：1分;',
            '3) 能完成主动屈曲和伸展：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能屈曲;', '能屈曲但不充分能放松主动屈曲的手指;', '能完成主动屈曲和伸展;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '2. 集团伸展',
          id: ['25-1', '25-2', '25-3'],
          content: [
            '1) 不能伸展：0分;',
            '2) 能放松主动屈曲的手指：1分;',
            '3) 能完全主动伸展：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能伸展;', '能放松主动屈曲的手指;', '能完全主动伸展;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '3. 钩状抓握',
          id: ['26-1', '26-2', '26-3'],
          content: [
            '1) 不能保持要求位置：0分;',
            '2) 握力微弱：1分;',
            '3) 能抵抗相当大的阻力：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能保持要求位置;', '握力微弱;', '能抵抗相当大的阻力;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '4. 侧捏',
          id: ['27-1', '27-2', '27-3'],
          content: [
            '1) 完全不能：0分;',
            '2) 捏力微弱：1分;',
            '3) 能抵抗相当大的阻力：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能;', '捏力微弱;', '能抵抗相当大的阻力;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '5. 对捏（拇食指可挟住一根铅笔）',
          id: ['28-1', '28-2', '28-3'],
          content: [
            '1) 完全不能：0分;',
            '2) 捏力微弱：1分;',
            '3) 能抵抗相当大的阻力：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能;', '捏力微弱;', '能抵抗相当大的阻力;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '6. 圆柱状抓握',
          id: ['29-1', '29-2', '29-3'],
          content: [
            '1) 不能保持要求位置：0分;',
            '2) 握力微弱：1分;',
            '3) 能抵抗相当大的阻力：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能;', '捏力微弱;', '能抵抗相当大的阻力;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '7. 球形抓握',
          id: ['30-1', '30-2', '30-3'],
          content: [
            '1) 不能保持要求位置：0分;',
            '2) 握力微弱：1分;',
            '3) 能抵抗相当大的阻力：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['完全不能;', '捏力微弱;', '能抵抗相当大的阻力;']
        }
      ],
      // 协同能力与速度（手指指鼻试验连续5次）
      Synergy: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 震颤(上肢)',
          id: ['31-1', '31-2', '31-3'],
          content: [
            '1) 明显震颤：0分;',
            '2) 轻度震颤：1分;',
            '3) 无震颤：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['明显震颤;', '轻度震颤;', '无震颤;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 辨距障碍上肢',
          id: ['32-1', '32-2', '32-3'],
          content: [
            '1) 明显或不规则：0分;',
            '2) 轻度或规则：1分;',
            '3) 无：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['明显或不规则;', '轻度或规则;', '无;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 速度上肢',
          id: ['33-1', '33-2', '33-3'],
          content: [
            '1) 较健侧长6秒：0分;',
            '2) 较健侧长2~5秒：1分;',
            '3) 两侧差别<2秒：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['较健侧长6秒;', '较健侧长2~5秒;', '两侧差别<2秒;']
        }
      ],
      // 有无反射活动（仰卧位）
      SupinePosition:[
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 220px;',
          title: '1. 跟腱反射',
          id: ['34-1', '34-2'],
          content: [
            '1) 无反射活动：0分;',
            '2) 有反射活动：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['无反射活动;', '有反射活动;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 220px;',
          title: '2. 膝腱反射',
          id: ['35-1', '35-2'],
          content: [
            '1) 无反射活动：0分;',
            '2) 有反射活动：1分。'
          ],
          grade: ['0', '1'],
          simpleContent: ['无反射活动;', '有反射活动;']
        }
      ],
      // 屈肌协同运动（仰卧位）
      flexorSynergy:[
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '1. 髋关节伸展上肢',
          id: ['36-1', '36-2', '36-2'],
          content: [
            '1) 不能进行：0分;',
            '2) 部分进行：1分,',
            '3) 充分进行：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能进行;', '部分进行;', '充分进行;']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '2. 膝关节屈曲上肢',
          id: ['37-1', '37-2', '37-3'],
          content: [
            '1) 不能进行：0分;',
            '2) 部分进行：1分,',
            '3) 充分进行：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能进行;', '部分进行;','充分进行']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '3. 踝关节屈曲上肢',
          id: ['38-1', '38-2', '38-3'],
          content: [
            '1) 不能进行：0分;',
            '2) 部分进行：1分,',
            '3) 充分进行：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能进行;', '部分进行;','充分进行']
        }
      ],

      // 伸肌协同运动（仰卧位）
      extensorSynergy:[
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 髋关节伸展',
          id: ['39-1', '39-2', '39-2'],
          content: [
            '1) 没有运动：0分;',
            '2) 微弱运动：1分,',
            '3) 几乎与对侧相同：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有运动;', '微弱运动;', '几乎与对侧相同;']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 膝关节伸展',
          id: ['40-1', '40-2', '40-3'],
          content: [
            '1) 没有运动：0分;',
            '2) 微弱运动：1分,',
            '3) 几乎与对侧相同：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有运动;', '微弱运动;','几乎与对侧相同']
        },{
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 踝关节伸展',
          id: ['41-1', '41-2', '41-3'],
          content: [
            '1) 没有运动：0分;',
            '2) 微弱运动：1分,',
            '3) 几乎与对侧相同：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有运动;', '微弱运动;','几乎与对侧相同']
        },{
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 踝关节屈曲坐位',
          id: ['42-1', '42-2', '42-3'],
          content: [
            '1) 没有运动：0分;',
            '2) 微弱运动：1分,',
            '3) 几乎与对侧相同：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['没有运动;', '微弱运动;','几乎与对侧相同']
        },
      ],
      // 伴协同运动的活动（坐位）
      companionCollaborative:[
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 膝关节屈曲',
          id: ['43-1', '43-2', '43-3'],
          content: [
            '1) 无主动运动：0分;',
            '2) 膝关节能从位伸位屈曲，但<90°：1分,',
            '3) 屈曲>90°：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['无主动运动;', '膝关节能从位伸位屈曲，但<90°;','屈曲>90°']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 踝关节屈曲',
          id: ['44-1', '44-2', '44-3'],
          content: [
            '1) 不能主动背屈：0分;',
            '2) 主动背屈不完：1分;',
            '3) 正常背屈站立：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能主动背屈;', '主动背屈不完;', '正常背屈站立;']
        }
      ],
      // 脱离协同运动的活动（站位）
      disengagingCollaboration:[
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 膝关节背曲',
          id: ['45-1', '45-2', '45-3'],
          content: [
            '1) 在髋关节伸展位时不能屈膝：0分;',
            '2) 髋关节0°时膝关节能屈曲<90°，或进行时髋屈曲：1分,',
            '3) 能自如运动：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['在髋关节伸展位时不能屈膝;', '髋关节0°时膝关节能屈曲<90°，或进行时髋屈曲;','能自如运动']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 踝关节背屈坐位',
          id: ['46-1', '46-2', '46-3'],
          content: [
            '1) 不能自主活动：0分;',
            '2) 能部分背屈：1分;',
            '3) 能充分背屈：2分。'
          ],
          grade: ['0', '1', '2'],
          simpleContent: ['不能自主活动;', '能部分背屈;', '能充分背屈;']
        }
      ],
      // 反射亢进（坐位）
      hyperreflexia:[
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 查跟腱、膝和膝屈肌三种反射',
          id: ['47-1', '47-2', '47-3'],
          content: [
            '1) 2~3个反射明显亢进：0分;',
            '2) 1个反射明显亢进或至少2个反射活跃：1分,',
            '3) 活跃反射≤1个且无反射亢进：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['2~3个反射明显亢进;', '1个反射明显亢进或至少2个反射活跃;','活跃反射≤1个且无反射亢进']
        }
      ],
      // 协调能力与速度（跟-膝-胫试验，快速连续作5次）
      coordinationAbility:[
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '1. 震颤下肢',
          id: ['48-1', '48-2', '48-3'],
          content: [
            '1) 2~3个反射明显亢进：0分;',
            '2) 1个反射明显亢进或至少2个反射活跃：1分,',
            '3) 活跃反射≤1个且无反射亢进：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['2~3个反射明显亢进;', '1个反射明显亢进或至少2个反射活跃;','活跃反射≤1个且无反射亢进']
        },
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '2. 辨距障碍',
          id: ['49-1', '49-2', '49-3'],
          content: [
            '1) 明显或不规则：0分;',
            '2) 轻度或规则：1分,',
            '2) 无：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['明显或不规则;', '轻度或规则;','无']
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '3. 速度',
          id: ['50-1', '50-2', '50-3'],
          content: [
            '1) 较健侧长6秒：0分;',
            '2) 较健侧长2~5秒：1分,',
            '2) 两侧差别<2秒：2分。'
          ],
          grade: ['0', '1','2'],
          simpleContent: ['较健侧长6秒;', '较健侧长2~5秒;','两侧差别<2秒']
        }
      ],

      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 50,
      // 总分数
      totalPoints: 0,
      choiceBoxs: [[], []],
      score: {
        // 运动功能 分数
        motorFunctionScore: 0,
        // 认知功能 分数
        cognitiveFunctionScore: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: []
    }
  },

  computed: {
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      let _this = this.totalPoints
      if (_this>=96&&_this<= 99) {
        return `${_this}分，IV级，轻度运动障碍`
      } else if (_this>=85&&_this<= 95) {
        return `${_this}分，III级，中度运动障碍`
      } else if (_this>=50&&_this<= 84) {
        return `${_this}分，II级，明显运动障碍`
      } else if (_this<50) {
        return `${_this}分，I级，严重运动障碍`
      }
    }
  },

  beforeMount () {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '运动功能评定法')
  },

  mounted () {
    this.motorFn()
    this.cognitiveFn()
    this.loginEnd()
  },

  methods: {
    // 未登录状态下 做题 登录之后的逻辑
    loginEnd () {
      // 登录之后 从本地拿去数据 勾选选项
      if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
        this.allInput.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
            ].choice
        })
      }
      // 勾选完，循环数组，拿去分数和选中个数
      this.allInput.map((item) => {
        // 如果 处于选中状态 就处理以下逻辑
        if (item.checked === true) {
          if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
            this.choiceBox.push(item.id.slice(0, 2))
          }
          this.selected = this.choiceBox.length
          this.unselected = 50 - this.selected
          this.totalPoints += item.attributes.grade.value * 1
        }
      })
    },
    // 获取 运动功能 中的所有input框
    motorFn () {
      for (let i = 0; i < this.$refs.motorFunction.children.length; i++) {
        let index = this.$refs.motorFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[0].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
      // console.log(this.choiceBoxs[0])
    },
    // 获取 认知功能 中的所有input框
    cognitiveFn () {
      for (let i = 0; i < this.$refs.cognitiveFunction.children.length; i++) {
        let index = this.$refs.cognitiveFunction.children[i]
        if (index.children.length != 0) {
          let length = index.children[0].children[3].children.length
          for (let j = 0; j < length; j++) {
            this.choiceBoxs[1].push(
              index.children[0].children[3].children[j]
                .children[0]
            )
          }
        }
      }
      // 清空 allInput
      this.allInput = []
      // 合并两个数组
      this.allInput = [...this.allInput, ...this.choiceBoxs[0], ...this.choiceBoxs[1]]
    },
    // 总分
    result () {
      this.totalPoints = this.score.motorFunctionScore + this.score.cognitiveFunctionScore
    },
    checkedMotor (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 50 - this.selected
        this.score.motorFunctionScore += item.attributes.grade.value * 1
        // console.log(this.score.motorFunctionScore,"motorFunctionScore")
      }
    },
    checkedCognitive (item) {
      if (item.checked) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 50 - this.selected
        this.score.cognitiveFunctionScore += item.attributes.grade.value * 1
        // console.log(this.score.cognitiveFunctionScore,"cognitiveFunctionScore")
      }
    },
    // 选项的点击事件
    getTatals () {
      // 初始化数据
      this.selected = 0
      this.choiceBox = []
      this.unselected = 50
      this.totalPoints = 0
      this.optionMessage = []
      this.score.motorFunctionScore = 0
      this.score.cognitiveFunctionScore = 0
      // 运动功能
      this.choiceBoxs[0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedMotor(item)
      })
      // 认知功能
      this.choiceBoxs[1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedCognitive(item)
      })
      console.log(this.choiceBox,"this.choiceBoxs[0]",this.optionMessage)
      // 计算总分
      this.result()
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        console.log(keepArguments,"keepArguments")

        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    .title {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      text-align: center;
      margin: 18px 0;
    }

    .subTitle {
      font-family: 'Microsoft YaHei';
      font-weight: 500;
      font-size: 18px;
      color: #202020;
      margin: 20px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    .threeTopic {
      div {
        width: 877px;
        margin-top: 16px;
        padding: 3px 16px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(5, 129, 206, 0.03);

        p:nth-of-type(1) {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }

        p:nth-of-type(2) {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
