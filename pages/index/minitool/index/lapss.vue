<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <SheetChange :information="age"></SheetChange>
          <SheetChange :information="epilepsy"></SheetChange>
          <SheetChange :information="symptom"></SheetChange>
          <SheetChange :information="lieInBed"></SheetChange>
          <SheetChange :information="bloodGlucose"></SheetChange>
          <SheetChange :information="around"></SheetChange>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue';
import SheetChange from '@/components/MiniTool/SheetChange.vue';
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue';


export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails },
  // tdk
  head () {
    return {
      title: '洛杉矶院前中风筛检表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '洛杉矶院前卒中筛查量表（Los Angeles Pre-hospital Stroke Screen，LAPSS）由Kidwell等人设计，是一个专门为院前确认中风而设计的简便（耗时≤3 min）、可靠的中风筛检工具。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '洛杉矶院前中风筛检表, lapss, 洛杉矶院前卒中筛查, 洛杉矶院前卒中筛量表'
        }
      ]
    }
  },
  data () {
    return {
      // 1.年龄大于45岁
      age: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 719px;',
        title: '1. 年龄大于45岁',
        id: ['1-1', '1-2', '1-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 2.以前有癫痫发作史
      epilepsy: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 719px;',
        title: '2. 以前有癫痫发作史',
        id: ['2-1', '2-2', '2-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 3.症状持续小于24小时
      symptom: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 719px;',
        title: '3. 症状持续小于24小时',
        id: ['3-1', '3-2', '3-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 4.病前不能乘坐轮椅或卧床
      lieInBed: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 719px;',
        title: '4. 病前不能乘坐轮椅或卧床',
        id: ['4-1', '4-2', '4-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 5.血糖在60-400mg/l间
      bloodGlucose: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 719px;',
        title: '5. 血糖在60-400mg/l间',
        id: ['5-1', '5-2', '5-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 6.根据以下三项查体，患者有明显的单侧力弱（包括两侧面部表情，左右侧握力，左右侧臂力）
      around: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 719px;',
        title: '6. 根据以下三项查体，患者有明显的单侧力弱（包括两侧面部表情，左右侧握力，左右侧臂力）',
        id: ['6-1', '6-2', '6-3'],
        content: ['是',
          '不详',
          '否'],
        grade: ['是', '不详', '否'],
        show: false,
        simpleContent: ['是;',
          '不详;',
          '否;'],
        describe: [],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 6,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', '洛杉矶院前中风筛检表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (let j = 0; j < this.$refs.reference.children[i].children[0].children[0].children[1].children.length; j++) {
        this.choiceBox.push(this.$refs.reference.children[i].children[0].children[0].children[1].children[j].children[0])
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1;
        this.unselected -= 1;
        if (element.attributes.grade.value !== '否') {
          this.totalPoints = '符合LAPSS筛检表准，为可疑卒中病人，应通知医院'
        } else {
          this.totalPoints = '继续选择适当的治疗协议'
        }
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.unselected = 6;
      this.selected = 0;
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach(element => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      });
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }

    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 600px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #FFFFFF;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>