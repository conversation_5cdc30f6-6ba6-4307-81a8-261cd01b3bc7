<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="walk"></Options>
          <Options :information="dressing"></Options>
          <Options :information="feelingDepressed"></Options>
          <Options :information="feelEmbarrassed"></Options>
          <Options :information="interpersonalRelation"></Options>
          <Options :information="attention"></Options>
          <Options :information=" communicate"></Options>
          <Options :information=" cramps"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '帕金森氏病患者生活质量量表简化版本(PDQ-8) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'PDQ-8（Parkinson‘s Disease Questionnaire - 8）是评估帕金森病患者生活质量的标准量表，包含8个问题，通过问卷形式让患者自评疾病对其生活的影响程度，量化疾病所致生活质量的下降，为临床治疗与研究提供关键参考依据。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'PDQ-8,PDQ-39,帕金森病生活质量问卷,帕金森病39项生活质量量表'
        }
      ]
    }
  },
  data () {
    return {
      // 随便走走
      walk: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 465px;',
        title: '1. 在外面随便走走，有问题吗?',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 自己穿衣
      dressing: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 465px;',
        title: '2. 自己穿衣，有困难吗?',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 感到抑郁
      feelingDepressed: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 465px;',
        title: '3. 感到抑郁吗?',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 觉得尴尬
      feelEmbarrassed: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 465px;',
        title: '4. 因为帕金森病，觉得在公共场合很尴尬吗?',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 处理人际关系
      interpersonalRelation: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 465px;',
        title: '5. 处理好朋友之间的人际关系，有问题吗?',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 注意力
      attention: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 465px;',
        title: '6. 在看电视、读报纸的时候，集中注意力会有问题吗?',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 无法进行沟通
      communicate: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 465px;',
        title: '7. 感觉和他人无法进行沟通，是吗?',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 肌肉抽筋疼痛
      cramps: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 465px;',
        title: '8. 有肌肉抽筋或抽筋导致的疼痛吗?',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: ['从不;','偶尔;','有时;','经常;','始终或根本无法做。'],
        grade: ['0', '1', '2', '3','4'],
        simpleContent: ['从不;','偶尔;','有时;','经常;','始终或根本无法做;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 8,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
      // 风险说明
      risk: '',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', '帕金森氏病患者生活质量量表简化版本(PDQ-8)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },

    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 8
      this.selected = 0
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      // this.result()

      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
