<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="topicOne" class="changeWord"></Options>
          <Options v-show="twoShow" :information="topicTwo" class="changeWord"></Options>
          <Options v-show="threeShow" :information="topicThree" class="changeWord"></Options>
          <Options v-show="fourShow" :information="topicFour" class="changeWord"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="totalPoints === 0">暂无</span>
            <span v-show="totalPoints !== 0">{{ totalPoints }}</span>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'CAM-ICU评估 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '谵妄的特点是：快速出现、注意力障碍、意识混乱，可呈现出一过性，这种病症具有波动性（突然发病）。谵妄需要和老年痴呆相鉴别，老年痴呆的特点是:逐渐出现、智力障碍、记忆力障碍、个人性格改变，病变没有波动性。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'CAM-ICU, ICDSC, 常规谵妄评估, 谵妄, RASS, 镇静, 躁动, Bis, Riker镇静, 特异性, 疼痛强度'
        }
      ]
    }
  },
  data () {
    return {
      // 题目一
      topicOne: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 意识状态急性改变或波动：\n患者的意识状态是否与其基线状态不同？或在过去的24h内，患者的意识状态是否有任何波动？表现为RASS,GCS或既往谵妄评估得分有波动？',
        id: ['1-1', '1-2'],
        grade: ['是', '否'],
        show: 'false',
        simpleContent: ['是;', '否;'],
      },
      // 题目二
      topicTwo: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: `2. 注意力障碍：患者进行如下操作，错误的次数是否大于2次？\n数字法检查注意力：指导语：跟患者说：“我要给您读10个数字，任何时候当您听到数字8时，捏一下我的手。”正常朗读语调，每隔3s。\n如：6 8 5 9 8 3 8 8 4 7 ，当读到8时患者没有捏手或者读到其他数字捏手为错误。`,
        id: ['2-1', '2-2'],
        grade: ['是（大于2个错误）', '否（0-2个错误）'],
        show: 'false',
        simpleContent: ['是（大于2个错误）;', '否（0-2个错误）;'],
      },
      // 题目三
      topicThree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 419px;',
        title:
          '3. 意识水平改变（RASS实际得分），RASS评分是否为0/4？\nRASS评分(先进行意识水平评估，若RASS≥-3，则继续CAM-ICU，若RASS为-4或-5，患者无意识过会儿再评估)\n+4 攻击性 暴力行为\n+3 非常躁动 想拔管\n+2 躁动 剧烈移动无法配合呼吸机\n+1 不安 焦虑轻微移动\n0 警觉但安静\n-1 嗜睡 清醒＞10s\n-2 轻度镇静 清醒＜10s\n-3 中度镇静 声音反应\n-4 深度镇静 身体刺激反应\n-5 不易警觉 昏迷 不能唤醒',
        id: ['3-1', '3-2'],
        grade: ['是（RASS为0/4）', '否（RASS不为0，也不为4）'],
        show: 'false',
        simpleContent: ['是（RASS为0/4）;', '否（RASS不为0，也不为4）;'],
      },
      // 题目四
      topicFour: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 419px;',
        title:
          '4. 思维混乱：患者对以下问题进行回答？\n是非题？\n（1）石头是否能浮在水面上?\n（2）海里是否有鱼？\n（3）1斤是否比2斤重？\n（4）您是否用榔头钉钉子？\n执行命令，跟患者说：“伸出这几根手指”（检查者，在患者面前伸出2根手指，然后说：“现在用另一只手伸出同样多的手指“（这次检查者不做示范）如果患者不能成功执行全部指令，记录1个错误',
        id: ['4-1', '4-2'],
        grade: ['是（大于等于两个错误）', '否（0-1个错误）'],
        show: 'false',
        simpleContent: ['是（大于等于两个错误）;', '否（0-1个错误）;'],
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 4,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
      // 2 3 4 题目的显示与隐藏
      twoShow: false,
      threeShow: false,
      fourShow: false,
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', 'CAM-ICU评估')
  },

  mounted () {
    for (let j = 0; j < this.$refs.reference.children.length; j++) {
      for (
        let i = 0;
        i <
        this.$refs.reference.children[j].children[0].children[3].children
          .length;
        i++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[j].children[0].children[3].children[i]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.map((item, index) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(item, index)
    })
  },
  methods: {
    // 公共逻辑
    checked (item, index) {
      if (item.checked === true) {
        if (index === 0) {
          this.twoShow = true
          this.totalPoints = 0
          this.unselected = 1
        } else if (index === 1) {
          this.unselected = 0
          for (let i = 2; i < this.choiceBox.length; i++) {
            this.choiceBox[i].checked = false
          }
          this.twoShow = false
          this.threeShow = false
          this.fourShow = false
          this.totalPoints = '谵妄阴性'
        } else if (index === 2) {
          this.unselected = 1
          this.threeShow = true
          this.totalPoints = 0
        } else if (index === 3) {
          this.unselected = 0
          for (let i = 4; i < this.choiceBox.length; i++) {
            this.choiceBox[i].checked = false
          }
          this.threeShow = false
          this.fourShow = false
          this.totalPoints = '谵妄阴性'
        } else if (index === 4) {
          this.unselected = 1
          this.fourShow = true
          this.totalPoints = 0
        } else if (index === 5) {
          this.unselected = 0
          this.fourShow = false
          this.totalPoints = '谵妄阴性'
        } else if (index === 6) {
          this.unselected = 0
          this.totalPoints = '谵妄阳性'
        } else if (index === 7) {
          this.unselected = 0
          this.totalPoints = '谵妄阴性'
        }
      }
    },
    getTatals (e) {
      // 每次触发点击事件之后先初始化数据
      this.selected = 0
      this.unselected = 4
      // 选项的状态
      let optionMessage = []

      // 遍历 choiceBox 数组
      this.choiceBox.map((item, index) => {
        // 存储选项的状态
        optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(item, index)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .changeWord /deep/ .openEyes>p {
    font-weight: 400 !important;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
