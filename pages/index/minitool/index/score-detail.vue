<template>
  <div class='bigBox'>
    <!-- 评分信息 -->
    <div class='headline'>
      <p>评分信息</p>
    </div>
    <div class='type'>
      <div class='typeLift'>
        <p>类型：</p>
        <span>{{ information.scoreTitle }}</span>
        <p>时间：</p>
        <span>{{ information.createTime }}</span>
      </div>
      <div class='typeRight'>
        <nuxt-link :to='{ path: againScorePath, query: relevanceId }'>
          <button class='btnOne' @click='scoreAgain'>再次评分</button>
        </nuxt-link>
        <button class='btnTwo' @click='shareFlag = true'>分享评分</button>
      </div>
    </div>
    <!-- 患者信息 -->
    <div class='patientinformation'>
      <div class='headlines'>
        <p>患者信息</p>
        <div class='filament'>
          <div class='pachytene'></div>
        </div>
      </div>
      <div class='content'>
        <div class='top'>
          <p>姓名：</p>
          <span>{{ information.name }}</span>
          <p>性别：</p>
          <span>{{ information.gender || '未知' }}</span>
          <p>年龄：</p>
          <span>{{ information.age || '未知' }}</span>
        </div>
        <div class='bottom'>
          <p>诊断：</p>
          <span>{{ information.diagnosis }}</span>
        </div>
      </div>
    </div>
    <!-- 评分详情 -->
    <div class='scoringdetails'>
      <div class='headlines'>
        <p>评分详情</p>
        <div class='filament'>
          <div class='pachytene'></div>
        </div>
      </div>
      <div v-show="information.scoreTitle !== '弹簧圈栓塞密度计算'" class='content'>
        <button v-show="information.scoreType !== ''" class='btn'>
          {{ information.scoreType }}
        </button>
        <ul>
          <li v-for='(i, index) in scoreContent' :key='index'>
            <!-- 题目 -->
            <p v-show='topicShow'>
              <div v-if="information.scoreTitle == '帕金森病非运动症状评价量表(NMSS)'">
            <p v-show='shouldDisplayTopic(i.topic)'>
              {{ i.topic }}
            </p>
          </div>
            <div v-else>{{ i.topic }}</div>
            </p>
            <!-- 选项 -->
            <div class='alone'>
              <div v-if="information.scoreTitle == '帕金森病非运动症状评价量表(NMSS)'">
                <p v-show='optionShow'>
                 {{ i.option.includes('：') ? i.option.slice(0, i.option.length) : i.option }}
                </p>
              </div>
              <div v-if="information.scoreTitle == '蛛网膜下腔出血短期和长期结局评估(FRESH评分)'">
                <p v-show='optionShow'>
                  {{i.option }} {{ i.score }}分
                </p>
              </div>
              <div v-else>
                <p v-show='optionShow'>
                  {{ i.option }}
                <span v-show="resultGrade && lastscoreShow && i.score !== ''">
                  {{ i.score }}
                </span>
                <span v-show="resultGrade && lastscoreShow && framing && i.score !== ''">分</span>
                </p>
              </div>
            </div>
          </li>
        </ul>
        <p>
          结果：<span style='white-space: pre-wrap'>{{ dvtResult ? dvtResult : information.scoreResult }}</span><span
          v-show='resultGrade && dvtShow'>分</span>
        </p>
        <p></p>
      </div>
      <!-- 弹簧圈 -->
      <div v-show="information.scoreTitle === '弹簧圈栓塞密度计算'" class='content'>
        <button v-show="information.scoreType !== ''" class='btn'>
          {{ information.scoreType }}
        </button>
        <ul>
          <li v-for='(i, index) in arterialAneurysmData' :key='index'>
            <!-- 题目 -->
            <p v-show='topicShow'>
              {{ i.topic }}
            </p>
            <!-- 选项 -->
            <div class='alone'>
              <p v-show='optionShow' v-for='(j, ind) in i.option' :key='ind'>
                {{ j }}
              </p>
            </div>
          </li>
          <li v-for='(i, index) in springData' :key='index'>
            <!-- 题目 -->
            <p v-show='topicShow'>
              {{ i.topic }}
            </p>
            <!-- 选项 -->
            <div class='alone'>
              <p v-show='optionShow' v-for='(j, ind) in i.option' :key='ind'>
                {{ j }}
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 关联评分 -->
    <div v-show='associatedScore' class='associatedscore'>
      <div class='headlines'>
        <p>关联评分</p>
        <div class='filament'>
          <div class='pachytene'></div>
        </div>
      </div>
      <div class='score'>
        <div @click='relevance($event)' v-for='(i, index) in information.scores' :key='index'>
          <p :id='information.scores[index].id'>
            {{ i.createTime }}
          </p>
        </div>
      </div>
    </div>
    <ScoreShare :share-data='information' :share-flag='shareFlag' @editFlag='shareOpenFun'></ScoreShare>
  </div>
</template>

<script>
import ScoreShare from '@/components/MiniTool/ScoreShare'
import { mapState } from 'vuex'

export default {
  components: {
    ScoreShare
  },
  data() {
    return {
      framingham: [
        '年龄',
        '性别',
        '吸烟状况',
        '整体胆固醇水平',
        '好的胆固醇水平',
        '收缩压',
        '血压是否被治疗'
      ],
      shareFlag: false, // 分享弹框
      // 标题 显示
      topicShow: true,
      //  Framingham风险评分
      framing: true,
      // 分 是否显示
      scoreShow: true,
      // 选项显示
      optionShow: true,
      // 结果 分 显示
      resultGrade: true,
      dvtShow: true,
      // 选项后边 的分数显示
      lastscoreShow: true,
      // 再次评分路径
      againScorePath: '',
      // 评分详情
      scoreContent: [],
      // 动脉瘤数据
      arterialAneurysmData: [],
      springData: [],
      // 关联id
      relevanceId: {
        id: ''
      },
      // 关联评分 显示
      associatedScore: true,
      // 蒙特利尔分数
      score: {
        scoreOneTopic: [],
        scoreTwoTopic: [],
        scoreThreeTopic: [],
        scoreFourTopic: [],
        scoreFiveTopic: [],
        scoreSixTopic: [],
        scoreSevenTopic: []
      },
      // 评分题目
      topic: [
        '肌力评分',
        'ASIA损伤分级',
        '改良Hoehn-Yahr(H-Y分期法)',
        '洼田饮水试验评分',
        '辛辛那提院前卒中量表',
        '洛杉矶院前中风筛检表',
        'WFNS评分',
        '改良Fisher分级',
        'Borden分型',
        'Cognard分级',
        'CAM-ICU评估',
        'FRANKEL脊髓损伤分级',
        'oa颈髓损伤评分',
        'Brunnstrom偏瘫功能评价法',
        '机体反应水平分级(RLS)',
        '徒手肌力评定法',
        '改良Ashworth分级评定法',
        '三级平衡检测法',
        '6分钟步行测试',
        'MGS-GCS评分系统',
        'SICH评分',
        '弹簧圈栓塞密度计算',
        '帕金森病非运动症状评价量表(NMSS)',
        '远期生活质量评估（Karnofsky Performance Scale，KPS）',
        '匹兹堡睡眠质量指数量表PSQI',
        '简易智能精神状态检查量表(MMSE)'
      ]
    }
  },

  computed: {
    // 从 vuex 中解析出来的数据
    ...mapState('minitool', [
      'information',
      'generalBoolTitle',
      'ischemia',
      'apoplexy',
      'artery',
      'malformation',
      'walkNametion',
      'parkinson',
      'other',
      'generalName',
      'ischemiaName',
      'apoplexyName',
      'walkName',
      'arterialAneurysmName',
      'cerebrovascularMalformation',
      'parkinsonName',
      'otherName'
    ]),
    dvtResult() {
      let score = this.information.scoreResult
      if (this.information.scoreTitle == '深静脉血栓(DVT)Autar评估表') {
        if (score <= 10) {
          return `${score}分，低风险`
        } else if (score > 10 && score <= 14) {
          return `${score}分，中风险`
        } else if (score >= 15) {
          return `${score}分，高风险`
        }
      }
    }
  },

  async mounted() {
    // 使用 querySelector 选择 class 为 miniToolMain 的元素
    var miniToolMain = document.querySelector('.miniToolMain');
    // 将元素的背景样式设置为空字符串，清除背景
    if(miniToolMain){
      miniToolMain.style.background = '';
    }

    localStorage.setItem('className', '评分记录')
    if (this.$route.query.id === undefined) {
      await this.$store.dispatch('minitool/viewdetails', {
        id: `${window.location.href.slice(-4)}`,
        $axios: this.$axios.$request,
        router: this.$router
      })
    } else {
      await this.$store.dispatch('minitool/viewdetails', {
        id: this.$route.query.id,
        $axios: this.$axios.$request,
        router: this.$router
      })
    }

    this.initialize()
  },

  methods: {
    shouldDisplayTopic(topic) {
      // 判断题目中是否包含"频率"和"程度"关键词，如果包含则不显示
      if (topic.includes('频率') || topic.includes('程度')) {
        return false; // 不显示该题目
      } else {
        return true; // 显示该题目
      }
    },
    // initialize 中 需要共用的逻辑
    comlognet() {
      localStorage.setItem('className', '评分记录')
      // 详情显示
      this.grandDetails()
      // 关联评分 显示
      if (this.information && this.information.scores.length === 0) {
        this.associatedScore = false
      }
      // 判断传过来的分数是否是数字 来判断分数是否显示
      if (isNaN(this.information.scoreResult.slice(0, 1)) === true||this.information.scoreTitle === '帕金森病非运动症状评价量表(NMSS)') {
        this.scoreShow = false
      }
      if (isNaN(this.information.scoreResult.slice(-1)) === true) {
        if (this.information.scoreTitle == '改良Barthel指数评定量表' ||
          this.information.scoreTitle == '改良Ashworth分级评定法' ||
          this.information.scoreTitle == '机体反应水平分级(RLS)' ||
          this.information.scoreTitle == '运动功能评定法' ||
          this.information.scoreTitle === '功能独立性评定(FIM)' ||
          this.information.scoreTitle === 'Tinetti步态和平衡量表' ||
        this.information.scoreTitle === '功能活动问卷(FAQ)'||
          this.information.scoreTitle === '帕金森病生活质量评分量表(PDQ-39)'||
          this.information.scoreTitle === '统一帕金森病评定量表'||
          this.information.scoreTitle === 'Berg平衡评定法'||
          this.information.scoreTitle === '创伤指数(TI)'||
          this.information.scoreTitle === '日常生活活动能力量表(Barthel Index，BI)'||
          this.information.scoreTitle === '日常生活能力量表(ADL)'||
          this.information.scoreTitle === '创伤CRAMS评分法'||
          this.information.scoreTitle === 'ICH评分量表'||
          this.information.scoreTitle === 'Sunnybrook面神经评定量表'||
          this.information.scoreTitle === '脑动脉瘤破裂风险评分(PHASES评分)'||
          this.information.scoreTitle === '脑动脉瘤生长风险评分(ELAPSS评分)'||
          this.information.scoreTitle === '卒中后迟发性痫性发作的预测评分(SeLECT评分)'||
          this.information.scoreTitle === '颅内血管动静脉畸形(AVM) Spetsler-Martin分级'||
          this.information.scoreTitle === '颅内出血预后评分(max-ICH评分)'||
          this.information.scoreTitle === '高血压性脑出血1年再复发预测'||
          this.information.scoreTitle === '蛛网膜下腔出血后不良事件和功能结局预测(HATCH得分)'||
          this.information.scoreTitle === '蛛网膜下腔出血短期和长期结局评估(FRESH评分)'
          ) {
          this.lastscoreShow = true
        } else {
          this.lastscoreShow = false
        }
      }
      if (this.information.scoreTitle === '医院焦虑抑郁量表(HAD)') {
        this.scoreShow = true
      }
      // 根据评分名称，判断再次评分路径
      // 通用工具
      this.generalBoolTitle.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.generalName[index]}`
        }
      })
      // 缺血
      this.ischemia.map((item, index) => {
        if (this.information.scoreTitle === item) {
          // 对 颅内血管动静脉畸形(AVM) Spetsler-Martin分级 评分进行特殊处理 使其跳转至 Spetzler Martin分级 评分
          if (this.information.scoreTitle === '颅内血管动静脉畸形(AVM) Spetsler-Martin分级') {
            this.againScorePath = '/minitool/spetzler-martin';
            return;
          }
          this.againScorePath = `/minitool/${this.ischemiaName[index]}`
        }
      })
      // 卒中再发风险评分
      this.apoplexy.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.apoplexyName[index]}`
        }
      })
      // 动脉瘤
      this.artery.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.arterialAneurysmName[index]}`
        }
      })
      // 脑血管畸形
      this.malformation.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.cerebrovascularMalformation[index]}`
        }
      })
      // 步态分析
      this.walkNametion.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.walkName[index]}`
        }
      })
      // 帕金森
      this.parkinson.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.parkinsonName[index]}`
        }
      })
      // 其他
      this.other.map((item, index) => {
        if (this.information.scoreTitle === item) {
          this.againScorePath = `/minitool/${this.otherName[index]}`
        }
      })

      // 判断详情结果的 分 是否显示
      if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
        this.resultGrade = false
      }
      if (this.information.scoreTitle === 'ASPECTS评分'||this.information.scoreTitle === '帕金森病非运动症状评价量表(NMSS)') {
        this.lastscoreShow = false
      }
      if (
        this.information.scoreTitle === '深静脉血栓(DVT)Autar评估表' ||
        this.information.scoreTitle === 'MMSE简易精神状态检查量表' ||
        this.information.scoreTitle === '汉密顿抑郁量表(HAMD)' ||
        this.information.scoreTitle === '脑室出血的Graeb评分' ||
        this.information.scoreTitle === 'Brunnstrom偏瘫功能评价法' ||
        this.information.scoreTitle === '汉密顿焦虑量表(HAMA)' ||
        this.information.scoreTitle === '医院焦虑抑郁量表(HAD)' ||
        this.information.scoreTitle === '改良Barthel指数评定量表' ||
        this.information.scoreTitle === '日常生活活动能力量表(Barthel Index，BI)' ||
        this.information.scoreTitle === '日常生活能力量表(ADL)'||
        this.information.scoreTitle === '创伤CRAMS评分法'||
        this.information.scoreTitle === '改良Ashworth分级评定法' ||
        this.information.scoreTitle === '机体反应水平分级(RLS)' ||
        this.information.scoreTitle === '功能独立性评定(FIM)' ||
        this.information.scoreTitle === '功能活动问卷(FAQ)'||
        this.information.scoreTitle === 'Tinetti步态和平衡量表'||
        this.information.scoreTitle === '运动功能评定法'||
        this.information.scoreTitle === 'Berg平衡评定法'||
        this.information.scoreTitle === '帕金森病生活质量评分量表(PDQ-39)'||
        this.information.scoreTitle === '统一帕金森病评定量表'||
        this.information.scoreTitle === '创伤指数(TI)'||
        this.information.scoreTitle === 'ICH评分量表'||
        this.information.scoreTitle === 'Sunnybrook面神经评定量表'||
        this.information.scoreTitle === '脑动脉瘤破裂风险评分(PHASES评分)'||
        this.information.scoreTitle === '脑动脉瘤生长风险评分(ELAPSS评分)'||
        this.information.scoreTitle === '卒中后迟发性痫性发作的预测评分(SeLECT评分)'||
        this.information.scoreTitle === '颅内血管动静脉畸形(AVM) Spetsler-Martin分级'||
        this.information.scoreTitle === '颅内出血预后评分(max-ICH评分)'||
        this.information.scoreTitle === '高血压性脑出血1年再复发预测'||
        this.information.scoreTitle === '蛛网膜下腔出血后不良事件和功能结局预测(HATCH得分)'||
        this.information.scoreTitle === '蛛网膜下腔出血短期和长期结局评估(FRESH评分)'||
        this.information.scoreTitle === '健康调查简表SF36(生存质量简表)'
      ) {
        this.lastscoreShow = true
        this.dvtShow = false
      }
    },
    // mounted 里的数据拿到之后 执行的逻辑
    initialize() {
      if (this.$cookies.get('medtion_isLogged_only_sign') === undefined) {
        // 未登录状态 跳转到登录页面
        this.$router.push({
          name: 'signin',
          query: { fallbackUrl: this.$route.fullPath }
        })
      } else if (
        this.information.userId ===
        this.$cookies.get('medtion_user_only_sign').id
      ) {
        this.comlognet()
      } else {
        this.comlognet()
        let nameLength = this.information.name.length
        if (nameLength === 2) {
          this.information.name = `${this.information.name.substring(0, 1) + '*'
          }`
        } else if (nameLength > 2) {
          let str = ''
          for (let i = 0; i < nameLength - 2; i++) {
            str = str + '*'
          }
          this.information.name = `${this.information.name.substring(0, 1) +
          str +
          this.information.name.substring(nameLength - 1, nameLength)
          }`
        }
      }
    },
    // 评分详情
    grandDetails() {
      let allDetails = ['0', '1', '2', '3', '4', '5', '6']
      let title = []
      if (this.information) {
        switch (this.information.scoreTitle) {
          case '蒙特利尔认知评估':
            let topicOne = 0
            let topicTwo = 0
            let topicThree = 0
            let topicFour = 0
            let topicFive = 0
            let topicSix = 0
            let topicSeven = 0
            let bigtopic = [[], [], [], [], [], [], []]
            const splitHandler = this.information.scoreDetail
              .split('{')
              .slice(1, -1)
            // 蒙特利尔认知评估
            for (let i = 0; i < splitHandler.length + 1; i++) {
              let backdata = this.information.scoreDetail.split('}')[i].slice(1)
              switch (backdata.split('?')[1]) {
                case '一、 视空间与执行功能':
                case '一、视空间与执行功能':
                  bigtopic[0].push(1)
                  break
                case '二、命名':
                case '二、 命名':
                  bigtopic[1].push(1)
                  break
                case '四、注意':
                case '四、 注意':
                  bigtopic[2].push(1)
                  break
                case '五、语言':
                case '五、 语言':
                  bigtopic[3].push(1)
                  break
                case '六、抽象':
                case '六、 抽象':
                  bigtopic[4].push(1)
                  break
                case '七、延迟回忆':
                case '七、 延迟回忆':
                  bigtopic[5].push(1)
                  break
                default:
                  bigtopic[6].push(1)
                  break
              }
            }
            for (let i = 0; i < splitHandler.length + 1; i++) {
              let backdata = this.information.scoreDetail.split('}')[i].slice(1)
              let details = {
                topic: '',
                option: '',
                score: 0
              }
              switch (backdata.split('?')[1]) {
                case '一、 视空间与执行功能':
                case '一、视空间与执行功能':
                  topicOne += 1
                  this.score.scoreOneTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreOneTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[0].length === topicOne) {
                    allDetails.splice(0, 1, details)
                  }
                  break
                case '二、命名':
                case '二、 命名':
                  topicTwo += 1
                  this.score.scoreTwoTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreTwoTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[1].length === topicTwo) {
                    allDetails.splice(1, 1, details)
                  }
                  break
                case '四、注意':
                case '四、 注意':
                  topicThree += 1
                  this.score.scoreThreeTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreThreeTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[2].length === topicThree) {
                    allDetails.splice(2, 1, details)
                  }
                  break
                case '五、语言':
                case '五、 语言':
                  topicFour += 1
                  this.score.scoreFourTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreFourTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[3].length === topicFour) {
                    allDetails.splice(3, 1, details)
                  }
                  break
                case '六、抽象':
                case '六、 抽象':
                  topicFive += 1
                  this.score.scoreFiveTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreFiveTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[4].length === topicFive) {
                    allDetails.splice(4, 1, details)
                  }
                  break
                case '七、延迟回忆':
                case '七、 延迟回忆':
                  topicSix += 1
                  this.score.scoreSixTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreSixTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[5].length === topicSix) {
                    allDetails.splice(5, 1, details)
                  }
                  break
                case '八、定向':
                case '八、 定向':
                  topicSeven += 1
                  this.score.scoreSevenTopic.push(
                    backdata.split('?')[0].slice(-1) * 1
                  )
                  this.score.scoreSevenTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = backdata.split('?')[1]
                  details.option = ''
                  if (bigtopic[6].length === topicSeven) {
                    allDetails.splice(6, 1, details)
                  }
                  break
                default:
                  break
              }
            }
            let details = {
              topic: '三、 记忆（此环节不计分）',
              option: '',
              score: ''
            }
            allDetails.splice(2, 0, details)
            this.scoreContent = allDetails
            break
          case 'MMSE简易精神状态检查量表':
            allDetails = []
            for (
              let i = 0;
              i < this.information.scoreDetail.split('{').length - 1;
              i++
            ) {
              let details = {
                topic: '',
                option: '',
                score: 0
              }
              let field = this.information.scoreDetail
                .split('}')
                [i].slice(1)
                .split(':')
              switch (field[0]) {
                case '1.定向力':
                  this.score.scoreOneTopic.push(
                    field[1].split(',')[1].slice(-1) * 1
                  )
                  this.score.scoreOneTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = field[0]
                  details.option = ''
                  if (
                    this.information.scoreDetail
                      .split('}')
                      [i + 1].slice(1)
                      .split(':')[0] !== '1.定向力'
                  ) {
                    allDetails.push(details)
                  }
                  break
                case '2.记忆力':
                  this.score.scoreTwoTopic.push(
                    field[1].split(',')[1].slice(-1) * 1
                  )
                  this.score.scoreTwoTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = field[0]
                  details.option = ''
                  if (
                    this.information.scoreDetail
                      .split('}')
                      [i + 1].slice(1)
                      .split(':')[0] !== '2.记忆力'
                  ) {
                    allDetails.push(details)
                  }
                  break
                case '3.注意力和计算力':
                  this.score.scoreThreeTopic.push(
                    field[1].split(',')[1].slice(-1) * 1
                  )
                  this.score.scoreThreeTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = field[0]
                  details.option = ''
                  if (
                    this.information.scoreDetail
                      .split('}')
                      [i + 1].slice(1)
                      .split(':')[0] !== '3.注意力和计算力'
                  ) {
                    allDetails.push(details)
                  }
                  break
                case '4.回忆能力':
                  this.score.scoreFourTopic.push(
                    field[1].split(',')[1].slice(-1) * 1
                  )
                  this.score.scoreFourTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = field[0]
                  details.option = ''
                  if (
                    this.information.scoreDetail
                      .split('}')
                      [i + 1].slice(1)
                      .split(':')[0] !== '4.回忆能力'
                  ) {
                    allDetails.push(details)
                  }
                  break
                default:
                  this.score.scoreFiveTopic.push(
                    field[1].split(',')[1].slice(-1) * 1
                  )
                  this.score.scoreFiveTopic.map((item) => {
                    details.score += item
                  })
                  details.topic = field[0]
                  details.option = ''
                  if (
                    i ===
                    this.information.scoreDetail.split('{').length - 2
                  ) {
                    allDetails.push(details)
                  }
                  break
              }
            }
            this.scoreContent = allDetails
            break
          case 'ASPECTS评分':
            allDetails = []
            // ASPECTS评分
            for (
              let i = 0;
              i < this.information.scoreDetail.split('{').length - 1;
              i++
            ) {
              let details = {
                topic: '',
                option: '',
                score: 0
              }
              let field = this.information.scoreDetail
                .split('}')
                [i].slice(1)
                .split(':')
              if (title.indexOf(field[0]) === -1) {
                details.topic = field[0]
                title.push(field[0])
              } else {
                details.topic = ''
              }
              details.option = field[1].slice(0, field[1].indexOf(','))
              details.score = 0
              this.scoreContent.push(details)
            }
            break
          case 'Framingham风险评分':
            allDetails = []
            this.framing = false
            for (
              let i = 1;
              i < this.information.scoreDetail.split('{').length;
              i++
            ) {
              let details = {
                topic: '',
                option: '',
                score: 0
              }
              details.topic = this.information.scoreDetail
                .split('{')
                [i].slice(0, -1)
                .split(':')[0]
              details.option = this.information.scoreDetail
                .split('{')
                [i].slice(0, -1)
                .split(':')[1]
              details.score = ''
              this.scoreContent.push(details)
            }
            break
          case '弹簧圈栓塞密度计算':
            let arterialAneurysm = {
              topic: '',
              option: [],
              score: 0
            }
            for (
              let i = 0;
              i <
              JSON.parse(this.information.scoreDetail).aneurysm.shapes.length;
              i++
            ) {
              arterialAneurysm.topic = '动脉瘤体积：'
              arterialAneurysm.option.push(
                `长度(mm)：${JSON.parse(this.information.scoreDetail).aneurysm.shapes[i]
                  .depth
                }`
              )
              arterialAneurysm.option.push(
                `宽度(mm)：${JSON.parse(this.information.scoreDetail).aneurysm.shapes[i]
                  .width
                }`
              )
              arterialAneurysm.option.push(
                `高度(mm)：${JSON.parse(this.information.scoreDetail).aneurysm.shapes[i]
                  .height
                }`
              )
              if (
                i ===
                JSON.parse(this.information.scoreDetail).aneurysm.shapes
                  .length -
                1
              ) {
                arterialAneurysm.option.push(
                  `体积：${JSON.parse(this.information.scoreDetail).aneurysm.volume
                  }`
                )
              }
            }
            this.arterialAneurysmData.push(arterialAneurysm)
            // 弹簧圈体积
            let spring = {
              topic: '',
              option: [],
              score: 0
            }
            spring.topic = '弹簧圈体积：'
            for (
              let i = 0;
              i <
              JSON.parse(this.information.scoreDetail).springcoil.coils.length;
              i++
            ) {
              spring.option.push(`弹簧圈${i + 1}：`)
              spring.option.push(
                `品牌：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                  .brand
                }`
              )
              if (
                JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                  .brand !== '手动输入'
              ) {
                spring.option.push(
                  `产品：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .product
                  }`
                )
                if (
                  JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .series !== '请选择弹簧圈的系列'
                ) {
                  spring.option.push(
                    `系列：${JSON.parse(this.information.scoreDetail).springcoil.coils[
                      i
                      ].series
                    }`
                  )
                }

                spring.option.push(
                  `型号：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .model
                  }`
                )
                spring.option.push(
                  `直径(mm)：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .diameter
                  }`
                )
                spring.option.push(
                  `长度(cm)：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .length
                  }`
                )
              } else {
                spring.option.push(
                  `直径(mm)：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .diameter
                  }`
                )
                spring.option.push(
                  `长度(cm)：${JSON.parse(this.information.scoreDetail).springcoil.coils[i]
                    .length
                  }`
                )
              }
              if (
                i ===
                JSON.parse(this.information.scoreDetail).springcoil.coils
                  .length -
                1
              ) {
                spring.option.push(
                  `体积：${JSON.parse(this.information.scoreDetail).springcoil.volume
                  }`
                )
                spring.option.push(
                  `栓塞率：${JSON.parse(this.information.scoreDetail).springcoil
                    .embolization
                  }`
                )
              }
            }
            this.springData.push(spring)
            break
          case '功能独立性评定(FIM)':
            allDetails = []
            this.framing = true
            let length = this.information.scoreDetail.split('{').length
            let motorFunction = {
              topic: '一、运动功能',
              score: 0
            }
            let cognitiveFunction = {
              topic: '二、认知功能',
              score: 0
            }
            for (let i = 0; i < length - 1; i++) {
              let score = this.information.scoreDetail
                .split('}')[i].split('?')[0].slice(-1) * 1
              if (i <= 12) {
                motorFunction.score += score
                if (i == 12) {
                  this.scoreContent.push(motorFunction)
                }
              } else {
                cognitiveFunction.score += score
                if (i == 17) {
                  this.scoreContent.push(cognitiveFunction)
                }
              }
            }
            break
          case '运动功能评定法':
            allDetails = []
            this.framing = true
            let lengthSport = this.information.scoreDetail.split('{').length
            let motorFunctionSport = {
              topic: '一、上肢（坐位）',
              score: 0
            }
            let cognitiveFunctionSport = {
              topic: '二、下肢',
              score: 0
            }
            for (let i = 0; i < lengthSport - 1; i++) {
              let score = this.information.scoreDetail
                .split('}')[i].split('?')[0].slice(-1) * 1
              if (i <= 32) {
                motorFunctionSport.score += score
                if (i == 32) {
                  this.scoreContent.push(motorFunctionSport)
                }
              } else {
                cognitiveFunctionSport.score += score
                if (i == 49) {
                  this.scoreContent.push(cognitiveFunctionSport)
                }
              }
            }
            break
          case 'Tinetti步态和平衡量表':
            allDetails = []
            this.framing = true
            let lengthes = this.information.scoreDetail.split('{').length
            let motorFunctiones = {
              topic: '一、平衡测试',
              score: 0
            }
            let cognitiveFunctiones = {
              topic: '二、步态测试',
              score: 0
            }
            for (let i = 0; i < lengthes - 1; i++) {
              let score = this.information.scoreDetail
                .split('}')[i].split('?')[0].slice(-1) * 1
              if (i <= 8) {
                motorFunctiones.score += score
                if (i == 8) {
                  this.scoreContent.push(motorFunctiones)
                }
              } else {
                cognitiveFunctiones.score += score
                if (i == 18) {
                  this.scoreContent.push(cognitiveFunctiones)
                }
              }
            }
            break
          case '帕金森病非运动症状评价量表(NMSS)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
          break
          case '帕金森病睡眠量表(PDSS)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: ''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          // 子分数超过10分的情况
          case '日常生活能力量表(ADL)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(
                      0,
                      allMesage
                        .split('}')
                        [i].slice(1)
                        .split(':')[1]
                        .lastIndexOf(',')
                    )} :`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          // 子分数超过10分的情况
          case '帕金森病睡眠量表(PDSS)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(
                      0,
                      allMesage
                        .split('}')
                        [i].slice(1)
                        .split(':')[1]
                        .lastIndexOf(',')
                    )} :`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          case '日常生活活动能力量表(Barthel Index，BI)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(
                      0,
                      allMesage
                        .split('}')
                        [i].slice(1)
                        .split(':')[1]
                        .lastIndexOf(',')
                    )} :`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          case '蛛网膜下腔出血短期和长期结局评估(FRESH评分)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(
                      0,
                      allMesage
                        .split('}')
                        [i].slice(1)
                        .split(':')[1]
                        .lastIndexOf(',')
                    )} :`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 不管后面有几位数字
                details.score =allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1].split('分数')[1]
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          case '健康调查简表SF36(生存质量简表)':
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: '',
                title:''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
                details.title = allMesage.split('}')[i].slice(1).split(':')[0]
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(
                      0,
                      allMesage
                        .split('}')
                        [i].slice(1)
                        .split(':')[1]
                        .lastIndexOf(',')
                    )} :`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 不管后面有几位数字
                details.score =allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1].split('分数')[1]
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
          default:
            allDetails = []
            for (
              let i = 0;
              i <
              this.$store.state.minitool.information.scoreDetail.split('}')
                .length -
              1;
              i++
            ) {
              let allMesage = this.$store.state.minitool.information.scoreDetail
              let details = {
                topic: '',
                option: '',
                score: ''
              }
              if (
                title.indexOf(
                  allMesage.split('}')[i].slice(1).split(':')[0]
                ) === -1
              ) {
                details.topic = allMesage.split('}')[i].slice(1).split(':')[0]
                title.push(allMesage.split('}')[i].slice(1).split(':')[0])
              } else {
                details.topic = ''
              }
              // 结果不是数字(分数)
              if (isNaN(this.information.scoreResult) === true) {
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  // 没有分数的 选项后边不加 ：
                  if (this.topic.indexOf(this.information.scoreTitle) !== -1) {
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )}`
                  } else {
                    // 有分数的 选项后边加 ：
                    details.option = `${allMesage
                      .split('}')
                      [i].slice(1)
                      .split(':')[1]
                      .slice(
                        0,
                        allMesage
                          .split('}')
                          [i].slice(1)
                          .split(':')[1]
                          .lastIndexOf(',')
                      )} :`
                  }
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}`
                }
              } else {
                // 结果是数字
                if (
                  isNaN(
                    allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                  ) === true
                ) {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -4)}:`
                } else {
                  details.option = `${allMesage
                    .split('}')
                    [i].slice(1)
                    .split(':')[1]
                    .slice(0, -5)}:`
                }
              }
              // 分数是一位或两位时，分情况截取字符串
              if (
                isNaN(
                  allMesage.split('}')[i].slice(1).split(':')[1].slice(-2)
                ) === true
              ) {
                // 一位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-1)
              } else {
                // 两位
                details.score = allMesage
                  .split('}')
                  [i].slice(1)
                  .split(':')[1]
                  .slice(-2)
              }
              allDetails.push(details)
            }
            this.scoreContent = allDetails
            break
        }
      }
    },
    async relevance(e) {
      // 清空评分详情的数据
      this.scoreContent = []
      this.arterialAneurysmData = []
      this.springData = []
      // 清空 存放分数的数组
      this.score = {
        scoreOneTopic: [],
        scoreTwoTopic: [],
        scoreThreeTopic: [],
        scoreFourTopic: [],
        scoreFiveTopic: [],
        scoreSixTopic: [],
        scoreSevenTopic: []
      }
      await this.$store.dispatch('minitool/viewdetails', {
        id: e.target.attributes.id.value,
        $axios: this.$axios.$request,
        router: this.$router
      })
      this.grandDetails()
    },
    // 再次评分
    scoreAgain() {
      // 携带 患者信息
      localStorage.setItem('scoreAgain', true)
      // 关联 id
      if (this.information.relationScoreId === '') {
        this.relevanceId.id = this.information.id
      } else {
        this.relevanceId.id = this.information.relationScoreId
      }
      if (this.generalBoolTitle.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '通用工具')
      } else if (this.ischemia.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '缺血')
      } else if (this.apoplexy.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '卒中再发风险评分')
      } else if (this.artery.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '动脉瘤')
      } else if (this.walkNametion.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '步态分析法')
      } else if (
        this.malformation.indexOf(this.information.scoreTitle) !== -1
      ) {
        localStorage.setItem('className', '脑血管畸形')
      } else if (this.other.indexOf(this.information.scoreTitle) !== -1) {
        localStorage.setItem('className', '其他')
      }
      localStorage.setItem('wordLast', this.information.scoreTitle)
      this.$store.commit('minitool/setRightnav', true)
      localStorage.setItem('openeds', 8)
      this.$store.commit('minitool/setIsOpenedShow', false)
    },

    /**
     * 分享按钮弹框打开
     */
    shareOpenFun(data) {
      this.$analysys.btn_click('分享', document.title)
      this.shareFlag = data
    }
  }
}
</script>

<style lang='less' scoped>
.bigBox {
  margin-top: 20px;
  width: 894px;

  .headline {
    width: 100%;
    background: #fbfbfb;
    border-radius: 6px 6px 0px 0px;

    P {
      font-family: 'Microsoft YaHei';
      font-weight: 700;
      font-size: 20px;
      color: #333333;
      line-height: 50px;
      margin-left: 16px;
    }
  }

  .type {
    width: 894px;
    display: flex;
    margin-top: 20px;

    .typeLift {
      width: 670px;
      display: flex;
      padding-left: 26px;

      p {
        white-space: nowrap;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
      }

      span:nth-of-type(1) {
        margin-right: 55px;
        max-width: 262px;
      }

      span {
        //white-space: nowrap;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
      }
    }

    .typeRight {
      margin-top: 2px;
      width: 300px;
      margin-left: 20px;

      .btnOne {
        width: 120px;
        height: 48px;
        background: #0581ce;
        border-radius: 6px;
        color: #ffffff;
        font-weight: 400;
        font-size: 16px;
        border: none;
        cursor: pointer;
      }

      .btnTwo {
        width: 120px;
        height: 48px;
        background: #0eb2f8;
        border-radius: 6px;
        color: #ffffff;
        border: none;
        margin-left: 16px;
        font-weight: 400;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }

  .patientinformation {
    width: 100%;
    padding: 0 16px;

    .headlines {
      width: 412px;
      height: 31px;
      line-height: 31px;
      font-family: 'Microsoft YaHei';

      p {
        font-weight: 700;
        color: #333333;
      }

      .filament {
        //width: 412px;
        //height: 1px;
        //background: rgba(5, 129, 206, 0.2);
        //
        //.pachytene {
        //  width: 15px;
        //  height: 2px;
        //  background: #0581ce;
        //}
      }
    }

    .content {
      width: 445px;
      margin-top: 20px;
      margin-left: 16px;

      .top {
        display: flex;

        p {
          font-weight: 400;
          font-size: 16px;
          color: #666666;
        }

        span {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }

        span:nth-of-type(1),
        span:nth-of-type(2) {
          margin-right: 60px;
        }
      }

      .bottom {
        display: flex;
        margin-top: 12px;

        p {
          font-weight: 400;
          font-size: 16px;
          width: 60px;
          color: #666666;
        }

        span {
          width: 480px;
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .scoringdetails {
    width: 894px;
    margin-top: 30px;
    padding: 0 16px;
    margin-bottom:40px;
    .headlines {
      width: 412px;
      height: 31px;
      line-height: 31px;
      font-family: 'Microsoft YaHei';

      p {
        font-weight: 700;
        color: #333333;
      }

      .filament {
        //width: 412px;
        //height: 1px;
        //background: rgba(5, 129, 206, 0.2);
        //
        //.pachytene {
        //  width: 15px;
        //  height: 2px;
        //  background: #0581ce;
        //}
      }
    }

    .content {
      margin-top: 20px;

      .btn {
        padding: 0 10px;
        background: #0581ce;
        border-radius: 6px;
        text-align: center;
        line-height: 29px;
        font-weight: 700;
        font-size: 16px;
        color: #ffffff;
        border: none;
      }

      ul {
        margin-top: 16px;

        li {
          margin-bottom: 24px;

          p {
            font-weight: 700;
            font-size: 16px;
            color: #333333;
            margin-bottom: 12px;
            margin-right: 50px;

            span {
              font-weight: 700;
              font-size: 20px;
              color: #333333;

              span {
                font-weight: 700;
                font-size: 16px !important;
                color: #888888 !important;
              }
            }
          }

          .alone {
            p {
              padding-left: 10px;
              font-weight: 400;
              font-size: 16px;
              color: #404040;
              margin-bottom: 12px;

              span:nth-of-type(1) {
                margin-left: 10px;
                color: #202020;
              }
            }
          }
        }
      }

      p {
        font-weight: 700;
        font-size: 20px;
        color: #666666;

        span:nth-of-type(1) {
          font-weight: 700;
          font-size: 20px;
          color: #333333;
        }

        span:nth-of-type(2) {
          font-weight: 700;
          font-size: 16px;
          color: #888888;
        }
      }
    }
  }

  .associatedscore {
    width: 100%;
    padding: 0 16px;
    margin-top: 30px;

    .headlines {
      width: 412px;
      height: 31px;
      line-height: 31px;
      font-family: 'Microsoft YaHei';

      p {
        font-weight: 700;
        color: #333333;
      }

      .filament {
        //width: 412px;
        //height: 1px;
        //background: rgba(5, 129, 206, 0.2);
        //
        //.pachytene {
        //  width: 15px;
        //  height: 2px;
        //  background: #0581ce;
        //}
      }
    }

    .score {
      display: flex;
      width: 724px;
      margin: 20px 10px;
      flex-wrap: wrap;

      div {
        width: 168px;
        height: 41px;
        background: rgba(5, 129, 206, 0.05);
        border-radius: 6px;
        text-align: center;
        line-height: 41px;
        cursor: pointer;
        margin-right: 13px;
        margin-bottom: 16px;
      }
    }
  }
}

.filament{
  width: 412px;
  height: 1px;
  background: rgba(5, 129, 206, .2);
}

.pachytene{
  width: 15px;
  height: 2px;
  background: #0581ce;
}

</style>
