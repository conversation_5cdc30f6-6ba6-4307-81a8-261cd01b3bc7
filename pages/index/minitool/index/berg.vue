<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in faq" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Berg平衡评定法 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Berg平衡评定法是一种广泛应用于评估平衡功能的工具。该评定法包括14个项目，涵盖了平衡能力的不同方面。这些项目包括站立、转身、单脚站立以及身体各部分的协调动作。通过对患者的观察和测试，可以评估出他们的平衡能力和稳定性。每个项目都有不同的难度和标准分值，根据患者的表现进行打分。最终的总分可以反映患者的平衡能力。Berg平衡评定法可以提供可靠的评估结果，帮助医生识别患者的平衡问题，了解他们的跌倒风险，并制定相应的康复计划。通过使用Berg平衡评定法，可以帮助改善患者的平衡功能，提高其生活质量和日常活动能力。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'Berg Balance Scale,Berg平衡能力评定量表,Berg步态和平衡评定量表,Berg平衡测量工具,Berg平衡指数,Berg平衡分析量表,Berg平衡评估工具,Berg平衡功能量表,Berg步态和平衡测试,Berg平衡能力测量,Berg平衡功能评估'
        }
      ]
    }
  },
  data () {
    return {
      // 危险因素
      faq: [
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '1. 由站到坐',
          id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
          content: [
            '1) 能不用手支撑站起并站稳：4分;',
            '2) 能独自用手支撑站起并站稳：3分;',
            '3) 能在尝试几次之后用手支撑站起来并站稳：2分;',
            '4) 需要轻微帮助下才可站起或站稳：1分;',
            '5) 需要中度或大量的帮助才能站起：0分;'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能不用手支撑站起并站稳;', '能独自用手支撑站起并站稳;', '能在尝试几次之后用手支撑站起来并站稳;', '需要轻微帮助下才可站起或站稳;', '需要中度或大量的帮助才能站起;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '2. 独立站立',
          id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
          content: [
            '1) 能安全地站2分钟：4分;',
            '2) 需在监护下才能站2分钟：3分;',
            '3) 不需要支撑能站30秒：2分;',
            '4) 尝试几次后才能在不需要支撑能站30秒：1分;',
            '5) 无法在没有帮助下站30秒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能安全地站2分钟;', '需在监护下才能站2分钟;', '不需要支撑能站30秒;', '尝试几次后才能在不需要支撑能站30秒;', '无法在没有帮助下站30秒;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '3. 独立坐',
          id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
          content: [
            '1) 能安稳且安全地坐2分钟：4分;',
            '2) 在监督下能坐2分钟：3分;',
            '3) 能坐30秒：2分;',
            '4) 能坐10秒：1分;',
            '5) 无法在没有支撑下坐10秒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能安稳且安全地坐2分钟;', '在监督下能坐2分钟;', '能坐30秒;', '能坐10秒;', '无法在没有支撑下坐10秒;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '4. 由站到坐',
          id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
          content: [
            '1) 用手稍微帮忙即可安全坐下：4分;',
            '2) 需要用手帮忙来控制坐下：3分;',
            '3) 需要用双腿后侧抵住椅子来控制坐下：2分;',
            '4) 能独立坐到椅子上但不能控制身体的下降：1分;',
            '5) 需要帮助才能做下：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['用手稍微帮忙即可安全坐下;', '需要用手帮忙来控制坐下;', '需要用双腿后侧抵住椅子来控制坐下;', '能独立坐到椅子上但不能控制身体的下降;', '需要帮助才能做下;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '5. 床椅转移',
          id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
          content: [
            '1) 用手稍微帮忙即可安全转移：4分;',
            '2) 必须用手帮忙才能安全转移：3分;',
            '3) 需要言语提示或监护才能完成转移：2分;',
            '4) 需要一个人帮助才能完成转移：1分;',
            '5) 需要两个人帮忙或监护才能完成转移：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['用手稍微帮忙即可安全转移;', '必须用手帮忙才能安全转移;', '需要言语提示或监护才能完成转移;', '需要一个人帮助才能完成转移;', '需要两个人帮忙或监护才能完成转移;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '6. 闭眼站立',
          id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
          content: [
            '1) 能安全地站立10秒：4分;',
            '2) 能在监护下站立10秒：3分;',
            '3) 能站立3秒：2分;',
            '4) 不能站3秒但睁眼后可以保持平衡：1分;',
            '5) 闭眼站立需要帮助以避免摔倒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能安全地站立10秒;', '能在监护下站立10秒;', '能站立3秒;', '不能站3秒但睁眼后可以保持平衡;', '闭眼站立需要帮助以避免摔倒;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '7. 双足并拢站立',
          id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
          content: [
            '1) 能独立、安全地双足并拢站立1分钟：4分;',
            '2) 需在监护下才能双足并拢独立站1分钟：3分;',
            '3) 能双足并拢独立站立但不能站30秒：2分;',
            '4) 需要帮助才能将双脚并找但并拢后能站15秒：1分;',
            '5) 需要帮助才能将双脚并找但并拢后不能站15秒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能独立、安全地双足并拢站立1分钟;', '需在监护下才能双足并拢独立站1分钟;', '能双足并拢独立站立但不能站30秒;', '需要帮助才能将双脚并找但并拢后能站15秒;', '需要帮助才能将双脚并找但并拢后不能站15秒;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '8. 站立位上肢前伸',
          id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
          content: [
            '1) 能安心地前伸25cm的距离：4分;',
            '2) 能前伸12cm的距离：3分;',
            '3) 能前伸5cm的距离：2分;',
            '4) 能前伸但需要监护：1分;',
            '5) 尝试前伸即失去平衡或需要外部帮助才能前伸：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能安心地前伸25cm的距离;', '能前伸12cm的距离;', '能前伸5cm的距离;', '能前伸但需要监护;', '尝试前伸即失去平衡或需要外部帮助才能前伸;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '9. 站立位从地上拾物',
          id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
          content: [
            '1) 能安全而轻易的捡起拖鞋：4分;',
            '2) 需要在监护下捡起拖鞋：3分;',
            '3) 不能捡起但能够到达距离拖鞋2-5cm的位置并且独立保持平衡：2分;',
            '4) 不能捡起并且当试图尝试时需要监护：1分;',
            '5) 不能尝试或需要帮助以避免失去平衡或跌倒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能安全而轻易的捡起拖鞋;', '需要在监护下捡起拖鞋;', '不能捡起但能够到达距离拖鞋2-5cm的位置并且独立保持平衡;', '不能捡起并且当试图尝试时需要监护;', '不能尝试或需要帮助以避免失去平衡或跌倒;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '10. 转身向后看',
          id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
          content: [
            '1) 能从两侧向后看且重心转移良好：0分;',
            '2) 只能从一侧向后看，另一侧重心转移较差：1分;',
            '3) 能前伸5cm的距离只能向侧方转身但能够保持平衡：3分;',
            '4) 当转身时需要监护：4分;',
            '5) 需要帮助以避免失去平衡或跌倒：5分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能从两侧向后看且重心转移良好;', '只能从一侧向后看，另一侧重心转移较差;', '能前伸5cm的距离只能向侧方转身但能够保持平衡;', '当转身时需要监护;', '需要帮助以避免失去平衡或跌倒;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '11. 转身一周',
          id: ['11-1', '11-2', '11-3', '11-4', '11-5'],
          content: [
            '1) 能从两个方向用≤4秒的时间安全的转一圈：4分;',
            '2) 只能在一个方向用≤4秒的时间安全的转一圈：3分;',
            '3) 能安全的转一圈但用时超过4秒：2分;',
            '4) 转身时需要密切监护或言语提示：1分;',
            '5) 转身时需要帮助：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能从两个方向用≤4秒的时间安全的转一圈;', '只能在一个方向用≤4秒的时间安全的转一圈;', '能安全的转一圈但用时超过4秒;', '转身时需要密切监护或言语提示;', '转身时需要帮助;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '12. 双足交替踏台阶',
          id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
          content: [
            '1) 能独立而安全的站立并20秒内完成8个动作：4分;',
            '2) 能独立站立但完成8个动作的时问超过20秒：3分;',
            '3) 在监护下不需要帮助能完成4个动作：2分;',
            '4) 需要较小帮助能完成2个或2个以上的动作：1分;',
            '5) 需要帮助以避免跌倒或不能尝试此项活动：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能独立而安全的站立并20秒内完成8个动作;', '能独立站立但完成8个动作的时问超过20秒;', '在监护下不需要帮助能完成4个动作;', '需要较小帮助能完成2个或2个以上的动作;', '需要帮助以避免跌倒或不能尝试此项活动;']
        }, {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 519px;',
          title: '13. 双足前后站立(如果不行，就尽量跨远，这样，前脚跟就在后脚足趾之前)',
          id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
          content: [
            '1) 能够独立的将一只脚放在另一只脚的正前方且保持30秒：4分;',
            '2) 能够独立的将一只脚放在另一只脚的前方且保持30秒：3分;',
            '3) 能够独立的将一只脚向前迈一小步且能够保持30秒：2分;',
            '4) 需要帮助才能向前迈步但能保持15秒：1分;',
            '5) 当迈步或站立时失去平衡：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能够独立的将一只脚放在另一只脚的正前方且保持30秒;', '能够独立的将一只脚放在另一只脚的前方且保持30秒;', '能够独立的将一只脚向前迈一小步且能够保持30秒;', '需要帮助才能向前迈步但能保持15秒;', '当迈步或站立时失去平衡;']
        }, {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 519px;',
          title: '14. 单腿站立)',
          id: ['14-1', '14-2', '14-3', '14-4', '14-5'],
          content: [
            '1) 能够独立抬起一条腿且保持10秒以上：4分;',
            '2) 能够独立抬起一条腿且保持5-10秒：3分;',
            '3) 能够独立抬起一条腿且保持3-5秒：2分;',
            '4) 经过努力能够抬起一条腿，保持时间不足3秒但能够保持独立站立：1分;',
            '5) 不能够尝试此项活动或需要帮助以避免跌倒：0分。'
          ],
          grade: ['4', '3', '2', '1', '0'],
          simpleContent: ['能够独立抬起一条腿且保持10秒以上;', '能够独立抬起一条腿且保持5-10秒;', '能够独立抬起一条腿且保持5-10秒;', '经过努力能够抬起一条腿，保持时间不足3秒但能够保持独立站立;', '不能够尝试此项活动或需要帮助以避免跌倒;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 14,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 最终结果(根据分数显示不同)
    finalScore () {
      if (this.totalPoints >= 0&&this.totalPoints<=20) {
        return `${this.totalPoints}分，须用轮椅，高危摔倒风险`
      }else if(this.totalPoints >= 21&&this.totalPoints<=40) {
        return `${this.totalPoints}分，辅下步行，中度摔倒风险 。`
      }else if(this.totalPoints >= 41&&this.totalPoints<=56){
        return `${this.totalPoints}分，完全独立，低危摔倒风险。`
      }

    }
  },

  beforeMount () {
    localStorage.setItem('className', '步态分析法')
    localStorage.setItem('wordLast', 'Berg平衡评定法')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 14
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.finalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
