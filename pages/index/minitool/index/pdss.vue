<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="consciousness"></Options>
          <Options :information="comprehend"></Options>
          <Options :information="language"></Options>
          <Options :information="view"></Options>
          <Options :information="gaze"></Options>
          <Options :information="underpart"></Options>
          <Options :information="upperLimbF"></Options>
          <Options :information="upperLimbN"></Options>
          <Options :information="StretchWrist"></Options>
          <Options :information="FingerStrength"></Options>
          <Options :information="lowerLimbs"></Options>
          <Options :information="lyingCurl"></Options>
          <Options :information="FootDorsiflexion"></Options>
          <Options :information="gait"></Options>
          <Options :information="sleep"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ explain }}</p>
            </div>
          </div>
        </div>

        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import { keepScore } from '@/api/minitool'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '帕金森病睡眠量表(PDSS) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '帕金森病睡眠量表（PDSS）是一种专门评估帕金森病患者睡眠障碍的量化工具，通过一系列问题衡量包括入睡困难、夜间觉醒次数、日间过度嗜睡等多维度睡眠质量。患者根据自身实际情况打分，帮助医生了解病情对睡眠的具体影响，并据此制定个性化治疗方案，改善患者生活质量。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '帕金森睡眠评估表,PD睡眠评分表,PDSS'
        }
      ]
    }
  },
  data () {
    return {
      // 睡眠质量
      consciousness: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '1. 您感觉总的睡眠质量如何？',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6'],
        content: [
         '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 理解能力（不可以示范）
      comprehend: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '2. 您每晚是否感觉入睡困难？',
        id: ['2-1', '2-2', '2-3'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 语言（与患者进行简单对话）
      language: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '3. 您是否感觉维持睡眠困难（容易在睡眠中醒来）？',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 视野
      view: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '4. 您是否因腿或手臂的不安而干扰正常睡眠？',
        id: ['4-1', '4-2'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 凝视
      gaze: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '5. 您睡觉时是否在床上有烦乱不安的感觉？ ',
        id: ['5-1', '5-2', '5-3', '5-4'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 下部面肌运动
      underpart: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '6. 您是否在夜间有令您烦恼的梦？ ',
        id: ['6-1', '6-2', '6-3'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 上肢保持上抬45°
      upperLimbF: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '7. 您是否在夜间有令您烦恼的幻觉（是否好像看到或听到不存在的事物）？',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 上肢保持上抬90°
      upperLimbN: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '8. 您是否起夜小便？',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 伸腕
      StretchWrist: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '9. 您是否因运动困难无法及时起夜小便而导致小便不能自制（尿失禁）？',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 手指力量
      FingerStrength: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '10. 您是否因手臂或腿的麻木感而从睡眠中醒来？',
        id: ['10-1', '10-2', '10-3'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 下肢活动
      lowerLimbs: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '11. 您是否在夜间睡觉时手臂或腿部肌肉有疼痛的抽筋现象？',
        id: ['11-1', '11-2', '11-3', '11-4'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 屈髋和屈膝
      lyingCurl: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '12. 您是否在早晨醒来时感觉手臂或腿部因姿势摆放问题感到疼痛？',
        id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 足背屈
      FootDorsiflexion: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '13. 在您尚未入睡时，您有震颤的感觉吗？',
        id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 步态
      gait: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 825px;',
        title: '14. 在您早晨醒来时，是否感觉到疲劳、嗜睡？',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5', '14-6'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 嗜睡
      sleep: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 825px;',
        title: '15. 在白天，您是否无意（不经意）间入睡？',
        id: ['15-1'],
        content: [
          '','','','','','','','','','',''
        ],
        grade: ['0', '1', '2', '3', '4', '5', '6','7','8','9','10'],
        simpleContent: [
          '','','','','','','','','','',''
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选个数
      unselected: 15,
      choiceBox: [],
      explain:
        '由15个问题组成，每一项评分均为0~10分，0分表示症状严重且持久，10分表示无症状。PDSS-1调查主观夜间总体睡眠质量，PDSS-2了解入睡困难，PDSS-3了解睡眠维持障碍（如片段睡眠的情况），PDSS-4调查周期性肢体活动障碍（Periodic Limb Movement Disorder，PLMD），PDSS-5了解不宁腿综合征（Restless Leg Syndrome，RLS），PDSS-6和PDSS-7用于评定夜间精神状况，PDSS-8和PDSS-9用来了解夜尿情况，PDSS-10至PDSS-13反映夜间运动情况，PDSS-14调查睡眠后精神恢复情况，PDSS-15可了解日间过度嗜睡（Excessive Daytime Sleepiness，EDS）情况。\nPDSS总分＜90分或条目分＜6分，特别是条目1、14、15，即应积极治疗，因为睡眠障碍的治疗在PD治疗中起重要作用，它对改善PD患者整体生存质量非常关键。',
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', '帕金森病睡眠量表(PDSS)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 15
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 874px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

</style>
