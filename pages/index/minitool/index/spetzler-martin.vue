<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="volume"></Options>
          <Options :information="brainTissue"></Options>
          <Options :information="venousReturn"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ explain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: 'Spetzler Martin分级 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'Spetzler-Martin分级量表通过评估AVM大小、静脉引流模式和是否位于重要脑功能区来评估AVM患者开放神经外科手术的风险，分为VI级。l级动静脉畸形被认为是小的、浅表的、位于非功能区的大脑中，手术风险低。IV级或5级动静脉畸形大、深、位于或邻近大脑重要功能区。VI级AVM被视为不可手术，指AVM位于或邻近下丘脑、脑干等。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑动静脉畸形, 颅内出血风险, 血流相关性动脉瘤, Spetzler Martin, 动静脉畸形, AVM, 颅内动静脉畸形患者, 低级别未破裂动静脉畸形, 未破裂动静脉畸形, 神经功能缺失, 动静脉畸形, 深部供血'
        }
      ]
    }
  },
  data () {
    return {
      // 体积
      volume: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '1. 体积',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '1) 小（<3cm）：1分;',
          '2) 中（3-6cm）：2分;',
          '3) 大（>6cm）：3分。',
        ],
        grade: ['1', '2', '3'],
        simpleContent: ['小（<3cm）;', '中（3-6cm）;', '大（>6cm）;'],
      },
      // 是否邻近脑组织是否重要功能区
      brainTissue: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '2. 是否邻近脑组织是否重要功能区',
        id: ['2-1', '2-2'],
        content: ['1) 否：0分;', '2) 是：1分。'],
        grade: ['0', '1'],
        simpleContent: ['否;', '是;'],
      },
      // 静脉回流类型
      venousReturn: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title: '3. 静脉回流类型',
        id: ['3-1', '3-2'],
        content: ['1) 仅有脑表面静脉：0分;', '2) 有深部静脉：1分。'],
        grade: ['0', '1'],
        simpleContent: ['仅有脑表面静脉;', '有深部静脉;'],
      },
      explain:
        '评分（5分）=体积（3分）+功能区（1分）+静脉回流（1分）\n*另外有独立的第6级，指无法手术的病变(切除不可避免地造成残疾性损害或死亡)\n*体积指在未放大的血管造影片上病变的最大直径。(和影响AVM切除难度的因素相关。如：供血动脉、盗血程度等)\n*重要功能区指感觉运动、语言和视觉皮层，下丘脑和丘脑、内囊、脑干、小脑脚、小脑深部神经核。',
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 3,
      // 总分数
      totalPoints: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '脑血管畸形')
    localStorage.setItem('wordLast', 'Spetzler Martin分级')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 3
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 450px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
