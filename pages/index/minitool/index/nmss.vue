<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="referenceOne" class="oneTopic">
            <sheetChangeSign :scores="score" :information="oneTopicA" ></sheetChangeSign>
            <sheetChangeSign v-show="oneTopicADegree.shouldHideComponent" :scores="score" :information="oneTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="oneTopicAFrequency.shouldHideComponent" :scores="score" :information="oneTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :scores="score" :information="oneTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="oneTopicBDegree.shouldHideComponent" :scores="score" :information="oneTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="oneTopicBFrequency.shouldHideComponent" :scores="score" :information="oneTopicBFrequency"></sheetChangeSign>
          </div>
          <div ref="referenceTwo" class="twoTopic">
            <sheetChangeSign :information="twoTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicADegree.shouldHideComponent" :information="twoTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicAFrequency.shouldHideComponent" :information="twoTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="twoTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicBDegree.shouldHideComponent" :information="twoTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicBFrequency.shouldHideComponent" :information="twoTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="twoTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicCDegree.shouldHideComponent" :information="twoTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicCFrequency.shouldHideComponent" :information="twoTopicCFrequency"></sheetChangeSign>
            <sheetChangeSign :information="twoTopicD"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicDDegree.shouldHideComponent" :information="twoTopicDDegree"></sheetChangeSign>
            <sheetChangeSign v-show="twoTopicDFrequency.shouldHideComponent" :information="twoTopicDFrequency"></sheetChangeSign>
          </div>
          <div ref="referenceThree" class="threeTopic">
            <sheetChangeSign :information="threeTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicADegree.shouldHideComponent" :information="threeTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicAFrequency.shouldHideComponent" :information="threeTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="threeTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicBDegree.shouldHideComponent" :information="threeTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicBFrequency.shouldHideComponent" :information="threeTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="threeTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicCDegree.shouldHideComponent" :information="threeTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicCFrequency.shouldHideComponent" :information="threeTopicCFrequency"></sheetChangeSign>
            <sheetChangeSign :information="threeTopicD"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicDDegree.shouldHideComponent" :information="threeTopicDDegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicDFrequency.shouldHideComponent" :information="threeTopicDFrequency"></sheetChangeSign>
            <sheetChangeSign :information="threeTopicE"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicEDegree.shouldHideComponent" :information="threeTopicEDegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicEFrequency.shouldHideComponent" :information="threeTopicEFrequency"></sheetChangeSign>
            <sheetChangeSign :information="threeTopicF"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicFDegree.shouldHideComponent" :information="threeTopicFDegree"></sheetChangeSign>
            <sheetChangeSign v-show="threeTopicFFrequency.shouldHideComponent" :information="threeTopicFFrequency"></sheetChangeSign>
          </div>
          <div ref="referenceFour" class="fourTopic">
            <sheetChangeSign :information="fourTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicADegree.shouldHideComponent" :information="fourTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicAFrequency.shouldHideComponent" :information="fourTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="fourTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicBDegree.shouldHideComponent" :information="fourTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicBFrequency.shouldHideComponent" :information="fourTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="fourTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicCDegree.shouldHideComponent" :information="fourTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="fourTopicCFrequency.shouldHideComponent" :information="fourTopicCFrequency"></sheetChangeSign>
          </div>

          <div ref="referenceFive" class="fiveTopic">
            <sheetChangeSign :information="fiveTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicADegree.shouldHideComponent" :information="fiveTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicAFrequency.shouldHideComponent" :information="fiveTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="fiveTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicBDegree.shouldHideComponent" :information="fiveTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicBFrequency.shouldHideComponent" :information="fiveTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="fiveTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicCDegree.shouldHideComponent" :information="fiveTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="fiveTopicCFrequency.shouldHideComponent" :information="fiveTopicCFrequency"></sheetChangeSign>
          </div>

          <div ref="referenceSix" class="sixTopic">
            <sheetChangeSign :information="sixTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicADegree.shouldHideComponent" :information="sixTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicAFrequency.shouldHideComponent" :information="sixTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="sixTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicBDegree.shouldHideComponent" :information="sixTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicBFrequency.shouldHideComponent" :information="sixTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="sixTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicCDegree.shouldHideComponent" :information="sixTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="sixTopicCFrequency.shouldHideComponent" :information="sixTopicCFrequency"></sheetChangeSign>
          </div>

          <div ref="referenceSeven" class="sevenTopic">
            <sheetChangeSign :information="sevenTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicADegree.shouldHideComponent" :information="sevenTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicAFrequency.shouldHideComponent" :information="sevenTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="sevenTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicBDegree.shouldHideComponent" :information="sevenTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicBFrequency.shouldHideComponent" :information="sevenTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="sevenTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicCDegree.shouldHideComponent" :information="sevenTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="sevenTopicCFrequency.shouldHideComponent" :information="sevenTopicCFrequency"></sheetChangeSign>
          </div>

          <div ref="referenceEight" class="eightTopic">
            <sheetChangeSign :information="eightTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="eightTopicADegree.shouldHideComponent" :information="eightTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="eightTopicAFrequency.shouldHideComponent" :information="eightTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="eightTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="eightTopicBDegree.shouldHideComponent" :information="eightTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="eightTopicBFrequency.shouldHideComponent" :information="eightTopicBFrequency"></sheetChangeSign>
          </div>

          <div ref="referenceNine" class="nineTopic">
            <sheetChangeSign :information="nineTopicA"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicADegree.shouldHideComponent" :information="nineTopicADegree"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicAFrequency.shouldHideComponent" :information="nineTopicAFrequency"></sheetChangeSign>
            <sheetChangeSign :information="nineTopicB"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicBDegree.shouldHideComponent" :information="nineTopicBDegree"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicBFrequency.shouldHideComponent" :information="nineTopicBFrequency"></sheetChangeSign>
            <sheetChangeSign :information="nineTopicC"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicCDegree.shouldHideComponent" :information="nineTopicCDegree"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicCFrequency.shouldHideComponent" :information="nineTopicCFrequency"></sheetChangeSign>
            <sheetChangeSign :information="nineTopicD"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicDDegree.shouldHideComponent" :information="nineTopicDDegree"></sheetChangeSign>
            <sheetChangeSign v-show="nineTopicDFrequency.shouldHideComponent" :information="nineTopicDFrequency"></sheetChangeSign>
          </div>

        </div>
        <div class="diseaseType">
          <div class="disease">{{cardiovascular.name}}<span>{{cardiovascular.result}}</span></div>
          <div class="disease">{{fatigued.name}}<span>{{fatigued.result}}</span></div>
          <div class="disease">{{emotion.name}}<span>{{emotion.result}}</span></div>
          <div class="disease">{{perceptual.name}}<span>{{perceptual.result}}</span></div>
          <div class="disease">{{attention.name}}<span>{{attention.result}}</span></div>
          <div class="disease">{{gastrointestinal.name}}<span>{{gastrointestinal.result}}</span></div>
          <div class="disease">{{mition.name}}<span>{{mition.result}}</span></div>
          <div class="disease">{{sexuality.name}}<span>{{sexuality.result}}</span></div>
          <div class="disease">{{Sexualmance.name}} <span>{{Sexualmance.result}}</span></div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span></span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import sheetChangeSign from '@/components/MiniTool/sheetChangeSign.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'

export default {
  // 注册组件
  components: { Patient, sheetChangeSign, ScoringDetails, MultipleChoice },
  // tdk
  head () {
    return {
      title: '帕金森病非运动症状评价量表(NMSS) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '帕金森病非运动症状评价量表（Non-Motor Symptoms Scale, NMSS）是一种专门用于评估帕金森病患者非运动症状的工具。帕金森病除了典型的运动障碍（如震颤、僵直和运动迟缓等）外，还伴有多种非运动症状，这些症状往往对患者的生活质量造成重大影响，但可能不如运动症状那样明显或容易识别。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'NMSS量表,PD非运动症状量表,非运动症状综合评价量表,帕金森病非运动症状问卷,NMSS,帕金森病非运动症状评估表'
        }
      ]
    }
  },
  data () {
    return {
      cardiovascular:{
        name:'心血管:',
        result:"0,0,0"
      },

      cardiovascularList: [],
      fatigued:{
        name:'睡眠/疲劳:',
        result:"0,0,0"
      },

      fatiguedList: [],

      emotion:{
        name:'情绪/认知:',
        result:"0,0,0"
      },

      emotionList: [],

      perceptual:{
        name:'感知障障碍:',
        result:"0,0,0"
      },

      perceptualList:[],

      attention:{
        name:'注意力/记忆:',
        result:"0,0,0"
      },

      attentionList:[],

      gastrointestinal:{
        name:'胃肠道症状:',
        result:"0,0,0"
      },

      gastrointestinalList:[],

      mition:{
        name:'泌尿系统症状:',
        result:"0,0,0"
      },

      mitionList:[],

      sexuality:{
        name:'性功能:',
        result:"0,0,0"
      },

      sexualityList:[],

      Sexualmance:{
        name:'性功能混合症状:',
        result:"0,0,0"
      },

      SexualmanceList:[],

      totalScore:[],

      oneTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 从躺着或坐着到站着时，觉得轻度头痛、头晕或乏力',
        id: ['1-1', '1-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'1',
        spreadhead: '1. 从躺着或坐着到站着时，觉得轻度头痛、头晕或乏力',
      },
      oneTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1.程度',
        id: ['2-1', '2-2', '2-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'1',
        spreadhead: '1. 从躺着或坐着到站着时，觉得轻度头痛、头晕或乏力',
      },

      oneTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1.频率',
        id: ['3-1', '3-2', '3-3', '3-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'1',
        spreadhead: '1. 从躺着或坐着到站着时，觉得轻度头痛、头晕或乏力',
      },


      oneTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '2. 因为头晕或失去知觉而摔倒',
        id: ['4-1', '4-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'2',
        spreadhead: '2. 因为头晕或失去知觉而摔倒',
      },

      oneTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '2.程度',
        id: ['5-1', '5-2', '5-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'2',
        spreadhead: '2. 因为头晕或失去知觉而摔倒',
      },

      oneTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '2.频率',
        id: ['6-1', '6-2','6-3', '6-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'2',
        spreadhead: '2. 因为头晕或失去知觉而摔倒',
      },


      twoTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '3. 白天常在一些场合打盹，如聊天、吃饭、看电视或阅读时',
        id: ['7-1', '7-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'3',
        spreadhead: '3. 白天常在一些场合打盹，如聊天、吃饭、看电视或阅读时',
      },

      twoTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '3.程度',
        id: ['8-1', '8-2', '8-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'3',
        spreadhead: '3. 白天常在一些场合打盹，如聊天、吃饭、看电视或阅读时',
      },

      twoTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '3.频率',
        id: ['9-1', '9-2','9-3', '9-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'3',
        spreadhead: '3. 白天常在一些场合打盹，如聊天、吃饭、看电视或阅读时',
      },

      twoTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '4. 疲劳或者无力影响患者白天的活动',
        id: ['10-1', '10-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'4',
        spreadhead: '4. 疲劳或者无力影响患者白天的活动',
      },

      twoTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '4.程度',
        id: ['11-1', '11-2', '11-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'4',
        spreadhead: '4. 疲劳或者无力影响患者白天的活动',
      },

      twoTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '4.频率',
        id: ['12-1', '12-2','12-3', '12-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'4',
        spreadhead: '4. 疲劳或者无力影响患者白天的活动',
      },


      twoTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '5. 夜间入睡困难或者容易醒',
        id: ['13-1', '13-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'5',
        spreadhead: '5. 夜间入睡困难或者容易醒',
      },

      twoTopicCDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '5.程度',
        id: ['14-1', '14-2', '14-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'5',
        spreadhead: '5. 夜间入睡困难或者容易醒',
      },

      twoTopicCFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '5.频率',
        id: ['15-1', '15-2','15-3', '15-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'5',
        spreadhead: '5. 夜间入睡困难或者容易醒',
      },

      twoTopicD: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '6. 坐着或躺着休息时双下肢感觉不适，需不断活动才能缓解',
        id: ['16-1', '16-2'],
        content: [
          '否;',
          '是。',
        ],
        grade: [ '0', '0'],
        show: 'false',
        simpleContent: [
          '否;',
          '是;',
        ],
        describe: [],
        sign:'6',
        spreadhead: '6. 坐着或躺着休息时双下肢感觉不适，需不断活动才能缓解',
      },

      twoTopicDDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '6.程度',
        id: ['17-1', '17-2', '17-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'6',
        spreadhead: '6. 坐着或躺着休息时双下肢感觉不适，需不断活动才能缓解',
      },

      twoTopicDFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '6.频率',
        id: ['18-1', '18-2','18-3', '18-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'6',
        spreadhead: '6. 坐着或躺着休息时双下肢感觉不适，需不断活动才能缓解',
      },


      threeTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '7. 对周围发生的事情失去兴趣',
        id: ['19-1', '19-2'],
        content: [ '否','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'7',
        spreadhead: '7. 对周围发生的事情失去兴趣',
      },

      threeTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '7.程度',
        id: ['20-1', '20-2', '20-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'7',
        spreadhead: '7. 对周围发生的事情失去兴趣',
      },

      threeTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '7.频率',
        id: ['21-1', '21-2','21-3', '21-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'7',
        spreadhead: '7. 对周围发生的事情失去兴趣',
      },


      threeTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '8. 活动的主动性降低，不愿尝试新鲜事物',
        id: ['22-1', '22-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'8',
        spreadhead: '8. 活动的主动性降低，不愿尝试新鲜事物',
      },

      threeTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '8.程度',
        id: ['23-1', '23-2', '23-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'8',
        spreadhead: '8. 活动的主动性降低，不愿尝试新鲜事物',
      },

      threeTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '8.频率',
        id: ['24-1', '24-2','24-3', '24-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'8',
        spreadhead: '8. 活动的主动性降低，不愿尝试新鲜事物',
      },

      threeTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '9. 看上去或患者自我感觉悲哀、情绪低落',
        id: ['25-1', '25-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'9',
        spreadhead: '9. 看上去或患者自我感觉悲哀、情绪低落',
      },

      threeTopicCDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '9.程度',
        id: ['26-1', '26-2', '26-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'9',
        spreadhead: '9. 看上去或患者自我感觉悲哀、情绪低落',
      },

      threeTopicCFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '9.频率',
        id: ['27-1', '27-2','27-3', '27-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'9',
        spreadhead: '9. 看上去或患者自我感觉悲哀、情绪低落',
      },

      threeTopicD: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '10. 感觉到焦虑、紧张或者恐慌不安',
        id: ['28-1', '28-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'10',
        spreadhead: '10. 感觉到焦虑、紧张或者恐慌不安',
      },

      threeTopicDDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '10.程度',
        id: ['29-1', '29-2', '29-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'10',
        spreadhead: '10. 感觉到焦虑、紧张或者恐慌不安',
      },

      threeTopicDFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '10.频率',
        id: ['30-1','30-2','30-3','30-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3','4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'10',
        spreadhead: '10. 感觉到焦虑、紧张或者恐慌不安',
      },

      threeTopicE: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '11. 情绪没有起伏，缺乏正常情绪体验',
        id: ['31-1', '31-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'11',
        spreadhead: '11. 情绪没有起伏，缺乏正常情绪体验',
      },

      threeTopicEDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '11.程度',
        id: ['32-1', '32-2', '32-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'11',
        spreadhead: '11. 情绪没有起伏，缺乏正常情绪体验',
      },

      threeTopicEFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '11.频率',
        id: ['33-1', '33-2','33-3', '33-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'11',
        spreadhead: '11. 情绪没有起伏，缺乏正常情绪体验',
      },

      threeTopicF: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '12. 日常生活中缺乏愉快的生活体验',
        id: ['34-1', '34-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'12',
        spreadhead: '12. 日常生活中缺乏愉快的生活体验',
      },

      threeTopicFDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '12.程度',
        id: ['35-1', '35-2', '35-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'12',
        spreadhead: '12. 日常生活中缺乏愉快的生活体验',
      },

      threeTopicFFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '12.频率',
        id: ['36-1', '36-2','36-3', '36-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'12',
        spreadhead: '12. 日常生活中缺乏愉快的生活体验',
      },

      fourTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '13. 看到或听到不存在的东西',
        id: ['37-1', '37-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'13',
        spreadhead: '13. 看到或听到不存在的东西',
      },

      fourTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '13.程度',
        id: ['38-1', '38-2', '38-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'13',
        spreadhead: '13. 看到或听到不存在的东西',
      },

      fourTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '13.频率',
        id: ['39-1', '39-2','39-3', '39-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'13',
        spreadhead: '13. 看到或听到不存在的东西',
      },

      fourTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '14. 妄想，如有人要害自己、遭抢劫或别人对自己不忠',
        id: ['40-1', '40-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'14',
        spreadhead: '14. 妄想，如有人要害自己、遭抢劫或别人对自己不忠',
      },

      fourTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '14.程度',
        id: ['41-1', '41-2', '41-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'14',
        spreadhead: '14. 妄想，如有人要害自己、遭抢劫或别人对自己不忠',
      },

      fourTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '14.频率',
        id: ['42-1', '42-2','42-3', '42-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'14',
        spreadhead: '14. 妄想，如有人要害自己、遭抢劫或别人对自己不忠',
      },

      fourTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '15. 看东西重影，一个看成两个',
        id: ['43-1', '43-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'15',
        spreadhead: '15. 看东西重影，一个看成两个',
      },

      fourTopicCDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '15.程度',
        id: ['44-1', '44-2', '44-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'15',
        spreadhead: '15. 看东西重影，一个看成两个',
      },

      fourTopicCFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '15.频率',
        id: ['45-1', '45-2','45-3', '45-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'15',
        spreadhead: '15. 看东西重影，一个看成两个',
      },

      fiveTopicA: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '16. 做事难以集中精力，如阅读或交谈时',
        id: ['46-1', '46-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'16',
        spreadhead: '16. 做事难以集中精力，如阅读或交谈时',
      },

      fiveTopicADegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '16.程度',
        id: ['47-1', '47-2', '47-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'16',
        spreadhead: '16. 做事难以集中精力，如阅读或交谈时',
      },

      fiveTopicAFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '16.频率',
        id: ['48-1', '48-2','48-3', '48-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        sign:'16',
        spreadhead: '16. 做事难以集中精力，如阅读或交谈时',
      },

      fiveTopicB: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '17. 对近期发生的事情记忆有困难',
        id: ['49-1', '49-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        shouldHideComponent:false,
        sign:'17',
        spreadhead: '17. 对近期发生的事情记忆有困难',
      },

      fiveTopicBDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '17.程度',
        id: ['50-1', '50-2', '50-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'17',
        spreadhead: '17. 对近期发生的事情记忆有困难',
      },

      fiveTopicBFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '17.频率',
        id: ['51-1', '51-2','51-3', '51-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        sign:'17',
        spreadhead: '17. 对近期发生的事情记忆有困难',
      },

      fiveTopicC: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '18. 忘记做一些事情，比如吃药',
        id: ['52-1', '52-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'18',
        spreadhead: '18. 忘记做一些事情，比如吃药',
      },

      fiveTopicCDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '18.程度',
        id: ['53-1', '53-2', '53-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'18',
        spreadhead: '18. 忘记做一些事情，比如吃药',
      },

      fiveTopicCFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '18.频率',
        id: ['54-1', '54-2','54-3', '54-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'18',
        spreadhead: '18. 忘记做一些事情，比如吃药',
      },

      sixTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '19. 白天流口水',
        id: ['55-1', '55-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'19',
        spreadhead: '19. 白天流口水',
      },

      sixTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '19.程度',
        id: ['56-1', '56-2', '56-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'19',
        spreadhead: '19. 白天流口水',
      },

      sixTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '19.频率',
        id: ['57-1', '57-2','57-3', '57-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'19',
        spreadhead: '19. 白天流口水',
      },

      sixTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '20. 吞咽困难或呛咳',
        id: ['58-1', '58-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'20',
        spreadhead: '20. 吞咽困难或呛咳',
      },

      sixTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '20.程度',
        id: ['59-1', '59-2', '59-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'20',
        spreadhead: '20. 吞咽困难或呛咳',
      },

      sixTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '20.频率',
        id: ['60-1', '60-2','60-3', '60-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'20',
        spreadhead: '20. 吞咽困难或呛咳',
      },

      sixTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '21. 便秘(一周少天3次大便) ',
        id: ['61-1', '61-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'21',
        spreadhead: '21. 便秘(一周少天3次大便) ',
      },

      sixTopicCDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '21.程度',
        id: ['62-1', '62-2', '62-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'21',
        spreadhead: '21. 便秘(一周少天3次大便) ',
      },

      sixTopicCFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '21.频率',
        id: ['63-1', '63-2','63-3', '63-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'21',
        spreadhead: '21. 便秘(一周少天3次大便) ',
      },

      sevenTopicA: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '22. 尿急',
        id: ['64-1', '64-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'22',
        spreadhead: '22. 尿急',
      },

      sevenTopicADegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '22.程度',
        id: ['65-1', '65-2', '65-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'22',
        spreadhead: '22. 尿急',
      },

      sevenTopicAFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '22.频率',
        id: ['66-1', '66-2','66-3', '66-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'22',
        spreadhead: '22. 尿急',
      },

      sevenTopicB: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '23. 尿频（两次小便间隔少于2小时）',
        id: ['67-1', '67-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        shouldHideComponent:false,
        sign:'23',
        spreadhead: '23. 尿频（两次小便间隔少于2小时）',
      },

      sevenTopicBDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '23.程度',
        id: ['68-1', '68-2', '68-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'23',
        spreadhead: '23. 尿频（两次小便间隔少于2小时）',
      },

      sevenTopicBFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '23.频率',
        id: ['69-1', '69-2','69-3', '69-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'23',
        spreadhead: '23. 尿频（两次小便间隔少于2小时）',
      },

      sevenTopicC: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '24. 夜间规律的起床排尿增多',
        id: ['70-1', '70-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'24',
        spreadhead: '24. 夜间规律的起床排尿增多',
      },

      sevenTopicCDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '24.程度',
        id: ['71-1', '71-2', '71-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'24',
        spreadhead: '24. 夜间规律的起床排尿增多',
      },

      sevenTopicCFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '24.频率',
        id: ['72-1', '72-2','72-3', '72-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'24',
        spreadhead: '24. 夜间规律的起床排尿增多',
      },

      eightTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '25. 性欲改变，增强或减退多',
        id: ['73-1', '73-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'25',
        spreadhead: '25. 性欲改变，增强或减退多',
      },

      eightTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '25.程度',
        id: ['74-1', '74-2', '74-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'25',
        spreadhead: '25. 性欲改变，增强或减退多',
      },

      eightTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '25.频率',
        id: ['75-1', '75-2','75-3', '75-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'25',
        spreadhead: '25. 性欲改变，增强或减退多',
      },

      eightTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '26. 性生活有困难',
        id: ['76-1', '76-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'26',
        spreadhead: '26. 性生活有困难',
      },

      eightTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '26.程度',
        id: ['77-1', '77-2', '77-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'26',
        spreadhead: '26. 性生活有困难',
      },

      eightTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '26.频率',
        id: ['78-1', '78-2','78-3', '78-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'26',
        spreadhead: '26. 性生活有困难',
      },

      nineTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '27. 不能解释的疼痛（是否与药物有关或抗PD药物能否缓解）',
        id: ['79-1', '79-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'27',
        spreadhead: '27. 不能解释的疼痛（是否与药物有关或抗PD药物能否缓解）',
      },

      nineTopicADegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '27.程度',
        id: ['80-1', '80-2', '80-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'27',
        spreadhead: '27. 不能解释的疼痛（是否与药物有关或抗PD药物能否缓解）',
      },

      nineTopicAFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '27.频率',
        id: ['81-1', '81-2','81-3', '81-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'27',
        spreadhead: '27. 不能解释的疼痛（是否与药物有关或抗PD药物能否缓解）',
      },

      nineTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '28. 味觉或嗅觉功能减退',
        id: ['82-1', '82-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'28',
        spreadhead: '28. 味觉或嗅觉功能减退',
      },

      nineTopicBDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '28.程度',
        id: ['83-1', '83-2', '83-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'28',
        spreadhead: '28. 味觉或嗅觉功能减退',
      },

      nineTopicBFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '28.频率',
        id: ['84-1', '84-2','84-3', '84-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'28',
        spreadhead: '28. 味觉或嗅觉功能减退',
      },

      nineTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '29. 不能解释的体重改变（排除饮食的影响）',
        id: ['85-1', '85-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'29',
        spreadhead: '29. 不能解释的体重改变（排除饮食的影响）',
      },

      nineTopicCDegree: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '29.程度',
        id: ['86-1', '86-2', '86-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'29',
        spreadhead: '29. 不能解释的体重改变（排除饮食的影响）',
      },

      nineTopicCFrequency: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '29.频率',
        id: ['87-1', '87-2','87-3', '87-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'29',
        spreadhead: '29. 不能解释的体重改变（排除饮食的影响）',
      },

      nineTopicD: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '30. 出汗增多（排除炎热天气的影响）',
        id: ['88-1', '88-2'],
        content: [ '否;','是。',],
        grade: ['0', '0'],
        show: 'false',
        simpleContent: ['否;','是;',],
        describe: [],
        sign:'30',
        spreadhead: '30. 出汗增多（排除炎热天气的影响）',
      },

      nineTopicDDegree: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '30.程度',
        id: ['89-1', '89-2', '89-3'],
        content: [
          '轻;',
          '中;',
          '重。',
        ],
        grade: ['1', '2','3'],
        show: 'false',
        simpleContent: [
          '轻;',
          '中;',
          '重;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'30',
        spreadhead: '30. 出汗增多（排除炎热天气的影响）',
      },

      nineTopicDFrequency: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '30.频率',
        id: ['90-1', '90-2','90-3', '90-4'],
        content: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁。',
        ],
        grade: ['1', '2','3', '4'],
        show: 'false',
        simpleContent: [
          '极少;',
          '经常;',
          '频繁;',
          '非常频繁;',
        ],
        describe: [],
        shouldHideComponent:false,
        sign:'30',
        spreadhead: '30. 出汗增多（排除炎热天气的影响）',
      },

      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 30,
      // 结果
      totalPoints: '0,0,0',
      choiceBoxs: [[[], []], [], [], [], [], [], [], [], [], []],
      topicExcessiveO: 0,
      topicExcessiveT: 0,
      score: {
        topicOne: 0,
        topicTwo: 0,
        topicFour: 0,
        topicFive: 0,
        topicSix: 0,
        topicSeven: 0,
        topicEight: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: [],
      numsOne:0,
      numsTwo:0,
      numsThree:0,
      numsFour:0,
      numsFive:0,
      numsSix:0,
      numsSeven:0,
      numsEight:0,
      numsNine:0,
      numsTen:0,
      numsEleven:0,
      numsTwelve:0,
      numsThirteen:0,
      numsFourteen:0,
      numsFifteen:0,
      numsSixteen:0,
      numsSeventeen:0,
      numsEighteen:0,
      numsNineteen:0,
      numsTwenty:0,
      numsTwentyOne:0,
      numsTwentyTwo:0,
      numsTwentyThree:0,
      numsTwentyFour:0,
      numsTwentyFive:0,
      numsTwentySix:0,
      numsTwentySeven:0,
      numsTwentyEight:0,
      numsTwentyNine:0,
      numsThirty:0,
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', '帕金森病非运动症状评价量表(NMSS)')
  },

  mounted () {
    // console.log(this.$refs.referenceFour.children,"this.$refs.referenceOne")
    // 一
    for (let i = 0; i < this.$refs.referenceOne.children.length; i++) {
        for (
          let j = 0;
          j <
          this.$refs.referenceOne.children[i].children[0].children[0]
            .children[1].children.length;
          j++
        ) {
          this.choiceBoxs[0][0].push(
            this.$refs.referenceOne.children[i].children[0].children[0]
              .children[1].children[j].children[0]
          )
        }
    }

    // 二
    for (let i = 0; i < this.$refs.referenceTwo.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceTwo.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[0][1].push(
          this.$refs.referenceTwo.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }
    // 三
    for (let i = 0; i < this.$refs.referenceThree.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceThree.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[3].push(
          this.$refs.referenceThree.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }
   //  // 四
    for (let i = 0; i < this.$refs.referenceFour.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceFour.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[4].push(
          this.$refs.referenceFour.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }

    // 五
    for (let i = 0; i < this.$refs.referenceFive.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceFive.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[5].push(
          this.$refs.referenceFive.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }

    // 六
    for (let i = 0; i < this.$refs.referenceSix.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceSix.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[6].push(
          this.$refs.referenceSix.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }

    // 七
    for (let i = 0; i < this.$refs.referenceSeven.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceSeven.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[7].push(
          this.$refs.referenceSeven.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }


    // 八
    for (let i = 0; i < this.$refs.referenceEight.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceEight.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[8].push(
          this.$refs.referenceEight.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }

   // 九
    for (let i = 0; i < this.$refs.referenceNine.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceNine.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[9].push(
          this.$refs.referenceNine.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }

    this.allInput = this.choiceBoxs[0][0].concat(this.choiceBoxs[0][1], this.choiceBoxs[1], this.choiceBoxs[2], this.choiceBoxs[3], this.choiceBoxs[4], this.choiceBoxs[5], this.choiceBoxs[6], this.choiceBoxs[7], this.choiceBoxs[8], this.choiceBoxs[9])

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.allInput.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
          ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.allInput.map((item) => {
      // 如果 处于选中状态 就处理以下逻辑
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = 30
        this.unselected = 0
        this.totalPoints += item.attributes.grade.value * 1
      }
    })
    // this.selected = 30
    // this.unselected = 0
    if(this.$store.state.minitool.conditionChange === 'true'&&localStorage.getItem('illnessTotalScore')){
      this.totalPoints =JSON.parse(localStorage.getItem('illnessTotalScore'))
      this.totalScore=JSON.parse(localStorage.getItem('illnessListDetail'))
      const totalScore=JSON.parse(localStorage.getItem('illnessListDetail'))
      this.cardiovascular.result=totalScore[0]
      this.fatigued.result=totalScore[1]
      this.emotion.result=totalScore[2]
      this.perceptual.result=totalScore[3]
      this.attention.result=totalScore[4]
      this.gastrointestinal.result=totalScore[5]
      this.mition.result=totalScore[6]
      this.sexuality.result=totalScore[7]
      this.Sexualmance.result=totalScore[8]
      var result = [];
      // 展示展开是的循序
      const scoringDetails= JSON.parse(localStorage.getItem('submitData'));
      scoringDetails.forEach(item => {
        const content = item[2] && item[2][0].content ? item[2][0].content : [];
        if (content.includes("是;")) {
          result.push(item[2][0].id);
        }
      });
      if(result&&result.length>0) {
        // 遍历 submitData 数组并将每个元素传递给 showEideShow 函数
        for (let i = 0; i < result.length; i++) {
          this.showEideShow(result[i]);
        }}
      // console.log(result,"result",scoringDetails)
    }
  },

  methods: {
    // 求和
    sumArrays(arrays) {
      // 过滤掉值为null的数组
      const filteredArrays = arrays.filter(array => array !== null);

      // 将字符串数组转换为数字数组
      const numberArrays = filteredArrays.map(array => array.split(',').map(Number));

      // 确定最长的数组长度
      const maxLength = Math.max(...numberArrays.map(array => array.length));

      // 补齐数组长度
      const fillArray = (arr, len) => [...arr, ...Array(len - arr.length).fill(0)];
      const filledArrays = numberArrays.map(array => fillArray(array, maxLength));

      // 计算各位数值之和并拼接结果字符串
      let result = '';
      for (let i = 0; i < maxLength; i++) {
        const sum = filledArrays.reduce((acc, array) => acc + array[i], 0);
        result += sum + ',';
     }

     // 去除最后一个逗号并返回结果
     return result.slice(0, -1);
    },


    showEideShow(id){
      switch (id) {
        case '1-2':
          this.oneTopicADegree.shouldHideComponent = true;
          this.oneTopicAFrequency.shouldHideComponent = true;
          break;
        case '1-1':
          this.oneTopicADegree.shouldHideComponent = false;
          this.oneTopicAFrequency.shouldHideComponent = false;
          break;
        case '4-2':
          this.oneTopicBDegree.shouldHideComponent = true;
          this.oneTopicBFrequency.shouldHideComponent = true;
          break;
        case '4-1':
          this.oneTopicBDegree.shouldHideComponent = false;
          this.oneTopicBFrequency.shouldHideComponent = false;
          break;
        case '7-2':
          this.twoTopicADegree.shouldHideComponent = true;
          this.twoTopicAFrequency.shouldHideComponent = true;
          break;
        case '7-1':
          this.twoTopicADegree.shouldHideComponent = false;
          this.twoTopicAFrequency.shouldHideComponent = false;
          break;
        case '10-2':
          this.twoTopicBDegree.shouldHideComponent = true;
          this.twoTopicBFrequency.shouldHideComponent = true;
          break;
        case '10-1':
          this.twoTopicBDegree.shouldHideComponent = false;
          this.twoTopicBFrequency.shouldHideComponent = false;
          break;
        case '13-2':
          this.twoTopicCDegree.shouldHideComponent = true;
          this.twoTopicCFrequency.shouldHideComponent = true;
          break;
        case '13-1':
          this.twoTopicCDegree.shouldHideComponent = false;
          this.twoTopicCFrequency.shouldHideComponent = false;
          break;
        case '16-2':
          this.twoTopicDDegree.shouldHideComponent = true;
          this.twoTopicDFrequency.shouldHideComponent = true;
          break;
        case '16-1':
          this.twoTopicDDegree.shouldHideComponent = false;
          this.twoTopicDFrequency.shouldHideComponent = false;
          break;
        case '19-2':
          this.threeTopicADegree.shouldHideComponent = true;
          this.threeTopicAFrequency.shouldHideComponent = true;
          break;
        case '19-1':
          this.threeTopicADegree.shouldHideComponent = false;
          this.threeTopicAFrequency.shouldHideComponent = false;
          break;
        case '22-2':
          this.threeTopicBDegree.shouldHideComponent = true;
          this.threeTopicBFrequency.shouldHideComponent = true;
          break;
        case '22-1':
          this.threeTopicBDegree.shouldHideComponent = false;
          this.threeTopicBFrequency.shouldHideComponent = false;
          break;
        case '25-2':
          this.threeTopicCDegree.shouldHideComponent = true;
          this.threeTopicCFrequency.shouldHideComponent = true;
          break;
        case '25-1':
          this.threeTopicCDegree.shouldHideComponent = false;
          this.threeTopicCFrequency.shouldHideComponent = false;
          break;
        case '28-2':
          this.threeTopicDDegree.shouldHideComponent = true;
          this.threeTopicDFrequency.shouldHideComponent = true;
          break;
        case '28-1':
          this.threeTopicDDegree.shouldHideComponent = false;
          this.threeTopicDFrequency.shouldHideComponent = false;
          break;
        case '31-2':
          this.threeTopicEDegree.shouldHideComponent = true;
          this.threeTopicEFrequency.shouldHideComponent = true;
          break;
        case '31-1':
          this.threeTopicEDegree.shouldHideComponent = false;
          this.threeTopicEFrequency.shouldHideComponent = false;
          break;
        case '34-2':
          this.threeTopicFDegree.shouldHideComponent = true;
          this.threeTopicFFrequency.shouldHideComponent = true;
          break;
        case '34-1':
          this.threeTopicFDegree.shouldHideComponent = false;
          this.threeTopicFFrequency.shouldHideComponent = false;
          break;
        case '37-2':
          this.fourTopicADegree.shouldHideComponent = true;
          this.fourTopicAFrequency.shouldHideComponent = true;
          break;
        case '37-1':
          this.fourTopicADegree.shouldHideComponent = false;
          this.fourTopicAFrequency.shouldHideComponent = false;
          break;
        case '40-2':
          this.fourTopicBDegree.shouldHideComponent = true;
          this.fourTopicBFrequency.shouldHideComponent = true;
          break;
        case '40-1':
          this.fourTopicBDegree.shouldHideComponent = false;
          this.fourTopicBFrequency.shouldHideComponent = false;
          break;
        case '43-2':
          this.fourTopicCDegree.shouldHideComponent = true;
          this.fourTopicCFrequency.shouldHideComponent = true;
          break;
        case '43-1':
          this.fourTopicCDegree.shouldHideComponent = false;
          this.fourTopicCFrequency.shouldHideComponent = false;
          break;
        case '46-2':
          this.fiveTopicADegree.shouldHideComponent = true;
          this.fiveTopicAFrequency.shouldHideComponent = true;
          break;
        case '46-1':
          this.fiveTopicADegree.shouldHideComponent = false;
          this.fiveTopicAFrequency.shouldHideComponent = false;
          break;
        case '49-2':
          this.fiveTopicBDegree.shouldHideComponent = true;
          this.fiveTopicBFrequency.shouldHideComponent = true;
          break;
        case '49-1':
          this.fiveTopicBDegree.shouldHideComponent = false;
          this.fiveTopicBFrequency.shouldHideComponent = false;
          break;
        case '52-2':
          this.fiveTopicCDegree.shouldHideComponent = true;
          this.fiveTopicCFrequency.shouldHideComponent = true;
          break;
        case '52-1':
          this.fiveTopicCDegree.shouldHideComponent = false;
          this.fiveTopicCFrequency.shouldHideComponent = false;
          break;
        case '55-2':
          this.sixTopicADegree.shouldHideComponent = true;
          this.sixTopicAFrequency.shouldHideComponent = true;
          break;
        case '55-1':
          this.sixTopicADegree.shouldHideComponent = false;
          this.sixTopicAFrequency.shouldHideComponent = false;
          break;
        case '58-2':
          this.sixTopicBDegree.shouldHideComponent = true;
          this.sixTopicBFrequency.shouldHideComponent = true;
          break;
        case '58-1':
          this.sixTopicBDegree.shouldHideComponent = false;
          this.sixTopicBFrequency.shouldHideComponent = false;
          break;
        case '61-2':
          this.sixTopicCDegree.shouldHideComponent = true;
          this.sixTopicCFrequency.shouldHideComponent = true;
          break;
        case '61-1':
          this.sixTopicCDegree.shouldHideComponent = false;
          this.sixTopicCFrequency.shouldHideComponent = false;
          break;
        case '64-2':
          this.sevenTopicADegree.shouldHideComponent = true;
          this.sevenTopicAFrequency.shouldHideComponent = true;
          break;
        case '64-1':
          this.sevenTopicADegree.shouldHideComponent = false;
          this.sevenTopicAFrequency.shouldHideComponent = false;
          break;
        case '67-2':
          this.sevenTopicBDegree.shouldHideComponent = true;
          this.sevenTopicBFrequency.shouldHideComponent = true;
          break;
        case '67-1':
          this.sevenTopicBDegree.shouldHideComponent = false;
          this.sevenTopicBFrequency.shouldHideComponent = false;
          break;
        case '70-2':
          this.sevenTopicCDegree.shouldHideComponent = true;
          this.sevenTopicCFrequency.shouldHideComponent = true;
          break;
        case '70-1':
          this.sevenTopicCDegree.shouldHideComponent = false;
          this.sevenTopicCFrequency.shouldHideComponent = false;
          break;
        case '73-2':
          this.eightTopicADegree.shouldHideComponent = true;
          this.eightTopicAFrequency.shouldHideComponent = true;
          break;
        case '73-1':
          this.eightTopicADegree.shouldHideComponent = false;
          this.eightTopicAFrequency.shouldHideComponent = false;
          break;
        case '76-2':
          this.eightTopicBDegree.shouldHideComponent = true;
          this.eightTopicBFrequency.shouldHideComponent = true;
          break;
        case '76-1':
          this.eightTopicBDegree.shouldHideComponent = false;
          this.eightTopicBFrequency.shouldHideComponent = false;
          break;
        case '79-2':
          this.nineTopicADegree.shouldHideComponent = true;
          this.nineTopicAFrequency.shouldHideComponent = true;
          break;
        case '79-1':
          this.nineTopicADegree.shouldHideComponent = false;
          this.nineTopicAFrequency.shouldHideComponent = false;
          break;
        case '82-2':
          this.nineTopicBDegree.shouldHideComponent = true;
          this.nineTopicBFrequency.shouldHideComponent = true;
          break;
        case '82-1':
          this.nineTopicBDegree.shouldHideComponent = false;
          this.nineTopicBFrequency.shouldHideComponent = false;
          break;
        case '85-2':
          this.nineTopicCDegree.shouldHideComponent = true;
          this.nineTopicCFrequency.shouldHideComponent = true;
          break;
        case '85-1':
          this.nineTopicCDegree.shouldHideComponent = false;
          this.nineTopicCFrequency.shouldHideComponent = false;
          break;
        case '88-2':
          this.nineTopicDDegree.shouldHideComponent = true;
          this.nineTopicDFrequency.shouldHideComponent = true;
          break;
        case '88-1':
          this.nineTopicDDegree.shouldHideComponent = false;
          this.nineTopicDFrequency.shouldHideComponent = false;
          break;
        default:
          break;
      }
    },

   calculateScore(questions) {
     // console.log(questions,"questions")
     let severity = 0;
     let frequency = 0;
     let luminance= 0;
    for (let i = 0; i < questions.length; i++) {
    const question = questions[i];
    const score = question.scores;
    const severityScore = score[score.length - 2];
    const frequencyScore = score[score.length - 1];
    severity +=severityScore;
    frequency +=frequencyScore;
    const scores = question.scores;
    for (let j = 0; j < scores.length; j++) {
      for (let k = j + 1; k < scores.length; k++) {
        luminance += scores[j] * scores[k];
      }
    }
  }
  return [severity, frequency, luminance];
},
    // 公共逻辑
    checkedOne (item) {

      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
          // console.log(this.choiceBox,"this.choiceBox")

        }
        const itemId = item.id.slice(0, 2);
        const existingIndex = this.cardiovascularList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.cardiovascularList.splice(existingIndex, 1);
        }
        this.cardiovascularList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});
        console.log("cardiovascularList",this.cardiovascularList);
        const grades = this.cardiovascularList.map(item => item.grade);
        // console.log("grades",grades)

        var filteredOneList = this.cardiovascularList.filter(item => ['1-', '2-', '3-'].includes(item.id));
        console.log("filteredOneList",filteredOneList)
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();

        var filteredTwoList = this.cardiovascularList.filter(item => ['4-', '5-', '6-'].includes(item.id));
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        // console.log("filteredOneList", filteredOneList);
        // this.selected = this.choiceBox.length;


      if(filteredOneList.length>0){
        const containsTitle = filteredOneList.some(obj => obj.title === "是;");
        if (containsTitle) {
          console.log("filteredOneList",filteredOneList)
          // 单选框选是的时候数量为0
          this.numsOne = 0;
          // console.log("numsOne",this.numsOne,filteredOneList.length);
          if(filteredOneList.length ===3){
            // 选的数量为3个的时候 说明已经全选了是的时候数量+1
            this.numsOne = 1;
          }else{
            // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
            gradesOneList=[0,0,0]
            this.numsOne=0
          }
          // console.log("包含 title 为 '是;' 的对象");
        } else {
          this.cardiovascularList=this.cardiovascularList.filter(item => item.id !== "2-" && item.id !== "3-");
          // 单选框选否的时候数量为1
          this.numsOne=1
          filteredOneList=filteredOneList.some(obj => obj.title === "否;");
          console.log("filteredOneList",filteredOneList)
          // console.log("不包含 title 为 '是;' 的对象");
          this.choiceBoxs[0][0].slice(2, 9).forEach(checkbox => checkbox.checked = false);
        }
      }


        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsTwo = 0;
            if(filteredTwoList.length ===3){
              this.numsTwo = 1;
            }else{
              this.numsTwo = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.cardiovascularList=this.cardiovascularList.filter(item => item.id !== "5-" && item.id !== "6-");
            this.numsTwo = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[0][0].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }
        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList}])
        this.cardiovascular.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);
      }
     // console.log("unselected",this.numsOne,this.numsTwo,this.unselected)
    },

    checkedTwo (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
          // console.log(this.choiceBox,"this.choiceBox")
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.fatiguedList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.fatiguedList.splice(existingIndex, 1);
        }
        this.fatiguedList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.fatiguedList.filter(item => ['7-', '8-', '9-'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }

        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsThree = 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsThree = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsThree=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.fatiguedList=this.fatiguedList.filter(item => item.id !== "8-" && item.id !== "9-");
            // 单选框选否的时候数量为1
            this.numsThree=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[0][1].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredTwoList = this.fatiguedList.filter(item => ['10', '11', '12'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsFour = 0;
            if(filteredTwoList.length ===3){
              this.numsFour = 1;
            }else{
              this.numsFour = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.fatiguedList=this.fatiguedList.filter(item => item.id !== "11" && item.id !== "12");
            console.log(this.fatiguedList,"fatiguedList",)
            this.numsFour = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[0][1].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredThreeList = this.fatiguedList.filter(item => ['13', '14', '15'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsFive = 0;
            if(filteredThreeList.length ===3){
              this.numsFive = 1;
            }else{
              this.numsFive = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.fatiguedList=this.fatiguedList.filter(item => item.id !== "14" && item.id !== "15");
            this.numsFive = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[0][1].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredFourList = this.fatiguedList.filter(item => ['16', '17', '18'].includes(item.id));
        var gradesFourList = filteredFourList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        const hasNegativeTitle = filteredFourList.some(item => item.title === "否;");
        if(hasNegativeTitle){
          gradesFourList=[0,0,0]
        }
        console.log(gradesFourList,"gradesFourList",filteredFourList)
        var filteredFourContainsNo = gradesFourList.some(obj => obj.title === "否;");
        if (filteredFourContainsNo) {
          filteredFourList = filteredFourList.filter(obj => obj.title === "否;");
        }
        if(filteredFourList.length>0){
          const gradesFourListTitle = filteredFourList.some(obj => obj.title === "是;");
          // console.log(gradesFourListTitle,"gradesFourListTitle",filteredFourList)
          if (gradesFourListTitle) {
            this.numsSix = 0;
            if(filteredFourList.length ===3){
              this.numsSix = 1;
            }else{
              this.numsSix = 0;
              filteredFourList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.fatiguedList=this.fatiguedList.filter(item => item.id !== "17" && item.id !== "18");
            console.log(this.fatiguedList,"this.fatiguedList")
            this.numsSix = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            filteredFourList=[0,0,0]
            this.choiceBoxs[0][1].slice(29,36).forEach(checkbox => checkbox.checked = false);
          }
        }
        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        if(gradesFourList.length<=2){
          gradesFourList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList},{scores:gradesFourList}])
        this.fatigued.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.topicExcessiveO += item.attributes.grade.value * 1
      }
    },
    checkedThree (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.emotionList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.emotionList.splice(existingIndex, 1);
        }
        this.emotionList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.emotionList.filter(item => ['19', '20', '21'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsSeven = 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsSeven = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsSeven=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "20" && item.id !== "21");
            // 单选框选否的时候数量为1
            this.numsSeven=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[3].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }



        var filteredTwoList = this.emotionList.filter(item => ['22', '23', '24'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsEight = 0;
            if(filteredTwoList.length ===3){
              this.numsEight = 1;
            }else{
              this.numsEight = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "23" && item.id !== "24");
            this.numsEight = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[3].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredThreeList = this.emotionList.filter(item => ['25', '26', '27'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsNine = 0;
            if(filteredThreeList.length ===3){
              this.numsNine = 1;
            }else{
              this.numsNine = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "26" && item.id !== "27");
            this.numsNine = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[3].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredFourList = this.emotionList.filter(item => ['28', '29', '30'].includes(item.id));
        var gradesFourList = filteredFourList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        const hasNegativeTitle = filteredFourList.some(item => item.title === "否;");
        if(hasNegativeTitle){
          gradesFourList=[0,0,0]
        }
        var filteredFourContainsNo = gradesFourList.some(obj => obj.title === "否;");
        if (filteredFourContainsNo) {
          filteredFourList = filteredFourList.filter(obj => obj.title === "否;");
        }
        if(filteredFourList.length>0){
          const gradesFourListTitle = filteredFourList.some(obj => obj.title === "是;");
          // console.log(gradesFourListTitle,"gradesFourListTitle",filteredFourList)
          if (gradesFourListTitle) {
            this.numsTen = 0;
            if(filteredFourList.length ===3){
              this.numsTen = 1;
            }else{
              this.numsTen = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "29" && item.id !== "30");
            this.numsTen = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[3].slice(29,36).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredFiveList = this.emotionList.filter(item => ['31', '32', '33'].includes(item.id));
        var gradesFiveList = filteredFiveList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredFiveContainsNo = filteredFiveList.some(obj => obj.title === "否;");
        if (filteredFiveContainsNo) {
          filteredFiveList = filteredFiveList.filter(obj => obj.title === "否;");
        }
        if(filteredFiveList.length>0){
          const gradesFiveListTitle = filteredFiveList.some(obj => obj.title === "是;");
          // console.log(gradesFiveListTitle,"gradesTwoListTitle",filteredFiveList)
          if (gradesFiveListTitle) {
            this.numsEleven = 0;
            if(filteredFiveList.length ===3){
              this.numsEleven = 1;
            }else{
              this.numsEleven = 0;
              gradesFiveList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "32" && item.id !== "33");
            this.numsEleven = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesFiveList=[0,0,0]
            this.choiceBoxs[3].slice(38, 45).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredSixList = this.emotionList.filter(item => ['34', '35', '36'].includes(item.id));
        var gradesSixList = filteredSixList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredSixContainsNo = filteredSixList.some(obj => obj.title === "否;");
        if (filteredSixContainsNo) {
          filteredSixList = filteredSixList.filter(obj => obj.title === "否;");
        }
        if(filteredSixList.length>0){
          const gradesSixListTitle = filteredSixList.some(obj => obj.title === "是;");
          // console.log(gradesSixListTitle,"gradesTwoListTitle",filteredSixList)
          if (gradesSixListTitle) {
            this.numsTwelve = 0;
            if(filteredSixList.length ===3){
              this.numsTwelve = 1;
            }else{
              this.numsTwelve = 0;
              gradesSixList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.emotionList=this.emotionList.filter(item => item.id !== "35" && item.id !== "36");
            this.numsTwelve = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesSixList=[0,0,0]
            this.choiceBoxs[3].slice(47, 54).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        if(gradesFourList.length<=2){
          gradesFourList=[0,0,0]
        }

        if(gradesFiveList.length<=2){
          gradesFiveList=[0,0,0]
        }

        if(gradesSixList.length<=2){
          gradesSixList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList},{scores:gradesFourList},{scores:gradesFiveList},{scores:gradesSixList}])
        this.emotion.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicFour += item.attributes.grade.value * 1
      }
    },
    checkedFour (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.perceptualList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.perceptualList.splice(existingIndex, 1);
        }
        this.perceptualList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.perceptualList.filter(item => ['37', '38', '39'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsThirteen = 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsThirteen = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsThirteen=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.perceptualList=this.perceptualList.filter(item => item.id !== "38" && item.id !== "39");
            // 单选框选否的时候数量为1
            this.numsThirteen=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[4].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredTwoList = this.perceptualList.filter(item => ['40', '41', '42'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsFourteen = 0;
            if(filteredTwoList.length ===3){
              this.numsFourteen = 1;
            }else{
              this.numsFourteen = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.perceptualList=this.perceptualList.filter(item => item.id !== "41" && item.id !== "42");
            this.numsFourteen = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[4].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredThreeList = this.perceptualList.filter(item => ['43', '44', '45'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsFifteen = 0;
            if(filteredThreeList.length ===3){
              this.numsFifteen = 1;
            }else{
              this.numsFifteen = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.perceptualList=this.perceptualList.filter(item => item.id !== "44" && item.id !== "45");
            this.numsFifteen = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[4].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }


        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList}])
        this.perceptual.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicFour += item.attributes.grade.value * 1
      }
    },
    checkedFive (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.attentionList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.attentionList.splice(existingIndex, 1);
        }
        this.attentionList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.attentionList.filter(item => ['46', '47', '48'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsSixteen= 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsSixteen = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsSixteen=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.attentionList=this.attentionList.filter(item => item.id !== "47" && item.id !== "48");
            // 单选框选否的时候数量为1
            this.numsSixteen=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[5].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredTwoList = this.attentionList.filter(item => ['49', '50', '51'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsSeventeen = 0;
            if(filteredTwoList.length ===3){
              this.numsSeventeen = 1;
            }else{
              this.numsSeventeen = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.attentionList=this.attentionList.filter(item => item.id !== "50" && item.id !== "51");
            this.numsSeventeen = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[5].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredThreeList = this.attentionList.filter(item => ['52', '53', '54'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsEighteen = 0;
            if(filteredThreeList.length ===3){
              this.numsEighteen = 1;
            }else{
              this.numsEighteen = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.attentionList=this.attentionList.filter(item => item.id !== "53" && item.id !== "54");
            this.numsEighteen = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[5].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList}])
        this.attention.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicFive += item.attributes.grade.value * 1
      }
    },
    checkedSix (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.gastrointestinalList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.gastrointestinalList.splice(existingIndex, 1);
        }
        this.gastrointestinalList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.gastrointestinalList.filter(item => ['55', '56', '57'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsNineteen= 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsNineteen = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsNineteen=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.gastrointestinalList=this.gastrointestinalList.filter(item => item.id !== "56" && item.id !== "57");
            // 单选框选否的时候数量为1
            this.numsNineteen=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[6].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredTwoList = this.gastrointestinalList.filter(item => ['58', '59', '60'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsTwenty = 0;
            if(filteredTwoList.length ===3){
              this.numsTwenty = 1;
            }else{
              this.numsTwenty = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.gastrointestinalList=this.gastrointestinalList.filter(item => item.id !== "59" && item.id !== "60");
            this.numsTwenty = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[6].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }



        var filteredThreeList = this.gastrointestinalList.filter(item => ['61', '62', '63'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsTwentyOne = 0;
            if(filteredThreeList.length ===3){
              this.numsTwentyOne = 1;
            }else{
              this.numsTwentyOne = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.gastrointestinalList=this.gastrointestinalList.filter(item => item.id !== "62" && item.id !== "63");
            this.numsTwentyOne = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[6].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList}])
        this.gastrointestinal.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicSix += item.attributes.grade.value * 1
      }
    },
    checkedSeven (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.mitionList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.mitionList.splice(existingIndex, 1);
        }
        this.mitionList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.mitionList.filter(item => ['64', '65', '66'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsTwentyTwo= 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsTwentyTwo = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsTwentyTwo=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.mitionList=this.mitionList.filter(item => item.id !== "65" && item.id !== "66");
            // 单选框选否的时候数量为1
            this.numsTwentyTwo=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[7].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredTwoList = this.mitionList.filter(item => ['67', '68', '69'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsTwentyThree = 0;
            if(filteredTwoList.length ===3){
              this.numsTwentyThree = 1;
            }else{
              this.numsTwentyThree = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.mitionList=this.mitionList.filter(item => item.id !== "68" && item.id !== "69");
            this.numsTwentyThree = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[7].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredThreeList = this.mitionList.filter(item => ['70', '71', '72'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsTwentyFour = 0;
            if(filteredThreeList.length ===3){
              this.numsTwentyFour = 1;
            }else{
              this.numsTwentyFour = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.mitionList=this.mitionList.filter(item => item.id !== "71" && item.id !== "72");
            this.numsTwentyFour = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[7].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList}])
        this.mition.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicSix += item.attributes.grade.value * 1
      }
    },
    checkedEight (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.sexualityList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.sexualityList.splice(existingIndex, 1);
        }
        this.sexualityList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.sexualityList.filter(item => ['73', '74', '75'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsTwentyFive= 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsTwentyFive = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsTwentyFive=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.sexualityList=this.sexualityList.filter(item => item.id !== "74" && item.id !== "75");
            // 单选框选否的时候数量为1
            this.numsTwentyFive=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[8].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredTwoList = this.sexualityList.filter(item => ['76', '77', '78'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsTwentySix = 0;
            if(filteredTwoList.length ===3){
              this.numsTwentySix = 1;
            }else{
              this.numsTwentySix = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.sexualityList=this.sexualityList.filter(item => item.id !== "77" && item.id !== "78");
            this.numsTwentySix = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[8].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }


        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList}])
        this.sexuality.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);

        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicSix += item.attributes.grade.value * 1
      }
    },

    checkedNine (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }

        const itemId = item.id.slice(0, 2);
        const existingIndex = this.SexualmanceList.findIndex(cl => cl.id === itemId);
        if (existingIndex !== -1) {
          this.SexualmanceList.splice(existingIndex, 1);
        }
        this.SexualmanceList.push({id: itemId, grade: item.attributes.grade.value,title:item.attributes.title.value});

        var filteredOneList = this.SexualmanceList.filter(item => ['79', '80', '81'].includes(item.id));
        var gradesOneList = filteredOneList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade) ).flat();
        var filteredOneContainsNo = filteredOneList.some(obj => obj.title === "否;");
        if (filteredOneContainsNo) {
          filteredOneList = filteredOneList.filter(obj => obj.title === "否;");
        }
        if(filteredOneList.length>0){
          const containsTitle = filteredOneList.some(obj => obj.title === "是;");
          if (containsTitle) {
            // 单选框选是的时候数量为0
            this.numsTwentySeven= 0;
            // console.log("numsOne",this.numsOne,filteredOneList.length);
            if(filteredOneList.length ===3){
              // 选的数量为3个的时候 说明已经全选了是的时候数量+1
              this.numsTwentySeven = 1;
            }else{
              // 选的数量小于3的时候 说明已经全选了是的时候数量还原为0
              gradesOneList=[0,0,0]
              this.numsTwentySeven=0
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.SexualmanceList=this.SexualmanceList.filter(item => item.id !== "80" && item.id !== "81");
            // 单选框选否的时候数量为1
            this.numsTwentySeven=1
            gradesOneList=[0,0,0]

            // console.log("filteredOneList",filteredOneList)
            // console.log("不包含 title 为 '是;' 的对象");
            this.choiceBoxs[9].slice(2, 9).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredTwoList = this.SexualmanceList.filter(item => ['82', '83', '84'].includes(item.id));
        var gradesTwoList = filteredTwoList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredTwoContainsNo = filteredTwoList.some(obj => obj.title === "否;");
        if (filteredTwoContainsNo) {
          filteredTwoList = filteredTwoList.filter(obj => obj.title === "否;");
        }
        if(filteredTwoList.length>0){
          const gradesTwoListTitle = filteredTwoList.some(obj => obj.title === "是;");
          // console.log(gradesTwoListTitle,"gradesTwoListTitle",filteredTwoList)
          if (gradesTwoListTitle) {
            this.numsTwentyEight = 0;
            if(filteredTwoList.length ===3){
              this.numsTwentyEight = 1;
            }else{
              this.numsTwentyEight = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            // this.SexualmanceList=this.SexualmanceList.filter(item => item.id !== "84" && item.id !== "85");
            this.numsTwentyEight = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[9].slice(11, 18).forEach(checkbox => checkbox.checked = false);
          }
        }


        var filteredThreeList = this.SexualmanceList.filter(item => ['85', '86', '87'].includes(item.id));
        var gradesThreeList = filteredThreeList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredThreeContainsNo = filteredThreeList.some(obj => obj.title === "否;");
        if (filteredThreeContainsNo) {
          filteredThreeList = filteredThreeList.filter(obj => obj.title === "否;");
        }
        if(filteredThreeList.length>0){
          const gradesThreeListTitle = filteredThreeList.some(obj => obj.title === "是;");
          // console.log(gradesThreeListTitle,"gradesTwoListTitle",filteredThreeList)
          if (gradesThreeListTitle) {
            this.numsTwentyNine = 0;
            if(filteredThreeList.length ===3){
              this.numsTwentyNine = 1;
            }else{
              this.numsTwentyNine = 0;
              gradesThreeList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            // this.SexualmanceList=this.SexualmanceList.filter(item => item.id !== "86" && item.id !== "87");
            this.numsTwentyNine = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesThreeList=[0,0,0]
            this.choiceBoxs[9].slice(20, 27).forEach(checkbox => checkbox.checked = false);
          }
        }

        var filteredFourList = this.SexualmanceList.filter(item => ['88', '89', '90'].includes(item.id));
        // console.log(filteredFourList,"filteredFourList")
        var gradesFourList = filteredFourList.map(item => item.title==='否;'? [0, 0, 0] : parseInt(item.grade)).flat();
        var filteredFourContainsNo = filteredFourList.some(obj => obj.title === "否;");
        const hasNegativeTitle = filteredFourList.some(item => item.title === "否;");
        if(hasNegativeTitle){
          gradesFourList=[0,0,0]
        }
        if (filteredFourContainsNo) {
          filteredFourList = filteredFourList.filter(obj => obj.title === "否;");
        }
        if(filteredFourList.length>0){
          const gradesFourListTitle = filteredFourList.some(obj => obj.title === "是;");
          // console.log(gradesFourListTitle,"gradesFourListTitle",filteredFourList)
          if (gradesFourListTitle) {
            this.numsThirty = 0;
            if(filteredFourList.length ===3){
              this.numsThirty = 1;
            }else{
              this.numsThirty = 0;
              gradesTwoList=[0,0,0]
            }
            // console.log("包含 title 为 '是;' 的对象");
          } else {
            this.SexualmanceList=this.SexualmanceList.filter(item => item.id !== "89" && item.id !== "90");
            this.numsThirty = 1;
            // console.log("不包含 title 为 '是;' 的对象");
            gradesTwoList=[0,0,0]
            this.choiceBoxs[9].slice(29,36).forEach(checkbox => checkbox.checked = false);
          }
        }

        if(gradesOneList.length<=2){
          gradesOneList=[0,0,0]
        }
        if(gradesTwoList.length<=2){
          gradesTwoList=[0,0,0]
        }

        if(gradesThreeList.length<=2){
          gradesThreeList=[0,0,0]
        }

        if(gradesFourList.length<=2){
          gradesFourList=[0,0,0]
        }

        var scoreList= this.calculateScore([{scores:gradesOneList},{scores:gradesTwoList},{scores:gradesThreeList},{scores:gradesFourList}])
        this.Sexualmance.result=scoreList.join(', ');
        // console.log("filtered data", gradesOneList,gradesTwoList,scoreList);
        // this.selected = this.choiceBox.length
        // this.unselected = 30 - this.selected
        this.score.topicSix += item.attributes.grade.value * 1
      }
    },
    result () {
     this.totalScore=[this.cardiovascular.result,this.fatigued.result,this.emotion.result,this.perceptual.result,this.attention.result,this.gastrointestinal.result,this.mition.result,this.sexuality.result,this.Sexualmance.result]
      var Score=this.sumArrays(this.totalScore)
      console.log(this.totalScore,"this.totalScore",Score)
      this.totalPoints =Score
    },
    // 选项的点击事件
    getTatals (e) {
      // console.log("this.choiceBoxs[0][0]",this.choiceBoxs[0])
      // 初始化数据
      // this.score.topicOne = 0
      this.score = {
        topicOne: 0,
        topicTwo: 0,
        topicFour: 0,
        topicFive: 0,
        topicSix: 0,
        topicSeven: 0,
        topicEight: 0,
      }
      // this.totalPoints = 0
      this.unselected = 30
      this.selected = 0
      this.topicExcessiveO = 0
      this.topicExcessiveT = 0
      this.choiceBox = []
      this.optionMessage = []
      // 遍历数组
      this.choiceBoxs[0][0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedOne(item)

      })



      const id = e.target.attributes.id?.value;

      this.showEideShow(id)



      // 小三题 多选清空逻辑
      // if (
      //   e.target.attributes.id !== undefined &&
      //   e.target.attributes.id.value !== '3-4' &&
      //   e.target.attributes.id.value.slice(0, 2) === '3-'
      // ) {
      //   this.choiceBoxs[0][1][3].checked = false
      // } else if (
      //   e.target.attributes.id !== undefined &&
      //   e.target.attributes.id.value === '3-4' &&
      //   e.target.attributes.id.value.slice(0, 2) === '3-'
      // ) {
      //   for (let i = 0; i < this.choiceBoxs[0][1].length - 1; i++) {
      //     this.choiceBoxs[0][1][i].checked = false
      //   }
      // }
      this.choiceBoxs[0][1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedTwo(item)
      })

      this.choiceBoxs[3].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedThree(item)
      })

      this.choiceBoxs[4].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedFour(item)
      })

      this.choiceBoxs[5].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedFive(item)
      })

      this.choiceBoxs[6].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedSix(item)
      })

      this.choiceBoxs[7].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedSeven(item)
      })

      this.choiceBoxs[8].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedEight(item)
      })

      this.choiceBoxs[9].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedNine(item)
      })

      this.unselected =this.unselected-this.numsOne-this.numsTwo-this.numsThree-this.numsFour-
      this.numsFive-this.numsSix-this.numsSeven-this.numsEight-this.numsNine-this.numsTen-
      this.numsEleven-this.numsTwelve- this.numsThirteen-this.numsFourteen-this.numsFifteen-
      this.numsSixteen-this.numsSeventeen-this.numsEighteen-this.numsNineteen-this.numsTwenty-
      this.numsTwentyOne-this.numsTwentyTwo-this.numsTwentyThree-this.numsTwentyFour-
      this.numsTwentyFive-this.numsTwentySix-this.numsTwentySeven-this.numsTwentyEight-
      this.numsTwentyNine-this.numsThirty

      this.selected=this.numsOne+this.numsTwo+this.numsThree+this.numsFour+
        this.numsFive+this.numsSix+this.numsSeven+this.numsEight+this.numsNine+this.numsTen+
        this.numsEleven+this.numsTwelve+this.numsThirteen+this.numsFourteen+this.numsFifteen+
        this.numsSixteen+this.numsSeventeen+this.numsEighteen+this.numsNineteen+this.numsTwenty+
        this.numsTwentyOne+this.numsTwentyTwo+this.numsTwentyThree+this.numsTwentyFour+
        this.numsTwentyFive+this.numsTwentySix+this.numsTwentySeven+this.numsTwentyEight+
        this.numsTwentyNine+this.numsThirty
      this.result()
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
      localStorage.setItem('illnessListDetail', JSON.stringify(this.totalScore))
      localStorage.setItem('illnessTotalScore', JSON.stringify(this.totalPoints))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          totalScore: this.totalScore,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    p {
      font-family: 'Microsoft YaHei';
      font-weight: 700;
      font-size: 22px;
      color: #202020;
      margin: 18px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    //.threeTopic {
    //  div {
    //    width: 877px;
    //    margin-top: 16px;
    //    padding: 3px 16px;
    //    box-sizing: border-box;
    //    border-radius: 6px;
    //    //background-color: rgba(5, 129, 206, 0.03);
    //
    //    p:nth-of-type(1) {
    //      font-weight: 700;
    //      font-size: 16px;
    //      color: #333333;
    //    }
    //
    //    p:nth-of-type(2) {
    //      font-weight: 400;
    //      font-size: 16px;
    //      color: #333333;
    //    }
    //  }
    //}
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
  .diseaseType{
    margin-top: 10px;
  }
  .disease{
    margin-bottom: 5px;
  }
}
</style>
