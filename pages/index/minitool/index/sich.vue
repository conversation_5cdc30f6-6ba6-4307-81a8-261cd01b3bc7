<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="age"></Options>
          <MultipleChoice :information="symptom"></MultipleChoice>
        </div>
        <!-- 关联评分 -->
        <div class="relevance">
          <p>不知道NIHSS评分？</p>
          <p @click="goScore">去评分</p>
          <!-- @click="routeSkit" -->
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div class="explain" style="white-space: pre-wrap">
            <div class="explainContent">
              <span class="explainContentResult">结果说明：</span> <p>{{ explain }}</p>
            </div>
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails, MultipleChoice },
  // tdk
  head () {
    return {
      title: 'SICH评分 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'SICH评分是预测静脉溶栓后症状性脑出血（SICH）的常用量表之一。分值≥10 分患者发生SICH的风险是0分患者的约70倍。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'SICH评分, 静脉溶栓后症状性脑出血风险评估, SITS'
        }
      ]
    }
  },
  data () {
    return {
      // 患者年龄
      age: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '1. NIHSS评分',
        id: ['1-1', '1-2', '1-3'],
        content: [
          '1) NIHSS<7：0分;',
          '2) NIHSS 7-12：1分;',
          '3) NIHSS≥13：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: ['NIHSS<7;', '65~NIHSS 7-12;', 'NIHSS≥13;'],
      },
      // 患者情况/症状
      symptom: {
        bgColor: 'background: #FBFBFB;',
        title: '2. 危险因素',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5', '2-6', '2-7', '2-8', '2-9'],
        content: [
          '阿司匹林+氯吡格雷 ：3分',
          '单用阿司匹林：2分',
          '血糖≥180mg/dl（10 mmol/L）：2分',
          '年龄≥72岁：1分',
          '收缩压≥146mmHg：1分',
          '体重≥95kg：1分',
          '发病到治疗时间≥180min：1分',
          '高血压病史：1分',
          '没有以上危险因素：0分',
        ],
        grade: ['3', '2', '2', '1', '1', '1', '1', '1', '0'],
        simpleContent: [
          '阿司匹林+氯吡格雷;',
          '单用阿司匹林;',
          '血糖≥180mg/dl（10 mmol/L）;',
          '年龄≥72岁;',
          '收缩压≥146mmHg;',
          '体重≥95kg;',
          '发病到治疗时间≥180min;',
          '高血压病史;',
          '没有以上危险因素;',
        ],
      },
      explain:
        '1.该评分是预测静脉溶栓后症状性脑出血（SICH）的常用量表之一。\n2.症状性脑出血定义（SITS-MOST标准）：\n溶栓后36h内，相对基线/最低NIHSS增加≥4分，影像显示有占位效应的脑血肿。\n3. 分值≥10分患者发生SICH的风险是0分患者的约70倍。',
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 2,
      // 总分数
      totalPoints: 0,
      totalPointsOne: 0,
      totalPointsTwo: 0,
      aloneChoice: [],
      moreChoice: [],
      choiceNumber: [],
      score: 0,
      nihssScore: null,
      optionMessage: [],
      allInput: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', 'SICH评分')
  },

  mounted () {
    this.nihssScore = this.$route.query.fromPath
    // 获取到单选的 input 元素，并存入到 arr 中
    for (
      let j = 0;
      j <
      this.$refs.reference.children[0].children[0].children[3].children.length;
      j++
    ) {
      this.aloneChoice.push(
        this.$refs.reference.children[0].children[0].children[3].children[j]
          .children[0]
      )
    }
    // 获取到 多选 的 input 框
    for (
      let j = 0;
      j <
      this.$refs.reference.children[1].children[0].children[1].children.length;
      j++
    ) {
      this.moreChoice.push(
        this.$refs.reference.children[1].children[0].children[1].children[j]
          .children[0]
      )
    }

    this.allInput = this.aloneChoice.concat(this.moreChoice)
    // 登录之后 从本地拿去数据 勾选选项
    if (
      this.$store.state.minitool.conditionChange === 'true' &&
      localStorage.getItem('scoringDetails') !== null
    ) {
      this.allInput.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 关联评分 跳转之后
    if (
      JSON.parse(localStorage.getItem('scoringDetails')) !== null &&
      JSON.parse(localStorage.getItem('scoringDetails')).length !== 0
    ) {
      if (this.$route.query.fromPath !== undefined) {
        this.allInput.map((item, index) => {
          item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
            index
          ].choice
        })
      }
    }

    // 关联评分 跳转之后 第一题选项
    if (this.nihssScore < 7) {
      this.aloneChoice[0].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['NIHSS<7;'],
            id: '',
            scores: '0',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    } else if (this.nihssScore >= 7 && this.nihssScore <= 12) {
      this.aloneChoice[1].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['65~NIHSS 7-12;'],
            id: '',
            scores: '1',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    } else if (this.nihssScore >= 13) {
      this.aloneChoice[2].checked = true
      this.$store.commit('minitool/gradeReturn', [
        [],
        [true],
        [
          {
            content: ['NIHSS≥13;'],
            id: '',
            scores: '2',
            title: 'NIHSS评分',
          },
        ],
        [],
        [1],
      ])
    }

    // 遍历 aloneChoice 数组
    this.aloneChoice.map((item) => {
      this.aloneChoiceFun(item)
    })
    // 遍历 moreChoice 数组
    this.moreChoice.map((item) => {
      this.moreChoiceFun(item)
    })
    this.score = this.totalPointsOne + this.totalPointsTwo
    // 结果展示
    this.result()
  },
  methods: {
    // 公共逻辑
    aloneChoiceFun (item) {
      if (item.checked === true) {
        if (this.choiceNumber.indexOf(item.id.slice(0, 1)) === -1) {
          this.choiceNumber.push(item.id.slice(0, 1))
        }
        this.selected = this.choiceNumber.length
        this.unselected = 2 - this.selected
        this.totalPointsOne += item.attributes.grade.value * 1
      }
    },
    moreChoiceFun (item) {
      if (item.checked === true) {
        this.totalPointsTwo += item.attributes.grade.value * 1
        if (this.choiceNumber.indexOf(item.id.slice(0, 1)) === -1) {
          this.choiceNumber.push(item.id.slice(0, 1))
        } else if (item.id === '2-9') {
          this.totalPointsTwo = 0
        }
        this.selected = this.choiceNumber.length
        this.unselected = 2 - this.selected
      }
    },
    // 结果展示
    result () {
      if (this.score >= 0 && this.score <= 2) {
        this.totalPoints = `${this.score}分，危险分层：低，SICH率：0.4%（0.2%-0.6%）`
      } else if (this.score >= 3 && this.score <= 5) {
        this.totalPoints = `${this.score}分，危险分层：平均水平，SICH率：1.5%（1.3%-1.7%）`
      } else if (this.score >= 6 && this.score <= 8) {
        this.totalPoints = `${this.score}分，危险分层：中，SICH率：3.6%（3.1%-4.1%）`
      } else {
        this.totalPoints = `${this.score}分，危险分层：高，SICH率：9.2%（5.9%-12.5%）`
      }
    },
    // 去评分
    goScore () {
      this.$router.push({
        path: '/minitool/nihss',
        query: {
          fromPath: '/minitool/sich',
        },
      })
      localStorage.setItem('className', '缺血')
      localStorage.setItem('wordLast', 'NIHSS评分 ')
      this.$store.commit('minitool/setClassName', '缺血')
      this.$store.commit('minitool/setWordLast', 'NIHSS评分 ')
      localStorage.setItem('associatedScore', true)
      localStorage.setItem('associated', JSON.stringify(this.optionMessage))
      localStorage.setItem('storagesubmitData', true)
    },
    // 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.totalPointsOne = 0
      this.totalPointsTwo = 0
      this.unselected = 2
      this.selected = 0
      this.choiceNumber = []
      this.score = 0
      this.optionMessage = []
      // 遍历 aloneChoice 数组
      this.aloneChoice.map((item) => {
        // 存储选项的状态
        this.optionMessage.push({
          choice: item.checked,
        })
        this.aloneChoiceFun(item)
      })

      // 最后一个选项的逻辑判断
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '2-9' &&
        e.target.attributes.id.value.substring(0, 1) === '2'
      ) {
        this.moreChoice[8].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '2-9' &&
        e.target.attributes.id.value.substring(0, 1) === '2'
      ) {
        for (let i = 0; i < this.moreChoice.length - 1; i++) {
          this.moreChoice[i].checked = false
        }
      }

      // 遍历 moreChoice 数组
      this.moreChoice.map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.moreChoiceFun(item)
      })
      this.score = this.totalPointsOne + this.totalPointsTwo
      // 将选项的状态 存入本地
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
      // 结果展示
      this.result()
    },
    // 保存评分
    save () {
      localStorage.removeItem('associatedScore')
      localStorage.removeItem('conditionChange')
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .relevance {
    position: absolute;
    left: 150px;
    top: 66px;
    width: 160px;
    display: flex;
    font-size: 12px;
    font-weight: 400;
    font-family: 'PingFang SC';

    p:nth-of-type(1) {
      color: #888888;
    }

    p:nth-of-type(2) {
      color: #0581ce;
      cursor: pointer;
      border-bottom: 1px solid #0581ce;
    }
  }

  .result {
    width: 700px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
