<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
<!--          <p>一、自理能力</p>-->
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
          <Options :information="fourteenTopic"></Options>
          <Options :information="fifteenTopic"></Options>
          <Options :information="sixteenTopic"></Options>
          <Options :information="seventeenTopic"></Options>
          <Options :information="eighteenTopic"></Options>
          <Options :information="nineteenTopic"></Options>
          <Options :information="twentyTopic"></Options>
          <Options :information="twentyOneTopic"></Options>
          <Options :information="twentyTwoTopic"></Options>
          <Options :information="twentyThreeTopic"></Options>
          <Options :information="twentyFourTopic"></Options>
          <Options :information="twentyFiveTopic"></Options>
          <Options :information="twentySixTopic"></Options>
          <Options :information="twentySevenTopic"></Options>
          <Options :information="twentyEightTopic"></Options>
          <Options :information="twentyNineTopic"></Options>
          <Options :information="thirtyTopic"></Options>
          <Options :information="thirtyOneTopic"></Options>
          <Options :information="thirtyTwoTopic"></Options>
          <Options :information="thirtyThreeTopic"></Options>
          <Options :information="thirtyFourTopic"></Options>
          <Options :information="thirtyFiveTopic"></Options>
          <Options :information="thirtySixTopic"></Options>
          <Options :information="thirtySevenTopic"></Options>
          <Options :information="thirtyEightTopic"></Options>
          <Options :information="thirtyNineTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '帕金森病生活质量评分量表(PDQ-39) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: 'PDQ-39（Parkinson’s Disease Questionnaire - 39）是评估帕金森病患者生活质量的标准量表，包含39个问题，覆盖8个生活领域：活动能力、日常活动、情绪、社会支持、认知功能、沟通、身体不适及性功能。通过问卷形式让患者自评疾病对其生活的影响程度，量化疾病所致生活质量的下降，为临床治疗与研究提供关键参考依据。'

        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '帕金森病39项生活质量量表,帕金森病生活质量问卷,PDQ-39'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 想要做一些休闲活动有困难?',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '2. 照顾你的家有困难?譬如家庭维修、处理家庭杂物，烹饪等等?',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '3. 逛街时，提购物袋有困难?',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '4. 走一公里路有困难?',
        id: ['4-1', '4-2', '4-3', '4-4', '4-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '5. 走100公尺路有困难?',
        id: ['5-1', '5-2', '5-3', '5-4', '5-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '6. 想和平常一样在屋子里走动有困难?',
        id: ['6-1', '6-2', '6-3', '6-4', '6-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '7. 到公共场所走动有困难?',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '8. 出门的时候需要有人陪伴着?',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '9. 害怕或是担心在公共场所跌倒?',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '10. 局限在家里没办法出去?',
        id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:'11. 自己洗澡有困难?',
        id: ['11-1', '11-2', '11-3', '11-4', '11-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '12. 自己穿衣服有困难?',
        id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '13. 扣纽扣或是绑鞋带有困难?',
        id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      fourteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '14. 想要把字写清楚有困难?',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      fifteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '15. 拿筷子或汤匙吃东西有困难?',
        id: ['15-1', '15-2', '15-3', '15-4', '15-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      sixteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '16. 拿杯子喝东西时，不溅出来有困难?',
        id: ['16-1', '16-2', '16-3','16-4', '16-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      seventeenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '17. 会感到心情沮丧?',
        id: ['17-1', '17-2', '17-3', '17-4', '17-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      eighteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '18. 会感到被孤立以及寂寞?',
        id: ['18-1', '18-2', '18-3','18-4', '18-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      nineteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '19. 会想哭或掉眼泪?',
        id: ['19-1', '19-2', '19-3', '19-4', '19-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '20. 会感到愤怒或痛苦?',
        id: ['20-1', '20-2', '20-3','20-4', '20-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },

      twentyOneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '21. 会感到焦虑不安?',
        id: ['21-1', '21-2', '21-3', '21-4', '21-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyTwoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '22. 会觉得担心你的未来?',
        id: ['22-1', '22-2', '22-3', '22-4', '22-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyThreeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '23. 觉得你必须对别人隐瞒你的帕金森氏病?',
        id: ['23-1', '23-2', '23-3','23-4', '23-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyFourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '24. 会特意避免在公共场合吃饭或是喝东西?',
        id: ['24-1', '24-2', '24-3','24-4', '24-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyFiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '25. 在公共场合会因为有帕金森氏病而感到尴尬?',
        id: ['25-1', '25-2', '25-3','25-4', '25-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentySixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '26. 会担心别人对你的反应?',
        id: ['26-1', '26-2', '26-3','26-4', '26-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentySevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '27. 觉得你的病导致你和亲人之间的关系有了问题?',
        id: ['27-1', '27-2', '27-3','27-4', '27-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyEightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '28. 无法从配偶或伴侣得到你所需要的支持与帮忙?',
        id: ['28-1', '28-2', '28-3','28-4', '28-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      twentyNineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '29. 无法从家人或好友得到你所需要的支援与帮忙?',
        id: ['29-1', '29-2', '29-3','29-4', '29-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '30. 在白天会不知不觉的睡着了?',
        id: ['30-1', '30-2', '30-3','30-4', '30-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyOneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '31. 觉得注意力难以集中，譬如在阅读或看电视的时候?',
        id: ['31-1', '31-2', '313','31-4', '31-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyTwoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '32. 觉得你的记忆力不好?',
        id: ['32-1', '32-2', '32-3','32-4', '32-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyThreeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '33. 会做噩梦或由幻觉? ',
        id: ['33-1', '33-2', '33-3','33-4', '33-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyFourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '34. 说话有困难?',
        id: ['34-1', '34-2', '34-3','34-4', '34-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyFiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '35. 觉得无法和别人好好沟通?',
        id: ['35-1', '35-2','35-3', '35-4', '35-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtySixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '36. 觉得别人忽视你?',
        id: ['36-1', '36-2','36-3', '36-4', '36-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtySevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '37. 觉得肌肉会有疼痛的抽筋或痉挛?',
        id: ['37-1', '37-2','37-3', '37-4', '37-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyEightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '38. 关节或身体会疼痛?',
        id: ['38-1', '38-2','38-3', '38-4', '38-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      thirtyNineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '39. 觉得会有不舒服的冷热感?',
        id: ['39-1', '39-2', '39-3','39-4', '39-5'],
        content: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '从来没有;',
          '偶尔会;',
          '有时候会;',
          '时常会;',
          '总是会，或完全无法自主;',
        ],
      },
      // reference: '',
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 39,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '帕金森')
    localStorage.setItem('wordLast', '帕金森病生活质量评分量表(PDQ-39)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.result()
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explain =
          this.explain + element.attributes.grade.value * 1
      }
    },
    // 结果展示
    result () {
      this.totalPoints = `${this.explain}分`
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 39
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
.subTitle{
  font-family: 'Microsoft YaHei';
  font-weight: 500;
  font-size: 18px;
  color: #202020;
  margin: 20px 0;
}
</style>
