<template>
  <div>
    <form action="">
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter">
          <div ref="referenceOne" class="oneTopic">
            <p>一、视空间与执行功能</p>
            <SheetChange :scores="score" :information="oneTopicA"></SheetChange>
            <SheetChange :scores="score" :information="oneTopicB"></SheetChange>
            <MultipleChoice :scores="score" :information="oneTopicC"></MultipleChoice>
          </div>
          <div ref="referenceTwo" class="twoTopic">
            <p>二、 命名</p>
            <MultipleChoice :information="twoTopicA"></MultipleChoice>
          </div>
          <div class="threeTopic">
            <p>三、 记忆</p>
            <div>
              <p>
                1.
                检查者读出下列词语，而后由患者复述一遍。此环节重复两次，5分钟后进行回忆。（此环节不计分）
              </p>
              <p>面孔；天鹅绒；教堂；菊花；红色</p>
            </div>
          </div>
          <div ref="referenceFour" class="fourTopic">
            <p>四、 注意</p>
            <SheetChange :information="fourTopicA"></SheetChange>
            <SheetChange :information="fourTopicB"></SheetChange>
            <SheetChange :information="fourTopicC"></SheetChange>
            <SheetChange :information="fourTopicD"></SheetChange>
          </div>
          <div ref="referenceFive" class="fiveTopic">
            <p>五、 语言</p>
            <SheetChange :information="fiveTopicA"></SheetChange>
            <SheetChange :information="fiveTopicB"></SheetChange>
            <SheetChange :information="fiveTopicC"></SheetChange>
          </div>
          <div ref="referenceSix" class="sixTopic">
            <p>六、 抽象</p>
            <span>题前练习：检查者：“请您说说桔子和香蕉在什么方面相类似？”。如果患者回答的是一种具体特征（如：都有皮，或都能吃等），那么只能再提示一次：“请再换一种说法，他们在什么方面相类似？”如果患者仍未给出准确回答“水果”，则说：“您说的没错，也可以说他们都是水果。”但不要给出其他任何解释或说明。</span>
            <SheetChange :information="sixTopicA"></SheetChange>
            <SheetChange :information="sixTopicB"></SheetChange>
          </div>
          <div ref="referenceSeven" class="sevenTopic">
            <p>七、 延迟回忆</p>
            <MultipleChoice :information="sevenTopicA"></MultipleChoice>
          </div>
          <div ref="referenceEight" class="eightTopic">
            <p>八、 定向</p>
            <MultipleChoice :information="eightTopicA"></MultipleChoice>
          </div>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import SheetChange from '@/components/MiniTool/SheetChange.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import MultipleChoice from '@/components/MiniTool/MultipleChoice.vue'

export default {
  // 注册组件
  components: { Patient, SheetChange, ScoringDetails, MultipleChoice },
  // tdk
  head () {
    return {
      title: '蒙特利尔认知评估量表（MoCA） - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '蒙特利尔认知评估量表，MoCA由加拿大Nasreddine等根据临床经验并参考MMSE（简明精神状态检查）的认知项目和评分而制定。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '蒙特利尔认知评估量表, moca'
        }
      ]
    }
  },
  data () {
    return {
      oneTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 请患者按照从数字到汉字并逐渐升高的顺序画一条连线。例如：从数字1连向汉字甲，再连向数字2，并一直连下去，到汉字戊结束',
        id: ['1-1', '1-2'],
        content: [
          '完全按照“1-甲-2-乙-3-丙-4-丁-5-戊”的顺序进行连线且没有任何交叉线：1分；',
          '出现错误而没有立刻自我纠正：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '完全按照“1-甲-2-乙-3-丙-4-丁-5-戊”的顺序进行连线且没有任何交叉线;',
          '出现错误而没有立刻自我纠正;',
        ],
        describe: [],
        img: 'one',
        spreadhead: '一、视空间与执行功能',
      },
      oneTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title: '2. 请患者照着这幅图在纸上空白处再画一遍，并尽可能精确。',
        id: ['2-1', '2-2'],
        content: [
          '三维结构，所有的线都存在，无多余或缺漏。相对的边基本平行，长度基本一致（长方体或棱柱体也算正确）：1分；',
          '违反上个选项中的任何一条：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '三维结构，所有的线都存在，无多余或缺漏。相对的边基本平行，长度基本一致（长方体或棱柱体也算正确）;',
          '违反上个选项中的任何一条;',
        ],
        describe: [],
        img: 'two',
        spreadhead: '一、视空间与执行功能',
      },
      oneTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '3. 请患者在纸上空白处画一个钟表，填上所有的数字并指示出11点10分。（多选）',
        id: ['3-1', '3-2', '3-3', '3-4'],
        content: [
          '轮廓：表面必须是个圆形，允许有轻微的缺陷（如：圆没有闭合）：1分；',
          '数字：所有的数字必须完整且无多余的数字；数字顺序必须正确且在所属的象限内；可以是罗马数字；数字可以放在圆圈之外：1分；',
          '指针：必须有两个指针且一起指向正确的时间；时针必须明显短于分针；指针的中心交点必须在表内且接近于钟表的中心：1分；',
          '未满足前三个选项中的任何一项：0分。',
        ],
        grade: ['1', '1', '1', '0'],
        simpleContent: [
          '轮廓：表面必须是个圆形，允许有轻微的缺陷（如：圆没有闭合）;',
          '数字：所有的数字必须完整且无多余的数字；数字顺序必须正确且在所属的象限内；可以是罗马数字；数字可以放在圆圈之外;',
          '指针：必须有两个指针且一起指向正确的时间；时针必须明显短于分针；指针的中心交点必须在表内且接近于钟表的中心;',
          '未满足前三个选项中的任何一项;',
        ],
        spreadhead: '一、视空间与执行功能',
      },
      twoTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '1. 自左向右指着图片问患者所指动物的名字，每答对一个给1分。（多选）',
        id: ['4-1', '4-2', '4-3', '4-4'],
        content: [
          '狮子',
          '犀牛',
          '骆驼或单峰骆驼',
          '未答对前三个选项中的任何一项',
        ],
        grade: ['1', '1', '1', '0'],
        location: [
          'padding-left: 80px;',
          'padding-left: 140px;',
          'padding-left: 100px;',
        ],
        simpleContent: [
          '狮子;',
          '犀牛;',
          '骆驼或单峰骆驼;',
          '未答对前三个选项中的任何一项;',
        ],
        img: 'one',
        spreadhead: '二、 命名',
      },
      fourTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 检查者以每秒钟1个数字的顺序顺背数列【 2 1 8 5 4 】，患者照样背出',
        id: ['5-1', '5-2'],
        content: [
          '患者正确复述数列【 2 1 8 5 4 】：1分；',
          '患者复述过程中有缺漏、错误等现象：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '患者正确复述数列【 2 1 8 5 4 】;',
          '患者复述过程中有缺漏、错误等现象;',
        ],
        describe: [],
        spreadhead: '四、 注意',
      },
      fourTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '2. 检查者以每秒钟1个数字的顺序顺背数列【 7 4 2 】，患者照原数列倒序背出',
        id: ['6-1', '6-2'],
        content: [
          '患者正确倒序背出数列【 2 4 7 】：1分；',
          '患者复述过程中有缺漏、错误等现象：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '患者正确倒序背出数列【 2 4 7 】;',
          '患者复述过程中有缺漏、错误等现象;',
        ],
        describe: [],
        spreadhead: '四、 注意',
      },
      fourTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '3. 检查者以每秒钟1个的速度读出数字串【 5 2 1 3 9 4 1 1 8 0 6 2 1 5 1 9 4 5 1 1 1 4 1 9 0 5 1 1 2 】，并向患者说明：“每当我读到1的时候，您就拍一下手。当我读其他的数字时不要拍手。”（当读1的时候患者没有拍手，或读其他数字时患者拍手则为判断错误。)',
        id: ['7-1', '7-2'],
        content: [
          '患者判断完全正确或只有一次错误：1分；',
          '患者判断错误达两次或两次以上：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '患者判断完全正确或只有一次错误;',
          '患者判断错误达两次或两次以上;',
        ],
        describe: [],
        spreadhead: '四、 注意',
      },
      fourTopicD: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '4. 请患者从100开始减去一个7，而后从得数中再减去一个7，一直往下减，直到减满5次。（每一个减数都单独评定，如果患者减错了一次，而从这一个减数开始后续的减7都正确，则后续的正确减数要给分。例如，如果患者的回答是93-85-78-71-64，85是错误的，而其他的结果都正确，因此给3分。）',
        id: ['8-1', '8-2', '8-3', '8-4'],
        content: [
          '5次计算中，仅1次正确：1分；',
          '5次计算中，2-3次正确：2分；',
          '5次计算中，4-5次正确：3分；',
          '5次计算中，0次正确：0分。',
        ],
        grade: ['1', '2', '3', '0'],
        show: 'false',
        simpleContent: [
          '5次计算中，仅1次正确;',
          '5次计算中，2-3次正确;',
          '5次计算中，4-5次正确;',
          '5次计算中，0次正确;',
        ],
        describe: [],
        spreadhead: '四、 注意',
      },
      fiveTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 检查者说出词句1：“我只知道今天张亮是来帮过忙的人。”患者尽可能将词句原原本本地复述出来',
        id: ['9-1', '9-2'],
        content: ['复述准确：1分；', '复述过程出现词语的省略/替换/增加：0分。'],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: ['复述准确;', '复述过程出现词语的省略/替换/增加;'],
        describe: [],
        spreadhead: '五、 语言',
      },
      fiveTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '2. 检查者说出词句2：“狗在房间的时候，猫总是躲在沙发下面。”患者尽可能将词句原原本本地复述出来',
        id: ['10-1', '10-2'],
        content: ['复述准确：1分；', '复述过程出现词语的省略/替换/增加：0分。'],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: ['复述准确;', '复述过程出现词语的省略/替换/增加;'],
        describe: [],
        spreadhead: '五、 语言',
      },
      fiveTopicC: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '3. 请患者在1分钟内，尽可能快、尽可能多地说出TA所知道的动物的名称。（龙、凤凰、麒麟等神化动物也算正确。）',
        id: ['11-1', '11-2'],
        content: [
          '患者1分钟内说出的动物名称≥11个：1分；',
          '患者1分钟内说出的动物名称﹤11个：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '患者1分钟内说出的动物名称≥11个;',
          '患者1分钟内说出的动物名称﹤11个;',
        ],
        describe: [],
        spreadhead: '五、 语言',
      },
      sixTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 219px;',
        title:
          '1. 请患者解释词组1【火车-自行车】在什么方面相类似，或者说有什么共性',
        id: ['12-1', '12-2'],
        content: [
          '“运输工具；交通工具；旅行用的”等相关表述：1分；',
          '“都有轮子”等相关表述：0分。',
        ],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '“运输工具；交通工具；旅行用的”等相关表述;',
          '“都有轮子”等相关表述;',
        ],
        describe: [],
        spreadhead: '六、 抽象',
      },
      sixTopicB: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 219px;',
        title:
          '2. 请患者解释词组2【手表-尺子】在什么方面相类似，或者说有什么共性',
        id: ['13-1', '13-2'],
        content: ['“测量仪器；测量用的”等相关表述：1分；', '“都有数字”等相关表述：0分。'],
        grade: ['1', '0'],
        show: 'false',
        simpleContent: [
          '“测量仪器；测量用的”等相关表述;',
          '“都有数字”等相关表述;',
        ],
        describe: [],
        spreadhead: '六、 抽象',
      },
      sevenTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '1. 请患者再尽量回忆第三题里的词语，在未经提示下自由回忆正确的词，每词给1分。（多选）',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5', '14-6'],
        content: [
          '面孔',
          '天鹅绒',
          '教堂',
          '菊花',
          '红色',
          '未答对上述选项中任何一项',
        ],
        grade: ['1', '1', '1', '1', '1', '0'],
        simpleContent: [
          '面孔;',
          '天鹅绒;',
          '教堂;',
          '菊花;',
          '红色;',
          '未答对上述选项中任何一项;',
        ],
        spreadhead: '七、 延迟回忆',
      },
      eightTopicA: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        title:
          '1. 检查者向患者提问当天的具体日期及地点，每正确回答一项给1分。（多选）（如果患者回答不完整，检查者可以就选项分别提示患者，患者必须回答精确的日期和地点[医院、诊所、办公室的名称]。日期上多一天或少一天都算错误，不给分。）',
        id: ['15-1', '15-2', '15-3', '15-4', '15-5', '15-6', '15-7'],
        content: [
          '年份',
          '月份',
          '日期',
          '星期几',
          '地点',
          '城市',
          '未答对上述选项中任何一项',
        ],
        grade: ['1', '1', '1', '1', '1', '1', '0'],
        simpleContent: [
          '年份;',
          '月份;',
          '日期;',
          '星期几;',
          '地点;',
          '城市;',
          '未答对上述选项中任何一项;',
        ],
        spreadhead: '八、 定向',
      },
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 15,
      // 总分数
      totalPoints: 0,
      choiceBoxs: [[[], []], [], [], [], [], [], []],
      topicExcessiveO: 0,
      topicExcessiveT: 0,
      score: {
        topicOne: 0,
        topicTwo: 0,
        topicFour: 0,
        topicFive: 0,
        topicSix: 0,
        topicSeven: 0,
        topicEight: 0,
      },
      choiceBox: [],
      allInput: [],
      optionMessage: []
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '蒙特利尔认知评估')
  },

  mounted () {
    // 题目一
    for (let i = 1; i < this.$refs.referenceOne.children.length; i++) {
      if (i === 3) {
        for (
          let j = 0;
          j <
          this.$refs.referenceOne.children[i].children[0].children[1].children
            .length;
          j++
        ) {
          this.choiceBoxs[0][1].push(
            this.$refs.referenceOne.children[i].children[0].children[1]
              .children[j].children[0]
          )
        }
      } else {
        for (
          let j = 0;
          j <
          this.$refs.referenceOne.children[i].children[0].children[0]
            .children[1].children.length;
          j++
        ) {
          this.choiceBoxs[0][0].push(
            this.$refs.referenceOne.children[i].children[0].children[0]
              .children[1].children[j].children[0]
          )
        }
      }
    }
    // 题目二
    for (
      let i = 0;
      i <
      this.$refs.referenceTwo.children[1].children[0].children[2].children
        .length;
      i++
    ) {
      this.choiceBoxs[1].push(
        this.$refs.referenceTwo.children[1].children[0].children[2].children[i]
          .children[0]
      )
    }
    // 题目四
    for (let i = 1; i < this.$refs.referenceFour.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceFour.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[2].push(
          this.$refs.referenceFour.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }
    // 题目五
    for (let i = 1; i < this.$refs.referenceFive.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceFive.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[3].push(
          this.$refs.referenceFive.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }
    // 题目六
    for (let i = 2; i < this.$refs.referenceSix.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceSix.children[i].children[0].children[0].children[1]
          .children.length;
        j++
      ) {
        this.choiceBoxs[4].push(
          this.$refs.referenceSix.children[i].children[0].children[0]
            .children[1].children[j].children[0]
        )
      }
    }
    // 题目七
    for (let i = 1; i < this.$refs.referenceSeven.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceSeven.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.choiceBoxs[5].push(
          this.$refs.referenceSeven.children[i].children[0].children[1]
            .children[j].children[0]
        )
      }
    }
    // 题目八
    for (let i = 1; i < this.$refs.referenceEight.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.referenceEight.children[i].children[0].children[1].children
          .length;
        j++
      ) {
        this.choiceBoxs[6].push(
          this.$refs.referenceEight.children[i].children[0].children[1]
            .children[j].children[0]
        )
      }
    }
    this.allInput = this.choiceBoxs[0][0].concat(this.choiceBoxs[0][1], this.choiceBoxs[1], this.choiceBoxs[2], this.choiceBoxs[3], this.choiceBoxs[4], this.choiceBoxs[5], this.choiceBoxs[6])

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.allInput.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.allInput.map((item) => {
      // 如果 处于选中状态 就处理以下逻辑
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
        this.totalPoints += item.attributes.grade.value * 1
      }
    })
  },

  methods: {
    // 公共逻辑
    checkedOne (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
        this.topicExcessiveO += item.attributes.grade.value * 1
      }
    },
    checkedTwo (item) {
      if (item.checked === true) {
        this.topicExcessiveT += item.attributes.grade.value * 1
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        } else if (item.id === '3-4') {
          this.topicExcessiveT = 0
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
      }
    },
    checkedThree (item) {
      if (item.checked === true) {
        this.score.topicTwo += item.attributes.grade.value * 1
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        } else if (item.id === '4-4') {
          this.score.topicTwo = 0
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
      }
    },
    checkedFour (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
        this.score.topicFour += item.attributes.grade.value * 1
      }
    },
    checkedFive (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
        this.score.topicFive += item.attributes.grade.value * 1
      }
    },
    checkedSix (item) {
      if (item.checked === true) {
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
        this.score.topicSix += item.attributes.grade.value * 1
      }
    },
    checkedSeven (item) {
      if (item.checked === true) {
        this.score.topicSeven += item.attributes.grade.value * 1
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        } else if (item.id === '14-6') {
          this.score.topicSeven = 0
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
      }
    },
    checkedEight (item) {
      if (item.checked === true) {
        this.score.topicEight += item.attributes.grade.value * 1
        if (this.choiceBox.indexOf(item.id.slice(0, 2)) === -1) {
          this.choiceBox.push(item.id.slice(0, 2))
        } else if (item.id === '15-7') {
          this.score.topicEight = 0
        }
        this.selected = this.choiceBox.length
        this.unselected = 15 - this.selected
      }
    },
    result () {
      this.totalPoints =
        this.score.topicOne +
        this.score.topicTwo +
        this.score.topicFour +
        this.score.topicFive +
        this.score.topicSix +
        this.score.topicSeven +
        this.score.topicEight
    },
    // 选项的点击事件
    getTatals (e) {
      // 初始化数据
      // this.score.topicOne = 0
      this.score = {
        topicOne: 0,
        topicTwo: 0,
        topicFour: 0,
        topicFive: 0,
        topicSix: 0,
        topicSeven: 0,
        topicEight: 0,
      }
      this.totalPoints = 0
      this.unselected = 15
      this.selected = 0
      this.topicExcessiveO = 0
      this.topicExcessiveT = 0
      this.choiceBox = []
      this.optionMessage = []
      // 遍历数组
      this.choiceBoxs[0][0].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedOne(item)
      })
      // 小三题 多选清空逻辑
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '3-4' &&
        e.target.attributes.id.value.slice(0, 2) === '3-'
      ) {
        this.choiceBoxs[0][1][3].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '3-4' &&
        e.target.attributes.id.value.slice(0, 2) === '3-'
      ) {
        for (let i = 0; i < this.choiceBoxs[0][1].length - 1; i++) {
          this.choiceBoxs[0][1][i].checked = false
        }
      }
      this.choiceBoxs[0][1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedTwo(item)
      })
      this.score.topicOne = this.topicExcessiveO + this.topicExcessiveT
      // 多选 清空逻辑
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '4-4' &&
        e.target.attributes.id.value.slice(0, 2) === '4-'
      ) {
        this.choiceBoxs[1][3].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '4-4' &&
        e.target.attributes.id.value.slice(0, 2) === '4-'
      ) {
        for (let i = 0; i < this.choiceBoxs[0][1].length - 1; i++) {
          this.choiceBoxs[1][i].checked = false
        }
      }
      this.choiceBoxs[1].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedThree(item)
      })
      this.choiceBoxs[2].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedFour(item)
      })
      this.choiceBoxs[3].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedFive(item)
      })
      this.choiceBoxs[4].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedSix(item)
      })
      // 多选 清空逻辑
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '14-6' &&
        e.target.attributes.id.value.slice(0, 2) === '14'
      ) {
        this.choiceBoxs[5][5].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '14-6' &&
        e.target.attributes.id.value.slice(0, 2) === '14'
      ) {
        for (let i = 0; i < this.choiceBoxs[5].length - 1; i++) {
          this.choiceBoxs[5][i].checked = false
        }
      }
      this.choiceBoxs[5].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedSeven(item)
      })
      // 多选 清空逻辑
      if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value !== '15-7' &&
        e.target.attributes.id.value.slice(0, 2) === '15'
      ) {
        this.choiceBoxs[6][6].checked = false
      } else if (
        e.target.attributes.id !== undefined &&
        e.target.attributes.id.value === '15-7' &&
        e.target.attributes.id.value.slice(0, 2) === '15'
      ) {
        for (let i = 0; i < this.choiceBoxs[6].length - 1; i++) {
          this.choiceBoxs[6][i].checked = false
        }
      }
      this.choiceBoxs[6].map((item) => {
        this.optionMessage.push({
          choice: item.checked,
        })
        this.checkedEight(item)
      })
      this.result()
      this.$store.commit('minitool/setScore', this.score)
      localStorage.setItem('scoringDetails', JSON.stringify(this.optionMessage))
    },

    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  position: relative;
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .matter {
    p {
      font-family: 'Microsoft YaHei';
      font-weight: 700;
      font-size: 22px;
      color: #202020;
      margin: 35px 0 0px 0;
    }

    span {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #888888;
    }

    .threeTopic {
      div {
        width: 877px;
        margin-top: 16px;
        padding: 3px 16px;
        box-sizing: border-box;
        border-radius: 6px;
        background-color: rgba(5, 129, 206, 0.03);

        p:nth-of-type(1) {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }

        p:nth-of-type(2) {
          font-weight: 400;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }

  .result {
    width: 500px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
