<template>
  <div>
    <form>
      <!-- 患者信息组件 -->
      <Patient></Patient>
      <!-- particulars 详情 -->
      <div class="particulars">
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容详情 -->
        <div @click="getTatals($event)" class="matter" ref="reference">
          <Options :information="oneTopic"></Options>
          <Options :information="twoTopic"></Options>
          <Options :information="threeTopic"></Options>
          <Options :information="fourTopic"></Options>
          <Options :information="fiveTopic"></Options>
          <Options :information="sixTopic"></Options>
          <Options :information="sevenTopic"></Options>
          <Options :information="eightTopic"></Options>
          <Options :information="nineTopic"></Options>
          <Options :information="tenTopic"></Options>
          <Options :information="elevenTopic"></Options>
          <Options :information="twelveTopic"></Options>
          <Options :information="thirteenTopic"></Options>
          <Options :information="fourteenTopic"></Options>
          <Options :information="fifteenTopic"></Options>
          <Options :information="sixteenTopic"></Options>
          <Options :information="seventeenTopic"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ totalPoints }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '汉密顿抑郁量表(HAMD) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '汉密尔顿抑郁量表是临床上评定抑郁状态时应用得最为普遍的量表。本量表有 17 项、21项和24 项等3种版本，这里介绍的是 24 项版本。这些项目包括抑郁所涉与的各种症状，并可归纳为7类因子结构。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '汉密顿抑郁量表, 汉密尔顿抑郁量表, hamd'
        }
      ]
    }
  },
  data () {
    return {
      oneTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '1. 抑郁情绪',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5'],
        content: [
          '1) 无：0分;',
          '2) 只在问到时才诉述：1分;',
          '3) 在访谈中自发地描述：2分;',
          '4) 不用言语也可以从表情、姿势、声音或欲哭中流露出这种情绪：3分;',
          '5) 病人的自发言语和非语言表达（表情、动作）几乎完全表现为这种情绪：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '只在问到时才诉述;',
          '在访谈中自发地描述;',
          '不用言语也可以从表情、姿势、声音或欲哭中流露出这种情绪;',
          '病人的自发言语和非语言表达（表情、动作）几乎完全表现为这种情绪;',
        ],
      },
      twoTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '2. 有罪感',
        id: ['2-1', '2-2', '2-3', '2-4', '2-5'],
        content: [
          '1) 无：0分;',
          '2) 责备自己，感到自己已连累他人：1分;',
          '3) 认为自己犯了罪，或反复思考以往的过失和错误：2分;',
          '4) 认为目前的疾病是对自己错误的惩罚，或有罪恶妄想：3分;',
          '5) 罪恶妄想伴有指责或威胁性幻想：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '责备自己，感到自己已连累他人;',
          '认为自己犯了罪，或反复思考以往的过失和错误;',
          '认为目前的疾病是对自己错误的惩罚，或有罪恶妄想;',
          '罪恶妄想伴有指责或威胁性幻想;',
        ],
      },
      threeTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '3. 自杀',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '1) 无：0分;',
          '2) 觉得活着没有意义：1分;',
          '3) 希望自己已经死去，或常想与死亡有关的事：2分;',
          '4) 消极观念（自杀念头）：3分;',
          '5) 有严重自杀行为：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '觉得活着没有意义;',
          '希望自己已经死去，或常想与死亡有关的事;',
          '消极观念（自杀念头）;',
          '有严重自杀行为;',
        ],
      },
      fourTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '4. 入睡困难',
        id: ['4-1', '4-2', '4-3'],
        content: [
          '1) 无：0分;',
          '2) 主诉入睡困难，上床半小时后仍不能入睡（要注意平时病人入睡的时间）：1分;',
          '3) 主诉每晚均有入睡困难：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '主诉入睡困难，上床半小时后仍不能入睡（要注意平时病人入睡的时间）;',
          '主诉每晚均有入睡困难;',
        ],
      },
      fiveTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '5. 睡眠不深',
        id: ['5-1', '5-2', '5-3'],
        content: [
          '1) 无：0分;',
          '2) 睡眠浅，多恶梦幻：1分;',
          '3) 半夜（晚12点钟以前）曾醒来（不包括上厕所）：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '睡眠浅，多恶梦幻;',
          '半夜（晚12点钟以前）曾醒来（不包括上厕所）;',
        ],
      },
      sixTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '6. 早醒',
        id: ['6-1', '6-2', '6-3'],
        content: [
          '1) 无：0分;',
          '2) 有早醒，比平时早醒1小时，但能重新入睡（应排除平时的习惯）：1分;',
          '3) 早醒后无法重新入睡：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '有早醒，比平时早醒1小时，但能重新入睡（应排除平时的习惯）;',
          '早醒后无法重新入睡;',
        ],
      },
      sevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '7. 工作和兴趣',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '1) 无：0分;',
          '2) 提问时才诉说：1分;',
          '3) 自发地直接或间接表达对活动、工作或学习失去兴趣，如感到没精打彩、犹豫不决，不能坚持或需强迫自己去工作或劳动：2分;',
          '4) 活动时间减少或成效下降，住院病人每天参加病房劳动或娱乐不满3小时：3分;',
          '5) 因目前的疾病而停止工作，住院病者不参加任何活动或者没有他人帮助便不能完成病室日常事务（注意不能凡住院就打4分）：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '提问时才诉说;',
          '自发地直接或间接表达对活动、工作或学习失去兴趣，如感到没精打彩、犹豫不决，不能坚持或需强迫自己去工作或劳动;',
          '活动时间减少或成效下降，住院病人每天参加病房劳动或娱乐不满3小时;',
          '因目前的疾病而停止工作，住院病者不参加任何活动或者没有他人帮助便不能完成病室日常事务（注意不能凡住院就打4分）;',
        ],
      },
      eightTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '8. 阻滞（思维和言语缓慢、注意力难以集中、主动性减退）',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '1) 无：0分;',
          '2) 精神检查中发现轻度阻滞：1分;',
          '3) 精神检查中发现明显阻滞：2分;',
          '4) 精神检查进行困难：3分;',
          '5) 完全不能回答问题（木僵）：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '精神检查中发现轻度阻滞;',
          '精神检查中发现明显阻滞;',
          '精神检查进行困难;',
          '完全不能回答问题（木僵）;',
        ],
      },
      nineTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '9. 激越',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '1) 无激越：0分;',
          '2) 检查时有些心神不定：1分;',
          '3) 明显心神不定或小动作多;',
          '4) 不能静坐，检查中曾起立：3分;',
          '5) 搓手、咬手指、头发、咬嘴唇：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无激越;',
          '检查时有些心神不定;',
          '明显心神不定或小动作多;',
          '不能静坐，检查中曾起立;',
          '搓手、咬手指、头发、咬嘴唇;',
        ],
      },
      tenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 519px;',
        title: '10.精神性焦虑',
        id: ['10-1', '10-2', '10-3', '10-4', '10-5'],
        content: [
          '1) 无：0分;',
          '2) 问时诉述：1分;',
          '3) 自发地表达：2分;',
          '4) 表情和言语流露出明显忧虑：3分;',
          '5) 明显惊恐：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '问时诉述;',
          '自发地表达;',
          '表情和言语流露出明显忧虑;',
          '明显惊恐;',
        ],
      },
      elevenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title:
          '11.躯体性焦虑（指焦虑的生理症状，包括：口干、腹胀、腹泻、打嗝、腹绞痛、心悸、头痛、过度换气和叹气，以及尿频和出汗）',
        id: ['11-1', '11-2', '11-3', '11-4', '11-5'],
        content: [
          '1) 无：0分;',
          '2) 轻度：1分;',
          '3) 中度，有肯定的上述症状：2分;',
          '4) 重度，上述症状严重，影响生活或需要处理：3分;',
          '5) 严重影响生活和活动：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '轻度;',
          '中度，有肯定的上述症状;',
          '重度，上述症状严重，影响生活或需要处理;',
          '严重影响生活和活动;',
        ],
      },
      twelveTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '12.胃肠道症状',
        id: ['12-1', '12-2', '12-3'],
        content: [
          '1) 无：0分;',
          '2) 食欲减退，但不需他人鼓励便自行进食：1分;',
          '3) 进食需他人催促或请求和需要应用泻药或助消化药：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '食欲减退，但不需他人鼓励便自行进食;',
          '进食需他人催促或请求和需要应用泻药或助消化药;',
        ],
      },
      thirteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '13.全身症状',
        id: ['13-1', '13-2', '13-3'],
        content: [
          '1) 无：0分;',
          '2) 四肢、背部或颈部沉重感，背痛、头痛、肌肉疼痛，全身乏力或疲倦：1分;',
          '3) 症状明显：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '四肢、背部或颈部沉重感，背痛、头痛、肌肉疼痛，全身乏力或疲倦;',
          '症状明显;',
        ],
      },
      fourteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '14.性症状（指性欲减退，月经紊乱等）',
        id: ['14-1', '14-2', '14-3'],
        content: [
          '1) 无，或不能肯定，或该项对被评者不适合（不计入总分）：0分;',
          '2) 轻度：1分;',
          '3) 重度：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无，或不能肯定，或该项对被评者不适合（不计入总分）;',
          '轻度;',
          '重度;',
        ],
      },
      fifteenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 519px;',
        title: '15.疑病',
        id: ['15-1', '15-2', '15-3', '15-4', '15-5'],
        content: [
          '1) 无：0分;',
          '2) 对身体过分关注：1分;',
          '3) 反复考虑健康问题：2分;',
          '4) 有疑病妄想：3分;',
          '5) 伴有幻觉的疑病妄想：4分。',
        ],
        grade: ['0', '1', '2', '3', '4'],
        simpleContent: [
          '无;',
          '对身体过分关注;',
          '反复考虑健康问题;',
          '有疑病妄想;',
          '伴有幻觉的疑病妄想;',
        ],
      },
      sixteenTopic: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 319px;',
        title: '16.体重减轻',
        id: ['16-1', '16-2', '16-3'],
        content: [
          '1) 无：0分;',
          '2) 患者主诉可能有体重减轻，或一周内体重减轻超过0.5公斤：1分;',
          '3) 肯定体重减轻，或一周内体重减轻超过1公斤：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '无;',
          '患者主诉可能有体重减轻，或一周内体重减轻超过0.5公斤;',
          '肯定体重减轻，或一周内体重减轻超过1公斤;',
        ],
      },
      seventeenTopic: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 319px;',
        title: '17.自知力',
        id: ['17-1', '17-2', '17-3'],
        content: [
          '1) 知道自己有病，表现为忧郁：0分;',
          '2) 知道自己有病，但归咎伙食太差、环境问题、工作过忙、病毒感染或需要休息：1分;',
          '3) 完全否认有病：2分。',
        ],
        grade: ['0', '1', '2'],
        simpleContent: [
          '知道自己有病，表现为忧郁;',
          '知道自己有病，但归咎伙食太差、环境问题、工作过忙、病毒感染或需要休息;',
          '完全否认有病;',
        ],
      },
      // reference: '',
      // 已选个数
      selected: 0,
      // 未选个数
      unselected: 17,
      // 总分数
      totalPoints: 0,
      explain: 0,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '汉密顿抑郁量表(HAMD)')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
    this.result()
  },

  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.explain =
          this.explain + element.attributes.grade.value * 1
      }
    },
    // 结果展示
    result () {
      if (this.explain >= 0 && this.explain <= 6) {
        this.totalPoints = `${this.explain}分，无抑郁症状`
      } else if (this.explain >= 7 && this.explain <= 16) {
        this.totalPoints = `${this.explain}分，可能有抑郁症状`
      } else if (this.explain >= 17 && this.explain <= 23) {
        this.totalPoints = `${this.explain}分，轻度或中度抑郁`
      } else if (this.explain >= 24) {
        this.totalPoints = `${this.explain}分，严重抑郁`
      }
    },
    // 选项 点击事件
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.explain = 0
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 17
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      this.result()
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },

    // 保存评分
    save () {
      this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    input {
      border: none;
    }
  }

  .btn:hover {
    background-color: #0581ce;
    color: #ffffff;
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
