<template>
  <div>
    <Patient></Patient>
    <div class="particulars">
      <!-- 评分详情 -->
      <ScoringDetails></ScoringDetails>
      <!-- 评分内容 -->
      <!-- 文本输入框 -->
      <div class="topic" v-for="data in inputTextData" :key="data.id">
        <p class="title">{{ data.title }}</p>
        <input
          @input="updateValue(data.id)"
          type="number"
          placeholder="请输入时间"
          id="1"
          v-model="data.moduleValue"
        />
        <label for="1">{{ data.explain }}</label>
      </div>
      <div @click="getTargetInput" ref="reference">
        <Options
          v-for="data in inputRadioData"
          :key="data.title"
          :information="data"
        ></Options>
      </div>
      <div class="score-details">
        <div
          class="details"
          v-for="(item, index) in scoreDetails"
          :key="item.id"
        >
          <p>{{ item.detail }}</p>
          <p>{{ item.score || 0 }}{{ index !== 7 ? '分' : '' }}</p>
        </div>
      </div>
      <!-- 结果展示 -->
      <div class="result">
        <div class="grade">
          结果：
          <span v-if="selectedSet.size === 0">暂无</span>
          <span v-else>{{ totalPoints }}</span>
        </div>
        <div v-show="unselected !== 0" class="explain">
          已选择
          <span>{{ selectedSet.size }}</span>
          个评分项，尚有
          <span>{{ unselected }}</span>
          个评分项未选择
        </div>
      </div>
      <!-- 保存评分 -->
      <div class="save">
        <div
          :class="{ scoreBtn: unselected === 0 }"
          @click="saveScore"
          class="btn"
        >
          保存评分
        </div>
      </div>
    </div>
    <PromptPopup
      :type="popupType"
      :show="popupShow"
      @closePopup="closePopup"
      @directSave="directSave"
    ></PromptPopup>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { saveToolScore } from '@/api/minitool'
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import PromptPopup from '@/components/MiniTool/PromptPopup.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, ScoringDetails, Options, PromptPopup },
  // 配置TDK
  head() {
    return {
      title: '匹兹堡睡眠质量指数量表PSQI - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            '为了进行脑梗塞病人的血液稀释治疗效果的研究，SSS评分分为预后评分和长期随访评分。最初预后评定项目包括意识水平，眼活动和瘫痪的严重性；随访评分项目包括上下肢和手的肌力，定向力，语言，面瘫和步态。',
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '面瘫, scandinavian stroke scale, 上肢肌力, 手的肌力, 眼球运动, 意识, SSS, SNSS',
        },
      ],
    }
  },
  computed: {
    ...mapState('minitool', ['PatientInformations']),
    inputRadioData() {
      return [
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5a. 近1个月，入睡困难(30分钟内不能入睡)',
          id: ['5a-1', '5a-2', '5a-3', '5a-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5b 近1个月，夜间易醒或早醒',
          id: ['5b-1', '5b-2', '5b-3', '5b-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5c 近1个月，夜间去厕所',
          id: ['5c-1', '5c-2', '5c-3', '5c-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5d. 近1个月，呼吸不畅',
          id: ['5d-1', '5d-2', '5d-3', '5d-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5e. 近1个月，咳嗽或鼾声高',
          id: ['5e-1', '5e-2', '5e-3', '5e-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5f. 近1个月，感觉冷',
          id: ['5f-1', '5f-2', '5f-3', '5f-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5g. 近1个月，感觉热',
          id: ['5g-1', '5g-2', '5g-3', '5g-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5h. 近1个月，做恶梦',
          id: ['5h-1', '5h-2', '5h-3', '5h-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5i. 近1个月，疼痛不适',
          id: ['5i-1', '5i-2', '5i-3', '5i-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '5j. 近1个月，其它影响睡眠的事情',
          id: ['5j-1', '5j-2', '5j-3', '5j-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '6.近1个月，总的来说，您认为自己的睡眠质量',
          id: ['6-1', '6-2', '6-3', '6-4'],
          content: ['1) 很好;', '2) 较好;', '3) 较差;', '4) 很差;'],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['很好;', '较好;', '较差;', '很差;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '7. 近1个月，您用药物催眠的情况',
          id: ['7-1', '7-2', '7-3', '7-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '8. 近1个月，您常感到困倦吗？',
          id: ['8-1', '8-2', '8-3', '8-4'],
          content: [
            '1) 无;',
            '2) <１次／周;',
            '3) １～２次／周;',
            '4) ≥３次／周;',
          ],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['无;', '<１次／周;', '１～２次／周;', '≥３次／周;'],
        },
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 400px;',
          title: '9.近1个月，您做事情的精力不足吗',
          id: ['9-1', '9-2', '9-3', '9-4'],
          content: ['1) 没有;', '2) 偶尔有;', '3) 有时有;', '4) 经常有;'],
          grade: ['0', '1', '2', '3'],
          simpleContent: ['没有;', '偶尔有;', '有时有;', '经常有;'],
        },
      ]
    },
    inputTextData() {
      return [
        {
          id: '1',
          title: '1.近1个月，晚上上床睡觉通常（ ）点钟',
          explain: '如22，以24小时计时',
          moduleValue: '',
        },
        {
          id: '2',
          title: '2.近1个月，从上床到入睡通常需要（ ）分钟',
          explain: '',
          moduleValue: '',
        },
        {
          id: '3',
          title: '3.近1个月，通常早上（ ）点起床',
          explain: '',
          moduleValue: '',
        },
        {
          id: '4',
          title: '4.近1个月，每夜通常实际睡眠（ ）小时(不等于卧床时间)',
          explain: '',
          moduleValue: '',
        },
      ]
    },
  },
  data() {
    return {
      selectedSet: new Set(),
      selectDetails: {},
      totalPoints: 0,
      unselected: 18,
      resultString: '',
      popupType: '0',
      popupShow: false,
      selectOptions: {},
      scoreDetails: [
        {
          id: 1,
          detail: '睡眠质量得分:',
          score: '',
        },
        {
          id: 2,
          detail: '入睡时间得分:',
          score: '',
        },
        {
          id: 3,
          detail: '睡眠时间得分:',
          score: '',
        },
        {
          id: 4,
          detail: '睡眠效率得分:',
          score: '',
        },
        {
          id: 5,
          detail: '睡眠障碍得分:',
          score: '',
        },
        {
          id: 6,
          detail: '催眠药物得分:',
          score: '',
        },
        {
          id: 7,
          detail: '日间功能障碍得分:',
          score: '',
        },
        {
          id: 8,
          detail: 'PSQI 总分:',
          score: '',
        },
      ],
      slTiScoreMap: new Map(),
      slEfScoreMap: new Map(),
      slObScoreMap: new Map(),
      daDyScoreMap: new Map(),
    }
  },

  beforeMount() {
    localStorage.setItem('className', '通用工具')
    localStorage.setItem('wordLast', '匹兹堡睡眠质量指数量表PSQI')
  },

  mounted() {
    const userSelected = JSON.parse(localStorage.getItem('userSelected'))

    if (userSelected) {
      Object.entries(userSelected).forEach(([key, { id }]) => {
        const selectedInput = document.getElementById(id)
        if (selectedInput) {
          selectedInput.checked = true
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window,
          })
          selectedInput.dispatchEvent(clickEvent)
        }
      })
      localStorage.removeItem('userSelected')
    }
  },

  methods: {
    ...mapMutations('minitool', ['setPatientInformations']),
    updateValue(id) {
      this.selectedSet.add(id)
      const data = this.inputTextData.find((item) => item.id === id)
      this.selectDetails[id] = {
        title: data.title,
        option: data.moduleValue,
        grade: '',
      }
      this.unselected = 18 - this.selectedSet.size
      id === '2' && this.getSlTiScore(id, data.moduleValue)
      id === '4' && this.getSleTiScore(data.moduleValue)
      if (id === '1' || id === '3' || id === '4') {
        this.getSlEfScore(id, data.moduleValue)
      }
      this.unselected === 0 && this.getAllScore()
    },
    getTargetInput(event) {
      if (event.target.nodeName !== 'INPUT') return

      const { target } = event
      const { attributes } = target
      const title = target.name
      const id = target.id.split('-')[0]
      const grade = attributes.grade.value
      const option = attributes.title.value
      this.selectOptions[id] = { id: target.id, type: 'radio', value: true }

      this.selectedSet.add(id)
      this.selectDetails[id] = { title, option, grade }
      this.unselected = 18 - this.selectedSet.size
      id === '6' && this.getSlQuScore(grade)
      id === '5a' && this.getSlTiScore(id, grade)
      if (id.includes('5') && !id.includes('a')) {
        this.getSlObScore(id, grade)
      }
      id === '7' && this.getHyMeScore(grade)
      if (id === '8' || id === '9') {
        this.getDaDyScore(id, grade)
      }
      this.unselected === 0 && this.getAllScore()
    },
    getSlQuScore(score) {
      this.scoreDetails[0].score = score
    },
    getSlTiScore(id, score) {
      const value = Number(score)
      let twoScore = 0
      if (id === '2') {
        if (value <= 15 || value === '') {
          twoScore = 0
        } else if (value <= 30) {
          twoScore = 1
        } else if (value < 60) {
          twoScore = 2
        } else if (value >= 60) {
          twoScore = 3
        }
        this.slTiScoreMap.set(id, twoScore)
      } else if (id === '5a') {
        this.slTiScoreMap.set(id, value)
      }
      const result = Array.from(this.slTiScoreMap.values()).reduce(
        (sum, score) => sum + score,
        0
      )
      this.scoreDetails[1].score = result
    },
    getSleTiScore(time) {
      const value = Number(time)
      if (isNaN(value) || value === '') {
        this.scoreDetails[2].score = 0
        return
      }
      let result = 0
      if (value >= 7) {
        result = 0
      } else if (value > 6) {
        result = 1
      } else if (value >= 5) {
        result = 2
      } else {
        result = 3
      }
      this.scoreDetails[2].score = result
    },
    getSlEfScore(id, time) {
      if (time === '') {
        this.slEfScoreMap.delete(id)
        return
      }
      this.slEfScoreMap.set(id, time)
      if (this.slEfScoreMap.size !== 3) return
      const value4 = this.slEfScoreMap.get('4')
      const value3 = this.slEfScoreMap.get('3')
      const value1 = this.slEfScoreMap.get('1')
      const result = (value4 / (value3 - value1)) * 100
      let score
      if (result > 85) {
        score = 0
      } else if (result > 75) {
        score = 1
      } else if (result >= 65) {
        score = 2
      } else {
        score = 3
      }
      this.scoreDetails[3].score = score
    },
    getSlObScore(id, score) {
      this.slObScoreMap.set(id, Number(score))
      const result = [...this.slObScoreMap.values()].reduce(
        (sum, score) => sum + score,
        0
      )
      let allScore =
        result === 0
          ? 0
          : result <= 9
          ? 1
          : result <= 18
          ? 2
          : result <= 27
          ? 3
          : 0
      this.scoreDetails[4].score = allScore
    },
    getHyMeScore(score) {
      this.scoreDetails[5].score = Number(score)
    },
    getDaDyScore(id, score) {
      this.daDyScoreMap.set(id, Number(score))
      const result = Array.from(this.daDyScoreMap.values()).reduce(
        (sum, score) => sum + score,
        0
      )
      let allScore = 0
      if (result === 0) {
        allScore = 0
      } else if (result >= 1 && result <= 2) {
        allScore = 1
      } else if (result >= 3 && result <= 4) {
        allScore = 2
      } else if (result >= 5 && result <= 6) {
        allScore = 3
      }
      this.scoreDetails[6].score = allScore
    },
    getAllScore() {
      this.resultString = ''
      const dataList = [...this.scoreDetails]
      dataList.pop()

      const allScore = dataList.reduce((sum, item) => sum + item.score, 0)
      let result = ''
      if (allScore <= 10) {
        result = '睡眠质量还行'
      } else if (allScore >= 11 && allScore <= 15) {
        result = '睡眠质量一般'
      } else if (allScore >= 16) {
        result = '睡眠质量很差'
      }
      this.scoreDetails[7].score = result
      this.totalPoints = result

      for (const key in this.selectDetails) {
        const { title, option, grade } = this.selectDetails[key]
        this.resultString += `{${title}:${option},分数${grade}}`
      }
    },
    // 保存评分
    saveScore(event) {
      if (!event.target.classList.contains('scoreBtn')) return
      const validationResult = this.validateInputs()
      if (validationResult) {
        this.popupType = validationResult
        this.popupShow = true
      }
      const {
        age,
        gender,
        diagnose: diagnosis,
        name,
      } = this.PatientInformations
      const params = {
        age: age !== '未知' ? age : '',
        gender: gender !== '未知' ? gender : '',
        diagnosis,
        name,
        scoreResult: this.totalPoints,
        scoreTitle: '匹兹堡睡眠质量指数量表PSQI',
        scoreDetail: this.resultString,
        scoreType: '',
        userId: this.$cookies.get('medtion_user_only_sign')?.id,
      }
      this.$axios.$request(saveToolScore(params)).then((res) => {
        if (res.code === 1) {
          this.$router.push('/minitool/page_save')
        }
      })
    },

    closePopup() {
      this.popupShow = false
    },

    directSave() {
      this.popupShow = false
      const { age, gender, diagnose, name } = this.PatientInformations
      this.setPatientInformations({
        age: age || '未知',
        name: name || '未知',
        gender: gender || '未知',
        diagnose: diagnose || '未知',
      })
    },

    // 统一验证用户信息
    validateInputs() {
      if (!this.checkPatientInformation()) return '0' // 信息不完整
      if (!this.checkAge()) return '1' // 年龄不正确
      if (!this.checkUnselected()) return '2' // 有未完成的评分项
      if (!this.checkLogin()) {
        this.savePatientInformation()
        return '3'
      } // 未登录
      return null // 所有检查通过
    },

    // 将用户信息暂时保存本地
    savePatientInformation() {
      localStorage.setItem('userSelected', JSON.stringify(this.selectOptions))
    },

    // 判断用户信息是否填写完整
    checkPatientInformation() {
      return Object.values(this.PatientInformations).every(
        (value) => value !== ''
      )
    },

    // 判断用户年龄是否正确
    checkAge() {
      const age = this.PatientInformations.age
      if (age === '未知') {
        return true
      } else {
        return !isNaN(Number(age)) && Number(age) > 0
      }
    },

    // 判断是否还有未完成的评分项
    checkUnselected() {
      return this.unselected === 0
    },

    // 判断是否登录
    checkLogin() {
      return this.$cookies.get('medtion_isLogged_only_sign') !== undefined
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 291px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}

.topic {
  width: 100%;
  margin-top: 18px;
  padding: 16px 0 16px 0;
  border-bottom: 0.5px solid #efefef;

  .title {
    color: #333333;
    font-weight: 700;
    margin-bottom: 24px;
  }

  input {
    width: 100px;
    height: 30px;
    padding: 0 6px;
    border-radius: 6px;
    margin-right: 12px;
    border: 1px solid #ccc !important;
  }

  label {
    color: #676c74;
    font-size: 14px;
  }
}

.score-details {
  gap: 15px;
  width: 100%;
  display: flex;
  margin-top: 20px;
  flex-direction: column;

  .details {
    width: 100%;
    color: #333;
    display: flex;
    gap: 5px;
  }
}
</style>
