<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div @click="getTatals($event)" ref="reference">
          <Options :information="consciousness"></Options>
          <Options :information="comprehend"></Options>
          <Options :information="language"></Options>
          <Options :information="view"></Options>
          <Options :information="gaze"></Options>
          <Options :information="underpart"></Options>
          <Options :information="upperLimbF"></Options>
          <Options :information="upperLimbN"></Options>
          <Options :information="StretchWrist"></Options>
          <Options :information="FingerStrength"></Options>
          <Options :information="lowerLimbs"></Options>
          <Options :information="lyingCurl"></Options>
          <Options :information="FootDorsiflexion"></Options>
          <Options :information="gait"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="selected === 0">暂无</span>
            <span v-show="selected !== 0">{{ totalPoints }}<span>分</span></span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">
            保存评分
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'
import { keepScore } from '@/api/minitool'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '欧洲卒中量表 - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '欧洲卒中量表包括意识水平、定向力、语言、视野、凝视及面瘫等 14 个项目。该量表真实性及敏感性较好，但评定的项目相对较多。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: 'ESS, 血管空间, 脑外伤, 嗜睡量表, PSQI, european stroke scale, FSS'
        }
      ]
    }
  },
  data () {
    return {
      // 意识水平
      consciousness: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 600px;',
        title: '1. 意识水平',
        id: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6'],
        content: [
          '1) 清醒，反应灵敏：10分;',
          '2) 嗜睡，最小刺激能唤醒，可回答问题、执行指令：8分;',
          '3) 昏睡或反应迟钝，需反复刺激或强烈痛刺激才有反应：6分;',
          '4) 任何刺激均不能唤醒，对痛刺激有躲避反应:4分;',
          '5) 任何刺激均不能唤醒，对痛刺激出现去大脑强直：2分;',
          '6) 任何刺激均不能唤醒，对痛刺激无反应：0分。',
        ],
        grade: ['10', '8', '6', '4', '2', '0'],
        simpleContent: [
          '清醒，反应灵敏：;',
          '嗜睡，最小刺激能唤醒，可回答问题、执行指令;',
          '昏睡或反应迟钝，需反复刺激或强烈痛刺激才有反应;',
          '任何刺激均不能唤醒，对痛刺激有躲避反应;',
          '任何刺激均不能唤醒，对痛刺激出现去大脑强直;',
          '任何刺激均不能唤醒，对痛刺激无反应;',
        ],
      },
      // 理解能力（不可以示范）
      comprehend: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '2. 理解能力（不可以示范）',
        id: ['2-1', '2-2', '2-3'],
        content: [
          '1) 三项指令均完成：8分;',
          '2) 完成两项或一项：4分;',
          '3) 三项均不能完成：0分。',
        ],
        grade: ['8', '4', '0'],
        simpleContent: [
          '三项指令均完成;',
          '完成两项或一项;',
          '三项均不能完成;',
        ],
      },
      // 语言（与患者进行简单对话）
      language: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title: '3. 语言（与患者进行简单对话）',
        id: ['3-1', '3-2', '3-3', '3-4', '3-5'],
        content: [
          '1) 正常：8分;',
          '2) 轻度找词困难，但可进行交谈：6分;',
          '3) 严重找词困难，交谈困难：4分;',
          '4) 只能说是或不:2分;',
          '5) 哑：0分。',
        ],
        grade: ['8', '6', '4', '2', '0'],
        simpleContent: [
          '正常;',
          '轻度找词困难，但可进行交谈;',
          '严重找词困难，交谈困难;',
          '只能说是或不;',
          '哑;',
        ],
      },
      // 视野
      view: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 200px;',
        title: '4. 视野',
        id: ['4-1', '4-2'],
        content: ['1) 正常：8分;', '2) 缺失：0分。'],
        grade: ['8', '0'],
        simpleContent: ['正常;', '缺失;'],
      },
      // 凝视
      gaze: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '5. 凝视',
        id: ['5-1', '5-2', '5-3', '5-4'],
        content: [
          '1) 正常：8分;',
          '2) 眼球居中，向一侧注视受限：4分;',
          '3) 双眼向一侧凝视，尚可回到中线：2分;',
          '4) 双眼向一侧凝视，不能回到中线:0分。',
        ],
        grade: ['8', '4', '2', '0'],
        simpleContent: [
          '正常;',
          '眼球居中，向一侧注视受限;',
          '双眼向一侧凝视，尚可回到中线;',
          '双眼向一侧凝视，不能回到中线;',
        ],
      },
      // 下部面肌运动
      underpart: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '6. 下部面肌运动',
        id: ['6-1', '6-2', '6-3'],
        content: ['1) 正常：8分;', '2) 轻瘫：4分;', '3) 全瘫：0分。'],
        grade: ['8', '4', '0'],
        simpleContent: ['正常;', '轻瘫;', '全瘫;'],
      },
      // 上肢保持上抬45°
      upperLimbF: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title: '7. 上肢保持上抬45°',
        id: ['7-1', '7-2', '7-3', '7-4', '7-5'],
        content: [
          '1) 上肢可保持体位5秒：4分;',
          '2) 上肢可保持体位5秒，但患手旋前：3分;',
          '3) 上肢在5秒内下落，但可保持较低的位置：2分;',
          '4) 上肢不能保持体位，但试图对抗重力:1分;',
          '5) 上肢下落:0分。',
        ],
        grade: ['4', '3', '2', '1', '0'],
        simpleContent: [
          '上肢可保持体位5秒;',
          '上肢可保持体位5秒，但患手旋前;',
          '上肢在5秒内下落，但可保持较低的位置;',
          '上肢不能保持体位，但试图对抗重力;',
          '上肢下落;',
        ],
      },
      // 上肢保持上抬90°
      upperLimbN: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 500px;',
        title: '8. 上肢保持上抬90°',
        id: ['8-1', '8-2', '8-3', '8-4', '8-5'],
        content: [
          '1) 正常：4分;',
          '2) 上肢可伸直，但运动不充分：3分;',
          '3) 上肢屈曲：2分;',
          '4) 轻微运动:1分;',
          '5) 无运动:0分。',
        ],
        grade: ['4', '3', '2', '1', '0'],
        simpleContent: [
          '正常;',
          '上肢可伸直，但运动不充分;',
          '上肢屈曲;',
          '轻微运动;',
          '无运动;',
        ],
      },
      // 伸腕
      StretchWrist: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 500px;',
        title: '9. 伸腕',
        id: ['9-1', '9-2', '9-3', '9-4', '9-5'],
        content: [
          '1) 正常：8分;',
          '2) 运动充分，力量减弱：6分;',
          '3) 运动不充分：4分;',
          '4) 轻微运动:2分;',
          '5) 无运动:0分。',
        ],
        grade: ['8', '6', '4', '2', '0'],
        simpleContent: [
          '正常;',
          '运动充分，力量减弱;',
          '运动不充分;',
          '轻微运动;',
          '无运动;',
        ],
      },
      // 手指力量
      FingerStrength: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 300px;',
        title: '10.手指力量',
        id: ['10-1', '10-2', '10-3'],
        content: [
          '1) 力量相等：8分;',
          '2) 力量减弱：4分;',
          '3) 不能对合：0分。',
        ],
        grade: ['8', '4', '0'],
        simpleContent: ['力量相等;', '力量减弱;', '不能对合;'],
      },
      // 下肢活动
      lowerLimbs: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '11.下肢活动',
        id: ['11-1', '11-2', '11-3', '11-4'],
        content: [
          '1) 坚持5秒：4分;',
          '2) 5秒时下降了一半：2分;',
          '3) 5秒之内回落到床上：1分;',
          '4) 立即回落到床上:0分。',
        ],
        grade: ['4', '2', '1', '0'],
        simpleContent: [
          '坚持5秒;',
          '5秒时下降了一半;',
          '5秒之内回落到床上;',
          '立即回落到床上;',
        ],
      },
      // 屈髋和屈膝
      lyingCurl: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 500px;',
        title: '12.屈髋和屈膝',
        id: ['12-1', '12-2', '12-3', '12-4', '12-5'],
        content: [
          '1) 正常：4分;',
          '2) 能抵抗阻力，力量减弱：3分;',
          '3) 能抵抗重力：2分;',
          '4) 微动：1分;',
          '5) 不动：0分。',
        ],
        grade: ['4', '3', '2', '1', '0'],
        simpleContent: [
          '正常;',
          '能抵抗阻力，力量减弱;',
          '能抵抗重力;',
          '微动;',
          '不动;',
        ],
      },
      // 足背屈
      FootDorsiflexion: {
        bgColor: 'background: rgba(5, 129, 206, 0.03);',
        width: 'width: 400px;',
        title: '13.足背屈',
        id: ['13-1', '13-2', '13-3', '13-4', '13-5'],
        content: [
          '1) 正常：8分;',
          '2) 运动充分，力量减弱：6分;',
          '3) 运动不充分：4分;',
          '4) 微动:2分;',
          '4) 不动:0分。',
        ],
        grade: ['8', '6', '4', '2', '0'],
        simpleContent: [
          '正常;',
          '运动充分，力量减弱;',
          '运动不充分;',
          '微动;',
          '不动;',
        ],
      },
      // 步态
      gait: {
        bgColor: 'background: #FBFBFB;',
        width: 'width: 600px;',
        title: '14.步态',
        id: ['14-1', '14-2', '14-3', '14-4', '14-5', '14-6'],
        content: [
          '1) 正常：10分;',
          '2) 步态异常，和/或距离、速度受限：8分;',
          '3) 扶持下能行走：6分;',
          '4) 一人或一人以上帮助下可行走：4分;',
          '5) 能支撑着站立，但不能行走：2分;',
          '6) 不能站立及行走：0分。',
        ],
        grade: ['10', '8', '6', '4', '2', '0'],
        simpleContent: [
          '正常：;',
          '步态异常，和/或距离、速度受限;',
          '扶持下能行走;',
          '一人或一人以上帮助下可行走;',
          '能支撑着站立，但不能行走;',
          '不能站立及行走;',
        ],
      },
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选个数
      unselected: 14,
      choiceBox: [],
    }
  },

  beforeMount () {
    localStorage.setItem('className', '缺血')
    localStorage.setItem('wordLast', '欧洲卒中量表')
  },

  mounted () {
    // 获取到所有的 input 元素，并存入到 choiceBox 中
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }

    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints =
          this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      // 每次触发点击事件事件之后先初始化数据
      this.totalPoints = 0
      this.unselected = 14
      this.selected = 0
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  font-family: 'Microsoft YaHei';

  .result {
    width: 310px;
    height: 56px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 30px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
</style>
