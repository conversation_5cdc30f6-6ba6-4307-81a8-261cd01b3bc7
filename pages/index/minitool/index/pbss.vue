<template>
  <div>
    <form action="">
      <Patient></Patient>
      <div class="particulars">
        <!-- 评分详情 -->
        <ScoringDetails></ScoringDetails>
        <!-- 评分内容 -->
        <div class="introduction-top">介绍：匹兹堡脑干评分（PBSS）能用于评估昏迷病人的脑干反射。</div>
        <div @click="getTatals($event)" ref="reference">
          <Options v-for="(i, index) in BST" :key="i.title" :information="i"></Options>
        </div>
        <!-- 结果展示 -->
        <div class="result">
          <div class="grade">
            结果：<span v-show="unselected !== 0">暂无</span>
            <span v-show="unselected === 0">{{ finalScore }}</span>
          </div>
          <div v-show="unselected !== 0" class="explain">
            已选择<span>{{ selected }}</span>个评分项，尚有<span>{{ unselected }}</span>个评分项未选择
          </div>
          <div style="white-space: pre-wrap" class="explain">{{ resultExplain }}</div>
        </div>
        <!-- 保存评分 -->
        <div class="save">
          <div :class="{ scoreBtn: unselected === 0 }" @click="save" class="btn">保存评分</div>
        </div>
      </div>
    </form>
  </div>
</template>

<script type="text/javascript">
import Patient from '@/components/MiniTool/Patient.vue'
import Options from '@/components/MiniTool/Options.vue'
import ScoringDetails from '@/components/MiniTool/ScoringDetails.vue'

export default {
  // 注册组件
  components: { Patient, Options, ScoringDetails },
  // tdk
  head () {
    return {
      title: '匹兹堡脑干评分(PBSS) - 临床评分小工具 - 脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '匹兹堡脑干评分(PBSS)是一种评估老年人能否独立生活的工具。匹兹堡脑干评分(PBSS)由6个项目组成，每个项目被评为能力（独立完成）或无能力（需要帮助），总分为6分。该评估工具被广泛用于医疗、社会保障和养老机构等领域，用于评估老年人的自理能力水平。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '匹兹堡脑干评分(PBSS), 日常生活功能指数评价, Katz指数, 日常生活能力评估'
        }
      ]
    }
  },
  data () {
    return {
      // 脑干反射
      BST: [
        // 1.睫毛反射
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '1. 睫毛反射',
          id: ['1-1', '1-2'],
          content: [
            '1) 两侧都有：2分;',
            '2) 两侧消失：1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['两侧都有;', '两侧消失;']
        },
        // 角膜反射
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '2. 角膜反射',
          id: ['2-1', '2-2'],
          content: [
            '1) 两侧都有：2分;',
            '2) 两侧消失：1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['两侧都有;', '两侧消失;']
        },
        // 3.玩偶眼反射
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '3. 玩偶眼反射',
          id: ['3-1', '3-2', '3-3'],
          content: [
            '1) 两侧都有：2分;',
            '2) 两侧消失：1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['两侧都有;', '两侧消失;']
        },
        // 4.右侧瞳孔会对光反应存在
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '4. 右侧瞳孔对光反应存在',
          id: ['4-1', '4-2'],
          content: [
            '1) 存在：2分;',
            '2) 缺乏:1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['存在;', '缺乏;']
        },
        // 5.左侧瞳孔对光反应存在
        {
          bgColor: 'background: rgba(5, 129, 206, 0.03);',
          width: 'width: 319px;',
          title: '5. 左侧瞳孔对光反应存在',
          id: ['5-1', '5-2'],
          content: [
            '1) 存在：2分;',
            '2) 缺乏：1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['存在;', '缺乏;']
        },
        // 6.咽反射和咳嗽反射
        {
          bgColor: 'background: #FBFBFB;',
          width: 'width: 319px;',
          title: '6. 咽反射和咳嗽反射',
          id: ['6-1', '6-2'],
          content: [
            '1) 存在:2分;',
            '2) 缺乏:1分。'
          ],
          grade: ['2', '1'],
          simpleContent: ['存在;', '缺乏;']
        }
      ],
      // 总分数
      totalPoints: 0,
      // 选中的个数
      selected: 0,
      // 未选择的个数
      unselected: 6,
      explain: 0,
      choiceBox: [],
    }
  },

  computed: {
    // 结果说明
    resultExplain () {
      return '结果说明：（1）匹兹堡脑干评分 = 各反射得分之和 \n                    最小得分：6 分 \n                    最大得分：12 分 \n                    分数越高越好 \n                    PBSS可以与格拉斯哥评分联合应用，\n                    成为格拉斯哥匹兹堡昏迷评分。经联合应用，PBSS得分范围可以达到 9-27分'
    },
    // 最终结果(根据分数显示不同风险等级)
    finalScore () {
      return `${this.totalPoints}分`
    }
  },

  beforeMount () {
    localStorage.setItem('className', '其他')
    localStorage.setItem('wordLast', '匹兹堡脑干评分(PBSS)')
  },

  mounted () {
    // 获取到 前循环 所有的 input 框
    for (let i = 0; i < this.$refs.reference.children.length; i++) {
      for (
        let j = 0;
        j <
        this.$refs.reference.children[i].children[0].children[3].children
          .length;
        j++
      ) {
        this.choiceBox.push(
          this.$refs.reference.children[i].children[0].children[3].children[j]
            .children[0]
        )
      }
    }

    // 登录之后 从本地拿去数据 勾选选项
    if (this.$store.state.minitool.conditionChange === 'true' && localStorage.getItem('scoringDetails') !== null) {
      this.choiceBox.map((item, index) => {
        item.checked = JSON.parse(localStorage.getItem('scoringDetails'))[
          index
        ].choice
      })
    }
    // 勾选完，循环数组，拿去分数和选中个数
    this.choiceBox.forEach((element) => {
      // 如果 处于选中状态 就处理以下逻辑
      this.checked(element)
    })
  },
  methods: {
    // 公共逻辑
    checked (element) {
      if (element.checked === true) {
        this.selected += 1
        this.unselected -= 1
        this.totalPoints = this.totalPoints + element.attributes.grade.value * 1
      }
    },
    getTatals (e) {
      this.totalPoints = 0
      this.selected = 0
      this.unselected = 6
      // 选项的状态
      let optionMessage = []
      // 遍历 choiceBox 数组
      this.choiceBox.forEach((element) => {
        // 存储选项的状态
        optionMessage.push({
          choice: element.checked,
        })
        // 如果 处于选中状态 就处理以下逻辑
        this.checked(element)
      })
      localStorage.setItem('scoringDetails', JSON.stringify(optionMessage))
    },
    // 保存评分
    save () {
      // this.$store.commit('minitool/setGradeShow', false)
      // 传到 vuex 的参数
      if (this.$cookies.get('medtion_isLogged_only_sign') !== undefined) {
        let keepArguments = {
          logIn: this.$cookies.get('medtion_isLogged_only_sign'),
          userId: this.$cookies.get('medtion_user_only_sign').id,
          unselected: this.unselected,
          totalPoints: this.totalPoints,
          router: this.$router,
          $axios: this.$axios.$request,
          relevanceId: this.$route.query.id,
        }
        // 触发 vuex 中的方法 并进行传参
        this.$store.dispatch('minitool/keepScore', keepArguments)
      } else {
        // 未登录
        this.$store.commit('minitool/setShowMaskLayer', 'true')
        this.$store.commit('minitool/setLoggingStatus', 'false')
        this.$store.commit('minitool/setGoLoginShow', 'true')
      }
    },
  },
}
</script>

<style lang="less" scoped>
.particulars {
  width: 894px;
  padding-left: 16px;
  margin-top: 25px;
  box-sizing: border-box;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  font-family: 'Microsoft YaHei';

  .icon {
    width: 10px;
    height: 10px;
    position: absolute;
    border: 1px solid #666666;
    font-size: 10px;
    color: #666666;
    text-align: center;
    line-height: 10px;
    border-radius: 50%;
    cursor: pointer;
  }

  .content {
    width: 382px;
    height: 88px;
    position: absolute;
    background: #ffffff;
    left: 265px;
    top: 30px;
    box-sizing: border-box;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    padding: 14px;
    font-size: 14px;
    color: #666666;
    overflow: auto;
  }

  .result {
    width: 894px;
    height: 170px;
    margin-top: 40px;

    .grade {
      font-weight: 700;
      font-size: 20px;
      color: #666666;

      span:nth-of-type(1) {
        color: #888888;
      }

      span:nth-of-type(2) {
        color: #333333;

        span {
          font-size: 16px;
          color: #888888;
          font-weight: 700;
        }
      }
    }

    .explain {
      font-weight: 400;
      font-size: 16px;
      margin-top: 10px;
      color: #888888;

      span {
        color: #0581ce;
      }
    }
  }

  .save {
    width: 100%;
    height: 48px;
    margin-top: 60px;

    .btn {
      width: 344px;
      height: 100%;
      float: right;
      line-height: 48px;
      text-align: center;
      background: #f4f4f4;
      border-radius: 6px;
      color: #666666;
      cursor: pointer;
    }

    .btn:hover {
      background-color: #0581ce;
      color: #ffffff;
    }
  }

  .scoreBtn {
    background-color: #0581ce !important;
    color: #ffffff !important;
  }
}
.introduction-top{
  margin-top: 38px;
}
</style>
