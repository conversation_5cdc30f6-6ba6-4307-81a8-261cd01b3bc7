<template>
  <div class="miniToolMain">
    <!-- 遮罩层 -->
    <div
      @click="maskLayerConceal"
      class="maskLayer"
      :style="{ height: windowHeight + 'px' }"
      :class="{ maskLayerShow: showMaskLayer === 'true' }"
    >
      <div @click.stop class="promptMessage">
        <p v-show="gradeOrHind === '患者信息' && loggingStatus === true">
          患者信息未填写，请补充完整后保存评分
        </p>
        <p v-show="gradeOrHind === '评分项' && loggingStatus === true">
          还有未完成的评分项，请继续完成评分
        </p>
        <p v-show="loggingStatus === 'false'">如需保存评分，请先登录</p>
        <p v-show="ageTip === 'true'">患者信息年龄有误</p>
        <div class="btnBox" v-if="goLoginShow === 'false'">
          <p
            class="btn-save-score"
            v-if="gradeOrHind === '患者信息'"
            @click="directSave"
          >
            直接保存
          </p>
          <p class="btn-know" @click="maskLayerConceal">知道了</p>
        </div>
        <div v-show="goLoginShow === 'true'" @click="goLogin" class="btnKnow">
          <p>去登录</p>
        </div>
      </div>
    </div>
    <!-- 面包屑 -->
    <!--    <div class="bread">-->
    <!--      <bm-breadcrumb>-->
    <!--        <el-breadcrumb-item :to="{ path: '/' }">脑医汇</el-breadcrumb-item>-->
    <!--        <el-breadcrumb-item :to="{ path: '/score' }"-->
    <!--        >临床评分小工具</el-breadcrumb-item>-->
    <!--        <el-breadcrumb-item>-->
    <!--          <span @click="wipeOff">-->
    <!--            {{ $store.state.minitool.className}}-->
    <!--          </span>-->
    <!--        </el-breadcrumb-item>-->
    <!--        <el-breadcrumb-item v-if="deletes && $store.state.minitool.rightNav">-->
    <!--          {{ $store.state.minitool.wordLast }}-->
    <!--        </el-breadcrumb-item>-->
    <!--      </bm-breadcrumb>-->
    <!--    </div>-->
    <div class="big-box">
      <div class="left">
        <el-row class="tac">
          <el-col :span="12">
            <Menu
              class="el-menu-vertical-demo"
              @open="handleOpen"
              @close="handleClose"
              :default-openeds="openeds"
              :unique-opened="true"
              router
              :default-active="$route.path"
              @select="selectHandler"
            >
              <!-- 通用工具 -->
              <Submenu index="1" ref="menuContainer">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>通用工具</span>
                  </div>
                </template>
                <div>
                  <MenuItemGroup>
                    <div
                      @click="getWord($event)"
                      v-for="(i, indexs) in generalBoolTitle"
                      :key="indexs"
                    >
                      <!-- {{generalName[indexs]}} -->
                      <Menu-item
                        :index="`/minitool/${generalName[indexs]}`"
                        :show="indexs"
                        :title="i"
                        >{{ i }}
                      </Menu-item>
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 缺血 -->
              <Submenu index="2" ref="menuIschemia">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>缺血</span>
                  </div>
                </template>
                <!-- 事件委托 -->
                <div>
                  <MenuItemGroup>
                    <div
                      @click="getWord($event)"
                      v-for="(i, indexs) in ischemia"
                      :key="indexs"
                    >
                      <Menu-item
                        :index="`/minitool/${ischemiaName[indexs]}`"
                        :title="i"
                        v-if="
                          i !== '颅内血管动静脉畸形(AVM) Spetsler-Martin分级'
                        "
                        >{{ i }}
                      </Menu-item>
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 卒中再发风险评分 -->
              <Submenu index="3" ref="menuApoplexy">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>卒中再发风险评分</span>
                  </div>
                </template>
                <div>
                  <MenuItemGroup>
                    <div
                      @click="getWord($event)"
                      v-for="(i, indexs) in apoplexy"
                      :key="indexs"
                    >
                      <Menu-item
                        :index="`/minitool/${apoplexyName[indexs]}`"
                        :title="i"
                        >{{ i }}
                      </Menu-item>
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 动脉瘤 -->
              <Submenu index="4" ref="menuAneurysm">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>动脉瘤</span>
                  </div>
                </template>
                <!-- 事件委托 -->
                <div>
                  <MenuItemGroup>
                    <div @click="getWord($event)">
                      <Menu-item
                        v-for="(i, indexs) in artery"
                        :key="indexs"
                        :index="`/minitool/${arterialAneurysmName[indexs]}`"
                        :class="{ 'is-active': wordLast === i }"
                        :title="i"
                        >{{ i }}</Menu-item
                      >
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 脑血管畸形 -->
              <Submenu index="5" ref="menuCerebrovascular">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>脑血管畸形</span>
                  </div>
                </template>
                <!-- 事件委托 -->
                <div>
                  <MenuItemGroup>
                    <div @click="getWord($event)">
                      <Menu-item
                        v-for="(i, indexs) in malformation"
                        :key="indexs"
                        :index="`/minitool/${cerebrovascularMalformation[indexs]}`"
                        :class="{
                          'is-active': wordLast === i,
                        }"
                        :title="i"
                        >{{ i }}</Menu-item
                      >
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 步态分析法 -->
              <Submenu index="8" ref="menuGaitAnalysis">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>步态分析法</span>
                  </div>
                </template>
                <div>
                  <MenuItemGroup>
                    <div @click.stop="getWord($event)">
                      <Menu-item
                        v-for="(i, indexs) in walkNametion"
                        :key="indexs"
                        :index="`/minitool/${walkName[indexs]}`"
                        :class="{ 'is-active': wordLast === i }"
                        :title="i"
                        >{{ i }}</Menu-item
                      >
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 帕金森 -->
              <Submenu index="9" ref="menuParkinson">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>帕金森</span>
                  </div>
                </template>
                <div>
                  <MenuItemGroup>
                    <div @click.stop="getWord($event)">
                      <Menu-item
                        v-for="(i, indexs) in parkinson"
                        :key="indexs"
                        :index="`/minitool/${parkinsonName[indexs]}`"
                        :class="{ 'is-active': wordLast === i }"
                        :title="i"
                        >{{ i }}</Menu-item
                      >
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 其他 -->
              <Submenu index="6" ref="menuOther">
                <template slot="title">
                  <div @click="classifyShow($event)">
                    <span>其他</span>
                  </div>
                </template>
                <div>
                  <MenuItemGroup>
                    <div @click.stop="getWord($event)">
                      <Menu-item
                        v-for="(i, indexs) in other"
                        :key="indexs"
                        :index="`/minitool/${otherName[indexs]}`"
                        :class="{ 'is-active': wordLast === i }"
                        :title="i"
                        >{{ i }}</Menu-item
                      >
                    </div>
                  </MenuItemGroup>
                </div>
              </Submenu>
              <!-- 评分记录 -->
              <Submenu index="7" :class="isOpened">
                <template slot="title">
                  <div class="record" @click="goRecord($event)">
                    <span>
                      <svg-icon icon-class="Vector"></svg-icon>
                      评分记录
                    </span>
                  </div>
                </template>
              </Submenu>
            </Menu>
          </el-col>
        </el-row>
      </div>
      <!-- 右边 评分详情页 -->
      <div class="right">
        <div v-show="rightNav" class="headline">
          <p>{{ wordLast }}</p>
        </div>
        <div class="content">
          <nuxt-child />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 引入 element ui 组件
import { Menu, MenuItem, Submenu, MenuItemGroup } from 'element-ui'
import { mapState, mapMutations } from 'vuex'

export default {
  // name: 'NavAndScorePage',
  head() {
    return {
      title: '通用工具',
    }
  },
  // 注册组件
  components: {
    Menu,
    MenuItem,
    Submenu,
    MenuItemGroup,
  },
  data() {
    return {
      // 实时屏幕宽度
      windowWidth: '',
      // 实时屏幕高度
      windowHeight: '',
      activeIndex: '1',
      activeIndex2: '1',
      tabPosition: 'left',
      // 父组件传过来的值 控制面包屑类名的显示
      ind: 7,
      // 控制面包屑 具体名称 的显示与隐藏
      deletes: true,
      uniqueOpened: true,
    }
  },
  computed: {
    // 从 vuex 中解析出来的数据
    ...mapState('minitool', [
      'generalBoolTitle',
      'generalName',
      'ischemia',
      'apoplexy',
      'artery',
      'malformation',
      'walkNametion',
      'parkinson',
      'other',
      'ischemiaName',
      'apoplexyName',
      'arterialAneurysmName',
      'cerebrovascularMalformation',
      'walkName',
      'otherName',
      'parkinsonName',
      'className',
      'showMaskLayer',
      'gradeOrHind',
      'loggingStatus',
      'ageTip',
      'goLoginShow',
      'openeds',
      'wordLast',
      'rightNav',
      'isOpenedShow',
      'PatientInformations',
    ]),
    isOpened() {
      return { 'is-opened': this.isOpenedShow && this.className === '评分记录' }
    },
  },
  mounted() {
    // 为了防止在点击左侧出现拉伸闪烁  获取网站的最后一个参数 将储存openeds 通过页面记住左侧的标记
    // 获取当前页面的路径
    const currentPath = window.location.pathname
    // 从路径中获取最后一个部分
    const lastPartOfPath = currentPath.substring(
      currentPath.lastIndexOf('/') + 1
    )

    // 定义包含路径参数的对象
    const pathParams = {
      generalName: [
        'gcs',
        'mrs',
        'barthel',
        'gos',
        'muscle_force',
        'currency',
        'change-barthel',
        'fim',
        'katz',
        'pulses',
        'faq',
        'rass',
        'oa',
        'brunnstrom',
        'mmt',
        'four',
        'pons-midbrain',
        'crs-r',
        'ashworth',
        'tertiary-balance',
        'fma',
        'adl',
        'crams',
        'bi',
        'ti',
        'asia',
        'ich',
        'frankel-asia',
      ],
      ischemiaName: [
        'aspect',
        'nihss',
        'last2-ch2ance',
        'cha2ds2-vasc',
        'has-bled',
        'sss',
        'ess',
        'cpss',
        'lapss',
        'spetsler-martin',
      ],
      apoplexyName: ['framingham', 'essen', 'abcd2', 'abcd3-I'],
      arterialAneurysmName: [
        'embolization_calculation',
        'hunt-hess',
        'wfns',
        'fisher',
        'phases',
        'elapss',
        'select',
      ],
      cerebrovascularMalformation: [
        'spetzler-martin',
        'borden',
        'cognard',
        'phases',
        'elapss',
        'select',
      ],
      walkName: ['tinetti', 'berg', 'six-mwt'],
      parkinsonName: [
        'hoehn-yahr',
        'pdq-39',
        'updrs',
        'pdss',
        'mmes',
        'nmss',
        'pdq-8',
      ],
      otherName: [
        'cam-icu',
        'frankel',
        'mgs-gcs',
        'hamd',
        'hama',
        'sich',
        'dvt',
        'had',
        'montreal-cognitive',
        'graeb',
        'rls',
        'flacc',
        'pbss',
        'max-ich',
        'max-ich',
        'fresh',
        'hatch',
        'sunnybrook',
        'sf-36',
      ],
    }

    // 根据最后一位值存储相应的值到 localStorage
    let valueToStore
    if (pathParams.generalName.includes(lastPartOfPath)) {
      valueToStore = 1
    } else if (pathParams.ischemiaName.includes(lastPartOfPath)) {
      valueToStore = 2
    } else if (pathParams.apoplexyName.includes(lastPartOfPath)) {
      valueToStore = 3
    } else if (pathParams.arterialAneurysmName.includes(lastPartOfPath)) {
      valueToStore = 4
    } else if (
      pathParams.cerebrovascularMalformation.includes(lastPartOfPath)
    ) {
      valueToStore = 5
    } else if (pathParams.walkName.includes(lastPartOfPath)) {
      valueToStore = 8
    } else if (pathParams.parkinsonName.includes(lastPartOfPath)) {
      valueToStore = 9
    } else if (pathParams.otherName.includes(lastPartOfPath)) {
      valueToStore = 6
    } else if (lastPartOfPath === 'history') {
      valueToStore = 7
    } else {
      valueToStore = null // 如果没有匹配的情况
    }
    console.log(valueToStore, 'valueToStore')
    // 将值openeds存储到 localStorage
    if (valueToStore !== null) {
      localStorage.setItem('openeds', valueToStore.toString())
    }

    // 获取左侧高度 将最小高度控制在左侧的高度 防止高度溢出
    const leftHeight =
      document.querySelector('.el-menu-vertical-demo').clientHeight + 40
    console.log(leftHeight, 'leftHeight')
    const targetElement = document.querySelector('.miniToolMain')
    targetElement.style.minHeight = leftHeight + 'px' // 将获取的高度赋值给另一个元素

    // 侧边栏部分 点击对应的病例 下拉部分会滑动对应的地方
    const selectedQuestion = localStorage.getItem('wordLast')

    const illnessMap = {
      通用工具: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuContainer',
        data: this.generalBoolTitle,
      },
      缺血: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuIschemia',
        data: this.ischemia,
      },
      卒中再发风险评分: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuApoplexy',
        data: this.apoplexy,
      },
      动脉瘤: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuAneurysm',
        data: this.artery,
      },
      脑血管畸形: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuCerebrovascular',
        data: this.malformation,
      },
      步态分析法: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuGaitAnalysis',
        data: this.walkNametion,
      },
      帕金森: {
        classNames: 'el-menu-item.is-active',
        menuContainer: 'menuParkinson',
        data: this.parkinson,
      },
      其他: {
        classNames: 'el-menu-item.is-active.is-active',
        menuContainer: 'menuOther',
        data: this.other,
      },
    }

    for (const [key, value] of Object.entries(illnessMap)) {
      // Object.entries(illnessMap)将illnessMap对象转换为一个数组，其中每个元素都是一个键值对数组，包含对象的键和对应的值。
      // 在for...of循环中，const [key, value]语法用于解构这些键值对数组，将键赋值给key，将值赋值给value。
      if (value.data.includes(selectedQuestion)) {
        const { classNames, menuContainer } = value
        this.$nextTick(() => {
          // 采取先判断点击选中的.is-active 测算出到顶部的距离 然后滑动到对应地方
          // 根据在储存在localstorage中wordLast：某一个详细的病种 去判断是否在侧边栏包含这个值 再进行判断
          // 由于点击部分可能是el-menu-item.is-activ 还可能是el-menu-item.is-active.is-active 分情况代入函数判断
          this.scrollToElementByClassName(classNames, menuContainer)
        })
        break // 如果找到匹配项，跳出循环
      }
    }

    // 首次加载页面 判断登录状态
    if (this.$cookies.get('medtion_isLogged_only_sign') !== true) {
      this.$store.commit('minitool/setLoggingStatus', false)
    } else {
      this.$store.commit('minitool/setLoggingStatus', true)
    }

    // 评分记录样式的显示
    if (localStorage.getItem('openeds') === '7') {
      this.$store.commit('minitool/setIsOpenedShow', true)
    }

    // 刷新页面 从本地拿数据进行面包屑的渲染
    if (localStorage.getItem('className') === '评分记录') {
      this.$store.commit('minitool/setRightnav', false)
    }
    this.$store.commit('minitool/setWordLast', localStorage.getItem('wordLast'))
    this.$store.commit(
      'minitool/setClassName',
      localStorage.getItem('className')
    )
    // 接收父组件传过来的数据 控制面包屑的类名
    if (this.$route.params.ind === 'undefined') {
      this.ind = this.openeds[0] + ''
    } else {
      this.ind = this.$route.params.ind + ''
    }
    // 根据父组件传过来的数据 更改 类名
    if (this.ind === '1') {
      this.$store.commit('minitool/setClassName', '通用工具')
      localStorage.setItem('className', '通用工具')
    } else if (this.ind === '2') {
      this.$store.commit('minitool/setClassName', '缺血')
      localStorage.setItem('className', '缺血')
    } else if (this.ind === '3') {
      this.$store.commit('minitool/setClassName', '卒中再发风险评分')
      localStorage.setItem('className', '卒中再发风险评分')
    } else if (this.ind === '4') {
      this.$store.commit('minitool/setClassName', '动脉瘤')
      localStorage.setItem('className', '动脉瘤')
    } else if (this.ind === '5') {
      localStorage.setItem('className', '脑血管畸形')
      this.$store.commit('minitool/setClassName', '脑血管畸形')
    } else if (this.ind === '6') {
      this.$store.commit('minitool/setClassName', '其他')
      localStorage.setItem('className', '其他')
    } else if (this.ind === '7') {
      this.$store.commit('minitool/setRightnav', false)
      this.$store.commit('minitool/setClassName', '评分记录')
      localStorage.setItem('className', '评分记录')
    } else if (this.ind === '8') {
      this.$store.commit('minitool/setRightnav', false)
      this.$store.commit('minitool/setClassName', '步态分析法')
      localStorage.setItem('className', '步态分析法')
    }
    // 面包屑 具体评分项名称
    this.show = this.$route.params.wordage

    // 可视窗口高度
    this.windowWidth = document.documentElement.clientWidth
    this.windowHeight = document.documentElement.clientHeight
  },
  methods: {
    ...mapMutations('minitool', ['setShowMaskLayer', 'setPatientInformations']),
    scrollToElementByClassName(className, menuContainer) {
      const element = this.$refs[menuContainer].$el.querySelector(
        `.${className}`
      )
      if (element) {
        let parentUl = element.closest('ul') // 找到最近的父级 ul 元素
        while (parentUl && parentUl.tagName !== 'UL') {
          parentUl = parentUl.parentElement
        }
        const elementTop = element.offsetTop // 获取元素相对于文档的偏移量
        const parentTop = parentUl.offsetTop // 获取父级 ul 元素相对于文档的偏移量
        const offset = elementTop - parentTop // 计算相对偏移量
        // console.log('Offset from parent ul:', offset);
        // 滚动到指定偏移量
        const menuItemGroups = document.querySelectorAll('.el-menu-item-group')
        menuItemGroups.forEach((group) => {
          group.scrollTop = offset
        })
      } else {
        // console.error('未找到具有类名 ' + className + ' 的元素.');
      }
    },

    selectHandler(item) {},
    // 侧边栏打开触发 首次进入自动打开并不会触发
    handleOpen(key, keyPath) {
      this.$store.commit('minitool/setIsOpenedShow', false)
      this.$store.commit('minitool/setOpenedsHandler', [key])
      this.ind = key
      this.deletes = false
      localStorage.setItem('openeds', key)
      if (key === '7') {
        this.deletes = false
      }
    },
    // 侧边栏收起触发
    handleClose(key, keyPath) {
      this.deletes = false
    },
    // 点击面包屑类名 隐藏
    wipeOff() {
      // this.deletes = false
      // this.$store.commit('minitool/setRightnav', false)
    },
    // 修改面包屑的具体名称
    getWord(e) {
      const key = localStorage.getItem('openeds')
      this.$store.commit('minitool/setOpenedsHandler', [key])
      // 清除本地存储的数据
      localStorage.removeItem('scoreAgain')
      localStorage.removeItem('associatedScore')
      localStorage.removeItem('conditionChange')
      localStorage.removeItem('submitData')
      localStorage.removeItem('submitDataSpecial')
      localStorage.removeItem('switchover')
      localStorage.removeItem('scoringDetails')
      localStorage.removeItem('illnessTotalScore')
      localStorage.removeItem('illnessListDetail')
      localStorage.removeItem('patient')
      localStorage.removeItem('associated')
      localStorage.removeItem('identification')
      this.$store.commit('minitool/setScoringDetails', null)
      // 删除本地存储的 scoringDetails 评分项数据
      this.$store.commit(
        'minitool/setWordLast',
        e.target.attributes.title.value
      )
      // 将 具体名称 存储到本地
      localStorage.setItem('wordLast', e.target.attributes.title.value)
      this.deletes = true
      this.$store.commit('minitool/setRightnav', true)
      var scrollTop = document.querySelector('.el-menu-item-group').scrollTop
      localStorage.setItem('scrollPosition', scrollTop)
    },
    classifyShow(e) {
      localStorage.setItem('className', e.target.innerText)
      this.$store.commit('minitool/setClassName', e.target.innerText)
      this.deletes = false
    },
    maskLayerConceal() {
      this.setShowMaskLayer('false')
    },
    directSave() {
      this.setShowMaskLayer('false')
      const { age, gender, diagnose, name } = this.PatientInformations
      this.setPatientInformations({
        age: age || '未知',
        name: name || '未知',
        gender: gender || '未知',
        diagnose: diagnose || '未知',
      })
    },
    // 评分记录 点击事件
    goRecord(e) {
      // 评分记录被选中的样式
      this.$store.commit('minitool/setIsOpenedShow', true)
      localStorage.setItem('openeds', ['7'])
      this.$store.commit('minitool/setOpenedsHandler', ['7'])
      localStorage.setItem('className', e.target.innerText)
      this.$store.commit('minitool/setClassName', e.target.innerText)
      this.$store.commit('minitool/setRightnav', false)
      // 判断是否登录
      if (this.loggingStatus === true) {
        // 登录状态
        // 将 vuex中的name title 置为空
        this.$store.commit('minitool/setName', '')
        this.$store.commit('minitool/setTitle', '')
        localStorage.setItem('name', '')
        localStorage.setItem('taxon', '全部')
        localStorage.setItem('currentPage', 1)
        this.$store.dispatch('minitool/gradingRecords', {
          $axios: this.$axios.$request,
          userId: this.$cookies.get('medtion_user_only_sign').id,
          router: this.$router,
          page: 1,
          skip: true,
        })
      } else {
        // 未登录状态 跳转到登录页面
        this.$router.push({
          name: 'signin',
          query: { fallbackUrl: this.$route.fullPath },
        })
      }
    },
    // 去登录
    goLogin() {
      // 改变登录状态
      localStorage.setItem('conditionChange', true)
      this.$router.push({
        name: 'signin',
        query: { fallbackUrl: this.$route.fullPath },
      })
    },
  },
}
</script>
<style lang="less" scoped>
@import '~@/pages/index/minitool/index.less';
</style>
