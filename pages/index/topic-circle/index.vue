<template>
  <div id="Home">
    <TopicCircleNav/>
    <div class="topicCircleBox">
      <div class="page-left">
        <!-- 广告 -->
        <div class='banner' v-if="advertisementData.length" ref="index_banner_wrapper">
          <el-carousel
            indicator-position='none'
            @change="changeHandler"
            :arrow="advertisementData.length > 1 ? 'hover' : 'never'"
          >
            <el-carousel-item v-for='(item, index) in advertisementData' :key='index'>
              <div class='banner_box'>
                <img
                  :alt='item.name'
                  :src='$tool.compressImg(item.image,793,264)'
                  class='img_cover'
                  @click='jumpBannerFun(item)'
                />
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 列表 -->
        <div class="left-bigbox">
          <tabs :value="pageActiveIndex" @tab-click="handleClick">
            <tab-pane label="全部">
              <template v-if="pageAllList_o.list.length">
                <ArticleList :data_list="pageAllList_o.list"/>
                <div v-show="pageAllList_o.loading" class="loadingList">
                  <el-skeleton animated/>
                </div>
              </template>
              <NullData v-else text="暂无内容"/>
            </tab-pane>
            <tab-pane label="推荐">
              <template v-if="pageRecommendList_o.list.length">
                <ArticleList :data_list="pageRecommendList_o.list"/>
                <div v-show="pageRecommendList_o.loading" class="loadingList">
                  <el-skeleton animated/>
                </div>
              </template>
              <NullData v-else text="暂无内容"/>
            </tab-pane>
            <tab-pane label="关注" v-if="pageFollowListList_o.list.length">
              <ArticleList :data_list="pageFollowListList_o.list"/>
              <div v-show="pageFollowListList_o.loading" class="loadingList">
                <el-skeleton animated/>
              </div>
            </tab-pane>
          </tabs>
        </div>
      </div>
      <div class="page-right">
        <ul class="topicCircleBtn">
          <li @click="toTopic_fn">
            <img src="~/assets/images/topic_circle/icon_topic.png" alt=""/>话题
          </li>
          <li @click="toCircle_fn">
            <img
              src="~/assets/images/topic_circle/icon_circle.png"
              alt=""
            />圈子
          </li>
        </ul>
        <div class="rightItem">
          <h3 class="rightItemTitle">内容创作</h3>
          <ul class="conCreation">
            <li @click="toAnswerList_fn">
              <el-popover
                placement="bottom"
                title=""
                width="192"
                trigger="manual"
                content="您发布的内容正在审核中，审核通过方可发布新内容。"
                v-model="verificationBtn1">
                <div style="display:flex;flex-direction:column;">
                  <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                  <b
                    style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                    @click="verificationBtn1 = !verificationBtn1">我知道了</b>
                </div>
                <div slot="reference">
                  <img src="~/assets/images/topic_circle/icon_answer.png" alt=""/>
                </div>
              </el-popover>
              <i>回答</i>
            </li>
            <li @click="toWrite_fn">
              <el-popover
                placement="bottom"
                title=""
                width="192"
                trigger="manual"
                content="您发布的内容正在审核中，审核通过方可发布新内容。"
                v-model="verificationBtn2">
                <div style="display:flex;flex-direction:column;">
                  <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                  <b
                    style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                    @click="verificationBtn2 = !verificationBtn2">我知道了</b>
                </div>
                <div slot="reference">
                  <img src="~/assets/images/topic_circle/icon_question.png" alt=""/>
                </div>
              </el-popover>
              <i>提问</i>
            </li>
            <li @click="writeInfo_fn">
              <div slot="reference">
                <img src="~/assets/images/topic_circle/icon_picture.png" alt=""/>
              </div>
              <i>图文</i>
            </li>
          </ul>
        </div>
        <HotTopicCircleList :list="hotActivity" title="热门活动"/>
        <HotTopicCircleList :list="hotTopic" title="热门话题"/>
        <HotTopicCircleList :list="hotCircle" title="热门圈子"/>
      </div>
      <div style="clear: both"></div>
    </div>
    <ShortVideoPlayback
      :video-id='$store.state.topicCircle.topicCircleHomeShortVideoId'
      :visible='$store.state.topicCircle.topicCircleHomeShortVisible'
      @cancelFn='(flag) => $store.commit("topicCircle/setTopicCircleHomeShortVisible",flag)'
    />
  </div>
</template>

<script>
import {Tabs, TabPane} from 'element-ui'
import Cookies from 'js-cookie'
import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
import ShortVideoPlayback from '@/components/optimize-components/public/ShortVideoPlayback'
import HotTopicCircleList from '@/components/optimize-components/TopicCircle/HotTopicCircleList'
import NullData from '@/components/optimize-components/TopicCircle/NullData'
import {userInfo} from '@/api/user'
import {loginByToken} from '@/api/login'
import {
  getCommunityHomePage,
  getUserPublishAbility, // 判断是否可以发布
  getFollowList,
  getHotCommunityTypeLimit,
  getUserInterestedSubspecialtyList,
  follow,
  getTrendingTopicEvents
} from '@/api/topic-circle'
import {getSlotContent} from '../../../api/banner/banner'
import brandAdJump from '../../../assets/helpers/brandAdJump'
import TopicCircleNav from '@/components/PageComponents/Nav/TopicCircleNav'

export default {
  name: 'Home',
  components: {
    Tabs,
    TabPane,
    ArticleList,
    ShortVideoPlayback,
    HotTopicCircleList,
    NullData,
    TopicCircleNav
  },
  async asyncData({app, params, error, store, query, req, redirect, route}) {
    if (!store.state.auth.user.id) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }
    // 获取亚专业
    const [subspecialty] = await Promise.all([
      app.$axios.$request(getUserInterestedSubspecialtyList({}))
    ])
    let subSpecialtyIds = [];
    let subSpecialtyNames = [];
    if (subspecialty.code === 1) {
      subspecialty.list.map((v) => {
        subSpecialtyIds.push(v.id);
        subSpecialtyNames.push(v.name);
      })
      subSpecialtyIds = subSpecialtyIds + ''
      subSpecialtyNames = subSpecialtyNames + ''
    }

    // 获取页面数据
    const [mainListAll, mainList, followList, hotTopic, hotCircle, hotActivity, advertisementData] =
      await Promise.all([
        // 全部列表
        app.$axios.$request(
          getCommunityHomePage({
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        // 推荐列表
        app.$axios.$request(
          getCommunityHomePage({
            subSpecialtyIds,
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        // 关注列表
        app.$axios.$request(
          getFollowList({
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        // 热门话题
        app.$axios.$request(
          getHotCommunityTypeLimit({
            communityType: 'T',
            userId: store.state.auth.user.id,
            limit: 5
          })
        ),
        // 热门圈子
        app.$axios.$request(
          getHotCommunityTypeLimit({
            communityType: 'C',
            userId: store.state.auth.user.id,
            limit: 5
          })
        ),
        // 热门活动
        app.$axios.$request(
          getHotCommunityTypeLimit({
            communityType: 'A',
            userId: store.state.auth.user.id,
            limit: 5
          })
        ),
        // 广告 (推荐，感兴趣亚专业)
        app.$axios.$request(
          getSlotContent({
            loginUserId: store.state.auth.user.id,
            detailIdStr: subSpecialtyIds,
            adCode: 'webapi_Community_HomePage',
          })
        ),
      ])

    function pageListDataProcessing(data) {
      let list = []
      if (data.answerList && data.answerList.length > 0) {
        list = []
        list.push(...data.answerList)
        if (data.circleList?.length > 0) {
          list.splice(8, 0, {
            id: 'tb-tuijianquanzi',
            data: data.circleList
          })
        }
        if (data.topicList?.length > 0) {
          list.splice(5, 0, {
            id: 'tb-tuijianhuati',
            data: data.topicList
          })
        }
        if (data.QuestionList?.length > 0) {
          list.splice(3, 0, {
            id: 'tb-yaoqinghuida',
            data: data.QuestionList
          })
        }
      }
      return list
    }

    // 全部列表
    const pageAllList = pageListDataProcessing(mainListAll.result)
    // // 推荐列表
    const pageRecommendList = pageListDataProcessing(mainList.result)
    // // 关注列表
    const pageFollowList = pageListDataProcessing(followList.result)

    return {
      pageRecommendList, // 推荐列表
      pageAllList, // 全部列表
      pageFollowList, // 关注列表
      hotTopic: hotTopic.list, // 热门话题
      hotCircle: hotCircle.list, // 热门圈子
      hotActivity: hotActivity.list, // 热门活动
      subSpecialtyIds, // 亚专业
      advertisementData: advertisementData.list, // 广告
    }
  },
  head() {
    return {
      title: this.questionInfo ? this.questionInfo.title + '-话题圈子-脑医汇' : '话题圈子-脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: (this.questionInfo ? this.questionInfo.title : '') + ',' + (this.subSpecialtyNames ? this.subSpecialtyNames : '')
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ],
      timer: null
    }
  },
  data() {
    return {
      oldScrolltop: 0, // 记录旧的滚动条高度
      timer: null, // 滚动条延迟事件
      lock: true,
      // 全部tab
      pageAllList_o: {
        pageNo: 2,
        pageSize: 10,
        list: [],
        loading: false, // 加载状态结束
        finished: false, // 数据全部加载完成
        loading_error: false, // 数据加载错误
        response: false // 请求是否成功
      },
      // 推荐tab
      pageRecommendList_o: {
        pageNo: 2,
        pageSize: 10,
        list: [],
        loading: false, // 加载状态结束
        finished: false, // 数据全部加载完成
        loading_error: false, // 数据加载错误
        response: false // 请求是否成功
      },
      pageFollowListList_o: {
        pageNo: 2,
        pageSize: 10,
        list: [],
        loading: false, // 加载状态结束
        finished: false, // 数据全部加载完成
        loading_error: false, // 数据加载错误
        response: false // 请求是否成功
      },
      verificationBtn1: false, // 回答按钮
      verificationBtn2: false, // 回答按钮
      isNavClick: false, // 是否点击导航
      isCurrentPage: true,
      bannerIO: null,
      bannerIndex: 0,
      bannerIndexNum: 1,
      isExposureBanner: false,
    }
  },
  watch: {
    // 监听导航的点击 滚动
    '$store.state.topicCircle.clickNavTab'(nv, ov) {
      if (nv !== ov) {
        // 是点击的nav
        this.isNavClick = true
        this.scrollTopHandle()
      }
    },
    // tabindex
    pageActiveIndex(n, o) {
      if (n === '0') {
        this.$axios.$request(
          getSlotContent({
            loginUserId: this.$store.state.auth.user.id,
            detailIdStr: '',
            adCode: 'webapi_Community_all',
          })
        ).then(res => {
          this.advertisementData = res.list;
        })
      } else if (n === '1') {
        this.$axios.$request(
          getSlotContent({
            loginUserId: this.$store.state.auth.user.id,
            detailIdStr: this.subSpecialtyIds,
            adCode: 'webapi_Community_HomePage',
          })
        ).then(res => {
          this.advertisementData = res.list;
        })
      }
    },

    '$store.state.global.scroll'(scroll) {
      if (this.isNavClick) return;
      const windowHeight = document.documentElement.clientHeight // 视口高度
      const ListDom = document.querySelector('.topicCircleBox') // 列表对象
      const listHeight = ListDom.clientHeight // 列表高度
      const ListTop = ListDom.offsetTop // 列表距离文档顶部

      const nav = document.getElementById('nav_header')
      const topicNav = document.getElementById('topic_circle_nav')

      // 是否显示话题圈子Nav
      if (scroll > 60) {
        if (scroll - this.oldScrolltop > 0) {
          nav.style.cssText = 'transform:translateY(-100%)'
          topicNav.style.cssText = 'transform:none'
        } else {
          nav.style.cssText = 'transform:none'
          topicNav.style.cssText = 'transform:translateY(-100%)'
        }
        this.oldScrolltop = scroll
      }
      if (listHeight + ListTop - (scroll + windowHeight) < 300) {
        if (this.pageActiveIndex === '0') {
          if (!this.pageAllList_o.loading && !this.pageAllList_o.finished) {
            this.getAllList_fn()
          }
        } else if (this.pageActiveIndex === '1') {
          if (
            !this.pageRecommendList_o.loading &&
            !this.pageRecommendList_o.finished
          ) {
            this.getRecommendList_fn()
          }
        } else if (this.pageActiveIndex === '2') {
          if (
            !this.pageFollowListList_o.loading &&
            !this.pageFollowListList_o.finished
          ) {
            this.getFollowList_fn()
          }
        }
      }
    }
  },
  computed: {
    pageActiveIndex() {
      return this.$store.state.topicCircle.indexTab
    }
  },
  created() {
    this.pageRecommendList_o.list = this.pageRecommendList // 推荐列表
    this.pageAllList_o.list = this.pageAllList // 全部列表
    this.pageFollowListList_o.list = this.pageFollowList // 关注列表
  },
  mounted() {

    if (this.pageFollowListList_o.list.length) {
      this.$store.commit('topicCircle/setHasIndexTabFollow', true)
    } else {
      this.$store.commit('topicCircle/setHasIndexTabFollow', false)
    }
    // console.log('推荐数据流', this.pageRecommendList)
    // console.log('全部数据流', this.pageAllList)
    // console.log('关注数据流', this.pageFollowList)
    // console.log('热门话题', this.hotTopic)
    // console.log('热门圈子', this.hotCircle)
    // console.log('热门活动', this.hotActivity)
    // console.log('亚专业', this.subSpecialtyIds)
    // console.log('广告', this.advertisementData)
    document.addEventListener("visibilitychange", this.isCurrentHandler); // 页面是否显示
    const bannerWrapper = this.$refs.index_banner_wrapper
    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const bannerAdItem = this.advertisementData[this.bannerIndex]
          this.isExposureBanner = true;
          if (!this.bannerLock) {
            this.bannerLock = true;
            this.$analysys.ad_exposure({
              adExtras: bannerAdItem.extras,
              adModule: bannerAdItem.module,
              exposureLocation: bannerAdItem.clickLocation,
              adCode: bannerAdItem.code,
              adId: bannerAdItem.adId,
              adName: bannerAdItem.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: bannerAdItem.extras,
            })
          }
        } else if (!entry.isIntersecting) {
          this.isExposureBanner = false;
        }
      })
    });

    this.bannerIO.observe(bannerWrapper);
  },
  beforeDestroy() {
    document.removeEventListener("visibilitychange", this.isCurrentHandler);
    this.isExposureBanner = false;
    this.$store.commit('topicCircle/setTopNavSwitch', false)
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  },
  methods: {
    // tab切换
    handleClick(tab, event) {
      this.$store.commit('topicCircle/setIndexTab', tab.index + '')
    },
    // 回答 跳问题列表
    toAnswerList_fn() {
      // 判断是否可以发布
      this.$axios
        .$request(
          getUserPublishAbility({
            userId: this.$store.state.auth.user.id
          })
        )
        .then((res) => {
          if (res.code === 1) {
            if (res.result) {
              this.$analysys.btn_click('回答', document.title)
              setTimeout(() => {
                const {href} = this.$router.resolve({
                  path: `/topic-circle/answerList`
                })
                window.open(href, '_blank')
              }, 0)
            } else {
              this.verificationBtn1 = true
              this.verificationBtn2 = false
            }
          }
        })
    },
    // 提问
    toWrite_fn() {
      const localToken = this.$cookies.get('medtion_token_only_sign')
      const isLogged = this.$store.state.auth.isLogged
      if (localToken || isLogged) {
        // 判断登录
        this.$axios.$request(loginByToken({
          token: localToken
        })).then(response => {
          if(response.code === 1){
            this.$axios.$request(userInfo()).then(res => {
              if (res && res.code === 1) {
                // 判断身份
                if (res.result.identity === 3 || res.result.identity === 4) {
                  this.$toast.fail('当前身份无法使用发布功能')
                  return
                }
                this.$axios
                  .$request(
                    getUserPublishAbility({
                      userId: this.$store.state.auth.user.id
                    })
                  )
                  .then((res) => {
                    if (res.code === 1) {
                      if (res.result) {
                        this.$analysys.btn_click('提问', document.title)
                        setTimeout(() => {
                          const { href } = this.$router.resolve({ path: `/topic-circle/write` })
                          window.open(href, '_blank')
                        }, 0)
                      } else {
                        this.verificationBtn1 = false
                        this.verificationBtn2 = true
                      }
                    }
                  })
              }
            })
          }else{
            this.$toast('请先登录')
            this.$store.commit('editBackUrl', window.location.href)
            this.$router.push({name: 'signin', query: {fallbackUrl: window.location.href}})
          }
        })
      } else {
        this.$toast('请先登录')
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({name: 'signin', query: {fallbackUrl: window.location.href}})
      }
    },
    // 图文
    writeInfo_fn() {
      const localToken = this.$cookies.get('medtion_token_only_sign')
      const isLogged = this.$store.state.auth.isLogged
      if (localToken || isLogged) {
        this.$axios.$request(loginByToken({
          token: localToken
        })).then(response => {
          this.$axios.$request(userInfo()).then(res => {
            if (res && res.code === 1) {
              // 判断身份
              if (res.result.identity === 3 || res.result.identity === 4) {
                this.$toast.fail('当前身份无法使用发布功能')
                return
              }

              if (res.result.isAuth === '1') {
                window.open('/editor?type=2')
              } else if (res.result.isAuth === '2') {
                // 病例 随笔 文章
                window.open('/editor?type=2')
              } else if (res.result.isAuth === '3') {
                this.$toast.fail('身份认证失败')
              } else if (!res.result.identity || res.result.identity === 'undefined') {
                const h = this.$createElement
                this.$msgbox({
                  message: h('p', null, [
                    h('span', null, '部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！')
                  ]),
                  center: true,
                  showCancelButton: true,
                  cancelButtonText: '以后再说',
                  confirmButtonText: '去完善信息',
                  customClass: 'personaldata-messagebox',
                  beforeClose: (action, instance, done) => {
                    if (action === 'confirm') {
                      this.$store.commit('editAccountTypeFun', this.$store.state.auth.user.email ? this.$store.state.auth.user.email : 'tel')
                      this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                      this.$router.push({name: 'register'})
                      done()
                    } else {
                      done()
                    }
                  }
                })
              } else {
                this.$toast.fail('请先认证')
                this.$store.dispatch('authenticationHandler')
              }
            }
          })
        })
      } else {
        this.$toast('请先登录')
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({name: 'signin', query: {fallbackUrl: window.location.href}})
      }
    },
    // 推荐列表翻页数据
    getRecommendList_fn() {
      this.pageRecommendList_o.loading = true
      this.$axios
        .$request(
          getCommunityHomePage({
            subSpecialtyIds: this.subSpecialtyIds,
            userId: this.$store.state.auth.user.id,
            pageNo: this.pageRecommendList_o.pageNo,
            pageSize: this.pageRecommendList_o.pageSize
          })
        )
        .then((res) => {
          if (res.code === 1) {
            this.pageRecommendList_o.list = [
              ...this.pageRecommendList_o.list,
              ...this.sortDate_fn(res)
            ]
            this.pageRecommendList_o.response = true // 请求成功状态
            if (this.pageRecommendList_o.pageNo >= res.result.page.totalPage) {
              this.pageRecommendList_o.finished = true
            }
            this.pageRecommendList_o.pageNo++
            this.pageRecommendList_o.loading = false
          }
        })
        .catch(() => {
          this.pageRecommendList_o.loading_error = true
          this.pageRecommendList_o.response = true
        })
    },
    // 全部列表翻页数据
    getAllList_fn() {
      this.pageAllList_o.loading = true
      this.$axios
        .$request(
          getCommunityHomePage({
            subSpecialtyIds: '',
            userId: this.$store.state.auth.user.id,
            pageNo: this.pageAllList_o.pageNo,
            pageSize: this.pageAllList_o.pageSize
          })
        )
        .then((res) => {
          if (res.code === 1) {
            this.pageAllList_o.list = [
              ...this.pageAllList_o.list,
              ...this.sortDate_fn(res)
            ]
            this.pageAllList_o.response = true // 请求成功状态

            if (this.pageAllList_o.pageNo >= res.result.page.totalPage) {
              this.pageAllList_o.finished = true
            }
            this.pageAllList_o.pageNo++
            this.pageAllList_o.loading = false
          }
        })
        .catch(() => {
          this.pageAllList_o.loading_error = true
          this.pageAllList_o.response = true
        })
    },
    // 关注列表翻页数据
    getFollowList_fn() {
      this.pageFollowListList_o.loading = true
      this.$axios
        .$request(
          getFollowList({
            userId: this.$store.state.auth.user.id,
            pageNo: this.pageFollowListList_o.pageNo,
            pageSize: this.pageFollowListList_o.pageSize
          })
        )
        .then((res) => {
          if (res.code === 1) {
            this.pageFollowListList_o.list = [
              ...this.pageFollowListList_o.list,
              ...this.sortDate_fn(res)
            ]
            this.pageFollowListList_o.response = true // 请求成功状态
            if (this.pageFollowListList_o.pageNo >= res.result.page.totalPage) {
              this.pageFollowListList_o.finished = true
            }
            this.pageFollowListList_o.pageNo++
            this.pageFollowListList_o.loading = false
          }
        })
        .catch(() => {
          this.pageFollowListList_o.loading_error = true
          this.pageFollowListList_o.response = true
        })
    },
    // list数据加工
    sortDate_fn(res) {
      let list = []
      if (res.result.answerList && res.result.answerList.length > 0) {
        list = []
        list.push(...res.result.answerList)
        if (res.result.circleList?.length > 0) {
          list.splice(8, 0, {
            id: 'tb-tuijianquanzi',
            data: res.result.circleList
          })
        }
        if (res.result.topicList?.length > 0) {
          list.splice(5, 0, {
            id: 'tb-tuijianhuati',
            data: res.result.topicList
          })
        }
        if (res.result.QuestionList?.length > 0) {
          list.splice(3, 0, {
            id: 'tb-yaoqinghuida',
            data: res.result.QuestionList
          })
        }
      }
      return list
    },
    // 话题列表
    toTopic_fn() {
      this.$analysys.btn_click('话题', document.title)
      const {href} = this.$router.resolve({path: `/topic-circle/topic`})
      window.open(href, '_blank')
    },
    // 圈子列表
    toCircle_fn() {
      this.$analysys.btn_click('圈子', document.title)
      const {href} = this.$router.resolve({path: `/topic-circle/spheres`})
      window.open(href, '_blank')
    },
    // 话题圈子关注
    followTopicCircle_fn(item) {
      this.$axios
        .$request(
          follow({
            contentId: item.id,
            userId: this.$store.state.auth.user.id,
            type: item.type
          })
        )
        .then((res) => {
          if (res.code === 1) {
            item.isFollow = !item.isFollow
          }
        })
    },
    scrollTopHandle() {
      window.scrollTo(0, 87, 300)
      setTimeout(() => {
        this.isNavClick = false
      }, 300)
    },
    // 监听是否为当前页面
    isCurrentHandler() {
      if (document.visibilityState === "hidden") {
        this.isCurrentPage = false;
      } else if (document.visibilityState === "visible") {
        this.isCurrentPage = true;
      }
    },
    // 切换走马灯
    changeHandler(index) {
      const item = this.advertisementData[index]
      if (this.bannerIndex !== index && this.isExposureBanner && this.bannerIndexNum < this.advertisementData.length && this.isCurrentPage) {
        this.bannerIndexNum++
        this.$analysys.ad_exposure({
          adExtras: item.extras,
          adModule: item.module,
          exposureLocation: item.clickLocation,
          adCode: item.code,
          adId: item.adId,
          adName: item.name,
          unionid: this.$store.state.auth.unionid,
          adUrl: item.extras,
        })
      }
      this.bannerIndex = index;
    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
  }
}
</script>
<style scoped lang="less">
.el-footer {
  margin-top: 0 !important;
}

#Home {
  background: #fbfbfb;
  overflow: hidden;
  padding-bottom: 60px;
}

.topicCircleBox {
  max-width: 1200px;
  width: 1200px;
  margin: 0 auto;
  transition: all 0.3s;
  padding: 20px 0;
}

.left-bigbox {
  padding: 0 20px 20px;
  border-radius: 6px;
  margin-bottom: 10px;
  box-shadow: 0 0 4px rgb(237 237 237);
  background: #fff;

  /deep/ .el-tabs__item {
    font-size: 18px;
  }

  /deep/ .el-tabs__nav-wrap {
    line-height: 57px;

    &::after {
      background-color: #f6f6f6;
    }
  }

  /deep/ .el-tabs__active-bar {
    width: 16px !important;
    margin-left: 10px;
  }
}

.topicCircleBtn {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  li {
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    width: 185px;
    line-height: 68px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;

    &:nth-of-type(1) {
      background-image: url('~/assets/images/topic_circle/icon_topic_btn.png');
    }

    &:nth-of-type(2) {
      background-image: url('~/assets/images/topic_circle/icon_circle_btn.png');
    }

    img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }
  }
}

.rightItem {
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  padding: 20px 16px 0;
  margin-bottom: 20px;

  .rightItemTitle {
    font-size: 18px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f6f6f6;
    font-weight: bold;
  }

  .conCreation {
    display: flex;
    justify-content: space-around;
    padding-bottom: 20px;
    margin-top: 20px;

    li {
      cursor: pointer;

      img {
        width: 68px;
        height: 68px;
      }

      i {
        font-size: 18px;
        display: block;
        text-align: center;
        margin-top: 12px;
        font-style: normal;
      }
    }
  }
}

.loadingList {
  border-top: 1px solid #f6f6f6;
  margin-top: 20px;
  padding-top: 20px;
}

/deep/ .el-tabs__item.is-active {
  color: #0581ce;
}

/deep/ .el-tabs__content {
  overflow: initial;
}

.banner {
  margin-bottom: 20px;

  .banner_box {
    height: 100%;
    position: relative;

    .mask {
      position: absolute;
      width: 100%;
      bottom: 0;
      left: 0;
      min-height: 44px;
      background: rgba(0, 0, 0, 0.3);
      color: #fff;
      padding: 6px 14px;
      display: flex;
      align-items: center;
      line-height: 1.5;
      box-sizing: border-box;
    }
  }

  /deep/ .el-carousel__container {
    border-radius: 6px 6px 6px 6px;
    height: 264px !important;
    overflow: hidden;
  }

  /deep/ .el-icon-arrow-right:before {
    font-family: 'iconfontNew';
    content: '\e624';
  }

  /deep/ .el-icon-arrow-left:before {
    font-family: 'iconfontNew';
    content: '\e626';
  }

  /deep/ .el-carousel__arrow {
    background: rgba(31, 45, 61, 0.3);
    font-size: 16px;
    opacity: 0.8;
    line-height: 36px;

    &:hover {
      background: #0581ce;
      color: white;
      opacity: 1;
    }
  }
}
</style>
