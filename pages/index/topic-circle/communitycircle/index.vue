<template>
  <div id="topicDetail">
    <div class="topicDetailHeader">
      <div class="main">
        <div class="page-left">
          <div class="info">
            <img :src="pageInfo.image?pageInfo.image:default_circle_cover" alt="">
            <div class="title">
              <h3>{{pageInfo.title}}</h3>
              <p><span>{{pageInfo.follows}}关注</span><span>{{pageInfo.contents}}内容</span><span>{{pageInfo.views}}浏览</span></p>
            </div>
          </div>
        </div>
        <div class="page-right">
          <ul class="menu">
            <li @click="followTopicCircle_fn(pageInfo)" :class="pageInfo.isFollow?'on':''">
              <template v-if="!pageInfo.isFollow">
                <svg-icon class-name='topic-icon' icon-class='topic_plus'/>
                关注圈子
              </template>
            </li>
            <li class="share">
              <svg-icon class-name='topic-icon' icon-class='topic_share_blue'/>
              分享
              <div class="shareQRBox">
                <img :src="qrShareUrl" alt="">
                <span>微信扫一扫</span>
              </div>
            </li>
          </ul>
        </div>
        <div style="clear: both"></div>
        <div class="topicMsg" v-if="pageInfo.description">
          <Unpack v-if="pageInfo.description" :text="pageInfo.description" :line="2"/>
        </div>
      </div>
    </div>

    <div class="main">
      <div class="page-left dataFlow">
        <div class="left-bigbox">
          <tabs v-model="pageActiveIndex">
            <tab-pane label="最新">
              <div v-if="pageNewestList_o.list.length">
                <ArticleList :data_list="pageNewestList_o.list"/>
                <div class="loadingList" v-show="pageNewestList_o.loading">
                  <el-skeleton  animated/>
                </div>
              </div>
              <div v-else class="nullData">
                <NullData />
                <span>暂时还没有问题,开始
                  <el-popover
                    placement="bottom"
                    title=""
                    width="192"
                    trigger="manual"
                    content="您发布的内容正在审核中，审核通过方可发布新内容。"
                    v-model="verificationBtn2">
                    <div style="display:flex;flex-direction:column;">
                      <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                      <b
                        style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                        @click="verificationBtn2 = !verificationBtn2">我知道了</b>
                    </div>
                    <div slot="reference">
                      <i @click="toWrite_fn(2)">写第一个问题</i>
                    </div>
                  </el-popover>
                </span>
              </div>
            </tab-pane>
            <tab-pane label="最热">
              <div v-if="pageHottestList_o.list.length">
                <ArticleList :data_list="pageHottestList_o.list"/>
                <div class="loadingList" v-show="pageHottestList_o.loading">
                  <el-skeleton  animated/>
                </div>
              </div>
              <div v-else class="nullData">
                <NullData />
                <span>暂时还没有问题,开始
                  <el-popover
                    placement="bottom"
                    title=""
                    width="192"
                    trigger="manual"
                    content="您发布的内容正在审核中，审核通过方可发布新内容。"
                    v-model="verificationBtn3">
                    <div style="display:flex;flex-direction:column;">
                      <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                      <b
                        style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                        @click="verificationBtn3 = !verificationBtn3">我知道了</b>
                    </div>
                    <div slot="reference">
                      <i @click="toWrite_fn(3)">写第一个问题</i>
                    </div>
                  </el-popover>
                </span>
              </div>
            </tab-pane>
          </tabs>
        </div>
      </div>
      <div class="page-right">
        <div class="rightItem">
          <h3 class="rightItemTitle">内容创作</h3>
          <ul class="conCreation">
            <li @click="toWrite_fn(1)">
              <el-popover
                placement="bottom"
                title=""
                width="192"
                trigger="manual"
                content="您发布的内容正在审核中，审核通过方可发布新内容。"
                v-model="verificationBtn1">
                <div style="display:flex;flex-direction:column;">
                  <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                  <b
                    style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                    @click="verificationBtn1 = !verificationBtn1">我知道了</b>
                </div>
                <div slot="reference">
                  <img src="~/assets/images/topic_circle/icon_question.png" alt="">
                </div>
              </el-popover>
              <i>提问</i>
            </li>
            <li @click="writeInfo_fn">
              <div slot="reference">
                <img src="~/assets/images/topic_circle/icon_picture.png" alt="">
              </div>
              <i>图文</i>
            </li>
          </ul>
        </div>
        <HotTopicCircleList :list="hotCommunity" title="热门圈子"/>
        <ContentContributor :list="contribution.list" title="内容贡献者"/>
        <ToBeAnsweredPage :list="toBeAnswered.list" title="待回答"/>
      </div>
      <div style="clear: both"></div>
    </div>
    <ShortVideoPlayback
      :video-id='$store.state.topicCircle.topicCircleHomeShortVideoId'
      :visible='$store.state.topicCircle.topicCircleHomeShortVisible'
      @cancelFn='(flag) => $store.commit("topicCircle/setTopicCircleHomeShortVisible",flag)'
    />
  </div>
</template>

<script>
  import { Tabs, TabPane } from 'element-ui'
  import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
  import ShortVideoPlayback from '@/components/optimize-components/public/ShortVideoPlayback'
  import Unpack from '@/components/optimize-components/TopicCircle/Unpack'
  import HotTopicCircleList from '@/components/optimize-components/TopicCircle/HotTopicCircleList'
  import ContentContributor from '@/components/optimize-components/TopicCircle/ContentContributor'
  import ToBeAnsweredPage from '@/components/optimize-components/TopicCircle/ToBeAnsweredPage'
  import qrcade from 'qrcode'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import { userInfo } from '@/api/user'
  import { loginByToken } from '@/api/login'
  import {
    getCommunityCircleOrTopicDetail, // 获取圈子列表
    follow,
    getCommunityContentPage, // 获取最新
    getHotCommunityContentPage, // 获取最热
    getHotCommunityTypeLimit, // 获取热门话题、圈子
    getCommunityContentContributor, // 热门贡献者
    getCommunityQuestionPageByCommunityId, // 获取话题或圈子的待回答问题
    getUserPublishAbility,
  } from '@/api/topic-circle'
  export default {
    name: 'topicDetail',
    components: {
      Tabs,
      TabPane,
      ArticleList,
      ShortVideoPlayback,
      Unpack,
      HotTopicCircleList,
      ContentContributor,
      ToBeAnsweredPage,
      NullData
    },
    head() {
      return {
        title: this.pageInfo?this.pageInfo.title + '-话题圈子-脑医汇':'话题圈子-脑医汇',
        meta: [
          {
            hid: 'description',
            name: 'description',
            content: this.pageInfo.description?this.pageInfo.description:''
          },
          {
            hid: 'keywords',
            name: 'keywords',
            content: '脑医汇,神外资讯,神介资讯,神内资讯,话题名/圈子名,话题圈子,用户社区'
          }
        ],
        timer: null
      }
    },
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      if (!store.state.auth.user.id) {
        redirect(`/signin?fallbackUrl=${route.fullPath}`)
        return
      }
      // 圈子列表
      const [pageInfo, newData, hotData, hotCommunity, contribution, toBeAnswered] = await Promise.all([
        // 页面信息
        app.$axios.$request(
          getCommunityCircleOrTopicDetail({
            userId: store.state.auth.user.id,
            communityId: query.id
          })
        ),
        // 最新
        app.$axios.$request(
          getCommunityContentPage({
            userId: store.state.auth.user.id,
            communityId: query.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        // 最热
        app.$axios.$request(
          getHotCommunityContentPage({
            userId: store.state.auth.user.id,
            communityId: query.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        // 热门话题/圈子
        app.$axios.$request(
          getHotCommunityTypeLimit({
            communityType: 'C',
            userId: store.state.auth.user.id,
            limit: 5
          })
        ),
        // 热门贡献者
        app.$axios.$request(
          getCommunityContentContributor({
            communityId: query.id,
            userId: store.state.auth.user.id,
            limit: 5
          })
        ),
        // 待回答
        app.$axios.$request(
          getCommunityQuestionPageByCommunityId({
            communityIds: query.id,
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 5,
          })
        ),
      ])
      function pageListDataProcessing(data) {
        let list = []
        data.map(v=>{
          v[v.type].isCollect = v.isCollect;
          v[v.type].isDigg = v.isDigg;
          v[v.type].type = v.type;
          v[v.type].isTop = v.isTop;
          list.push(v[v.type]);
        })
        return list;
      }
      let pageNewData = pageListDataProcessing(newData.list);
      let pageHotData = pageListDataProcessing(hotData.list);
      let pageNewData_finished = false;
      if (1 >= newData.page.totalPage) {
        pageNewData_finished = true
      }
      let pageHotData_finished = false;
      if (1 >= hotData.page.totalPage) {
        pageHotData_finished = true
      }
      toBeAnswered.list.map(v=>{
        v.verificationBtn = false;
      })
      return {
        pageInfo: pageInfo.result,
        pageNewData,
        pageNewData_finished,
        pageHotData,
        pageHotData_finished,
        hotCommunity: hotCommunity.list,
        contribution,
        toBeAnswered,
      }
    },
    data(){
      return{
        qrShareUrl:'',
        pageActiveIndex: '0',
        // 最新tab
        pageNewestList_o: {
          pageNo: 2,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        // 最热Tab
        pageHottestList_o: {
          pageNo: 2,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        verificationBtn1: false,
        verificationBtn2: false,
        verificationBtn3: false,
        default_circle_cover: require('~/assets/images/topic_circle/icon-circle-cover.png'),
      }
    },
    methods: {
      // 提问
      toWrite_fn(num){
          const localToken = this.$cookies.get('medtion_token_only_sign')
          const isLogged = this.$store.state.auth.isLogged
          if (localToken || isLogged) {
            // 判断登录
            this.$axios.$request(loginByToken({
              token: localToken
            })).then(response => {
              if(response.code === 1){
                this.$axios.$request(userInfo()).then(res => {
                  if (res && res.code === 1) {
                    // 判断身份
                    if(res.result.identity === 3 || res.result.identity === 4){
                      this.$toast.fail('当前身份无法使用发布功能')
                      return;
                    }

                    // 回答提问条件判断
                    this.$axios
                      .$request(
                        getUserPublishAbility({
                          userId: this.$store.state.auth.user.id
                        })
                      )
                      .then((res) => {
                        if (res.code === 1) {
                          if (res.result) {
                            setTimeout(() => {
                              const { href } = this.$router.resolve({ path: `/topic-circle/write?circle=${this.$route.query.id}&circletitle=${this.pageInfo.title}` })
                              window.open(href, '_blank')
                            }, 0)
                            this.$analysys.btn_click('提问', document.title)
                          } else {
                            if(num === 1){
                              this.verificationBtn1 = true
                              this.verificationBtn2 = false
                              this.verificationBtn3 = false
                            } else if (num === 2) {
                              this.verificationBtn1 = false
                              this.verificationBtn2 = true
                              this.verificationBtn3 = false
                            } else if (num === 3) {
                              this.verificationBtn1 = false
                              this.verificationBtn2 = false
                              this.verificationBtn3 = true
                            }
                          }
                        }
                      })
                  }
                })
              }else{
                this.$toast('请先登录')
                this.$store.commit('editBackUrl', window.location.href)
                this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
              }
            })
          } else {
            this.$toast('请先登录')
            this.$store.commit('editBackUrl', window.location.href)
            this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
          }
      },
      // 图文
      writeInfo_fn() {
        const localToken = this.$cookies.get('medtion_token_only_sign')
        const isLogged = this.$store.state.auth.isLogged
        if (localToken || isLogged) {
          this.$axios.$request(loginByToken({
            token: localToken
          })).then(response => {
            this.$axios.$request(userInfo()).then(res => {
              if (res && res.code === 1) {
                // 判断身份
                if(res.result.identity === 3 || res.result.identity === 4){
                  this.$toast.fail('当前身份无法使用发布功能')
                  return;
                }
                // 身份认证
                if (res.result.isAuth === '1') {
                  window.open('/editor?type=2')
                } else if (res.result.isAuth === '2') {
                  // 病例 随笔 文章
                  window.open('/editor?type=2')
                } else if (res.result.isAuth === '3') {
                  this.$toast.fail('身份认证失败')
                } else if (!res.result.identity || res.result.identity === 'undefined') {
                  const h = this.$createElement
                  this.$msgbox({
                    message: h('p', null, [
                      h('span', null, '部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！')
                    ]),
                    center: true,
                    showCancelButton: true,
                    cancelButtonText: '以后再说',
                    confirmButtonText: '去完善信息',
                    customClass: 'personaldata-messagebox',
                    beforeClose: (action, instance, done) => {
                      if (action === 'confirm') {
                        this.$store.commit('editAccountTypeFun', this.$store.state.auth.user.email ? this.$store.state.auth.user.email : 'tel')
                        this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                        this.$router.push({ name: 'register' })
                        done()
                      } else {
                        done()
                      }
                    }
                  })
                } else {
                  this.$toast.fail('请先认证')
                  this.$store.dispatch('authenticationHandler')
                }
              }
            })
          })
        } else {
          this.$toast('请先登录')
          this.$store.commit('editBackUrl', window.location.href)
          this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
        }
      },
      // 跳话题详情
      toCommunityDetails_fn(id) {
        const { href } = this.$router.resolve({ path: `/topic-circle/community${this.$route.query.type}?id=${id}` })
        window.open(href, '_blank')
      },
      // 关注话题圈子
      followTopicCircle_fn(item) {
        this.$axios.$request(
          follow({
            contentId: item.id,
            userId: this.$store.state.auth.user.id,
            type: item.type
          })
        ).then(res => {
          if (res.code === 1) {
            item.isFollow = !item.isFollow
            this.$analysys.add_follow(this.$store.state.auth.user.id + '',item.id + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','圈子');
          }
        })
      },
      // 最新列表翻页
      getNewestData_fn(){
        this.pageNewestList_o.loading = true;
        this.$axios.$request(
          getCommunityContentPage({
            userId: this.$store.state.auth.user.id,
            communityId: this.$route.query.id,
            pageNo: this.pageNewestList_o.pageNo,
            pageSize: this.pageNewestList_o.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.pageNewestList_o.list = [...this.pageNewestList_o.list, ...this.pageListDataProcessing(res.list)]
            this.pageNewestList_o.response = true // 请求成功状态
            if (this.pageNewestList_o.pageNo >= res.page.totalPage) {
              this.pageNewestList_o.finished = true
            }
            this.pageNewestList_o.pageNo++
            this.pageNewestList_o.loading = false
          }
        }).catch(() => {
          this.pageNewestList_o.loading_error = true
          this.pageNewestList_o.response = true
        })
      },
      // 最热列表翻页
      getHotestData_fn(){
        this.pageHottestList_o.loading = true;
        this.$axios.$request(
          getHotCommunityContentPage({
            userId: this.$store.state.auth.user.id,
            communityId: this.$route.query.id,
            pageNo: this.pageHottestList_o.pageNo,
            pageSize: this.pageHottestList_o.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.pageHottestList_o.list = [...this.pageHottestList_o.list, ...this.pageListDataProcessing(res.list)]
            this.pageHottestList_o.response = true // 请求成功状态
            if (this.pageHottestList_o.pageNo >= res.page.totalPage) {
              this.pageHottestList_o.finished = true
            }
            this.pageHottestList_o.pageNo++
            this.pageHottestList_o.loading = false
          }
        }).catch(() => {
          this.pageHottestList_o.loading_error = true
          this.pageHottestList_o.response = true
        })
      },
      // 上拉加载
      scrollEvent(){
        let scrolltop = document.documentElement.scrollTop // 滚动条高度
        let windowHeight = document.documentElement.clientHeight // 视口高度
        let ListDom = document.querySelector('.dataFlow') // 列表对象
        let listHeight = ListDom.clientHeight // 列表高度
        let ListTop = ListDom.offsetTop // 列表距离文档顶部

        if ((listHeight + ListTop) - (scrolltop + windowHeight) < 300) {
          if (this.pageActiveIndex === '0') {
            // 最新
            if (!this.pageNewestList_o.loading && !this.pageNewestList_o.finished) {
              this.getNewestData_fn()
            }
          } else if (this.pageActiveIndex === '1') {
            // 最热
            if (!this.pageHottestList_o.loading && !this.pageHottestList_o.finished) {
              this.getHotestData_fn()
            }
          }
        }
      },
      pageListDataProcessing(data) {
        let list = []
        data.map(v => {
          v[v.type].isCollect = v.isCollect;
          v[v.type].isDigg = v.isDigg;
          v[v.type].type = v.type;
          v[v.type].isTop = v.isTop;
          list.push(v[v.type])
        })
        return list
      },
    },
    created(){
      this.pageNewestList_o.list = this.pageNewData // 全部列表
      this.pageNewestList_o.finished = this.pageNewData_finished // 全部列表
      this.pageHottestList_o.list = this.pageHotData // 关注列表
      this.pageHottestList_o.finished = this.pageHotData_finished // 关注列表
    },
    mounted() {
      qrcade.toDataURL(window.location.href)
        .then((img) => {
          this.qrShareUrl = img
        })
        .catch((err) => {
          console.log(err)
        })
      window.addEventListener('scroll', this.scrollEvent) // 顶部导航切换、翻页数据
      // console.log('info',this.pageInfo)
      // console.log('new',this.pageNewData)
      // console.log('hot',this.pageHotData)
      // console.log('热门话题圈子',this.hotCommunity)
      // console.log('内容贡献者',this.contribution)
      // console.log('待回答',this.toBeAnswered)
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.scrollEvent)
    }
  }
</script>

<style scoped lang="less">
  #topicDetail {
    background: #FBFBFB;
  }
  .main{
    max-width: 1200px;
    width: 1200px;
    margin: 0 auto;
    transition: all 0.3s;
    padding: 30px 0;
  }
  .topicDetailHeader {
    width: 100%;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    .info{
      display: flex;
      img{
        height: 128px;
        border-radius: 4px;
        margin-right: 12px;
      }
      .title{
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 128px;
        h3{
          font-size: 32px;
        }
        p{
          font-size: 14px;
          color: #999;

          span{
            border-right: 1px solid #F6F6F6;
            padding-right: 8px;
            margin-right: 8px;
            &:last-of-type{
              border-right: none;
            }
          }
        }
      }
    }
    .menu{
      display: flex;
      align-items: center;
      justify-content: right;

      li{
        font-size: 14px;
        color: #0581ce;
        border: 1px solid #0581ce;
        border-radius: 4px;
        line-height: 40px;
        width: 108px;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .topic-icon{
          margin-right: 6px;
          width: 14px;
          height: 14px;
        }
        .topic-icon2{
          width: 16px;
          height: 16px;
        }
        &.on {
          background: #D2D2D2;
          color: #fff;
          border-color: #D2D2D2;

          &::after {
            content: '已关注';
          }

          &:hover {
            background: #AFC1CC;

            &::after {
              content: '取消关注';
            }
          }
        }
      }

      .share {
        position: relative;

        &:hover .shareQRBox, .shareQRBox:hover {
          visibility: inherit;
          opacity: 1;
          pointer-events: inherit;
        }

        .shareQRBox {
          position: absolute;
          top: 38px;
          left: -7px;
          visibility: hidden;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
          opacity: 0;
          transition: all .3s;
          pointer-events: none;
          padding-top: 10px;
          z-index: 2;

          img {
            width: 130px;
            height: 130px;
            border-radius: 4px 4px 0 0;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
          }

          span {
            color: #222222;
            font-size: 14px;
            margin-top: -0.7rem;
            background: #fff;
            width: 100%;
            border-radius: 0 0 4px 4px;
            text-align: center;
            box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
    .topicMsg{
      font-size: 15px;
      margin-top: 20px;
    }
  }
  .left-bigbox {
    padding: 0 20px 20px;
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 0 0 4px rgb(237 237 237);
    background: #fff;

    .nullData {
      padding-bottom: 100px;

      & > span {
        display: flex;
        justify-content: center;
        margin-top: 32px;
        font-size: 16px;
        color: #222;

        i {
          font-style: normal;
          color: #0581ce;
          cursor: pointer;
        }
      }
    }

    /deep/ .el-tabs__item {
      font-size: 18px;
    }

    /deep/ .el-tabs__nav-wrap{
      line-height: 57px;
      &::after {
        background-color: #F6F6F6;
      }
    }

    /deep/ .el-tabs__active-bar {
      width: 16px !important;
      margin-left: 10px;
    }
  }
  .rightItem {
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    padding: 20px 16px 0;
    margin-bottom: 20px;

    .rightItemTitle {
      font-size: 18px;
      padding-bottom: 12px;
      border-bottom: 1px solid #F6F6F6;
      font-weight: bold;
    }

    .conCreation {
      display: flex;
      justify-content: center;
      padding-bottom: 20px;
      margin-top: 16px;

      li {
        cursor: pointer;
        &:first-of-type{
          margin-right: 48px;
        }

        img {
          width: 68px;
          height: 68px;
        }

        i {
          font-size: 18px;
          display: block;
          text-align: center;
          margin-top: 12px;
          font-style: normal;
        }
      }
    }
  }
  .loadingList{
    border-top: 1px solid #F6F6F6;
    margin-top: 20px;
    padding-top: 20px;
  }
  /deep/ .el-tabs__item.is-a ctive{
    color: #0581ce;
  }

  /deep/ .el-tabs__content{
    overflow: initial;
  }
</style>
