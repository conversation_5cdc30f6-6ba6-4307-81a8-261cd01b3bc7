<template>
  <div id="citing">
    <tabs v-model="pageActiveIndex">
      <tab-pane label="我的发布">
        <el-input
          v-model="search_release_text"
          @input="releaseSearch_fn"
          class="myReleaseInput"
          type="text" placeholder="请输入标题"></el-input>
        <div class="release_list">
          <EditCiting v-if="this.myRelease.list.length" class="releaseListDom" :data_list="this.myRelease.list"/>
          <NullData v-else text="您还没有发布过内容哦"/>
        </div>
      </tab-pane>
      <tab-pane label="我的收藏">
        <el-input
          v-model="search_collect_text"
          @input="collectSearch_fn"
          class="myCollectInput"
          type="text" placeholder="请输入标题"></el-input>
        <div class="collect_list">
          <EditCiting v-if="this.myCollect.list" class="collectListDom" :data_list="this.myCollect.list"/>
          <NullData v-else text="您还没有收藏过内容哦"/>
        </div>
      </tab-pane>
    </tabs>
  </div>
</template>

<script>
  import { Tabs, TabPane } from 'element-ui'
  import EditCiting from '@/components/optimize-components/TopicCircle/EditCiting'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import {
    searchUserPublishInfo,
    getReferenceCollectContent,
  } from '@/api/topic-circle'

  export default {
    name: 'index',
    components: {
      Tabs,
      TabPane,
      EditCiting,
      NullData
    },
    data() {
      return {
        pageActiveIndex: '0',
        search_release_text: '',
        myRelease: {
          pageNo: 1,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        search_collect_text: '',
        myCollect: {
          pageNo: 1,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        }
      }
    },
    methods: {
      releaseSearch_fn(){
        // 我的发布搜索
        this.searchUserPublishInfo_fn('init')
      },
      // 我的发布搜索
      searchUserPublishInfo_fn(type){
        if (!this.$store.state.auth.user.id) {
          this.$router.push(`/signin?fallbackUrl=${this.$route.fullPath}`)
          return
        }
        if(type === 'init'){
          this.myRelease.pageNo = 1;
          this.myRelease.list = [];
        }
        this.myRelease.loading = true;
        this.$axios.$request(
          searchUserPublishInfo({
            userId: this.$store.state.auth.user.id,
            profileUserId: this.$store.state.auth.user.id,
            userType: this.$store.state.auth.user.userCategory === 'P'?'U':'EU',
            searchWord: this.search_release_text,
            pageNo: this.myRelease.pageNo,
            pageSize: this.myRelease.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.myRelease.list = [...this.myRelease.list, ...this.sortDate_fn(res.list)]
            this.myRelease.response = true // 请求成功状态
            if (this.myRelease.pageNo >= res.page.totalPage) {
              this.myRelease.finished = true
            }
            this.myRelease.pageNo++
            this.myRelease.loading = false
          }
        }).catch(() => {
          this.myRelease.loading_error = true
          this.myRelease.response = true
        })
      },
      collectSearch_fn(){
        // 我的收藏搜索
        this.getReferenceCollectContent_fn('init');
      },
      // 我的收藏搜索接口
      getReferenceCollectContent_fn(type){
        if(type === 'init'){
          this.myCollect.pageNo = 1;
          this.myCollect.list = [];
        }
        this.myCollect.loading = true;
        this.$axios.$request(
          getReferenceCollectContent({
            userId: this.$store.state.auth.user.id,
            searchCode: this.search_collect_text,
            pageNo: this.myCollect.pageNo,
            pageSize: this.myCollect.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.myCollect.list = [...this.myCollect.list, ...this.sortDate_fn(res.list)]
            this.myCollect.response = true // 请求成功状态
            if (this.myCollect.pageNo >= res.page.totalPage) {
              this.myCollect.finished = true
            }
            this.myCollect.pageNo++
            this.myCollect.loading = false
          }
        }).catch(() => {
          this.myCollect.loading_error = true
          this.myCollect.response = true
        })
      },
      sortDate_fn(res){
        let arr = [];
        res.map(v=>{
          let item = {};
          if(v.type === 'info'){
            item.type = v.type;
            item.showType = '文章'
            item.cover = v[v.type].infoImg;
            item.title = v[v.type].infoTitle;
            item.authorList = v[v.type].authorList;
            item.id = v[v.type].infoId;
            item.essences = v[v.type].essences;
            item.url = v[v.type].url
          }else if(v.type === 'mp_article'){
            item.type = v.type;
            item.showType = '病例'
            let creator = [];
            if(v[v.type].creator){
              creator.push(v[v.type].creator)
            }
            item.cover = v[v.type].cover;
            item.title = v[v.type].title;
            item.authorList = creator;
            item.id = v[v.type].id;
            item.essences = v[v.type].essences;
            item.url = v[v.type].articleUrl;
          }else if(v.type === 'shortVideo'){
            item.showType = '短视频'
            item.type = v.type;
            let creator = [];
            if(v[v.type].creator){
              creator.push(v[v.type].creator)
            }
            item.cover = v[v.type].cover;
            item.title = v[v.type].title;
            item.authorList = creator;
            item.id = v[v.type].id;
            item.url = `/shortvideo/detail?id=${v[v.type].id}`;
          }else if(v.type === 'communityQA'){
            item.type = v.type;
            item.title = v[v.type].title;
            item.id = v[v.type].id;
            item.time = v[v.type].publishTime;
            item.class = v[v.type].type;
            item.description = v[v.type].regexText;
            item.url = v[v.type].qaDetailUrl;
            item.desc = v[v.type].regexText;
          }else if(v.type === 'qa_topic'){

          }else if(v.contentType === 'mpArticle'){
            item.showType = v.showType
            item.type = v.contentType;
            let creator = [];
            if(v.content.creator){
              creator.push(v.content.creator)
            }
            item.cover = v.content.mpArticleCover;
            item.title = v.content.mpArticleTitle;
            item.authorList = creator;
            item.id = v.content.mpArticleId;
            item.essences = v.content.essences;
            item.url = v.content.url
          }else if(v.contentType === 'pMpArticle'){
            item.type = v.contentType;
            item.showType = v.showType
            let creator = [];
            if(v.content.creator){
              creator.push(v.content.creator)
            }
            item.cover = v.content.mpArticleCover;
            item.title = v.content.mpArticleTitle;
            item.authorList = creator;
            item.id = v.content.mpArticleId;
            item.essences = v.content.essences;
            item.url = v.content.url
          }else if(v.contentType === 'meeting'){
            item.type = v.contentType;
            item.showType = v.showType
            item.cover = v.content.meetingCover;
            item.title = v.content.meetingName;
            item.id = v.content.id;
            item.authorList = v.content.meetingDateStr;
            item.url = v.content.url
          }else if(v.contentType === 'info'){
            item.type = v.contentType;
            item.showType = v.showType
            item.cover = v.content.infoCover;
            item.title = v.content.infoTitle;
            item.id = v.content.infoId;
            item.authorList = v.content.authorList;
            item.essences = v.content.essences;
            item.url = v.content.url
          }else if(v.contentType === 'pInfo'){
            item.type = v.contentType;
            item.showType = v.showType
            item.cover = v.content.infoCover
            item.title = v.content.infoTitle
            item.id = v.content.infoId
            item.authorList = v.content.authorList
            item.essences = v.content.essences
            item.url = v.content.url
          }else if(v.contentType === 'courseVideo'){
            item.type = v.contentType;
            item.showType = v.showType
            // let creator = [];
            // if(v.content.speakerNames){
            //   creator.push(v.content.speakerNames)
            // }
            item.cover = v.content.courseCover;
            item.title = v.content.name;
            item.id = v.content.courseId;
            item.authorList = v.content.speakerNames;
            item.url = v.content.url
          }else if(v.contentType === 'shortVideo'){
            item.type = v.contentType;
            item.showType = v.showType
            let creator = [];
            if(v.content.creator){
              creator.push(v.content.creator)
            }
            item.cover = v.content.cover;
            item.title = v.content.title;
            item.id = v.content.id;
            item.authorList = creator;
            item.url = `/shortvideo/detail?id=${v.content.id}`;
          } else if (v.contentType === 'communityQA') {
            item.type = v.contentType;
            item.title = v.content.title;
            item.id = v.content.id;
            item.time = v.content.publishTime;
            item.class = v.content.type;
            item.description = v.content.regexText;
            item.url = v.content.qaDetailUrl;
          }
          // else if(true){
          //   item.cover = v.content.meetingCover;
          //   item.title = v.content.meetingName;
          //   item.id = v.content.id;
          //   item.time = v.content.meetingDateStr;
          //   item.authorList = v.content.meetingDateStr;
          //   item.essences = v.content.meetingDateStr;
          //   item.description = v.content.meetingDateStr;
          //   item.class = v.content.meetingDateStr;
          // }
          if(item.type){
            arr.push(item)
          }
        })
        return arr;
      },
      scrollEvent() {
        if (this.pageActiveIndex === '0') {
          let scrolltop = document.querySelector('.release_list').scrollTop // 滚动条高度
          let windowHeight = document.querySelector('.release_list').clientHeight // 视口高度
          let ListDom = document.querySelector('.releaseListDom') // 列表对象
          let listHeight = ListDom.clientHeight // 列表高度
          if (listHeight - (scrolltop + windowHeight) < 50) {
            if (!this.myRelease.loading && !this.myRelease.finished) {
              this.searchUserPublishInfo_fn()
            }
          }
        } else if (this.pageActiveIndex === '1') {
          let scrolltop = document.querySelector('.collect_list').scrollTop // 滚动条高度
          let windowHeight = document.querySelector('.collect_list').clientHeight // 视口高度
          let ListDom = document.querySelector('.collectListDom') // 列表对象
          let listHeight = ListDom.clientHeight // 列表高度
          if (listHeight - (scrolltop + windowHeight) < 50) {
            if (!this.myCollect.loading && !this.myCollect.finished) {
              this.getReferenceCollectContent_fn();
            }
          }
        }
      }
    },
    mounted() {
      let returntop = document.getElementById('returntop')
      if(returntop){
        returntop.style.cssText = 'transform:translateY(-100%);margin-top:-60px'
      }
      this.searchUserPublishInfo_fn('init'); // 我的发布
      this.getReferenceCollectContent_fn('init'); // 我的收藏
      document.querySelector('.release_list').addEventListener('scroll', this.scrollEvent)
      document.querySelector('.collect_list').addEventListener('scroll', this.scrollEvent)
    },
    beforeDestroy() {
      document.querySelector('.release_list').removeEventListener('scroll', this.scrollEvent)
      document.querySelector('.collect_list').removeEventListener('scroll', this.scrollEvent)
    }
  }
</script>

<style scoped lang="less">
  #citing {
    padding: 0 20px;
  }

  .release_list, .collect_list {
    height: 596px;
    overflow: auto;
    margin-top: 10px;
    box-sizing: border-box;
  }

  /deep/ .el-tabs__nav {
    line-height: 30px;
  }

  /deep/ .el-tabs__item {
    font-size: 14px;
    line-height: 40px;
    height: 40px;
  }

  /deep/ .el-tabs__nav-wrap {
    line-height: 57px;

    &::after {
      background-color: #F6F6F6;
    }
  }

  /deep/ .el-tabs__active-bar {
    width: 16px !important;
    margin-left: 20px;
  }
</style>
