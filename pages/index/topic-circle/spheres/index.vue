<template>
  <div id="circle">
    <header class="header">
      <div class="main">
        <div class="headerCon">
          <h3 class="pageTitle">脑医汇 — 圈子</h3>
          <span class="pageInfo">{{setNum1000(statistics.result.contents)}}内容&nbsp;&nbsp;&nbsp;&nbsp;{{setNum1000(statistics.result.views)}}浏览&nbsp;&nbsp;&nbsp;&nbsp;{{setNum1000(statistics.result.answers)}}回答</span>
        </div>
        <div class="share">
          <svg-icon class-name='topic-icon' icon-class='topic_share_blue'/>
          分享
          <div class="shareQRBox">
            <img :src="qrShareUrl" alt="">
            <span>微信扫一扫</span>
          </div>
        </div>
      </div>
    </header>
    <div class="main">
      <ul class="circleList">
        <li v-for="(item,index) in circleList_o.list" :key="index" @click="toCommunityDetails_fn(item)">
          <img :src="item.image?item.image:default_circle_share" alt="">
          <h5>{{item.title}}</h5>
          <p class="info"><span>{{item.follows}}关注</span><span>{{item.views}}内容</span></p>
          <p class="msg">{{item.latestContentTitle}}</p>
          <b :class="`${item.isFollow?'active':''}`" @click.stop="followTopicCircle_fn(item)">
            <template v-if="item.isFollow">
              已关注
            </template>
            <template v-else>
              <svg-icon class-name='topic-icon' icon-class='topic_plus'/>
              关注
            </template>
          </b>
        </li>
        <NullData v-if="!circleList_o.list.length" text="暂无内容"/>
      </ul>
    </div>
    <div class="more" @click="getMoreList_fn" v-if="!circleList_o.loading && !circleList_o.finished">查看更多<i class="el-icon-arrow-down"></i></div>
  </div>
</template>

<script>
  import qrcade from 'qrcode'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import {
    getCommunityCirclePage, // 获取圈子列表
    follow,
    getCommunitySums,
  } from '@/api/topic-circle'
  export default {
    name: 'circlePage',
    head() {
      return {
        title: '圈子-话题圈子-脑医汇',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '脑医汇,神外资讯,神介资讯,神内资讯,圈子,用户社区'
          },
          {
            hid: 'description',
            name: 'description',
            content: '分享业内热闻，畅游医学圈子。'
          },
        ],
        timer: null
      }
    },
    components: {NullData},
    computed:{
      setNum1000(){
        return (num)=>{
          if (num > 999) {
            return (num/1000).toFixed(1) + 'k'
          }else{
            return num;
          }
        }
      },
    },
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      if (!store.state.auth.user.id) {
        redirect(`/signin?fallbackUrl=${route.fullPath}`)
        return
      }
      // 圈子列表
      const [circleList, statistics] = await Promise.all([
        app.$axios.$request(
          getCommunityCirclePage({
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        app.$axios.$request(
          getCommunitySums({
            type: 'C'
          })
        ),
      ])
      let followFinished = false;
      if (1 >= circleList.page.totalPage) {
        followFinished = true;
      }
      return {
        circleList, // 列表
        followFinished, // 是否第一页就加载完了
        statistics,
      }
    },
    data(){
      return{
        qrShareUrl: '',
        circleList_o:{
          list: [],
          pageNo: 2,
          pageSize: 10,
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        default_topic_share: require('~/assets/images/topic_circle/icon-topic-share.png'),
        default_circle_share: require('~/assets/images/topic_circle/icon-circle-share.png'),
      }
    },
    methods:{
      // 跳转
      toCommunityDetails_fn(item){
        this.$router.push(`/topic-circle/communitycircle?id=${item.id}`)
      },
      // 关注
      followTopicCircle_fn(item) {
        this.$axios.$request(
          follow({
            contentId: item.id,
            userId: this.$store.state.auth.user.id,
            type: item.type
          })
        ).then(res => {
          if (res.code === 1) {
            item.isFollow = !item.isFollow
            this.$analysys.add_follow(this.$store.state.auth.user.id + '',item.id + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','圈子');
          }
        })
      },
      // 加載更多
      getMoreList_fn(){
        this.circleList_o.loading = true
        this.$axios.$request(
          getCommunityCirclePage({
            userId: this.$store.state.auth.user.id,
            pageNo: this.circleList_o.pageNo,
            pageSize: this.circleList_o.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.circleList_o.list = [...this.circleList_o.list, ...res.list]
            this.circleList_o.response = true // 请求成功状态
            if (this.circleList_o.pageNo >= res.result.page.totalPage) {
              this.circleList_o.finished = true
            }
            this.circleList_o.pageNo++
            this.circleList_o.loading = false
          }
        }).catch(() => {
          this.circleList_o.loading_error = true
          this.circleList_o.response = true
        })
      },
    },
    created(){
      this.circleList_o.list = this.circleList.list;
      this.circleList_o.finished = this.followFinished;
    },
    mounted() {
      qrcade.toDataURL(window.location.href)
        .then((img) => {
          this.qrShareUrl = img
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
</script>

<style scoped lang="less">
  #circle {
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
  }

  .main {
    max-width: 1200px;
    width: 1200px;
    margin: 0 auto;
    transition: all 0.3s;

    display: flex;
    justify-content: space-between;
  }

  .header {
    background: url("assets/images/topic_circle/circle_header_bg.png") no-repeat center;
    background-size: cover;
    height: 200px;
    margin-bottom: -60px;
    color: #fff;
    .headerCon{
      padding-top: 34px;
      .pageTitle {
        font-size: 32px;
        margin-bottom: 12px;
      }

      .pageInfo {
        font-size: 14px;
      }
    }
    .share {
      background: #fff;
      border-radius: 4px;
      color: #0581ce;
      padding: 0 30px;
      margin-top: 34px;
      line-height: 38px;
      height: 38px;
      flex: 0 0 auto;
      cursor: pointer;
      position: relative;

      .topic-icon {
        margin-right: 4px;
      }

      &:hover .shareQRBox,.shareQRBox:hover {
        visibility: inherit;
        opacity: 1;
        pointer-events: inherit;
      }

      .shareQRBox {
        position: absolute;
        top: 38px;
        left: -7px;
        visibility: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        opacity: 0;
        transition: all .3s;
        pointer-events: none;
        padding-top: 10px;
        z-index: 2;

        &::after{
          content:'';
          border-width: 6px;
          border-color: transparent;
          border-bottom-color: #fff;
          border-style: solid;
          position: absolute;
          top: -2px;
        }

        img {
          width: 130px;
          height: 130px;
          border-radius: 4px 4px 0 0;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
        }

        span {
          color: #222222;
          font-size: 14px;
          margin-top: -0.7rem;
          background: #fff;
          width: 100%;
          border-radius: 0 0 4px 4px;
          text-align: center;
          box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
  .circleList{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 40px;
    li{
      cursor: pointer;
      display: flex;
      flex-direction: column;
      padding: 22px;
      background: #fff;
      border-radius: 6px;
      width: 284px;
      box-sizing: border-box;
      position: relative;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
      margin-bottom: 42px;
      margin-right: 21px;
      &:nth-child(4n){
        margin-right: 0;
      }

      img{
        width: 240px;
        height: 135px;
        border-radius: 4px;
        object-fit: cover;
      }

      h5{
        font-size: 18px;
        text-align: center;
        font-weight: bold;
        margin-top: 16px;
      }
      .info{
        font-size: 14px;
        color: #999;
        text-align: center;
        margin-top: 12px;
        span:first-of-type{
          margin-right: 24px;
          margin-top: 14px;
        }
      }
      .msg{
        font-size: 15px;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        margin-top: 16px;
        margin-bottom: 14px;
      }
      b {
        color: #0581CE;
        border: 1px solid #0581ce;
        border-radius: 100px;
        font-size: 15px;
        width: 90px;
        line-height: 32px;
        font-weight: normal;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        position: absolute;
        bottom: -17px;
        left: 0;
        right: 0;
        margin: 0 auto;

        &.active {
          background: #D2D2D2;
          border-color: #D2D2D2;
          color: #fff;
        }

        svg {
          width: 10px;
          margin-right: 4px;
        }
      }
    }
  }
  .more{
    width: 164px;
    line-height: 48px;
    background: #fff;
    color: #666;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    font-size: 14px;
    text-align: center;
    margin: 0 auto;
    border-radius: 6px;
    cursor: pointer;
  }
</style>
