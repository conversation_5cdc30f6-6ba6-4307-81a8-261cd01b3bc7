<template>
  <div id="search">
    <TopicCircleNav/>
    <div class="main">
      <div class="page-left">
        <div class="left-bigbox">
          <tabs :value="type" @tab-click="handleClick">
            <tab-pane label="默认">
              <ArticleList
                v-if="$store.state.topicCircle.searchListData.length"
                :data_list="
                  $store.state.topicCircle.searchListData.length > 0
                    ? $store.state.topicCircle.searchListData.map(
                        (i) => i.community_qa
                      )
                    : []
                "
                :is-only-answers="false"
              />
              <NullData v-else text="暂无内容"/>
            </tab-pane>
            <tab-pane label="最新"
            >
              <ArticleList
                v-if="$store.state.topicCircle.searchListData.length"
                :data_list="
                  $store.state.topicCircle.searchListData.length > 0
                    ? $store.state.topicCircle.searchListData.map(
                        (i) => i.community_qa
                      )
                    : []
                "
                :is-only-answers="false"
              />
              <NullData v-else text="暂无内容"/>
            </tab-pane>
            <tab-pane label="最热"
            >
              <ArticleList
                v-if="$store.state.topicCircle.searchListData.length"
                :data_list="
                  $store.state.topicCircle.searchListData.length > 0
                    ? $store.state.topicCircle.searchListData.map(
                        (i) => i.community_qa
                      )
                    : []
                "
                :is-only-answers="false"
              />
              <NullData v-else text="暂无内容"/>
            </tab-pane>
          </tabs>
        </div>
      </div>
      <div class="page-right">
        <div class="rightItem">
          <h3 class="rightItemTitle">热门搜索</h3>
          <ul class="hotSearchlist">
            <li
              v-for="item in hotSearchList"
              :key="item.id"
              @click="hotClick(item)"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <ShortVideoPlayback
      :video-id='$store.state.topicCircle.topicCircleHomeShortVideoId'
      :visible='$store.state.topicCircle.topicCircleHomeShortVisible'
      @cancelFn='(flag) => $store.commit("topicCircle/setTopicCircleHomeShortVisible",flag)'
    />
  </div>
</template>

<script>
import {Tabs, TabPane} from 'element-ui'
import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
import ShortVideoPlayback from '@/components/optimize-components/public/ShortVideoPlayback'
import {getHotSearch, searchAns} from '@/api/topic-circle'
import QuestionsAnswers from '@/components/optimize-components/TopicCircle/QuestionsAnswers'
import NullData from '@/components/optimize-components/TopicCircle/NullData'
import TopicCircleNav from '@/components/PageComponents/Nav/TopicCircleNav'

export default {
  name: 'SearchTopic',
  head() {
    return {
      title: this.$route.query.keywordsTopic + ' - 搜索 - 话题圈子',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content:
            '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ],
      timer: null
    }
  },
  components: {
    Tabs,
    TabPane,
    ArticleList,
    ShortVideoPlayback,
    QuestionsAnswers,
    NullData,
    TopicCircleNav
  },
  data() {
    return {
      // type: '0', // 数据类型 0:全部 1:最新 2:最热
      hotSearchList: [],
      loading: false, // 加载状态结束
      finished: false, // 数据全部加载完成
      oldScrolltop: 0, // 记录旧的滚动条高度
      timer: null, // 滚动条延迟事件
      isNavClick: false, // 是否是点击的nav
    }
  },
  // watchQuery(nv, ov) {
  //   if (nv.hasOwnProperty('index')) {
  //     this.$store.commit('topicCircle/setSearchTab', nv.index)
  //     this.type = nv.index
  //   } else {
  //     this.$store.commit('topicCircle/setSearchTab', '1')
  //     this.type = '1'
  //   }
  //   if (nv.hasOwnProperty('keywordsTopic')) {
  //     this.$store.commit('topicCircle/editSeachInfoDataFun', nv.keywordsTopic)
  //   }
  //   if (!this.loading) {
  //     this.searchFunTopic()
  //   }
  // },
  computed: {
    type() {
      return this.$store.state.topicCircle.searchTab
    },
    keywordsTopic() {
      return this.$store.state.topicCircle.seachInfo
    },
  },
  watch: {
    type(nv, ov) {
      if (nv !== ov) {
        this.finished = false
        this.$store.commit('topicCircle/editSearchListCurrent', 1)
        this.$store.commit('topicCircle/editSearchListDataFun', [])
        this.$store.commit('topicCircle/editSearchListTotalFun', 0)
        this.searchFunTopic()
      }
    },
    keywordsTopic(nv, ov) {
      if (nv !== ov) {
        this.finished = false;
        this.$store.commit('topicCircle/editSearchListCurrent', 1)
        this.$store.commit('topicCircle/editSearchListDataFun', [])
        this.$store.commit('topicCircle/editSearchListTotalFun', 0)
        this.searchFunTopic()
      }
    },
    '$store.state.topicCircle.clickNavTab'(nv, ov) {
      if (nv !== ov) {
        // 是点击的nav
        this.isNavClick = true
        this.scrollTopHandle()
      }
    },
    '$store.state.global.scroll'(scroll) {
      if (this.isNavClick) return;
      const windowHeight = document.documentElement.clientHeight // 视口高度
      const ListDom = document.querySelector('.page-left') // 列表对象
      const listHeight = ListDom.clientHeight // 列表高度
      const ListTop = ListDom.offsetTop // 列表距离文档顶部

      const nav = document.getElementById('nav_header')
      const topicNav = document.getElementById('topic_circle_nav')


      // 是否显示话题圈子Nav
      if (scroll > 60) {
        if (scroll - this.oldScrolltop > 0) {
          nav.style.cssText = 'transform:translateY(-100%)'
          topicNav.style.cssText = 'transform:none'
        } else {
          nav.style.cssText = 'transform:none'
          topicNav.style.cssText = 'transform:translateY(-100%)'
        }
        this.oldScrolltop = scroll
      }
      if (listHeight + ListTop - (scroll + windowHeight) < 300) {
        if (!this.loading && !this.finished) {
          this.getList()
        }
      }
    }
  },
  created() {
    this.$store.commit('topicCircle/setSearchTab', '1')
    if (this.$route.query.hasOwnProperty('keywordsTopic')) {
      this.$store.commit(
        'topicCircle/editSeachInfoDataFun',
        this.$route.query.keywordsTopic
      )
    }
    this.getHotSearchList()
    this.searchFunTopic()
  },
  beforeDestroy() {
    this.$store.commit('topicCircle/setTopNavSwitch', false)
  },
  methods: {
    handleClick(tab, event) {
      // this.type = tab.index
      this.$store.commit('topicCircle/editSearchListCurrent', 1)
      this.$store.commit('topicCircle/editSearchListDataFun', [])
      this.$store.commit('topicCircle/editSearchListTotalFun', 0)
      // this.$router.replace({
      //   path: '/topic-circle/search',
      //   query: {
      //     index: tab.index,
      //     keywordsTopic: this.$store.state.topicCircle.seachInfo,
      //   },
      // })
      this.$store.commit('topicCircle/setSearchTab', tab.index + '')
      // this.searchFunTopic()
    },
    // 获取热门搜索
    getHotSearchList() {
      if (!this.$store.state.auth.user.id) {
        this.$router.push(`/signin?fallbackUrl=${this.$route.fullPath}`)
        return
      }
      this.$axios.$request(getHotSearch({
        userId: this.$store.state.auth.user.id
      })).then((res) => {
        if (res && res.code === 1) {
          if (res.list && res.list.length > 0) {
            this.hotSearchList = [...res.list]
          }
        }
      })
    },
    searchFunTopic() {
      // this.$analysys.btn_click('搜索', document.title)
      // this.$store.commit('topicCircle/editSeachInfoDataFun', this.keywords)
      // 如果没有更多数据了，就不再请求
      if (this.finished) {
        return
      }

      this.loading = true

      this.$axios
        .$request(
          searchAns({
            platform: 'A',
            keywords: this.$store.state.topicCircle.seachInfo,
            orderRule: this.type,
            userId: this.$store.state.auth.user.id,
            // order: 'desc',
            pageno: 1,
            pagesize: this.$store.state.channel_count,
          })
        )
        .then((res) => {
          if (res && res.code === 1) {
            this.$store.commit('topicCircle/editSearchListCurrent', 1)
            // this.$analysys.search(
            //   res.list.length > 0,
            //   res.list.length,
            //   this.iscruxFlag,
            //   this.searchInfo,
            //   this.iscruxFlag,
            //   '全局搜索'
            // )
            this.$store.commit('topicCircle/editSearchListDataFun', res.list)
            this.$store.commit(
              'topicCircle/editSearchListTotalFun',
              res.page.totalCount
            )
            // 保存历史搜索
            this.$store.commit('topicCircle/editHistoricalSearchInfoFun', this.$store.state.topicCircle.seachInfo)
            if (
              res.page.totalCount >
              this.$store.state.topicCircle.searchListData.length
            ) {
              this.finished = false
            } else {
              this.finished = true
            }
          } else {
            this.$toast.fail(res.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getList() {
      // 如果没有更多数据了，就不再请求
      if (this.finished) {
        return
      }
      this.loading = true

      this.$axios
        .$request(
          searchAns({
            platform: 'A',
            keywords: this.$store.state.topicCircle.seachInfo,
            orderRule: this.type,
            userId: this.$store.state.auth.user.id,
            // order: 'desc',
            pageno: this.$store.state.topicCircle.searchListCurrent + 1,
            pagesize: this.$store.state.channel_count,
          })
        )
        .then((res) => {
          if (res && res.code === 1) {
            this.$store.commit(
              'topicCircle/editSearchListCurrent',
              this.$store.state.topicCircle.searchListCurrent + 1
            )
            // this.$analysys.search(
            //   res.list.length > 0,
            //   res.list.length,
            //   this.iscruxFlag,
            //   this.searchInfo,
            //   this.iscruxFlag,
            //   '全局搜索'
            // )
            this.$store.commit('topicCircle/editSearchListDataFun', [
              ...this.$store.state.topicCircle.searchListData,
              ...res.list,
            ])
            this.$store.commit(
              'topicCircle/editSearchListTotalFun',
              res.page.totalCount
            )
            if (
              res.page.totalCount >
              this.$store.state.topicCircle.searchListData.length
            ) {
              this.finished = false
            } else {
              this.finished = true
            }
          } else {
            this.$toast.fail(res.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    hotClick(item) {
      let url = ''
      switch (item.type) {
        case 'T':
          url = '/topic-circle/communitytopic?id=' + item.id
          break
        case 'Q':
          url = '/topic-circle/questionhome?id=' + item.id
          break
        default:
          url = '/topic-circle'
          break
      }
      this.$router.push(url)
    },
    scrollTopHandle() {
      window.scrollTo(0, 87, 300)
      setTimeout(() => {
        this.isNavClick = false
      }, 300)

    }
  },
}
</script>

<style scoped lang="less">
#search {
  background: #fbfbfb;
  overflow: hidden;
  padding-bottom: 60px;
}

.main {
  max-width: 1200px;
  width: 1200px;
  margin: 0 auto;
  transition: all 0.3s;
  padding: 30px 0;
}

.left-bigbox {
  padding: 0 20px 20px;
  border-radius: 6px;
  margin-bottom: 10px;
  box-shadow: 0 0 4px rgb(237 237 237);
  background: #fff;

  /deep/ .el-tabs__item {
    font-size: 18px;
  }

  /deep/ .el-tabs__nav-wrap {
    line-height: 57px;

    &::after {
      background-color: #f6f6f6;
    }
  }

  /deep/ .el-tabs__active-bar {
    width: 16px !important;
    margin-left: 10px;
  }
}

.rightItem {
  background: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  padding: 20px 16px 0;
  margin-top: 20px;

  &:first-of-type {
    margin-top: 0;
  }

  .rightItemTitle {
    font-size: 18px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f6f6f6;
    font-weight: bold;
  }

  .hotSearchlist {
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    font-size: 16px;

    li {
      margin-bottom: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }
  }
}

/deep/ .el-tabs__item.is-active {
  color: #0581ce;
}

/deep/ .el-tabs__content {
  overflow: initial;
}
</style>
