<template>
  <div id="activity">
    <header class="header">
      <div class="main">
        <div class="headerCon">
          <h3 class="pageTitle">活动广场</h3>
        </div>
        <div class="share">
          <svg-icon class-name='share-icon' icon-class='topic_share_blue'/>
          分享
          <div class="shareQRBox">
            <img :src="qrShareUrl" alt="">
            <span>微信扫一扫</span>
          </div>
        </div>
      </div>
    </header>
    <div class="main">
      <ul class="activityList">
        <li v-for="item in pageData_a.list" :key="item.id" :class="item.isActivityEnd?'end':'ing'" @click="toDetail_fn(item.id)">
          <img :src="item.thumbnail?item.thumbnail:default_activity_cover" alt="">
          <span>{{item.title}}</span>
        </li>
      </ul>
    </div>
    <div class="more" @click="getList_fn" v-show="pageData_a.list.length && !pageData_a.loading && !pageData_a.finished">查看更多<i class="el-icon-arrow-down"></i></div>
  </div>
</template>

<script>
  import qrcade from 'qrcode'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import {
    getCommunityActivityList,
  } from '@/api/topic-circle'

  export default {
    name: 'activity',
    head() {
      return {
        title: '话题-活动广场-脑医汇',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '脑医汇,神外资讯,神介资讯,神内资讯,话题,用户社区'
          },
          {
            hid: 'description',
            name: 'description',
            content: '聚焦行业动态，讨论热门话题。'
          },
        ],
        timer: null
      }
    },
    components: {NullData},
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      if (!store.state.auth.user.id) {
        redirect(`/signin?fallbackUrl=${route.fullPath}`)
        return
      }
      // 获取亚专业
      const [activityList] = await Promise.all([
        app.$axios.$request(
          getCommunityActivityList({
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 8,
          })
        )
      ])
      let listFinished = false;
      if (1 >= activityList.page.totalPage) {
        listFinished = true;
      }
      return {
        activityList: activityList.list, // 页面列表
        listFinished, // 是否加载完毕
      }
    },
    data() {
      return {
        qrShareUrl: '',
        pageData_a:{
          list: [],
          pageNo: 2,
          pageSize: 8,
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        default_activity_cover: require('~/assets/images/topic_circle/icon-activity-cover.png'),
      }
    },
    methods: {
      // 翻页
      getList_fn(){
        this.$axios.$request(
          getCommunityActivityList({
            userId: this.$store.state.auth.user.id,
            pageNo: this.pageData_a.pageNo,
            pageSize: this.pageData_a.pageSize
          })
        ).then(res=>{
          if (res.code === 1) {
            this.pageData_a.list = [...this.pageData_a.list, ...res.list]
            this.pageData_a.response = true // 请求成功状态
            if (this.pageData_a.pageNo >= res.page.totalPage) {
              this.pageData_a.finished = true
            }
            this.pageData_a.pageNo++
            this.pageData_a.loading = false
          }
        })
      },
      // 跳活动详情
      toDetail_fn(id){
        this.$router.push(`/topic-circle/activitydetail?id=${id}`)
      }
    },
    created(){
      this.pageData_a.list = this.activityList;
      this.pageData_a.finished = this.listFinished;
    },
    mounted() {
      qrcade.toDataURL(window.location.href)
        .then((img) => {
          this.qrShareUrl = img
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
</script>

<style scoped lang="less">
  #activity {
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
  }

  .main {
    max-width: 1200px;
    width: 1200px;
    margin: 0 auto;
    transition: all 0.3s;

    display: flex;
    justify-content: space-between;
  }

  .header {
    background: url("assets/images/topic_circle/acticity_header_bg.jpg") no-repeat center;
    background-size: cover;
    height: 200px;
    color: #fff;
    margin-bottom: -85px;

    .headerCon {
      padding-top: 34px;

      .pageTitle {
        font-size: 32px;
      }
    }

    .share {
      background: #fff;
      border-radius: 4px;
      color: #0581ce;
      padding: 0 30px;
      margin-top: 34px;
      line-height: 38px;
      height: 38px;
      flex: 0 0 auto;
      cursor: pointer;
      position: relative;

      .share-icon {
        margin-right: 4px;
      }

      &:hover .shareQRBox, .shareQRBox:hover {
        visibility: inherit;
        opacity: 1;
        pointer-events: inherit;
      }

      .shareQRBox {
        position: absolute;
        top: 38px;
        left: -7px;
        visibility: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        opacity: 0;
        transition: all .3s;
        pointer-events: none;
        padding-top: 10px;
        z-index: 2;

        &::after {
          content: '';
          border-width: 6px;
          border-color: transparent;
          border-bottom-color: #fff;
          border-style: solid;
          position: absolute;
          top: -2px;
        }

        img {
          width: 130px;
          height: 130px;
          border-radius: 4px 4px 0 0;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
        }

        span {
          color: #222222;
          font-size: 14px;
          margin-top: -0.7rem;
          background: #fff;
          width: 100%;
          border-radius: 0 0 4px 4px;
          text-align: center;
          box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .more {
    width: 164px;
    line-height: 48px;
    background: #fff;
    color: #666;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    font-size: 14px;
    text-align: center;
    margin: 0 auto;
    border-radius: 6px;
    cursor: pointer;
  }

  .activityList{
    display: flex;
    flex-wrap: wrap;
    gap: 0 16px;
    width: 100%;
    li{
      background: #fff;
      padding: 18px 18px 78px;
      border-radius: 6px;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.08);
      margin-bottom: 20px;
      width: 24%;
      flex: 0 0 auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      position: relative;
      cursor: pointer;
      &:hover span{
        color: #0581ce;
      }

      &::after{
        position: absolute;
        right: 0;
        bottom: 32px;
        color: #fff;
        border-radius: 24px 0 0 24px;
        font-size: 14px;
        padding: 0 4px 0 6px;
        line-height: 22px;
        display: none;
      }
      &.ing::after{
        content:'进行中';
        background: #F23C17;
        display: block;
      }
      &.end::after{
        content:'已结束';
        background: #1CC25B;
        display: block;
      }

      img{
        width: 100%;
        height: 157.5px;
        border-radius: 6px;
        margin-bottom: 20px;
      }

      span{
        font-size: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
