<template>
  <div id="answerList">
    <TopicCircleNav/>
    <div class="answerListBox">
      <div class="page-left">
        <div class="left-bigbox dataFlow">
          <tabs @tab-click="handleClick">
            <tab-pane label="推荐">
              <InvitationAnswerList :list="recommendList_o.list" :loading="recommendList_o.loading"/>
            </tab-pane>
            <tab-pane label="最新">
              <InvitationAnswerList :list="newestList_o.list" :loading="newestList_o.loading"/>
            </tab-pane>
          </tabs>
        </div>
      </div>
      <div class="page-right">
        <HotTopicCircleList :list="hotTopic" title="热门话题"/>
        <HotTopicCircleList :list="hotCircle" title="热门圈子"/>
      </div>
    </div>
  </div>
</template>

<script>
import {Tabs, TabPane} from 'element-ui'
import HotTopicCircleList from '@/components/optimize-components/TopicCircle/HotTopicCircleList'
import InvitationAnswerList from '@/components/optimize-components/TopicCircle/InvitationAnswer/list'
import {
  getHotCommunityTypeLimit,
  getCommunityInviteQuestionPage,
  getUserInterestedSubspecialtyList,
} from '@/api/topic-circle'
import TopicCircleNav from '@/components/PageComponents/Nav/TopicCircleNav'

export default {
  name: 'Index',
  components: {
    Tabs,
    TabPane,
    HotTopicCircleList,
    InvitationAnswerList,
    TopicCircleNav
  },
  async asyncData({app, params, error, store, query, req, redirect, route}) {
    if (!store.state.auth.user.id) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }
    // 获取亚专业
    const [subspecialty] = await Promise.all([
      app.$axios.$request(
        getUserInterestedSubspecialtyList({})
      )
    ])
    let subSpecialtyIds = []
    if (subspecialty.code === 1) {
      subspecialty.list.map((v) => {
        subSpecialtyIds.push(v.id)
      })
      subSpecialtyIds = subSpecialtyIds + ''
    }
    // 获取页面数据
    const [newest, recommend, hotTopic, hotCircle] = await Promise.all([
      // 待回答列表 最新
      app.$axios.$request(
        getCommunityInviteQuestionPage({
          userId: store.state.auth.user.id,
          pageNo: 1,
          pageSize: 10
        })
      ),
      // 待回答列表 推荐
      app.$axios.$request(
        getCommunityInviteQuestionPage({
          subSpecialtyIds,
          userId: store.state.auth.user.id,
          pageNo: 1,
          pageSize: 10
        })
      ),
      // 热门话题
      app.$axios.$request(
        getHotCommunityTypeLimit({
          communityType: 'T',
          userId: store.state.auth.user.id,
          limit: 5
        })
      ),
      // 热门圈子
      app.$axios.$request(
        getHotCommunityTypeLimit({
          communityType: 'C',
          userId: store.state.auth.user.id,
          limit: 5
        })
      )
    ])
    return {
      subSpecialtyIds, // 亚专业
      newest, // 最新
      recommend, // 推荐
      hotTopic: hotTopic.list,
      hotCircle: hotCircle.list
    }
  },
  data() {
    return {
      newestList_o: {
        pageNo: 2,
        pageSize: 10,
        list: [],
        loading: false, // 加载状态结束
        finished: false, // 数据全部加载完成
        loading_error: false, // 数据加载错误
        response: false // 请求是否成功
      },
      recommendList_o: {
        pageNo: 2,
        pageSize: 10,
        list: [],
        loading: false, // 加载状态结束
        finished: false, // 数据全部加载完成
        loading_error: false, // 数据加载错误
        response: false // 请求是否成功
      },
      oldScrolltop: 0, // 记录旧的滚动条高度
    }
  },
  head() {
    return {
      title: '待回答列表-话题圈子-脑医汇',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '聚焦行业动态，讨论热门话题；分享业内热闻，畅游医学圈子。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '脑医汇,神外资讯,神介资讯,神内资讯,话题圈子,用户社区'
        }
      ],
      timer: null
    }
  },
  watch: {
    // 监听导航的点击 滚动
    '$store.state.topicCircle.clickNavTab'(nv, ov) {
      if (nv !== ov) {
        // 是点击的nav
        this.isNavClick = true
        this.scrollTopHandle()
      }
    },
    '$store.state.global.scroll'(scroll) {
      const windowHeight = document.documentElement.clientHeight // 视口高度
      const ListDom = document.querySelector('.dataFlow') // 列表对象
      const listHeight = ListDom.clientHeight // 列表高度
      const ListTop = ListDom.offsetTop // 列表距离文档顶部

      const nav = document.getElementById('nav_header')
      const topicNav = document.getElementById('topic_circle_nav')

      // 是否显示话题圈子Nav
      if (scroll > 80) {
        if (scroll - this.oldScrolltop > 0) {
          nav.style.cssText = 'transform:translateY(-100%)'
          topicNav.style.cssText = 'transform:none'
        } else {
          nav.style.cssText = 'transform:none'
          topicNav.style.cssText = 'transform:translateY(-100%)'
        }
        this.oldScrolltop = scroll
      }
      if ((listHeight + ListTop) - (scroll + windowHeight) < 100) {
        if (this.$store.state.topicCircle.answerListTab === '0') {
          // 推荐
          if (!this.recommendList_o.loading && !this.recommendList_o.finished) {
            this.getRecommendList_fn()
          }
        } else if (this.$store.state.topicCircle.answerListTab === '1') {
          // 最新
          if (!this.newestList_o.loading && !this.newestList_o.finished) {
            this.getNewestList_fn()
          }
        }
      }
    }
  },
  created() {
    this.newestList_o.list = this.newest.list
    if (this.newest.page.totalPage <= 1) {
      this.newestList_o.finished = true
    }
    this.recommendList_o.list = this.recommend.list
    if (this.recommend.page.totalPage <= 1) {
      this.recommendList_o.finished = true
    }
  },
  methods: {
    handleClick(tab) {
      this.$store.commit('topicCircle/setAnswerListTab', tab.index + '')
    },
    // 待回答列表翻页数据 推荐
    getRecommendList_fn() {
      this.recommendList_o.loading = true
      this.$axios.$request(
        getCommunityInviteQuestionPage({
          subSpecialtyIds: this.subSpecialtyIds,
          userId: this.$store.state.auth.user.id,
          pageNo: this.recommendList_o.pageNo,
          pageSize: this.recommendList_o.pageSize
        })
      ).then(res => {
        if (res.code === 1) {
          res.list.map(v => {
            v.verificationBtn = false
          })
          this.recommendList_o.list = [...this.recommendList_o.list, ...res.list]
          this.recommendList_o.response = true // 请求成功状态
          if (this.recommendList_o.pageNo >= res.page.totalPage) {
            this.recommendList_o.finished = true
          }
          this.recommendList_o.pageNo++
          this.recommendList_o.loading = false
        }
      }).catch(() => {
        this.recommendList_o.loading_error = true
        this.recommendList_o.response = true
      })
    },
    // 待回答列表翻页数据 最新
    getNewestList_fn() {
      this.newestList_o.loading = true
      this.$axios.$request(
        getCommunityInviteQuestionPage({
          userId: this.$store.state.auth.user.id,
          pageNo: this.newestList_o.pageNo,
          pageSize: this.newestList_o.pageSize
        })
      ).then(res => {
        if (res.code === 1) {
          this.newestList_o.list = [...this.newestList_o.list, ...res.list]
          this.newestList_o.response = true // 请求成功状态
          if (this.newestList_o.pageNo >= res.page.totalPage) {
            this.newestList_o.finished = true
          }
          this.newestList_o.pageNo++
          this.newestList_o.loading = false
        }
      }).catch(() => {
        this.newestList_o.loading_error = true
        this.newestList_o.response = true
      })
    },
    scrollTopHandle() {
      window.scrollTo(0, 80, 300)
      setTimeout(() => {
        this.isNavClick = false
      }, 300)
    }
  },

}
</script>

<style scoped lang="less">
#answerList {
  background: #FBFBFB;
  overflow: hidden;
  padding-bottom: 60px;
}

.answerListBox {
  max-width: 1200px;
  width: 1200px;
  margin: 0 auto;
  transition: all 0.3s;
  padding: 20px 0;
}

.left-bigbox {
  padding: 0 20px 20px;
  border-radius: 6px;
  margin-bottom: 10px;
  -webkit-box-shadow: 0 0 4px rgb(237 237 237);
  box-shadow: 0 0 4px rgb(237 237 237);
  background: #fff;

  /deep/ .el-tabs__item {
    font-size: 18px;
  }

  /deep/ .el-tabs__nav-wrap {
    line-height: 57px;

    &::after {
      background-color: #F6F6F6;
    }
  }

  /deep/ .el-tabs__active-bar {
    width: 16px !important;
    margin-left: 10px;
  }
}

.quiz {
  display: flex;
  flex-direction: column;
  margin: 0;

  dd {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
    border-bottom: 1px solid #F6F6F6;
    margin: 0;

    &:last-of-type {
      padding-bottom: 0;
      border-bottom: none;
    }

    .inviteTitle {
      font-size: 18px;
      font-weight: bold;
      margin-top: 18px;
    }

    .answerNumber {
      margin-top: 14px;
      font-size: 15px;
      color: #999;
    }

    .inviteMenu {
      display: flex;
      align-items: center;
      margin-top: 30px;

      .write {
        width: 108px;
        box-sizing: border-box;
        line-height: 32px;
        color: #0581CE;
        border: 1px solid #0581CE;
        border-radius: 4px;
        margin-right: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.on {
          background: #0581ce;
          color: #fff;
        }

        .topic-icon {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
      }
    }
  }
}

/deep/ .el-tabs__content {
  overflow: initial;
}
</style>
