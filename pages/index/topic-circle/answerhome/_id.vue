<template>
  <div id="answerhome">
    <div class="questionHeader">
      <div class="main" v-if="questionInfo">
        <div class="page-left">
          <ul class="label">
            <li v-for="(item) in questionInfo.topicList" @click="toCommunityTopic(item)">#{{item.title}}</li>
            <li v-for="(item) in questionInfo.circleList" @click="toCommunityCircle(item)">#{{item.title}}
            </li>
          </ul>
          <h3 class="title" @click="toQuestuibHome_fn">{{questionInfo.title}}</h3>
          <div class="description">
            <Unpack v-if="questionInfo.regexText" :item="questionInfo" :isHideAvatar="true" :line="2"/>
          </div>
          <div class="userInfo">
            <img @click="toUserCenter_fn(questionInfo.creator.id)"
                 :src="this.questionInfo.isHideCreator?defaultHeader:questionInfo.creator.avatarAddress?questionInfo.creator.avatarAddress:defaultHeader" alt="">
            <div class="userName">
              <b @click="toUserCenter_fn(questionInfo.creator.id)">{{this.questionInfo.isHideCreator?'匿名':questionInfo.creator.realName}}</b>
              的问题
              <i>{{timeStamp.timestamp_13(questionInfo.createTime, 'yyyy-mm-d')}}</i>
            </div>
          </div>
          <ul class="menu">
            <li @click="followTopicCircle_fn('问题', questionInfo, questionInfo.id)"
                :class="questionInfo.isFollow?'on':''">
              <template v-if="!questionInfo.isFollow">
                <svg-icon class-name='topic-icon' icon-class='topic_plus'/>
                关注问题
              </template>
            </li>
            <li @click="toReply_fn(1)">
              <el-popover
                placement="bottom"
                title=""
                width="192"
                trigger="manual"
                v-model="verificationBtn1">
                <div style="display:flex;flex-direction:column;">
                  <p style="font-size: 16px;color: #222;">
                    {{questionInfo.isAnswered?'该问题已存在您的回答,暂不支持修改，如需修改，请前往app修改。':'您发布的内容正在审核中，审核通过方可发布新内容。'}}
                  </p>
                  <b
                    style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                    @click="verificationBtn1 = !verificationBtn1">我知道了</b>
                </div>
                <div slot="reference">
                  <svg-icon class-name='topic-icon' icon-class='topic_write'/>
                  写回答
                </div>
              </el-popover>
            </li>
            <li @click="showInvite_fn">
              <svg-icon class-name='topic-icon topic-icon2' icon-class='topic_invite'/>
              邀请回答
            </li>
            <li class="share">
              <svg-icon class-name='topic-icon' icon-class='topic_share_blue'/>
              分享
              <div class="shareQRBox">
                <img :src="qrShareUrl" alt="">
                <span>微信扫一扫</span>
              </div>
            </li>
            <li @click="toQuestuibHome_fn">
              返回问题
            </li>
          </ul>
        </div>
        <div class="page-right">
          <ul class="problemInfo">
            <li>
              <span>回答</span>
              <i>{{questionInfo.answers}}</i>
            </li>
            <li>
              <span>浏览</span>
              <i>{{questionInfo.views}}</i>
            </li>
          </ul>
        </div>
        <div style="clear: both"></div>
      </div>
    </div>

    <div class="main">
      <div class="page-left dataFlow">
        <div class="left-bigbox">
          <div v-if="answerList.length">
            <ArticleList :data_list="answerList" :isOnlyAnswers="true" :isHideItemShare="true"/>
            <div class="loadingList" v-show="pageNewestList_o.loading">
              <el-skeleton animated/>
            </div>
          </div>
          <div v-else class="nullData">
            <NullData/>
            <span>暂时还没有回答,开始
                  <el-popover
                    placement="bottom"
                    title=""
                    width="192"
                    trigger="manual"
                    content="您发布的内容正在审核中，审核通过方可发布新内容。"
                    v-model="verificationBtn2">
                    <div style="display:flex;flex-direction:column;">
                      <p style="font-size: 16px;color: #222;">您发布的内容正在审核中，审核通过方可发布新内容。</p>
                      <b
                        style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                        @click="verificationBtn2 = !verificationBtn2">我知道了</b>
                    </div>
                    <div slot="reference">
                      <i @click="toReply_fn(2)">写第一个回答</i>
                    </div>
                  </el-popover>
                </span>
          </div>
        </div>
      </div>
      <div class="page-right" v-if="belongToTopic.length">
        <div class="rightItem">
          <h3 class="rightItemTitle">所属话题</h3>
          <ul class="topicList">
            <li v-for="(item,index) in belongToTopic" :key="index">
              <div class="topicIHeader" @click="toTopicDetails_fn(item)">
                <img :src="item.thumbnail?item.thumbnail:default_topic_share" alt="">
                <div class="topicInfo">
                  <h5>{{item.title}}</h5>
                  <div class="specialityDom">
                    <p v-for="(speciality,specialityIndex) in item.specialityList" :key="specialityIndex">
                      {{speciality.name}}</p>
                  </div>
                </div>
                <b @click.stop="followTopicCircle_fn('话题', item, item.communityId)"
                   :class="`${item.isFollow?'on':'off'}`">
                  <svg-icon v-if="!item.isFollow" class-name='topic-icon' icon-class='topic_plus'/>
                  {{item.isFollow?'已关注': '关注'}}</b>
              </div>
              <p class="topicMsg">{{item.follows?item.follows + '关注':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.views?item.views
                + '浏览':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.contents?item.contents + '内容' : ''}}</p>
              <p class="describe" v-if="item.description">{{item.description}}</p>
            </li>
          </ul>
        </div>
        <div class="rightItem" v-if="belongToCircle.length">
          <h3 class="rightItemTitle">所属圈子</h3>
          <ul class="topicList">
            <li v-for="(item,index) in belongToCircle" :key="index">
              <div class="topicIHeader" @click="toCircleDetails_fn(item)">
                <img :src="item.image?item.image:default_circle_share" alt="">
                <div class="topicInfo">
                  <h5>{{item.title}}</h5>
                  <div class="specialityDom">
                    <p v-for="(speciality,specialityIndex) in item.specialityList" :key="specialityIndex">
                      {{speciality.name}}</p>
                  </div>
                </div>
                <b @click.stop="followTopicCircle_fn('圈子', item, item.communityId)"
                   :class="`${item.isFollow?'on':'off'}`">
                  <svg-icon v-if="!item.isFollow" class-name='topic-icon' icon-class='topic_plus'/>
                  {{item.isFollow?'已关注': '关注'}}
                </b>
              </div>
              <p class="topicMsg">{{item.follows?item.follows + '关注':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.views?item.views
                + '浏览':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.contents?item.contents + '内容' : ''}}</p>
              <Unpack v-if="item.description" :text="item.description" :line="3"/>
            </li>
          </ul>
        </div>
        <div class="rightItem" v-if="belongToActivity.length">
          <h3 class="rightItemTitle">所属活动</h3>
          <ul class="topicList">
            <li v-for="(item,index) in belongToActivity" :key="index">
              <div class="topicIHeader" @click="toActivityDetails_fn(item)">
                <img :src="item.thumbnail?item.thumbnail:default_activity_share" alt="">
                <div class="topicInfo">
                  <h5>{{item.title}}</h5>
                  <div class="specialityDom" v-if="item.specialityList && item.specialityList.length">
                    <p>
                      <span v-for="(speciality,specialityIndex) in item.specialityList" :key="specialityIndex">
                        {{speciality.name}}
                      </span>
                    </p>
                  </div>
                </div>
                <b @click.stop="followTopicCircle_fn('活动', item, item.communityId)"
                   :class="`${item.isFollow?'on':'off'}`">
                  <svg-icon v-if="!item.isFollow" class-name='topic-icon' icon-class='topic_plus'/>
                  {{item.isFollow?'已关注': '关注'}}
                </b>
              </div>
              <p class="topicMsg">{{item.follows?item.follows + '关注':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.views?item.views
                + '浏览':''}}&nbsp;&nbsp;&nbsp;&nbsp;{{item.contents?item.contents + '内容' : ''}}</p>
              <Unpack v-if="item.description" :text="item.description" :line="3"/>
            </li>
          </ul>
        </div>
        <div class="rightItem" v-if="related_a.length">
          <h3 class="rightItemTitle">相关问答</h3>
          <ul class="correlationList">
            <li v-for="(item,index) in related_a" :key="index" @click="toProblemDetail_fn(item.id)">
              <div class="itemCon">
                <h5>{{item.title}}</h5>
                <div class="info" v-if="item.answers || item.views">
                  <span v-if="item.answers">{{`${item.answers}回答`}}</span>
                  <span v-if="item.views">{{`${item.views}浏览`}}</span>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="rightItem">
          <h3 class="rightItemTitle">待回答</h3>
          <ul class="correlationList">
            <li v-for="(item,index) in toBeAnswered_a" :key="index" @click="toProblemDetail_fn(item.id)">
              <div class="itemCon">
                <h5>{{item.title}}</h5>
                <div class="info" v-if="item.answers || item.views">
                  <span v-if="item.answers">{{`${item.answers}回答`}}</span>
                  <span v-if="item.views">{{`${item.views}浏览`}}</span>
                </div>
              </div>
              <!-- 注意！verificationBtn3是在数据中 -->
              <el-popover
                placement="bottom"
                title=""
                width="192"
                trigger="manual"
                v-model="item.verificationBtn3">
                <div style="display:flex;flex-direction:column;">
                  <p style="font-size: 16px;color: #222;">{{item.isAnswered?'该问题已存在您的回答,暂不支持修改，如需修改，请前往app修改。':'您发布的内容正在审核中，审核通过方可发布新内容。'}}</p>
                  <b
                    style="cursor: pointer;font-weight: normal;color: #0581ce;font-size: 14px;margin-left: auto;margin-top: 10px;"
                    @click="item.verificationBtn3 = !item.verificationBtn3">我知道了</b>
                </div>
                <div slot="reference">
                  <b @click.stop="toReply_fn(3,item)" class="attention">
                    <svg-icon class-name='topic-icon' icon-class='topic_write'/>
                    写回答
                  </b>
                </div>
              </el-popover>
            </li>
          </ul>
        </div>
      </div>
      <div style="clear: both"></div>
    </div>
    <OneClickInitation v-show="invite.show" :subSpecialtyIds="subSpecialtyIds" :questionInfo="questionInfo"/>
    <ShortVideoPlayback
      :video-id='$store.state.topicCircle.topicCircleHomeShortVideoId'
      :visible='$store.state.topicCircle.topicCircleHomeShortVisible'
      @cancelFn='(flag) => $store.commit("topicCircle/setTopicCircleHomeShortVisible",flag)'
    />
  </div>
</template>

<script>
  import { Tabs, TabPane } from 'element-ui'
  import ArticleList from '@/components/optimize-components/TopicCircle/ArticleList'
  import ShortVideoPlayback from '@/components/optimize-components/public/ShortVideoPlayback'
  import Unpack from '@/components/optimize-components/TopicCircle/Unpack'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import OneClickInitation from '@/components/optimize-components/TopicCircle/OneClickInitation'
  import qrcade from 'qrcode'
  import {
    getCommunityAnswerParent,
    follow,
    getUserInterestedSubspecialtyList,
    getTheirSubject,
    getCommunityQuestionPageByCommunityId,
    getRelatedTopics,
    getAnswerPageByQuestionId,
    getUserPublishAbility
  } from '@/api/topic-circle'
  import { saveBrowsingHistory } from '@/api/browsing-history'
  import { loginByToken } from '@/api/login'
  import { userInfo } from '@/api/user'

  export default {
    name: 'answerhome',
    components: {
      Tabs,
      TabPane,
      ArticleList,
      ShortVideoPlayback,
      Unpack,
      NullData,
      OneClickInitation
    },
    head() {
      return {
        title: this.questionInfo?this.questionInfo.title + '-话题圈子-脑医汇':'话题圈子-脑医汇',
        meta: [
          {
            hid: 'description',
            name: 'description',
            content: (this.questionInfo?this.questionInfo.title:'') + ',' + (this.subSpecialtyNames?this.subSpecialtyNames:'')
          },
          {
            hid: 'keywords',
            name: 'keywords',
            content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
          }
        ],
        timer: null
      }
    },
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      if (!store.state.auth.user.id) {
        redirect(`/signin?fallbackUrl=${route.fullPath}`)
        return
      }
      // 获取亚专业
      const [subspecialty] = await Promise.all([
        app.$axios.$request(
          getUserInterestedSubspecialtyList({})
        )
      ])
      let subSpecialtyIds = [];
      let subSpecialtyNames = [];
      if (subspecialty.code === 1) {
        subspecialty.list.map((v) => {
          subSpecialtyIds.push(v.id);
          subSpecialtyNames.push(v.name);
        })
        subSpecialtyIds = subSpecialtyIds + ''
        subSpecialtyNames = subSpecialtyNames + ''
      }
      const [questionInfo] = await Promise.all([
        // 问题详情
        app.$axios.$request(
          getCommunityAnswerParent({
            qaId: params.id
          })
        ),
      ])
      let answerList = [];
      answerList.push(questionInfo.result.answer)
      app.head.title = questionInfo?questionInfo.title + '-话题圈子-脑医汇':'话题圈子-脑医汇'
      app.head.keywords = (questionInfo?questionInfo.title:'') + ',' + (subSpecialtyNames?subSpecialtyNames:'')
      return {
        subSpecialtyIds, // 亚专业字符串 eg:815,813,812
        subSpecialtyNames,
        questionInfo: questionInfo.result.question, // 问题详情
        answerList, // 回答页面的回答
      }
    },
    data() {
      return {
        defaultHeader: require('~/assets/images/user.png'),
        qrShareUrl: '', // 分享二维码
        // 推荐用户
        invite: {
          show: false,
        },
        pageActiveIndex: 0, // 数据流 最新/最热
        belongTo_a: [], // 所属话题和圈子
        belongToTopic: [], // 所属话题
        belongToCircle: [], // 所属圈子
        belongToActivity: [], // 所属活动
        related_a: [], // 相关问答
        toBeAnswered_a: [], // 待回答
        // 最新tab
        pageNewestList_o: {
          pageNo: 2,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        // 最热Tab
        pageHottestList_o: {
          pageNo: 2,
          pageSize: 10,
          list: [],
          loading: false, // 加载状态结束
          finished: false, // 数据全部加载完成
          loading_error: false, // 数据加载错误
          response: false // 请求是否成功
        },
        verificationBtn1: false,
        verificationBtn2: false,
        default_topic_share: require('~/assets/images/topic_circle/icon-topic-share.png'),
        default_circle_share: require('~/assets/images/topic_circle/icon-circle-share.png'),
      }
    },
    methods: {
      // 跳问题详情
      toQuestuibHome_fn(){
        this.$router.push(`/topic-circle/questionhome?id=${this.questionInfo.id}`)
      },
      // 跳话题详情
      toCommunityTopic(item) {
        const { href } = this.$router.resolve({ path: `/topic-circle/communitytopic?id=${item.id}` })
        window.open(href, '_blank')
      },
      // 跳圈子详情
      toCommunityCircle(item) {
        const { href } = this.$router.resolve({ path: `/topic-circle/communitycircle?id=${item.id}` })
        window.open(href, '_blank')
      },
      // 所属话题 话题详情
      toTopicDetails_fn(item){
        const { href } = this.$router.resolve({ path: `/topic-circle/communitytopic?id=${item.communityId}` })
        window.open(href, '_blank')
      },
      // 所属圈子 圈子详情
      toCircleDetails_fn(item){
        const { href } = this.$router.resolve({ path: `/topic-circle/communitycircle?id=${item.communityId}` })
        window.open(href, '_blank')
      },
      // 所属活动 活动详情
      toActivityDetails_fn(item) {
        const { href } = this.$router.resolve({ path: `/topic-circle/activitydetail?id=${item.communityId}` })
        window.open(href, '_blank')
      },
      // 写回答
      toReply_fn(num,item) {
        const localToken = this.$cookies.get('medtion_token_only_sign')
        const isLogged = this.$store.state.auth.isLogged
        if (localToken || isLogged) {
          // 判断登录
          this.$axios.$request(loginByToken({
            token: localToken
          })).then(response => {
            if(response.code === 1){
              this.$axios.$request(userInfo()).then(res => {
                if (res && res.code === 1) {
                  // 判断身份
                  if (res.result.identity === 3) {
                    this.$toast.fail('当前身份无法使用发布功能')
                    return
                  }
                  // 判断是否可以发布
                  this.$axios
                    .$request(
                      getUserPublishAbility({
                        userId: this.$store.state.auth.user.id
                      })
                    )
                    .then((res) => {
                      if (res.code === 1) {
                        if (res.result) {
                          if (num === 1) {
                            this.$analysys.btn_click('回答', document.title)
                            if (this.questionInfo.isAnswered) {
                              // 二次编辑
                              setTimeout(() => {
                                const { href } = this.$router.resolve({ path: `/topic-circle/write?anid=${/\/(\d+)\.jspx/.exec(this.questionInfo.userAnswerUrl)[1]}` })
                                window.open(href, '_blank')
                              }, 0)
                            } else {
                              window.sessionStorage.setItem('editTitle' + this.questionInfo.id, this.questionInfo.title)
                              // 创建回答
                              setTimeout(() => {
                                const { href } = this.$router.resolve({ path: `/topic-circle/write?id=${this.questionInfo.id}` })
                                window.open(href, '_blank')
                              }, 0)
                            }
                          } else if (num === 2) {
                            this.$analysys.btn_click('回答', document.title)
                            window.sessionStorage.setItem('editTitle' + this.questionInfo.id, this.questionInfo.title)
                            setTimeout(() => {
                              const { href } = this.$router.resolve({ path: `/topic-circle/write?id=${this.questionInfo.id}` })
                              window.open(href, '_blank')
                            }, 0)
                          } else if (num === 3 && item) {
                            if (item.isAnswered) {
                              item.verificationBtn3 = true
                            } else {
                              this.$analysys.btn_click('回答', document.title)
                              window.sessionStorage.setItem('editTitle' + item.id, item.title)
                              setTimeout(() => {
                                const { href } = this.$router.resolve({ path: `/topic-circle/write?id=${item.id}` })
                                window.open(href, '_blank')
                              }, 0)
                            }
                          }
                        } else {
                          if (num === 1 && this.questionInfo.isAnswered) {
                            // 二次编辑
                            setTimeout(() => {
                              const { href } = this.$router.resolve({ path: `/topic-circle/write?anid=${/\/(\d+)\.jspx/.exec(this.questionInfo.userAnswerUrl)[1]}` })
                              window.open(href, '_blank')
                            }, 0)
                          } else {
                            this['verificationBtn' + num] = true
                          }
                        }
                      }
                    })
                }
              })
            }else{
              this.$toast('请先登录')
              this.$store.commit('editBackUrl', window.location.href)
              this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
            }
          })
        }else{
          this.$toast('请先登录')
          this.$store.commit('editBackUrl', window.location.href)
          this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
        }
      },
      // 跳个人中心
      toUserCenter_fn(id) {
        if (!this.questionInfo.isHideCreator) {
          const { href } = this.$router.resolve({ path: `/user-center?profileUserId=${id}` })
          window.open(href, '_blank')
        }
      },
      // 邀请回答按钮
      showInvite_fn() {
        this.invite.show = !this.invite.show
      },
      // 话题圈子问题关注
      followTopicCircle_fn(typeCode, item, contentId) {
        let type = ''
        if (typeCode === '话题') {
          type = 'T'
        } else if (typeCode === '圈子') {
          type = 'C'
        } else if (typeCode === '问题') {
          type = 'Q'
        } else if (typeCode === '活动'){
          type = 'A'
        }
        this.$axios.$request(
          follow({
            contentId: contentId,
            userId: this.$store.state.auth.user.id,
            type
          })
        ).then(res => {
          if (res.code === 1) {
            item.isFollow = !item.isFollow;
            if(type === 'T'){
              this.$analysys.add_follow(this.$store.state.auth.user.id + '',contentId + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','话题');
            }else if(type === 'C'){
              this.$analysys.add_follow(this.$store.state.auth.user.id + '',contentId + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','圈子');
            }else if(type === 'Q'){
              this.$analysys.add_follow(this.$store.state.auth.user.id + '',contentId + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','问题');
            }else if(type === 'A'){
              this.$analysys.add_follow(this.$store.state.auth.user.id + '',contentId + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','活动');
            }
          }
        })
      },
      // 所属话题或圈子
      getTheirSubject_fn() {
        this.$axios.$request(
          getTheirSubject({
            qaId: this.questionInfo.id,
            userId: this.$store.state.auth.user.id,
            type: 'T',
          })
        ).then(res => {
          if (res.code === 1) {
            this.belongToTopic = res.list
            this.getRelatedTopics_fn()  // 相关问答
            this.getCommunityQuestionPageByCommunityId_fn() // 待回答
          }
        })
        this.$axios.$request(
          getTheirSubject({
            qaId: this.questionInfo.id,
            userId: this.$store.state.auth.user.id,
            type: 'C',
          })
        ).then(res => {
          if (res.code === 1) {
            this.belongToCircle = res.list
          }
        })
        this.$axios.$request(
          getTheirSubject({
            qaId: this.questionInfo.id,
            userId: this.$store.state.auth.user.id,
            type: 'A',
          })
        ).then(res => {
          if (res.code === 1) {
            this.belongToActivity = res.list
          }
        })
      },
      // 相关问答
      getRelatedTopics_fn() {
        var communityIds = []
        this.belongToTopic.map(v => {
          communityIds.push(v.communityId)
        })
        this.$axios.$request(
          getRelatedTopics({
            communityIds: communityIds.join(','),
            userId: this.$store.state.auth.user.id,
            limit: 5,
            qaId: this.questionInfo.id,
          })
        ).then(res => {
          if (res.code === 1) {
            this.related_a = res.list
          }
        })
      },
      // 待回答
      getCommunityQuestionPageByCommunityId_fn() {
        var communityIds = []
        this.belongToTopic.map(v => {
          communityIds.push(v.communityId)
        })
        this.$axios.$request(
          getCommunityQuestionPageByCommunityId({
            communityIds: communityIds.join(','),
            userId: this.$store.state.auth.user.id,
            pageNo: 1,
            pageSize: 5
          })
        ).then(res => {
          if (res.code === 1) {
            res.list.map(v=>{
              v.verificationBtn3 = false;
            })
            this.toBeAnswered_a = res.list
          }
        })
      },
      // 跳问题详情
      toProblemDetail_fn(qid) {
        const { href } = this.$router.resolve({ path: `/topic-circle/questionhome?id=${qid}` })
        window.open(href, '_blank')
      },
      // 最新列表翻页
      getNewestData_fn() {
        this.pageNewestList_o.loading = true
        this.$axios.$request(
          getAnswerPageByQuestionId({
            questionId: this.questionInfo.id,
            type: 'L',
            pageNo: this.pageNewestList_o.pageNo,
            pageSize: this.pageNewestList_o.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.pageNewestList_o.list = [...this.pageNewestList_o.list, ...res.list]
            this.pageNewestList_o.response = true // 请求成功状态
            if (this.pageNewestList_o.pageNo >= res.page.totalPage) {
              this.pageNewestList_o.finished = true
            }
            this.pageNewestList_o.pageNo++
            this.pageNewestList_o.loading = false
          }
        }).catch(() => {
          this.pageNewestList_o.loading_error = true
          this.pageNewestList_o.response = true
        })
      },
      // 最热列表翻页
      getHotestData_fn() {
        this.pageHottestList_o.loading = true
        this.$axios.$request(
          getAnswerPageByQuestionId({
            questionId: this.questionInfo.id,
            type: 'H',
            pageNo: this.pageHottestList_o.pageNo,
            pageSize: this.pageHottestList_o.pageSize
          })
        ).then(res => {
          if (res.code === 1) {
            this.pageHottestList_o.list = [...this.pageHottestList_o.list, ...res.list]
            this.pageHottestList_o.response = true // 请求成功状态
            if (this.pageHottestList_o.pageNo >= res.page.totalPage) {
              this.pageHottestList_o.finished = true
            }
            this.pageHottestList_o.pageNo++
            this.pageHottestList_o.loading = false
          }
        }).catch(() => {
          this.pageHottestList_o.loading_error = true
          this.pageHottestList_o.response = true
        })
      },
      // 页面滚动事件
      page_scrollEvent() {
        let scrolltop = document.documentElement.scrollTop // 滚动条高度
        let windowHeight = document.documentElement.clientHeight // 视口高度
        let ListDom = document.querySelector('.dataFlow') // 列表对象
        let listHeight = ListDom.clientHeight // 列表高度
        let ListTop = ListDom.offsetTop // 列表距离文档顶部

        if ((listHeight + ListTop) - (scrolltop + windowHeight) < 300) {
          if (this.pageActiveIndex === '0') {
            // 最新
            if (!this.pageNewestList_o.loading && !this.pageNewestList_o.finished) {
              this.getNewestData_fn()
            }
          } else if (this.pageActiveIndex === '1') {
            // 最热
            if (!this.pageHottestList_o.loading && !this.pageHottestList_o.finished) {
              this.getHotestData_fn()
            }
          }
        }
      },
      // 历史记录
      saveBrowsingHistory_fn(){
        if(!this.$store.state.auth.user.id) return;
        this.$axios.$request(saveBrowsingHistory({
          loginUserId: this.$store.state.auth.user.id,
          // 内容类型 12 问答 13回答
          contentSource: 13,
          //         浏览内容 id（浏览内容类型的id）
          contentId: this.$route.params.id,
          courseId: null,
          playDuration: null
        }))
      },
    },
    mounted() {
      // 历史记录
      this.saveBrowsingHistory_fn();
      // 分享
      qrcade.toDataURL(window.location.href)
        .then((img) => {
          this.qrShareUrl = img
        })
        .catch((err) => {
          console.log(err)
        })

      this.getTheirSubject_fn()  // 所属话题或圈子
      window.addEventListener('scroll', this.page_scrollEvent) // 顶部导航切换、翻页数据
      this.$analysys.detail({
        creator_id:this.questionInfo.creator.id + '',
        creator_name:this.questionInfo.creator.realName,
        qa_id:this.questionInfo.id + '',
        qa_name:this.questionInfo.title,
        qa_type:'问题'
      });
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.page_scrollEvent)
    }
  }
</script>

<style scoped lang="less">
  #answerhome {
    background: #FBFBFB;
  }

  .main {
    max-width: 1200px;
    width: 1200px;
    margin: 0 auto;
    transition: all 0.3s;
    padding: 20px 0;
  }

  .questionHeader {
    width: 100%;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);

    .label {
      display: flex;
      flex-wrap: wrap;

      li {
        background: #ECF8FF;
        color: #0581CE;
        font-size: 15px;
        line-height: 32px;
        border-radius: 100px;
        padding: 0 16px;
        margin-right: 20px;
        margin-bottom: 17px;
        cursor: pointer;
      }
    }

    .title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 12px;
      cursor: pointer;
    }

    .description {
      margin-bottom: 12px;
    }

    .userInfo {
      display: flex;
      align-items: center;
      font-size: 16px;
      margin-bottom: 20px;

      img {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        cursor: pointer;
      }

      b {
        font-size: 16px;
        font-weight: bold;
        margin: 0 8px 0 12px;
        cursor: pointer;
      }

      i {
        margin-left: 30px;
        font-style: normal;
      }
    }

    .menu {
      display: flex;
      align-items: center;

      li {
        font-size: 14px;
        color: #0581ce;
        border: 1px solid #0581ce;
        border-radius: 4px;
        line-height: 40px;
        width: 108px;
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &.on {
          background: #D2D2D2;
          color: #fff;
          border-color: #D2D2D2;

          &::after {
            content: '已关注';
          }

          &:hover {
            background: #AFC1CC;

            &::after {
              content: '取消关注';
            }
          }
        }

        .topic-icon {
          margin-right: 6px;
          width: 14px;
          height: 14px;
        }

        .topic-icon2 {
          width: 16px;
          height: 16px;
        }
      }

      .share {
        position: relative;

        &:hover .shareQRBox, .shareQRBox:hover {
          visibility: inherit;
          opacity: 1;
          pointer-events: inherit;
        }

        .shareQRBox {
          position: absolute;
          top: 38px;
          left: -7px;
          visibility: hidden;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
          opacity: 0;
          transition: all .3s;
          pointer-events: none;
          padding-top: 10px;
          z-index: 2;

          &::after {
            content: '';
            border-width: 6px;
            border-color: transparent;
            border-bottom-color: #fff;
            border-style: solid;
            position: absolute;
            top: -2px;
          }

          img {
            width: 130px;
            height: 130px;
            border-radius: 4px 4px 0 0;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
          }

          span {
            color: #222222;
            font-size: 14px;
            margin-top: -0.7rem;
            background: #fff;
            width: 100%;
            border-radius: 0 0 4px 4px;
            text-align: center;
            box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }

    .problemInfo {
      display: flex;
      align-items: center;

      li {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 16px;

        &:nth-of-type(1) {
          border-right: 1px solid #f6f6f6;
        }

        span {
          font-size: 15px;
          color: #999;
          margin-bottom: 5px;
        }

        i {
          font-size: 18px;
          font-weight: bold;
          font-style: normal;
        }
      }
    }
  }

  .left-bigbox {
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 0 0 4px rgb(237 237 237);
    background: #fff;

    .nullData {
      padding-bottom: 100px;

      & > span {
        display: flex;
        justify-content: center;
        margin-top: 32px;
        font-size: 16px;
        color: #222;

        i {
          font-style: normal;
          color: #0581ce;
          cursor: pointer;
        }
      }
    }

    /deep/ .el-tabs__item {
      font-size: 18px;
    }

    /deep/ .el-tabs__nav-wrap {
      line-height: 57px;

      &::after {
        background-color: #F6F6F6;
      }
    }

    /deep/ .el-tabs__active-bar {
      width: 16px !important;
      margin-left: 10px;
    }

    /deep/ .el-tabs__header {
      margin: 0 0 20px;
    }
  }

  .rightItem {
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    padding: 20px 16px 0;
    margin-bottom: 20px;

    .rightItemTitle {
      font-size: 18px;
      padding-bottom: 12px;
      border-bottom: 1px solid #F6F6F6;
      font-weight: bold;
    }

    .topicList {
      display: flex;
      flex-direction: column;

      li {
        border-bottom: 1px solid #F6F6F6;
        padding: 20px 0;

        &:last-of-type {
          border-bottom: none;
          margin-bottom: 0;
        }

        .topicIHeader {
          display: flex;
          align-items: center;
          cursor: pointer;

          img {
            width: 58px;
            height: 58px;
            border-radius: 4px;
            margin-right: 12px;
            object-fit: cover;
          }

          .topicInfo {
            height: 58px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            h5 {
              font-size: 16px;
            }

            .specialityDom {
              display: flex;
              flex-wrap: wrap;

              p {
                font-size: 14px;
                color: #999;
                margin-right: 6px;
              }
            }
          }

          b {
            color: #0581CE;
            font-size: 14px;
            line-height: 32px;
            width: 72px;
            text-align: center;
            border-radius: 72px;
            border: 1px solid #0581CE;
            font-weight: normal;

            svg {
              width: 10px;
            }

            &.on {
              background: #D2D2D2;
              color: #fff;
              border-color: #D2D2D2;
            }
            &.off{
              &:hover{
                background: rgba(5, 129, 206, 0.05);
              }
            }
          }
        }

        .topicMsg {
          font-size: 14px;
          color: #999;
          margin-top: 16px;
          margin-bottom: 10px;
        }

        .describe{
          color: #222;
          font-size: 15px;
        }
      }
    }

    .correlationList {
      display: flex;
      flex-direction: column;

      li {
        padding: 16px 0;
        border-bottom: 1px solid #F6F6F6;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:last-of-type {
          border-bottom: none;
        }

        .itemCon {
          flex: 1;

          h5 {
            font-size: 16px;
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }

          .info {
            margin-top: 12px;

            span {
              font-size: 14px;
              color: #999;
              margin-right: 10px;
            }
          }
        }

        .attention {
          color: #0581CE;
          font-size: 14px;
          text-align: center;
          border-radius: 4px;
          border: 1px solid #0581CE;
          font-weight: normal;
          width: 84px;
          line-height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          &:hover{
            background: rgba(5, 129, 206, 0.05);
          }

          .topic-icon{
            margin-right: 4px;
          }
        }
      }
    }
  }

  .loadingList {
    border-top: 1px solid #F6F6F6;
    margin-top: 20px;
    padding-top: 20px;
  }
  /deep/ .el-tabs__item.is-active{
    color: #0581ce;
  }
  /deep/ .el-tabs__content{
    overflow: initial;
  }
</style>
