<template>
  <div id="topic">
    <header class="header">
      <div class="main">
        <div class="headerCon">
          <h3 class="pageTitle">脑医汇 — 话题</h3>
          <span class="pageInfo">{{setNum1000(statistics.result.contents)}}内容&nbsp;&nbsp;&nbsp;&nbsp;{{setNum1000(statistics.result.views)}}浏览&nbsp;&nbsp;&nbsp;&nbsp;{{setNum1000(statistics.result.answers)}}回答</span>
        </div>
        <div class="share">
          <svg-icon class-name='topic-icon' icon-class='topic_share_blue'/>
          分享
          <div class="shareQRBox">
            <img :src="qrShareUrl" alt="">
            <span>微信扫一扫</span>
          </div>
        </div>
      </div>
      <div class="main">
        <ul class="navClass">
          <div class="bottom_line"
               :style="`left:${tabActive*(1200/pageData_a.length) + (1200/(pageData_a.length*2)) - 7.5 + 'px'}`"></div>
          <li v-for="(item,index) in pageData_a" :key="index" :class="`${tabActive === index?'active':''}`"
              @click="tabChange_fn(index)">
            <div>
              <img :src="item.icon" alt="">
              <span>{{item.name}}</span>
            </div>
          </li>
          <NullData v-if="!pageData_a.length" text="暂无内容"/>
        </ul>
      </div>
    </header>
    <div class="main">
      <ul class="topicList">
        <li v-for="(item,index) in pageData_a[tabActive].list" :key="index" @click="toCommunityDetails_fn(item.id)">
          <div class="topicInfo">
            <img :src="item.thumbnail?item.thumbnail:default_topic_share" alt="">
            <div class="title">
              <h5>{{item.title}}</h5>
              <span><i>{{item.follows}}关注</i><i>{{item.contents}}内容</i><i>{{item.views}}浏览</i></span>
            </div>
            <b @click.stop="followTopicCircle_fn(item,'话题')" :class="`${item.isFollow?'active':''}`">
              <template v-if="item.isFollow">
                已关注
              </template>
              <template v-else>
                <svg-icon class-name='topic-icon' icon-class='topic_plus'/>
                关注
              </template>
            </b>
          </div>
          <p v-if="item.description">{{item.description}}</p>
        </li>
        <NullData v-if="!pageData_a[tabActive].list.length" text="暂无内容"/>
      </ul>
    </div>
    <div class="more" @click="getTabList_fn" v-if="!pageData_a[tabActive].loading && !pageData_a[tabActive].finished">查看更多<i class="el-icon-arrow-down"></i></div>
  </div>
</template>

<script>
  import qrcade from 'qrcode'
  import NullData from '@/components/optimize-components/TopicCircle/NullData'
  import {
    getCommunityUserSpecialityList, // 亚专业
    getCommunityFollowTopicPage, // 已关注的话题
    getCommunityTopicPageBySubSpecialty, // 根据亚专业获取话题
    follow,
    getCommunitySums, // 统计
    getOtherNoSubSpecialtyCommunityTopicPage, // 获取其他亚专业
  } from '@/api/topic-circle'

  export default {
    name: 'topic',
    head() {
      return {
        title: '话题-话题圈子-脑医汇',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '脑医汇,神外资讯,神介资讯,神内资讯,话题,用户社区'
          },
          {
            hid: 'description',
            name: 'description',
            content: '聚焦行业动态，讨论热门话题。'
          },
        ],
        timer: null
      }
    },
    components: {NullData},
    computed:{
      setNum1000(){
        return (num)=>{
          if (num > 999) {
            return (num/1000).toFixed(1) + 'k'
          }else{
            return num;
          }
        }
      },
    },
    async asyncData({ app, params, error, store, query, req, redirect, route }) {
      if (!store.state.auth.user.id) {
        redirect(`/signin?fallbackUrl=${route.fullPath}`)
        return
      }
      // 获取亚专业
      const [subspecialty] = await Promise.all([
        app.$axios.$request(
          getCommunityUserSpecialityList({})
        )
      ])
      // 关注的话题
      const [followData, statistics] = await Promise.all([
        app.$axios.$request(
          getCommunityFollowTopicPage({
            userId: store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ),
        app.$axios.$request(
          getCommunitySums({
            type: 'T'
          })
        ),
      ])
      let followFinished = false;
      if (1 >= followData.page.totalPage) {
        followFinished = true;
      }
      return {
        subspecialty, // 亚专业
        followData_a: followData.list, // 关注
        followFinished, // 关注数据是否已全部加载完
        statistics, // 统计
      }
    },
    data() {
      return {
        qrShareUrl: '',
        tabActive: 0,
        pageData_a: [
          {
            icon: require('~/assets/images/topic_circle/topic_attention.png'),
            name: '关注',
            list: [],
            pageNo: 2,
            pageSize: 10,
            loading: false, // 加载状态结束
            finished: false, // 数据全部加载完成
            loading_error: false, // 数据加载错误
            response: false // 请求是否成功
          }
        ],
        other_icon: require('~/assets/images/topic_circle/icon_topic_other.png'),
        default_topic_share: require('~/assets/images/topic_circle/icon-topic-share.png'),
        default_circle_share: require('~/assets/images/topic_circle/icon-circle-share.png'),
      }
    },
    methods: {
      // 切换tab
      tabChange_fn(index) {
        this.tabActive = index
        if (!this.pageData_a[index].list.length) {
          if (index === 0) {
            // 我的关注
          } else {
            // 亚专业
            this.getTabList_fn()
          }
        }
      },
      // 加載更多
      async getTabList_fn() {
        this.pageData_a[this.tabActive].loading = true
        let res = {};
        if (this.tabActive === 0) {
          // 我的关注
          const [response] = await Promise.all([
            this.$axios.$request(
              getCommunityFollowTopicPage({
                userId: this.$store.state.auth.user.id,
                pageNo: this.pageData_a[this.tabActive].pageNo,
                pageSize: this.pageData_a[this.tabActive].pageSize
              })
            )
          ])
          res = response;
        }else if(this.pageData_a[this.tabActive].name === '其他'){
          const [response] = await Promise.all([
            this.$axios.$request(
              getOtherNoSubSpecialtyCommunityTopicPage({
                userId: this.$store.state.auth.user.id,
                pageNo: this.pageData_a[this.tabActive].pageNo,
                pageSize: this.pageData_a[this.tabActive].pageSize
              })
            )
          ])
          res = response;
        } else {
          // 亚专业
          const [response] = await Promise.all([
            this.$axios.$request(
              getCommunityTopicPageBySubSpecialty({
                subSpecialtyId: this.pageData_a[this.tabActive].id,
                userId: this.$store.state.auth.user.id,
                pageNo: this.pageData_a[this.tabActive].pageNo,
                pageSize: this.pageData_a[this.tabActive].pageSize
              })
            )
          ])
          res = response;
        }
        if (res.code === 1) {
          this.pageData_a[this.tabActive].list = [...this.pageData_a[this.tabActive].list, ...res.list]
          this.pageData_a[this.tabActive].response = true // 请求成功状态
          if (this.pageData_a[this.tabActive].pageNo >= res.page.totalPage) {
            this.pageData_a[this.tabActive].finished = true
          }
          this.pageData_a[this.tabActive].pageNo++
          this.pageData_a[this.tabActive].loading = false
        }
      },
      // 跳话题详情
      toCommunityDetails_fn(id) {
        this.$router.push(`/topic-circle/communitytopic?id=${id}`)
      },
      // 关注话题
      followTopicCircle_fn(item) {
        this.$axios.$request(
          follow({
            contentId: item.id,
            userId: this.$store.state.auth.user.id,
            type: item.type
          })
        ).then(res => {
          if (res.code === 1) {
            item.isFollow = !item.isFollow
            this.$analysys.add_follow(this.$store.state.auth.user.id + '',item.id + '',this.$store.state.auth.user.realName,item.title,item.isFollow?'关注':'取消','话题');
          }
        })
      },
      // 组装数据
      setData_fn() {
        this.subspecialty.list.map(v => {
          this.pageData_a.push({
            name: v.name,
            icon: v.dictIcon,
            id: v.id,
            iconSwitch: v.iconSwitch,
            isNew: v.isNew,
            list: [],
            pageNo: 1,
            pageSize: 10,
            loading: false, // 加载状态结束
            finished: false, // 数据全部加载完成
            loading_error: false, // 数据加载错误
            response: false // 请求是否成功
          })
        })

        this.$axios.$request(
          getOtherNoSubSpecialtyCommunityTopicPage({
            userId: this.$store.state.auth.user.id,
            pageNo: 1,
            pageSize: 10
          })
        ).then(res => {
          if (res.code === 1) {
            if(res.list.length){
              this.pageData_a.push({
                name: '其他',
                icon: this.other_icon,
                list: res.list,
                pageNo: 2,
                pageSize: 10,
                loading: false, // 加载状态结束
                finished: (1 >= res.page.totalPage?true:false), // 数据全部加载完成
                loading_error: false, // 数据加载错误
                response: true // 请求是否成功
              })
            }
          }
        })
      }
    },
    created(){
      this.pageData_a[0].list = this.followData_a;
      this.pageData_a[0].finished = this.followFinished;
      this.setData_fn()
    },
    mounted() {
      qrcade.toDataURL(window.location.href)
        .then((img) => {
          this.qrShareUrl = img
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
</script>

<style scoped lang="less">
  #topic {
    background: #FBFBFB;
    overflow: hidden;
    padding-bottom: 60px;
  }

  .main {
    max-width: 1200px;
    width: 1200px;
    margin: 0 auto;
    transition: all 0.3s;

    display: flex;
    justify-content: space-between;
  }

  .header {
    background: url("assets/images/topic_circle/topic_header_bg.png") no-repeat center;
    background-size: cover;
    height: 200px;
    margin-bottom: 100px;
    color: #fff;

    .headerCon {
      padding-top: 34px;

      .pageTitle {
        font-size: 32px;
        margin-bottom: 12px;
      }

      .pageInfo {
        font-size: 14px;
      }
    }

    .share {
      background: #fff;
      border-radius: 4px;
      color: #0581ce;
      padding: 0 30px;
      margin-top: 34px;
      line-height: 38px;
      height: 38px;
      flex: 0 0 auto;
      cursor: pointer;
      position: relative;

      .topic-icon {
        margin-right: 4px;
      }

      &:hover .shareQRBox, .shareQRBox:hover {
        visibility: inherit;
        opacity: 1;
        pointer-events: inherit;
      }

      .shareQRBox {
        position: absolute;
        top: 38px;
        left: -7px;
        visibility: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        opacity: 0;
        transition: all .3s;
        pointer-events: none;
        padding-top: 10px;
        z-index: 2;

        &::after {
          content: '';
          border-width: 6px;
          border-color: transparent;
          border-bottom-color: #fff;
          border-style: solid;
          position: absolute;
          top: -2px;
        }

        img {
          width: 130px;
          height: 130px;
          border-radius: 4px 4px 0 0;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.15);
        }

        span {
          color: #222222;
          font-size: 14px;
          margin-top: -0.7rem;
          background: #fff;
          width: 100%;
          border-radius: 0 0 4px 4px;
          text-align: center;
          box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }

    .navClass {
      display: flex;
      width: 100%;
      background: #fff;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
      border-radius: 6px;
      margin-top: 34px;
      position: relative;

      .bottom_line {
        content: '';
        display: block;
        width: 15px;
        height: 4px;
        border-radius: 4px;
        background: #0581ce;
        position: absolute;
        bottom: 0;
        left: 78.21px;
        transition: all .3s;
      }

      li {
        padding: 30px 0;
        width: 100%;
        cursor: pointer;

        &.active span {
          color: #0581ce;
        }

        div {
          display: flex;
          flex-direction: column;
          align-items: center;
          border-right: 1px solid #F6F6F6;
        }

        &:last-of-type {
          border-right: none;
        }

        img {
          width: 52px;
          height: 52px;
          border-radius: 50%;
          margin-bottom: 20px;
        }

        span {
          color: #222;
        }
      }
    }
  }

  .topicList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    margin-top: 20px;

    li {
      display: flex;
      flex-direction: column;
      width: 558px;
      background: #FFFFFF;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 20px;

      .topicInfo {
        display: flex;
        align-items: center;

        img {
          width: 80px;
          height: 80px;
          border-radius: 4px;
          cursor: pointer;
          object-fit: cover;
        }

        .title {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin: 0 12px;
          height: 80px;
          cursor: pointer;

          h5 {
            font-size: 18px;
          }

          span {
            font-size: 14px;
            color: #999;

            i {
              padding-right: 8px;
              margin-right: 8px;
              border-right: 1px solid #F6F6F6;
              font-style: normal;
            }
          }
        }

        b {
          color: #0581CE;
          border: 1px solid #0581ce;
          border-radius: 100px;
          font-size: 15px;
          padding: 0 17px;
          line-height: 32px;
          font-weight: normal;
          display: flex;
          align-items: center;
          cursor: pointer;


          &.active {
            background: #D2D2D2;
            border: none;
            color: #fff;
          }

          svg {
            width: 10px;
            margin-right: 4px;
          }
        }
      }

      p {
        font-size: 16px;
        margin-top: 16px;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        cursor: pointer;
      }
    }
  }

  .more {
    width: 164px;
    line-height: 48px;
    background: #fff;
    color: #666;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    font-size: 14px;
    text-align: center;
    margin: 0 auto;
    border-radius: 6px;
    cursor: pointer;
  }
</style>
