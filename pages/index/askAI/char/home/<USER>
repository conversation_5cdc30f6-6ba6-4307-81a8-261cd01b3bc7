<script>
import History from '@/components/Ai/History'
import FeedBack from '@/components/Ai/FeedBack'
import {Dialog, Drawer} from 'element-ui'
import {AISocket} from '@/utils/aiWS.js'
import {getListBySessionId, saveChat, saveOtherQuestion, getDetailByCid} from '@/api/ai.js'
import MessageChatItem from '@/components/Ai/MessageChatItem.vue'
import MessageItem from '@/components/Ai/MessageItem.vue'

export default {
  name: 'AI',
  components: {Dialog, MessageItem, MessageChatItem, Drawer, FeedBack, History},
  head() {
    return {
      title: '脑医汇 - AI问答 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS，AI问答'
        }
      ]
    }
  },
  data() {
    return {
      newTopicIcon: require('@/assets/images/ai/new_topic.png'),
      historyIcon: require('@/assets/images/ai/history.png'),
      sendIcon: require('@/assets/images/ai/ai_send.png'),
      sendActiveIcon: require('@/assets/images/ai/ai_send_active.png'),
      sendSuc: require('@/assets/images/ai/sendSuc.png'),
      placeholder: '请输入您的问题...',
      textarea: '',
      rows: 1,
      historyDrawer: false,
      CPDrawer: false,
      drawerStyle: {},
      ws: null,
      sessionId: '',
      aiChatBeanList: [],
      aiChatBean: {
        queryData: '',
        answerFlag: '',
        answer: '',
        chatId: '',
        sessionId: '',
        keywords: [],
        question: '',
        collect: 'F',
        feedback: 'F',
        interrupt: 'F',
        saveStatus: false,
        isCollect: false,
        knowledgeInfo: [],
        relatedQuestions: [],
      },
      isIng: false,
      fullAnswer: '',
      isFirst: true,
      hasNew: false,
      feedCid: '',
      sId: '',
      feedTitle: '',
      dialogVisible: false,
    }
  },
  mounted() {
    this.$analysys.pageview(
      this.$store.state.auth.user.id,
      this.$store.state.auth.unionid,
      'AI问答'
    )
    document.querySelector('footer').style.display = 'none'
    document.body.classList.add('no-scroll_ai');
    // 获取参数
    console.log('参数', this.$route.query)
    if (this.$route.query && this.$route.query.chatId) {
      // 获取历史
      this.getChat(this.$route.query.chatId)
    }

    // 启动socket
    // this.$AIWebSocket
    this.sessionId = this.guid()
    console.log('执行连接')

    if (this.$route.query && this.$route.query.question) {
      this.ws = new AISocket((data) => this.responseFn(data), () => {
        this.createNewMessage(this.$route.query.question)
      })
    } else {
      this.ws = new AISocket((data) => this.responseFn(data))
    }
    // this.ws.WebSocketInit()
  },
  beforeDestroy() {
    // 组件销毁前移除 .no-scroll 类以恢复滚动
    document.querySelector('footer').style.display = 'block'
    document.body.classList.remove('no-scroll_ai');
  },
  destroyed() {
    this.ws.close()
  },
  computed: {
    showImage() {
      if (this.isIng) {
        return this.sendSuc
      } else {
        // if (this.chatStatus == 0) {
        //   return $r('app.media.ai_chat_send_off')
        // } else {
        if (this.textarea.length > 0) {
          return this.sendActiveIcon
        } else {
          return this.sendIcon
        }
        // }
      }
    }
  },
  methods: {
    addRows() {
      console.log(this.$refs.textareaRef.$el.children)
      // 获取行数
      const style = window.getComputedStyle(this.$refs.textareaRef.$el.children[0]);
      // 获取计算样式
      const lineHeight = parseFloat(style.lineHeight); // 提取 line-height
      const scrollHeight = this.$refs.textareaRef.$el.children[0].scrollHeight; // 文本内容总高度
      this.rows = Math.round(scrollHeight / lineHeight);
    },
    sendMessage(event) {
      event && event.preventDefault()
      if (this.isIng) {
        this.stopMessage()
        return
      }
      if (!this.textarea) return;
      // 没有登录禁止发送
      if (!this.$store.state.auth.token) {
        this.$store.commit('editBackUrl', window.location.href) // 用于微信跳转
        this.$router.push({
          path: '/signin',
          query: {
            fallbackUrl: `/askAI/char/home?question=${this.textarea}`,
          },
        })
        return;
      }
      if (this.hasNew) {
        this.sessionId = this.guid()
        // this.aiChatBeanList.push(this.aiChatBean)
        this.aiChatBean = {
          queryData: '',
          answerFlag: '',
          answer: '',
          chatId: '',
          sessionId: '',
          keywords: [],
          question: this.textarea,
          collect: 'F',
          feedback: 'F',
          interrupt: 'F',
          saveStatus: false,
          isCollect: false,
          knowledgeInfo: [],
          relatedQuestions: [],
        }
        this.hasNew = false
      }
      let message = {
        question: this.textarea,
        model: "doubao",
        sessionId: this.sessionId,
        history: []
      }
      this.ws.send(JSON.stringify(message), () => {
        console.log('发送成功')
        this.hasNew = true
      }, (err) => {
        console.log('发送失败' + JSON.stringify(err))
      })
      this.textarea = ''
    },
    historyHandler(type) {
      // 获取目标元素的位置和高度
      const targetElement = this.$refs.targetElement;
      console.log(targetElement)
      const rect = targetElement.getBoundingClientRect();

      // 设置 Drawer 的遮罩层样式
      this.drawerStyle = {
        position: 'absolute',
        top: `0px`,
        right: `0px`,
        width: `${rect.width}px`, // 固定宽度
        height: `${rect.height}px`, // 高度为目标元素的高度
        borderRadius: '20px'
      };

      if (type === 'his') {
        this.historyDrawer = true;
        this.$analysys.btn_click('历史提问', `AI问答`)

      } else if (type === 'cp') {
        this.CPDrawer = true

      }
    },
    guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
          v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    responseFn(aiBean) {
      if (aiBean) {
        if (this.sessionId === aiBean.sessionId) {
          let existingIndex = this.aiChatBeanList.findIndex(bean => bean.chatId === aiBean.chatId);
          if (existingIndex === -1) {
            if (this.aiChatBean.interrupt === "F") {
              this.aiChatBean.sessionId = aiBean.sessionId
              this.aiChatBean.queryData = aiBean.queryData
              this.aiChatBean.chatId = aiBean.chatId
              this.aiChatBean.answer = aiBean.answer
              this.aiChatBean.answerFlag = aiBean.answerFlag
              this.aiChatBean.question = aiBean.question
              if (aiBean.knowledgeInfo.length > 0) {
                this.aiChatBean.knowledgeInfo = []
                this.aiChatBean.knowledgeInfo = aiBean.knowledgeInfo
              }
              if (aiBean.keywords.length > 0) {
                this.aiChatBean.keywords = []
                this.aiChatBean.keywords = aiBean.keywords
              }
              //检查当前列表中是否存在该数据。不存在则保存
              this.isIng = true
              // 将当前接收到的answer追加到fullAnswer
              if (this.aiChatBean.answer != null) {
                this.fullAnswer += this.aiChatBean.answer;
              }
              this.aiChatBean.answer = this.fullAnswer
              this.$nextTick(() => {
                this.$refs.chat_container?.scrollIntoView();
              });

            }
          }
        }
      }
    },
    stopMessage() {
      this.$analysys.btn_click('暂停回答', 'AI问答-' + this.aiChatBean.question)
      console.log('执行了stop')
      this.aiChatBean.interrupt = "T"
      this.clearData([])
      this.$nextTick(() => {
        this.heightChange()
      })
      // this.showImage()
    },
    createNewMessage(question, type = undefined) {
      if (type) {
        if (type === 'about') {
          this.$analysys.btn_click('相关问题-' + question, 'AI问答')
        }
      }
      this.textarea = question
      this.sendMessage()
    },
    feedbackHandler(id, sid, t) {
      this.feedCid = id
      this.sId = sid
      this.feedTitle = t
      this.historyHandler('cp')
    },
    clearData(otherList) {
      this.fullAnswer = ""
      this.isIng = false
      this.aiChatBean.relatedQuestions = otherList
      //结束后插入数据
      let existingIndexTwo = this.aiChatBeanList.findIndex(bean => bean.chatId === this.aiChatBean.chatId);
      console.log('执行了clear' + existingIndexTwo)
      if (existingIndexTwo !== -1) {
        // 更新 aiChatBeanList 中的对象
        this.aiChatBeanList.splice(existingIndexTwo, 1, this.aiChatBean)
        // this.aiChatBeanList[existingIndexTwo] = this.aiChatBean;
      } else {
        console.log('执行了push')
        this.aiChatBeanList.push(this.aiChatBean)
      }
      console.log("MZQ_AI---存储状态：" + this.aiChatBean.saveStatus + "相关问题列表：" + otherList.length);
      if (this.aiChatBean.saveStatus === false) {
        //调用存储方法
        this.saveAI(this.aiChatBean.chatId).then(() => {
          console.log("MZQ_AI---执行保存saveAI返回");
        })
      }
      this.aiChatBean = {
        queryData: '',
        answerFlag: '',
        answer: '',
        chatId: '',
        sessionId: '',
        keywords: [],
        knowledgeInfo: [],
        question: '',
        collect: 'F',
        feedback: 'F',
        interrupt: 'F',
        saveStatus: false,
        isCollect: false,
        relatedQuestions: []
      }
      this.hasNew = false
      // console.log("MZQ_AI---结束：" + this.aiChatBeanList.length);
    },
    async saveAI(chatId) {
      console.log("MZQ_AI---执行保存" + chatId);
      const index = this.aiChatBeanList.findIndex(bean => bean.chatId === chatId);
      console.log("MZQ_AI---执行保存id" + index)
      if (index !== -1) {
        const foundBean = this.aiChatBeanList[index];
        let data = new Object();
        data['userId'] = this.$store.state.auth.user.id;
        data['sessionId'] = foundBean.sessionId;
        data['chatId'] = foundBean.chatId;
        data['question'] = foundBean.question;
        data['queryData'] = foundBean.queryData;
        if (foundBean.answerFlag) {
          data['answerFlag'] = foundBean.answerFlag;
        }
        if (foundBean.answer) {
          data['answer'] = foundBean.answer;
        }
        let json = this.refineObj(foundBean.knowledgeInfo);
        data['knowledgeInfo'] = JSON.stringify(json);
        data['keywords'] = JSON.stringify(foundBean.keywords);
        data['interrupt'] = foundBean.interrupt;
        const res = await this.$axios.$request(saveChat(data))
        if (res && res.code === 1) {
          this.aiChatBeanList[index].saveStatus = true
          console.log('相关问题', foundBean.relatedQuestions.length)
          if (foundBean.relatedQuestions.length > 0) {
            console.log("MZQ_AI---开始存储相关问题:" + chatId);
            this.saveOtherQuestionList(foundBean)
          }
        }
      } else {
        console.log("MZQ_AI---存储失败,未查询到数据 AI——ID" + chatId + "====列表数据：" + this.aiChatBeanList);
      }
      this.$nextTick(() => {
        this.heightChange()
      })
    },
    async saveOtherQuestionList(foundBean) {
      console.log('相关问题执行到这里')
      const res = await this.$axios.$request(saveOtherQuestion({
        chatId: foundBean.chatId,
        relatedQuestions: JSON.stringify(this.refineObj(foundBean.relatedQuestions))
      }))
      if (res && res.code === 1) {
        console.log("MZQ_AI---相关问题存储成功:" + foundBean.chatId);
      }
    },
    async getAiHistoryBySessionId(sessionId) {
      this.historyDrawer = false
      console.log('历史ID2-' + sessionId)
      this.sessionId = sessionId
      const res = await this.$axios.$request(getListBySessionId({
        userId: this.$store.state.auth.user.id,
        sessionId
      }))
      if (res && res.code) {
        this.aiChatBeanList = []
        res.list?.map((item) => {
          this.aiChatBeanList.push(item)
        })
        this.$nextTick(() => {
          this.heightChange()
        })
      }
    },
    async getChat(chatId) {
      const res = await this.$axios.$request(getDetailByCid({
        userId: this.$store.state.auth.user.id,
        chatId
      }))
      if (res && res.code === 1 && res.result) {
        this.aiChatBeanList.push(res.result)
        this.$nextTick(() => {
          this.heightChange()
        })
      }
    },
    heightChange() {
      if (!this.$refs.ai_box_container) return;
      this.$refs.ai_box_container.scrollTop = this.$refs.ai_box_container.scrollHeight - this.$refs.ai_box_container.clientHeight
    },
    createNewSession() {
      this.$analysys.btn_click('新会话', 'AI问答')

      if (this.aiChatBeanList.length == 0) {
        this.$toast('当前已是最新会话')
        return
      }
      if (this.isIng) { //正在输出内容，结束输出
        this.$toast('当前问题正在输出')
        return
      }
      this.sessionId = this.guid()
      this.aiChatBeanList = []
    },
    refineObj(obj) {
      if (Array.isArray(obj)) {
        let returnContext = [];
        obj.forEach((item) => {
          returnContext.push(this.refineObj(item))
        })
        return returnContext
      } else if (Object.prototype.toString.call(obj) === '[object Object]') {
        let returnContext = new Object();
        const keys = Object.keys(obj)
        for (let i = 0; i <= keys.length - 1; i++) {
          let data = this.refineObj(obj[keys[i]])
          if (data && data != null) {
            returnContext[keys[i]] = data
          }
        }
        return returnContext
      } else {
        return obj
      }
    },
    changeStatusCd(chatId, type, value) {
      const i = this.aiChatBeanList.findIndex(item => item.chatId === chatId)
      if (i > -1) {
        const obj = JSON.parse(JSON.stringify(this.aiChatBeanList[i]))
        switch (type) {
          case 'c':
            obj.collect = value
            this.$toast(value === 'T' ? '收藏成功' : '取消收藏成功')
            break
          case 'd':
            obj.feedback = value
        }
        this.aiChatBeanList.splice(i, 1, obj)
      }
    },
    close() {
      this.dialogVisible = false
      // this.$emit('change');
    },
    toOpenAns() {
      this.$analysys.btn_click('发提问', `AI问答-${this.feedTitle}`)
      this.close()
      window.sessionStorage.setItem('editTitleGlobal', this.feedTitle)
      this.feedTitle = ''
      window.open('/topic-circle/write');

    },
    fbChange() {
      this.CPDrawer = false
      const identity = this.$store.state.auth.user.identity
      if (identity === 1 || identity === 2 || identity === 5) {
        this.dialogVisible = true
      } else {
        this.$toast('反馈已提交')
      }

    }
  }
}
</script>

<template>
  <div class="ai_home">
    <div class="ai_container" ref="targetElement">
      <div class="ai_container_box">

        <div ref="ai_box_container" class="ai_box_container">
          <div class="ai_box_title">
            <div>AI问答</div>
          </div>
          <MessageChatItem ref="chat_container" :chatList="aiChatBeanList"
                           :aIChatBeanBottom="hasNew ? aiChatBean : undefined" :clear="(data) => clearData(data)"
                           :createNew="(d, type) => createNewMessage(d, type)"
                           :feedbackHandler="(i, s, t) => feedbackHandler(i,s, t)" :heightChange="() => heightChange()"
                           :cbStatus="(s,t,v) => changeStatusCd(s,t,v)"></MessageChatItem>
          <!--        <MessageItem :left="true"></MessageItem>-->
          <div v-if="aiChatBeanList.length && !hasNew" class="open_new">
            <div class="line"></div>
            <div class="text">开启新话题</div>
            <div class="line"></div>

          </div>
        </div>
        <div class="ai_box_bottom">
          <div class="ai_input" :style="{
          borderRadius: rows === 1 ? '54px' : '24px'
        }">
            <el-input
              ref="textareaRef"
              type="textarea"
              :autosize="{maxRows: 6}"
              resize="none"
              :placeholder="placeholder"
              v-model="textarea"
              @input="addRows"
              @keydown.enter.native="sendMessage"
            >
            </el-input>
            <img class="ai_send" :src="this.showImage" @click="sendMessage"/>
          </div>
          <div class="ai_tip">内容由AI生成，请自行核实内容准确性</div>
        </div>
        <Dialog
          :visible.sync="dialogVisible"
          custom-class="digclass"
          :modal="false"
          :show-close="false"
        >
          <div class="container">感谢你的反馈，你可将提问发布至话题圈子，以获得更多回答。</div>
          <div slot="footer" class="dialog-footer">
            <div class="err" @click="close">不了</div>
            <div class="suc" @click="toOpenAns">发提问</div>
          </div>
        </Dialog>
      </div>
      <div class="ai_right_btns">
        <img :src="newTopicIcon" alt="" @click="createNewSession">
        <img :src="historyIcon" @click="historyHandler('his')" alt="">
      </div>
      <Drawer
        :with-header="false"
        :visible.sync="historyDrawer"
        :modal="false"
        :show-close="false"
        :style="drawerStyle"
        custom-class="drawc"
        direction="rtl">
        <History @historyContent="getAiHistoryBySessionId" :sessionId="sessionId"></History>
      </Drawer>
      <Drawer
        :with-header="false"
        :visible.sync="CPDrawer"
        :modal="false"
        :show-close="false"
        :style="drawerStyle"
        custom-class="drawc"
        direction="rtl">
        <FeedBack :cid="feedCid" @change="fbChange" :cbStatus="(s,t,v) => changeStatusCd(s,t,v)"></FeedBack>
      </Drawer>
      <div v-if="historyDrawer || CPDrawer || dialogVisible" class="overflow">

      </div>
    </div>

  </div>
</template>
<style scoped lang="less">
.ai_home {
  padding: 32px 0;
  width: 100%;
  height: calc(100vh - 60px);
  background-color: #EEF4F6;
  box-sizing: border-box;
  display: flex;
  justify-content: center;

  .ai_container {
    position: relative;
    width: 792px;
    height: 100%;
    border-radius: 20px;
    border: 1px solid #D3E7F2;
    background: #FFF;

    box-sizing: border-box;

    .overflow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 20px;
      border: 1px solid #D3E7F2;
      background: rgba(51, 51, 51, 0.30);
    }
  ;

    .ai_container_box {
      position: absolute;
      top: 0;
      left: 0;
      padding: 24px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      width: 100%;
      height: 100%;

      .ai_box_title {
        margin-bottom: 12px;
        width: 647px;
        height: 146px;
        flex-shrink: 0;
        position: relative;
        background-image: url("../../../../../assets/images/ai/ai_title_bg.png");
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;

        div {
          position: absolute;
          top: 0;
          left: 0;
          color: #333;
          font-family: "Source Han Sans CN";
          font-size: 18px;
          font-style: normal;
          font-weight: 500;
          line-height: 150%; /* 27px */
        }
      }

      .ai_box_container {
        flex: 1;
        width: 100%;
        overflow-y: scroll;

        & > div {
          margin-top: 24px;
        }

        &::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
        }
      }

      .ai_box_bottom {
        padding-top: 20px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;

        .ai_input {
          padding: 8px;
          box-sizing: border-box;
          display: flex;
          align-items: flex-end;
          width: 100%;
          min-height: 48px;
          border-radius: 54px;
          border: 1px solid #E4F0F7;
          background: #F7F9FB;
          overflow: hidden;
          align-items: center;

          /deep/ .el-textarea {
            padding-left: 8px;
            flex: 1;
            //min-height: 48px;

            textarea {
              resize: none;
              padding: 0;
              width: 100%;
              outline: none;
              border: none;
              background-color: transparent;
              color: #333;
              text-align: start;
              font-family: "Source Han Sans CN";
              font-size: 15px;
              font-style: normal;
              font-weight: 400;
              line-height: 32px;
            }
          }

          .ai_send {
            width: 32px;
            height: 32px;
            flex-shrink: 0;

            &:hover {
              cursor: pointer;
            }
          }
        }

        .ai_tip {
          margin-top: 16px;
          width: 100%;
          color: #95AECD;
          text-align: center;
          font-family: "Source Han Sans CN";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 14.4px; /* 120% */
        }
      }
    }

    .ai_right_btns {
      position: absolute;
      right: -56px;
      top: 24px;
      width: 36px;

      & > img {
        width: 36px;

        &:hover {
          cursor: pointer;
        }
      }
    }


  }
}

/deep/ .drawc {
  border-radius: 20px;
  width: 374px !important;
}

.open_new {
  margin-top: 24px;
  margin-bottom: 24px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .line {
    width: 108.5px;
    height: 0.5px;
    background-color: #B4C5D9;
  }

  .text {
    margin: 0 12px;
    color: #95AECD;
    white-space: wrap;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%; /* 18px */
  }
}

/deep/ .digclass {
  margin-top: 120px;
  width: 278px;
  padding-top: 10px;
  background-color: #fff;
  border-radius: 14px;
  overflow: hidden;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 12px 20px;
  }

  .container {
    //padding: 12px 20px;
    box-sizing: border-box;
    width: 100%;
    color: #333;
    text-align: center;
    font-variant-numeric: lining-nums tabular-nums;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .el-dialog__footer {
    padding: 0;
  }

  .dialog-footer {
    margin-top: 15px;
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;

    div {
      height: 100%;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #F3F3F3;
      color: #888;
      text-align: center;
      font-variant-numeric: lining-nums tabular-nums;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px; /* 125% */

      &:hover {
        cursor: pointer;
      }
    }

    .suc {
      background: #0581CE;
      color: #FFF;
    }
  }
}
</style>
