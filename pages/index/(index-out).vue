<template>
  <div class='container-box consulting-box'>
    <!--幸运大抽奖 弹窗-->
    <LuckyDrawPop/>
    <!-- 首页二级导航 _ 新样式 -->
    <SecondaryNavigation/>
    <!--End-->
    <div class='page-left'>
      <!--首页左侧内容 bigbox Start-->
      <div class='left-bigbox'>
        <div ref="index_banner_wrapper" class='block' id="index_banner_wrapper">
          <el-carousel
            indicator-position='none'
            @change="changeHandler"
            :arrow="advertisementData.length > 1 ? 'hover' : 'never'"
          >
            <el-carousel-item v-for='(item, index) in advertisementData' :key='item.id'>
              <div class='banner_box'>
                <img
                  :alt='item.name'
                  :class="item.extras ? 'cursor' : ''"
                  :src='$tool.compressImg(item.image,793,264)'
                  class='img_cover'
                  @click='jumpBannerFun(item)'
                />
                <div class='mask fontSize18'>
                  {{ item.name }}
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <!--new consulting Start-->
        <div class='NewConsulting'>
          <div class='title-content' style='margin-top: 30px'>
            <div class='left-con'>
                <span style='color: #0581ceff; font-weight: 540'>【 </span
                ><span>最新资讯</span
            ><span style='color: #adc4d0ff; margin: 0 12px'>|</span
            ><span style='color: #adc4d0ff'>NEWS</span>
            </div>
            <div class='right-con'>
              <div class='line'></div>
              <nuxt-link :to="{ name: 'index-info' }">
                <p @click="analysysFun('最新咨询-查看更多')">
                  查看更多 <i class='el-icon-caret-right'></i>
                </p>
              </nuxt-link>
            </div>
          </div>
          <ul class='consulting'>
            <li
              v-for='item in consultingData'
              :key='item.id'
              class='consulting-item flex_start cursor'
              @click='jumpDetailsPageFun(item.infoId)'
            >
              <div class='item-left'>
                <img v-if='item.infoImg' :alt='item.infoTitle' :src='$tool.compressImg(item.infoImg,224,126)'
                     class='img_cover'/>
                <img v-else :alt='item.infoTitle' class='img_cover' src='~assets/images/default16.png'/>
              </div>
              <div class='item-right flex_column'>
                <p class='pone fontWeight text-limit-1'>
                  {{ item.infoTitle }}
                </p>
                <p class='pthree'>
                  <i class='el-icon-time'></i>
                  <span style='margin-right: 22px'>{{
                      timeStamp.timestampFormat(
                        Date.parse(item.publishDate.replace(/-/g, '/')) / 1000
                      )
                    }}</span>
                  <svg-icon
                    class-name='pthreeIcon cursor'
                    icon-class='yuanchan'
                  ></svg-icon>
                  <span>{{
                      item.authorNames ? item.authorNames : '脑医资讯助手'
                    }}</span>
                </p>
                <p class='ptwo text-limit-2'>
                  {{ item.description }}
                </p>
              </div>
            </li>
          </ul>
        </div>
        <!--End-->
        <!--hot Meeting Start-->
        <div v-if='hotMeetingDataList.length > 0' class='title-content'>
          <div class='left-con'>
              <span style='color: #0581ceff; font-weight: 540'>【 </span
              ><span>热门会议</span><span style='color: #adc4d0ff; margin: 0 12px'>|</span
          ><span style='color: #adc4d0ff'>CONFERENCE</span>
          </div>
          <div class='right-con'>
            <div class='line'></div>
            <nuxt-link :to="{ name: 'index-meeting-home' }">
              <p @click="analysysFun('热门会议-查看更多')">
                查看更多 <i class='el-icon-caret-right'></i>
              </p>
            </nuxt-link>
          </div>
        </div>
        <HotMeetings v-if='hotMeetingDataList.length > 0' :hot-meeting-data-list='hotMeetingDataList'/>
        <!--common image Start-->
        <el-row ref="middle_banner_wrapper" :gutter='20' class='common-image' id="middle_banner_wrapper">
          <el-col
            v-for='(item, index) in leftBannerData'
            :key='index'
            :lg='12'
            :md='12'
            :sm='12'
            :xl='12'
            :xs='24'
          >
            <div class='common_advertisement'>
              <img
                :class="item.extras ? 'cursor' : ''"
                :src='$tool.compressImg(item.image,387,128)'
                :alt='item.name'
                class='img_cover'
                @click='jumpBannerFun(item)'
              />
              <div class='position_top'>广告</div>
            </div>
          </el-col>
        </el-row>
        <!--End-->
        <!--End-->
        <!--case Start-->
        <div class='title-content' style='margin-top: 70px'>
          <div class='left-con subspecial-box-left'>
              <span style='color: #0581ceff; font-weight: 540'>【 </span
              ><span>精选病例</span><span style='color: #adc4d0ff; margin: 0 12px'>|</span
          ><span style='color: #adc4d0ff'>CASES</span>
          </div>
          <div class='flex_end right-con subspecial-box'>
            <p class='subspecial-box-p text-limit-1'>
                <span
                  v-for='(item, index) in subSpecialtyDataList'
                  v-show='index < 5'
                  :key='item.id'
                  :style="
                    subSpecialtyDataListCurrent === item.id
                      ? 'font-weight:bold;color:#000000'
                      : null
                  "
                  class='right_con_a cursor'
                  href=''
                  @click='switchSubspecialFun(item.id, index, item.name)'
                >{{ item.name }}
                </span>
            </p>
            <p class='flex_shrink' @click="analysysFun('精选病例-查看更多')">
              <nuxt-link to='/case'>查看更多</nuxt-link>
              <i class='el-icon-caret-right'></i>
            </p>
          </div>
        </div>

        <SelectedCases
          :case-info-data-list='caseInfoDataList'
          :sub-specialty-data-list-current='subSpecialtyDataListCurrent'/>
        <!--End-->
        <!--curriculum Start-->
        <div class='title-content' style='margin: 50px 0 20px'>
          <div class='left-con'>
              <span style='color: #0581ceff; font-weight: 540'>【 </span
              ><span>精品课程</span><span style='color: #adc4d0ff; margin: 0 12px'>|</span
          ><span style='color: #adc4d0ff'>COURSES</span>
          </div>
          <div class='right-con'>
            <div class='line'></div>
            <nuxt-link to='/mooc'>
              <p @click="analysysFun('精品课程-查看更多')">
                查看更多 <i class='el-icon-caret-right'></i>
              </p>
            </nuxt-link>
          </div>
        </div>
        <ExcellentCourses :curriculum='curriculum'/>
        <!--End-->
      </div>
      <!--End-->
    </div>


    <div class='page-right'>
      <!--首页右侧内容 bigBox Start-->
      <div class='right-bigbox'>
        <!--Live today Start-->
        <div v-if='todayMeetingsDataList.length > 0' class='shareStyle'>
          <div style='overflow: hidden'>
            <div class='title flex_between flex_align_center live_title'>
              <div class='flex_start flex_align_center'>
                <svg-icon class-name='shareStyleIcon' icon-class='live'></svg-icon>
                <span>今日直播</span>
              </div>
              <nuxt-link to='/meeting/home'>
                <div class='see_more fontSize12 themeFontColor'>
                  查看更多
                  <i class='el-icon-caret-right'></i>
                </div>
              </nuxt-link>
            </div>

            <div
              ref='live_list_bovex_big_box'
              class='live_list_bovex_big_box flex_start'
            >
              <ul
                v-for='(item, index) in todayMeetingsDataList'
                :key='index'
                class='live_list_bovex'
              >
                <li
                  v-for='live in item'
                  :key='live.id'
                  class='live-box flex_start flex_align_center'
                >
                  <nuxt-link
                    :to='{ path: `/meeting/detail`,query: {id:live.id} }'
                    target='_blank'
                  >
                    <div class='live-right flex_start'>
                      <todayLive-state
                        :state='live.meetingLiveStatus'
                      ></todayLive-state>
                      <div>
                        <div class='live-title text-limit-2'>
                          <b>{{ live.meetingName }}</b>
                        </div>
                        <p class='live-info text-limit-1'>
                            <span style='margin-right: 10px'
                            >{{ live.province }}-{{ live.city }}</span
                            >
                          {{ timeStamp.timestamp_13(live.startTime, 'h-m') }}
                          -
                          {{ timeStamp.timestamp_13(live.endTime, 'h-m') }}
                        </p>
                      </div>
                    </div>
                  </nuxt-link>
                </li>
              </ul>
            </div>
            <div v-if='todayMeetingsDataList.length > 1' class='slide_box flex_center'>
              <div @click="slideLiveFun('left')">
                <svg-icon
                  :icon-class="liveSlideNum <= 0 ? 'slide_left' : 'left'"
                  class-name='liveStyleIcon icon cursor'
                ></svg-icon>
              </div>
              <div @click="slideLiveFun('right')">
                <svg-icon
                  :icon-class="
                      liveSlideNum >= todayMeetingsDataList.length - 1
                        ? 'slide_right'
                        : 'right'
                    "
                  class-name='liveStyleIcon icon cursor'
                ></svg-icon>
              </div>
            </div>
          </div>
        </div>
        <!--End-->
        <div v-for='item in rightBannerData' :key="item.adId" class='image-share' :title="item.name">
          <img
            :class="item.extras ? 'cursor' : ''"
            :src='$tool.compressImg(item.image,387,130)'
            :alt='item.name'
            class='img_cover'
            @click='jumpBannerFun(item)'
          />
        </div>
        <!--End-->
        <!--Competition Start-->
        <div class='shareStyle'>
          <div class='title'>
            <svg-icon class-name='shareStyleIcon cursor' icon-class='bingli'></svg-icon>
            <span>病例大赛</span>
          </div>
          <div
            v-for='item in caseData'
            :key='item.id'
            class='live-box flex_start flex_align_center cursor'
            @click='jumpCompetitionPageFun(item.code, item.competitionUrl)'
          >
            <div class='live-left themeBorderRadius'>
              <img
                v-if='item.coverImage'
                :src='$tool.compressImg(item.coverImage,142,80)'
                alt='病例大赛'
                class='img_cover'
              />
              <img v-else alt='病例大赛' class='img_cover' src='~assets/images/default16.png'/>
            </div>
            <div class='live-right bingli'>
              <div class='cpmperti text-limit-2'>
                <competition-state :state='item.status'></competition-state>
                {{ item.name }}
              </div>
              <p class='live-info'>
                {{ timeStamp.timestamp_13(item.addTime, 'yyyy-mm-d') }}
              </p>
            </div>
          </div>
        </div>
        <!--End-->
        <!--literature Start-->
        <literature :literatureNewsData.sync='literatureNewsData'></literature>
        <!--End-->
      </div>
      <!--End-->
    </div>
  </div>
</template>

<script>
import {getSlotContent} from "../../api/banner/banner";
import brandAdJump from "../../assets/helpers/brandAdJump";
import ExcellentCourses from '@/components/page/home/<USER>/ExcellentCourses'
import HotMeetings from '@/components/page/home/<USER>/HotMeetings'
import SelectedCases from '@/components/page/home/<USER>/SelectedCases'
import literature from '@/components/PageComponents/Literature/Literature'
import SecondaryNavigation from '@/components/page/home/<USER>/SecondaryNavigation'
import {
  getCompetitionList,
  getNewestHotMeetingList,
  getNewInfoList,
  getRecommendCourseList,
  selectedArticleList
} from '@/api/home'
import {getTodayMeetings} from '@/api/meeting' // 会议
import {getSubSpecialty} from '@/api/case'
import {literatureNews} from '@/api/article' // 文献速览
import CompetitionState from '@/components/CompetitionState/CompetitionState'
// eslint-disable-next-line import/no-absolute-path
import env from '/env-module'

import TodayLiveState from '@/components/PageComponents/TodayLiveState/TodayLiveState'
import LuckyDrawPop from '@/components/LuckyDrawPop/LuckyDrawPop'

export default {
  name: 'IndexPage',
  components: {
    literature,
    CompetitionState,
    TodayLiveState,
    SecondaryNavigation,
    SelectedCases,
    HotMeetings,
    ExcellentCourses,
    LuckyDrawPop
  },
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '脑医汇 - 神外资讯、神介资讯 - 领先的临床神经科学互联网平台'
    /**
     * request1 最新咨询
     * request2 广告轮播
     * request3 推荐课程
     * request4 病例大赛
     * request5 文献速览
     * request6 左侧广告
     * request7 右侧广告
     * request8 今日直播
     * request9 精选病例
     * request10 精选病例亚专业
     * request11 热门会议
     * request12 获取科室列表
     * request13 获取职称列表
     * request14 获取亚专业列表
     */
    const [
      request1,
      request2,
      request3,
      request4,
      request5,
      request6,
      request7,
      request8,
      request9,
      request10,
      request11
    ] = await Promise.all([
      app.$axios.$request(
        getNewInfoList({
          limit: store.state.datalist_count
        })
      ),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webAPi_banner',
        })
      ),
      app.$axios.$request(
        getRecommendCourseList({
          coursePageType: 'M', //页面课程类型 F 免费课程   T 神豆专区（神豆课程）  M 推荐课程（付费课程） FTS 福利课程排行榜（免费加神豆课程） MS 推荐课程排行榜（付费课程） NEWEST 最新课程
          pageNo: 1,
          pageSize: store.state.curriculum_count
        })
      ),
      app.$axios.$request(
        getCompetitionList({
          limit: 5
        })
      ),
      app.$axios.$request(
        literatureNews({
          pageNo: 1,
          pageSize: store.state.article_count
        })
      ),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webAPi_leftBanner',
        })
      ),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webAPi_rightBanner',
        })
      ),
      app.$axios.$request(getTodayMeetings()),
      app.$axios.$request(
        selectedArticleList({
          pageNo: 1,
          pageSize: store.state.case_count,
          userId: store.state.auth.user.id
        })
      ),
      app.$axios.$request(getSubSpecialty()),
      // 正式服的时候更换地址 getNewestHotMeetingList
      app.$axios.$request(
        getNewestHotMeetingList({
          limit: 6
        })
      )
    ])

    const caseData = request4.list.filter(item => item.id !== 25 && item.id !== 27)

    // 将今日直播数据 分割成 每3哥一组
    const todayMeetingresult = []
    for (let i = 0; i < request8.list.length; i += 3) {
      todayMeetingresult.push(request8.list.slice(i, i + 3))
    }

    return {
      consultingData: request1.list,
      advertisementData: request2.list,
      curriculum: request3.list,
      caseData,
      literatureNewsData: request5.list,
      leftBannerData: request6.list,
      rightBannerData: request7.list,
      todayMeetingsDataList: todayMeetingresult,
      caseInfoDataList: request9.list,
      subSpecialtyDataList: request10.list,
      hotMeetingDataList: request11.list
    }
  },
  data() {
    return {
      isCurrentPage:true,
      bannerLock: false,
      middleLock: false,
      bannerIO: null,
      subSpecialtyDataListCurrent: null, // 精选病例切换亚专业
      liveStatusleft: 'slide_left', //  鼠标悬停状态
      liveStatusright: 'slide_right',
      liveSlideNum: 0, //  今日直播滑动
      bannerIndex: 0,
      bannerIndexNum: 0,
      isExposureBanner: false
    }
  },
  mounted() {
    document.addEventListener("visibilitychange", this.isCurrentHandler);


    const bannerWrapper = this.$refs.index_banner_wrapper
    const middleWrapper = this.$refs.middle_banner_wrapper?.$el

    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && entry.target.id === "index_banner_wrapper") {
          const bannerAdItem = this.advertisementData[this.bannerIndex]
          this.isExposureBanner = true;


          if (!this.bannerLock) {
            this.bannerLock = true;
            this.$analysys.ad_exposure({
              adExtras: bannerAdItem.extras,
              adModule: bannerAdItem.module,
              exposureLocation: bannerAdItem.clickLocation,
              adCode: bannerAdItem.code,
              adId: bannerAdItem.adId,
              adName: bannerAdItem.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: bannerAdItem.extras,
            })
          }


        } else if (entry.target.id === "index_banner_wrapper" && !entry.isIntersecting) {
          this.isExposureBanner = false;
        } else if (entry.isIntersecting && entry.target.id === "middle_banner_wrapper" && !this.middleLock) {
          this.middleLock = true;
          this.leftBannerData?.forEach(item => {
            this.$analysys.ad_exposure({
              adExtras: item.extras,
              adModule: item.module,
              exposureLocation: item.clickLocation,
              adCode: item.code,
              adId: item.adId,
              adName: item.name,
              unionid: this.$store.state.auth.unionid,
              adUrl: item.extras,
            })
          })

        }
      })
    });

    this.bannerIO.observe(bannerWrapper);
    this.bannerIO.observe(middleWrapper);

    if (this.liveSlideNum <= 0) {
      this.liveStatusleft = 'left'
    }
    if (this.liveSlideNum >= this.todayMeetingsDataList.length - 1) {
      this.liveStatusright = 'right'
    }
  },
  beforeDestroy() {
    document.removeEventListener("visibilitychange", this.isCurrentHandler);
    this.isExposureBanner = false;
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }
  },
  methods: {
    // 监听是否为当前页面
    isCurrentHandler(){
      if(document.visibilityState === "hidden") {
        this.isCurrentPage = false;
      } else if (document.visibilityState === "visible") {
        this.isCurrentPage = true;
      }
    },
    // 切换走马灯
    changeHandler(index) {
      const item = this.advertisementData[index]
      if (this.bannerIndex !== index && this.isExposureBanner && this.bannerIndexNum < this.advertisementData.length && this.isCurrentPage) {
        this.bannerIndexNum = this.bannerIndexNum + 1
        this.$analysys.ad_exposure({
          adExtras: item.extras,
          adModule: item.module,
          exposureLocation: item.clickLocation,
          adCode: item.code,
          adId: item.adId,
          adName: item.name,
          unionid: this.$store.state.auth.unionid,
          adUrl: item.extras,
        })
      }
      this.bannerIndex = index;

    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    },
    // 切换亚专业
    switchSubspecialFun(id, index, name) {
      this.analysysFun(String(name))
      this.subSpecialtyDataListCurrent = id
      this.$axios.$request(selectedArticleList({
        specilityId: id,
        pageNo: 1,
        pageSize: this.$store.state.case_count,
        userId: this.$store.state.auth.user.id
      })).then((res) => {
        if (res && res.code === 1) {
          this.caseInfoDataList = res.list
        }
      })
    },
    // 跳转病例大赛
    jumpCompetitionPageFun(code, competitionUrl) {
      if (competitionUrl) {
        window.open(competitionUrl, '_blank')
      } else {
        window.open(env[process.env.NODE_ENV].ENV_API + code + '/', '_blank')
      }
    },
    //  今日直播滑动
    slideLiveFun(data) {
      switch (data) {
        case 'left':
          if (this.liveSlideNum > 0) {
            this.liveSlideNum -= 1
            this.$refs.live_list_bovex_big_box.style.transform = `translateX(-${this.liveSlideNum}00%)`
          } else {
          }
          break

        case 'right':
          // 第一个是0 所以要减一
          if (this.liveSlideNum < this.todayMeetingsDataList.length - 1) {
            this.liveSlideNum += 1
            this.$refs.live_list_bovex_big_box.style.transform = `translateX(-${this.liveSlideNum}00%)`
          } else {
            this.$toast('没有喽~')
          }
          break
      }
    },
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({
        name: 'index-info-detail',
        query: {id: infoid}
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang='less' scoped>
@import "~@/pages/index/index.less";
@import "/assets/responsive-layout/index/index-md"; // 首页中屏下样式
</style>
