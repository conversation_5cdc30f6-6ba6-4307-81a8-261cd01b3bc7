<template>
  <Container :styles="{padding:'12px 0 32px'}">
    <div class="instrument_nav">
      <FilterInstrument
        :filter-types="operationManualContentTypeList.list"
        :second-category-list="operationManualSecondCategoryList.list"
        @changeType="changeType"
        @changeCategoryId="changeCategoryId"
      />
      <div class="instrument_search_wrapper">
        <svg-icon
          v-if="!isSearchFlag"
          class-name='search_icons'
          icon-class='search'
          @click="isSearchFlag = true"
        />
        <div v-else class="instrument_search_content">
          <SearchInput
            search-history-key="instrument"
            @searchHandler="searchHandler"
            @syncInputValue="(value) => keywords = value"
          />
          <span class="search_cancel" @click="searchCancelHandler">取消</span>
        </div>

      </div>
    </div>
    <div class="list_container">
      <RollingLoad
        :loading="loading"
        :empty="infoData.list.length===0"
        :no-more="currentPageNo >= infoData.page.totalPage"
        empty-height="50vh"
        @hit-bottom="hitBottomChangeHandler"
      >
        <template #loading>
          <InfoSkeleton :limit="16" :loading="loading"/>
        </template>
        <div class="skeleton_container">
          <template v-for="(item,index) in infoData.list">
            <InfoDefaultItem
              v-if="item.type === 'info'"
              :key="index"
              :info-id="item.info.infoId"
              :image="item.info.infoImg"
              :title="item.info.infoTitle"
              :author-names="item.info.authorNames"
              :publish-date="timeStamp.timeFormat(item.info.publishDate / 1000)"
              :product-list="item.info.productList"
              :essences="item.info.essences"
            />
            <BmsVideoItem
              v-else-if="item.type === 'bmsVideo'"
              :key="index"
              :info-id="item.bmsVideo.id"
              :image="item.bmsVideo.cover"
              :title="item.bmsVideo.videoName"
              :author-names="''"
              :publish-date="timeStamp.timeFormat(item.bmsVideo.createTime / 1000)"
              :product-list="item.bmsVideo.productList"
            />
          </template>

        </div>
      </RollingLoad>
    </div>
  </Container>
</template>

<script>
import FilterInstrument from "../../../opt-components/page/instrument/FilterInstrument/index.vue";
import SearchInput from "../../../opt-components/component/SearchInput/index.vue";
import Container from "../../../opt-components/template/Container/index.vue";
import RollingLoad from "../../../opt-components/component/RollingLoad/index.vue";
import InfoSkeleton from "../../../opt-components/ui/skeleton/InfoSkeleton/index.vue";
import {BmsVideoItem, InfoDefaultItem} from '../../../opt-components/data-list'
import {
  getOperationManualByCondition,
  getOperationManualContentTypeList,
  getOperationManualSecondCategoryList
} from "../../../api/instrument.js";

export default {
  name: "InstrumentPage",
  components: {
    BmsVideoItem,
    InfoDefaultItem,
    InfoSkeleton,
    RollingLoad,
    Container,
    SearchInput,
    FilterInstrument
  },
  async asyncData({app, params, error, store, query, req}) {
    const [listData, operationManualContentTypeList, operationManualSecondCategoryList] = await Promise.all([
      app.$axios.$request(getOperationManualByCondition({
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 16
      })),
      app.$axios.$request(getOperationManualContentTypeList()),
      app.$axios.$request(getOperationManualSecondCategoryList())
    ])

    return {
      listData,
      operationManualContentTypeList,
      operationManualSecondCategoryList
    }
  },
  head() {
    return {
      title: '器械百科',
    }
  },
  created() {
    this.$set(this.infoData, "list", this.listData.list)
    this.$set(this.infoData, "page", this.listData.page)
  },
  data() {
    return {
      isSearchFlag: false,
      infoData: {
        list: [],
        page: {}
      },
      loading: true,
      currentPageNo: 1,
      contentType: null,
      categoryId: null,
      keywords: null,
      historyList: []
    }
  },
  mounted() {
    this.loading = false
  },
  methods: {
    searchCancelHandler() {
      this.isSearchFlag = false;
      if (this.keywords) {
        this.searchHandler("")
      }
    },
    async getData({pageNo, categoryId, contentType, keywords, pageUp} = {pageNo: 1}) {
      const data = await this.$axios.$request(getOperationManualByCondition({
        loginUserId: this.$store.state.auth.user.id,
        categoryId,
        contentType,
        keywords,
        pageNo,
        pageSize: 16
      }))

      if (data.code === 1) {

        if (pageUp) {
          this.$set(this.infoData, "list", [...this.infoData.list, ...data.list])
          this.$set(this.infoData, "page", data.page)
        } else {
          this.$set(this.infoData, "list", data.list)
          this.$set(this.infoData, "page", data.page)
        }

        if (this.keywords) {
          this.$analysys.search(
            data.list.length > 0,
            data.list.length,
            false,
            keywords,
            false,
            '器械百科'
          )
        }
      }

      this.loading = false;
    },
    changeType(id) {
      if (this.contentType !== id) {
        this.contentType = id;
        this.$tool.scrollIntoTop()
        this.currentPageNo = 1;
        this.$set(this.infoData, "list", [])
        this.$set(this.infoData, "page", {})
        this.loading = true

        const filterNames = this.operationManualContentTypeList.list.filter(item => item.code === id)?.[0]?.desc
        this.$analysys.app_filter({
          filter_page: '器械百科',
          filter_ids: id + '',
          filter_names: filterNames
        })

        this.getData({
          pageNo: this.currentPageNo,
          contentType: this.contentType,
          keywords: this.keywords,
          categoryId: this.categoryId,
          pageUp: true
        })

      }

    },
    changeCategoryId(params) {
      if (this.categoryId !== params.id) {
        this.categoryId = params.id;
        this.$tool.scrollIntoTop()
        this.currentPageNo = 1;
        this.$set(this.infoData, "list", [])
        this.$set(this.infoData, "page", {})
        this.loading = true

        this.$analysys.app_filter({
          filter_page: '器械百科',
          filter_ids: params.id + '',
          filter_names: params.name
        })

        this.getData({
          pageNo: this.currentPageNo,
          contentType: this.contentType,
          keywords: this.keywords,
          categoryId: this.categoryId,
          pageUp: true
        })

      }
    },
    searchHandler(value) {
      this.keywords = value;
      this.$tool.scrollIntoTop()
      this.currentPageNo = 1;
      this.$set(this.infoData, "list", [])
      this.$set(this.infoData, "page", {})
      this.loading = true

      this.getData({
        pageNo: this.currentPageNo,
        contentType: this.contentType,
        keywords: this.keywords,
        categoryId: this.categoryId,
        pageUp: true
      })

    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.currentPageNo += 1;
        this.loading = true

        this.getData({
          pageNo: this.currentPageNo,
          contentType: this.contentType,
          keywords: this.keywords,
          categoryId: this.categoryId,
          pageUp: true
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.instrument_nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .instrument_search_wrapper {
    text-align: right;

    .search_icons {
      width: 22px;
      height: 22px;
      color: #676C74;
      cursor: pointer;
    }

    .instrument_search_content {
      display: flex;
      align-items: center;
      justify-content: end;

      .search_cancel {
        color: #676C74;
        font-size: 16px;
        margin-left: 10px;
        cursor: pointer;
        font-weight: 500;
      }
    }
  }
}

.list_container {
  /deep/ .el-skeleton, .skeleton_container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 24px;
  }
}
</style>
