<template>
  <div class="container-box consulting-box padding_bottom">
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/elabweb' }">手术复盘 </el-breadcrumb-item>
      <el-breadcrumb-item>{{ elabItem.caseName }}</el-breadcrumb-item>
    </bm-breadcrumb>
    <!-- 标题 -->
    <div class="elab_title">{{ elabItem.caseName }}</div>
    <!-- 医院 -->
    <div v-if="elabItem.authorList.length == 0 && elabItem.hospital" class="hospital">{{
      elabItem.hospital ? elabItem.hospital.name : '' }}</div>

    <!-- 主体 -->
    <div class="videoBox">
      <div class="leftBox">

        <!-- 视频 -->
        <div class="videoPlay">
          <div class="video_box_play" :class="{ 'hideBtn': hideFSBtn }">
            <ali-player v-if="elabItem.videoList.length > 0" ref="player" :auto-play-delay='0' :autoplay='true'
              :is-live='false' :playauth='playauth2.authinfo' :use-h5-prism='true' :plan-time="planTime" :vid='elabVid'
              control-bar-visibility='hover' height='100%' show-bar-time='3000' width='100%' @play="changeAudioStatus" @pause="changePause">
            </ali-player>
            <img v-else :src="elabItem.image" />
          </div>
          <!-- 去登录弹窗 -->
          <div class='player_limit_mask' v-if="goLoginPopShow">
            <div class='login_user_box'>
              <p class='title'>登录后观看完整版</p>
              <div class='login_button cursor' @click='jumpSigninFun'>登录</div>
            </div>
          </div>
        </div>
        <!-- 按钮 -->
        <div class="videoBtn">
          <div class="iconBtn">
            <div class="iconItem">
              <img src="~assets/images/elabweb/view.png" alt="" />
              <span>{{ elabItem.views }}</span>
            </div>
            <div class="iconItem" @click="diggOnClick()">
              <img v-if="elabItem.diggStatus == 'T'" src="~assets/images/elabweb/like4.png" alt="" />
              <img v-else src="~assets/images/elabweb/like3.png" alt="" />
              <span :class="elabItem.diggStatus == 'T' ? 'cur' : ''">点赞</span>
            </div>
            <div class="iconItem" @click="collectOnClick">
              <img v-if="elabItem.collectStatus == 'T'" src="~assets/images/elabweb/collect2.png" alt="" />
              <img v-else src="~assets/images/elabweb/collect.png" alt="" />
              <span :class="elabItem.collectStatus == 'T' ? 'cur' : ''">收藏</span>
            </div>
            <div class="iconItem" @click='shareFlag = !shareFlag'>
              <img src="~assets/images/elabweb/share.png" alt="" />
              <span>分享</span>
            </div>
          </div>
          <Audio v-if="elabItem.audioUrl" :audio-url="elabItem.audioUrl" :audio-duration="elabItem.audioDuration" />
        </div>

        <!-- 区位 -->
        <div v-if="elabVideo.length > 1" class="videoListBox">
          <div v-for="(item, index) in elabVideo" :key="item.id" class="oneBox"
            :style="`background-image: url(${item.image});`" @click="playClick(index, item.vid)">
            <img v-if="index == videoIndex" src="~assets/images/elabweb/meeting_fileds_white.gif" />
            <img v-else src="~assets/images/elabweb/play.png" />
            <span>{{ item.title }}</span>
          </div>
        </div>

        <!-- 标签 -->
        <p class="tagList">
          <i v-for="item in elabItem.classificationList" :key="item.id" @click="navGetTo(item.parentId, item.id)">{{ item.name
          }}</i>
        </p>

        <!-- 术者 -->
        <div class="practitioner">
          <div v-for="sitem in categoryAuthorList" :key="sitem.id" class="PractitionerItem"
            @click="navGetToCenter(sitem.id)">
            <div class="p_title" :class="sitem.categoryName ? '' : 'opacity0'">{{ sitem.categoryName }}</div>
            <div class="p_box">
              <div class="author">
                <img v-if="sitem.avatarAddress" :src="sitem.avatarAddress" />
                <img v-else src="~assets/images/elabweb/noImg.png" />
              </div>
              <div class="p_con">
                <div class="p_name">
                  <p>{{ sitem.realName }}</p>
                  <span>{{ sitem.title }}</span>
                </div>
                <p class="dec">{{ sitem.company }} <span v-if="sitem.company && sitem.department">|</span> {{
                  sitem.department }}</p>
              </div>
              <div class="r_icon">
                <img src="~assets/images/elabweb/right_icon.png" />
              </div>
            </div>
          </div>
        </div>

        <!--  病例信息 -->
        <!-- 一级菜单 -->
        <ul class="selectList">
          <li v-for="(item, index) in list" :key="item" :class="tabIndex == index ? 'tabCur' : ''"
            @click="tabSelect(index)">{{ item }}
          </li>
        </ul>

        <!-- 内容切换 -->
        <div class="tabBox">
          <div v-if="tabIndex == 0">
            <div v-if="caseMessageList.length > 0">
              <div class="twoClass">
                <div v-for="(item, index) in caseMessageList" :key="item.id" :class="twoIndex == index ? 'classCur' : ''"
                  class="twoItem" @click="gettScreen(index)">
                  {{ item.title }}
                </div>
              </div>
              <div class="myhtml" v-html="caseMessageList[twoIndex].content ? caseMessageList[twoIndex].content : ''">
              </div>
            </div>
            <div v-else>
              <Comment :comment-list='commentListSet' :see-more-flag='seeMoreFlag'
                @commentLikeHandler='commentLikeHandler' @submitCommentsHandler='submitCommentsHandler'
                @submitReplyCommentsHandler='submitReplyCommentsHandler'
                @viewMoreDataSetHandler='viewMoreDataSetHandler' />
            </div>
          </div>
          <div v-if="tabIndex == 1">
            <Comment :comment-list='commentListSet' :see-more-flag='seeMoreFlag' @commentLikeHandler='commentLikeHandler'
              @submitCommentsHandler='submitCommentsHandler' @submitReplyCommentsHandler='submitReplyCommentsHandler'
              @viewMoreDataSetHandler='viewMoreDataSetHandler' />
          </div>
        </div>

      </div>

      <div class="right_box">
        <div v-if="thumbtacks.length > 0" class="pivotBox">
          <img src="~assets/images/elabweb/picot.png" alt="" />
          <span>手术要点</span>
        </div>

        <div v-if="thumbtacks.length > 0" class="over">
          <ul class="pivotUl">
            <li v-for="(item, i) in thumbtacks" :key="item.id"
              :class="curTime >= formatTime2(elabItem.beginTime, item.time) ? 'tabCur2' : ''"
              @click="planOnClick(item.time, i)">
              <div class="default_box">
                <div class="list_item_top">
                  <div class="time">{{ formatTime(elabItem.beginTime, item.time) }}</div>
                  <Audio v-if="item.audioUrl" :hide-play="true" :audio-url="item.audioUrl"
                    :audio-duration="item.audioDuration" :play-flag="curTime >= formatTime2(elabItem.beginTime, item.time)
                      && thumbtacks[i + 1]
                      && curTime < formatTime2(elabItem.beginTime, thumbtacks[i + 1].time)
                      && tudingIndex === i
                      && aliPlayFlag"/>
                </div>
                <p class="txt">{{ item.name ? item.name : '' }}</p>
                <div v-if="item.productList.length > 0" class="tag_List_item">
                  <div v-for="sitem in item.productList" :key="sitem.id">
                    <div class="item_tag" @click.stop="getToInstrument(sitem.id)">
                      <img  src="~assets/images/elabweb/icon1.png" />
                      <span> {{ sitem.name ? sitem.name : '' }}</span>
                      <img src="~assets/images/elabweb/right3.png" alt="" />
                    </div>
                  </div>
                </div>
                <div v-if="item.tagList&&item.tagList.length > 0" class="addTag">
                  <div class="myTagList" v-for="sitem in item.tagList" :key="sitem.id" @click.stop="JumpPage(sitem)">
                    <img v-if="sitem.module === 'search'" src="~assets/images/elabweb/icon3.png" />
                    <img v-else src="~assets/images/elabweb/icon2.png" />
                    <p :style="sitem.module === 'search'?'color:#0581CE;':'color:#E76333;'">{{ sitem.name }}</p>
                    <img v-if="sitem.module === 'search'" src="~assets/images/elabweb/right4.png" />
                    <img v-else src="~assets/images/elabweb/right5.png" />
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <!-- 专栏 -->
        <div v-if="ElabCaseSpecialList.length > 0" class="special_list">
          <h1>专栏</h1>
          <div v-for="item in ElabCaseSpecialList" :key="item.id" class="specialItem" @click="goToSpecial(item.id)">
            <div class="specialImg"
              :style="`background-image: url('${item.cover ? item.cover : 'https://medtion-image.medtion.com/uploads/1/image/public/202309/20230925132857_v9j3958whb.png'}');`">
            </div>
            <div class="sprcialTxt sprcialTxt2">
              <h3>{{ item.title }}</h3>
              <div class="viewNum">
                <p><i style="margin-right:8px">{{ item.cases }}视频</i><i>{{ item.views }}观看</i></p>
                <div class="goSpecial">进入专栏</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="special_list">
          <h1>相关推荐</h1>
          <div v-for="item in ElabCaseList" :key="item.id" class="specialItem" @click="goToCaseDetail(item.id)">
            <div class="specialImg" :style="`background-image: url('${item.image}');`">
              <!-- <span v-if="item.elabSurgicalClassification.code === '全息手术'">全景手术</span> -->
            </div>
            <div class="sprcialTxt">
              <h3 class="line-2">{{ item.caseName }}</h3>
              <div class="viewNum">
                <span v-if="item.elabSurgicalClassification.code === '全息手术'">{{ item.hospital ? item.hospital.name : ''
                }}</span>
                <p v-else>
                  <i v-for="(sitem) in item.authorList" :key="sitem.id" @click.stop="navGetToCenter(sitem.id)">{{
                    sitem.realName + ' ' }}</i>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <client-only>
      <SharePopup :share-data='shareData' :share-flag='shareFlag' @editFlag='closeSharePopupHandler' />
    </client-only>
  </div>
</template>

<script>
import { saveBrowsingHistory } from '@/api/browsing-history'
import Aliplayer from '@/components/AliPlayer/AliPlayer'
import SharePopup from '@/components/page/detail/SharePopup/SharePopup'
import Comment from '@/components/Elab/Comment/Comment'
import brandAdJump from '../../../assets/helpers/brandAdJump'
import { getElabCaseDetail, saveWatchRecord, getChildElabCommentList, getPlayAuth, getElabCommentPage, elabApicommentDiggs, comment, elabCaseDigg, collectElabCase, getElabCaseSpecialList, getRecommendCaseList } from '@/api/elabweb'
import Audio from '@/components/Elab/Audio'
export default {
  name: 'ElabDetail',
  components: {
    'ali-player': Aliplayer,
    Comment,
    SharePopup,
    Audio
  },
  async asyncData ({ app, params, error, store, query, req }) {
    const [request1] = await Promise.all([
      app.$axios.$request(
        getElabCaseDetail({
          caseId: query.id,
          loginUserId: store.state.auth.user.id,
        })
      ),
    ])

    // 获取记录的播放视频
    const watchRecord = request1.result.watchRecord;
    let vIndex = 0;
    if (watchRecord && watchRecord.videoId) {
      request1.result.videoList.forEach((item, index) => {
        if (watchRecord.videoId === item.id) {
          vIndex = index;
        }
      })
    }
    // 获取播放凭证
    let request2 = null;
    if (request1.result.videoList.length > 0) {
      const myVid = request1.result.videoList[Number(vIndex)].vid;
      request2 = await app.$axios.$request(
        getPlayAuth({
          vid: myVid
        })
      )
    }

    // 获取评论列表
    const request3 = await app.$axios.$request(
      getElabCommentPage({
        caseId: query.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 5
      })
    )

    let seeMoreFlag2 = null;
    if (request3.page&&request3.page.totalCount <= 5) {
      seeMoreFlag2 = null
    } else {
      seeMoreFlag2 = false
    }

    // 获取相关专栏
    const request4 = await app.$axios.$request(
      getElabCaseSpecialList({
        caseId: query.id,
        loginUserId: store.state.auth.user.id,
      })
    )

    // 获取相关推荐
    const request5 = await app.$axios.$request(
      getRecommendCaseList({
        caseId: query.id,
        limit: 5
      })
    )

    // 分享信息
    const obj = {};
    obj.realName = request1.result.caseName;
    obj.title = request1.result.shareDesc;
    obj.avatarAddress = request1.result.shareImage;

    // 如果病例信息为空则不显示病例信息
    const tablist = ['评论'];
    const tabIndex2 = 0;
    if (request1.result.caseMessageList.length > 0) {
      tablist.unshift('病例信息')
    }

    // 术者相关信息
    const arr = [];
    if (request1.result.categoryAuthorList.length > 0) {
      request1.result.categoryAuthorList.forEach(item => {
        item.authorList.forEach(sitem => {
          arr.push({
            ...sitem,
            categoryName: item.categoryName,
            categoryId: item.categoryId
          })
        })
      })
    }

    return {
      elabItem: request1.result,
      elabVideo: request1.result.videoList,
      thumbtacks: request1.result.thumbtacks,
      myWatchRecord: watchRecord,
      shareData: obj,
      elabVid: request1.result.videoList.length > 0 ? request1.result.videoList[vIndex].vid : null,
      caseMessageList: request1.result.caseMessageList,
      playauth2: request2 ? request2.result : '',
      commentListSet: request3.list,
      seeMoreFlag: seeMoreFlag2, // 查看更多loading开关
      commentData: request3,
      ElabCaseSpecialList: request4.list,
      ElabCaseList: request5.list,
      list: tablist,
      tabIndex: tabIndex2,
      categoryAuthorList: arr,
      planTime: watchRecord ? watchRecord.watchedDuration : 0,
      videoIndex: vIndex,
    }
  },
  data () {
    return {
      twoIndex: 0,// 默认选中第一个
      SideBarHeight: 0,
      // commentData: {},    // 评论数据
      // commentListSet: [], // 评论列表
      pageSize: 5,// 评论数量
      // videoIndex: 0,// 默认播放第几个视频

      shareFlag: false,  //  分享弹框
      curTime: 0, // 当前播放时间
      setInter: null, // 计时器

      scroll: 0,                 // 滚动条距离顶部位置
      aliPlayFlag: false, // 阿里播放器是否播放 用户点击播放时触发图钉音频
      tudingIndex: -1, // 当前播放的图钉索引

      cutTackLogin: false, // 切换图钉是否登录
      conditionDuration: '', // 播放多少秒后需要登录
      goLoginPopShow: false,
      hideFSBtn: false,
    }
  },
  head () {
    return {
      title: this.elabItem.caseName,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.elabItem.caseName || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,${this.elabItem.caseName}`
        }
      ],
      commentId: null,// 父评论id
      oldCommentId: null,// 老的父评论id
      commentNum: 0,
    }
  },
  watch: {
    /**
     * 监听距离顶部滚动条位置
     */
    scroll: {
      handler: 'showTop'
    },
    "$store.state.elab.commentId" (newVal, oldVal) {
      this.commentId = newVal
      this.oldCommentId = oldVal
    },
    "$store.state.elab.commentNum" (newVal, oldVal) {
      // 表示点的同一个评论
      let num = 0;// 获取当前点击评论有几个
      this.commentListSet.forEach((item, index) => {
        if (item.id === this.commentId) {
          num = item.childComments.length
        }
      })
      // 点击展开 且评论小于三个的情况下请求
      if (num <= 3) {
        this.onClickExpand(newVal)
      }
    },
    // 条件登录播放
    curTime (time) {
      const token = this.$store.state.auth.token
      if (this.conditionDuration && time >= this.conditionDuration && !token) {
        this.$refs.player.pause()
        this.goLoginPopShow = true
      }
    }
  },
  watchQuery: ['id'],
  mounted () {
    // 条件登录
    this.conditionLogin()

    /**
     * 加入滚动事件 用于监听直播或者录播 滚动到指定位置出现小播放器
     */
    window.addEventListener('scroll', this.getScroll)

    // 判断是否存在视频
    if (this.elabVid) {
      this.getPlayTimeHandler();// 默认调用计时器获取播放时长
    }

    // 添加浏览历史
    if (this.$store.state.auth.token) {
      this.$axios.$request(saveBrowsingHistory({
        loginUserId: this.$store.state.auth.user.id,
        contentSource: 14,
        //  浏览内容 id（浏览内容类型的id）
        contentId: this.$route.query.id,
        playDuration: 0
      }))
    }

    // document.addEventListener("visibilitychange", this.pageVisiable);
  },
  beforeDestroy () {
    const token = this.$store.state.auth.token
    if (token) {
      this.setSaveWatchRecord();// 记录播放时长
    }
    // document.removeEventListener('visibilitychange', this.pageVisiable)
    console.log('页面销毁了');
  },
  methods: {
    // 条件登录
    conditionLogin () {
      const { condition, conditionDuration } = this.elabItem
      const token = this.$store.state.auth.token
      /**
       * @condition
       *  '1' 无条件播放
       *  '2' 必须登录才能播放
       *  '3' 播放 conditionDuration 秒需登录
       *  '4' 切信号和图钉时登录
       * @conditionDuration
       *  播放多少秒需要登录 condition 为 3 时使用当前字段(单位秒)
      */
      switch (condition) {
        case '2':
          !token ? this.jumpSigninFun() : ""
          break;
        case '3':
          this.cutTackLogin = true
          this.conditionDuration = conditionDuration
          break;
        case '4':
          this.cutTackLogin = true
          break;
      }
      this.hideFSBtn = !token && conditionDuration ? true : false
    },

    /**
     * 距离顶部位置
     */
    getScroll () {
      this.scroll = document.documentElement.scrollTop || document.body.scrollTop
    },

    /**
     * 滚动条超过播放器 就显示小播放器
     * @param newValue
     * @param oldValue
     */
    showTop (newValue, oldValue) {
      const token = this.$store.state.auth.token
      if (!this.conditionDuration || token) {
        const myPlay = document.querySelector('.videoPlay') // 手术复盘播放器
        const bottomBtnBox = document.querySelector('.videoBtn') // 手术复盘底部按钮
        const meetingVideoTop = myPlay.getBoundingClientRect().top // 距离顶部位置
        const meetingVideo = document.querySelector('.video_box_play') // 手术复盘播放器
        const meetingVideoHeight = myPlay.clientHeight // 手术复盘播放器高度
        const bottomBtnBoxHeight = bottomBtnBox.clientHeight
        const navHeight = 60 // 导航高度
        /**
         * 会议播放器的高度 加上具体顶部  在加底部按钮的高度 减去 导航高度
         */
        if ((meetingVideoHeight + meetingVideoTop + bottomBtnBoxHeight - navHeight) < 0) {
          meetingVideo.setAttribute('id', 'video_aliplayer_smallscreen')
        } else {
          meetingVideo.setAttribute('id', '')
        }
      }
    },

    // 选项卡切换
    tabSelect (index) {
      if (this.list.length === 1) {
        this.tabIndex = 0;
      } else {
        this.tabIndex = index;
      }
    },

    // 手术要点 tag 点击跳转
    JumpPage(sitem) {
      if(sitem.module === 'search'){
        window.open(`/search?keywords=${sitem.extras}`, '_blank')
      }else{
        brandAdJump(this, sitem.module, sitem.extras)
      }
    },

    // 点击的展开数据
    onClickExpand (num) {
      if (num === 2) {// 代表展开子评论
        this.$axios.$request(getChildElabCommentList({
          parentCommentId: this.commentId,
          loginUserId: this.$store.state.auth.user.id,
          pageNo: 1,
          pageSize: 99999
        })).then(res => {
          this.commentListSet.forEach((item, index) => {
            if (item.id === this.commentId) {
              this.commentListSet[index].childComments = res.list
            }
          })
        })
      }

    },


    // 病例切换
    gettScreen (index) {
      this.twoIndex = index;
    },

    /**
     * 前往专栏
     */
    goToSpecial (id) {
      this.$router.push({
        path: '/elabweb/columnlist/' + id,
      })
    },

    // 前往器械
    getToInstrument (id) {
      const { href } = this.$router.resolve({
        path: '/bms/classify/-/product-details/' + id,
      })
      window.open(href, '_blank')
    },

    // 前往术者中心
    navGetToCenter (id) {
      const { href } = this.$router.resolve({
        path: '/user-center',
        query: {
          profileUserId: id
        }
      })
      window.open(href, '_blank')
    },

    /**
    * 前往详情
    */
    goToCaseDetail (id) {
      this.$router.push({
        path: '/elabweb/detail',
        query: {
          id
        }
      })
    },

    /**
     * 前往分类
     */
    navGetTo (id, childId) {
      this.$router.push({ path: '/elabweb/classify/' + id, query: { id: childId } })
    },

    // // 监听页面隐藏
    // pageVisiable(e) {
    //   if (e.target.visibilityState === "visible") {
    //     console.log("显示");
    //   } else {
    //     console.log("隐藏");
    //     const formData = new FormData()
    //     formData.append('watchedDuration', Math.floor(this.curTime))
    //     formData.append('videoId', this.elabVideo[this.videoIndex].id)
    //     navigator.sendBeacon("/wapi/elab/saveWatchRecord.do", formData)
    //     // this.setSaveWatchRecord();// 记录播放时长
    //   }
    // },


    /**
     * 点击切换视频机位
     */
    playClick (index, myvid) {
      const token = this.$store.state.auth.token
      if (!token && this.cutTackLogin) {
        this.jumpSigninFun()
      } else {
        this.elabVid = myvid;
        this.videoIndex = index;

        // 切换前获取当前播放时间
        const aa = this.$refs.player.getCurrentTime();

        this.$axios
          .$request(
            getPlayAuth({
              vid: myvid,
            })
          )
          .then((res) => {
            this.playauth2 = res.result;
            this.planTime = aa;
          })
      }
    },

    /**
     * 点击手术要点跳转视频节点
     * @param {*} time
     */
    planOnClick (time, i) {
      this.tudingIndex = i
      const token = this.$store.state.auth.token
      if (!token && this.cutTackLogin) {
        this.jumpSigninFun()
      } else {
        const num = time - this.elabItem.beginTime
        this.$refs.player.seek(num / 1000);
      }
    },

    /**
     * 获取视频是否播放及播放时间
     */
    getPlayTimeHandler () {
      let time = 0;
      this.setInter = setInterval(() => {
        const bb = this.$refs.player.getCurrentTime();
        if (this.curTime !== bb) {
          this.curTime = bb;
          this.thumbtacks.findIndex((item, index) => {
            if (this.curTime < this.formatTime2(this.elabItem.beginTime, item.time)) {
              this.tudingIndex = index - 1
              return true
            }
            return false
          })
          time++;
        }
        // console.log('this.curTime', this.curTime);
        if (time === 60) {
          this.OnRecord();
          time = 0;
        }
      }, 1000)

      this.$once('hook:beforeDestroy', () => {
        // $once一次性监听事件，触发后即关闭
        // hook 自定义钩子函数

        clearInterval(this.setInter)
        this.setInter = null
      })
    },

    /**
     * 记录播放时长
     */
    setSaveWatchRecord () {
      this.$axios
        .$request(
          saveWatchRecord({
            watchedDuration: Math.floor(this.curTime),
            videoId: this.elabVideo[this.videoIndex].id,
          })
        )
        .then((res) => {

        })
    },

    /**
     * 视频埋点记录时长
     */
    OnRecord () {
      let str = '';
      this.categoryAuthorList.forEach((item) => {
        str += item.realName + ','
      })
      this.$analysys.view_video_elab(60, this.elabVideo[this.videoIndex].title, this.elabItem.id + '', this.elabVideo[this.videoIndex].vid + '', this.elabItem.caseName, 60, str, this.$store.state.auth.unionid)
    },

    /**
     * 病例点赞
     */
    diggOnClick () {
      this.$axios
        .$request(
          elabCaseDigg({
            loginUserId: this.$store.state.auth.user.id,
            caseId: this.$route.query.id,
          })
        )
        .then((res) => {
          this.$analysys.like('', '', '', '点赞视频', '', '', '', '', [], [], '手术复盘视频', '点赞视频', '', String(this.$route.query.id))
          this.elabItem.diggStatus = res.result.diggStatus
        })
    },

    /**
     * 病例收藏
     */
    collectOnClick () {
      this.$axios
        .$request(
          collectElabCase({
            loginUserId: this.$store.state.auth.user.id,
            caseId: this.$route.query.id,
          })
        )
        .then((res) => {
          this.elabItem.collectStatus = res.result.collectStatus;
          this.$analysys.add_to_favorite('', '', '', [], '', '', '', [], '', '', String(this.$route.query.id), '', '手术复盘视频', this.elabItem.caseName)
        })
    },

    closeSharePopupHandler (flag) {
      this.shareFlag = flag
    },

    /**
     *
     *  跳转登录
     */
    jumpSigninFun () {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
    },

    /**
     *  获取评论列表
     */
    getInfoCommentsPageHandler (params = {
      pageSize: 5
    }) {
      this.$axios.$request(getElabCommentPage({
        caseId: this.$route.query.id,
        userId: this.$store.state.auth.user.id,
        pageNo: 1,
        pageSize: params.pageSize
      })).then(response => {
        if (response && response.code === 1) {
          this.commentListSet = response.list

          this.commentData = response
          params.callback && params.callback(true)

          const count = this.commentData.page.totalCount

          if (count <= this.pageSize) {
            this.seeMoreFlag = null
          } else {
            this.seeMoreFlag = false
          }
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/20 13:45
     *  查看更多
     */
    viewMoreDataSetHandler () {
      const count = this.commentData.page.totalCount
      this.seeMoreFlag = true
      if (this.pageSize + 5 < (count + 5)) {
        this.pageSize = this.pageSize + 5
        this.getInfoCommentsPageHandler({ pageSize: this.pageSize })
      }
    },

    /**
     *
     *  评论点赞
     */
    commentLikeHandler (id) {
      this.$axios.$request(elabApicommentDiggs({
        commentId: id,
        userId: this.$store.state.auth.user.id
      })).then(response => {
        if (response && response.code === 1) {
          if (response.result.diggStatus === 'T') {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'T'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs + 1
              }
            })
          } else {
            this.commentListSet.forEach((item, index) => {
              if (item.id === id) {
                this.commentListSet[index].diggStatus = 'F'
                this.commentListSet[index].diggs = this.commentListSet[index].diggs - 1
              }
            })
          }
        }
      })
    },

    /**
     *
     *  提交评论
     */
    submitCommentsHandler ({ commentInfo, parentId = null }, callback) {
      if (commentInfo === '' || !commentInfo) {
        this.$toast('请输入评论内容')
        return
      }

      this.$axios.$request(comment({
        content: commentInfo,
        userId: this.$store.state.auth.user.id,
        caseId: this.$route.query.id,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast('评论成功')
          this.getInfoCommentsPageHandler({ callback, pageSize: this.pageSize })
        }
      })

    },

    /**
     *  @author:Rick  @date:2022/10/20 10:02
     *  提交回复评论
     */
    submitReplyCommentsHandler ({ info, parentId, isType }, callback) {
      this.$axios.$request(comment({
        content: info,
        userId: this.$store.state.auth.user.id,
        caseId: this.$route.query.id,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast('评论成功')
          // this.getInfoCommentsPageHandler({ callback, pageSize: this.pageSize })
          if (isType === 2) {
            this.getInfoCommentsPageHandler({ callback, pageSize: this.pageSize })
          } else {
            this.getCommentsListChild({ callback, id: parentId })
          }
          // eslint-disable-next-line node/no-callback-literal
        }
      })
    },


    getCommentsListChild (params) {
      this.$axios.$request(getChildElabCommentList({
        parentCommentId: params.id,
        loginUserId: this.$store.state.auth.user.id,
        pageNo: 1,
        pageSize: 99999
      })).then(res => {
        this.commentListSet.forEach((item, index) => {
          if (item.id === params.id) {
            this.commentListSet[index].childComments = res.list;

            params.callback && params.callback(true)
          }
        })
      })
    },

    formatTime (startTime, endTime) {
      // 计算两个时间戳之间的差值（单位：秒）
      const timeDifference = (endTime - startTime) / 1000;
      // 计算小时、分钟和秒
      const hours = Math.floor(timeDifference / 3600);
      const minutes = Math.floor((timeDifference % 3600) / 60);
      const seconds = timeDifference % 60;

      // 使用三元运算符确保单数小时、分钟和秒数前面有零
      const formattedHours = hours < 10 ? `0${hours}` : hours;
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
      const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

      // 将格式化后的时间以字符串形式返回
      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    },

    // 获取多少秒
    formatTime2 (startTime, endTime) {
      // 计算两个时间戳之间的差值（单位：秒）
      const timeDifference = (endTime - startTime) / 1000;
      // 将格式化后的时间以字符串形式返回
      return timeDifference;
    },

    /**
     * 监听点击暂停
     */
    changePause(){
      this.aliPlayFlag = false;// 设置图钉音频展厅
    },

    /**
     * 播放器的播放事件
     */
    changeAudioStatus () {
      this.aliPlayFlag = true
    }
  },
}
</script>

<style lang="less" scoped>
.content /deep/ .home_carousel {
  padding: 32px 0 0;
}

.el-breadcrumb {
  margin-top: 24px;
}

.tabBox /deep/ .comment-modular-box /deep/ .comments_tips_box_user {
  display: none !important;
}

// 隐藏 全屏按钮
.hideBtn {
  .prism-player /deep/ .prism-fullscreen-btn {
    display: none;
  }
}



// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }

// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }

.cur {
  color: #0581CE !important;
}

.elab_title {
  color: #333;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 19.9px;
  margin-top: 32px;
}

.hospital {
  color: #999EA4;
  text-align: left;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  margin-top: 16px;
}

.tagList {
  margin-top: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  i {
    font-style: normal;
    border-radius: 2px;
    background: rgba(112, 138, 162, 0.08);
    display: block;
    padding: 4px;
    box-sizing: border-box;
    color: #708AA2;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-right: 19px;
    cursor: pointer;
    margin-bottom: 16px;
  }
}

.videoBox {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;

  .leftBox {
    width: 840px;
    padding-bottom: 200px;

    .videoPlay {
      width: 100%;
      height: 470px;
      display: block;
      position: relative;

      .video_box_play {
        width: 100%;
        height: 470px;
        display: block;
      }

      img {
        width: 100%;
        height: 100%;
        display: block;
        object-fit: cover;
      }

      .player_limit_mask {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        text-align: center;
        z-index: 1000;

        .login_user_box {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);

          .tips {
            color: #FFF;
            margin-bottom: 70px;
            font-size: 18px;
            line-height: 1.5;
          }

          .parment_btn {
            width: 313px;
            height: 44px;
            border-radius: 4px;
            background: #0581CE;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            color: #FFF;
            line-height: 1.5;
            cursor: pointer;

            .price {
              margin-right: 12px;
            }
          }


          .title {
            color: white;
            font-size: 26px;

            .mixin_desktop({
              font-size: 16px;
            })
        }

        .login_button {
          display: inline-block;
          background: red;
          color: white;
          font-size: 14px;
          line-height: 16px;
          text-align: center;
          border-radius: 30px;
          padding: 5px 18px;
          margin-top: 20px;
        }
      }

    }
  }

  .videoBtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #F4F6F8;
    height: 41px;
    padding: 0 29px 0 16px;
    box-sizing: border-box;
    width: 100%;
    border-radius: 0 0 8px 8px;

    .iconBtn {
      //width: 252px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .iconItem {
        margin-right: 24px;
        display: flex;
        align-items: center;
        cursor: pointer;

        img {
          display: block;
          width: 24px;
          height: 24px;
          flex-shrink: 0;
          margin-right: 4px;
        }

        span {
          color: #676C74;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 12px;
        }
      }
    }

    .iconItem2 {
      display: flex;
      align-items: center;

      img {
        height: 20px;
        width: 20px;
        margin-right: 4px;
        flex-shrink: 0;
      }

      span {
        color: #C7C7C7;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }
    }

  }

  .videoListBox {
    display: flex;
    margin-top: 23px;
    flex-wrap: wrap;

    .oneBox {
      width: 198px;
      height: 111px;
      background-size: cover;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      margin-right: 16px;
      position: relative;
      overflow: hidden;
      margin-bottom: 16px;
      cursor: pointer;

      &::after {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.40) 100%);
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        content: '';
        border-radius: 8px;
      }

      img {
        width: 20px;
        height: 20px;
        display: block;
        flex-shrink: 0;
        margin-right: 8px;
        z-index: 2;
      }

      span {
        color: #FFF;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        z-index: 2;
      }
    }
  }

  .oneBox:nth-child(4n) {
    margin-right: 0 !important;
  }

  .practitioner {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 20px;

    .PractitionerItem {
      margin-bottom: 24px;

      .p_title {
        background-color: #E0F0FF;
        height: 26px;
        display: inline-block;
        color: #0581CE;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        border-radius: 8px 0 0 0;
        position: relative;
        padding-left: 12px;

        &::after {
          content: '';
          position: absolute;
          right: -16px;
          top: 0;
          width: 16px;
          height: 26px;
          background-image: url('~assets/images/elabweb/jiao.png');
          background-size: cover;
          background-repeat: no-repeat;
        }
      }

      .p_box {
        width: 408px;
        height: 73px;
        border: 1px solid #E0F0FF;
        border-radius: 0 8px 8px 8px;
        padding: 16px 22px 16px 16px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        cursor: pointer;
        background-color: rgba(224, 240, 255, 0.2);

        .author {
          border-radius: 50%;
          width: 40px;
          height: 40px;
          flex-shrink: 0;
          overflow: hidden;
          margin-right: 12px;

          img {
            display: block;
            width: 100%;
            height: 100%;
            flex-shrink: 0;
          }
        }

        .p_con {
          width: 290px;
          margin-right: 12px;

          .p_name {
            display: flex;
            align-items: center;

            p {
              color: #333;
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 21px;
              margin-right: 9px;
              display: inline-block;
            }

            span {
              color: #304B64;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 17px;
              padding: 4px;
              background-color: #EEF3F8;
              border-radius: 4px;
            }
          }

          .dec {
            color: #708AA2;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 1em;
            margin-top: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .r_icon {
          width: 16px;
          height: 16px;
          flex-shrink: 0;

          img {
            display: block;
            width: 100%;
            height: 100%;
            flex-shrink: 0;
          }
        }

      }
    }
  }

  .selectList {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #E6E6E6;

    li {
      color: #333;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: 19.9px;
      position: relative;
      padding: 12px 0px;
      margin-right: 45px;
      cursor: pointer;
    }

    .tabCur {
      color: #0581CE !important;

      &::after {
        content: '';
        width: 16px;
        height: 4px;
        border-radius: 2px;
        background-color: #0581CE;
        position: absolute;
        bottom: 0;
        left: calc(50% - 8px);
        transition: transform 0.3s;
      }
    }
  }


  .twoClass {
    display: flex;
    margin: 0 24px 24px 0;
    flex-wrap: wrap;

    .twoItem {
      display: flex;
      height: 38px;
      padding: 0 16px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: #F4F6F8;
      color: #333;
      text-align: center;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      margin-right: 24px;
      margin-bottom: 5px;
      position: relative;
      cursor: pointer;
      border: 1px solid #F4F6F8;

      &:hover {
        border: 1px solid #0581CE;
        color: #0581CE;
      }
    }

    .classCur {
      border: 1px solid #0581CE !important;
      color: #0581CE !important;
    }
  }


  .tabBox {
    width: 100%;
    padding-top: 24px;

    .myhtml /deep/ img {
      width: 100%;
      height: auto;
    }

    .myhtml /deep/ iframe {
      width: 100%;
      height: 474px;
    }

    .myhtml /deep/ video {
      width: 100%;
    }
  }
}

.right_box {
  width: 339px;

  .pivotBox {
    width: 100%;
    height: 38px;
    border-radius: 4px;
    background: #F4F6F8;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 8px;
    box-sizing: border-box;

    img {
      display: block;
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      margin-right: 8px;
    }

    span {
      color: #304B64;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }

  .over {
    max-height: 533px;
    overflow-y: scroll;
    margin-bottom: 24px;
  }

  .over::-webkit-scrollbar {
    display: none;
  }

  .over:hover::-webkit-scrollbar {
    display: block;
  }

  .pivotUl {
    width: 100%;

    .tabCur2 {

      .time,
      .txt {
        color: #0581CE !important;
      }

      &::before {
        background-color: #0581CE !important;
      }
    }

    li:last-child {
      &::after {
        display: none !important;
      }
    }

    li {
      width: 100%;
      position: relative;
      padding-left: 12px;
      box-sizing: border-box;
      margin-bottom: 16px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 14px;
        width: 7px;
        height: 7px;
        border-radius: 50%;
        background-color: #999EA4;
      }

      &::after {
        content: '';
        position: absolute;
        left: 3px;
        top: 21px;
        width: 0px;
        border-left: 1px dashed #999EA4;
        height: calc(100% + 16px);
      }

      .default_box {
        padding: 8px;
        box-sizing: border-box;
        width: 100%;
        border-radius: 4px;
        background: #F8F8F8;
        cursor: pointer;

        .list_item_top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .time {
            color: #999EA4;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 1em;
            margin-bottom: 8px;
          }
        }

        .txt {
          color: #333;
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }

        .tag_List_item {
          .item_tag {
            border-radius: 4px;
            background: #EFEFEF;
            height: 29px;
            padding: 0 8px;
            margin-top: 8px;
            display: inline-flex;
            align-items: center;

            img:last-child {
              width: 16px;
              height: 16px;
              margin-left: 4px;
              display: block;
              flex-shrink: 0;
            }
            img:first-child {
              width: 16px;
              height: 16px;
              margin-right: 4px;
              display: block;
              flex-shrink: 0;
            }

            span {
              color: #62A83F;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 29px;
            }
          }
        }

        .addTag{
          display: flex;
          flex-wrap: wrap;
        }
        .myTagList{
            border-radius: 4px;
            background: #EFEFEF;
            height: 29px;
            padding: 0 8px;
            margin-top: 8px;
            display: inline-flex;
            align-items: center;
            margin-right: 8px;
            p{
              color: #62A83F;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 29px;
            }
            img:last-child {
              width: 16px;
              height: 16px;
              margin-left: 4px;
              display: block;
              flex-shrink: 0;
            }
            img:first-child {
              width: 16px;
              height: 16px;
              margin-right: 4px;
              display: block;
              flex-shrink: 0;
            }
        }
      }
    }
  }

  .special_list {
    margin-bottom: 24px;

    h1 {
      color: #333;
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }

    .specialItem:hover {
      .sprcialTxt {
        h3 {
          color: #0581CE !important;
        }
      }
    }

    .specialItem {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      border-radius: 8px;
      background: #F8F8F8;
      padding: 7px 6px 7px 8px;

      .specialImg {
        width: 90px;
        height: 52px;
        flex-shrink: 0;
        background-size: cover;
        background-repeat: no-repeat;
        margin-right: 16px;
        position: relative;
        border-radius: 4px;
        overflow: hidden;


        span {
          color: #FFF;
          font-size: 12px;
          font-style: normal;
          font-weight: 600;
          line-height: 15px;
          position: absolute;
          top: 4px;
          right: 4px;
          z-index: 2;
          display: block;
          background-color: #427083;
          padding: 4px;
          border-radius: 4px;
        }
      }

      .sprcialTxt2 h3 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height: auto !important;
      }

      .sprcialTxt {
        width: calc(100% - 106px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        h3 {
          color: #333;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
          margin-bottom: 2px;
        }

        .line-2 {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .viewNum {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;

          span {
            color: #999EA4;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
          }

          p {
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;

            i {
              color: #708AA2;
              text-align: center;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 12px;
            }
          }

          .goSpecial {
            padding: 8px;
            border-radius: 4px;
            background: #0581CE;
            color: #FFF;
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 12px;
          }
        }
      }
    }
  }
}
}

.opacity0 {
  opacity: 0;
}

.padding_bottom {
  padding-bottom: 50px;
}

#video_aliplayer_smallscreen {
  position: fixed;
  right: 2vw;
  bottom: 20px !important;
  width: 400px;
  height: 225px;
  z-index: 5000;
  animation: an 0.5s;
  border-radius: 10px;
  overflow: hidden;

  .prism-player {
    border-radius: 10px;
    overflow: hidden;
  }

  .resize_box {
    z-index: 999999;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: red;

    // .resize_li {}
  }
}
</style>
