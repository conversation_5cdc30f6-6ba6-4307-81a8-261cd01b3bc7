<template>
  <div class="content padding_bottom">
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/elabweb' }">手术复盘 </el-breadcrumb-item>
      <el-breadcrumb-item>全景手术</el-breadcrumb-item>
    </bm-breadcrumb>

    <!-- 广告位 -->
    <swiper-list v-if="swiperList2.length > 0" :list="swiperList2" :type="2" code="webAPi_elab_case"></swiper-list>

    <!-- 选项 -->
    <div class="wid1080">
      <div class="class_box">
        <div v-for="(item, index) in list" :key="item.name" :class="item.isOpen ? '' : 'height30'" class="row_box">
          <span>{{ item.name }}</span>
          <ul>
            <li v-for="(sitem, sindex) in item.arr" :key="sitem.id" :class="item.tabIndex == sindex ? 'cur' : ''"
                @click="tabSelect(index, sindex)">{{ sitem.name
              }}</li>
            <li v-if="item.isShow && item.arr.length > 4" class="openBtn" @click="onClickOpen(index, item.isOpen)">
              <i>{{ item.isOpen ? '收起' : '展开' }}</i>
              <img v-if="item.isOpen" style="transform: rotate(180deg);" src="~assets/images/elabweb/select.png" />
              <img v-else src="~assets/images/elabweb/select.png" />
            </li>
          </ul>
        </div>
      </div>

      <div class="box_list">
        <video-list :list="caseList" :type="1"></video-list>
      </div>

      <!-- 分页 -->
      <el-pagination align="center" :hide-on-single-page="true" background layout="prev, pager, next" :current-page="pageNo"
                     small :pager-count="7" :page-count="pageCount" @current-change="onPageClick"></el-pagination>
    </div>


  </div>
</template>


<script>
import SwiperList from '~/components/Elab/SwiperList'
import VideoList from '~/components/Elab/VideoList'
import { getElabAaList, getClassification, getElabCaseAllHospitals, getHologramCaseList } from '@/api/elabweb'

export default {
  name: 'HologramPage',
  components: {
    SwiperList,
    VideoList
  },
  async asyncData({ app, params, error, store, query, req }) {
    const list = [{ name: '疾病', id: 4, isShow: false, isOpen: false, tabIndex: 0, arr: [] }, { name: '部位', id: 5, isShow: false, isOpen: false, tabIndex: 0, arr: [] }, { name: '疗法', id: 6, isShow: false, isOpen: false, tabIndex: 0, arr: [] }, { name: '医院', id: '', isShow: false, isOpen: false, tabIndex: 0, arr: [] }];
    const [request1, request2, request3, request4, request5] = await Promise.all([
      app.$axios.$request(
        getElabAaList({
          adCode: 'webAPi_elab_case'
        })
      ),
      app.$axios.$request(
        getClassification({
          parentId: 4
        })
      ),
      app.$axios.$request(
        getClassification({
          parentId: 5
        })
      ),
      app.$axios.$request(
        getClassification({
          parentId: 6
        })
      ),
      app.$axios.$request(
        getElabCaseAllHospitals({})
      ),
    ])

    list[0].arr = request2.result.children;
    list[1].arr = request3.result.children;
    list[2].arr = request4.result.children;


    // 加一个属性
    list.map((item, index) => {
      return item.arr.unshift({ name: '全部', id: '' })
    })

    // 处理医院展开收起
    let len2 = 0;
    request5.list.unshift({ name: '全部', id: '' })
    request5.list.forEach((item, index) => {
      len2 += (item.name.length * 16) + 32;
    });
    if (len2 > 1054) {
      list[3].arr = request5.list;
      list[3].isShow = true;// 显示展开
    }

    const request6 = await app.$axios.$request(
      getHologramCaseList({
        pageNo: 1,
        pageSize: 16
      })
    )

    return {
      swiperList2: request1.list,
      caseList: request6.list,
      pageCount: request6.page.totalPage,
      hospital: request5.list,
      list
    }
  },
  data() {
    return {
      pageNo: 1,
      diseaseIndex: 0,// 疾病索引
      localIndex: 0,// 部位索引
      lherapyIndex: 0,// 疗法索引
      hospitalIndex: 0,// 医院索引
    }
  },
  head() {
    return {
      title: '全景手术-手术复盘-脑医汇'
    }
  },
  mounted() {

  },
  methods: {
    // 点击页
    onPageClick(item) {
      this.pageNo = item;
      this.getCaseList()
    },


    getCaseList() {
      this.$axios
        .$request(getHologramCaseList({
          diseaseId: this.list[0].arr[this.diseaseIndex].id,
          positionId: this.list[1].arr[this.localIndex].id,
          therapyId: this.list[2].arr[this.lherapyIndex].id,
          hospitalId: this.list[3].arr[this.hospitalIndex].id,
          pageNo: this.pageNo,
          pageSize: 16
        })).then(res => {
        this.caseList = res.list;
        this.pageCount = res.page.totalPage;
      })
    },

    // 点击标签选择 筛选
    tabSelect(index, sindex) {
      this.list[index].tabIndex = sindex;
      this.diseaseIndex = this.list[0].tabIndex;
      this.localIndex = this.list[1].tabIndex;
      this.lherapyIndex = this.list[2].tabIndex;
      this.hospitalIndex = this.list[3].tabIndex;
      this.getCaseList();
    },

    // 点击展开
    onClickOpen(index, isopen) {
      this.list[index].isOpen = !this.list[index].isOpen;
    }
  }
}
</script>

<style lang="less" scoped>
.content {
  width: 1128px;
  margin: 0 auto;
}

.content /deep/ .home_carousel {
  padding: 32px 0 0;
}

.el-breadcrumb {
  margin-top: 24px;
}

// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }

// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }

.box_list {
  margin-top: 8px;
}

.box_list /deep/ .listBox {
  width: 100%;

  .box_item {
    margin-top: 24px;
    margin-right: 40px;
  }

  .box_item:nth-child(4n) {
    margin-right: 0 !important;
  }
}

.class_box {
  padding-top: 10px;

  .row_box {
    display: flex;
    margin-top: 22px;
    // align-items: center;

    span {
      color: #999EA4;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 30px;
      width: 74px;
    }

    ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: calc(100% - 74px);
      position: relative;

      li {
        color: #333;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;
        margin-right: 32px;
        cursor: pointer;

        &:hover {
          color: #0581CE;
        }
      }

      .openBtn {
        display: flex;
        align-items: center;
        position: absolute;
        margin-right: 0 !important;
        right: 0;
        bottom: 0;
        padding: 0 10px;
        background-color: #fff;

        i {
          color: #0581CE;
          text-align: center;
          font-family: PingFang SC;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 30px;
        }

        img {
          width: 16px;
          height: 16px;
          display: block;
          margin-left: 4px;
        }
      }

      .cur {
        color: #0581CE;
        font-weight: 600;
      }
    }
  }
}

.padding_bottom {
  padding-bottom: 50px;
}

.height30 {
  height: 30px;
  overflow: hidden;
}
.wid1080{
  width: 1080px;
  margin: 0 auto;
}
</style>
