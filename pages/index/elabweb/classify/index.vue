<template>
  <div class="content padding_bottom">
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/elabweb/' }">手术复盘 </el-breadcrumb-item>
      <el-breadcrumb-item>{{ getName() }}</el-breadcrumb-item>
    </bm-breadcrumb>

    <!-- 选项 -->
    <div class="class_box">
      <div v-for="(item, index) in list" :key="index" class="row_box">
        <span>{{ item.name }}</span>
        <ul>
          <li v-for="(sitem, sindex) in item.arr" :key="sitem.id" :class="item.tabIndex == sindex ? 'cur' : ''"
            @click="tabSelect(index, sindex)">{{ sitem.name }}</li>
        </ul>
      </div>
    </div>

    <div class="box_list">
      <video-list :list="caseList" :type="13"></video-list>
    </div>

    <!-- 分页 -->
    <el-pagination layout="prev, pager, next" :current-page="pageNo" :pager-count="7" :hide-on-single-page="true"
      background align="center" small :page-count="pageCount" @current-change="onPageClick"></el-pagination>

  </div>
</template>


<script>
import VideoList from '~/components/Elab/VideoList'
import { getClassificationInfo, getRecordingCaseList } from '@/api/elabweb'

export default {
  name: 'HologramPage',
  components: {
    VideoList
  },
  async asyncData({ app, params, error, store, query, req, route }) {
    const list = [{ name: '类型', id: '', tabIndex: 0, arr: [{ name: '全景手术', id: 1 }, { name: '录播手术', id: 2 }, { name: '手术精讲', id: 3 }] }, { name: '亚专业', id: '', tabIndex: 0, arr: [] }];
    const [request2] = await Promise.all([
      app.$axios.$request(
        getClassificationInfo({})
      ),
    ])
    const list3 = [];
    let tabindex = 0;
    const arr = [{ name: '全部', id: '' }];
    const list4 = [];
    request2.list.forEach((item, index) => {
      // 用于首页亚专业下一页进入默认选中
      if(route.query.type){
        list[0].tabIndex = Number(route.query.type) - 1;
      }
      // 存在亚专业id
      if (query.specialityId && item.id === Number(query.specialityId)) {
        tabindex = index;
        list[1].tabIndex = index;
      }

      // 赋值亚专业数组
      list[1].arr.push({
        name: item.name,
        id: item.id
      })
      // 提前用于类型切换
      list4.push({
        name: item.name,
        id: item.id
      })
      // 设置三级亚专业遍历 设置默认值
      list3.push({ tab: index,  arr: [] })
      item.children.forEach((sitem, sindex) => {
        // 默认添加全部属性
        const newarr = arr.concat(sitem.children);
        // 亚专业点击进入
        if ((index === tabindex && item.id === Number(query.specialityId))) {
          list.push({
            name: sitem.name,
            id: sitem.id,
            tabIndex: 0,
            arr: newarr
          })
        }
        // 录播手术 手术精讲 查看更多进入
        if (query.type && !query.specialityId && index === tabindex) {
          list.push({
            name: sitem.name,
            id: sitem.id,
            tabIndex: 0,
            arr: newarr
          })
        }
        list3[index].arr.push({
          name: sitem.name,
          id: sitem.id,
          tabIndex: 0,
          arr: newarr
        })
      })
    })

    // 如果是全息手术则不要其他亚专业，只需神经介入
    if (list[0].tabIndex === 0) {
      list[1].arr.splice(1, list[1].arr.length);
    }
    const request6 = await app.$axios.$request(
      getRecordingCaseList({
        surgicalClassificationId: list[0].arr[list[0].tabIndex].id,
        firstClassificationId: list[1].arr[0].id,
        pageNo: 1,
        pageSize: 16
      })
    )

    return {
      caseList: request6.list,
      pageCount: request6.page.totalPage,
      myList: list3,
      myTabIndex: 0,
      speList: list4,
      list
    }
  },
  data() {
    return {
      pageNo: 1,
      typeIndex: 0,// 类型索引
      diseaseIndex: 0,// 疾病索引
      localIndex: 0,// 部位索引
      lherapyIndex: 0,// 疗法索引
      checkId: '',// 选中id字符串
    }
  },
  head() {
    return {
      title: this.getName() + '-手术复盘-脑医汇'
    }
  },
  mounted() {

  },
  methods: {
    // 面包屑导航名字
    getName() {
      switch (this.$route.query.type) {
        case '2':
          return '录播手术'
        case '3':
          return '手术精讲'
        default:
          return '全景手术'
      }
    },

    // 分页发生变化
    onPageClick(item) {
      this.pageNo = item;
      this.getCaseList()
    },

    getCaseList(index) {
      this.$axios
        .$request(getRecordingCaseList({
          surgicalClassificationId: this.list[0].arr[this.list[0].tabIndex].id,
          firstClassificationId: this.list[1].arr[this.list[1].tabIndex].id,
          thirdClassificationIdStr: this.checkId,
          pageNo: this.pageNo,
          pageSize: 16
        })).then(res => {
          this.caseList = res.list;
          this.pageCount = res.page.totalPage;
        })
    },

    // 点击标签选择 筛选
    tabSelect(index, sindex) {
      this.list[index].tabIndex = sindex;
      this.pageNo = 1;
      if (index === 1) {
        this.list.splice(2, this.list.length);
        const newArr = this.list.concat(this.myList[sindex].arr);
        this.list = newArr;
      } else if (index === 0) {
        if (sindex === 0) {
          const arr = [];
          arr.push(this.speList[0]);
          this.list[1].tabIndex = 0;
          this.list[1].arr = arr;
          this.list.splice(2, this.list.length);
          const newArr = this.list.concat(this.myList[sindex].arr);
          this.list = newArr;
        } else {
          const arr = this.speList;
          this.list[1].arr = arr;
        }
      }
      let str = '';
      this.list.forEach((mitem, mindex) => {
        if (mindex > 1) {
          str += mitem.arr[mitem.tabIndex].id
        }
      })

      this.checkId = str;
      this.$analysys.btn_click(this.list[index].name + '-' +this.list[index].arr[sindex].name ,'手术复盘'+ this.getName())
      this.getCaseList(index);
    }
  }
}
</script>

<style lang="less" scoped>
.content {
  width: 1128px;
  margin: 0 auto;
}

.content /deep/ .home_carousel {
  padding: 32px 0 0;
}

.el-breadcrumb {
  margin-top: 24px;
}

// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }
// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }
.box_list {
  margin-top: 8px;
  margin-bottom: 32px;
}

.box_list /deep/ .listBox {
  width: 100%;

  .box_item {
    margin-top: 24px;
    margin-right: 56px;
  }

  .box_item:nth-child(4n) {
    margin-right: 0 !important;
  }
}

.class_box {

  .row_box {
    display: flex;
    margin-top: 29px;
    // align-items: center;

    span {
      color: #999EA4;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
      width: 74px;
    }

    ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: calc(100% - 74px);

      li {
        color: #333;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px;
        margin-right: 32px;
        cursor: pointer;

        &:hover {
          color: #0581CE;
        }
      }

      .cur {
        color: #0581CE;
      }
    }
  }
}

.padding_bottom {
  padding-bottom: 50px;
}
</style>
