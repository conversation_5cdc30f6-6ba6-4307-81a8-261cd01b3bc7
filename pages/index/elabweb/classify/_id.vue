<template>
  <div class="bgwhite padding_bottom">
    <div class="content">
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/elabweb' }">手术复盘 </el-breadcrumb-item>
        <el-breadcrumb-item>{{ getName() }}</el-breadcrumb-item>
      </bm-breadcrumb>

      <!-- 内容 -->
      <div class="tabBox">
        <!-- 一级级菜单 -->
        <ul class="selectList">
          <li
v-for="(item, index) in tabList" :key="item.id" :class="tabIndex == index ? 'tabCur' : ''"
            @click="tabSelect(index, item.id,item.name)">{{
              item.name }}
          </li>
        </ul>
        <!-- 二级菜单 -->
        <div class="twoClass">
          <div
v-for="(item, index) in classifyList" :key="item.id" :class="item.checked ? 'classCur' : ''" class="twoItem"
            @click="gettScreen(index)">
            {{ item.name }}
          </div>
        </div>
        <!-- 列表 -->
        <div class="storeVideoBox">
          <video-list :list="caseList" :type="13"></video-list>
        </div>

        <!-- 分页 -->
        <el-pagination
align="center" :hide-on-single-page="true" background layout="prev, pager, next"
          :current-page="pageNo" :pager-count="7" small :page-count="pageCount" @current-change="onPageClick"></el-pagination>
      </div>


    </div>
  </div>
</template>

<script>
import { getClassification, getElabSurgicalClassification, getElabCaseList,getChildClassificationByParentId } from '@/api/elabweb'
import VideoList from '~/components/Elab/VideoList'
export default {
  name: 'TwoClassify',
  components: {
    VideoList
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [request1, request2] = await Promise.all([
      app.$axios.$request(
        getChildClassificationByParentId({
          classificationId: params.id
        })
      ),
      app.$axios.$request(
        getElabSurgicalClassification({
        })
      ),
    ])


    // 处理二级分类多选
    const mylist = [];
    request2.list.forEach((item) => {
      if(query.specialCode === 'naozhongliu'){
        if(item.code !== '全息手术'){
          mylist.push({
            ...item,
            checked: false
          })
        }
      }else{
        mylist.push({
          ...item,
          checked: false
        })
      }
    })

    // 获取数据列表
    // 判断有分类id就用id，没有就用第一个
    const classId = query.id ? query.id : request1.list[0].id;
    const request3 = await app.$axios.$request(
      getElabCaseList({
        classificationId: classId,
        pageNo: 1,
        pageSize: 16
      })
    )

    return {
      tabList: request1.list,
      classifyList: mylist,
      caseList: request3.list,
      pageCount: request3.page.totalPage
    }
  },
  data() {
    return {
      tabIndex: 0,// 默认选中第一个
      tab:0,// 一级分类第一个
      pageNo: 1,
      classId: '',// 分类id
      specialityList:{
        jieru:'神经介入',
        naozhongliu:'脑肿瘤'
      }
    }
  },
  head() {
    return {
      title: this.getName()+'-手术复盘-脑医汇'
    }
  },
  mounted() {
    const id = this.$route.query.id;
    // 处理通过热门标签进入默认选中标签
    if (id) {
      this.tabList.forEach((item, index) => {
        if (item.id === parseInt(id)) {
          this.tabIndex = index
        }
      });
    }
  },
  methods: {
    // 面包屑导航名字
    getName() {
      const code = this.$route.query.specialCode;
      let name = '';
      if(code){
        name = this.specialityList[code] + '-'
      }
      switch (this.$route.params.id) {
        case '4':
          return name + '疾病'
        case '5':
          return name + '部位'
        case '6':
          return name + '疗法'
        case '41':
          return name + '部位'
        case '53':
          return name + '疾病'
        case '67':
          return name + '入路'
        default:
          return '其他'
      }
    },
    // 选项卡切换
    tabSelect(index, id,name) {
      this.$analysys.btn_click(this.getName() + '-' + name, '手术复盘-' + this.getName())
      this.tabIndex = index;
      this.pageNo = 1;
      this.getCaseList();
    },

    // 请求tab切换手术列表
    gettScreen(index) {
      if(!this.classifyList[index].checked){
        this.$analysys.btn_click(this.getName() + '-' + this.classifyList[index].name, '手术复盘-' + this.getName())
      }
      this.classifyList[index].checked = !this.classifyList[index].checked;
      this.pageNo = 1;
      const arr = this.classifyList.filter(item => item.checked).map(item => item.id);
      this.classId = arr.join(',');
      this.getCaseList()
    },

    // 分页发生变化
    onPageClick(item){
      this.pageNo = item;
      this.getCaseList()
    },


    // 请求接口
    getCaseList() {
      const id = this.tabList[this.tabIndex].id;
      this.$axios
        .$request(
          getElabCaseList({
            classificationId: id,
            surgicalClassificationIdsStr:this.classId,
            pageNo: this.pageNo,
            pageSize: 16
          })
        )
        .then((res) => {
          this.caseList = res.list;
          this.pageCount = res.page.totalPage;
        })
    }
  }
}
</script>


<style lang="less" scoped>
.el-footer {
  margin-top: 0 !important;
}

// 修改父组底部上边距50样式

.bgwhite {
  background: #EEF2F3 !important;
  padding-bottom: 100px;
}

.content {
  width: 1128px;
  margin: 0 auto;
  padding-top: 24px;
}

.el-breadcrumb {
  margin-top: 0px;
}

// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }
// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }
.tabBox /deep/ .active {
  background-color: #0581CE !important;
  color: #fff !important;
}

.tabBox {
  background-color: #fff;
  border-radius: 8px;
  margin-top: 24px;
  padding: 24px;

  .specialityBox{
    display: flex;
    align-items: center;
    .specialityItem{
      color: #333;
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      cursor: pointer;
      margin-right: 45px;
    }
    .curSpeciality{
      color: #0581CE !important;
      position: relative;
      &::after{
        content: "";
        position: absolute;
        left: calc(50% - 14px);
        bottom: -21px;
        width: 0;
        height: 0;
        border-left: 14px solid transparent;
        border-right: 14px solid transparent;
        border-bottom: 14px solid #F4F6F8;
      }
    }
  }

  .selectList {
    display: flex;
    flex-wrap: wrap;
    background-color: #F4F6F8;
    border-radius: 4px;
    padding: 10px 0;

    li {
      color: #333;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: 19.9px;
      position: relative;
      padding:10px 16px;
      cursor: pointer;
    }

    .tabCur {
      color: #0581CE !important;

      &::after {
        // content: '';
        width: 16px;
        height: 4px;
        border-radius: 2px;
        background-color: #0581CE;
        position: absolute;
        bottom: 0;
        left: calc(50% - 8px);
        transition: transform 0.3s;
      }
    }
  }

  .twoClass {
    display: flex;
    margin: 24px 0px 0;

    .twoItem {
      display: flex;
      width: 126px;
      height: 38px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: #F4F6F8;
      color: #333;
      text-align: center;
      font-size: 14px;
      font-weight: 600;
      line-height: 16px;
      margin-right: 24px;
      position: relative;
      cursor: pointer;
      border: 1px solid #F4F6F8;

      &:hover {
        border: 1px solid #0581CE;
        color: #0581CE;
      }
    }

    .classCur {
      border: 1px solid #0581CE !important;
      color: #0581CE !important;
    }
  }

  .storeVideoBox {
    padding: 0 0px;
    margin-bottom: 40px;
  }

  .storeVideoBox /deep/ .listBox {
    width: 100%;

    .box_item {
      margin-top: 24px;
      margin-right: 40px;
    }

    .box_item:nth-child(4n) {
      margin-right: 0 !important;
    }
  }

}
.padding_bottom{
  padding-bottom: 50px;
}</style>
