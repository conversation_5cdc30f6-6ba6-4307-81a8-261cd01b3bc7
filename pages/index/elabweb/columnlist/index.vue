<template>
  <div class="bgwhite padding_bottom">
    <div class="content">
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/elabweb' }">手术复盘 </el-breadcrumb-item>
        <el-breadcrumb-item>手术专栏</el-breadcrumb-item>
      </bm-breadcrumb>

      <!-- 内容 -->
      <div class="columnBox">
          <ul class="selectList">
            <li v-for="(item, index) in tabList" :key="item.id" :class="tabIndex == index ? 'tabCur' : ''"
              @click="tabSelect(index, item.subspecialityId, item.name)">{{
                item.name }}
            </li>
          </ul>
          <column-list-two :list="specialList"></column-list-two>

          <!-- 分页 -->
          <el-pagination align="center" :hide-on-single-page="true" background layout="prev, pager, next"
            :current-page="pageNo" :pager-count="7" small :page-count="pageCount"
            @current-change="onPageClick"></el-pagination>
        </div>
      </div>


  </div>
</template>

<script>
import ColumnListTwo from '~/components/Elab/ColumnListTwo'
import { getElabSpecialList,getClassificationInfo } from '@/api/elabweb'
export default {
  name: 'ColumnList',
  components: {
    ColumnListTwo
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [request1, request3] = await Promise.all([
      app.$axios.$request(
        getElabSpecialList({
          loginUserId: store.state.auth.user.id,
          pageNo: 1,
          pageSize: 16
        })
      ),
      app.$axios.$request(
        getClassificationInfo({})
      ),
    ])

    const arr = [{name:'全部',id:''}]
    request3.list.forEach((item) => {
      arr.push({
        name:item.name,
        id:item.id,
        subspecialityId:item.subspecialityId
      })
    })
    return {
      specialList: request1.list,
      pageCount: request1.page.totalPage,
      tabList:arr
    }
  },
  data() {
    return {
      pageNo: 1,
      tabIndex:0
    }
  },
  head() {
    return {
      title: '手术专栏-手术复盘-脑医汇'
    }
  },
  methods: {
    // 分页发生变化
    onPageClick(item) {
      this.pageNo = item;
      this.getCaseList()
    },

    // 选项卡切换
    tabSelect(index, id, name) {
      this.tabIndex = index;
      this.pageNo = 1;
      this.$analysys.btn_click('手术复盘-'+name, document.title)
      this.getCaseList(id);
    },

    // 请求接口
    getCaseList(id) {
      this.$axios
        .$request(
          getElabSpecialList({
            loginUserId: this.$store.state.auth.user.id,
            subSpecialityId:id,
            pageNo: this.pageNo,
            pageSize: 16
          })
        )
        .then((res) => {
          this.specialList = res.list;
          this.pageCount = res.page.totalPage;
        })
    },

    /**
    * 跳转病例详情
    * @param {*} id
    */
    navgetTo(Id) {
      this.$router.push({
        path: '/elabweb/detail',
        query: {
          id: Id
        }
      })
    },


    /**
     * 进入专栏详情
     */
    goColumnDetail(id) {
      this.$router.push({
        path: '/elabweb/columnlist/' + id
      })
    }
  }
}
</script>

<style lang="less" scoped>
.bgwhite {
  background: #EEF2F3 !important;
  padding-bottom: 100px;
}

.content {
  width: 1128px;
  margin: 0 auto;
  padding-top: 24px;

  .columnBox{
    background-color: #fff;
    border-radius: 8px;
    margin-top: 27px;
    padding-bottom: 40px;
  }
  .selectList {
    display: flex;
    flex-wrap: wrap;
    border-radius: 4px;
    border-bottom: 1px solid rgba(230, 230, 230, 1);

    li {
      color: #333;
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      line-height: 19.9px;
      position: relative;
      padding:24px 16px 12px;
      cursor: pointer;
    }

    .tabCur {
      color: #0581CE !important;

      &::after {
        content: '';
        width: 16px;
        height: 4px;
        border-radius: 2px;
        background-color: #0581CE;
        position: absolute;
        bottom: 0;
        left: calc(50% - 8px);
        transition: transform 0.3s;
      }
    }
  }


}

.el-breadcrumb {
  margin-top: 0px;
}

// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }
// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }
.tabBox /deep/ .active {
  background-color: #0581CE !important;
  color: #fff !important;
}

.padding_bottom {
  padding-bottom: 50px;
}

.curse {
  cursor: pointer;
}</style>
