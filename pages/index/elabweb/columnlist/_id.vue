<template>
  <div class="column_list_detail_container">
    <div
      class="bg_wrapper"
      :style="{background:`url(${DetailData.cover})`, backgroundSize: 'cover',backgroundRepeat:'no-repeat'}">
      <div class="bg_wrapper_fixed"/>
      <div v-if="DetailData.rotateText" class="rotateText">
        <div class="rotateText_container">
          <p class="box">{{ toHtml(DetailData.rotateText) }}</p>
        </div>
      </div>
      <Container :styles="{position:'relative',padding:'0 36px',boxSizing:'border-box'}">
        <div class="column_detail_head">
          <div class="image">
            <zip-img
              :width="250"
              :height="140"
              :src="DetailData.cover "
              fill
            />
          </div>
          <div class="detail_content">
            <p class="title">
              {{ DetailData.title }}
            </p>
            <div class="detail_info">
              <div class="tips">
                {{ $tool.formatterNum(DetailData.cases) }}病例
                {{ $tool.formatterNum(DetailData.views) }}观看
              </div>
              <div class="button_wrapper">
                <SubscribeDefault
                  :is-follow="DetailData.subscribeStatus === 'T'"
                  @follow="followHandler"
                />
                <ShareDefaultBtn/>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>

    <Container :styles="{padding:'0 36px 32px',boxSizing:'border-box'}">
      <div v-if="DetailData.children.length>0" class="column_tabs">
        <div class="three_tabs_container">
          <div
            :class="specialId === $route.params.id? 'active_three_tab_item' : '' "
            class="three_tab_item"
            @click="changeTabHandler($route.params.id)"
          >
            全部
          </div>
          <div
            v-for="(item,index) in DetailData.children"
            :key="item.id"
            class="three_tab_item"
            :class="specialId === item.id ? 'active_three_tab_item' : '' "
            @click="changeTabHandler(item.id)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>

      <RollingLoad
        :loading="loading"
        :empty="columnData.list.length===0"
        :no-more="currentPageNo >= columnData.page.totalPage"
        empty-height="50vh"
        @hit-bottom="hitBottomChangeHandler"
      >
        <template #loading>
          <InfoSkeleton :limit="16" :loading="loading"/>
        </template>

        <template>
          <div class="list_wrapper" :style="{marginTop:'24px'}">
            <template v-for="item in columnData.list">
              <ElabDefaultItem
                :key="item.id"
                :infoId="item.id"
                :image="item.image"
                :title="item.caseName"
                :hospital="item.hospital ? item.hospital.name : ''"
                :is-label="item.elabSurgicalClassification.code === '全息手术'"
                :author-list="item.authorList"
                :classificationList="item.classificationList"
              />
            </template>

          </div>
        </template>
      </RollingLoad>
    </Container>
  </div>
</template>

<script>
import RollingLoad from "../../../../opt-components/component/RollingLoad/index.vue";
import {Container} from "../../../../opt-components/template/index.js";
import {InfoSkeleton} from "../../../../opt-components/ui/skeleton/index.js";
import {ZipImg} from "../../../../opt-components/component/index.js";
import {ShareDefaultBtn} from "../../../../opt-components/component/ShareBtn/index.js";
import {SubscribeDefault} from "../../../../opt-components/component/Subscribe/index.js";
import {getElabSpecialDetail, subscribeElabSpecial, getElabCaseList} from "../../../../api/elabweb.js";
import {ElabDefaultItem} from "../../../../opt-components/data-list/index.js";

export default {
  name: "columnListDetail",
  components: {
    RollingLoad,
    Container,
    InfoSkeleton,
    ZipImg,
    ShareDefaultBtn,
    SubscribeDefault,
    ElabDefaultItem
  },
  async asyncData({app, params, error, store, query, req, $axios}) {
    const [Detail] = await Promise.all([
      app.$axios.$request(
        getElabSpecialDetail({
          loginUserId: store.state.auth.user.id,
          specialId: params.id,
        })
      )])

    return {
      DetailData: Detail.result
    }
  },
  head() {
    return {
      title: this.DetailData.title
    }
  },
  data() {
    return {
      columnData: {
        list: [],
        page: {}
      },
      specialId: this.$route.params.id,
      loading: true,
      currentPageNo: 1,
    }
  },
  mounted() {
    this.getListData({
      pageNo: 1,
      specialId: this.specialId
    })
  },
  methods: {
    /**
     * 去掉字符串中换行
     */
    toHtml(str) {
      return str.replace(/[\r\n]/g, "")
    },
    async getListData({pageNo, pageUp, specialId} = {pageNo: 1}) {
      const data = await this.$axios.$request(getElabCaseList({
        specialId,
        pageNo,
        pageSize: 12
      }))

      if (data.code === 1) {

        if (pageUp) {
          this.$set(this.columnData, "list", [...this.columnData.list, ...data.list])
          this.$set(this.columnData, "page", data.page)
        } else {
          this.$set(this.columnData, "list", data.list)
          this.$set(this.columnData, "page", data.page)
        }

      }
      this.loading = false;
    },
    changeTabHandler(id) {
      this.specialId = id;
      this.currentPageNo = 1;
      this.loading = true;
      this.$set(this.columnData, "list", [])
      this.$set(this.columnData, "page", {})
      this.$tool.scrollIntoTop()

      this.getListData({
        pageNo: this.currentPageNo,
        specialId: this.specialId
      })

    },
    followHandler() {
      this.$axios.$request(subscribeElabSpecial({
        specialId: this.$route.params.id,
      })).then((res) => {
        this.$analysys.add_follow(this.$store.state.auth.user.id + '', this.$route.params.id + '', this.$store.state.auth.user.realName, this.DetailData.title, this.DetailData.subscribeStatus, '手术专栏');
      })
    },
    hitBottomChangeHandler(flag) {
      if (flag) {
        this.loading = true;
        this.currentPageNo += 1;

        this.getListData({
          pageNo: this.currentPageNo,
          pageUp: true,
          specialId: this.specialId
        })
      }
    },
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
