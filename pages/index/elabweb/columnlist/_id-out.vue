<template>
  <div class="bgwhite padding_bottom">
    <div class="content">
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/elabweb' }">手术复盘 </el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/elabweb/columnlist' }">专栏列表 </el-breadcrumb-item>
        <el-breadcrumb-item>{{ specialItem.title }}</el-breadcrumb-item>
      </bm-breadcrumb>

      <!-- 内容 -->
      <div class="columnBox">
        <div class="column_box">
          <div v-if="specialItem.rotateText" class="top_advert">
            <p ref="box">{{ toHtml(specialItem.rotateText) }}</p>
          </div>
          <div class="pd-lr-24">
            <div class="column_item">
              <div class="flex">
                <img v-if="specialItem.cover" class="imgUrl" :src="specialItem.cover" />
                <img v-else class="imgUrl" src="~assets/images/elabweb/default.png" />
                <div class="right_txt">
                  <h3>{{ specialItem.title }}</h3>
                  <p>{{ specialItem.cases }}病例｜{{ specialItem.views }}观看</p>
                  <div class="flex_right">
                    <div class="columnBtn" @click="followClick()">
                      <img v-if="specialItem.subscribeStatus == 'F'" src="~assets/images/elabweb/guanzhu.png" />
                      <span>{{ specialItem.subscribeStatus == 'F' ? '关注' : '已关注' }}</span>
                    </div>

                    <div class="columnBtn share" @click='shareFlag = !shareFlag'>
                      <img src="~assets/images/elabweb/share3.png" />
                      <span>分享</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <video-list :list="caseList"></video-list>
          </div>
          <!-- 分页 -->
          <el-pagination
align="center" :hide-on-single-page="true" background layout="prev, pager, next"
            :current-page="pageNo" :pager-count="7" small :page-count="pageCount"
            @current-change="onPageClick"></el-pagination>
        </div>

        <!-- 右侧 -->
        <div class="column_right">
          <div class="hot_title">
            <img src="~assets/images/elabweb/hot.png" />
            <span>热门手术</span>
          </div>
          <div v-for="(item, index) in hotList" :key="item.id" class="hot_item" @click="navgetTo(item.id,item.caseName)">
            <span>{{ index + 1 }}</span>
            <p>{{ item.caseName }}</p>
          </div>
        </div>
      </div>

      <client-only>
        <SharePopup :share-data='shareData' :share-flag='shareFlag' @editFlag='closeSharePopupHandler' />
      </client-only>

    </div>
  </div>
</template>

<script>
import VideoList from '~/components/Elab/VideoListStrip'
import SharePopup from '@/components/page/detail/SharePopup/SharePopup'
import { getElabSpecialDetail, getHotElabCaseList, getElabCaseList, subscribeElabSpecial } from '@/api/elabweb'
export default {
  name: 'ColumnList',
  components: {
    VideoList,
    SharePopup
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [request1, request2, request3] = await Promise.all([
      app.$axios.$request(
        getElabSpecialDetail({
          loginUserId: store.state.auth.user.id,
          specialId: params.id,
        })
      ),
      app.$axios.$request(
        getHotElabCaseList({
          limit: 10
        })
      ),
      app.$axios.$request(
        getElabCaseList({
          specialId: params.id,
          pageNo: 1,
          pageSize: 10
        })
      ),
    ])

    // 分享信息
    const obj = {};
    obj.realName = request1.result.title;
    obj.title = request1.result.shareDesc;
    obj.avatarAddress = request1.result.shareImage;

    return {
      specialItem: request1.result,
      hotList: request2.list,
      caseList: request3.list,
      shareData: obj,
      pageCount: request3.page.totalPage
    }
  },
  data() {
    return {
      pageNo: 1,
      timer: null,
      shareFlag: false,  //  分享弹框
    }
  },
  head() {
    return {
      title: this.specialItem.title
    }
  },
  mounted() {
    if (this.specialItem.rotateText) {
      if (this.timer) {
        clearTimeout(this.timer)
      } else {
        this.timer = setTimeout(() => {
          this.startText();
        }, 1500);
      }
    }
  },
  methods: {
    startText() {
      if (this.timer != null) return;
      const width = this.getLenPx(this.toHtml(this.specialItem.rotateText), 14); // 第一个是文本，第二个参数是文本font-size值
      this.$refs.box.style.width = width * 2.2 + "px";
    },
    // 获取字符串占位px
    getLenPx(str, size) {
      const leng = str.replace(/[^\x00-\xFF]/gi, "aa").length;
      return (leng * size) / 2;
    },

    /**
     * 关注
     */
    followClick() {
      const token = this.$store.state.auth.token
      if (!token) {
        this.jumpSigninFun()
      } else {
        this.$axios
          .$request(
            subscribeElabSpecial({
              specialId: this.$route.params.id,
            })
          )
          .then((res) => {
            this.specialItem.subscribeStatus = res.result.subscribeStatus;
            this.$analysys.add_follow(this.$store.state.auth.user.id + '',this.$route.params.id + '',this.$store.state.auth.user.realName,this.specialItem.title,this.specialItem.isFollow?'T':'F','手术专栏');
            if (res.result.subscribeStatus === 'T') {
              this.$toast('关注成功')
            } else {
              this.$toast('取消关注成功')
            }
          })
      }

    },

    /**
     *
     *  跳转登录
     */
    jumpSigninFun() {
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
    },


    closeSharePopupHandler(flag) {
      this.shareFlag = flag
    },

    // 分页发生变化
    onPageClick(item) {
      this.pageNo = item;
      this.getCaseList()
    },

    // 请求接口
    getCaseList() {
      this.$axios
        .$request(
          getElabCaseList({
            loginUserId: this.$store.state.auth.user.id,
            specialId: this.$route.params.id,
            pageNo: this.pageNo,
            pageSize: 10
          })
        )
        .then((res) => {
          this.caseList = res.list;
          this.pageCount = res.page.totalPage;
          this.$tool.scrollIntoTop()
        })
    },



    /**
     * 跳转病例详情
     * @param {*} id
     */
    navgetTo(Id,name) {
      this.$analysys.resource_click({ resource_title: name, resource_type: '热门手术' })
      this.$router.push({
        path: '/elabweb/detail',
        query: {
          id: Id
        }
      })
    },

    /**
     * 去掉字符串中换行
     */
    toHtml(str) {
      return str.replace(/[\r\n]/g, "")
    }
  }
}
</script>

<style lang="less" scoped>
.bgwhite {
  background: #EEF2F3 !important;
  padding-bottom: 100px;
}

.content {
  width: 1128px;
  margin: 0 auto;
  padding-top: 24px;


  .columnBox {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .column_right {
    width: 264px;
    flex-shrink: 0;
    border-radius: 8px;
    background: #FFF;
    margin-top: 24px;
    padding: 16px;
    box-sizing: border-box;
    height: 315px;

    .hot_title {
      display: flex;
      align-items: center;

      img {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        display: block;
        margin-right: 6px;
      }

      span {
        color: #333;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
    }

    .hot_item {
      margin-top: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;

      span {
        color: #C7C7C7;
        font-family: Alibaba PuHuiTi;
        font-size: 18px;
        font-style: normal;
        font-weight: 800;
        line-height: 20.439px;
        display: block;
        margin-right: 11px;
      }

      p {
        overflow: hidden;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20.439px;
        width: calc(100% - 11px - 2em);
      }
    }

    .hot_item:nth-child(1) span {
      color: #E4320D !important;
    }

    .hot_item:nth-child(2) span {
      color: #F27026 !important;
    }

    .hot_item:nth-child(3) span {
      color: #E79E0C !important;
    }
  }

  .column_box {
    // padding: 24px;
    background-color: #fff;
    width: 840px;
    border-radius: 8px;
    margin-top: 24px;
    box-sizing: border-box;
    padding-bottom: 30px;

    .pd-lr-24 {
      padding: 24px;
      box-sizing: border-box;
    }

    .top_advert {
      border-radius: 8px 8px 0px 0px;
      background: rgba(48, 75, 100, 0.80);
      height: 38px;
      overflow: hidden;

      p {
        width: 100%;
        color: #F6F6F6;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 38px;

        padding-left: 100%; // 把文字弄出可见区域
        animation: myMove 30s linear infinite; // 重点，定义动画
        animation-fill-mode: forwards;
      }

      /*文字无缝滚动*/
      @keyframes myMove {
        0% {
          transform: translateX(0);
        }

        100% {
          transform: translateX(-100%);
        }
      }
    }

    .column_item /deep/ .null_box {
      display: none;
    }

    .column_item /deep/ .listBox {
      margin-top: 36px;

      .box_item {
        margin-right: 36px;

        .videoItem {
          width: 240px;
          height: 216px;
        }
      }

      .box_item:nth-child(3n) {
        margin-right: 0 !important;
      }
    }

    .column_item {
      border-bottom: 1px solid #C7C7C7;
      padding-bottom: 40px;

      .imgUrl {
        width: 250px;
        height: 140px;
        border-radius: 4px;
        object-fit: cover;
        display: block;
        flex-shrink: 0;
        margin-right: 16px;
      }

      .right_txt {
        width: calc(100% - 250px - 16px);
        position: relative;

        h3 {
          color: #333;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 19.9px;
          margin-bottom: 23px;
        }

        p {
          color: #708AA2;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          margin-bottom: 30px;
        }

        .flex_right {
          display: flex;
          justify-content: flex-end;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
        }

        .share {
          background: #fff !important;
          border: 1px solid #0581CE;
          box-sizing: border-box;
          margin-left: 16px;

          span {
            color: #0581CE !important;
          }
        }

        .columnBtn {
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          height: 32px;
          padding: 0 12px;
          background: #0581CE;
          cursor: pointer;

          span {
            color: #FFF;
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
          }

          img {
            display: block;
            width: 20px;
            height: 20px;
            display: block;
            margin-right: 8px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

.el-breadcrumb {
  margin-top: 0px;
}

// .el-breadcrumb /deep/ .el-breadcrumb__inner {
//   color: #999EA4 !important;
// }
// .el-breadcrumb /deep/ .el-breadcrumb__separator {
//   color: #999EA4 !important;
// }
.tabBox /deep/ .active {
  background-color: #0581CE !important;
  color: #fff !important;
}
.padding_bottom{
  padding-bottom: 50px;
}
</style>
