<template>
  <div>
    <div class='container-box2'>
      <!-- 广告位 -->
      <div class="bannerBox">
        <swiper-home v-if="swiperList2.length > 0" :list="swiperList2" code="webAPi_elab_ad"></swiper-home>
        <div class="left_menu">
          <div class="menuItem" @click="getTo('/holographic', '全景手术')">
            <img src="~assets/images/elabweb/quanjing.png" />
          </div>
          <div class="menuItem" @click="getTo('/elabweb/columnlist', '手术专栏')">
            <img src="~assets/images/elabweb/zhuanlan.png" />
          </div>
        </div>
      </div>


      <!-- 菜单按钮 -->
      <div class="menu_box">
        <div class="right_tag">
          <div v-for="(item) in tagList" :key="item.id" class="tag_item_box"
               @click="navGetTo(item.name, item.parentId, item.id)">{{
            item.name }}</div>
        </div>
      </div>

      <!-- 神经介入 -->
      <speciality-class :data="specialityData.jieru" @ClickPageAll="myPageAll"></speciality-class>

      <!-- 全息手术  -->
      <div class="moudle_box">
        <home-list :hologram="hologramList" :type="1" :page="pageNo2" :is-more="hologramPage2" @prevPage2="prevPage2"
                   @nextPage2="nextPage2"></home-list>
      </div>

      <!-- 脑肿瘤 -->
      <speciality-class v-if="specialityData.naozhongliu && specialityData.naozhongliu.playauth2"
                        :data="specialityData.naozhongliu" @ClickPageAll="myPageAll"></speciality-class>

      <!-- 录播手术  -->
      <div class="moudle_box">
        <home-list :hologram="recordList" :type="2"></home-list>
      </div>

      <!-- 手术精讲  -->
      <div class="moudle_box">
        <home-list :hologram="speakList" :type="3"></home-list>
      </div>

      <!-- 客服 -->
      <div class="service">
        <div class="icon-serve">
          <img class="img-service" src="~assets/images/elabweb/service.png">
          <p class="text-serve">客服</p>
        </div>
        <img class="img-QRCode" src="~assets/images/elabweb/QRCode.jpg">
      </div>
    </div>
  </div>
</template>

<script>
  import HomeList from '~/components/Elab/HomeList'
  import SwiperHome from '~/components/Elab/SwiperHome'
  import SpecialityClass from '~/components/Elab/SpecialityClass'
  // import VideoListStrip from '~/components/Elab/VideoListStrip'


  import { getElabAaList, getHomePageClassification, getPlayAuth, getHomeShowClassificationList, getPlayVideoList, getElabCaseList } from '@/api/elabweb'
  export default {
    name: 'ElabwebPage',
    components: {
      SwiperHome,
      HomeList,
      SpecialityClass
    },
    async asyncData ({ app, params, error, store, query, req }) {
      const [request1, request6, request7] = await Promise.all([
        app.$axios.$request(
          getElabAaList({
            adCode: 'webAPi_elab_ad'
          })
        ),
        app.$axios.$request(
          getHomePageClassification({
            code: 'jieru'
          })
        ),
        app.$axios.$request(
          getHomePageClassification({
            code: 'naozhongliu'
          })
        ),
      ])
      // 用于亚专业等组件渲染
      const data = {
        jieru: {
          name: 'jieru',
          pageNo: 1,
          speciality: request6.result,
          specialityId: request6.result.id,
          isMore: false,
          playauth2: {},
          elabVid: '',
          playVideoItem: {},
          playVideoList: [],
          caseList: [],
          casePageNo: 1
        },
        naozhongliu: {
          name: 'naozhongliu',
          pageNo: 1,
          speciality: request7.result,
          specialityId: request7.result.id,
          isMore: false,
          playauth2: {},
          elabVid: '',
          playVideoItem: {},
          playVideoList: [],
          caseList: [],
          casePageNo: 1
        }
      };

      return {
        swiperList2: request1.list,
        specialityData: data
      }
    },
    data () {
      return {
        isShow: false,
        pageNo: 1,
        pageNo2: 1,
        iconList: ['https://medtion-image.medtion.com/uploads/1/image/public/202309/20230906155024_f4g15pc8wl.png', 'https://medtion-image.medtion.com/uploads/1/image/public/202309/20230906154855_p4s0jg26jo.png', 'https://medtion-image.medtion.com/uploads/1/image/public/202309/20230906154854_jby84drj4a.png'],
        planTime: 0,
        tagList: [],// 热门标签
        hologramList: [],// 全息手术列表
        hologramPage: 1,// 全息手术当前页码
        recordList: [],// 录播手术列表
        speakList: [], // 手术精讲
        isShow2: false,
      }
    },
    head () {
      return {
        title: '手术复盘'
      }
    },
    computed: {
      hologramPage2 () {
        if (this.pageNo2 < this.hologramPage) {
          return true
        } else {
          return false
        }
      }
    },
    mounted () {
      this.$forceUpdate();
      // console.log('this.data', this.specialityData);
      this.getHotTagList(); // 获取热门标签
      this.getHologramList();// 获取全息手术
      this.getRecordList();// 获取录播手术
      this.getSpeakList();// 获取手术精讲
      this.executeAfterBothRequests();
    },

    methods: {
      doSomethingWithBothData (data1, data2, name) {
        // 执行你的操作，此时两个接口请求都已成功
        const myData = this.specialityData[name];
        if (data1.list.length > 0) {
          this.tumorList = data1.list;
          myData.elabVid = data1.list[0].playCaseVideo.vid;
          myData.playVideoItem = data1.list[0];
          myData.playVideoList = data1.list;
        }

        this.tumorListVideo = data2.list;
        myData.caseList = data2.list;
        myData.casePageNo = data2.page ? data2.page.totalPage : 1;
        myData.isMore = false;
        if (myData.playVideoList.length > 1 && data2.page && data2.page.totalPage > 1) {
          myData.isMore = true
        }
        this.specialityData[name] = myData;
      },

      async executeAfterBothRequests () {
        // 同时调用两个接口请求，并等待它们都完成
        for (const key in this.specialityData) {
          const [data1, data2] = await Promise.all([this.getTumorVideoList(this.specialityData[key].specialityId, key), this.getTumorList(this.specialityData[key].specialityId, key)]);
          // 在这里执行你希望在两个接口请求都成功后执行的操作
          // console.log('-----------------data1',data1);
          if (data1.list.length > 0) {
            this.getPlayAuth2(data1.list[0].playCaseVideo.vid, key)
          }
          this.doSomethingWithBothData(data1, data2, key);
        }
      },

      // 获取脑肿瘤播放视频列表
      getTumorVideoList (id, name) {
        return new Promise((resolve, reject) => {
          this.$axios
            .$request(
              getPlayVideoList({
                classificationId: id
              })
            )
            .then((res) => {
              resolve(res);
            })
            .catch(e => {
              reject(e)
            })
        })
      },

      // 获取脑肿瘤六个视频
      getTumorList (id, name) {
        return new Promise((resolve, reject) => {
          this.$axios
            .$request(
              getElabCaseList({
                classificationId: id,
                pageNo: 1,
                pageSize: 6
              })
            )
            .then((res) => {
              resolve(res);
            }).catch(e => {
            reject(e)
          })
        })
      },


      // 获取热门标签
      getHotTagList () {
        this.$axios
          .$request(
            getHomeShowClassificationList({})
          )
          .then((res) => {
            this.tagList = res.list
          })
      },

      // 获取全息手术列表
      getHologramList () {
        this.$axios
          .$request(
            getElabCaseList({
              surgicalClassificationId: 1,
              pageNo: this.pageNo2,
              pageSize: 5
            })
          )
          .then((res) => {
            this.hologramList = res.list;
            this.hologramPage = res.page.totalPage
          })
      },

      // 获取录播手术
      getRecordList () {
        this.$axios
          .$request(
            getElabCaseList({
              surgicalClassificationId: 2,
              pageNo: 1,
              pageSize: 5
            })
          )
          .then((res) => {
            this.recordList = res.list;
          })
      },

      // 获取手术精讲
      getSpeakList () {
        this.$axios
          .$request(
            getElabCaseList({
              surgicalClassificationId: 3,
              pageNo: 1,
              pageSize: 5
            })
          )
          .then((res) => {
            this.speakList = res.list;
          })
      },

      // 获取当前点击哪个亚专业的下一个
      myPageAll (name, type) {
        if (type === 'next') {
          this.nextPage(name)
        } else {
          this.prevPage(name)
        }
      },

      // 神经介入点击请求上一页
      prevPage (name) {
        // console.log('myDataPrev', this.specialityData);
        const myData = this.specialityData[name];
        this.$axios
          .$request(
            getElabCaseList({
              classificationId: myData.specialityId,
              pageNo: --myData.pageNo,
              pageSize: 6
            })
          )
          .then((res) => {
            myData.caseList = res.list;
            myData.playVideoItem = myData.playVideoList[myData.pageNo - 1];
            myData.elabVid = myData.playVideoList[myData.pageNo - 1].playCaseVideo.vid;
            this.getPlayAuth2(myData.elabVid, myData.name)
            myData.isMore = true;
            this.specialityData[name] = myData;


          })
      },

      // 神经介入点击请求下一页
      nextPage (name) {
        // console.log('myDataNext', this.specialityData);
        const myData = this.specialityData[name];
        const num = myData.playVideoList.length;// 获取视频数量
        const num2 = myData.casePageNo > num ? num : myData.casePageNo;// 获取页数和视频数量作比较 可展示最大页数
        const num3 = num2 > 6 ? 6 : num2;// 获取最大展示页数 且不超过6页
        // console.log('num3', num3);
        this.$axios
          .$request(
            getElabCaseList({
              classificationId: myData.specialityId,
              pageNo: ++myData.pageNo,
              pageSize: 6
            })
          )
          .then((res) => {
            // console.log('myData.pageNo', myData.pageNo);
            myData.caseList = res.list;
            myData.playVideoItem = myData.playVideoList[myData.pageNo - 1];
            myData.elabVid = myData.playVideoList[myData.pageNo - 1].playCaseVideo.vid;
            this.getPlayAuth2(myData.elabVid, myData.name)
            if (myData.pageNo >= num3) {
              myData.isMore = false;
            }
            this.specialityData[name] = myData;
          })
      },

      // 获取视频播放所需凭证
      getPlayAuth2 (id, type) {
        // console.log('111111111111111',id,type);
        this.$axios.$request(
          getPlayAuth({
            vid: id,
          })
        )
          .then((res) => {
            const myData = this.specialityData[type];
            myData.playauth2 = res.result;
            this.specialityData[type] = myData;
            // console.log('this.specialityData',this.specialityData);
          }).catch(e => {
          console.log('e');
        })
      },




      // 全息手术点击请求上一页
      prevPage2 () {
        this.$axios
          .$request(
            getElabCaseList({
              surgicalClassificationId: 1,
              pageNo: --this.pageNo2,
              pageSize: 5
            })
          )
          .then((res) => {
            this.hologramList = res.list;
          })
      },


      // 全息手术点击请求下一页
      nextPage2 () {
        this.$axios
          .$request(
            getElabCaseList({
              surgicalClassificationId: 1,
              pageNo: ++this.pageNo2,
              pageSize: 5
            })
          )
          .then((res) => {
            this.hologramList = res.list;
          })
      },

      // 跳转到 疾病 部位 疗法 列表页
      navGetTo (name, id, childId) {
        if (childId) {
          this.$analysys.btn_click('手术复盘-' + name, document.title)
          this.$router.push({ path: '/elabweb/classify/' + id, query: { id: childId } })
        } else {
          this.$analysys.btn_click(name, document.title)
          this.$router.push({ path: '/elabweb/classify/' + id })
        }
      },

      // 点击跳转全息和手术复盘专栏
      getTo (url, name) {
        this.$analysys.btn_click(name, document.title)
        this.$router.push({ path: url })
      }

    }
  }
</script>

<style scoped lang='less'>
  body {
    background: #fff !important;
  }

  .container-box2 {
    max-width: 1236px;
    margin: 0 auto;
    padding-bottom: 50px;

    .bannerBox {
      display: flex;
      justify-content: space-between;
      margin-top: 32px;

      .left_menu {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;

        .menuItem {
          width: 312px;
          height: 138px;
          border-radius: 4px;
          overflow: hidden;
          cursor: pointer;

          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }

    .menu_box {
      display: flex;
      margin-top: 40px;
      justify-content: space-between;


      .right_tag {
        display: flex;
        align-content: space-between;
        flex-wrap: wrap;

        .tag_item_box {
          padding: 0px 8px;
          border-radius: 4px;
          background: #F4F6F8;
          color: #708AA2;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          height: 28px;
          line-height: 28px;
          display: inline-block;
          margin-right: 16px;
          // margin-bottom: 16px;

          &:hover {
            cursor: pointer;
            color: #304B64;
            background: #DFE4E9;
          }
        }
      }
    }

    // #region 客服
    .service {
      width: 48px;
      height: 48px;
      right: 2vw;
      display: flex;
      position: fixed;
      cursor: pointer;
      border-radius: 6px;
      justify-content: center;
      align-items: center;
      bottom: calc(225px + 20px + 48px + 15px + 15px + 55px) !important;
      background: linear-gradient(180deg, #DEF2FF 0%, #FBFDFF 100%);

      .icon-serve {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;

        .img-service {
          width: 40%;
          margin-bottom: 4px;
        }

        .text-serve {
          color: #0581CE;
          font-size: 12px;
          font-weight: 500;
        }
      }



      .img-QRCode {
        width: 100px;
        position: absolute;
        top: -26px;
        left: -105px;
        display: none;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .icon-serve:hover+.img-QRCode {
        display: block;
      }
    }

    // #endregion

    .aaa {
      max-width: 220px !important;
    }
  }
</style>
