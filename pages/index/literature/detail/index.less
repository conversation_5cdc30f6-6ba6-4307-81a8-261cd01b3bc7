/deep/ .el-breadcrumb__item {
  i {
    margin: 0 !important;
    color: #0581CEFF;
  }

  .el-breadcrumb__inner {
    color: #0581CEFF;
    font-size: 14px;
    font-weight: normal;
  }
}

.MT-tips {
  font-size: 12px;
  font-weight: 400;
  color: #888888;
  line-height: 14px;
}

/deep/ .el-collapse-item__header {
  background: #F3F3F3;
  max-height: 28px;
  padding-left: 11px;

  i {
    color: #708AA2;
  }
}

.screen-flag-true {
  background: #ffffff !important;
  color: #000000 !important;
  outline: 1px solid #0581CE;
}

.previous-flag-true {
  background: #ffffff !important;
  color: #000000 !important;
  outline: 1px solid #0581CE;
}

.moun-box {
  line-height: 28px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}

.moun-box-is_activeaa {
  background: #E6F2FA;
  font-size: 12px;
  color: #0581CE;
  text-align: center;
}

.moun-box-is_activeaa::before {
  content: '';
  width: 2px;
  height: 26px;
  display: none !important;
  background: #0581CE;
  position: absolute;
  left: 0;
}

.moun-box-is_activeaa:hover::before {
  display: block !important;
}

.moun-box-is_active {
  background: #E6F2FA;
  font-size: 12px;
  color: #0581CE;
  text-align: center;
}

.moun-box-is_active::before {
  content: '';
  width: 2px;
  height: 26px;
  display: block !important;
  background: #0581CE;
  position: absolute;
  left: 0;
}

.moun-box-is_active:hover::before {
  display: block;
}

.moun-box:hover {
  background: #E6F2FA;
  font-size: 12px;
  color: #0581CE;
  text-align: center;
}

.moun-box:hover::before {
  display: block;
}

.moun-box::before {
  content: '';
  display: none;
  width: 2px;
  height: 26px;
  background: #0581CE;
  position: absolute;
  left: 0;
}

.literature-box {
  margin-top: 20px;
  position: relative;

  .literature-left {
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;

    .zhanwei {
      height: 1px;
      width: 100%;
    }

    .left-box {
      padding-top: 60px;
    }

    .right-box {
      padding-left: 34px;

      .title-box {
        border-radius: 6px;
        background: #F2FAFF;
        padding: 13px 20px 11px;
        margin-top: 30px;

        .title {
          margin-bottom: 12px;
          font-size: 18px;
          font-weight: 540;
        }

        .switch-box {
          .twan {
            display: inline-block;
            border-radius: 6px;
            background: #F0F0F0;
            color: #808080;
            font-size: 12px;
            margin-right: 12px;
            cursor: pointer;
            min-width: 74px;
            line-height: 26px;
            text-align: center;
          }

          .Past {
            display: inline-block;
            border-radius: 6px;
            background: #F0F0F0;
            font-size: 12px;
            color: #808080;
            cursor: pointer;
            min-width: 74px;
            line-height: 26px;
            text-align: center;
            margin-right: 12px;
          }
        }
      }

      .title-two {
        font-weight: 540;
        color: #0581CE;
        font-size: 18px;
        margin: 20px 0;
      }
      .hide-article-content {
        max-height: 126px;
        overflow: hidden;
        margin-bottom: 52px !important;
      }
      .content-box {
        .item {
          border-bottom: 1px solid #F0F0F0;
          padding-bottom: 14px;
          margin-bottom: 14px;

          .title {
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 540;
          }

          .info {
            font-size: 14px;
            color: #666666;
            margin-bottom: 8px;
            line-height: 20px;
          }

          .con {
            font-size: 12px;
            color: #708AA2;
            line-height: 18px;
          }
        }

        .item:last-child {
          margin-bottom: 35px;
        }
      }
    }
  }

  .literature-right {
    .more-box {
      border-radius: 6px;
      padding: 17px 14px 0;
      overflow: hidden;
      background: #FBFBFB;

      .title {
        font-size: 18px;
        line-height: 20px;
        position: relative;
        padding-left: 14px;
        margin-bottom: 19px;
      }

      .title::before {
        content: '';
        display: block;
        width: 3px;
        height: 15px;
        background: #0581CEFF;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -7.5px;
      }

      .Related-item {
        overflow: hidden;

        li {
          display: flex;
          justify-items: flex-start;
          margin-bottom: 16px;
          align-items: center;

          &:hover .item-title {
            color: #0581ce !important;
          }

          .left {
            width: 80px;
            height: 45px;
            margin-right: 10px;
            display: flex;
            border-radius: 6px;
            overflow: hidden;
          }

          .right {
            width: calc(100% - 80px - 10px);


            .item-title {
              font-size: 14px;
              color: #000000FF;
              margin-bottom: 6px;
            }

            .con {
              color: #708AA2FF;
              font-size: 12px;
            }
          }
        }
      }

      .more-more {
        border-radius: 0px 0px 8px 8px;
        background: #E8F1F6;
        text-align: center;
        font-size: 14px;
        color: #0581CE;
        padding: 14px 0;
        margin: 0 -14px;
        cursor: pointer;
        margin-top: 4px;
      }
    }

  }
}
