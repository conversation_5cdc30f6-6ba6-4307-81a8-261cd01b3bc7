<template>
  <div class='literature-box container-box'>
    <el-row>
      <el-col :lg='16' :md='16' :sm='17' :xl='16' :xs='24'>
        <div class='literature-left'>
          <!--Navigation Start-->
          <bm-breadcrumb>
            <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ name: 'index-literature-home'}">文献速览</el-breadcrumb-item>
            <el-breadcrumb-item> {{ literatureDetails.pname }}</el-breadcrumb-item>
          </bm-breadcrumb>
          <!--End-->
          <el-row>
            <el-col :lg='3' :md='4' :sm='4' :xl='3' :xs='8'>
              <div ref='zhanwei' style='width: 100%;height: 1px;display: none'></div>
              <div ref='literaturebox' class='left-box'>
                <el-collapse v-if='false' v-model='activeNames'>
                  <el-collapse-item v-for='(item,index) in periodicalsPreviousData' :key='item.id' :name='item.year'
                                    :title='item.year'>
                    <div v-for='(list,index) in item.res' :key='list.id'
                         :class='{"moun-box-is_activeaa":periodicalslistCurrent===list.id}'
                         class='moun-box text-limit-1' @click='selectperiodicalsNowFun(list.id,list.month + "月速览")'>
                      {{ list.month }} 月速览
                      <span>
                        {{ list.magazineType === '1' ? '(上)' : list.magazineType === '2' ? '(中)' : list.magazineType === '3' ? '(下)' : ''
                        }}
                      </span>
                    </div>
                  </el-collapse-item>
                </el-collapse>
                <div v-for='(periodicalslist,index) in periodicalsPreviousData'
                     v-show='periodicalslist.year === activeYear'
                     :key='index'>
                  <div v-for='(resItem,index) in periodicalslist.res' :key='index'
                       :class='{"moun-box-is_active": periodicalslistCurrent === resItem.id}'
                       class='moun-box text-limit-1' @click='selectperiodicalsNowFun(resItem.id,resItem.month + "月速览")'>
                    {{ resItem.month }} 月速览
                    <span>
                          {{ resItem.magazineType === '1' ? '(上)' : resItem.magazineType === '2' ? '(中)' : resItem.magazineType === '3' ? '(下)' : ''
                      }}
                      </span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :lg='21' :md='20' :sm='20' :xl='21' :xs='16'>
              <div class='right-box'>
                <div class='title-box'>
                  <p class='title'>
                    {{ literatureDetails.pname }}
                  </p>
                  <div class='switch-box'>
                    <!--                    <div v-show="periodicalsNowData.length>0" :class='{"screen-flag-true":!flag}' class='twan' @click='switchflagfun(1,currentYear,periodicalsNowData[0].id)'>{{ currentYear }}</div>-->
                    <div v-for='(item,index) in periodicalsPreviousData' :key='index'
                         ref='Past' :class='{"previous-flag-true":activeYear === item.year}' class='Past'
                         @click='switchflagfun(2,"往期",item.res[0].id,item.year)'>
                      {{ item.year }}
                    </div>
                  </div>
                </div>
                <div class='title-two'>
                  {{ literatureDetails.name }}
                </div>
                <ul class='content-box'>
                  <li v-for='(item,index) in literatureArticle' :key='item.infoId' class='item'>
                    <p class='title text-limit-1'>{{ item.infoTitle }}</p>
                    <p class='info'>{{ item.inBrief }}</p>
                    <p class='con'>
                      {{ item.references }}
                    </p>
                  </li>
                </ul>
                <el-pagination
                  :current-page.sync='currentPage'
                  :hide-on-single-page='$store.state.hideOnSinglePage'
                  :layout='$store.state.layout'
                  :page-size='$store.state.article_count'
                  :pager-count='$store.state.pager_count'
                  :total='total'
                  background
                  small
                  style='text-align: center; margin-bottom: 10px'
                  @current-change='handleCurrentChange'
                >
                </el-pagination>
                <p v-if='literatureArticle.length===0'
                   style='text-align: center;margin-top: 10vh; color: #888888;font-size: 14px'>
                  没有更多内容
                </p>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :sm='7' :xl='8' :xs='24'>
        <div class='literature-right'>
          <div class='more-box'>
            <div class='title'>更多</div>
            <ul class='Related-item'>
              <li v-for='(liter) in moreLiterature' :key='liter.id' class='cursor'
                  @click='jumpDetailPageFun(liter.id,liter.pId)'>
                <div class='left flex_shrink'>
                  <img :src='$tool.compressImg(liter.smallImageUrl,80,45)' alt='' class='img_cover' />
                </div>
                <div class='right flex_shrink'>
                  <p class='item-title text-limit-1'>{{ liter.parentName }}{{ liter.name }}</p>
                  <p class='con'>{{ liter.showViews }}阅读</p>
                </div>
              </li>
            </ul>
            <nuxt-link :to="{name:'index-literature-home'}" target='_blank'>
              <div class='more-more' @click='analysysFun()'>
                查看更多
              </div>
            </nuxt-link>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>

import { infoPeriodicals, periodicalsDetail, periodicalsInfos, periodicalsNow, periodicalsOld } from '@/api/literature'

export default {
  head() {
    return {
      title: this.literatureDetails.pname,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.literatureDetails.shareDescription
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.literatureDetails.pname
        }]
    }
  },
  name: 'LiteraturePage',
  async asyncData({ app, params, error, store, query, req }) {
    const [request1, request2, request3] = await Promise.all([
      app.$axios.$request(infoPeriodicals({ pageNo: 1, pageSize: store.state.article_count })),
      app.$axios.$request(periodicalsNow({
        pid: query.pid,
        year: new Date().getFullYear(),
        pageNo: 1,
        pageSize: 9999
      })),
      app.$axios.$request(periodicalsOld({
        pid: query.pid,
        year: new Date().getFullYear() + 1, // 当年+1
        pageNo: 1,
        // pageSize: store.state.article_count
        pageSize: 9999
      }))
    ])
    let magazineIdGetset = null
    // 此处判断是从杂志进来的 还是从二级分类进来的, 从杂志进来的选择今年或者往期的第一个
    if (query.id === undefined) {
      magazineIdGetset = request3.list[0].id
    } else {
      magazineIdGetset = query.id
    }
    const [request4, request5] = await Promise.all([
      app.$axios.$request(periodicalsDetail({
        magazineId: magazineIdGetset
      })),
      app.$axios.$request(periodicalsInfos({
        magazineId: magazineIdGetset,
        pageNo: 1,
        pageSize: store.state.article_count
      }))
    ])
    app.head.title = request4.result.pname
    let yearArray = []// 存储共有几个年
    let activeNames = []// 存储面板全部展开
    let activeYear = null// 默认选中年份
    let activeBtn = 'now'
    // 将往期 年份 按年份归类,   2021 [x,x,x]   2020 [x,x,x]
    request3.list.forEach((item, i) => {
      if (item.id === Number(magazineIdGetset)) {
        activeBtn = 'old'
        activeYear = item.year
      }
      var index = -1
      var createTime = item.year.substring(0, 10)
      var alreadyExists = yearArray.some(function(newData, j) {
        if (item.year.substring(0, 10) === newData.year.substring(
          0, 10)) {
          index = j
          return true
        }
      })
      if (!alreadyExists) {
        var res = []
        res.push(item)
        yearArray.push({
          year: item.year,
          res: res
        })
      } else {
        yearArray[index].res.push(item)
      }
    })
    const descYearArray = []
    yearArray.forEach((item) => {
      const sortArray = item.res.sort((a, b) => {
        return Number(a.month) >= Number(b.month) ? -1 : 1
      })
      descYearArray.push({
        year: item.year,
        res: sortArray
      })
      activeNames.push(item.year)
    })
    return {
      moreLiterature: request1.list,// 精选编译更多二级分类
      periodicalsNowData: request2.list,// 精选编译当年二级分类
      periodicalsPreviousData: descYearArray,// 往期二级分类 这里对往年处理了一下 将往期 年份 按年份归类,   2021 [x,x,x]   2020 [x,x,x]
      literatureDetails: request4.result,// 精选编译文章详情
      literatureArticle: request5.list,// 精选编译文章列表
      periodicalslistCurrent: Number(magazineIdGetset),// 当前二级分类下标 用于高亮
      flag: activeBtn === 'old',// 用户判断 是往期还是当年
      activeNames,// 用于展开所有面板
      total: request5.page ? request5.page.totalCount : 0,
      activeYear // 默认选中年份
    }
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),// 当前年
      literatureDetails: [],// 文献速览详情
      literatureArticle: [],// 文献速览文章列表
      particularYear: [
        {
          year: '2021',
          content: ['1月速览（下）', '1月速览（中）', '1月速览（上）', '2月速览（下）', '2月速览（中）', '2月速览（上）', '3月速览（下）', '3月速览（中）', '3月速览（上）']
        },
        {
          year: '2020',
          content: ['1月速览（下）', '1月速览（中）', '1月速览（上）', '2月速览（下）', '2月速览（中）', '2月速览（上）', '3月速览（下）', '3月速览（中）', '3月速览（上）']
        },
        {
          year: '2019',
          content: ['1月速览（下）', '1月速览（中）', '1月速览（上）', '2月速览（下）', '2月速览（中）', '2月速览（上）', '3月速览（下）', '3月速览（中）', '3月速览（上）']
        },
        {
          year: '2018',
          content: ['1月速览（下）', '1月速览（中）', '1月速览（上）', '2月速览（下）', '2月速览（中）', '2月速览（上）', '3月速览（下）', '3月速览（中）', '3月速览（上）']
        },
        {
          year: '2017',
          content: ['1月速览（下）', '1月速览（中）', '1月速览（上）', '2月速览（下）', '2月速览（中）', '2月速览（上）', '3月速览（下）', '3月速览（中）', '3月速览（上）']
        }
      ],
      scroll: '',// 监听滚动条
      currentPage: 1, // 分页页数
      yearCurrent: String(new Date().getFullYear())
    }
  },
  methods: {
    // 埋点
    analysysFun() {
      this.$analysys.btn_click('文献速览-查看更多', document.title)
    },
    //切换样式
    switchflagfun(data, name, id, year) {
      this.activeYear = year
      this.selectperiodicalsNowFun(id, name)
      this.$analysys.btn_click(String(name), document.title)
      this.flag = data !== 1
    },
    //监听滚动条
    listenerfun() {
      document.addEventListener('scroll', this.handscroll, true)
    },
    handscroll(e) {
      if (e.target.scrollTop > 200) {
        this.$refs.zhanwei.style.display = 'block'
        this.$refs.literaturebox.style.cssText = 'position:fixed;top:30px;left:80px'
      } else {
        this.$refs.zhanwei.style.display = 'none'
        this.$refs.literaturebox.style.cssText = 'position:none;'
      }
      if (e.target.scrollTop > 1100) {
        this.$refs.zhanwei.style.display = 'none'
        this.$refs.literaturebox.style.cssText = 'position:none;'
      }
    },
    // 跳转详情页
    jumpDetailPageFun(id, pId) {
      const { href } = this.$router.resolve({
        name: 'index-literature-detail',
        query: { id: id, pid: pId }
      })
      window.open(href, '_blank')
    },
    // 切换当前年份的文献速览详情
    selectperiodicalsNowFun(id, name) {
      this.currentPage = 1
      this.$analysys.btn_click(String(name), document.title)
      this.periodicalslistCurrent = id
      this.$router.push({
        name: 'index-literature-detail',
        query: { id, pid: this.$route.query.pid }
      })
      // id 当前二级分类id
      Promise.all([
        this.$axios.$request(periodicalsDetail({
          magazineId: id
        })),
        this.$axios.$request(periodicalsInfos({
          magazineId: id,
          pageNo: 1,
          pageSize: this.$store.state.article_count
        }))
      ]).then(res => {
        if (res[0] && res[0].code === 1) {
          this.literatureDetails = res[0].result
        }
        if (res[1] && res[1].code === 1) {
          this.literatureArticle = res[1].list
          this.total = res[1].page.totalCount
        }
      })
    },
    // 分页
    handleCurrentChange(item) {
      // this.$tool.scrollIntoView()
      document.documentElement.scrollTop = 0
      this.$axios.$request(periodicalsInfos({
        magazineId: this.periodicalslistCurrent,
        pageNo: item,
        pageSize: this.$store.state.article_count
      })).then((res) => {
        this.literatureArticle = res.list
        this.total = res.page.totalCount
      })
    }
  },
  mounted() {

  },
  watch: {}
}
</script>

<style lang='less' scoped>
@import "./index";
</style>
