<template>
  <div class='LiteratureList-box container-box'>
    <!--Navigation Start-->
    <div class='navigation-container'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item>文献速览</el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <!--End-->
    <!--    <p class='title-first fontWeight'>文献速览</p>-->
    <div class='big-box'>
      <div v-for='(list) in literatureNewsData' :key='list.id' class='img-box cursor'
           @click='jumpDetailPageFun(list.id)'>
        <div class='box img_box_scale'>
          <div class='literaturelist-image-box'>
            <img :src='$tool.compressImg(list.largeImageUrl,229,127)' alt='' class='img_cover'>
          </div>
          <p class='title text-limit-1'>{{ list.name }}</p>
          <div class='flex_between'>
            <p class='con'>{{ list.showViews }}阅读</p>
            <p class='date'>{{ list.publishDate }}更新</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { literature } from '@/api/literature'

export default {
  head() {
    return {
      title: '文献速览'
    }
  },
  name: 'LiteratureList',
  // eslint-disable-next-line require-await
  async asyncData({ app, params, error, store, query, req }) {
    app.head.title = '文献速览'
    const [request1] = await Promise.all([
      app.$axios.$request(literature())
    ])
    return {
      literatureNewsData: request1.list
    }
  },
  data() {
    return {}
  },
  methods: {
    // 跳转详情页
    jumpDetailPageFun(id) {
      const { href } = this.$router.resolve({ name: 'index-literature-detail', query: { pid: id } })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "./index";
</style>
