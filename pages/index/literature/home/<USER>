/*中屏时开始*/
@media screen and (max-width: 1199px) {
  .big-box {
    .img-box {
      width: 25% !important;
    }
  }
}

.navigation-container {
  margin: 20px 0;
}

/*中屏时结束*/
/*大屏时结束*/
.LiteratureList-box {

  .title-first {
    margin: 20px 0;
    font-size: 18px;
    font-weight: 540;
  }

  .big-box {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin: 0 -7px;

    .img-box {
      width: 20%;
      margin-bottom: 20px;
      padding: 0 7px;
      box-sizing: border-box;

      .box {
        border-radius: 6px;
        background: #FBFBFB;

        &:hover {
          transition: all .6s;
          box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
        }

        .literaturelist-image-box {
          width: 100%;
          height: 127px;
          border-radius: 6px;
          overflow: hidden;
        }
      }

      .title {
        font-size: 16px;
        margin: 9px 11px 12px;

      }

      .con {
        color: #708AA2;
        font-size: 13px;
        padding-bottom: 14px;
        margin-left: 11px;
      }

      .date {
        color: #708AA2;
        font-size: 13px;
        padding-bottom: 16px;
        margin-right: 11px;
      }
    }
  }

}
