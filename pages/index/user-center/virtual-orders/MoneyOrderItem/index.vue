<template>
  <div class="order_wrapper">
    <div class="order_top">
      <p class="order_num">
        订单编号 <span style="color: #333">{{orderNumber}}</span>
      </p>
      <p class="order_time">
        {{timeStamp.timestamp_13(orderTime, 'yyyy-mm-d-h-m-s')}}
      </p>
    </div>
    <div class="order_content">
      <div class="left">
        <div class="image">
          <SvgIcon icon-class="user-money-s" class-name="icons"/>
        </div>
        <div class="info">
          +{{price}}神贝
        </div>
      </div>
      <div class="right">
        <p class="tips">
          充值成功
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MoneyOrderItem",
  components: {},
  props:["orderTime","orderNumber","price"]
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
