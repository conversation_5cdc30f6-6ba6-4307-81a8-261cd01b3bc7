<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的订单
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <UserOrderSidebar
          is-open-data="我的订单"
        />
      </div>
      <div class='mall_container'>
        <div style='margin-bottom: 20px'>
          <MallTab
            :tab-list='tabList'
            @changeTabHandler='changeTabHandler'
          />
        </div>
        <DataLoad loading-height='40vh' :loading='loading' :no-more='orderListData.list.length === 0' tips='暂无订单'>
          <div class='order_list_wrapper_container' ref="order_list_wrapper_container">
            <div v-for="(item,index) in orderListData.list" :key="index" class="order_item">
              <CoursOrderItem
                v-if="item.type === 'PR'"
                :authorname="item.authorname"
                :course-name="item.courseName"
                :course-cover="item.courseCover"
                :order-number="item.orderNumber"
                :order-time="item.orderTime"
                :price="item.price"
                :redirect-id="item.redirectId"
                :course-video-list="item.courseVideoList"
              />
              <MoneyOrderItem
                v-if="item.type === 'RC'"
                :order-time="item.orderTime"
                :order-number="item.orderNumber"
                :price="item.price"
              />
            </div>

          </div>
          <div style='margin-top: 60px'>
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='8'
              :pager-count='$store.state.pager_count'
              :total='orderListData.page.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </DataLoad>
      </div>
    </div>
  </div>
</template>

<script>
import UserOrderSidebar
  from "../../../../components/optimize-components/page-components/user-center/UserOrderSidebar/index.vue";
import MallTab from '../../../../components/optimize-components/page-components/mall/MallTab/index.vue'
import DataLoad from '../../../../components/optimize-components/public/DataLoad/index.vue'
import {getBillOrderList} from "../../../../api/new-user-center";
import CoursOrderItem from "./CoursOrderItem/index.vue";
import MoneyOrderItem from "./MoneyOrderItem/index.vue";

export default {
  name: 'VirtualOrdersPage',
  components: {MoneyOrderItem, CoursOrderItem, DataLoad, MallTab, UserOrderSidebar },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [orderListData] = await Promise.all([
      app.$axios.$request(getBillOrderList({
        pageNo: 1,
        pageSize: 8,
      }))
    ])

    return {
      orderListData
    }
  },
  mounted() {

  },
  head() {
    return {
      title: `虚拟订单 - 个人中心 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `虚拟订单 - 个人中心 - 脑医汇`
        },
      ]
    }
  },
  data() {
    return {
      loading: false,
      currentPage: 1,
      tabList: [
        { id: "No", tabName: '全部订单', isEnable: true },
        { id: "RC", tabName: '充值', isEnable: true },
        { id: "PR", tabName: '消费', isEnable: true },
      ],
      recordType:null
    }
  },
  methods: {
    handleCurrentChange(item){
      this.$refs.order_list_wrapper_container.classList.add("order_list_wrapper_container_active")
      this.$axios.$request(getBillOrderList({
        recordType:this.recordType,
        pageNo: item,
        pageSize: 8,
      })).then(res=>{
        if(res.code === 1){
          this.orderListData = res;
          this.$refs.order_list_wrapper_container.classList.remove("order_list_wrapper_container_active");
          this.$tool.scrollIntoTop()
        }
      })
    },
    changeTabHandler(recordType,backFn){
      this.currentPage = 1;
      this.recordType = recordType !== "No" ? recordType : null;
      this.$axios.$request(getBillOrderList({
        recordType:recordType !== "No" ? recordType : null,
        pageNo: 1,
        pageSize: 8,
      })).then(res=>{
        if(res.code === 1){
          this.orderListData = res;
          backFn(true)
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }

  .order_list_wrapper_container {
    display: flex;
    flex-direction: column;
    grid-gap: 20px 0;
  }
  .order_list_wrapper_container_active{
    position: relative;
    &::before{
      content:"";
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255,255,255,0.6);
    }
  }
}
</style>
