<template>
  <div class="order_wrapper">
    <div class="order_top">
      <p class="order_num">
        订单编号 <span style="color: #333">{{orderNumber}}</span>
      </p>
      <p class="order_time">
        {{timeStamp.timestamp_13(orderTime, 'yyyy-mm-d-h-m-s')}}
      </p>
    </div>
    <div class="order_content">
      <div class="left cursor" @click="jumpPageHandler">
        <div class="subject">
          <div class="image">
            <img class="img_cover" :src="courseCover ? $tool.compressImg(courseCover,153,86) : require('/assets/images/default16.png')" alt="">
          </div>
          <div class="info">
            <p class="title text-limit-2">
              {{courseName}}
            </p>
            <p class="title_name">
              {{authorname}}
            </p>
          </div>
        </div>
        <ul class="section">
          <li v-for="(item,index) in courseVideoList" :key="index">{{item.name}}</li>
        </ul>
      </div>
      <div class="right">
        <div class="money">
          <span>实付</span><span class="price">{{price}}</span> 神贝
        </div>
        <div class="coursorder_item">
          <p class="tips">购买成功</p>
          <div class="btn" @click="jumpPageHandler">查看课程</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CoursOrderItem",
  components: {},
  props:["courseName","authorname","courseCover","orderNumber","orderTime","price","redirectId","courseVideoList"],
  methods:{
    jumpPageHandler(){
      this.$router.push({ path: '/cloudclassroomCourse', query: { courseId: this.redirectId } })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
