<template>
  <div class='user_center_page_wrapper'>
    <UserCenterNav
      :tab-list='tabList'
      :is-active-id="isActive"
      :personal-info='personalInfo'
      :is-login-user='personalInfo.isLoginUser'
      :view-settings="viewSettings"
      @changeTabHandler='changeTabHandler'
    />
    <div class='header_wrapper_content'>
      <div class='container-box'>
        <BackgroundImage
          :personal-bgi='personalInfo.personalBgi'
          :is-login-user='personalInfo.isLoginUser'
        />
        <PersonalProfile
          :personal-info='personalInfo'
          :is-login-user='personalInfo.isLoginUser'
          :view-settings="viewSettings"
          @medalsInfoHandler="medalsInfoHandler"
          @changeTabHandler='changeTabHandler'
        />
      </div>
    </div>
    <div class='user_wrapper_content'>
      <PageContainer>
        <template #page-left>
          <div class='white_content'>
            <UserCenterTab
              :tab-list='tabList'
              :is-active-id="isActive"
              :is-login-user='personalInfo.isLoginUser'
              @changeTabHandler='changeTabHandler'
            >
              <template #right>
                <div v-if="false" class='drafts'>
                  草稿箱(26)
                </div>
              </template>
            </UserCenterTab>
            <UserCenterTabContent
              :type='isActive'
              :user-category="personalInfo.userCategory"
              :is-login-user='personalInfo.isLoginUser'
              :view-settings="viewSettings"
            />
          </div>
        </template>
        <template #page-right>
          <div id="right_slide_wrapper_father">
            <div id="right_slide_wrapper" ref="right_slide_wrapper">
              <!-- 添加资料库组件 -->
              <div style='margin-bottom: 20px'>
                <ResourceLibrary
                  :userId="personalInfo.id"
                  :isOwner="personalInfo.isLoginUser === 'T'"
                />
              </div>
              <div
                v-if="personalInfo.isLoginUser === 'T' && personalInfo.statusForPatient === 2"
                style='margin-bottom: 20px'>
                <ManageButton/>
              </div>
              <div
                v-if="personalInfo.isLoginUser === 'T' && personalInfo.levelNumber >= 1"
                style='margin-bottom: 20px'>
                <RecentHotspots/>
              </div>
              <div
                v-if="!personalInfo.brandId && !personalInfo.brandProductLineId && personalInfo.departmentHomePageInfoList && personalInfo.departmentHomePageInfoList.length>0 && isDepartmentShow"
                style='margin-bottom: 20px'>
                <AffiliationDepartment
                  :department-home-page-info="personalInfo.departmentHomePageInfoList"
                />
              </div>
              <div
                v-if="personalInfo.brandId && personalInfo.brandProductLineId"
                style='margin-bottom: 20px'
              >
                <AffiliationTeam
                  :department-home-page-info="personalInfo.departmentHomePageInfoList"
                  :brand-product-line-id="personalInfo.brandProductLineId"
                />
              </div>
              <div
                v-if="personalInfo.userCategory === 'E'"
                style='margin-bottom: 20px'>
                <UserTeamMembers
                  :is-login-user='personalInfo.isLoginUser'
                />
              </div>
              <div
                v-if='personalInfo.userCategory === "P"'>
                <UserCenterJoinTeam
                  :is-login-user='personalInfo.isLoginUser'
                />
              </div>
              <div style='margin-bottom: 20px'>
                <UserInfluence
                  :personal-info='personalInfo'
                  :medals-list="medalsList"
                  @medalsInfoHandler="medalsInfoHandler"
                />
              </div>
              <UserStudy
                :is-login-user='personalInfo.isLoginUser'
              />
            </div>
          </div>
        </template>
      </PageContainer>
    </div>
    <transition name="el-fade-in">
      <MedalDialog
        v-if="isEnableMedalDialog"
        :medals-info="medalsInfo"
        @close="isEnableMedalDialog = false"
      />
    </transition>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import PageContainer from '../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import BackgroundImage
  from '../../../components/optimize-components/page-components/user-center/BackgroundImage/index.vue'
import PersonalProfile
  from '../../../components/optimize-components/page-components/user-center/PersonalProfile/index.vue'
import UserCenterTab from '../../../components/optimize-components/page-components/user-center/UserCenterTab/index.vue'
import ManageButton from '../../../components/optimize-components/page-components/user-center/ManageButton/index.vue'
import RecentHotspots
  from '../../../components/optimize-components/page-components/user-center/RecentHotspots/index.vue'
import AffiliationDepartment
  from '../../../components/optimize-components/page-components/user-center/AffiliationDepartment/index.vue'
import UserTeamMembers
  from '../../../components/optimize-components/page-components/user-center/UserTeamMembers/index.vue'
import UserInfluence from '../../../components/optimize-components/page-components/user-center/UserInfluence/index.vue'
import UserStudy from '../../../components/optimize-components/page-components/user-center/UserStudy/index.vue'
import UserCenterTabContent
  from '../../../components/optimize-components/page-components/user-center/UserCenterTabContent/index.vue'
import {getMedals, getWebApiHomeViewSettings, getWebApiPersonalWebsite} from '../../../api/new-user-center'
import MedalDialog from "../../../components/optimize-components/page-components/user-center/MedalDialog/index.vue";
import UserCenterJoinTeam
  from "../../../components/optimize-components/page-components/user-center/UserCenterJoinTeam/index.vue";
import ShortVideoPlayback from "../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import UserCenterNav from "../../../components/optimize-components/page-components/user-center/UserCenterNav/index.vue";
import AffiliationTeam
  from "../../../components/optimize-components/page-components/user-center/AffiliationTeam/index.vue";
import ResourceLibrary from '../../../components/UserCenter/ResourceLibrary.vue'
export default {
  name: 'UserCenterPage',
  components: {
    UserCenterNav,
    ShortVideoPlayback,
    UserCenterJoinTeam,
    MedalDialog,
    UserCenterTabContent,
    UserStudy,
    UserInfluence,
    UserTeamMembers,
    AffiliationDepartment,
    RecentHotspots,
    ManageButton,
    UserCenterTab,
    PersonalProfile,
    BackgroundImage,
    PageContainer,
    AffiliationTeam,
    ResourceLibrary
  },
  beforeRouteLeave(to, form, next) {
    if (to.path !== "/user-center") {
      const userCenterNav = document.getElementById("user_center_nav_wrapper")
      const navHeader = document.querySelector('#nav_header')

      navHeader.style.cssText = 'transform:translateY(0%)'
      userCenterNav.style.cssText = 'transform:translateY(-100%)'
    }
    next()
  },
  async asyncData({app, params, error, store, query, req, redirect}) {
    if (query.hasOwnProperty('profileuserid')) {
      redirect(`/user-center?profileUserId=${query.profileuserid}`)
    }
    const userId = store.state.auth.user.id
    const [personalInfo, viewSettings] = await Promise.all([
      app.$axios.$request(getWebApiPersonalWebsite({
        userId,
        profileUserId: query.profileUserId
      })),
      app.$axios.$request(getWebApiHomeViewSettings({
        userId: query.profileUserId
      }))
    ])

    let isActive = "ReleaseOpt";
    const infoData = personalInfo.result

    const tabList = [
      {id: 'ReleaseOpt', tabName: '发布', isEnable: true, open: true},
      {id: 'UserSpecialColumnOpt', tabName: '专栏', isEnable: true, open: true},
      {id: 'MeetingOpt', tabName: '会议', isEnable: true, open: true},
      {id: 'CloudClassroomAndBooks', tabName: '云课堂·书籍', isEnable: true, open: true},
      {id: 'ElabWeb', tabName: '手术复盘', isEnable: true, open: true},
      {id: 'UserShortVideo', tabName: '短视频', isEnable: true, open: true},
      {
        id: 'UserFavorites',
        tabName: '收藏',
        isEnable: viewSettings.result.haveCollect,
        open: viewSettings.result.openCollect
      },
      {id: 'Follow', tabName: '关注', isEnable: false, open: viewSettings.result.openFollow},
    ]

    if (query.appoint) {

      const filterArr = tabList.filter(item => item.id + '' === query.appoint + '')
      if (filterArr.length > 0 && infoData.isLoginUser === 'T') {
        isActive = query.appoint
      } else if (filterArr.length > 0 && infoData.isLoginUser === 'F' && filterArr[0].open) {
        isActive = query.appoint
      } else {
        isActive = "ReleaseOpt"
      }
    }


    const specialityList = infoData.specialityList
    let specialityNames = ''
    if (specialityList && specialityList.length > 0) {
      specialityList.forEach(item => {
        specialityNames += item.name + ','
      })
    }


    const title = `【${infoData.realName}】 ${infoData.company || ''} - ${infoData.department || ''} - ${specialityNames || ''} 学术主页 - 脑医汇 - 神外资讯 - 神介资讯 - 神内资讯`
    const keywords = `${infoData.realName},${infoData.realName}${infoData.title || ''},${infoData.company || ''},${infoData.department || ''},脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,介入手术,手术入路,在线课程`
    const description = `${infoData.resume ? infoData.resume.introduction : null}`


    let flag = false
    if (personalInfo.result.departmentHomePageInfoList && personalInfo.result.departmentHomePageInfoList.length > 0) {
      personalInfo.result.departmentHomePageInfoList.forEach(item => {
        if (item.isDepartmentHomePage) {
          flag = true;
        }
      })
    }


    return {
      tabList,
      isActive,
      personalInfo: personalInfo.result,
      viewSettings: viewSettings.result,
      title,
      keywords,
      description,
      isDepartmentShow: flag
    }
  },
  head() {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.description
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.keywords
        }
      ]
    }
  },
  data() {
    return {
      isEnableMedalDialog: false,
      medalsInfo: {},
      medalsList: [],

      membersByAdministratorData: {}
    }
  },
  watchQuery: false,
  mounted() {
    this.getMedalsHandler();
  },
  methods: {
    medalsInfoHandler({id, rank}) {
      this.medalsInfo = this.medalsList.filter(item => item.id === id)[0];
      this.medalsInfo.rank = rank

      this.isEnableMedalDialog = true;
    },
    changeTabHandler(id, backFn) {
      if (typeof id === 'object') {
        this.isActive = id.name;
        this.$router.replace({path: this.$route.fullPath, query: {appoint: id.name, type: id.type}});
        if (id.name === 'UserFavorites' || id.name === 'Follow') {
          this.$axios.$request(getWebApiHomeViewSettings({
            userId: this.$route.query.profileUserId
          })).then(res => {
            if (res.code === 1) {
              this.viewSettings = res.result;
            }
          })
        }

        if (backFn) {
          backFn(true)
        }

      } else {
        this.isActive = id;
        this.$router.replace({path: this.$route.fullPath, query: {appoint: id, type: null}});


        if (id === 'UserFavorites' || id === 'Follow') {
          this.$axios.$request(getWebApiHomeViewSettings({
            userId: this.$route.query.profileUserId
          })).then(res => {
            if (res.code === 1) {
              this.viewSettings = res.result;
            }
          })
        }

        if (backFn) {
          backFn(true)
        }
      }

    },
    getMedalsHandler() {
      this.$axios.$request(getMedals({
        userId: this.$route.query.profileUserId
      })).then(res => {
        if (res.code === 1) {
          this.medalsList = res.list;
        }
      })
    },
  }
}
</script>

<style scoped lang='less'>
.user_center_page_wrapper {
  background: rgb(248, 248, 248);
  padding-top: 10px;
  padding-bottom: 80px;

  .header_wrapper_content {

  }

  .user_wrapper_content {
    .white_content {
      .drafts {
        border-radius: 6px;
        width: 98px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: #999;
        background: #F8F8F8;
        margin-right: 20px;
        cursor: pointer;
      }
    }
  }
}

/deep/ #toFixed {
  position: fixed;
  top: 0;
}
</style>
