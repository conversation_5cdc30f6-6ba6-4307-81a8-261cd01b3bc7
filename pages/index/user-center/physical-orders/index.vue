<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的订单
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <UserOrderSidebar
          is-open-data="我的订单"
        />
      </div>
      <div class='mall_container'>
        <div style='margin-bottom: 20px'>
          <MallTab
            :default-id='defaultId'
            :tab-list='tabList'
            @changeTabHandler='changeTabHandler'
          />
        </div>
        <DataLoad loading-height='40vh' :loading='loading' :no-more='orderListData.list.length === 0' tips='暂无订单'>
          <div class='order_list_wrapper_container'>
            <OrderList
              v-for='item in orderListData.list'
              :key='item.id'
              :order-id='item.id'
              :press='item.press || {}'
              :order-number='item.orderNumber'
              :order-status='item.status'
              :create-time='item.createTime'
              :osm-order-item-list='item.osmOrderItemList'
              :after-sales-status='item.afterSalesStatus'
              :refund-type='item.refundType'
              @confirmReceipt='confirmReceipt'
              @deleteOrder='deleteOrder'
            />
          </div>
          <div style='margin-top: 60px'>
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='8'
              :pager-count='$store.state.pager_count'
              :total='orderListData.page.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
          <Confirm
            :visible='deleteOrderDialog'
            button-name='删除'
            background='rgba(0,0,0,0.2)'
            height='130px'
            @cancelFn='deleteOrderDialog = false'
            @confirm='deleteOrderSubmit'
          >
            <template #content>
              <div class='tips'
                   style='height: 110px;line-height:110px;text-align:center;font-size: 16px;color: #333333'>
                是否删除该订单
              </div>
            </template>
          </Confirm>
        </DataLoad>
      </div>
    </div>
  </div>
</template>

<script>
import UserOrderSidebar
  from "../../../../components/optimize-components/page-components/user-center/UserOrderSidebar/index.vue";
import MallTab from '../../../../components/optimize-components/page-components/mall/MallTab/index.vue'
import OrderList from '../../../../components/optimize-components/page-components/mall/OrderList/index.vue'
import {
  confirmReceipt,
  deleteOrder,
  getAfterSalesList,
  getAfterSalesListByItemId,
  getOrderList
} from '../../../../api/mall'
import DataLoad from '../../../../components/optimize-components/public/DataLoad/index.vue'
import Confirm from '../../../../components/optimize-components/UI/Confirm/index.vue'

export default {
  name: 'UserOrdersPage',
  components: { DataLoad, OrderList, MallTab, UserOrderSidebar, Confirm },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [orderListData] = await Promise.all([
      app.$axios.$request(getOrderList({
        pageNo: 1,
        pageSize: 8,
        /**
         * 订单状态 0代表全部 1代表待发货，2代表待收货,5代表待支付
         */
        orderStatus: 0
      }))
    ])

    return {
      orderListData
    }
  },
  head() {
    return {
      title: `实物订单 - 个人中心 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `实物订单 - 个人中心 - 脑医汇`
        },
      ]
    }
  },
  data() {
    return {
      deleteOrderDialog: false,
      deleteOrderId: null,
      loading: false,
      currentPage: 1,
      tabList: [
        { id: 0, tabName: '全部订单', isEnable: true },
        { id: 5, tabName: '待支付', isEnable: true },
        { id: 1, tabName: '待发货', isEnable: true },
        { id: 2, tabName: '待收货', isEnable: true },
        { id: 69, tabName: '售后/退换', isEnable: true }
      ],
      activeTab: 0,
      defaultId: 0
    }
  },
  mounted() {
    if (this.$route.query.type === 'service' && this.$route.query.itemId) {
      this.defaultId = 69
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-12 9:48
     * 删除订单
     * ------------------------------------------------------------------------------
     */
    deleteOrder(orderId) {
      this.deleteOrderDialog = true
      this.deleteOrderId = orderId
    },
    deleteOrderSubmit(orderId, backFn) {
      this.$axios.$request(deleteOrder({
        orderId: this.deleteOrderId
      })).then(res => {
        if (res.code === 1) {
          this.$toast('删除成功!')
          this.getOrderListHandler({ pageNo: this.currentPage, orderStatus: this.activeTab, backFn })
          backFn(true)
        } else {
          backFn(true)
          this.$toast(res.message)
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-09 18:26
     * 确认收货
     * ------------------------------------------------------------------------------
     */
    confirmReceipt(id) {
      this.$toast.loading({ message: '请稍候' })
      this.$axios.$request(confirmReceipt({
        orderId: id
      })).then(res => {
        if (res.code === 1) {
          this.getOrderListHandler({ pageNo: this.currentPage, orderStatus: this.activeTab })
          this.$toast.loading().clear()
        }
      })
    },
    /* ------------------------------------------------------------------------------ */

    getOrderListHandler({ pageNo = 1, orderStatus = 0, backFn }) {
      this.loading = true
      this.orderListData.list = []
      this.$axios.$request(getOrderList({
        pageNo,
        pageSize: 8,
        orderStatus
      })).then(res => {
        if (res && res.code === 1) {
          if (backFn) {
            backFn(true)
          }
          this.orderListData = res
          this.loading = false
        }
      })
    },
    getAfterSalesListHandler({ pageNo = 1, backFn }) {
      this.loading = true
      this.orderListData.list = []
      this.$axios.$request(getAfterSalesList({
        pageNo,
        pageSize: 8
      })).then(res => {
        if (res && res.code === 1) {

          if (backFn) {
            backFn(true)
          }
          this.orderListData = res

          let newOrderItem
          this.orderListData.list = res.list.map(item => {
            newOrderItem = {
              ...item.orderItem,
              num: item.num
            }
            return {
              id: item.id,
              orderNumber: item.afterSaleNo,
              status: '69',
              osmOrderItemList: [newOrderItem],
              afterSalesStatus: item.status,
              refundType: item.refundType,
              press: item.press
            }
          })


          this.loading = false
        }
      })
    },
    getAfterSalesListByItemIdHandler({ backFn }) {
      this.loading = true
      this.orderListData.list = []
      this.$axios.$request(getAfterSalesListByItemId({
        itemId: this.$route.query.itemId
      })).then(res => {
        if (res && res.code === 1) {

          if (backFn) {
            backFn(true)
          }
          this.orderListData = res
          this.orderListData.page = {}

          let newOrderItem

          this.orderListData.list = res.list.map(item => {
            newOrderItem = {
              ...item.orderItem,
              num: item.num
            }
            return {
              id: item.id,
              orderNumber: item.afterSaleNo,
              status: '69',
              osmOrderItemList: [newOrderItem],
              afterSalesStatus: item.status,
              refundType: item.refundType,
              press: item.press
            }
          })


          this.loading = false
        }
      })
    },
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.currentPage = item

      if (this.activeTab !== 69) {
        this.getOrderListHandler({ pageNo: this.currentPage, orderStatus: this.activeTab })
      } else if (this.activeTab === 69) {
        this.getAfterSalesListHandler({ pageNo: this.currentPage })
      }

    },
    changeTabHandler(id, backFn) {
      this.activeTab = id
      this.currentPage = 1

      if (id !== 69) {
        if (this.$route.query.type === 'service' && this.$route.query.itemId) {
          this.$router.push({ path: this.$route.path })
        }
        this.getOrderListHandler({ pageNo: this.currentPage, orderStatus: id, backFn })
      } else if (id === 69 && (this.$route.query.type === 'service' && this.$route.query.itemId)) {
        this.getAfterSalesListByItemIdHandler({ backFn })
      } else if (id === 69) {
        this.getAfterSalesListHandler({ pageNo: this.currentPage, backFn })
      }

    }
  }
}
</script>

<style scoped lang='less'>
.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }

  .order_list_wrapper_container {
    display: flex;
    flex-direction: column;
    grid-gap: 20px 0;
  }
}
</style>
