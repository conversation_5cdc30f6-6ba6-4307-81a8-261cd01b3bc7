<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的优惠券
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <UserOrderSidebar />
      </div>
      <div class='mall_container'>
        <div style='margin-bottom: 20px'>
          <MallTab
            :tab-list='tabList'
            @changeTabHandler='changeTabHandler'
          >
            <template #right>
              <div class='tips' style='font-size: 12px;color: #999999;padding-right: 20px'>
                <span style='cursor: pointer' @click='isIllustrate = true'>优惠券使用说明</span>
              </div>
            </template>
          </MallTab>
        </div>
        <DataLoad
          :tips='status === "N" ? "暂无优惠券" : "暂无优惠券"'
          :loading='false'
          :no-more='status === "N" ? couponListData.list.length === 0 : notCouponListData.list.length === 0'
        >
          <div v-if='status === "N"' class='order_list_wrapper_container'>
            <CouponUseItem
              v-for='item in couponListData.list'
              :key='item.id'
              :item='item'
              :coupon-id='item.id'
              :title='item.title'
              :reduction-amount='item.reductionAmount'
              :preferential-type='item.preferentialType'
              :threshold='item.threshold'
              :end-date='item.endDate'
              :specify-product-ids='item.specifyProductIds'
              :category="item.category"
              :conversionCode="item.code"
              @chooseCouponDialogHandler='chooseCouponDialogHandler'
              @chooseCourseDialogHandler="chooseCourseDialogHandler"
              @chooseVoucherCourseDialogHandler="chooseVoucherCourseDialogHandler"
            />
          </div>
          <div v-else class='order_list_wrapper_container'>
            <CouponUseItem
              v-for='item in notCouponListData.list'
              :key='item.id'
              :item='item'
              :coupon-id='item.id'
              :title='item.title'
              :reduction-amount='item.reductionAmount'
              :preferential-type='item.preferentialType'
              :threshold='item.threshold'
              :end-date='item.endDate'
              :category="item.category"
              :conversionCode="item.code"
              :is-use='true'
              :specify-product-ids='item.specifyProductIds'
            />
          </div>
          <div style='margin-top: 60px'>
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='10'
              :pager-count='$store.state.pager_count'
              :total='status === "N" ? couponListData.page.totalCount : notCouponListData.page.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </DataLoad>
      </div>
    </div>
    <CouponIllustrateDialog
      v-if='isIllustrate'
      @cancel='isIllustrate = false'
    />
    <ChooseCouponDialog
      v-if='isChooseCouponDialog'
      :choose-coupon-list='chooseCouponList'
      :coupon-id='chooseCouponId'
      @cancelHandler='isChooseCouponDialog = false'
    />
    <ChooseCourseDialog
      v-if="isChooseCourseDialog"
      :course-list='chooseCouponList'
      :course-id="chooseCouponId"
      :courseDialogType="courseDialogType"
      @cancelHandler='isChooseCourseDialog = false'
    />
  </div>
</template>

<script>
import UserOrderSidebar
  from "../../../../components/optimize-components/page-components/user-center/UserOrderSidebar/index.vue";
import MallTab from '../../../../components/optimize-components/page-components/mall/MallTab/index.vue'
import CouponUseItem from '../../../../components/optimize-components/page-components/mall/CouponUseItem/index.vue'
import { getMerchandiseListByCouponId } from '../../../../api/mall'
import DataLoad from '../../../../components/optimize-components/public/DataLoad/index.vue'
import CouponIllustrateDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/CouponIllustrateDialog/index.vue'
import ChooseCouponDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/ChooseCouponDialog/index.vue'
import ChooseCourseDialog
  from "../../../../components/optimize-components/page-components/user-center/dialog/ChooseCourseDialog/index.vue";
import {getCoursePageByCoupon, getUserCoupons, getExchangeVoucherCourseList} from "../../../../api/new-user-center";

export default {
  name: 'MallCouponsPage',
  components: {ChooseCourseDialog, ChooseCouponDialog, CouponIllustrateDialog, DataLoad, CouponUseItem, MallTab, UserOrderSidebar },
  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [couponListData, notCouponListData] = await Promise.all([
      app.$axios.$request(getUserCoupons({
        status: 'N',
        pageNo: 1,
        pageSize: 8
      })),
      app.$axios.$request(getUserCoupons({
        status: 'E',
        pageNo: 1,
        pageSize: 8
      })),
    ])

    return {
      couponListData,
      notCouponListData
    }
  },
  data() {
    return {
      chooseCouponId: null,
      isChooseCourseDialog: false,
      courseDialogType: '', // 用来区分课程兑换A和优惠券V
      isChooseCouponDialog: false,
      chooseCouponList: [],
      isIllustrate: false,
      status: 'N',
      currentPage: 1,
      tabList: [
        { id: 'N', tabName: '可使用', isEnable: true },
        { id: 'E', tabName: '已失效', isEnable: true }
      ]
    }
  },
  head() {
    return {
      title: `我的优惠券 - 个人中心 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `我的优惠券 - 个人中心 - 脑医汇`
        },
      ]
    }
  },
  methods: {
    chooseCouponDialogHandler(couponId) {
      this.$axios.$request(getMerchandiseListByCouponId({
        couponId,
        limit: 5
      })).then(res => {
        if (res.code === 1) {
          this.chooseCouponList = res.list
          this.chooseCouponId = couponId
          this.isChooseCouponDialog = true
        }
      })
    },
    chooseCourseDialogHandler(couponId){
      this.$axios.$request(getCoursePageByCoupon({
        couponId,
        limit: 5
      })).then(res => {
        if (res.code === 1) {
          this.chooseCouponList = res.list
          this.chooseCouponId = couponId
          this.courseDialogType = 'V'
          this.isChooseCourseDialog = true
        }
      })
    },
    chooseVoucherCourseDialogHandler(couponId){
      this.$axios.$request(getExchangeVoucherCourseList({
        voucherId: couponId,
        pageNo: 1,
        pageSize: 5
      })).then(res => {
        if (res.code === 1) {
          this.chooseCouponList = res.list
          this.chooseCouponId = couponId
          this.courseDialogType = 'A'
          this.isChooseCourseDialog = true
        }
      })
    },
    getUserMyCouponListHandler({ pageNo = 1, status = 'N' }) {
      this.$axios.$request(getUserCoupons({
        status,
        pageNo,
        pageSize: 8
      })).then(res => {
        if (res.code === 1) {
          if (status === 'N') {
            this.couponListData = res
          } else {
            this.notCouponListData = res
          }
        }
      })
    },
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.currentPage = item
      this.getUserMyCouponListHandler({ pageNo: item, status: this.status })
    },
    changeTabHandler(id, backFn) {
      this.currentPage = 1
      this.status = id
      this.getUserMyCouponListHandler({ pageNo: 1, status: this.status })
      backFn(true)
    }
  }
}
</script>

<style scoped lang='less'>
.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }

  /deep/ .order_list_wrapper_container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
  }
}
</style>
