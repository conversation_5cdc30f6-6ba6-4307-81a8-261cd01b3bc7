<template>
  <NewPageContainer>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/user-center/coupons' }">
          我的优惠券
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="$route.query.type === 'V'">
          使用优惠券课程
        </el-breadcrumb-item>
        <el-breadcrumb-item v-else-if="$route.query.type === 'A'">
          使用兑换券课程
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>

    <div class='book_wrapper_content'>
      <CourseArticleItem
        v-for="item in courseList.list"
        :key="item.id"
        :money="item.money"
        :cover="item.cover"
        :name="item.name"
        :buys="item.buys"
        :course-id="item.id"
      />
    </div>
    <el-pagination
      v-if="$route.query.type === 'A'"
      :current-page.sync='currentPage'
      :hide-on-single-page='courseList.page.totalPage == 1'
      :layout='$store.state.layout'
      :page-size='coursePageSize'
      :pager-count='$store.state.pager_count'
      :total='courseList.page.totalCount'
      background
      small
      style='text-align: center; margin-top: 50px;'
      @current-change='handleCurrentChange'
    >
    </el-pagination>
  </NewPageContainer>
</template>

<script>
import NewPageContainer from '../../../../../components/optimize-components/UI/NewPageContainer/index.vue'
import CourseArticleItem
  from "../../../../../components/optimize-components/public/article-types-list/user-center-page/CourseArticleItem/index.vue";
import {getCoursePageByCoupon, getExchangeVoucherCourseList} from "../../../../../api/new-user-center";

export default {
  name: 'CouponsBooks',
  components:{CourseArticleItem, NewPageContainer},
  async asyncData({ app, params, error, store, query, req, redirect }){
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }
    if(query.type === 'V'){
      const [request1] = await Promise.all([
        app.$axios.$request(getCoursePageByCoupon({
          couponId:params.id,
        }))
      ])
      return {
        courseList:request1
      }
    }else if(query.type === 'A'){
      const coursePageSize = 5
      const [request1] = await Promise.all([
        app.$axios.$request(getExchangeVoucherCourseList({
          voucherId: params.id,
          pageNo: 1,
          pageSize: coursePageSize
        }))
      ])
      return {
        courseList:request1,
        coursePageSize
      }
    }
  },
  head() {
    return {
      title: `更多课程 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `更多课程 - 我的优惠劵 - 脑医汇`
        },
      ]
    }
  },
  data(){
    return{
      currentPage: 1,
    }
  },
  methods: {
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.currentPage = item
      this.getAPage(item)
    },
    getAPage(pageNo){
      this.$axios.$request(getExchangeVoucherCourseList({
        voucherId: this.$route.params.id,
        pageNo: pageNo,
        pageSize: this.coursePageSize
      })).then(res=>{
        if (res.code === 1) {
          this.courseList = res;
        }
      })
    },
  }
}
</script>

<style scoped lang='less'>
.book_wrapper_content{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 30px 18px;
}
</style>
