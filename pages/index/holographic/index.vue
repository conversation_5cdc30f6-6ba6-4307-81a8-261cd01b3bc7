<template>
  <div id="HologramPage" class="padding_bottom">
    <div class="banner">
      <img :src="require('~/assets/images/elabweb/banner_panoramic.png')" />
    </div>
    <div class="content">
      <div class="leftBox">
        <SpecialColumn :specialList="specialColumnList"/>
        <MainList :elab_list="elab_list" :filtrateList="filtrateList"/>
      </div>
      <div class="rightBox">
        <Menu :menuData="menuData"/>
      </div>
    </div>
  </div>
</template>


<script>
import SpecialColumn from '~/components/Elab/Holographic/SpecialColumn'
import MainList from '~/components/Elab/Holographic/MainList'
import Menu from '~/components/Elab/Holographic/Menu'

import { getPanoramicSurgeryCategoryCatalogs,getElabPanoramicSurgery,panoramicSurgicalFlow,getPanoramicSurgeryCategoryTabList } from '@/api/elabweb'

export default {
  name: 'HologramPage',
  components: {
    SpecialColumn,
    MainList,
    Menu
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [request1,request2,request3,request4] = await Promise.all([
      app.$axios.$request(
        getPanoramicSurgeryCategoryCatalogs({})
      ),
      app.$axios.$request(
        getElabPanoramicSurgery({
          loginUserId: store.state.auth.user.id,
          type: 1,
          pageNo: 1,
          pageSize: 100,
        })
      ),
      app.$axios.$request(
        panoramicSurgicalFlow({
          pageNo: 1,
          pageSize: 24,
          sortType: 'D', // 排序 默认D 最新L    最热H
        })
      ),
      app.$axios.$request(
        getPanoramicSurgeryCategoryTabList({})
      )
    ])

    return {
      menuSourceData: request1.list,
      specialColumnList: request2.list,
      elab_list: request3.list,
      filtrateList: request4.list,
    }
  },
  data() {
    return {
      menuData: [],
    }
  },
  head() {
    return {
      title: '全景手术-手术复盘-脑医汇',
    }
  },
  mounted() {
    this.recursion_fn(this.menuSourceData,0)
  },
  methods: {
    recursion_fn(list,num){
      for (let i = 0; i < list.length; i++) {
        list[i].deep = num;
        if (list[i].children.length) {
          this.recursion_fn(list[i].children, list[i].deep + 1)
        } else if (num === 1) {
          list[i].deep = num + 1;
        }
      }
      this.menuData = this.menuSourceData;
    }
  }
}
</script>

<style lang="less" scoped>
@import 'swiper/css/swiper.min.css';
#HologramPage{
  background: #EEF4F6;
}
.padding_bottom {
  padding-bottom: 37px;
}
.banner{
  width: 100%;
  display: flex;
  img{
    width: 100%;
  }
}
.content {
  width: 1128px;
  margin: 0 auto;
  background: #fff;
  position: relative;
  top: -70px;
  border-radius: 8px;
  display: flex;
  padding: 24px;
}
.leftBox{
  flex: 1;
  width: 0;
}
.rightBox{
  width: 272px;
  margin-left: 24px;
}
</style>
