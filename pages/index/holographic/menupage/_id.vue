<template>
  <div id="MenuPage" class="padding_bottom">
    <div class="content">
      <div class="leftBox">
        <MainList :elab_list="elab_list" :isMenuPage="true"/>
      </div>
      <div class="rightBox">
        <Menu :menuData="menuData" :isMenuPage="true"/>
      </div>
    </div>
  </div>
</template>

<script>
import MainList from '~/components/Elab/Holographic/MainList'
import Menu from '~/components/Elab/Holographic/Menu'

import { getPanoramicSurgeryCategoryCatalogs,panoramicSurgicalFlow } from '@/api/elabweb'

export default {
  name: 'MenuPage',
  components: {
    MainList,
    Menu
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [request1,request2] = await Promise.all([
      app.$axios.$request(
        getPanoramicSurgeryCategoryCatalogs({})
      ),
      app.$axios.$request(
        panoramicSurgicalFlow({
          pageNo: 1,
          pageSize: 24,
          sortType: 'D', // 排序 默认D 最新L    最热H
          packageId: params.id // 目录id
        })
      ),
    ])

    return {
      menuSourceData: request1.list,
      elab_list: request2.list,
    }
  },
  data() {
    return {
      menuData: [],
    }
  },
  head() {
    return {
      title: '全景手术-手术复盘-脑医汇',
    }
  },
  mounted() {
    this.recursion_fn(this.menuSourceData,0)
  },
  methods: {
    recursion_fn(list,num){
      for (let i = 0; i < list.length; i++) {
        list[i].deep = num;
        if (list[i].children.length) {
          this.recursion_fn(list[i].children, list[i].deep + 1)
        } else if (num === 1) {
          list[i].deep = num + 1;
        }
      }
      this.menuData = this.menuSourceData;
    }
  },
}
</script>


<style scoped lang="less">
#MenuPage{
  background: #EEF4F6;
  padding-top: 24px;
  min-height: 90vh;
}
.padding_bottom {
  padding-bottom: 37px;
}
.content {
  width: 1128px;
  margin: 0 auto;
  background: #fff;
  border-radius: 8px;
  display: flex;
  padding: 24px;
}
.leftBox{
  flex: 1;
  width: 0;
}
.rightBox{
  width: 272px;
  margin-left: 24px;
}
</style>
