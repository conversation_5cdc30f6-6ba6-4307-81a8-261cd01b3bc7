<template>
  <div class='container-box channel-box'>
    <el-row>
      <el-col :lg='16' :md='16' :sm='18' :xl='16' :xs='24' class='page-left'>
        <div class='channel-leftBox'>
          <div v-if='bannerData.length > 0' class='block'>
            <el-carousel :arrow="bannerData.length > 1 ? 'hover' : 'never'" indicator-position='none'>
              <el-carousel-item v-for='item in bannerData' :key='item.adId'>
                <div class='banner_box' :title="item.name">
                  <img :class="item.extras ? 'cursor' : ''" :src='$tool.compressImg(item.image,793,264)' alt=''
                       class='img_cover'
                       @click='jumpBannerFun(item)'/>
                  <div class='mask fontSize18'>
                    {{ item.name }}
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div class='channel-title'>
            <p class='flex_start flex_align_center'>
              <span class='fontWeight'>频道</span>
              <span style='margin: 0 6px'>·</span>
              <span>{{ $route.query.channelTitle }}</span>
              <span v-if='false' class='subscribe flex_start flex_align_center'>
                <svg-icon className='tab_icon' iconClass='subscribe'></svg-icon>订阅
              </span>
              <span
                v-if='false'
                class='subscribe flex_start flex_align_center'
                style='background:  #E3F1FA; color: #0581ceff'
              >
                <svg-icon className='tab_icon' iconClass='subscribeok'></svg-icon
                >已订阅</span
              >
            </p>
          </div>
          <div class='channel-content'>
            <div class='column-content'>
              <ul class='channel-content-list flex_start flex_align_center flex_warp'>
                <li
                  v-for='item in nodeDataList'
                  :key='item.id'
                  :class='{"item-active" : parseInt(dataType) === parseInt(item.id) ,"item-active-column" : parseInt(columnActive)  === parseInt(item.id)}'
                  class='item flex_start flex_align_center userselect_none'
                  @click='changeActiveType(item.id,"channel",item.diplayCondition,item.number,item.code)'
                  @mouseleave='subListShow = false'
                  @mouseover='item.id === 0 ? ( subListShow = true) : null'
                >
                  {{ item.name }}
                  <i
                    v-if='item.id === 0'
                    class='el-icon-caret-bottom fontWeight'
                    style='font-size: 10px; color: #0483cf'
                  ></i>
                  <transition name='el-fade-in-linear'>
                    <div
                      v-if='item.id === 0 && subListShow'
                      class='classifivation_list_sub_selection'
                    >
                      <div
                        v-for='subList in latestAndEssence'
                        :key='subList.id'
                        :class='{ is_active: subList.filterType === essencesActive }'
                        class='classifivation_list_sub_selection_list fontSize14'
                        @click.stop='changeActiveType(subList.filterType,"essences","all")'
                      >
                        {{ subList.name }}
                      </div>
                    </div>
                  </transition>
                </li>
              </ul>
              <transition mode='out-in' name='slideTop'>
                <div v-if='secondNodeDataList && secondNodeDataList.length >=1'
                     class='second-column-box userselect_none'>
                  <Divider></Divider>
                  <ul class='second-column-content-list  flex_start flex_align_center'>
                    <li v-for='item in secondNodeDataList'
                        :key='item.id' :class='{"is_active":secondActive === item.id, "item cursor": true}'
                        @click='changeActiveType(item.id,"secondNode","aibrain",item.number,"CompileList")'>
                      {{ item.name }}
                    </li>
                  </ul>
                </div>
              </transition>
            </div>
            <!--  栏目内容  -->
            <div class='channel-content-div'>
              <keep-alive>
                <component :is='channelActive' :key='channelActive'
                           :informationData='informationData.list'></component>
              </keep-alive>
            </div>
            <div class='channel-content-all'>
              <Empty :loading='loading'
                     :no-more='!loading && informationData.list === 0'/>
              <el-pagination
                v-if='!loading'
                :current-page.sync='currentPage'
                :hide-on-single-page='$store.state.hideOnSinglePage'
                :layout='$store.state.layout'
                :page-size='pageSizeNum'
                :pager-count='$store.state.pager_count'
                :total='informationData.page.totalCount'
                background
                small
                style='text-align: center; margin-bottom: 10px'
                @current-change='handleCurrentChange'
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :sm='6' :xl='8' :xs='24' class='page-right'>
        <div class='channel-rightBox'>
          <!--hot channel Start-->
          <hot-channel v-if='hotChannelData.length>0' :hotChannelData='hotChannelData' title='热门频道'></hot-channel>
          <!--End-->
          <literature :literatureNewsData.sync='literatureData'></literature>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import {Divider, TabPane, Tabs} from 'element-ui'
import {getSlotContent} from "../../../../api/banner/banner";
import Empty from '@/components/UI/Empty/Empty'
import literature from '@/components/PageComponents/Literature/Literature'
import HotChannel from '@/components/PageComponents/HotChannel/HotChannel'
import {literatureNews} from '@/api/article'
import InformationList from '@/components/PageComponents/InformationList/InformationList'
import {
  getChannelDataList,
  getInfoList,
  getMeetings,
  getNode,
  getWebApiHotChannel,
  informationFlow
} from '@/api/channel'
import InfoList from '@/components/PageComponents/DataList/InfoList/InfoList' // 资讯列表
import MeetingList from '@/components/PageComponents/DataList/MeetingList/MeetingList'
import CaseList from '@/components/PageComponents/DataList/CaseList/CaseList'
import CloudClassroomList from '@/components/PageComponents/DataList/CloudClassroomList/CloudClassroomList'
import CompileList from '@/components/PageComponents/DataList/CompileList/CompileList'
import brandAdJump from "../../../../assets/helpers/brandAdJump";
import ElabList from '@/components/PageComponents/DataList/ElabList'

export default {
  async asyncData({app, params, error, store, query, req}) {
    const channelList = [
      {id: 0, name: '最新', code: 'InformationList', diplayCondition: 'all'},
      {id: 1, name: '资讯', code: 'InfoList', diplayCondition: 'all'},
      {id: 2, name: '会议', code: 'MeetingList', diplayCondition: 'all'},
      {id: 3, name: '病例', code: 'CaseList', diplayCondition: 'all'},
      {id: 4, name: '课程', code: 'CloudClassroomList', diplayCondition: 'all'},
      {id: 8, name: '手术复盘', code: 'ElabList', diplayCondition: 'all'},
      {id: 5, name: '编译', code: 'CompileList', diplayCondition: 'all'}
    ]
    const latestAndEssence = [
      {
        id: 1,
        name: '最新',
        code: 'latest',
        filterType: null
      },
      {
        id: 2,
        name: '精华',
        code: 'essence',
        filterType: 1
      },
      {
        id: 3,
        name: '品牌认证',
        code: 'bmsAuth',
        filterType: 2
      }
    ] // 最新和精华切换
    const channelId = query.channelId  // 频道id
    const [literatureData, bannerData, hotChannelData] = await Promise.all([
      app.$axios.$request(
        literatureNews({pageNo: 1, pageSize: store.state.article_count})
      ),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: `app_channel_${channelId}`,
        })
      ),
      app.$axios.$request(
        getWebApiHotChannel()
      )
    ])
    // 如果是神经科学得话 请求神经科学下面得栏目接口
    let nodeDataList      // 栏目
    let current           // 当前栏目下标
    let channelActive     // 当前显示组件code
    let informationData   // 列表数据
    let pageSizeNum = store.state.channel_count       // 显示数据  个别栏目展示的条目不同
    if (parseInt(query.channelId) === 22) {
      nodeDataList = await app.$axios.$request(getNode({number: 'aibrain'}))  // 获取神经科学栏目
      if (nodeDataList.result) {
        let newArr = [
          {id: 6, name: '最新', code: 'InformationList', diplayCondition: 'aibrain', number: 'aibrain'},
          {id: 7, name: '会议', code: 'MeetingList', diplayCondition: 'aibrain', number: 'aibrain'}
        ]
        nodeDataList.result.children.forEach((item) => {
          newArr.push(
            {
              id: item.id,
              name: item.name,
              code: `CompileList`,
              diplayCondition: 'aibrain',
              number: item.number
            }
          )
        })
        nodeDataList = newArr
      }
      // 获取神经科学列表内容
      informationData = await app.$axios.$request(getChannelDataList({
        userId: store.state.auth.user.id,   // 平台 T 介入 F 神外  B 神经科学/脑科学（为空默认神经科学）
        pageNo: 1,
        pageSize: store.state.channel_count
      }))
      current = nodeDataList.findIndex(item => item.id === (query.columnActive ? Number(query.columnActive) : 0))
      channelActive = nodeDataList[0].code // 根据id 切换组件
    } else {
      switch (parseInt(query.dataType)) {
        case 2: {
          // 会议
          pageSizeNum = 15
          break
        }
        case 4: {
          // 云课堂
          pageSizeNum = 12
          break
        }
        default: {
          pageSizeNum = store.state.channel_count
        }
      }
      nodeDataList = channelList // 如果是神经科学就用神经的栏目 如果不是 就用正常栏目
      informationData = await app.$axios.$request(
        informationFlow({
          channelId,
          filterType: query.essencesActive ? query.essencesActive : null,
          dataType: query.dataType ? query.dataType : 0,
          userId: store.state.auth.user.id,
          pageNo: query.currentPage ? query.currentPage : 1,
          pagesize: pageSizeNum
        })
      )

      channelList[0].name = !query.essencesActive ? '最新' : query.essencesActive === '1' ? '精华' : '品牌认证'
      current = channelList.findIndex(item => item.id === (query.dataType ? Number(query.dataType) : 0))
      channelActive = channelList[current].code// 根据id 切换组件
    }
    return {
      channelId,
      channelName: null,// 频道名称
      channelList,
      latestAndEssence,
      pageSizeNum,      // 每页显示几条 栏目不同显示条数不同
      literatureData: literatureData.list,
      informationData,
      channelActive,// 频道组件切换
      dataType: query.dataType ? query.dataType : 0,// 频道亚专业选中id [全部]
      essencesActive: query.essencesActive ? query.essencesActive : null,// 频道是否选中精华 [全部]
      currentPage: query.currentPage ? parseInt(query.currentPage) : 1           // 分页选中页数 [全部,神经科学]
      , bannerData: bannerData.list
      , hotChannelData: hotChannelData.list      // 热门频道
      , nodeDataList   // 频道下的栏目
      , secondNodeDataList: [] //  神经科学下得栏目 栏目二 默认是最新 最新没有第二栏目 所以是空
      , secondActive: null     //  神经科学第二栏目 选中状态
      , columnActive: 6     //  神经科学第一栏目 选中状态id  6是定死的最新id 因为不同栏目需请求不同接口 所以这里不做持久化处理
      , numberActive: 'aibrain'   // 栏目标识选中
    }
  },
  head() {
    return {
      title: this.$route.query.channelTitle,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$route.query.channelTitle
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$route.query.channelTitle
        }
      ]
    }
  },
  // 动态参数校验
  name: 'ChannelPage',
  components: {
    Empty,
    literature,
    HotChannel,
    Tabs,
    TabPane,
    InformationList,
    InfoList,
    MeetingList,
    CaseList,
    CloudClassroomList,
    CompileList,
    Divider,
    CollapseTransition,
    ElabList
  },
  watchQuery: ['channelId'],
  data() {
    return {
      loading: false,
      subListShow: false // 最新精华显示隐藏
    }
  },
  mounted() {
    document.body.scrollTop = 0
    document.documentElement.scrollTop = 0
  },
  watch: {
    $route: {
      deep: true,    // 加上此配置之后，watch即可以在首次进入或刷新之后执行handler （），即初始化即可执行监听
      handler(val, form) {
        if (val.query.channelId !== form.query.channelId) {
          document.body.scrollTop = 0
          document.documentElement.scrollTop = 0
        }
        this.dataType = val.query.dataType            // 监听类型id
      }
    }
  },
  methods: {
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    /**
     * 切换选中类型
     */
    async changeActiveType(state, type, column, number, code) {
      if (type !== 'currentChange') {
        this.loading = true
        this.informationData.list = []
      }
      if (column === 'all') {
        /**
         * 适用于所有频道, 通过不同类型 不同精华 不同分页拿不同数据
         */
        let current
        switch (type) {
          // 切换类型
          case 'channel': {
            this.dataType = state
            this.currentPage = 1  // 切换类型 分页到1
            current = this.channelList.findIndex(item => item.id === state) // 找到下标
            break
          }
          // 切换精华
          case 'essences': {
            this.channelList[0].name = state === null ? '最新' : state === 1 ? '精华' : '品牌认证'
            this.essencesActive = state
            this.currentPage = 1    // 切换精华 分页到1
            break
          }
          case 'currentChange': {
            this.currentPage = parseInt(state)
            const listBox = document.querySelector('.channel-title')
            listBox ? listBox.scrollIntoView({block: 'start', behavior: 'smooth'}) : null
            break
          }
        }
        switch (parseInt(this.dataType)) {
          case 2: {
            this.pageSizeNum = 15
            break
          }
          case 4: {
            this.pageSizeNum = 12
            break
          }
          default: {
            this.pageSizeNum = this.$store.state.channel_count
          }
        }
        this.$axios.$request(
          informationFlow({
            channelId: this.channelId,
            filterType: this.essencesActive,
            dataType: this.dataType,
            userId: this.$store.state.auth.user.id,
            pageNo: this.currentPage,
            pagesize: this.pageSizeNum
          })
        ).then(res => {
          this.loading = false
          if (res && res.code === 1) {
            this.informationData = res
            type === 'channel' ? this.channelActive = this.channelList[current].code : null  // 根据id 切换组件
            this.$router.push({
              path: `/channel/home?channelId=${this.channelId}&channelTitle=${this.$route.query.channelTitle}&mpId=${this.$route.query.mpId}&ocsId=${this.$route.query.ocsId}`,
              query: {dataType: this.dataType, essencesActive: this.essencesActive, currentPage: this.currentPage}
            })
          }
        })
      } else if (column === 'aibrain') {
        /**
         * 只适用于频道的神经科学
         * 首先 判断number 是否有 有number的去请求他的二级目录 有 没有number 或者 number是aibrain的没有二级目录
         * 如果type 是channel 选中状态赋值id  如果是分页点击 那么滑动到上面
         * 判断如果code是会议的话 那么请求会议接口 赋值 然后切换组件
         * 判断如果code是编译类型的话请求列表接口   这里可能是二级目录点击的 所以二级目录点击的话 type 加了一个二级目录标识 把选中状态给到secondActive
         * 判断如果code是最新也就是第一个栏目的话 请求接口
         */
        this.numberActive = number      // 栏目标识
        number === 'aibrain' ? this.secondNodeDataList = [] : null
        number && number !== 'aibrain' && type !== 'secondNode' && type !== 'currentChange' ? this.$axios.$request(getNode({number})).then(res => {
          this.secondNodeDataList = res.result.children // 如果是二级导航 就不赋值了
        }) : null // 获取神经科学栏目
        this.currentPage = 1 // 默认页数是一
        this.secondActive = null
        this.pageSizeNum = this.$store.state.channel_count  // 默认是8条
        type === 'channel' ? this.columnActive = state : null
        type === 'secondNode' ? this.secondActive = state : null
        type === 'currentChange' ? this.currentPage = state : 1
        if (type === 'currentChange') {
          const listBox = document.querySelector('.channel-title')
          listBox ? listBox.scrollIntoView({block: 'start', behavior: 'smooth'}) : null
        }
        if (code === 'MeetingList') {
          this.pageSizeNum = 15
          this.$axios.$request(getMeetings({
            siteType: 'B',   // 平台 T 介入 F 神外  B 神经科学/脑科学（为空默认神经科学）
            pageNo: this.currentPage,
            pageSize: 15
          })).then(res => {
            this.loading = false
            if (res && res.code === 1) {
              // 数据重组
              let newArray = {code: 1, list: [], page: res.page}
              res.list.forEach((item) => {
                newArray.list.push({type: 'meeting', meeting: item})
              })
              this.informationData = newArray
              this.channelActive = code // 切换组件
            }
          })
        } else if (code === 'CompileList') {
          number ? this.$axios.$request(getInfoList({
            number,   // 平台 T 介入 F 神外  B 神经科学/脑科学（为空默认神经科学）
            pageNo: this.currentPage,
            pageSize: this.$store.state.channel_count
          })).then(res => {
            this.loading = false
            if (res && res.code === 1) {
              // 数据重组
              let newArray = {code: 1, list: [], page: res.page}
              res.list.forEach((item) => {
                newArray.list.push({type: 'info', info: item})
              })
              this.informationData = newArray
              this.channelActive = code // 切换组件
            }
          }) : null
        } else if (code === 'InformationList') {
          number ? this.$axios.$request(getChannelDataList({
            userId: this.$store.state.auth.user.id,   // 平台 T 介入 F 神外  B 神经科学/脑科学（为空默认神经科学）
            pageNo: this.currentPage,
            pageSize: this.$store.state.channel_count
          })).then(res => {
            this.loading = false
            if (res && res.code === 1) {
              this.informationData = res
              this.channelActive = code // 切换组件
            }
          }) : null
        }
      }
    },
    /**
     *  分页
     */
    handleCurrentChange(item) {
      parseInt(this.$route.query.channelId) === 22 ?
        this.changeActiveType(item, 'currentChange', 'aibrain', this.numberActive, this.channelActive) :
        this.changeActiveType(item, 'currentChange', 'all')
    }
  }
}
</script>
<style>
.slideTop-enter-active,
.slideTop-leave-active {
  transition: all .6s ease;
  position: relative;
}

.slideTop-enter {
  opacity: 0;
}

.slideTop-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: all .1s ease;
  position: relative;
}

.slide-enter {
  opacity: 0;
}

.slide-leave-to {
  opacity: 0;
}
</style>
<style lang='less' scoped>
@import "./index";

</style>
