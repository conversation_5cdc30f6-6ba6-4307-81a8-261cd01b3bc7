/*小屏时结束*/
/*中屏时开始*/
@media screen and (max-width: 1199px) {
  .content-item {
    .item-right {
      .info {
        margin-bottom: 20px !important;
      }

      .label-box {
        margin-bottom: 20px !important;
      }
    }
  }

  .meeting-box {
    .meeting-item {
      .title {
        font-size: 14px !important;
      }
    }
  }

  .case-box {
    .top-right {
      p {
        font-size: 12px !important;
      }

      .label-box {
        padding: 10px 0 15px !important;
      }
    }
  }

  .big-boxbox {
    .special-item {
      margin-bottom: 10px !important;

      .item-left {
        max-width: 120px !important;
      }

      .item-right {
        .title {
          font-size: 12px !important;
        }
      }
    }
  }

  .channel-rightBox {
    .box-item {
      li {
        padding: 2px 15px !important;
      }
    }
  }
}

/*中屏时结束*/
/deep/ .el-carousel__container {
  border-radius: 6px 6px 6px 6px;
  height: 264px !important;
  overflow: hidden;

  .el-icon-arrow-right:before {
    font-family: 'iconfontNew';
    content: '\e624';
  }

  .el-icon-arrow-left:before {
    font-family: 'iconfontNew';
    content: '\e626';
  }
}

/deep/ .el-carousel__arrow {
  background: rgba(31, 45, 61, 0.3);
  font-size: 16px;
  line-height: 36px;
  opacity: 0.8;

  &:hover {
    background: #0581ce;
    color: white;
    opacity: 1;
  }
}

.banner_box {
  height: 100%;
  position: relative;

  .mask {
    position: absolute;
    width: 100%;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    padding: 6px 14px;
    min-height: 44px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    line-height: 1.2;
  }
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.channel-box {
  padding: 24px 0 32px;

  /deep/ .el-tabs__nav-wrap::after {
    display: none;
  }

  /deep/ .el-tabs__active-bar {
    display: none;
  }

  /deep/ .el-tabs__header {
    margin: 0;
  }

  /deep/ .el-tabs__item {
    padding: 0 10px;
  }

  .channel-leftBox {
    box-sizing: border-box;

    /deep/ .el-carousel__container {
      height: 262px;
    }

    .block {
      padding-bottom: 30px;
    }

    .channel-title {
      font-size: 20px;
      line-height: 20px;
      margin-bottom: 16px;

      .tab_icon {
        width: 9px;
        height: 9px;
        margin-right: 3px;
      }

      span {
        line-height: 20px;
      }

      .subscribe {
        height: 20px;
        padding: 0 6px;
        color: white;
        font-size: 12px;
        border-radius: 4px;
        box-sizing: border-box;
        background: #0581ceff;
        line-height: 20px;
        margin-left: 14px;
        cursor: pointer;
        text-align: center;
      }
    }

    .channel-content {
      .channel-content-list {
        .item-active {
          color: #0581ce !important;
          font-weight: bold;
        }

        .item-active-column {
          color: #0581ce !important;
          font-weight: bold;
        }

        .item {
          font-size: 18px;
          color: #666666;
          line-height: 18px;
          margin-right: 30px;
          position: relative;

          &:hover {
            cursor: pointer;
            color: #0581ce;
            font-weight: bold;
          }


          .classifivation_list_sub_selection {
            z-index: 1000;
            position: absolute;
            left: 50%;
            top: calc(100% + 7px);
            transform: translateX(-50%);
            min-width: 80px;
            text-align: center;
            background: #ffffff;
            box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.15);
            border-radius: 4px;

            &::after {
              content: '';
              display: block;
              width: 100%;
              height: 7px;
              position: absolute;
              left: 0;
              top: -7px;
            }

            .classifivation_list_sub_selection_list {
              font-weight: 400;
              line-height: 25px;
              color: #666666;
            }

            .is_active {
              background: #dbf1ff;
              color: #0581ce !important;
            }
          }
        }
      }

      .column-content {
        margin-bottom: 20px;

        /deep/ .el-divider--horizontal {
          margin: 15px 0 !important;
        }
      }

      .second-column-content-list {

        .item {
          color: #666666;
          line-height: 19px;
          font-size: 16px;
          margin-right: 30px;

          &:hover {
            font-weight: bold;
            color: #202020 !important;
          }
        }

        .is_active {
          font-weight: bold;
          color: #202020 !important;
        }
      }

      .content-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 35px;

        .item-left {
          img {
            border-radius: 6px;
            max-width: 220px;
            margin-right: 10px;
            display: block;
          }
        }

        .item-right {
          .title {
            font-size: 16px;
            margin-bottom: 12px;
          }

          .info {
            font-size: 14px;
            color: #708aa2ff;
            margin-bottom: 46px;
          }

          .con {
            font-size: 12px;
            color: #708aa2ff;

            span {
              display: inline-block;
              text-align: right;
              position: absolute;
              right: 0;
            }
          }

          .con1 {
            font-size: 12px;
            color: #708aa2ff;

            span:nth-child(1) {
              font-size: 14px;
              color: #dd5a42ff;
              margin-right: 20px;
            }

            span:nth-child(2) {
              margin-right: 20px;
            }

            span:nth-child(3) {
              position: absolute;
              right: 0;
            }
          }

          .label-box {
            margin-bottom: 46px;

            .label {
              display: inline-block;
              border-radius: 11px;
              background: #ebedefff;
              color: #0581ceff;
              font-size: 14px;
              line-height: 12px;
              padding: 3px 6px;
              margin-right: 10px;
              margin-bottom: 5px;
            }
          }
        }
      }

      .consulting {
        .con2 {
          font-size: 12px;
          color: #708aa2ff;

          span {
            margin-left: 10px;
          }
        }
      }

      .channel-content-all {
        margin-top: 30px;
      }
    }

    .meeting-box {
      .meeting-item {
        border-radius: 6px;
        overflow: hidden;
        background: #fbfbfbff;
        position: relative;
        margin-bottom: 12px;

        img {
          display: block;
          max-width: 100%;
        }

        .title {
          font-size: 16px;
          color: #000000ff;
          margin: 12px 10px 10px 10px;
        }

        .con {
          color: #708aa2ff;
          font-size: 12px;
          padding: 0 10px 10px;

          span:nth-child(1) {
            margin-right: 10px;
          }
        }

        .top-cpn {
          display: inline-block;
          padding: 1px 6px;
          font-size: 12px;
          color: white;
          position: absolute;
          top: 5px;
          right: 5px;
          border-radius: 8px;
        }
      }
    }

    .case-box {
      border-radius: 6px;
      background: #ffffffff;
      border: 1px solid #e5edf1ff;
      overflow: hidden;
      padding: 13px;
      margin-bottom: 20px;

      .top-left {
        max-width: 50%;
        float: left;

        img {
          max-width: 100%;
          vertical-align: middle;
          border-radius: 6px;
        }
      }

      .top-right {
        max-width: 50%;
        float: left;
        padding-left: 10px;
        box-sizing: border-box;

        p {
          font-size: 16px;
          line-height: 26px;
          font-weight: 540;
        }
      }

      .label-box {
        padding: 14px 0 18px;

        li {
          display: inline-block;
          border-radius: 11px;
          background: #f0f9ffff;
          color: #0a83ceff;
          font-size: 12px;
          padding: 3px 8px;
          margin: 0px 6px 10px 0;
        }
      }

      .author-box {
        padding-top: 12px;
        border-top: 1px solid #e5edf1ff;
        overflow: hidden;

        .author-left {
          float: left;

          img {
            max-width: 22px;
            max-height: 22px;
            vertical-align: middle;
            border-radius: 50%;
            margin-right: 8px;
          }

          span {
            color: #333333ff;
            font-size: 14px;
          }
        }

        .author-right {
          float: right;
          font-size: 12px;
          color: #708aa2ff;
        }
      }
    }

    .Cloud-box {
      border-radius: 6px;
      background: #fbfbfbff;
      overflow: hidden;
      margin-bottom: 20px;
      position: relative;

      img {
        max-width: 100%;
      }

      .title {
        font-size: 16px;
        margin: 10px 0;
        padding: 0 10px;
        box-sizing: border-box;
      }

      .bottom {
        font-size: 12px;
        color: #708aa2ff;
        margin-bottom: 16px;
        padding: 0 10px;
        box-sizing: border-box;
        line-height: 22px;

        span:nth-child(1) {
          margin-right: 10px;
        }

        span:nth-child(3) {
          font-size: 14px;
          color: #dd5a42ff;
        }
      }

      .study {
        cursor: pointer;
        position: absolute;
        left: 5px;
        top: 5px;
        display: inline-block;
        color: white;
        padding: 2px 8px;
        font-size: 12px;
        border-radius: 8px;
        background: #0000007f;
      }
    }

    .guide-box {
      border-radius: 6px;
      background: #fbfbfbff;
      padding: 13px 14px 11px;
      margin-bottom: 20px;

      .title {
        font-size: 16px;
        margin-bottom: 12px;
      }

      .info {
        font-size: 14px;
        color: #708aa2ff;
        margin-bottom: 35px;
      }

      .bottom {
        font-size: 12px;
        color: #708aa2ff;

        span:nth-child(1) {
          margin-right: 22px;
        }
      }
    }

    .special {
      .big-boxbox {
        border-radius: 6px;
        background: #ffffffff;
        border: 1px solid #dfe6eaff;
        padding: 12px 12px 17px 12px;
        margin-bottom: 20px;

        .special-item {
          display: flex;
          justify-content: flex-start;
          margin-bottom: 19px;

          .item-left {
            max-width: 150px;
            margin-right: 10px;

            img {
              max-width: 100%;
              border-radius: 6px;
              display: block;
            }
          }

          .item-right {
            .title {
              font-size: 16px;
              margin-bottom: 8px;
            }

            .bottom {
              font-size: 12px;
              color: #708aa2ff;
              line-height: 22px;

              span:nth-child(2) {
                cursor: pointer;
                display: inline-block;
                border-radius: 11px;
                background: #0581ceff;
                padding: 0px 6px;
                color: white;
                float: right;
              }
            }
          }
        }

        .label-box {
          .item {
            color: #44627dff;
            font-size: 14px;
            margin-bottom: 10px;
            position: relative;
            padding-left: 15px;
          }

          .item:last-child {
            margin-bottom: 0;
          }

          .item::after {
            content: '';
            display: block;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: #44627dff;
            position: absolute;
            left: 0;
            top: 40%;
          }
        }
      }
    }
  }

  .channel-rightBox {
    .channel-box {
      padding: 15px 15px 3px;
      box-sizing: border-box;
      border-radius: 6px;
      background: #fbfbfbff;
      margin-bottom: 20px;

      .title {
        margin-bottom: 16px;

        img {
          max-width: 20px;
          vertical-align: middle;
        }

        span {
          font-size: 16px;
          margin-left: 10px;
        }
      }

      .box-item-phone {
        display: none;
      }

      .box-item {
        display: flex;
        justify-content: flex-start;
        max-width: 100%;
        flex-wrap: wrap;

        li {
          padding: 2px 35px;
          border-radius: 17px;
          border: 1px solid #0581ceff;
          margin-bottom: 12px;
          margin-right: 12px;

          img {
            max-width: 20px;
            vertical-align: middle;
            margin-right: 10px;
          }

          span {
            color: #333333ff;
            font-size: 16px;
          }
        }
      }
    }
  }
}

.channel-content-div {
  transition: all .3s;
}
