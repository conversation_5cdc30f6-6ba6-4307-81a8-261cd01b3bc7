<template>
  <div ref='containerbox' class='container-box details-con'>
    <!--Navigation Start-->
    <luckArticleTips/>
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item
        :to="{
                path: '/case',
                query: {
                  currentPage: $route.query.currentPage,
                  subSpecialtyId: $route.query.subSpecialtyId,
                  selectedFalg: $route.query.selectedFalg,
                  subSpecialtyName: $route.query.subSpecialtyName,
                },
              }"
      >
        病例
      </el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/case' }">正文</el-breadcrumb-item>
    </bm-breadcrumb>
    <!--End-->
    <el-row>
      <el-col :lg='16' :md='16' :sm='18' :xl='16' :xs='24'>
        <div class='details-con-left'>
          <!--text Start-->
          <el-row>
            <!--            <el-col :xl="3" :lg="3" :md="3" :sm="3" :xs="3" class="hidden-xs-only">-->
            <!--              <div class="zhanwei"></div>-->
            <!--            </el-col>-->
            <el-col :lg='24' :md='24' :sm='24' :xl='24' :xs='24'>
              <div class='title-box right-box'>
                <div class='line'>
                  <el-skeleton :loading='skeleFlag' :rows='1' animated>
                    <template slot='template'>
                      <el-skeleton-item
                        style='width: 80%; margin-bottom: 32px'
                        variant='text'
                      />
                      <el-skeleton-item
                        style='width: 30%; margin-bottom: 10px'
                        variant='text'
                      />
                    </template>
                    <p v-if='infoData.title' class='title'>
                      <svg-icon
                        v-if="infoData.essences === 'T'"
                        class-name='poneIcon cursor'
                        icon-class='jinghua'
                      ></svg-icon>
                      {{ infoData.title }}
                    </p>
                  </el-skeleton>
                  <p ref='detailDom' :class="openFlag ? '' : 'text-limit-2'" class='con'>
                    <span v-if='infoData.publishTime' style='margin-right: 15px'>
                      {{ timeStamp.timestamp_13(infoData.publishTime, 'yyy-mm-d-h-m-s') }}
                    </span>
                    <span v-if='infoData.creator' style='margin-right: 10px'>
                      {{ infoData.creator.realName }}
                    </span>
                    <span
                      v-for='item in infoData.subspecialtys'
                      :key='item.id'
                      class='con_lable_sub'
                    >
                      <span
                        v-for='itemChildren in item.children'
                        :key='itemChildren.id'
                        class='con_lable_sub_item'
                      >{{ item.name + '-' + itemChildren.name }}</span
                      >
                    </span>
                    <nuxt-link
                      v-for='item in infoData.productList'
                      :key='item.id'
                      target='_blank'
                      :to='`/bms/classify/${item.category.firstCategoryId || "-"}/product-details/${item.id}`'
                      class='con_lable_sub_productList'
                    >
                      <svg-icon className='productIcon' iconClass='product2'></svg-icon>
                      {{ item.name }}
                    </nuxt-link>
                  </p>
                  <p v-if='openBtn' class='open_sub cursor' @click='openFlag = !openFlag'>
                    {{ openFlag ? '收回' : '展开 >' }}
                  </p>
                </div>
                <ColumnBox></ColumnBox>
              </div>
            </el-col>
          </el-row>
          <no-ssr>
            <el-row class='article_box flex_start'>
              <div id='left_share' class='left-box hidden-xs-only'>
                <img alt='' src='~assets/images/comment.png' @click='discussJumpFun'/>
                <p>
                  <el-skeleton :loading='skeleFlag' :rows='1' animated>
                  <span class='fontSize16'>{{
                      infoData ? infoData.comments : ''
                    }}</span
                  >评论
                  </el-skeleton>
                </p>
                <div class='line'></div>
                <svg-icon
                  :iconClass="infoData.diggStatus === 'T' ? 'dianzan' : 'placedianzan'"
                  className='dianzanbox cursor'
                  @click='fabulousFun'
                ></svg-icon>
                <p>
                  <el-skeleton :loading='skeleFlag' :rows='1' animated>
                  <span class='fontSize16'>{{
                      infoData ? infoData.showDiggs : ''
                    }}</span
                  >点赞
                  </el-skeleton>
                </p>
                <div class='line'></div>
                <svg-icon
                  :iconClass='isCollect?"collectionok":"collectiono"'
                  className='dianzanbox cursor'
                  @click='collectFun'
                ></svg-icon>
                <p>
                  <el-skeleton :loading='false' :rows='1' animated>
                    <span class='fontSize16'>
                      {{
                        infoData ? infoData.favorites : ''
                      }}</span>收藏
                  </el-skeleton>
                </p>
                <div class='line'></div>
                <p style='margin-bottom: 10px'>分享至</p>
                <el-popover placement='right-start' title='扫码分享' trigger='click'>
                  <img :src='weixinUrl' alt=''/>
                  <img
                    slot='reference'
                    alt=''
                    src='~assets/images/weixin.png'
                    style='min-width: 38px'
                    @click="shareFun('weixin')"
                  />
                </el-popover>

                <img
                  alt=''
                  src='~assets/images/weibo.png'
                  style='min-width: 38px'
                  @click="shareFun('weibo')"
                />
                <img
                  alt=''
                  src='~assets/images/qqkongjian.png'
                  style='min-width: 38px'
                  @click="shareFun('qq')"
                />
              </div>
              <div class='left-boxzhanwei'></div>
              <div class='right-box twocon'>
                <el-skeleton :loading='skeleFlag' :rows='30' animated>
                  <div :class='!$store.state.auth.isLogged ? "hide-article" :""'>
                    <div
                      v-if='infoData.paragraphType === "O"'
                      id='fuwenben'
                      @click="previewHandler"
                      v-html='infoContent'
                    ></div>
                    <div
                      v-else
                      v-for='(item, index) in infoContent'
                      id='fuwenben'
                      :key='index'
                      class='content'
                      @click="previewHandler"
                      style='margin-bottom: 10px'
                    >
                      <div v-if='item.type === "txt"'>
                        <img v-if='item.resource' :src='item.resource' alt=''/>
                        <p v-if='item.text' v-html='item.text'></p>
                      </div>
                      <div v-if='item.type === "title"'>
                        <img v-if='item.resource' :src='item.resource' alt=''/>
                        <p v-if='item.text' v-html='item.text'></p>
                      </div>
                      <div v-if='item.type === "img"'>
                        <img v-if='item.resource' :src='item.resource' alt=''/>
                        <p v-if='item.text' v-html='item.text'></p>
                      </div>
                      <div v-if='item.type === "video"'>
                        <video
                          v-if='item.resource'
                          :poster='item.image'
                          :src='item.resource'
                          class='video'
                          controls='controls'
                          loop='-1'
                          muted='muted'
                          type='video/mp4'
                        >
                          <p>你的浏览器不支持video标签.</p>
                        </video>
                        <p v-if='item.text' v-html='item.text'></p>
                      </div>
                    </div>
                  </div>
                </el-skeleton>
                <HideArticle v-if='!$store.state.auth.isLogged'/>
                <div class='phone-fabulous'>
                  <b class='cursor' @click='fabulousFun'>
                    <img alt='' class='first' src='~assets/images/fabulous.png'/>
                    <span>点赞</span>
                  </b>
                  <span>分享至</span>
                  <el-popover
                    placement='right-start'
                    title='扫码分享'
                    trigger='click'
                    width='50'
                  >
                    <img :src='weixinUrl' alt='' style='width: 120px; text-align: center'/>
                    <img
                      slot='reference'
                      alt=''
                      class='second'
                      src='~assets/images/weixin.png'
                      @click="shareFun('weixin')"
                    />
                  </el-popover>
                  <!--                  <img src="~assets/images/weixin.png" alt=""  @click="shareFun('weixin')">-->
                  <img
                    alt=''
                    class='three'
                    src='~assets/images/qqkongjian.png'
                    @click="shareFun('qq')"
                  />
                  <img alt='' src='~assets/images/weibo.png' @click="shareFun('weibo')"/>
                </div>
                <!-- 评论 -->
                <Comments
                  :commentsApiType="'caseApi'"
                  :commentsDataList='commentsDataList'
                  :commentsPage='commentsPage'
                  :commentsType='0'
                  :moreFlag='moreFlag'
                  @getcomment='getInfoCommentsPage'
                ></Comments>
                <!-- End -->
              </div>
            </el-row>
          </no-ssr>
          <!--End-->
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :sm='6' :xl='8' :xs='24'>
        <div class='details-con-right'>
          <div v-if='infoData.creator' class='Text-author-box'>
            <el-skeleton :loading='skeleFlag' :rows='30' animated>
              <template slot='template'>
                <el-skeleton-item
                  style='
                    width: 78px;
                    height: 78px;
                    border-radius: 50%;
                    margin: 0 auto 10px;
                  '
                  variant='image'
                />
                <div style='text-align: center'>
                  <el-skeleton-item
                    class='pone'
                    style='width: 30%; margin: 0 auto 10px'
                    variant='text'
                  />
                </div>
                <div style='text-align: center'>
                  <el-skeleton-item
                    class='pone'
                    style='width: 30%; margin: 0 auto 10px'
                    variant='text'
                  />
                </div>
                <div class='flex' style='display: flex; justify-content: space-around'>
                  <el-skeleton-item
                    class='pone'
                    style='width: 30%; margin: 0 auto'
                    variant='text'
                  />
                  <el-skeleton-item
                    class='pone'
                    style='width: 30%; margin: 0 auto'
                    variant='text'
                  />
                </div>
              </template>
              <template>
                <div v-if="infoData.creator && typeof infoData.creator === 'object'">
                  <div class='user-box'>
                    <div class='author-info-popup usercoverbox-div' @mouseout='num = -1'
                         @mouseover='num = infoData.creator.id'>
                      <div class='usercoverbox-div author-info-popup-hover cursor'
                           @click="$store.dispatch('jumpUserCenterPageHandler',infoData.creator.id)">
                        <div
                          v-if='infoData.creator.avatarAddress'
                          class='img_box'
                        >
                          <img
                            :src='$tool.compressImg(infoData.creator.avatarAddress,78,78)'
                            alt=''
                            class='img_cover'
                          />
                        </div>
                        <svg-icon
                          v-else
                          className='imgbox_amll cursor'
                          iconClass='signinavatar'
                        ></svg-icon>
                      </div>
                      <div v-show='num === infoData.creator.id'>
                        <InformationPopUp :id='infoData.creator.id'
                                          :author-name='infoData.creator.realName' :fans='infoData.creator.fans'
                                          :follow='infoData.followStatus'
                                          :follows='infoData.creator.follows'
                                          :head-image='infoData.creator.avatarAddress'
                                          :is-auth='infoData.creator.isAuth' :post-num='infoData.creator.userPostNum'
                                          :title='infoData.creator.title'
                                          :user-diggs='infoData.userDiggs'
                                          @followFn='followFun(infoData.creator.id)'/>
                      </div>
                    </div>
                    <p class='user_name text-limit-1' style='line-height: 26px'>
                      {{ infoData.creator.realName }}
                    </p>
                    <div v-if='parseInt(infoData.creator.id) !== parseInt($store.state.auth.user.id)'
                         :class='{"followed" : infoData.creator.isFollow}'
                         class='addfollow userselect_none'
                         @click='followFun(infoData.creator.id)'>
                      <span v-if='!infoData.creator.isFollow' class='flex_center flex_align_center'>
                        <svg-icon className='tab_icon flex_shirk' iconClass='subscribe'></svg-icon>
                        关注
                      </span>
                      <span v-else>已关注</span>
                    </div>

                    <div class='addbox' style='display: none'>
                      <div class='article'>
                        <p>202</p>
                        <p>文章数</p>
                      </div>
                      <div class='follow'>
                        <p>202</p>
                        <p>关注数</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class='user-box-Multiple'>
                  <div
                    v-for='(user, index) in infoData.creator'
                    :key='user + index'
                    class='item'
                  >
                    <div class='imgbox'>
                      <img
                        v-if='user.avatarAddress !== null'
                        :src='user.avatarAddress'
                        alt=''
                        class='img_cover'
                      />
                      <svg-icon
                        v-if='user.avatarAddress === null'
                        className='imgbox_amll cursor'
                        iconClass='signinavatar'
                      ></svg-icon>
                    </div>
                    <p class='text-limit-1' style='line-height: 26px'>
                      {{ user.realName }}
                    </p>
                    <div v-if='false' class='addfollow'><span>+</span>关注</div>
                  </div>
                </div>
              </template>
            </el-skeleton>
            <!--consultation Start-->
            <div
              v-if='newsInfo && newsInfo.length <= 0'
              class='newsInfoMask'
              style='margin-top: 40px'
            ></div>
            <div v-else class='consultation_box'>
              <div class='title'>最新发布</div>
              <ul class='consu-item'>
                <el-skeleton :count='4' :loading='skeleFlag' :rows='1' animated>
                  <template slot='template'>
                    <li>
                      <p class='item-title text-limit-2'>
                        <el-skeleton-item
                          style='width: 80%; margin-bottom: 10px'
                          variant='text'
                        />
                      </p>
                      <p class='time'>
                        <el-skeleton-item style='width: 50%' variant='text'/>
                      </p>
                    </li>
                  </template>
                  <template>
                    <li
                      v-for='vn in newsInfo'
                      :key='vn.id'
                      style='cursor: pointer'
                      @click='jumpDetailsPageFun(vn.id)'
                    >
                      <p class='item-title text-limit-2'>
                        {{ vn.title }}
                      </p>
                      <p class='time'>
                        {{ timeStamp.timestamp_13(vn.publishTime, 'yyyy-mm-d') }}
                      </p>
                    </li>
                  </template>
                </el-skeleton>
              </ul>
            </div>
            <!--End-->
            <div
              v-if='infoData.authors && infoData.authors.length < 2'
              v-show='false'
              class='more-box'
            >
              阅读更多内容
            </div>
          </div>

          <!--Related Start-->
          <div v-if='relatedArticles && relatedArticles.length > 0' class='Related_box'>
            <div class='title'>相关文章</div>
            <ul class='Related-item'>
              <el-skeleton :count='4' :loading='skeleFlag' :rows='1' animated>
                <template slot='template'>
                  <li>
                    <div
                      class='left'
                      style='width: 100px; height: 100px; margin-right: 20px'
                    >
                      <el-skeleton-item
                        style='width: 100%; height: 100%'
                        variant='image'
                      />
                    </div>
                    <div class='right' style='width: 60%; margin-bottom: 30px'>
                      <el-skeleton-item
                        style='width: 70%; margin: 10px 0px'
                        variant='text'
                      />
                      <el-skeleton-item
                        style='width: 100%; margin: 10px 0px'
                        variant='text'
                      />
                    </div>
                  </li>
                </template>
                <template>
                  <li
                    v-for='item in relatedArticles'
                    :key='item.id'
                    @click='jumpDetailsPageFun(item.id)'
                  >
                    <div v-if='item.cover' class='left'>
                      <img :src='$tool.compressImg(item.cover,142,80)' alt='' class='img_cover'/>
                    </div>
                    <div class='right'>
                      <p class='item-title text-limit-2'>
                        {{ item.title }}
                      </p>
                      <p v-if='item.publishTime' class='con'>
                        {{ timeStamp.timestamp_13(item.publishTime) }}
                      </p>
                    </div>
                  </li>
                </template>
              </el-skeleton>
            </ul>
          </div>
          <!--End-->
        </div>
      </el-col>
    </el-row>
    <el-image
      ref="preview"
      style="width: 100px; height: 100px;display: none"
      :preview-src-list="srcList">
    </el-image>
  </div>
</template>

<script>
import qrcade from 'qrcode'
import {articleDigg, followerArticle, getArticleCommentsPage, getAssoInfos, getMpArticle} from '@/api/case'
import Comments from '@/components/Comments/Comments.vue'
import {addCollect, isCollect, userInfo} from '@/api/user'
import {saveBrowsingHistory} from '@/api/browsing-history'
import InformationPopUp from '@/components/PageComponents/InfoPopup/InfoPopup'
import HideArticle from '@/components/page/detail/HideArticle/HideArticle'
import ColumnBox from '@/components/ArticleColumn/ColumnBox/index'
import luckArticleTips from '@/components/LuckyDrawPop/luckArticleTips' // 幸运中奖弹窗

export default {
  name: 'ConsultationDetails',
  components: {
    Comments,
    InformationPopUp,
    HideArticle,
    ColumnBox,
    luckArticleTips
  },
  async asyncData({app, params, error, store, query, req}) {
    // 获取咨询文章详情 以及咨询相关文章 以及 咨询评论内容 以及 咨询 最新发布*/
    const [request1, request2, request3] = await Promise.all([
      app.$axios.$request(
        getAssoInfos({
          articleId: query.id,
          limit: 10
        })
      ),
      app.$axios.$request(
        getMpArticle({
          articleId: query.id,
          userId: store.state.auth.user.id,
          articleType: 'U'
        })
      ),
      app.$axios.$request(
        getArticleCommentsPage({
          articleId: query.id,
          userId: store.state.auth.user.id,
          order: 0,
          pageNo: 1,
          pageSize: 5
        })
      )
    ])
    const request4 = store.state.auth.user.id ? await app.$axios.$request(isCollect({
      type: 3,
      contentId: query.id
    })) : {result: {isCollect: false}}

    app.head.title = request2.result.title
    const newsInfo = await app.$axios.$request(
      followerArticle({
        articleId: query.id,
        creatorId: request2.result.creator.id,
        pageNo: 1,
        pageSize: store.state.datalist_count
      })
    )

    const attrsNames = request2.result.subspecialtys ? request2.result.subspecialtys.map((item) => item.name + ',') : ''
    const authorNames = request2.result.creator.realName && request2.result.creator.realName !== '' ? request2.result.creator.realName + ',' : ''
    let keywordsName = request2.result.searchKeyWords || ''
    keywordsName = keywordsName !== '' ? keywordsName + ',' : ''


    return {
      relatedArticles: request1.list,
      infoData: request2.result,
      infoContent: request2.result.paragraphType === 'O' ? request2.result.content : JSON.parse(request2.result.content),
      commentsDataList: request3.result.list,
      commentsPage: request3.result.page,
      newsInfo: newsInfo.list,
      moreFlag: request3.result.list.length <= request3.result.page.totalCount,
      isCollect: request4.result.isCollect,
      keywordsName,
      authorNames,
      attrsNames
    }
  },
  data() {
    return {
      srcList: [],
      num: -1,   // 移入显示
      openBtn: false, // 展开收回是否显示
      openFlag: false, // 展开收回
      skeleFlag: false, // 骨架屏开关
      weixinUrl: '', // 微信二维码
      pageSize: 5,
      timer: null,
      clearTimeSet: null, // 所有评论轮询
      scroll: 0 // 浏览器滚动条位置
    }
  },
  head() {
    return {
      title: this.infoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇, ${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }
      ]
    }
  },
  computed: {},
  watch: {
    $route(to, from) {
      if (JSON.stringify(to.query) === '{}') {
        this.$router.back(-1)
      }
    },
    scroll: {
      handler: 'showTop'
    }
  },
  watchQuery(newQuery, oldQuery) {
    let detail
    return {
      detail
    }
  },
  beforeDestroy() {
    // clearInterval(this.clearTimeSet); // 清楚评论定时器
  },
  updated() {
  },
  mounted() {
    // 防止用户使用富文本里的文本框
    this.$nextTick(() => {
      let textareaListDom = document.querySelectorAll('#fuwenben textarea');
      for (let i = 0, len = textareaListDom.length; i < len; i++) {
        if (!textareaListDom[i].value) {
          textareaListDom[i].remove();
        } else {
          textareaListDom[i].readOnly = true;
        }
      }
    })

    // document.addEventListener('keydown', function (event) {
    //   return event.keyCode !== 123 || (event.returnValue = false)
    // })
    //
    // document.addEventListener('contextmenu', function (event) {
    //   // eslint-disable-next-line no-return-assign
    //   return event.returnValue = false
    // })

    this.$analysys.Browse_article({
      userid: String(this.$store.state.auth.user.id),
      articleid: String(this.$route.query.id),
      type: 'mp',
      articletitle: String(this.infoData.title)
    })


    /**
     *采集浏览记录
     */
    this.$store.state.auth.user.id ? this.$axios.$request(saveBrowsingHistory({
      loginUserId: this.$store.state.auth.user.id,
      contentSource: 3,
      contentId: this.$route.query.id,
      courseId: null,
      playDuration: null
    })) : null
    this.$analysys.view_case_page(
      this.infoData.creator.realName,
      this.infoData.showViews,
      String(this.infoData.id),
      this.infoData.title,
      this.infoData.creator.company,
      this.infoData.showDiggs,
      '',
      '',
      this.infoData.showComments,
      '视频图文',
      []
    )
    this.$nextTick(function () {
      this.openBtn = this.$refs.detailDom
        ? this.$refs.detailDom.clientHeight < this.$refs.detailDom.scrollHeight
        : false
      //  修改富文本样式
      const numbox = document.getElementById('fuwenben')
      const num = numbox !== null ? numbox.getElementsByTagName('textarea') : []
      for (let i = 0; i < num.length; i++) {
        if (!num[i].value || num[i].value === '') {
          num[i].style.cssText = 'display:none'
        }
      }
    })
    // 加入滚动事件
    window.addEventListener('scroll', this.getScroll)
  },
  methods: {
    previewHandler(event) {
      if (event?.target?.tagName === 'IMG' && event?.target.src) {
        this.srcList = [event?.target.src]
        setTimeout(() => {
          this.$refs.preview.clickHandler()
        }, 0)
      }
    },
    /**
     * 文章关注
     * @param newValue
     * @param oldValue
     */
    async followFun(id) {
      await this.$store.dispatch('follow', id)
      await this.$axios.$request(
        getMpArticle({
          articleId: this.$route.query.id,
          userId: this.$store.state.auth.user.id,
          articleType: 'U'
        })).then((res) => {
        this.infoData = res.result
      })
    },
    // 滚动条固定左侧分享
    showTop(newValue, oldValue) {
      let left_share = document.getElementById('left_share')
      let left_part_box = document.querySelector('.article_box ')
      let seat = document.querySelector('.left-boxzhanwei')
      let twocon = document.querySelector('.details-con')
      const left_share_Top = left_part_box?.offsetTop
      if (newValue > left_share_Top) {
        left_share.style.cssText = 'position: fixed;'
        seat.style.cssText = 'display:block'
      } else {
        left_share.style.cssText = 'position: static;'
        seat.style.cssText = 'display:none'
      }
      if (newValue > twocon.scrollHeight - left_share.scrollHeight) {
        left_share.style.cssText = 'position: static;'
        seat.style.cssText = 'display:none'
      }
    },
    getScroll() {
      this.scroll = document.documentElement.scrollTop || document.body.scrollTop
    },
    // 获取病例评论列表
    getInfoCommentsPage(pageSize, callback) {
      this.pageSize = pageSize // 页数
      this.$axios
        .$request(
          getArticleCommentsPage({
            articleId: this.$route.query.id,
            userId: this.$store.state.auth.user.id,
            order: 0,
            pageNo: 1,
            pageSize: this.pageSize
          })
        )
        .then((res) => {
          this.commentsPage = res.result.page
          this.commentsDataList = res.result.list
          this.moreFlag = this.commentsDataList.length <= res.result.page.totalCount
          callback ? callback(true) : null
        })
    },
    //文章点赞
    fabulousFun() {
      this.$analysys.like(
        '',
        '',
        '',
        '点赞原文',
        '',
        '',
        '',
        '',
        [],
        [],
        '病例',
        '',
        '',
        String(this.$route.query.id)
      )
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(async () => {
        await this.$axios
          .$request(
            articleDigg({
              articleId: this.$route.query.id,
              userId: this.$store.state.auth.user.id
            })
          )
          .then((res) => {
            if (res.code === 1) {
              if (res.result) {
                this.$toast('点赞成功')
              }
            }
          })
        await this.$axios
          .$request(
            getMpArticle({
              articleId: this.$route.query.id,
              userId: this.$store.state.auth.user.id,
              articleType: 'U'
            })
          )
          .then((res) => {
            this.infoData = res.result
          })
      }, 300)
    },

    //分享事件
    shareFun(data) {
      this.$store.state.auth.isLogged ? this.$axios.$request(userInfo()).then((res) => {
        if (res.code === 1) {
          this.$analysys.share(
            res.result.realName,
            data === 'weixin' ? '微信' : data === 'weibo' ? '微博' : 'QQ',
            '',
            '',
            res.result.company,
            this.infoData.title,
            '',
            [],
            [],
            '病例',
            this.infoData.creator.realName,
            '病例',
            res.result.title,
            this.infoData.title,
            this.infoData.creator.company,
            ''
          )
        }
      }) : null
      switch (data) {
        case 'weixin': {
          this.sharweixin()
          break
        }
        case 'weibo': {
          this.shareweibo()
          break
        }
        case 'qq': {
          this.shareqq()
          break
        }
      }
    },
    // 跳转评论
    discussJumpFun() {
      // document.querySelector('.participate').scrollIntoView({ behavior: 'smooth' }) // 跳到指定元素位置
      document.querySelector('#comment').focus() // 高亮
    },
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({
        name: 'index-case-detail-ugc',
        query: {id: infoid}
      })
      window.open(href, '_blank')
    },
    shareweibo() {
      // event.preventDefault();防止链接打开 URL：
      var _shareUrl = '//service.weibo.com/share/share.php?'
      _shareUrl += 'url=' + encodeURIComponent(document.location) //分享地址
      _shareUrl += '&title=' + encodeURIComponent(this.infoData.title) //分享标题
      window.open(
        _shareUrl,
        '_blank',
        'height=300,width=500',
        'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    },
    shareqq() {
      const share = {
        title: this.infoData.title,
        desc: '神外咨询',
        image_url: this.infoData.images,
        share_url: document.location //注意 localhost 生成失败
      }
      let image_urls = share.image_url.map(function (image) {
        return encodeURIComponent(image)
      })
      //跳转地址
      var qqurl =
        'https://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?url=' +
        encodeURIComponent(share.share_url) +
        '&title=' +
        share.title +
        '&pics=' +
        image_urls.join('|') +
        '&summary=' +
        share.desc
      window.open(
        qqurl,
        '_blank',
        'height=300,width=500',
        'scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes'
      )
    },
    sharweixin() {
      var url = location.href
      qrcade
        .toDataURL(url)
        .then((img) => {
          this.weixinUrl = img
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 文章收藏
    collectFun() {
      this.$analysys.like('', '', '', '收藏原文', '', '', '', '', [], [], '病例Ugc', '', '', String(this.$route.query.id))
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(async () => {
        await this.$axios.$request(addCollect({
          contentId: this.$route.query.id,
          type: 3
        })).then((res) => {
          if (res.code === 1) {
            if (!res.result) {
              this.$toast('收藏成功')
            }
          }
        })
        await this.$axios.$request(isCollect({
          contentId: this.$route.query.id,
          type: 3
        })).then((res) => {
          if (res && res.code === 1) {
            this.isCollect = res.result.isCollect
            if (res.result.isCollect) {
              this.$toast('收藏成功')
              this.infoData.favorites = this.infoData.favorites + 1
            } else {
              this.infoData.favorites = this.infoData.favorites > 0 ? this.infoData.favorites - 1 : null
            }
          }

        })
      }, 1000)
    }
  }
}
</script>
<style>
/*新版编辑器标题*/
#fuwenben {
  line-height: 1.8;
}

#fuwenben img {
  max-width: 100%;
  width: 100%;
  height: auto
}

#fuwenben ul li:before {
  display: inline-block;
  content: '•';
  width: 20px;
  font-size: inherit;
  text-align: center;
  text-indent: 0 !important;
}

#fuwenben ol {
  counter-reset: item;
}

#fuwenben ol li:before {
  display: inline-block;
  content: counter(item) ".";
  counter-increment: item;
  font-size: inherit;
  min-width: 20px;
  text-align: center;
}

#fuwenben h2 {
  margin: 1.2rem 0 .8rem;
  padding: 0;
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.1rem;
  color: #333333;
}

#fuwenben h2.htwo {
  position: relative;
  padding-left: 0.6rem;
}

#fuwenben .imageDelete, .imageEdit {
  position: absolute;
  display: none;
}

#fuwenben h2.htwo::before {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  content: '';
  width: 0.2rem;
  height: 1.1rem;
  background: #0581ce;
}

#fuwenben h2.hthree {
  position: relative;
  padding-bottom: 0.6rem;
  text-align: center;
}

#fuwenben h2.hthree::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  content: '';
  width: 1.1rem;
  height: 0.2rem;
  background: #0581ce;
}

#fuwenben h2.hone {
  position: relative;
  padding-left: 0.9rem;
}

#fuwenben h2.hone::before {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  content: '';
  width: 0.5rem;
  height: 0.6rem;
  background: url("/template/1/bluewise/_files/h5_images/triger.png") no-repeat center / cover;
}

#fuwenben h2.hfour {
  position: relative;
  padding: 0.3rem 0.2rem;
  background-color: #0581ce;
  color: #fff;
  /* display: inline-block; */
  display: inline-flex;
  display: table;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  left: 50%;
  transform: translateX(-50%);
  /* line-height: 30px; */
}

#fuwenben h2.hfour::before {
  position: absolute;
  left: 0.2rem;
  top: 0.2rem;
  content: '';
  width: 100%;
  height: 100%;
  border: 0.05rem solid #0581CE;
  box-sizing: border-box;
}

#fuwenben h2.hfive {
  position: relative;
  padding: 0.3rem 0.55rem;
  background-color: #0581ce;
  color: #fff;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 0.2rem;
  display: inline-flex;
  display: table;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}

/*新病例*/
/*图片描述*/
#fuwenben p.tp_desc {
  margin-top: 0.6rem;
  margin-bottom: .6rem;
  text-align: center;
  font-size: 0.6rem;
  line-height: 1.2em;
  color: #999;
}

#fuwenben img {
  display: block;
  cursor: zoom-in;
}

#fuwenben {
  padding-top: 1rem;
  /*line-height: 1.5em;*/
}

/*兼容旧版段落标题*/
.old_text_box {
  position: relative;
  margin: 0;
}

#fuwenben img:not(.imageDiv img) {
  margin-bottom: 1.2rem;
}

#fuwenben .imageDiv {
  margin-bottom: 1.2rem;
}

#fuwenben .noMarginBottom{
  margin-bottom: 0;
}
/*隐藏新编辑器操作按钮*/
#fuwenben .imageDelete, .imageEdit {
  display: none;
  visibility: hidden;
}

/* pc编辑器发布的列表 */
#fuwenben li p {
  display: inline;
}

#fuwenben ul, #fuwenben ol, #fuwenben li {
  list-style: none;
  padding: 0 !important;
  margin: 0 !important;
}

#fuwenben .quote_box,
#fuwenben .link_box {
  position: relative;
  margin: 10px 0;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  background: rgba(5, 129, 206, 0.04);
  border-radius: 4px;
  display: flex;
  align-items: center;
}

#fuwenben .link_box {
  line-height: 1;
  display: flex;
  align-items: center;
}

#fuwenben .link_box .link_icon {
  margin-right: 8px;
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url('https://www.brainmed.com/template/1/bluewise/_files/h5_images/question/icon-lj.png') no-repeat center /
      cover;
}

#fuwenben .link_box i {
  font-style: normal;
  color: #279aee;
  font-size: 16px;
  font-weight: 500;
  line-height: 18px;
  text-decoration: underline;
}

#fuwenben .close {
  display: none !important;
}

#fuwenben textarea {
  font-size: 16px !important;
  line-height: normal !important;
  height: 20px !important;
  max-width: 100%;
  min-width: 100%;
  resize: none;
  white-space: nowrap;
  display: inline-block !important;
}

#fuwenben .imageDelete,
#fuwenben .imageEdit,
#fuwenben .close_btn,
#fuwenben .progress_bar,
#fuwenben .video_play_btn,
#fuwenben .webModule,
#fuwenben .close {
  display: none !important;
  visibility: hidden !important;
}
</style>

<style lang='less' scoped>
@import "~@/pages/index/case/details.less";
</style>
