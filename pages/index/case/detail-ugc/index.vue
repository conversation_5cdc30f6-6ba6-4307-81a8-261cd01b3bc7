<template>
  <div class="page_wrapper">
    <ContainerPage>
      <template #left>
        <div class="page_left_wrapper">
          <Sidebar
            :comments="InfoData.comments"
            article-type="ugc"
            :show-diggs="InfoData.showDiggs"
            :show-collects="InfoData.showCollects"
            :is-article-digg="InfoData.isArticleDigg"
            :is-article-collect="InfoData.isArticleCollect"
            :meta-description="InfoData.metaDescription"
            @articleLikeHandler="articleLikeHandler"
            @articleCollectHandler="articleCollectHandler"
          />
          <InfoTopTitle
            article-type="ugc"
            :bmsAuth="InfoData.bmsAuth"
            :template="Template"
            :title="InfoData.title"
            :publish-date="timeStamp.timestamp_13(InfoData.publishDate, 'yyyy-mm-d')"
            :show-views="InfoData.showViews"
            :attrs="InfoData.attrs"
            :author-list="InfoData.authorList"
            :article-honer-info="articleHonerInfo"
            :guide-source="InfoData.guideSource"
          />
          <InfoContent :content="InfoData.text"/>
          <BottomToolbars
            :comments="InfoData.comments"
            article-type="ugc"
            :show-diggs="InfoData.showDiggs"
            :show-collects="InfoData.showCollects"
            :is-article-digg="InfoData.isArticleDigg"
            :is-article-collect="InfoData.isArticleCollect"
            :meta-description="InfoData.metaDescription"
            @articleLikeHandler="articleLikeHandler"
            @articleCollectHandler="articleCollectHandler"
          />
          <RelatedArticles
            v-if="relatedArticleData.list.length>0"
            :info-title="InfoData.title"
            :list="relatedArticleData.list || []"
            article-type="ugc"
          />
          <InfoComments
            :comment-data="commentData"
            @likeHandler="likeHandler"
            @pushHandler="pushHandler"
            @pageUp="(current) => articleCommentListHandler({pageNo : current})"
          />
        </div>
      </template>
      <template #right>
        <div ref="page_right_wrapper" class="page_right_wrapper">
          <!--普通模板-->
          <CircleEntrance
            v-if="circleList.length>0 && Template === ''"
            :circle-list="circleList"
          />
          <ColumnEntrance
            v-if="articleSpecials.length>0 && Template === ''"
            :article-specials="articleSpecials"
          />
          <RelatedCategories
            v-if="infoMiniTool.length>0 || articleSpecials.length>0 || (circleList.length>0 && Template !== '') || talkList.length>0 || infoProducts.length>0"
            :template="Template"
            :info-mini-tool="infoMiniTool"
            :article-specials="articleSpecials"
            :circle-list="circleList"
            :talk-list="talkList"
            :info-products="infoProducts"
          />
          <SearchKeyword v-if="searchKeyWordList.length > 0" :search-key-word-list="searchKeyWordList"/>
        </div>
      </template>
    </ContainerPage>
  </div>
</template>

<script>
import {ContainerPage} from "../../../../opt-components/template";
import {
  InfoTopTitle,
  InfoContent,
  RelatedArticles,
  InfoComments,
  Sidebar,
  CircleEntrance,
  ColumnEntrance,
  SearchKeyword,
  RelatedCategories,
  BottomToolbars
} from "../../../../opt-components/page/info-detail";
import {
  getArticleHonerInfo,
  getArticleSpecials,
  getCommunityByArticleAndType,
  getRelatedArticles, getTypeArticleOrInfoProducts, getTypeArticleOrInfoSearchKeyWord
} from "../../../../api/article";
import {addCollect} from "../../../../api/user";
import {saveBrowsingHistory} from "../../../../api/browsing-history";
import {
  articleDigg,
  caseApicomment,
  caseApicommentDiggs,
  getArticleCommentsPage,
  getMpArticle,
  getTypeMpArticleOrInfoMiniTool
} from "../../../../api/case";

export default {
  name: "InfoDetailPage",
  components: {
    ContainerPage,
    InfoTopTitle,
    InfoContent,
    RelatedArticles,
    InfoComments,
    Sidebar,
    CircleEntrance,
    ColumnEntrance,
    SearchKeyword,
    RelatedCategories,
    BottomToolbars
  },
  /**
   * UGC 普通模板
   * @param app
   * @param params
   * @param error
   * @param store
   * @param query
   * @param req
   * @returns {Promise<void>}
   */
  async asyncData({app, params, error, store, query, req}) {
    const [Request1, Request2, ArticleHonerInfo, InfoMiniTool, ArticleSpecials, CircleData, TalkData, InfoProducts] = await Promise.all([
      // 文章详情
      app.$axios.$request(getMpArticle({
        articleId: query.id,
        userId: store.state.auth.user.id,
        articleType: 'U'
      })),
      // 关键字
      app.$axios.$request(getTypeArticleOrInfoSearchKeyWord({
        id: query.id,
        type: 'U'
      })),
      // 荣誉信息
      app.$axios.$request(getArticleHonerInfo({
        articleId: query.id,
        articleType: 'A' // 文章类型 A (UGC)| I (PGC)
      })),
      // 评分小工具
      app.$axios.$request(getTypeMpArticleOrInfoMiniTool({
        id: query.id,
        type: 'U'
      })),
      // 所属专栏
      app.$axios.$request(getArticleSpecials({
        articleId: query.id,
        type: 'U',
        loginUserId: store.state.auth.user.id
      })),
      // 所属圈子
      app.$axios.$request(getCommunityByArticleAndType({
        articleId: query.id,
        userId: store.state.auth.user.id,
        articleType: 'U',
        communityType: 'C'   // 话题：T ,圈子：C 活动：A
      })),
      // 所属话题
      app.$axios.$request(getCommunityByArticleAndType({
        articleId: query.id,
        userId: store.state.auth.user.id,
        articleType: 'U',
        communityType: 'T'   // 话题：T ,圈子：C 活动：A
      })),
      // 相关产品
      app.$axios.$request(getTypeArticleOrInfoProducts({
        id: query.id,
        type: 'U',
      }))
    ])

    const attrArr = []
    if (Request1.result.subspecialtys && Request1.result.subspecialtys.length > 0) {
      Request1.result.subspecialtys.forEach(item => {
        let newName = item.name
        if (item.children && item.children.length > 0) {
          item.children.forEach(itemChild => {
            newName = `${item.name}-${itemChild.name}`

            attrArr.push({
              id: item.id,
              name: newName
            })
          })
        } else {
          attrArr.push({
            id: item.id,
            name: newName
          })
        }
      })
    }

    const InfoData = {
      infoId: Request1.result.id,
      bmsAuth: Request1.result.bmsAuth,
      attrs: attrArr,
      publishDate: Request1.result.publishTime,
      comments: Request1.result.showComments,
      showViews: Request1.result.showViews,
      showDiggs: Request1.result.showDiggs,
      showCollects: Request1.result.favorites,
      metaDescription: Request1.result.description,
      guideSource: "",
      title: Request1.result.title,
      authorList: [{
        avatarAddress: Request1.result.creator.avatarAddress,
        authorName: Request1.result.creator.realName,
        company: Request1.result.creator.company,
        postNum: Request1.result.creator.userPostNum,
        fans: Request1.result.creator.fans,
        userDiggs: Request1.result.userDiggs,
        authorUserId: Request1.result.creator.id,
        followStatus: Request1.result.followStatus,
      }],
      authorNames: "",
      text: Request1.result.paragraphType === 'O' ? Request1.result.mpArticleParagraphs : Request1.result.mpArticleParagraphs,
      isArticleDigg: Request1.result.diggStatus === 'T',
      isArticleCollect: Request1.result.isArticleCollect,
    }

    /**
     * UGC 为普通模板为空
     */
    const Template = ""
    // 关键字列表
    const searchKeyWordList = Request2.list || []

    // tdk
    let keywordsName = ""
    if (searchKeyWordList.length > 0) {
      searchKeyWordList.forEach(item => {
        keywordsName += item + ","
      })
    }

    let authorNames = ""
    let authorNameAndCompany = ""
    if (InfoData.authorList && InfoData.authorList.length > 0) {
      InfoData.authorList.forEach(item => {
        authorNames += item.authorName + ","
        authorNameAndCompany += `${item.company ? item.company + "_" : ""}${item.authorName}、`
      })
    }
    authorNameAndCompany = authorNameAndCompany.slice(0, authorNameAndCompany.length - 1)
    let attrsNames = ""

    if (InfoData.attrs && InfoData.attrs.length > 0) {
      InfoData.attrs.forEach(item => {
        attrsNames += item.name + ","
      })
    }

    return {
      InfoData,
      Template,
      searchKeyWordList,
      articleHonerInfo: ArticleHonerInfo.result || {},
      infoMiniTool: InfoMiniTool.list || [],
      articleSpecials: ArticleSpecials.list || [],
      circleList: CircleData.list || [],
      talkList: TalkData.list || [],
      infoProducts: InfoProducts.list || [],
      keywordsName,
      authorNames,
      authorNameAndCompany,
      attrsNames
    }
  },
  data() {
    return {
      commentData: {
        list: [],
        page: {}
      },
      relatedArticleData: {
        list: [],
        page: {}
      }
    }
  },
  head() {
    return {
      title: this.InfoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.InfoData.metaDescription || `${this.authorNameAndCompany}，文章分享：${this.InfoData.title}）`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }]
    }
  },
  mounted() {
    // 客户端请求接口
    this.getDataHandler()
    // 评论列表
    this.articleCommentListHandler({pageNo: 1});

    // 计算右侧栏目高度
    window.setTimeout(() => {
      const rightWrapper = this.$refs.page_right_wrapper
      if (rightWrapper) {
        const rightTop = (rightWrapper.offsetHeight - window.innerHeight) + 32

        if (rightWrapper.offsetHeight > (window.innerHeight - 84)) {
          rightWrapper.style.top = rightTop > 0 ? "-" + rightTop + "px" : rightTop + "px"
        }

      }
    }, 10)

    // 采集历史记录
    this.saveBrowsingHistoryHandler()
  },
  methods: {
    // 获取相关文章
    async getDataHandler() {
      const [relatedArticleData] = await Promise.all([
        this.$axios.$request(getRelatedArticles({
          articleId: this.$route.query.id,
          count: 4,
          type: 'ugc',
          userId: this.$store.state.auth.user.id
        }))
      ])
      if (relatedArticleData.code === 1) {
        this.$set(this.relatedArticleData, 'list', relatedArticleData.list);
      }
    },


    // 采集浏览记录
    saveBrowsingHistoryHandler() {
      if (this.$store.state.auth.token) {
        this.$axios.$request(saveBrowsingHistory({
          loginUserId: this.$store.state.auth.user.id,
          contentSource: 3,
          contentId: this.$route.query.id,
          courseId: null,
          playDuration: null
        }))
      }

      // 顺便埋个点
      let company = ''
      let name = '' // 作者名字
      if (this.InfoData.authorList && this.InfoData.authorList.length > 0) {
        this.InfoData.authorList.forEach(item => {
          company += item.company + ','
          name += item.authorName + ','
        })
      }

      this.$analysys.view_info_page(this.InfoData.title, company, [], '', '图文', [], String(this.InfoData.infoId), name, 0)

      this.$analysys.Browse_article({
        userid: String(this.$store.state.auth.user.id),
        articleid: String(this.$route.query.id),
        type: 'mp',
        articletitle: String(this.InfoData.title)
      })

      this.$analysys.view_case_page(
        this.InfoData.authorList?.[0]?.authorName,
        this.InfoData.showViews,
        String(this.InfoData.id),
        this.InfoData.title,
        this.InfoData.authorList?.[0]?.company,
        this.InfoData.showDiggs,
        '',
        '',
        this.InfoData.showComments,
        '视频图文',
        []
      )
    },

    // 文章点赞
    articleLikeHandler() {
      this.$set(this.InfoData, "isArticleDigg", !this.InfoData.isArticleDigg)
      // 埋点
      this.$analysys.like('', '', '', '点赞原文', '', '', '', '', [], [], '病例', '', '', String(this.$route.query.id))
      // 点赞接口
      this.$axios.$request(articleDigg({
        articleId: this.$route.query.id,
        userId: this.$store.state.auth.user.id
      }))
    },

    // 文章收藏
    articleCollectHandler() {
      this.$set(this.InfoData, "isArticleCollect", !this.InfoData.isArticleCollect)
      // 埋点
      this.$analysys.like('', '', '', '收藏原文', '', '', '', '', [], [], '病例', '', '', String(this.$route.query.id))
      // 收藏接口
      this.$axios.$request(addCollect({
        contentId: this.$route.query.id,
        type: 3
      }))
    },

    // 文章评论列表
    async articleCommentListHandler({pageNo = 1}) {
      const data = await this.$axios.$request(getArticleCommentsPage({
        articleId: this.$route.query.id,
        userId: this.$store.state.auth.user.id,
        order: 0,
        pageNo,
        pageSize: 10
      }))

      if (data.code === 1) {
        const list = data.result.list.map(item => {
          return item.parent
        })

        this.$set(this.commentData, 'list', this.commentData.list.concat(list));
        this.$set(this.commentData, 'page', data.result.page);
      }
    },

    // 评论点赞
    likeHandler(id, backFn) {
      this.$analysys.like('', '', '', '点赞留言', '', '', '', '', [], [], 'ugc', '', '', String(id))

      this.$axios.$request(caseApicommentDiggs({
        commentId: id,
        userId: this.$store.state.auth.user.id
      })).then((response) => {
        response.result && response.code === 1 ? backFn(true) : backFn(false)
      })
    },

    // 评论发送
    pushHandler({info, parentId}, backFn) {
      this.$analysys.comment('评论留言', '', '', '', [], '', '', '', '', String(this.replyId), '', '评论留言', [], String(document.title), '')
      this.$axios.$request(caseApicomment({
        text: info,
        parentId,
        userId: this.$store.state.auth.user.id,
        articleId: this.$route.query.id
      })).then((response) => {
        if (response && response.code === 1) {
          backFn(response.result)
          this.$toast(response.message)
        } else {
          backFn(false)
          this.$toast(response.message)
        }
      })
    },
  }
}
</script>
<style>
@media screen and (max-width: 1280px) {
  #bottom_tool_bars_container_id {
    display: block;
  }

  #sidebar_wrapper_tool_bars_id {
    display: none !important;
  }
}

@media screen and (min-width: 1280px) {
  #bottom_tool_bars_container_id {
    display: none;
  }

  #sidebar_wrapper_tool_bars_id {
    display: block !important;
  }
}
</style>
<style scoped lang="less">
@import "./styles";
</style>
