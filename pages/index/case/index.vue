<template>
  <div class='container-box consulting-box'>
    <el-row>
      <el-col :lg='16' :md='16' :sm='18' :xl='16' :xs='24' class='page-left'>
        <div class='consulting-left'>
          <NavTwo
            :channelType='channelType'
            :navSelectDataCurrentA='subSpecialtyIndex'
            :subSpecialtyCurrent='subSpecialtyName'
            :subSpecialtyData='subSpecialtyData'
            :subSpecialtyType="'case'"
            @selectRecommendFun='selectRecommendFun'
            @selectsubSpecialtyId='selectsubSpecialtyId'
          >
          </NavTwo>
          <div class='NewConsulting'>
            <ul class='consulting'>
              <li
                v-for='(itemCase, index) in caseData'
                :key='itemCase.id'
                class='consulting-item'
                @click="jumpDetailsPageFun(itemCase.type === 'P' ? itemCase.info.id : itemCase.article.id,itemCase.type)"
              >
                <div v-if="itemCase.type === 'P'" class='first_article'>
                  <nuxt-link
                    :to="{path:`/info/detail?id=${itemCase.info.id}`}"
                    target='_blank'
                  >
                    <div class='item-left themeBorderRadius'>
                      <img
                        v-if='itemCase.info.infoImg'
                        :src='$tool.compressImg(itemCase.info.infoImg,220,124)'
                        class='img_cover'
                      />
                      <img v-else class='img_cover' src='~assets/images/default16.png'/>
                    </div>
                    <div class='item-right flex_column flex_column_justify'>
                      <div>
                        <p class='pone text-limit-2 fontWeight'>
                          <svg-icon
                            v-if='itemCase.info.essences==="T"'
                            class-name='poneIcon cursor'
                            icon-class='jinghua'
                          ></svg-icon>
                          <svg-icon v-if="itemCase.info.bmsAuth === 1" icon-class="auth-new"
                                    style="width: 18px;height: 18px;vertical-align:middle"/>
                          <span class='vertical_align_middle'> {{ itemCase.info.title }}</span>
                        </p>
                        <ul class='lable-box text-limit-2'>
                          <li
                            v-for='(item, index) in itemCase.info.subspecialtys'
                            :key='item.id'
                            style='display: inline'
                          >
                            <p v-for='itemChildren in item.children' :key='itemChildren.id' class='lable-item'>
                              {{
                                item.name +
                                '-' +
                                itemChildren.name
                              }}
                            </p>
                          </li>
                          <!-- 产品标签 -->
                          <div
                            v-for='item in itemCase.info.productList'
                            :key='item.id'
                            class='lable-item lable-item_product flex_start flex_align_center flex_warp'
                          >
                            <svg-icon
                              className='productIcon'
                              iconClass='product2'
                            ></svg-icon>
                            {{ item.name }}
                          </div>
                        </ul>
                      </div>
                      <div class='lable_time flex_between flex_align_center'>
                        <div class='flex_start flex_shrink flex_align_center'>
                          <div v-for='(authorList,index) in itemCase.info.authorList' v-show='index<3'
                               class='article_headimage flex_start'>
                            <img v-if='authorList.headImage' :key='authorList.id'
                                 :src='$tool.compressImg(authorList.headImage,16,16)'
                                 class='img_cover img_radius flex_shrink'/>
                            <svg-icon v-else class-name='img_cover' icon-class='signinavatar'/>
                          </div>
                          <div class='headName text-limit-1'>
                            {{
                              itemCase.info.authorList.length > 1 ? `${itemCase.info.authorList[0].authorName}等${itemCase.info.authorList.length}位作者` : `${itemCase.info.authorList.length === 1 ? itemCase.info.authorList[0].authorName : ''}`
                            }}
                          </div>
                        </div>
                        <div class='flex_shrink'>
                          <span>{{
                              timeStamp.timestampFormat(Date.parse(itemCase.info.publishTime.replace(/-/g, '/')) / 1000)
                            }}</span>
                          <span>{{ itemCase.info.showViews }}阅读</span>
                          <span>{{ itemCase.info.showComments }}评论</span>
                          <span>{{ itemCase.info.showDiggs }}点赞</span>
                        </div>
                      </div>
                    </div>
                  </nuxt-link>
                </div>
                <div v-if="itemCase.type === 'U'" class='second_article cursor'>
                  <div class='second_article_smallbox' @click='jumpPageFun(itemCase.article.id,itemCase.type)'>
                    <p class='pone text-limit-2 fontWeight'>
                      <svg-icon
                        v-if='itemCase.article.essences==="T"'
                        class-name='poneIcon cursor'
                        icon-class='jinghua'
                      ></svg-icon>
                      <svg-icon v-if="itemCase.article.bmsAuth === 1" icon-class="auth-new"
                                style="width: 18px;height: 18px;vertical-align:middle"/>
                      <span class='vertical_align_middle'>{{ itemCase.article.title }}</span>
                    </p>
                    <!-- 亚专业标签 -->
                    <div id='lableBox' class='lable-box'>
                      <div id='productBox' class='flex_start flex_warp'>
                        <ul id='lableItemUl' class='flex_start flex_warp text-limit-2'>
                          <li
                            v-for='(item, index) in itemCase.article.subspecialtys'
                            :key='item.id' style='display: inline'
                          >
                            <p v-for='(itemChildren, index) in item.children' :key='itemChildren.id'
                               class='lable-item'>
                              {{
                                item.name +
                                '-' +
                                itemChildren.name
                              }}
                            </p>
                          </li>
                          <!-- 产品标签 -->
                          <div
                            v-for='item in itemCase.article.productList'
                            id='productItem'
                            :key='item.id'
                            class='lable-item lable-item_product flex_start flex_align_center flex_warp'
                          >
                            <svg-icon
                              className='productIcon'
                              iconClass='product2'
                            ></svg-icon>
                            {{ item.name }}
                          </div>
                        </ul>
                      </div>
                    </div>
                    <div
                      v-if='itemCase.article.images.length > 0 || itemCase.article.cover'
                      class='flex_start flex_warp'
                    >
                      <div
                        v-for='(item, index) in itemCase.article.images.length > 0 ? itemCase.article.images : [itemCase.article.cover]'
                        v-if='index<3'
                        :key='index'
                        class='second_article_smallImg themeBorderRadius'
                      >
                        <img :src='$tool.compressImg(item,220,124)' alt='' class='img_cover'/>
                      </div>
                    </div>
                    <div v-if='itemCase.article.creator' class='Author_info'>
                      <div class='left'>
                        <img
                          v-if="
                                  itemCase.article.creator.avatarAddress !== null &&
                                  itemCase.article.creator.avatarAddress !== ''
                                "
                          :src='$tool.compressImg(itemCase.article.creator.avatarAddress,28,28)'
                          alt='error'
                          class='img_cover'
                        />
                        <svg-icon
                          v-if="
                                  itemCase.article.creator.avatarAddress === null ||
                                  itemCase.article.creator.avatarAddress === ''
                                "
                          className='imgbox_amll cursor'
                          iconClass='signinavatar'
                        ></svg-icon>
                      </div>
                      <div class='right'>
                        <p class='user flex_start flex_align_center'>
                                <span class='fontSize14'>
                                  {{
                                    itemCase.article.creator.realName
                                  }}</span>
                          <span v-if='itemCase.article.creator.id !== $store.state.auth.user.id'
                                :class='{"themeFollowed" :itemCase.article.creator.isFollow }'
                                class='follow'
                                @click.stop='followFun(itemCase.article.creator.id)'>
                              <span v-if='!itemCase.article.creator.isFollow' class='flex_center flex_align_center'>
                                 <svg-icon className='tab_icon flex_shirk'
                                           iconClass='subscribe'></svg-icon>
                                 <span>关注</span>
                              </span>
                            <span v-else>已关注</span>
                            </span>
                        </p>
                        <div class='info'>
                          <span>{{ itemCase.article.creator.company }}</span>
                          <div style='text-align: right'>
                                  <span>{{
                                      timeStamp.timestampFormat(
                                        itemCase.article.publishTime / 1000
                                      )
                                    }}</span>
                            <span>{{ itemCase.article.showViews }}阅读</span>
                            <span>{{ itemCase.article.showComments }}评论</span>
                            <span>{{ itemCase.article.showDiggs }}点赞</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <Empty :loading='loading' :no-more='!loading && caseData.length===0' height='70vh'/>
          <el-pagination
            v-if='!loading'
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='$store.state.article_count'
            :pager-count='$store.state.article_count'
            :total='caseDatatotal'
            background
            small
            style='text-align: center; margin-bottom: 10px'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :sm='6' :xl='8' :xs='24' class='page-right'>
        <div class='consulting-right'>
          <div class='advertisement cursor' @click='jumpReleaseCaseFun()'>
            <img alt='' class='img_cover' src='~assets/images/case/facase.png'/>
          </div>
          <div v-for='item in advertisementData' :key='item.adId' class='advertisement' :title="item.name">
            <img
              :class="item.extras?'cursor':''"
              :src='$tool.compressImg(item.image,387,132)'
              :alt='item.name'
              class='img_cover'
              @click='jumpBannerFun(item)'/>
          </div>
          <hotlist :is-first-list='true' :popular-search-data='popularSearchData' title='精选病例'></hotlist>
          <identity-authentication :authenticationDataInfo='authenticationDataInfo'
                                   :authenticationDialog.sync='authenticationDialog'
                                   :identity-current-father='$store.state.auth.user.identity'
                                   :identityFlag='!!$store.state.auth.user.identity'
                                   @editFlag='authenticationDialogFun'></identity-authentication>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {getSlotContent} from "../../../api/banner/banner"; // 用户接口
import Empty from '@/components/UI/Empty/Empty'
import hotlist from '@/components/PageComponents/Hotlist/Hotlist'
import NavTwo from '@/components/NavigationTwo/NavigationTwo' // 文献速览
import IdentityAuthentication from '@/components/IdentityAuthentication/IdentityAuthentication' // 身份认证
import {caseViews, getInfoList, getRightArticleList, getSubSpecialty} from '@/api/case'
import {getBanner} from '@/api/home'
import env from '../../../env-module'
import {loginByToken} from '@/api/login'
import {userInfo} from '@/api/user'
import brandAdJump from "../../../assets/helpers/brandAdJump";

export default {
  name: 'Consulting',
  head() {
    return {
      title: '病例'
    }
  },
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '病例'
    // 获取文章列表 and 病例 亚专业
    const [request1, request2, request3, request4] = await Promise.all([
      app.$axios.$request(getInfoList({
        userId: store.state.auth.user.id,
        specilityId: query.subSpecialtyId === '0' ? null : query.subSpecialtyId,
        recommend: query.selectedFalg ? query.selectedFalg === 'F' ? null : query.selectedFalg : null,
        pageNo: query.currentPage ? Number(query.currentPage) : Number(1),
        pageSize: store.state.article_count
      })),
      app.$axios.$request(getSubSpecialty()),
      app.$axios.$request(getSlotContent({
        loginUserId: store.state.auth.user.id,
        detailIdStr: '',
        adCode: 'webAPi_case',
      })),
      app.$axios.$request(getRightArticleList({
        pageNo: 1,
        pageSize: 10
      }))
    ])

    let subSpecialtyData = request2.list
    if (typeof subSpecialtyData !== 'string') {
      let msg = query.selectedFalg ? query.selectedFalg === 'T' ? '精华' : '全部' : '全部'
      subSpecialtyData.unshift({
        name: msg,
        id: 0
      })
    }
    return {
      caseData: request1.list,
      caseDatatotal: request1.page.totalCount,
      casePage: request1.page.totalPage,
      subSpecialtyData,
      advertisementData: request3.list,
      popularSearchData: request4.list,
      currentPage: query.currentPage ? Number(query.currentPage) : 1,          // 分页页码
      subSpecialtyIndex: query.subSpecialtyId ? Number(query.subSpecialtyId) : null,// 亚专业ID
      subSpecialtyName: query.subSpecialtyName ? query.subSpecialtyName : '全部', // 选中亚专业名称
      channelType: query.selectedFalg ? query.selectedFalg : null,       // 是否为精选
      pageNo: 1
    }
  },
  data() {
    return {
      loading: false,
      authenticationDataInfo: {},// 用户身份信息
      authenticationDialog: false// 病例身份认证弹框
    }
  },
  watch: {
    '$route'(res) {
      this.currentPage = Number(res.query.currentPage)
      this.subSpecialtyIndex = Number(res.query.subSpecialtyId)
      this.channelType = res.query.selectedFalg
      this.subSpecialtyName = res.query.subSpecialtyName
    }
  },
  methods: {
    async followFun(id) {
      /**

       **@desc: 注释

       **@date:2022/6/22 16:59

       **@author:Rick

       **@method:followFun

       **@params: id 作者id

       */
      await this.$store.dispatch('follow', id)
      await this.$axios.$request(getInfoList({
        userId: this.$store.state.auth.user.id,
        specilityId: this.subSpecialtyIndex,
        recommend: this.channelType === 'F' ? null : this.channelType,
        pageNo: this.pageNo,
        pageSize: this.$store.state.article_count
      })).then((res) => {
        if (res && res.code === 1) {
          this.caseData = res.list
          this.caseDatatotal = res.page.totalCount
        }
      })
    },
    /**

     **@desc: 跳转文章详情

     **@date:2022/6/22 16:53

     **@author:Rick

     **@method:

     **@params:

     */
    jumpPageFun(id, type) {
      let routeUrl = this.$router.resolve({
        name: 'index-case-detail-ugc',
        query: {
          id
        }
      })
      window.open(routeUrl.href, '_blank')
    },
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    // 身份认证
    authenticationDialogFun(data) {
      this.authenticationDialog = data
    },
    // 切换亚专业
    selectsubSpecialtyId(index) {
      if (!this.loading) {
        this.caseData = []
        this.loading = true
        this.pageNo = 1
        this.currentPage = 1
        this.subSpecialtyIndex = index.id
        if (index.parentId) {
          this.subSpecialtyData.forEach(item => {
            if (index.parentId === item.id) {
              this.subSpecialtyName = item.name
            }
          })
        } else {
          this.subSpecialtyName = index.name
        }
        this.channelType = null
        this.channelType === 'T' ? this.subSpecialtyData[0].name = '精华' : this.subSpecialtyData[0].name = '全部'
        this.$analysys.btn_click(index.name, document.title)
        this.$axios.$request(getInfoList({
          userId: this.$store.state.auth.user.id,
          specilityId: index.id === 0 ? null : index.id,
          pageNo: 1,
          pageSize: this.$store.state.article_count
        })).then((res) => {
          this.loading = false
          if (res && res.code === 1) {
            this.caseData = res.list
            this.caseDatatotal = res.page.totalCount
          }
        })
      }
    },
    // 切换精华
    selectRecommendFun(type) {
      if (type === "T") {
        this.subSpecialtyData[0].name = '精华'
      } else if (type === 1) {
        this.subSpecialtyData[0].name = '品牌认证'
      } else {
        this.subSpecialtyData[0].name = '全部'
      }

      this.channelType = type
      this.currentPage = 1
      this.pageNo = 1
      this.$axios.$request(getInfoList({
        userId: this.$store.state.auth.user.id,
        specilityId: this.subSpecialtyIndex !== 0 ? this.subSpecialtyIndex : null,
        pageNo: 1,
        pageSize: this.$store.state.article_count,
        recommend: this.channelType === 1 ? null : this.channelType,
        bmsAuth: this.channelType === 1 ? 1 : null
      })).then((res) => {
        if (res && res.code === 1) {
          this.caseData = res.list
          this.caseDatatotal = res.page.totalCount
        }
      })
    },
    // 分页
    handleCurrentChange(item) {
      this.pageNo = item
      this.$tool.scrollIntoView()
      this.$axios.$request(getInfoList({
        userId: this.$store.state.auth.user.id,
        pageNo: item,
        pageSize: this.$store.state.article_count,
        specilityId: this.subSpecialtyIndex !== 0 ? this.subSpecialtyIndex : null,
        recommend: this.channelType === 1 || this.channelType === 'F' ? null : this.channelType,
        bmsAuth: this.channelType === 1 ? 1 : null
      })).then((res) => {
        if (res && res.code === 1) {
          this.caseData = res.list
          this.caseDatatotal = res.page.totalCount
        }
      })
    },
    // 跳转文章详情
    jumpDetailsPageFun(id, type) {
      this.$axios.$request(caseViews({
        articleId: id,
        articleType: type
      }))
    },
    // 跳转发布病例
    jumpReleaseCaseFun() {
      this.$analysys.btn_click('发病例', document.title)
      if (!this.$store.state.auth.token || this.$store.state.auth.token === 'undefined') {
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
      } else {
        this.$axios.$request(userInfo()).then(res => {
          if (res && res.code === 1) {
            if (res.result.isAuth === '1') {
              // window.open('publish/case/create?platform=F&type=0' + `${this.$store.state.auth.token ? '&token=' + this.$store.state.auth.token : ''}`)
              const {href} = this.$router.resolve({
                path: `/editor?type=0`
              })
              window.open(href, '_blank')
            } else if (res.result.isAuth === '2') {
              this.$toast.fail('身份审核中')
            } else if (res.result.isAuth === '3') {
              this.$toast.fail('身份认证失败')
            } else if (!res.result.identity || res.result.identity === 'undefined') {
              const h = this.$createElement
              this.$msgbox({
                message: h('p', null, [
                  h('span', null, '部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！')
                ]),
                center: true,
                showCancelButton: true,
                cancelButtonText: '以后再说',
                confirmButtonText: '去完善信息',
                customClass: 'personaldata-messagebox',
                beforeClose: (action, instance, done) => {
                  if (action === 'confirm') {
                    this.$store.commit('editAccountTypeFun', this.$store.state.auth.user.email ? this.$store.state.auth.user.email : 'tel')
                    this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                    this.$router.push({name: 'register'})
                    done()
                  } else {
                    done()
                  }
                }
              })
            } else {
              this.authenticationDataInfo = res.result
              this.authenticationDialog = true// 打开认证弹框
            }
          }
        })
      }
    }
  },
  components: {
    hotlist,
    NavTwo,
    IdentityAuthentication,
    Empty
  }
}
</script>

<style lang='less' scoped>
@import "~@/pages/index/case/index.less";
</style>
