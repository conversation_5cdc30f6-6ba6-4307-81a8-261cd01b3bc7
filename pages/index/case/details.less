/deep/ .el-breadcrumb__item {
  i {
    margin: 0 !important;
    color: #0581ceff;
  }

  .el-breadcrumb__inner {
    color: #0581ceff;
    font-size: 14px;
    font-weight: normal;
  }
}

.dianzanbox {
  width: 28px !important;
  height: 28px;
  margin-bottom: 10px;
}

/deep/ .el-popover {
  text-align: center;
}

.details-con {
  padding: 24px 0 36px;

  .details-con-left {
    padding-right: 20px;

    .zhan<PERSON> {
      width: 100%;
      height: 1px;
    }

    .title-box {
      margin: 30px 0 16px;
      font-weight: 540;
      overflow: hidden;
      padding-left: 88px !important;

      .line {
        border-bottom: 1px solid #d3dce5ff;

        .open_sub {
          text-align: right;
          color: #708AA2;
          font-size: 12px;
          margin-bottom: 10px;
        }

        .title {
          color: #000000ff;
          font-size: 20px;
          margin-bottom: 32px;
          font-weight: bold;

          .poneIcon {
            min-width: 20px;
            min-height: 20px;
            max-width: 20px;
            max-height: 20px;
            margin-right: 6px;
          }
        }

        .con {
          font-size: 12px;
          color: #708aa2ff;

          .con_lable_sub_productList {
            display: inline-block;
            border-radius: 6px;
            background: #F6F6F6 !important;
            color: #0CA92E !important;
            font-size: 12px;
            line-height: 22px;
            padding: 0px 9px;
            margin-right: 10px;
            margin-bottom: 10px;
          }

          .con_lable_sub {
            .con_lable_sub_item {
              display: inline-block;
              border-radius: 6px;
              padding: 0px 9px;
              line-height: 22px;
              background: #F0F9FF;
              color: #0A83CE;
              margin-right: 10px;
              margin-bottom: 10px;
            }
          }
        }
      }
    }

    .left-boxzhanwei {
      min-width: 68px;
      max-width: 68px;
      height: 60vh;
      display: none;
    }

    .left-box {
      top: calc(0px + 60px + 16px);
      margin-bottom: 15px;
      min-width: 68px;
      max-width: 68px;
      max-height: 492px;
      box-sizing: border-box;
      background: #fbfbfb;
      border-radius: 50px;
      padding: 20px 0 10px;
      text-align: center;
      box-sizing: border-box;

      img {
        display: block;
        margin: 0 auto;
        max-width: 28px;
        margin-bottom: 10px;
        cursor: pointer;
      }

      p {
        font-size: 12px;
        color: #708aa2ff;

        span {
          color: black;
          font-size: 16px;
          margin-right: 2px;
        }
      }

      .line {
        width: 30px;
        height: 1px;
        background: #e5e3e3ff;
        margin: 18px auto 17px;
      }
    }

    .right-box {
      width: 100%;
      padding-left: 20px;
      box-sizing: border-box;
      overflow: hidden;

      .hide-article {
        display: block;
        max-height: 226px;
        overflow: hidden;
        margin-bottom: 52px !important;
      }

      .content {
        font-size: 14px;
        line-height: 26px;
        letter-spacing: 1px;
        color: #1a1a1aff;

        img {
          max-width: 100% !important;
        }

        video {
          width: 100% !important;
        }
      }
    }

    .twocon {
    }
  }

  .details-con-right {
    .Text-author-box {
      border-radius: 6px;
      background: #fbfbfbff;
      border: 1px solid #f0f0f0ff;
      padding-top: 40px;
      margin-bottom: 20px;

      .user-box {
        text-align: center;

        .imageOneUser {
          width: 78px;
          height: 78px;
          border-radius: 50%;
          margin: 0 auto;
        }

        .usercoverbox-div {
          width: 78px;
          height: 78px;
          border-radius: 50%;
          margin: 0 auto;
        }

        .imgbox_amll, .img_box {
          width: 78px;
          height: 78px;
          border-radius: 50%;
          overflow: hidden;
          margin: 0 auto 0;
        }

        .user_name {
          color: #0581ceff;
          font-size: 22px;
          line-height: 22px;
          margin-bottom: 15px;
          margin-top: 15px;
        }

        .followed {
          background: rgba(0, 0, 0, 0.2) !important;
        }

        .addfollow {
          cursor: pointer;
          border-radius: 18px;
          background: #00000099;
          color: #ffffffff;
          font-size: 16px;
          width: 82px;
          line-height: 30px;
          margin: 0 auto;

          &:active {
            background: rgba(0, 0, 0, 0.2) !important;
          }

          .tab_icon {
            width: 11px;
            height: 11px;
            margin-right: 4px;
          }
        }

        .addbox {
          display: flex;
          justify-content: center;
          font-size: 14px;
          color: #708aa2ff;

          .article {
            margin-right: 79px;

            p:nth-child(1) {
              font-size: 22px;
              color: #000000ff;
            }
          }

          .follow {
            p:nth-child(1) {
              font-size: 22px;
              color: #000000ff;
            }
          }
        }
      }

      .user-box-Multiple-exceed-four {
        width: calc(62px * 4 + 60px) !important;
        margin: 0 auto;
        box-sizing: border-box;
        overflow-y: hidden;
        overflow-x: scroll;
        display: flex;
        justify-content: flex-start;

        &::-webkit-scrollbar {
          height: 5px !important;
        }

        .item {
          width: 62px;
          margin-bottom: 10px;
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }

          .imgbox {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;

            .imgbox_amll {
              width: 60px;
              height: 60px;
            }
          }

          img {
            display: block;
          }

          p {
            font-size: 20px;
            color: #0581ce;
            margin: 15px 0;
          }

          .addfollow {
            cursor: pointer;
            border-radius: 18px;
            background: #00000099;
            color: #ffffffff;
            font-size: 12px;
            line-height: 24px;
            width: 62px;

            &:active {
              background: rgba(0, 0, 0, 0.2) !important;
            }

            .tab_icon {
              width: 9px;
              height: 9px;
              margin-right: 4px;
            }
          }
        }
      }

      .user-box-Multiple {
        width: 100%;
        text-align: center;
        box-sizing: border-box;
        display: flex;
        padding: 0 20px;
        justify-content: space-around;

        .item {
          width: 62px;
          margin-bottom: 10px;
          margin-right: 20px;

          &:last-child {
            margin-right: 0;
          }

          .imgbox {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;

            .imgbox_amll {
              width: 60px;
              height: 60px;
            }
          }

          img {
            display: block;
          }

          p {
            font-size: 20px;
            color: #0581ce;
            margin: 15px 0;
          }

          .addfollow {
            cursor: pointer;
            border-radius: 18px;
            background: #00000099;
            color: #ffffffff;
            font-size: 12px;
            line-height: 24px;
            width: 62px;

            &:active {
              background: rgba(0, 0, 0, 0.2) !important;
            }

            .tab_icon {
              width: 9px;
              height: 9px;
              margin-right: 4px;
            }
          }
        }
      }

      .consultation_box {
        padding: 30px 14px 0;

        .title {
          font-size: 18px;
          line-height: 20px;
          position: relative;
          padding-left: 14px;
        }

        .title::before {
          content: "";
          display: block;
          width: 3px;
          height: 14px;
          background: #0581ceff;
          position: absolute;
          left: 0;
          top: 50%;
          margin-top: -7.5px;
          border-radius: 6px;
        }

        .consu-item {
          margin-top: 20px;

          li {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 20px;
            }

            .item-title {
              font-size: 14px;
              margin-bottom: 6px;
            }

            .time {
              font-size: 12px;
              color: #708aa2ff;
            }
          }
        }
      }

      .more-box {
        cursor: pointer;
        background: #e8f1f6ff;
        color: #0581ceff;
        font-size: 14px;
        text-align: center;
        padding: 14px 0;
      }
    }

    .Related_box {
      cursor: pointer;
      border-radius: 6px;
      border: 1px solid #f0f0f0ff;
      padding: 17px 14px 0;
      overflow: hidden;
      margin-bottom: 20px;

      .title {
        font-size: 18px;
        line-height: 20px;
        position: relative;
        padding-left: 14px;
        margin-bottom: 19px;
      }

      .title::before {
        content: "";
        display: block;
        width: 3px;
        height: 15px;
        background: #0581ceff;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -7.5px;
      }

      .Related-item {
        overflow: hidden;

        li {
          display: flex;
          justify-items: flex-start;
          margin-bottom: 16px;

          .left {
            min-width: 142px;
            max-width: 142px;
            height: 80px;
            margin-right: 10px;
            border-radius: 6px;
            overflow: hidden;
          }

          .right {
            .item-title {
              font-size: 14px;
              color: #000000ff;
              margin-bottom: 9px;
              line-height: 24px;
            }

            .con {
              color: #708aa2ff;
              font-size: 12px;
              line-height: 24px;
            }
          }
        }
      }
    }
  }
}

/*小屏时结束*/
/*中屏时开始*/
@media screen and (max-width: 1199px) {
  .user-box-Multiple {
    padding: 0 15px !important;

    .item {

      p {
        font-size: 16px !important;
        margin: 10px 0 8px !important;
      }

      .addfollow {
        line-height: 20px !important;
        padding: 1px 6px !important;
      }
    }
  }
}

/*中屏时结束*/
.phone-fabulous {
  display: none;
}

#fuwenben /deep/ img{
  cursor: zoom-in;
}

#fuwenben /deep/ iframe {
  width: 100%;
  min-height: 40vh;
}

#fuwenben /deep/ iframe > video {
  width: 100%;
}

#fuwenben /deep/ section {
  max-width: 100% !important;
}

/deep/ video {
  width: 100%;
}

.userListFixed {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  width: 1200px;
  z-index: 3000;
}
