<template>
  <div class="personaldata-container-box container-box">
    <div class="flex_between">
      <PersonalConLeft
        :avatarAddress="userInfomation.avatarAddress"
        :company="userInfomation.company"
        :department="userInfomation.department"
        :isAuth="userInfomation.isAuth"
        :realName="userInfomation.realName"
        :title="userInfomation.title"
        :departmentHomePageInfoList="userInfomation.departmentHomePageInfoList"
      />
      <div class="personaldata-container-right">
        <div class="personaldata-container-select">
          <Tabs v-model="$store.state.personalCenterState" @tab-click="editTabsStateFun">
            <tab-pane label="个人信息" name="personal">
              <div class="personal-information-tabs">
                <div class="modify-avatar">
                  <img
                    v-if="!editPersonalFlag && userInfomation.avatarAddress"
                    :src="userInfomation.avatarAddress"
                    alt=""
                    class="img_cover"
                  />
                  <img
                    v-if="!editPersonalFlag && !userInfomation.avatarAddress"
                    alt=""
                    class="img_cover"
                    src="~assets/images/user.png"
                  />
                  <upload
                    v-if="editPersonalFlag"
                    :http-request="httpRequest"
                    :show-file-list="false"
                    action="string"
                    class="avatar-uploader"
                    name="file"
                    accept="image/*"
                  >
                    <div class="nullupload img_radius overflow_hidden">
                      <img v-if="imageUrl" :src="imageUrl" alt="" class="img_cover"/>
                      <img
                        v-if="personalInformationForm.avatarAddress && !imageUrl"
                        :src="personalInformationForm.avatarAddress"
                        alt=""
                        class="img_cover"
                      />
                      <img
                        v-if="!personalInformationForm.avatarAddress && !imageUrl"
                        alt=""
                        class="img_cover"
                        src="~assets/images/user.png"
                      />
                      <div class="mack fontSize18">修改头像</div>
                    </div>
                  </upload>
                </div>
                <el-form
                  v-if="editPersonalFlag"
                  ref="form"
                  :model="personalInformationForm"
                >
                  <el-form-item label="姓名">
                    <el-input
                      v-model="personalInformationForm.realName"
                      placeholder="请输入姓名"
                    ></el-input>
                  </el-form-item>
                  <el-form-item class="sexbox" label="性别">
                    <radio-group v-model="personalInformationForm.gender">
                      <Radio label="M">男</Radio>
                      <Radio label="F">女</Radio>
                    </radio-group>
                  </el-form-item>
                  <el-form-item label="个人简介">
                    <el-input
                      v-model="personalInformationForm.introduction"
                      :autosize="{ minRows: 2, maxRows: 4 }"
                      maxlength="300"
                      placeholder="编辑个人简介"
                      show-word-limit
                      type="textarea"
                      resize="none"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="身份">
                    <Select
                      ref="identityshen"
                      v-model="personalInformationForm.identity"
                      placeholder="请选择身份"
                      @change="editShenfen"
                    >
                      <Option
                        v-for="item in identitySelectionData"
                        :key="item.id"
                        :label="item.identityName"
                        :value="item.id"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 医务工作者-->
                  <el-form-item
                    required
                    v-if="identityType === 1"
                    label="医院"
                  >
                    <autocomplete
                      v-model="personalInformationForm.company"
                      :fetch-suggestions="querySearch"
                      :trigger-on-focus="true"
                      class="inline-input"
                      placeholder="请输入医院"
                      style="width: 100%"
                      value-key="name"
                      @select="selectCompanyHandler"
                      @change="selectCompanyHandler"
                    >
                      <template slot-scope="{ item }">
                        <div class="name">{{ item.name }}</div>
                      </template>
                    </autocomplete>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 1"
                    label="科室"
                  >
                    <Select
                      v-model="personalInformationForm.department"
                      placeholder="请选择科室"
                      multiple
                      collapse-tags
                      popper-class="personalInformationForm_department_select"
                      no-data-text="请先选择医院"
                      key="departmentKey"
                    >
                      <Option
                        v-for="item in departmentOpt"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      >
                        <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                        <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                        <span style="margin-left: 10px">{{ item.name }}</span>
                      </Option>
                    </Select>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 1"
                    label="亚专业"
                  >
                    <Select
                      v-model="personalInformationForm.speciality"
                      placeholder="请选择亚专业"
                    >
                      <Option
                        v-for="item in specialitieList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 1"
                    label="职称"
                  >
                    <Select
                      v-model="personalInformationForm.title"
                      multiple
                      collapse-tags
                      popper-class="personalInformationForm_department_select"
                      placeholder="请选择职称"
                      @change="changeTitleHandler"
                      key="titleKey"
                    >
                      <Option
                        v-for="item in titleList ? titleList.listA : []"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      >
                        <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                        <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                        <span style="margin-left: 10px">{{ item.name }}</span>
                      </Option>
                      <div class="line" style="margin: 8px 10px;width:100%;height:1px;background: #EEE"></div>
                      <Option
                        v-for="item in titleList ? titleList.listB : []"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      >
                        <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                        <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                        <span style="margin-left: 10px">{{ item.name }}</span>
                      </Option>
                      <div class="line" style="margin: 8px 10px;width:100%;height:1px;background: #EEE"></div>
                      <Option
                        v-for="item in titleList ? titleList.listC : []"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      >
                        <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                        <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                        <span style="margin-left: 10px">{{ item.name }}</span>
                      </Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 医务工作者 End-->

                  <!--TODO 医学生 Start-->
                  <el-form-item required v-if="identityType === 2" label="学校">
                    <el-input
                      v-model="personalInformationForm.company"
                      placeholder="请输入学校"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 2"
                    label="亚专业"
                  >
                    <Select
                      v-model="personalInformationForm.speciality"
                      placeholder="请选择亚专业"
                    >
                      <Option
                        v-for="item in specialitieList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 医学生 End-->

                  <!--TODO 企业人士 Start-->
                  <el-form-item
                    required
                    v-if="identityType === 4" class="sexbox" label="企业类型">
                    <radio-group
                      :value="personalInformationForm.typesOfEnterprisePersonnel"
                      @input="changeTypesOfEnterprisePersonnel"
                    >
                      <Radio :label="0">原厂/总代</Radio>
                      <Radio :label="1">经销商及相关企业</Radio>
                    </radio-group>
                  </el-form-item>

                  <el-form-item
                    v-if="identityType === 4"
                    :required="!personalInformationForm.typesOfEnterprisePersonnel || personalInformationForm.typesOfEnterprisePersonnel === 0"
                    :class="personalInformationForm.typesOfEnterprisePersonnel !== 0 && personalInformationForm.typesOfEnterprisePersonnel !== 1 ? 'activeCompanyWrapper' : ''"
                    @click.native="activeCompanyHandler"
                    label="企业名称">
                    <Select
                      :disabled="personalInformationForm.typesOfEnterprisePersonnel !== 0 && personalInformationForm.typesOfEnterprisePersonnel !== 1"
                      v-model="personalInformationForm.company"
                      filterable
                      remote
                      placeholder="请输入企业"
                      :remote-method="remoteMethod"
                      @change="changeCompanyHandler"
                      :loading="identityType4loading">
                      <Option
                        style="max-width: 590px"
                        class="text-limit-1"
                        v-for="item in companyOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name">
                      </Option>

                      <div
                        style="background: #ffffff;height: 50px;display: flex;align-items: center;justify-content: center;width: 100%">
                        <div style="font-size: 12px;margin-right: 15px">没有找到您想要的企业?</div>
                        <button
                          id="themeButton"
                          style="border: none;line-height: 25px;color: #FFFFFF;font-size: 14px"
                          @click="addCompanyHandler"
                        >添加企业
                        </button>
                      </div>
                      <div v-if="!identityType4loading" slot="empty"
                           style="color: #c0c4cc; text-align: center; line-height: 50px">
                        <p>暂无数据</p>
                        <div
                          style="background: #ffffff;height: 50px;display: flex;align-items: center;justify-content: center;width: 100%">
                          <div style="font-size: 12px;margin-right: 15px">没有找到您想要的企业?</div>
                          <button
                            id="themeButton"
                            style="border: none;line-height: 25px;color: #FFFFFF;font-size: 14px"
                            @click="addCompanyHandler"
                          >添加企业
                          </button>
                        </div>
                      </div>
                    </Select>
                  </el-form-item>
                  <el-form-item
                    :required="personalInformationForm.company || !personalInformationForm.typesOfEnterprisePersonnel || personalInformationForm.typesOfEnterprisePersonnel === 0"
                    v-if="identityType === 4"
                    key="productLineNameKey"
                    label="产品线"
                    :class="personalInformationForm.typesOfEnterprisePersonnel !== 0 && personalInformationForm.typesOfEnterprisePersonnel !== 1 ? 'activeCompanyWrapper' : ''"
                    @click.native="activeCompanyHandler"
                  >
                    <Select
                      v-model="personalInformationForm.productLineName"
                      placeholder="请选择产品线"
                      no-data-text="请先选择企业"
                    >
                      <Option
                        v-for="item in productLineNameList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 4"
                    label="部门"
                  >
                    <Select
                      v-model="personalInformationForm.department"
                      placeholder="请选择部门"
                      @change="changeDepartment"
                    >
                      <Option
                        v-for="item in departmentList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 4 && personalInformationForm.department === '销售部'"
                    label="销售大区">
                    <el-input
                      v-model="personalInformationForm.salesArea"
                      placeholder="请输入销售大区"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 4 && personalInformationForm.typesOfEnterprisePersonnel === 1"
                    label="代理品牌">
                    <el-input
                      v-model="personalInformationForm.proxyBrand"
                      placeholder="请输入代理品牌"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 4 && personalInformationForm.typesOfEnterprisePersonnel === 1"
                    label="代理区域">
                    <Cascader
                      v-model="personalInformationForm.proxyArea"
                      :options="proxyAreaOptions"
                      placeholder="请选择代理区域"
                      clearable>
                    </Cascader>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 4"
                    label="亚专业"
                  >
                    <Select
                      v-model="personalInformationForm.speciality"
                      placeholder="请选择亚专业"
                    >
                      <Option
                        v-for="item in specialitieList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 企业人士 End-->

                  <!--TODO 科研人员 Start-->
                  <el-form-item v-if="identityType === 5" label="单位名称" required>
                    <el-input
                      v-model="personalInformationForm.company"
                      placeholder="请输入单位名称"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    required
                    v-if="identityType === 5"
                    label="亚专业"
                  >
                    <Select
                      v-model="personalInformationForm.speciality"
                      placeholder="请选择亚专业"
                    >
                      <Option
                        v-for="item in specialitieList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 科研人员 End-->

                  <!--TODO 感兴趣亚专业 Start-->
                  <el-form-item
                    label="感兴趣亚专业"
                  >
                    <Select
                      v-model="personalInformationForm.specialityStr"
                      placeholder="请选择感兴趣亚专业"
                      multiple
                      collapse-tags
                      popper-class="personalInformationForm_department_select"
                      @remove-tag="changeSpecialityStr"
                    >
                      <Option
                        v-for="item in InterestedSubspecialtyList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                        :disabled="item.id === personalInformationForm.speciality"
                      >
                        <svg-icon style="display: none" icon-class="select_user" class-name="select_icon"/>
                        <svg-icon icon-class="not_select_user" class-name="not_select_icon"/>
                        <span style="margin-left: 10px">{{ item.name }}</span>
                      </Option>
                    </Select>
                  </el-form-item>
                  <!--TODO 感兴趣亚专业 End-->

                  <el-form-item class="sexbox" label="身份认证">
                    <identity-state :state="personalInformationForm.isAuth">
                      <span
                        v-if="personalInformationForm.isAuth !== '1'"
                        class="identity-apply cursor"
                        @click="authenticationFun"
                      >申请认证 ></span
                      >
                    </identity-state>
                  </el-form-item>
                  <el-form-item class="button_item">
                    <el-button
                      id="themeButton"
                      class="backpreservation themeBorderRadius"
                      type="primary"
                      @click="(editPersonalFlag = false),analysysFun('返回')"
                    >返回
                    </el-button>
                    <el-button
                      id="themeButton"
                      class="preservation themeBorderRadius"
                      type="primary"
                      @click="submitPreservationFun"
                    >保存
                    </el-button>
                  </el-form-item>
                </el-form>
                <el-form v-if="!editPersonalFlag && userInfomation">
                  <el-form-item label="姓名">
                    <p class="personalInformation-user fontSize18">
                      {{ userInfomation.realName || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item class="sexbox" label="性别">
                    <p v-if="userInfomation.gender" class="personalInformation-user">
                      {{ userInfomation.gender === "M" ? "男" : "女" }}
                    </p>
                    <p v-else class="personalInformation-user">未选择</p>
                  </el-form-item>
                  <el-form-item label="个人简介">
                    <p class="personalInformation-user jianjie text-limit-5">
                      {{ userInfomation.introduction || '未填写' }}
                    </p>
                  </el-form-item>
                  <el-form-item label="身份">
                    <identity :identity="userInfomation.identity"></identity>
                  </el-form-item>
                  <el-form-item
                    v-if="userInfomation.identity === 1 || !userInfomation.identity"
                    label="医院"
                  >
                    <p class="personalInformation-user">
                      {{ userInfomation.company || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item
                    v-if="userInfomation.identity === 1 || !userInfomation.identity"
                    label="科室"
                  >
                    <p class="personalInformation-user">
                      {{ userInfomation.multiDepartment || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item
                    v-if="userInfomation.identity === 1 || !userInfomation.identity"
                    label="亚专业"
                  >
                    <p class="personalInformation-user">
                      {{ specialityListName || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item
                    v-if="userInfomation.identity === 1 || !userInfomation.identity"
                    label="职称"
                  >
                    <p class="personalInformation-user">
                      {{ userInfomation.title || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 2" label="学校">
                    <p class="personalInformation-user">
                      {{ userInfomation.company || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 2" label="亚专业">
                    <p class="personalInformation-user">
                      {{ specialityListName || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4" label="企业类型">
                    <p class="personalInformation-user">
                      {{
                        userInfomation.typesOfEnterprisePersonnel === 0 ? '原厂/总代' : userInfomation.typesOfEnterprisePersonnel === 1 ? '经销商及相关企业' : "未选择"
                      }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4" label="企业名称">
                    <p class="personalInformation-user">
                      {{ userInfomation.company || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4" label="产品线">
                    <p class="personalInformation-user">
                      {{ userInfomation.productLineName || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4" label="部门">
                    <p class="personalInformation-user">
                      {{ userInfomation.department || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4 && userInfomation.department === '销售部'"
                                label="销售大区">
                    <p class="personalInformation-user">
                      {{ userInfomation.salesArea || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4 && userInfomation.typesOfEnterprisePersonnel === 1"
                                label="代理品牌">
                    <p class="personalInformation-user">
                      {{ userInfomation.proxyBrand || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4 && userInfomation.typesOfEnterprisePersonnel === 1"
                                label="代理区域">
                    <p class="personalInformation-user">
                      {{ userInfomation.proxyArea || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 4" label="亚专业">
                    <p class="personalInformation-user">
                      {{ specialityListName || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 5" label="单位名称">
                    <p class="personalInformation-user">
                      {{ userInfomation.company || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="userInfomation.identity === 5" label="亚专业">
                    <p class="personalInformation-user">
                      {{ specialityListName || "未选择" }}
                    </p>
                  </el-form-item>
                  <el-form-item label="感兴趣亚专业">
                    <p class="personalInformation-user">
                      {{ interestedSubspecialtyName || "未填写" }}
                    </p>
                  </el-form-item>
                  <el-form-item class="sexbox" label="身份认证">
                    <identity-state :state="userInfomation.isAuth">
                      <span
                        v-if="userInfomation.isAuth !== '1'"
                        class="identity-apply cursor"
                        @click="authenticationFun"
                      >申请认证 ></span
                      >
                    </identity-state>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      id="themeButton"
                      class="preservationedit themeBorderRadius"
                      type="primary"
                      @click="editPersonalFun"
                    >编辑个人信息
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </tab-pane>
            <tab-pane label="账号设置" name="account">
              <div class="personal-information-tabs">
                <el-form
                  ref="accountSettingsForm"
                  :model="accountSettingsForm"
                  :rules="accountSettingsFormRules"
                >
                  <el-form-item label="注册手机号">
                    <p class="personalInformation-user">
                      {{ accountInfomation["mobile"] }}
                    </p>
                  </el-form-item>
                  <el-form-item v-if="!accountSettingsEmailFlag" label="邮箱">
                    <p class="personalInformation-user identity-infomation-box">
                      {{ accountInfomation.email }}
                      <span
                        v-if="accountInfomation.email"
                        class="identity-apply cursor"
                        @click="editEmailFun"
                      >更改 ></span
                      >
                      <span
                        v-if="!accountInfomation.email"
                        class="identity-apply cursor"
                        style="margin-left: 0"
                        @click="editEmailFun"
                      >绑定</span
                      >
                    </p>
                  </el-form-item>

                  <el-form-item
                    v-if="accountSettingsEmailFlag"
                    key="accountemail"
                    label="邮箱"
                    prop="email"
                  >
                    <el-input
                      v-model="accountSettingsForm.email"
                      placeholder="请输入邮箱"
                    ></el-input>
                  </el-form-item>

                  <el-form-item class="sexbox" label="绑定第三方账号">
                    <div class="identity-infomation-box">
                      <svg-icon class-name="weixinIcon" icon-class="weixin"/>
                      <span v-if="accountInfomation.weixinUid">
                        <span class="identity-type">{{
                            accountInfomation.nickName
                          }}</span>
                        <span class="identity-apply cursor" @click="unboundWeChatFun"
                        >解绑 ></span
                        >
                      </span>
                      <span v-if="!accountInfomation.weixinUid">
                        <span class="identity-type">未绑定</span>
                        <span class="identity-apply cursor" @click="bindingWechatFun"
                        >去绑定></span
                        >
                      </span>
                    </div>
                  </el-form-item>
                  <el-form-item v-if="!accountSettingsPasswordFlag" label="登录密码">
                    <p class="personalInformation-user identity-infomation-box">
                      ******
                      <span
                        class="identity-apply cursor"
                        @click="
                          (accountSettingsPasswordFlag = true),
                            (accountSettingsEmailFlag = false),
                            analysysFun('更改')
                        "
                      >更改 ></span
                      >
                    </p>
                  </el-form-item>
                  <el-form-item
                    v-if="accountSettingsPasswordFlag"
                    label="登录密码"
                    prop="newpassword"
                  >
                    <el-input
                      v-model="accountSettingsForm.newpassword"
                      autocomplete="off"
                      placeholder="请输入新密码"
                      show-password
                      type="password"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="accountSettingsPasswordFlag"
                    label="确认密码"
                    prop="password"
                  >
                    <el-input
                      v-model="accountSettingsForm.password"
                      autocomplete="off"
                      placeholder="请确认新密码"
                      show-password
                      type="password"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="accountSettingsPasswordFlag || accountSettingsEmailFlag"
                    class="button_item"
                  >
                    <el-button
                      id="themeButton"
                      class="backpreservation themeBorderRadius"
                      type="primary"
                      @click="
                        (accountSettingsPasswordFlag = false),
                          (accountSettingsEmailFlag = false),
                          analysysFun('返回')
                      "
                    >
                      返回
                    </el-button>
                    <el-button
                      id="themeButton"
                      class="preservation themeBorderRadius"
                      type="primary"
                      @click="submitAccountFun('accountSettingsForm')"
                    >保存
                    </el-button>
                  </el-form-item>
                </el-form>
              </div>
            </tab-pane>
          </Tabs>
        </div>
      </div>
    </div>
    <!-- 身份认证 -->
    <identity-authentication
      :authenticationDataInfo="authenticationDataInfo"
      :authenticationDialog.sync="authenticationDialog"
      :identity-current-father="userInfomation.identity"
      :identityFlag="!!userInfomation.identity"
      :reselect-flag="false"
      @editFlag="authenticationDialogFun"
      @updateUserInfoFun="updateUserInfoFun"
    ></identity-authentication>

    <Confirm
      :close-button-flag="false"
      :visible="emptyConfirmFlag"
      grid-gap="20px"
      buttonName="切换"
      @confirm="emptyConfirmHandler"
      @cancelFn="emptyCancelHandler"
    >
      <template #content>
        <div style="width:100%;display: flex;flex-flow: column;justify-content: center;padding: 12px 0 30px">
          <p style="text-align: center">切换企业类型后将清空企业信息，</p>
          <p style="text-align: center">是否确认切换</p>
        </div>
      </template>
    </Confirm>
  </div>
</template>

<script>
import {
  Autocomplete,
  Option,
  Radio,
  RadioGroup,
  Select,
  TabPane,
  Tabs,
  Upload,
  Checkbox,
  Cascader
} from "element-ui";
import {getInterestedSubspecialtyList} from "../../../api/personalized/default";
import Identity from "@/components/Identity/Identity";
import IdentityState from "@/components/PageComponents/IdentityState/IdentityState";
import {
  getMemberInfo,
  updateMemberEmail,
  updateMemberPassword,
  addDepartmentMemberByUserId,
  userInfo,
  updateUserInfoV3
} from "@/api/user";
import {getOpenIdAndAccessToken, unboundWeChatUser, userBindWeChat} from "@/api/login";
import {uploadOssImage} from "@/api/upload";
import validate from "@/validate/form-validate";
import IdentityAuthentication from "@/components/IdentityAuthentication/IdentityAuthentication";
import {hospitalList} from "@/api/register";
import PersonalConLeft from "@/components/PageComponents/page/PersonalConLeft/PersonalConLeft";
import {
  getBrandListByKeywords,
  getBrandProductLineListByBrandName,
  getCompanyDepartments, getDepartmentList, getShowHospitalList,
  saveCompanyToBrand,
  getAllProvinceCityInfo
} from "../../../api/user";
import {getSpecialities, getTitles} from "../../../api/register";
import Confirm from "../../../components/optimize-components/UI/Confirm/index.vue";
import data from '../../../assets/js/address-filtering'

export default {
  async asyncData({app, params, error, store, query, req}) {
    // app.$cookies.set("medtion_token_only_sign",111)
    app.head.title = "个人中心";
    const [request1, request5] = await Promise.all([
      /**
       * request1 用户信息
       * request5 获取用户账户信息
       */
      app.$axios.$request(userInfo()),
      app.$axios.$request(
        getMemberInfo({
          userId: store.state.auth.user.id,
        })
      ),
    ]);
    const email = /^(\w+\.?)*\w+@(?:\w+\.)\w+$/; // 邮箱
    return {
      userInfomation: request1.result,
      accountInfomation: request5.result,
      editEmailFlag: email.test(request5.result.username),
    };
  },
  data() {
    const confirmPassword = (rule, value, callback) => {
      if (value === "") {
        callback({tip: "请再次输入密码", isEnable: false});
      } else if (value !== this.accountSettingsForm.newpassword) {
        callback({tip: "两次密码不一致", isEnable: false});
      } else {
        callback({isEnable: true});
      }
    };
    return {
      typesOfEnterprisePersonnel: null,
      proxyAreaOptions: [],
      emptyConfirmFlag: false,
      titleList: null,
      specialitieList: [],
      hospitalDefaultList: [],
      departmentList: [],
      productLineNameList: [],
      companyOptions: [],
      queryCompany: "",
      identityType4loading: false,
      InterestedSubspecialtyList: [],
      departmentOpt: [],
      authenticationDataInfo: {}, // 身份认证信息
      authenticationDialog: false, // 身份认证弹框
      imageUrl: "", // 头像
      editPersonalFlag: false, // 编辑个人信息
      personalInformationForm: {
        department: [],
        specialityStr: []
      }, // 修改个人信息表达
      identitySelectionData: [
        {
          id: 1,
          name: "Medicalwork",
          identityName: "医务工作者",
          identityContent: "医生、药师、护士",
        },
        {
          id: 2,
          name: "Medicalstudents",
          identityName: "医学生",
          identityContent: "医学院在校学生",
        },
        {
          id: 4,
          name: "Businesspeople",
          identityName: "企业人士",
          identityContent: "从事制药、医疗器械等工作",
        },
        {
          id: 5,
          name: "Scientificresearchers",
          identityName: "科研人员",
          identityContent: "从事科学研究、科学实验等工作",
        },
      ], // 身份选择
      identityType: "Medicalwork", // 认证状态
      identityFlag: false, // 确认身份认证
      accountSettingsForm: {}, // 账号设置表单
      accountSettingsFormRules: {
        newpassword: [
          {
            validator: validate.elformValidate().RegisterPaswword,
            trigger: "change",
          },
        ],
        password: [{validator: confirmPassword, trigger: "change"}],
        email: [{validator: validate.loginformValidate().Email, trigger: "change"}],
      },
      accountSettingsEmailFlag: false, // 编辑账号设置邮箱开关
      accountSettingsPasswordFlag: false, // 编辑账号设置密码开关
    };
  },
  watch: {
    'personalInformationForm.speciality'(newValue) {
      if (newValue) {
        if (this.personalInformationForm.specialityStr && typeof this.personalInformationForm.specialityStr === 'object') {
          if (!this.personalInformationForm.specialityStr.includes(newValue)) {
            if (this.InterestedSubspecialtyList.filter(item => item.id === Number(newValue)).length > 0) {
              this.personalInformationForm.specialityStr.push(newValue)
            }
          }
        }
      }
    }
  },
  head() {
    return {
      title: "个人中心",
    };
  },
  name: "PersonalDataPage",
  components: {
    Confirm,
    PersonalConLeft,
    Tabs,
    TabPane,
    Upload,
    RadioGroup,
    Radio,
    Select,
    Option,
    Identity,
    IdentityState,
    IdentityAuthentication,
    Autocomplete,
    Checkbox,
    Cascader,
  },
  computed: {
    specialityListName() {
      let name = ""
      if (this.userInfomation.specialityList && this.userInfomation.specialityList.length > 0) {
        this.userInfomation.specialityList.forEach(item => {
          name += item.name + ",";
        })
        name = name.substring(0, name.length - 1);
      }

      return name
    },
    interestedSubspecialtyName() {
      let name = ""
      if (this.userInfomation.interestedSubspecialty && this.userInfomation.interestedSubspecialty.length > 0) {
        this.userInfomation.interestedSubspecialty.forEach(item => {
          name += item.name + ",";
        })
        name = name.substring(0, name.length - 1);
      }

      return name
    }
  },
  mounted() {
    this.getDataHandler()
    if (this.$route.query.code && this.$route.query.wechat_login === "F") {
      this.bingdingweChatFun();
    }
    this.getDefaultSelectListHandler()

    this.$axios.$request(getBrandListByKeywords({
      keywords: '',
      typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
    })).then(res => {
      if (res.code === 1) {
        this.companyOptions = res.list
      }
    })

    this.$axios.$request(getShowHospitalList()).then(res => {
      if (res.code === 1) {
        this.hospitalDefaultList = res.list
      }
    })
  },
  methods: {
    emptyConfirmHandler(params, callBackFn) {
      this.personalInformationForm.typesOfEnterprisePersonnel = this.typesOfEnterprisePersonnel
      callBackFn(false)
      this.emptyConfirmFlag = false;
      this.$axios.$request(getBrandListByKeywords({
        keywords: '',
        typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.companyOptions = res.list
        }
      })
      this.$set(this.personalInformationForm, 'proxyBrand', "")
      this.$set(this.personalInformationForm, 'proxyArea', [])
      this.personalInformationForm.company = ""
      this.personalInformationForm.productLineName = ""
      this.personalInformationForm.department = ""
      this.personalInformationForm.salesArea = ""
      this.productLineNameList = []
    },
    emptyCancelHandler() {
      this.emptyConfirmFlag = false;
    },
    activeCompanyHandler() {
      if (this.personalInformationForm.typesOfEnterprisePersonnel !== 0 && this.personalInformationForm.typesOfEnterprisePersonnel !== 1) {
        this.$toast('请先选择企业类型')
      }
    },
    /**
     * 切换企业类型
     * @returns {Promise<void>}
     */
    changeTypesOfEnterprisePersonnel(value) {
      this.typesOfEnterprisePersonnel = value
      this.emptyConfirmFlag = true;
    },
    async getDataHandler() {
      const [titles, specialities, allProvinceCityInfo] = await Promise.all([
        this.$axios.$request(getTitles()),
        this.$axios.$request(getSpecialities()),
        this.$axios.$request(getAllProvinceCityInfo())
      ])

      if (titles.code === 1) {
        this.titleList = titles.result
      }

      if (specialities.code === 1) {
        this.specialitieList = specialities.list
      }

      if (allProvinceCityInfo.code === 1) {
        this.proxyAreaOptions = allProvinceCityInfo.list.map(item => {
          return {
            value: item.province,
            label: item.province,
            children: ["北京市", "天津市", "上海市", "重庆市"].includes(item.province) ? [
              {
                value: item.province,
                label: item.province,
              }
            ] : [{value: "全部", label: "全部"}].concat(item.cityList.map(itemCity => {
              return {
                value: itemCity.city,
                label: itemCity.city,
              }
            }))
          }
        })
      }
    },


    changeDepartment() {
      this.$set(this.personalInformationForm, 'salesArea', null)
    },
    changeSpecialityStr(params) {
      if (params === this.personalInformationForm.speciality) {
        this.$toast('不可删除')
        this.personalInformationForm.specialityStr.push(params)
      }
      return false;

    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)          -- 2023-09-21 15:46
     * 远程搜索企业
     * ------------------------------------------------------------------------------
     */
    remoteMethod(query) {
      this.queryCompany = query;
      if (query !== '') {
        this.identityType4loading = true;
        // eslint-disable-next-line no-undef
        this.$axios.$request(getBrandListByKeywords({
          keywords: query,
          typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
        })).then(res => {
          if (res.code === 1) {
            this.identityType4loading = false;
            this.companyOptions = res.list;

            // this.$axios.$request(getBrandProductLineListByBrandName({
            //   brandName: query,
            //   typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
            // })).then(res => {
            //   if (res.code === 1) {
            //     this.productLineNameList = res.list
            //   }
            // })
          }
        })
      } else {
        this.companyOptions = [];
      }
    },
    addCompanyHandler() {
      this.$axios.$request(saveCompanyToBrand({
        company: this.queryCompany,
        typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
      })).then(res => {
        if (res.code === 1) {
          this.personalInformationForm.company = this.queryCompany
          this.changeCompanyHandler(this.queryCompany)
          this.companyOptions.push({
            id: new Date().getTime(),
            name: this.queryCompany
          })
        }
      })
    },
    changeCompanyHandler(params) {
      if (this.personalInformationForm.typesOfEnterprisePersonnel === 0 || this.personalInformationForm.typesOfEnterprisePersonnel === 1) {
        this.personalInformationForm.productLineName = null;
        this.$axios.$request(getBrandProductLineListByBrandName({
          brandName: params,
          typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
        })).then(res => {
          if (res.code === 1) {
            this.productLineNameList = res.list
          }
        })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-09-20 17:22
     * 切换职称
     * ------------------------------------------------------------------------------
     */
    changeTitleHandler(params) {
      let listA = []
      let listB = []
      let listC = []

      params.forEach(itemParams => {
        for (const key in this.titleList) {
          this.titleList[key].forEach((item, index) => {
            if (key === "listA" && itemParams === item.name) {
              listA = [itemParams]
            }

            if (key === "listB" && itemParams === item.name) {
              listB = [itemParams]
            }

            if (key === "listC" && itemParams === item.name) {
              listC = [itemParams]
            }

          })
        }
      })
      this.personalInformationForm.title = listA.concat(listB, listC)
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-09-20 16:19
     * 获取感兴趣亚专业
     * ------------------------------------------------------------------------------
     */
    getDefaultSelectListHandler() {
      this.$axios.$request(getInterestedSubspecialtyList({
        loginUserId: this.$store.state.auth.user.id
      })).then(res => {
        if (res.code === 1) {
          this.InterestedSubspecialtyList = res.list
        }
      })

      this.$axios.$request(getCompanyDepartments()).then(res => {
        if (res.code === 1) {
          this.departmentList = res.list;
        }
      })
    },
    selectCompanyHandler(item) {
      this.personalInformationForm.department = [];
      this.$axios.$request(getDepartmentList({
        hospitalName: item.name
      })).then(res => {
        if (res.code === 1) {
          this.departmentOpt = res.list.map((item, index) => {
            return {
              id: index,
              name: item
            }
          })
        }
      })
    },
    /**
     * 输入医院进行搜索
     * @param queryString
     * @param cb
     */
    querySearch(queryString, cb) {
      if (queryString) {
        this.$axios.$request(hospitalList({
          hospital: queryString,
        })).then((res) => {
          if (res && res.code === 1) {
            const restaurants = res.list;
            // 调用 callback 返回建议列表的数据
            cb(restaurants);
          }
        });
      } else {
        cb(this.hospitalDefaultList);
      }

    },
    // 身份认证
    authenticationFun() {
      this.$axios.$request(userInfo()).then((res) => {
        if (res && res.code === 1) {
          if (!res.result.identity || res.result.identity === "undefined") {
            const h = this.$createElement;
            this.$msgbox({
              message: h("p", null, [
                h(
                  "span",
                  null,
                  "部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！"
                ),
              ]),
              center: true,
              showCancelButton: true,
              cancelButtonText: "以后再说",
              confirmButtonText: "去完善信息",
              customClass: "personaldata-messagebox",
              beforeClose: (action, instance, done) => {
                if (action === "confirm") {
                  this.$store.commit(
                    "editAccountTypeFun",
                    this.$store.state.auth.user.email
                      ? this.$store.state.auth.user.email
                      : "tel"
                  );
                  this.$store.commit("editIdentityInformationFun", "SelectIdentity"); // 到身份选择页面
                  this.$router.push({name: "register"});
                  done();
                } else {
                  done();
                }
              },
            });
          } else {
            this.authenticationDataInfo = res.result;
            this.authenticationDialog = true;
          }
        }
      });
    },
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title);
    },
    // 获取code 绑定微信
    async bingdingweChatFun() {
      let weChatInfo = {}; // 微信登录信息
      /**
       * 替换code
       * @type {any}
       */
      const newQuery = JSON.parse(JSON.stringify(this.$route.query)); // 深拷贝
      delete newQuery.code;
      this.$router.replace({query: newQuery});
      if (this.$route.query.code && this.$route.query.wechat_login === "F") {
        await this.$axios
          .$request(
            getOpenIdAndAccessToken({
              code: this.$route.query.code,
            })
          )
          .then((res) => {
            if (res && res.code === 1) {
              weChatInfo = res.result;
            }
          });
        await this.$axios
          .$request(
            userBindWeChat({
              unionid: weChatInfo.unionid,
              username: this.$store.state.auth.user.username,
              nickname: weChatInfo.nickname,
            })
          )
          .then((res) => {
            this.$toast(res.message);
            if (res.code === 1) {
              this.accountSettingsForm = {};
              this.accountInfomation = res.result;
            }
          });
      }
    },
    // 身份认证
    authenticationDialogFun(data) {
      this.authenticationDialog = data;
    },
    // 更新用户信息
    updateUserInfoFun(item) {
      this.userInfomation = item;
    },
    // 编辑个人信息
    editPersonalFun() {

      this.analysysFun("编辑个人信息");
      this.editPersonalFlag = true;
      // 默认填充数据 Start
      this.personalInformationForm = JSON.parse(JSON.stringify(this.userInfomation));

      this.identityType = this.personalInformationForm.identity;


      if (this.identityType !== 4) {
        this.personalInformationForm.department = this.personalInformationForm.departmentList;
      }

      if (this.personalInformationForm.specialityList && this.personalInformationForm.specialityList.length > 0) {
        this.personalInformationForm.speciality = this.personalInformationForm.specialityList[0].id
      } else {
        this.personalInformationForm.speciality = null
      }
      if (this.personalInformationForm.title) {
        this.personalInformationForm.title = this.personalInformationForm.title.split(',')
      }
      if (this.personalInformationForm.interestedSubspecialty && this.personalInformationForm.interestedSubspecialty.length > 0) {
        const interestedSubspecialtyArr = this.personalInformationForm.interestedSubspecialty.map(item => item.id)
        this.$set(this.personalInformationForm, 'specialityStr', interestedSubspecialtyArr)
      }
      // End


      if (this.personalInformationForm.identity === 3) {
        this.$set(this.personalInformationForm, "identity", "患者及其家属");
      }

      // 如果有亚专业 默认感兴趣亚专业勾选
      if (this.personalInformationForm.speciality) {
        if (!this.personalInformationForm.specialityStr.includes(this.personalInformationForm.speciality)) {
          this.personalInformationForm.specialityStr.push(this.personalInformationForm.speciality)
        }
      }


      if (this.identityType === 1 && this.personalInformationForm.company) {
        this.$axios.$request(getDepartmentList({
          hospitalName: this.personalInformationForm.company
        })).then(res => {
          if (res.code === 1) {
            this.departmentOpt = res.list.map((item, index) => {
              return {
                id: index,
                name: item
              }
            })
          }
        })
      }

      if (this.identityType === 4 && this.personalInformationForm.company && this.personalInformationForm.typesOfEnterprisePersonnel) {
        this.$axios.$request(getBrandProductLineListByBrandName({
          brandName: this.personalInformationForm.company,
          typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel
        })).then(res => {
          if (res.code === 1) {
            this.productLineNameList = res.list
          }
        })
      }
      if (this.identityType === 4 && this.personalInformationForm.proxyArea) {
        this.personalInformationForm.proxyArea = this.personalInformationForm.proxyArea.split(',')
      }
    },
    // 身份选中 清空
    editShenfen(item) {
      // 弹框
      if (!this.identityFlag) {
        // specialityStr
        let specialityStr = "";
        if (this.personalInformationForm.specialityStr.length) {
          this.personalInformationForm.specialityStr.forEach((item) => {
            specialityStr += item + ",";
          });

          specialityStr = specialityStr.substring(0, specialityStr.length - 1);
        }


        const h = this.$createElement;
        this.$msgbox({
          message: h("p", null, [
            h("span", null, "更换身份之后您的信息需要重新录入,"),
            h("p", null, "是否确认更换？"),
          ]),
          center: true,
          showCancelButton: true,
          cancelButtonText: "暂不替换",
          confirmButtonText: "更换",
          customClass: "personaldata-messagebox",
          beforeClose: (action, instance, done) => {
            if (action === "confirm") {
              this.identityFlag = false;
              this.$set(this.personalInformationForm, 'company', '')
              this.$set(this.personalInformationForm, 'department', '')
              this.$set(this.personalInformationForm, 'proxyBrand', '')
              this.$set(this.personalInformationForm, 'proxyArea', '')
              this.$set(this.personalInformationForm, 'title', '')
              this.$set(this.personalInformationForm, 'speciality', '')
              this.$set(this.personalInformationForm, 'productLineName', '')
              this.$set(this.personalInformationForm, 'salesArea', '')
              if (this.personalInformationForm.interestedSubspecialty && this.personalInformationForm.interestedSubspecialty.length > 0) {
                const interestedSubspecialtyArr = this.personalInformationForm.interestedSubspecialty.map(item => item.id)
                this.$set(this.personalInformationForm, 'specialityStr', interestedSubspecialtyArr)
              }
              this.identityType = item;
              done();
            } else {
              this.identityFlag = false;
              this.personalInformationForm.identity = this.userInfomation.identity;
              done();
            }
          },
        });
      }
    },
    // 解绑微信
    unboundWeChatFun() {
      const h = this.$createElement;
      this.$msgbox({
        message: h("p", null, [
          h("span", null, "解绑微信后您将无法使用微信登陆脑医汇,"),
          h("span", null, " 确定要解绑吗？"),
        ]),
        center: true,
        showCancelButton: true,
        cancelButtonText: "暂不解绑",
        confirmButtonText: "解绑",
        customClass: "personaldata-messagebox",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            this.$axios
              .$request(
                unboundWeChatUser({
                  username: this.$store.state.auth.user.username,
                })
              )
              .then((res) => {
                if (res.code === 1) {
                  this.accountSettingsForm = {};
                  this.accountInfomation = res.result;
                  this.$toast.success("解绑成功");
                }
              });
            done();
          } else {
            done();
          }
        },
      });
    },
    // tabs点击切换
    editTabsStateFun(item) {
      this.$analysys.btn_click(item.label, document.title);
      this.$store.commit("editPersonalFun", item.name);
    },
    // 保存个人信息
    submitPreservationFun() {
      this.$toast.loading({message: '修改中', duration: 0,})
      this.$analysys.btn_click("保存个人信息", document.title);

      // department
      let department = "";
      if (this.personalInformationForm.department && this.personalInformationForm.department.length && typeof this.personalInformationForm.department === 'object') {
        this.personalInformationForm.department.forEach((item) => {
          department += item + ",";
        });
        department = department.substring(0, department.length - 1);
      } else if (this.personalInformationForm.department && typeof this.personalInformationForm.department === 'string') {
        department = this.personalInformationForm.department
      }
      // title
      let title = "";
      if (this.personalInformationForm.title && this.personalInformationForm.title.length) {
        this.personalInformationForm.title.forEach((item) => {
          title += item + ",";
        });
        title = title.substring(0, title.length - 1);
      }
      // specialityStr
      let specialityStr = "";
      if (this.personalInformationForm.specialityStr && this.personalInformationForm.specialityStr.length) {
        this.personalInformationForm.specialityStr.forEach((item) => {
          specialityStr += item + ",";
        });

        specialityStr = specialityStr.substring(0, specialityStr.length - 1);
      }

      // departmentCode
      let departmentCode = ""
      if (this.identityType === 4) {
        const departmentArr = this.departmentList.filter(item => item.name === this.personalInformationForm.department)
        if (departmentArr.length > 0) {
          departmentCode = departmentArr[0].code
        }
      }

      const updateInfo = {
        userId: this.personalInformationForm.id,
        identity: this.personalInformationForm.identity,
        avatar: this.imageUrl || this.personalInformationForm.avatarAddress || null,
        realName: this.personalInformationForm.realName,
        gender: this.personalInformationForm.gender,
        userType: this.personalInformationForm.userType,
        typesOfEnterprisePersonnel: this.personalInformationForm.typesOfEnterprisePersonnel,
        company: this.personalInformationForm.company,
        department,
        proxyBrand: this.personalInformationForm.proxyBrand,
        proxyArea: this.personalInformationForm.proxyArea ? this.personalInformationForm.proxyArea.join(',') : null,
        title,
        specialityIds: this.personalInformationForm.speciality,
        introduction: this.personalInformationForm.introduction,
        specialityStr,
        productLineName: this.personalInformationForm.productLineName,
        // 销售大区
        salesArea: this.personalInformationForm.salesArea,
        departmentCode
      }

      const str = validate.identityFormVerification(this.identityType, updateInfo)
      if (str) {
        this.$toast(str)
      } else {
        this.$axios.$request(updateUserInfoV3(updateInfo)).then((resF) => {
          if (resF && resF.code === 1) {
            this.$axios.$request(addDepartmentMemberByUserId()).then(res => {
              if (res.code === 1) {
                this.$toast.success("修改成功");
                this.editPersonalFlag = false;
                this.userInfomation = resF.result;
                // 持久化登录 所以在更改用户头像的时候， 也需要讲cookie里面的信息更改掉
                this.$store.commit("auth/setUserInfo", {
                  user: resF.result,
                  this: this,
                });
              }
            });
          }
        });
      }
    },
    // 实现图片上传功能
    httpRequest(item) {
      // 验证图片格式大小信息
      const isJPG = item.file.type.startsWith('image/');
      const isLt2M = item.file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("请选择图片文件");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      const fileObj = item.file;
      // 图片格式大小信息没问题 执行上传图片的方法
      if (isJPG && isLt2M === true) {
        // 定义FormData对象 存储文件
        let mf = new FormData();
        // 将图片文件放入mf
        mf.append("file", fileObj);
        mf.append("module", "avatar");
        this.$axios.$request(uploadOssImage(mf)).then((res) => {
          if (res && res.code === 1) {
            this.imageUrl = res.result.filePath;
          }
        });
      }
    },
    // 绑定微信
    bindingWechatFun() {
      this.analysysFun("去绑定");
      let callBackUrlname = window.location.href;
      if (!this.$route.query.wechat_login) {
        callBackUrlname = callBackUrlname + "?wechat_login='F'";
      }
      window.location.href =
        "https://open.weixin.qq.com/connect/qrconnect?appid=" +
        this.$appid +
        "&redirect_uri=" +
        this.$DDNS_URL +
        "&response_type=code&scope=snsapi_login&state=" +
        callBackUrlname;
    },
    // 修改账户信息
    submitAccountFun(formName) {
      this.analysysFun("保存");
      let storage = [];
      let flag = true;
      this.$refs[formName].validateField(
        ["password", "newpassword", "email"],
        (valid) => {
          storage.push(valid);
        }
      );
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false;
          this.$toast.fail(storage[i].tip);
          return;
        }
      }
      if (flag) {
        if (this.accountSettingsEmailFlag) {
          this.$axios
            .$request(
              updateMemberEmail({
                userId: this.accountInfomation.id,
                email: this.accountSettingsForm.email,
              })
            )
            .then((res) => {
              if (res && res.code === 1) {
                this.$axios
                  .$request(
                    getMemberInfo({
                      userId: this.accountInfomation.id,
                    })
                  )
                  .then((res) => {
                    if (res && res.code === 1) {
                      this.accountSettingsForm = {};
                      this.accountInfomation = res.result;
                      this.$toast.success("邮箱修改成功");
                      this.accountSettingsEmailFlag = false;
                    }
                  });
              }
            });
        } else if (this.accountSettingsPasswordFlag) {
          this.$axios
            .$request(
              updateMemberPassword({
                userId: this.accountInfomation.id,
                password: this.accountSettingsForm.password,
              })
            )
            .then((res) => {
              if (res && res.code === 1) {
                this.accountSettingsForm = {};
                this.$toast.success("密码修改成功");
                this.accountSettingsPasswordFlag = false;
              }
            });
        }
      }
    },
    // 修改邮箱
    editEmailFun() {
      this.analysysFun("修改");
      this.accountSettingsEmailFlag = true;
      this.accountSettingsPasswordFlag = false;
      this.$set(this.accountSettingsForm, "email", this.accountInfomation.email);
    },
  },
};
</script>

<style lang="less">
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 0;
  right: 0;
}

.el-cascader-panel .el-cascader-menu__wrap {
  height: 204px
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}

.personalInformationForm_department_select .el-select-dropdown__item {
  height: 44px;
  line-height: 44px;
  display: flex;
  align-items: center;
}

.personalInformationForm_department_select .el-select-dropdown__item::after {
  display: none;
}

.personalInformationForm_department_select .selected {
  color: var(--theme-color);
  font-weight: unset;
}

.personalInformationForm_department_select .selected .select_icon {
  display: block !important;
}

.personalInformationForm_department_select .selected .not_select_icon {
  display: none !important;
}


/* message box */
.personaldata-messagebox {
  width: 372px;
  padding: 36px 0 40px;
  text-align: center;
  border-radius: 30px;
  box-sizing: border-box;

  p {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
  }
}

.personaldata-messagebox > .el-message-box__content {
  padding: 0 !important;
  text-align: center;
}

.personaldata-messagebox > .el-message-box__btns {
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  margin-top: 30px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button {
  float: left;
  width: 120px;
  height: 38px;
  line-height: 38px;
  padding: 0 !important;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--small {
  background: #efefef;
  border-radius: 12px;
  border: 0;
  color: #80a4b9;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--primary {
  background: #0581ce;
  color: #ffffff;
}
</style>
<style lang="less" scoped>
@import "./index";
</style>
