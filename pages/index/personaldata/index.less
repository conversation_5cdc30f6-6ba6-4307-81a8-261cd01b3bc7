.personaldata-container-box {
  margin-top: 20px;

  .personaldata-container-left {
    width: 285px;
    .mixin_desktop({
      width: 20%;
      .container-imguser {
        padding: 20px 0 30px !important;
      }
    });

    .container-imguser {
      border-radius: 6px;
      background: #fbfbfb;
      text-align: center;
      padding: 25px 45px 25px;
      max-height: 410px;
    }

    .personaldata-container-left-imagebox {
      width: 98px;
      height: 98px;
      overflow: hidden;
      margin: 0 auto 24px;
    }

    .personaldata-container-left-personinfo {
      .personinfo-name {
        color: #666666;
        font-size: 15px;
        margin-bottom: 12px;
        text-align: left;

        .authentication {
          width: 18px;
          height: 18px;
        }

        .name {
          font-size: 20px;
          font-weight: 500;
          color: #202020;
          margin-right: 6px;
        }
      }

      .personinfo-company,
      .personinfo-companyDepartment {
        max-width: 200px;
        margin: 0 auto;
        color: #333333;
        line-height: 24px;
        font-size: 14px;
      }
    }
  }

  .personaldata-container-right {
    width: 895px;
    .mixin_desktop({
      width: 75%;
    });


    .personaldata-container-select {
      /deep/ .el-tabs__item {
        user-select: none !important;
        color: #666666;
        padding: 0 15px;

        &:focus {
          user-select: none;
          border: none;
          box-shadow: none !important;
        }
      }

      /deep/ .el-form-item__error {
        display: none;
      }

      /deep/ .el-tabs__content {
        text-align: center;
      }

      /deep/ .el-tabs__active-bar {
        height: 3px;
      }

      /deep/ .el-tabs__item.is-active {
        color: #333333;
      }

      /deep/ .el-tabs__nav-wrap {
        padding: 0 26px;
        height: 54px;
        background: #fbfbfb;
        border-radius: 6px;
        line-height: 54px;
      }

      /deep/ .el-tabs__nav-wrap::after {
        display: none;
      }

      .personal-information-tabs {
        margin: 15px 0 200px;

        .modify-avatar,
        .nullupload {
          user-select: none;
          margin: 0 auto 40px;
          width: 120px;
          height: 120px;
          position: relative;
          border-radius: 50%;
          overflow: hidden;

          .mack {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.1);
            text-align: center;
            line-height: 120px;
            color: #ffffff;
          }
        }

        .identity-infomation-box {
          padding-left: 16px;
          font-size: 16px;

          .weixinIcon {
            width: 20px;
            height: 20px;
          }

          .identity-type {
            color: #888888;
            margin-left: 16px;
          }

          .identity-apply {
            color: #0581ce;
            margin-left: 20px;
          }
        }

        .personalInformation-user {
          padding-left: 16px;
          text-align: left;
          font-size: 16px;
          color: #333333;
        }

        .jianjie {
          display: inline-block !important;
          line-height: 19px;
          vertical-align: middle;
        }

        /deep/ .el-icon-arrow-up:before {
          font-family: 'iconfontNew';
          content: '\e61d';
          font-size: 10px;
        }

        /deep/ .el-form-item__label {
          width: 153px;
          line-height: 48px;
          padding-right: 40px;
          font-size: 16px;
          color: #666666;
        }

        /deep/ .el-form-item__content {
          margin-left: 153px;
          text-align: left;
          .mixin_desktop({
            width: calc(100% - 153px) !important;
          });
        }

        /deep/ .el-input,
        /deep/ .el-form-item__content {
          width: 590px;
          line-height: 48px;
          .mixin_desktop({
            width: calc(100% - 153px) !important;
          });
        }

        /deep/ .el-select {
          width: 100%;
        }

        /deep/ .el-radio__label {
          font-size: 16px;
        }

        /deep/ .el-form-item {
          margin: 0 auto 18px;

          &:last-child {
            margin: 0 auto;
          }
        }

        .sexbox {
          text-align: left;
        }

        /deep/ .el-textarea .el-input__count {
          right: 15px;
          line-height: 18px;
          background: #f5f5f5;
        }

        /deep/ .el-textarea__inner {
          border: none;
          padding: 5px 15px 26px;
          background: #f5f5f5;
        }

        /deep/ .el-input__inner {
          border: none;
          line-height: 48px;
          height: 48px;
          background: #f5f5f5;
          border-radius: 6px;
          font-size: 16px;

          &::placeholder {
            font-size: 16px;
          }
        }

        .preservationedit {
          margin-top: 22px;
          width: 100%;
          background: #0581ce;
          line-height: 48px;
          padding: 0;
          border: none;
          float: right;
        }

        .preservation {
          margin-top: 22px;
          width: 45%;
          background: #0581ce;
          line-height: 48px;
          padding: 0;
          border: none;
          float: right;
        }

        .backpreservation {
          margin-top: 22px;
          width: 45%;
          background: #efefef !important;
          line-height: 48px;
          padding: 0;
          border: none;
          color: #80a4b9;
          float: left;
        }
      }
    }
  }
}

/deep/ .button_item > .el-form-item__content {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

/deep/ .check_wrapper {
  position: relative;

  &::before {
    z-index: 10;
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
  }
}


/deep/ .activeCompanyWrapper {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
  }
}
