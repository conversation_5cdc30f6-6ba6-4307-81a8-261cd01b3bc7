<template>
  <SearchPageContainer :styles="{padding:'20px 0 50px'}">
    <template #top>
      <!--      <SearchNav ref="SearchNav"/>-->
      <div v-if="bannerList.length>0" class="search_bg_wrapper">
        <el-carousel
          indic ator-position='none'
          :arrow="bannerList.length > 1 ? 'hover' : 'never'"
        >
          <el-carousel-item v-for='(item) in bannerList' :key='item.id'>
            <div class='search_banner_wrapper'>
              <img
                :alt='item.name'
                :class="item.extras ? 'cursor' : ''"
                :src='$tool.compressImg(item.image,1128,94)'
                class='img_cover'
                @click='jumpBannerFun(item)'
              />
            </div>
          </el-carousel-item>
        </el-carousel>
        <!--        <img src="~assets/images/search/bg_default.jpg" alt="search">-->
      </div>
      <div class="search_box">
        <SearchInput ref="SearchInput" :search-value-default="searchValueDefault"/>
        <div class="search_right_btn_wrapper">
          <nuxt-link :to="`/askAI/char/home?question=${searchValueDefault}`"><img class="toaiclass" :src="aiSearch" alt=""></nuxt-link>
<!--          <div class="search_right_btn_ask" @click="askHandler"></div>-->
          <a target="_blank" href="/advanced-search" class="search_senior_btn"></a>
        </div>
      </div>
    </template>
    <template #left>
      <div class="search_right_wrapper" :style="{position:'relative'}">
        <div ref="positioning" class="positioning_wrapper"></div>
        <SearchTab/>
        <ShortVideoPlayback
          :video-id='$store.state.bms.bmsHomeShortVideoId'
          :visible='$store.state.bms.bmsHomeShortVisible'
          @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
        />
      </div>
    </template>
    <template #right>
      <div ref="search_right_wrapper" class="search_right_wrapper" :style="{position: 'sticky'}">

        <HotSearch :list="hotSearchList"/>
        <RenownedExperts/>
        <HotAnswer/>
        <HotTalk/>
      </div>
    </template>
  </SearchPageContainer>

</template>

<script>
import {
  HotSearch,
  RenownedExperts,
  HotAnswer,
  HotTalk,
  SearchInput,
  SearchTab,
} from "../../../components/optimize-components/page-components/search";
import SearchPageContainer from "../../../components/optimize-components/UI/SearchPageContainer/index.vue";
import {getLeaderBoard} from "../../../api/search";
import ShortVideoPlayback from "../../../components/optimize-components/public/ShortVideoPlayback/index.vue";
import {getSlotContent} from "../../../api/banner/banner";
import brandAdJump from "../../../assets/helpers/brandAdJump";
import { loginByToken } from '@/api/login'
import { userInfo } from '@/api/user'

export default {
  name: "SearchPage",
  components: {
    ShortVideoPlayback,
    SearchPageContainer,
    HotSearch,
    RenownedExperts,
    HotAnswer,
    HotTalk,
    SearchInput,
    SearchTab,
  },
  async asyncData({app, params, error, store, query, req}) {
    const [hotSearchData, bannerData] = await Promise.all([
      app.$axios.$request(getLeaderBoard()),
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webapi_search_page_ad',
        })
      )
    ])

    return {
      hotSearchList: hotSearchData.list,
      bannerList: bannerData.list,
      searchValueDefault:query.keywords,
    }
  },
  data() {
    return {
      rightTop: 0,
      bannerIO: null,
      aiSearch: require('@/assets/images/ai/search_ai.png')

    }
  },
  head() {
    return {
      title: '搜索'
    }
  },
  mounted() {
    // eslint-disable-next-line no-undef
    setTimeout(() => {
      const rightWrapper = this.$refs.search_right_wrapper
      if (rightWrapper) {
        this.rightTop = (rightWrapper.clientHeight - window.innerHeight) + 50

        rightWrapper.style.top = "-" + this.rightTop + "px"
      }
    }, 1000)

    const positioning = this.$refs.positioning
    const SearchNav = this.$refs.SearchInput?.$el?.querySelector('#top_nav_search_input')
    const headerBox = document.querySelector('#nav_header')
    // eslint-disable-next-line no-undef
    this.bannerIO = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (!entry.isIntersecting) {

          if (headerBox) {
            headerBox.style.cssText = 'transform:translateY(-100%)'
          }

          if (SearchNav) {
            SearchNav.classList.add('search_nav_top_wrapper')
            SearchNav.style.marginTop = '76px'
          }

        } else {
          if (headerBox) {
            headerBox.style.cssText = 'transform:none'
          }

          if (SearchNav) {
            SearchNav.classList.remove('search_nav_top_wrapper')
            SearchNav.style.marginTop = '0'
          }

        }
      })
    });

    this.bannerIO.observe(positioning);
  },
  beforeDestroy() {
    if (this.bannerIO) {
      this.bannerIO.disconnect()
      // 清空 IntersectionObserver 实例
      this.bannerIO = null;
    }

    const headerBox = document.querySelector('.header-bigbox')
    if (headerBox) {
      headerBox.style.cssText = 'transform:none'
    }
  },
  methods: {
    // 跳转广告外部连接
    jumpBannerFun(item) {
      this.$analysys.ad_click_new({
        adId: item.adId,
        adUrl: item.extras,
        adModule: item.module,
        unionid: this.$store.state.auth.unionid,
        adClickLocation: item.clickLocation,
        adCode: item.code,
        adExtras: item.extras,
        adName: item.name
      })
      brandAdJump(this, item.module, item.extras)
    },
    // 提问跳转
    askHandler() {
      const localToken = this.$cookies.get('medtion_token_only_sign')
      const isLogged = this.$store.state.auth.isLogged
      if (localToken || isLogged) {
        // 判断登录
        this.$axios.$request(loginByToken({
          token: localToken
        })).then(response => {
          if (response.code === 1) {
            this.$axios.$request(userInfo()).then(res => {
              if (res && res.code === 1) {
                // 判断身份
                if (res.result.identity === 3 || res.result.identity === 4) {
                  this.$toast.fail('当前身份无法使用发布功能')
                  return
                }
                window.sessionStorage.setItem('editTitleGlobal', this.$route.query.keywords)
                window.open('/topic-circle/write');
                this.$analysys.btn_click(`搜索词转提问`, '搜索结果页')
              }
            })
          }else{
            this.$toast('请先登录')
            this.$store.commit('editBackUrl', window.location.href)
            this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
          }
        })
      }else{
        this.$toast('请先登录')
        this.$store.commit('editBackUrl', window.location.href)
        this.$router.push({ name: 'signin', query: { fallbackUrl: window.location.href } })
      }
    }
  }

}
</script>

<style scoped lang="less">
@import "./styles";
.search_box {
  display: flex;
  width: 1128px;
  margin: 0 auto;
  height: 52px;
  margin-bottom: 20px;
  .search_input_big_wrapper {
    flex: 1;
  }
  .search_right_btn_wrapper > * {
      margin-left: 24px;
  }
}
</style>
