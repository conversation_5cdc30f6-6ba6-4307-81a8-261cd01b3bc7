
/deep/ .el-carousel__container {
  border-radius: 8px;
  height: 94px !important;
  overflow: hidden;
}

/deep/ .search_banner_wrapper {
  width: 100%;
  height: 100%;
}

.search_bg_wrapper {
  width: 1128px;
  margin: 0 auto;
  height: 94px;
  margin-bottom: 20px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.search_right_btn_wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
.toaiclass {
  width: 103px;
}
  .search_right_btn_ask {
    width: 103px;
    height: 52px;
    background: url("assets/images/search/search_ask.png");
    cursor: pointer;
  }

  .search_senior_btn {
    cursor: pointer;
    width: 137px;
    height: 52px;
    background: url("assets/images/search/search_senior_3.png");
  }
}

.search_right_wrapper {
  width: 100%;
  max-width: 100%;
  display: grid;
  grid-template-columns: minmax(264px, 1fr);
  gap: 24px 0;
}

.positioning_wrapper {
  position: absolute;
  top: -52px;
  left: 0;
  width: 100%;
  height: 52px;
  background: red;
  opacity: 0;
  z-index: -1;
}
