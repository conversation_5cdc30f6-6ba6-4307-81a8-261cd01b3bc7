.consulting-box {
  margin-top: 16px;
}

.container-box {
  transition: all 0.5s;

  .nav-tow {
    max-width: 100%;
    margin: 20px 0;
    transition: all 0.5s;
    padding: 10px 0 14px;
    border-radius: 6px;

    &:hover {
      box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.1);
    }

    .rightline {
      border-right: 1px solid #dededeff;
      border-radius: 0.5px;
    }

    .navtow-first {
      max-width: 570px;
      overflow: hidden;
      margin: 0 auto;
      color: #0581ceff;

      li {
        float: left;
        margin: 5px 12px;
        position: relative;

        :hover {
          border-radius: 2px;
          background: #0581ceff;
          color: white;
        }

        a {
          text-align: center;
          font-size: 16px;
          color: #0581ceff;
          padding: 5px 10px 5px 15px;
          box-sizing: border-box;
          cursor: pointer;
        }
      }
    }

    .nav-before {
      ::after {
        content: '';
        display: block;
        width: 4px;
        height: 4px;
        background: #b4dbef;
        border-radius: 50%;
        position: absolute;
        top: 10px;
        left: 5px;
      }
    }

    .navtow-second {
      max-width: 480px;

      a {
        color: #457999ff !important;
      }

      li {
        :hover {
          border-radius: 2px;
          background: #ddeaf4ff;
          color: #0581ceff !important;
        }
      }
    }

    .navtow-three {
      max-width: 146px;

      a {
        color: #40ac8aff !important;
      }

      li {
        :hover {
          border-radius: 2px;
          background: #ebf8f4ff;
          color: #40ac8aff;
        }
      }
    }
  }

  .left-bigbox {
    width: 100%;

    .banner_box {
      height: 100%;
      position: relative;

      .mask {
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;
        min-height: 44px;
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        padding: 6px 14px;
        display: flex;
        align-items: center;
        line-height: 1.5;
        box-sizing: border-box;
      }
    }

    /deep/ .el-carousel__container {
      border-radius: 6px 6px 6px 6px;
      height: 264px !important;
      overflow: hidden;
    }

    /deep/ .el-icon-arrow-right:before {
      font-family: 'iconfontNew';
      content: '\e624';
    }

    /deep/ .el-icon-arrow-left:before {
      font-family: 'iconfontNew';
      content: '\e626';
    }

    /deep/ .el-carousel__arrow {
      background: rgba(31, 45, 61, 0.3);
      font-size: 16px;
      opacity: 0.8;
      line-height: 36px;

      &:hover {
        background: #0581ce;
        color: white;
        opacity: 1;
      }
    }

    .title-content {
      margin: 60px 0 20px 0;
      line-height: 20px;
      overflow: hidden;

      .left-con {
        float: left;
        font-size: 20px;

        & span:nth-child(2) {
          font-weight: bold;
        }
      }

      .subspecial-box-left {
        max-width: 35%;
      }

      .subspecial-box {
        max-width: 65%;

        .subspecial-box-p {
          display: inline;

          .right_con_a:hover {
            font-weight: bold;
            color: #000000;
          }
        }
      }

      .right-con {
        float: right;
        font-size: 12px;
        position: relative;
        color: #0581ceff;

        a {
          color: #0581ceff;
        }

        .right_con_a {
          color: #545454ff;
          font-size: 14px;
          margin-right: 15px;
        }

        .line {
          position: absolute;
          left: -318px;
          top: 50%;
          width: 300px;
          height: 1px;
          background: #0581ceff;
        }
      }
    }

    .NewConsulting {
      .consulting {
        .consulting-item {
          overflow: hidden;
          margin-bottom: 20px;
          transition: all 0.3s;
          padding: 10px 0 10px 10px;
          border-radius: 6px;
          box-sizing: border-box;

          &:last-child {
            margin-bottom: 30px;
          }

          .item-left {
            border-radius: 6px;
            min-width: 224px;
            max-width: 224px;
            height: 126px;
            margin-right: 14px;
            display: flex;
            align-items: center;
            overflow: hidden;
          }

          .item-right {
            padding-right: 20px;

            .pone {
              font-size: 18px;
              line-height: 25px;
              margin-bottom: 8px;
            }

            .ptwo {
              display: block;
              font-size: 12px;
              line-height: 22px;
              color: #708aa2ff;
            }

            .pthree {
              font-size: 12px;
              color: #708aa2ff;
              margin-bottom: 15px;

              i {
              }
            }
          }
        }

        .consulting-item:hover {
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .col-row {
      display: flex;
      flex-wrap: wrap;
    }

    .common-image {
      margin-top: 10px;
      margin-bottom: -10px;
      flex: 0 0 auto;

      .common_advertisement {
        position: relative;
        height: 128px;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 10px;

        .position_top {
          position: absolute;
          right: 8px;
          top: 7px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 6px;
          color: white;
          padding: 2px 11px;
          font-size: 12px;
        }
      }
    }

    .first-curriculum {
      overflow: hidden;

      .team-box {
        float: left;
        overflow: hidden;

        li {
          max-width: 218px;
          padding: 8px 35px 8px 8px;
          box-sizing: border-box;
          float: left;
          border-radius: 6px;
          background: #fbfbfbff;
          margin-right: 14px;

          .team-li-left {
            float: left;
            height: 100%;

            img {
              max-width: 38px;
              max-height: 38px;
              border-radius: 50%;
              vertical-align: middle;
              margin-right: 8px;
            }
          }

          .team-li-right {
            float: left;

            .right-one {
              color: #000000ff;
              font-size: 16px;
              margin-bottom: 10px;
            }

            .right-two {
              color: #708aa2ff;
              font-size: 12px;
            }
          }
        }
      }

      .switch-box {
        float: right;
        height: 100%;
        line-height: 63px;
        cursor: pointer;
        font-size: 14px;

        span {
          color: #708aa2ff;
          margin-left: 6px;
        }
      }
    }
  }

  .right-bigbox {
    width: 100%;

    .shareStyle {
      padding: 18px 14px 0;
      box-sizing: border-box;
      border-radius: 6px;
      background: #fbfbfb;
      margin-bottom: 20px;
      overflow: hidden;

      .live_title {
        justify-content: space-between !important;

        .see_more {
        }
      }

      .title {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 16px;

        .shareStyleIcon {
          width: 20px;
          height: 20px;
        }

        span {
          font-size: 18px;
          margin-left: 10px;
          font-weight: bold;
        }
      }
    }

    .slide_box {
      padding-bottom: 6px;
    }

    .live_list_bovex_big_box {
      transition: all 0.8s;
    }

    .live_list_bovex {
      transition: all 0.8s;
      min-width: 100%;

      & .live-box:last-child {
        padding-bottom: 12px;
      }
    }

    .live-box {
      overflow: hidden;
      padding-bottom: 18px;

      &:hover .cpmperti {
        color: #0581ce;
      }

      &:last-child {
        margin-bottom: 10px;
      }

      .live-left {
        min-width: 142px;
        max-width: 142px;
        height: 80px;
        float: left;
        margin-right: 10px;
        display: flex;
        align-items: center;
        overflow: hidden;
      }

      .live-right {
        &:hover .live-title {
          color: #0581ce;
        }

        .live-title {
          flex-shrink: 0;
          font-size: 14px;
          font-weight: inherit;
          line-height: 24px;
        }

        .cpmperti {
          line-height: 24px;
          font-size: 14px;
        }

        .live-info {
          font-size: 12px;
          color: #708aa2ff;
          margin-top: 6px;
        }
      }
    }

    .slide_box {
      text-align: center;

      .icon {
        width: 20px;
        height: 20px;
      }

      .liveStyleIcon {
        margin-right: 15px;
      }
    }

    .image-share {
      height: 130px;
      margin-bottom: 20px;
      border-radius: 6px;
      overflow: hidden;
      width: 100%;

      img {
        display: block;
        max-width: 100%;
      }
    }

    .hotNumber {
      font-size: 14px;
      margin: 15px 0;

      span {
        display: inline-block;
        padding: 2px 5px;
        font-size: 10px;
        color: white;
        border-radius: 5px;
        background: #ee7e22ff;
        margin-right: 8px;
      }
    }

    .colr {
      span {
        background: #eeda22ff;
      }
    }

    .whitcolr {
      span {
        background: #c0cfddff;
      }
    }

    .first {
      position: relative;
      padding: 0;
    }

    .span-first {
      display: inline-block;
      padding: 2px 5px;
      font-size: 10px;
      color: white;
      border-radius: 6px 0px 0px 0px;
      background: #ee4a22ff;
      margin-right: 8px;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

.el-carousel__item h3 {
  color: #475669;
  font-size: 14px;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.pthreeIcon {
  width: 12px;
  height: 12px;
}
