.introduce_page_box {
  padding: 24px 0 32px;
  overflow: hidden;

  .introduce_content {
    //padding: 60px 0 10px;
    min-height: 40vh;
  }
}

.top_nav_select {
  z-index: 99999;
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 59px;
  background: #FFFFFF;
  border-bottom: 1px solid #f1f1f1;
  transition: all .3s;
  transform: translateY(-100%);
}

.introduce_option_box {
  padding: 0 85px;
  box-sizing: border-box;
  height: 60px;
  line-height: 60px;

  .introduce_option_list {
    color: #888888;
    user-select: none;
  }

  .introduce_option_list_isactive {
    font-weight: 600;
    color: #202020;
    position: relative;

    &::before {
      display: block;
      content: "";
      width: 100%;
      height: 4px;
      background: #0581CE;
      position: absolute;
      left: 0;
      bottom: 0;
    }
  }
  &::after{
    display: none!important;
  }
}
