<template>
  <div class='introduce_page_box'>
    <div class='container-box'>
      <div v-if='false' class='introduce_banner'>
        <img
          v-show="optionActive === 'aboutUs'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/aboutUs.jpg'
        />
        <img
          v-show="optionActive === 'contactUs'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/contactUs.png'
        />
        <img
          v-show="optionActive === 'joinUs'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/joinUs.png'
        />
        <img
          v-show="optionActive === 'versionStatement'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/versionStatement.png'
        />
        <img
          v-show="optionActive === 'privacyProtection'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/privacyProtection.png'
        />
        <img
          v-show="optionActive === 'responsibleClause'"
          alt=''
          class='img_cover'
          src='@/assets/images/introduce/responsibleClause.png'
        />
      </div>
      <div class='top_nav_select'>
        <ul class='introduce_option_box flex_between container-box'>
          <template v-for='item in navList'>
            <li
              v-if='item.isIntroduceEnable'
              :key='item.code'
              :class="$route.fullPath === item.url ? 'introduce_option_list_isactive' : ''"
              class='introduce_option_list cursor fontSize16'
              @click='jumpOptionActive(item.url)'
            >
              {{ $t(item.name) }}
            </li>
          </template>
        </ul>
      </div>
      <div class='introduce_content'>
        <transition mode='out-in' name='slide'>
          <component :is='optionActive'></component>
        </transition>
      </div>
    </div>
  </div>
</template>

<script>
import aboutUs from '/components/introduce/AboutUs' // 关于我们
import contactUs from '/components/introduce/ContactUs' // 联系我们
import joinUs from '/components/introduce/JoinUs' // 加入我们
import privacyProtection from '/components/introduce/PrivacyProtection' // 版权声明
import responsibleClause from '/components/introduce/ResponsibleClause' // 隐私保护
import versionStatement from '/components/introduce/VersionStatement' // 免责条款
export default {
  name: 'IntroducePage',
  components: {
    aboutUs,
    contactUs,
    joinUs,
    privacyProtection,
    responsibleClause,
    versionStatement
  },
  asyncData({app, params, error, store, query, req}) {
    let active = 'aboutUs' // 选项高亮 默认关于我们
    let title = null
    switch (query.optionActive) {
      case 'aboutUs':
        title = '关于我们'
        active = 'aboutUs'
        break
      case 'contactUs':
        title = '联系我们'
        active = 'contactUs'
        break

      case 'joinUs':
        title = '加入我们 - 脑医汇 - 神外资讯、神介资讯 - 领先的临床神经科学互联网平台'
        active = 'joinUs'
        break

      case 'versionStatement':
        title = '版权声明'
        active = 'versionStatement'
        break

      case 'privacyProtection':
        title = '隐私保护'
        active = 'privacyProtection'
        break

      case 'responsibleClause':
        title = '免责条款'
        active = 'responsibleClause'
        break

      default:
        title = '关于我们'
        active = 'aboutUs'
        break
    }
    return {
      optionActive: active,
      title
    }
  },
  head() {
    return {
      title: this.title,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '招聘职位， 加入我们'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '热招，招募，职位，优秀, 热爱，智联招聘，Boss直聘，拉钩招聘'
        }
      ]
    }
  },
  computed: {
    navList() {
      return this.$store.state.global.footerNav
    }
  },
  watch: {
    '$store.state.global.scroll'(scroll) {
      const topNavSelect = document.querySelector('.top_nav_select')
      const headerBox = document.querySelector('#nav_header')
      if (scroll > 0) {
        if (topNavSelect) {
          topNavSelect.style.cssText = 'transform:translateY(0%)'
        }


        if (headerBox) {
          headerBox.style.cssText = 'transform:translateY(-100%)'
        }


      } else {

        if (topNavSelect) {
          topNavSelect.style.cssText = 'transform:translateY(-100%)'
        }

        if (headerBox) {
          headerBox.style.cssText = 'transform:none'
        }
      }
    },
    // 监听到路由地址的改变
    $route: {
      immediate: true,
      handler() {
        if (this.$route.query.optionActive) {
          switch (this.$route.query.optionActive) {
            case 'aboutUs':
              this.title = this.$t('aboutUs.AboutUs')
              this.optionActive = "aboutUs"
              break
            case 'contactUs':
              this.optionActive = "contactUs"
              this.title = this.$t('aboutUs.contactUs')
              break

            case 'joinUs':
              this.optionActive = "joinUs"
              this.title = '加入我们 - 脑医汇 - 神外资讯、神介资讯 - 领先的临床神经科学互联网平台'
              break

            case 'versionStatement':
              this.optionActive = "versionStatement"
              this.title = this.$t('aboutUs.versionStatement')
              break

            case 'privacyProtection':
              this.optionActive = "privacyProtection"
              this.title = this.$t('aboutUs.privacyProtection')
              break

            case 'responsibleClause':
              this.optionActive = "responsibleClause"
              this.title = this.$t('aboutUs.responsibleClause')
              break

            default:
              this.optionActive = "aboutUs"
              this.title = this.$t('aboutUs.AboutUs')
              break
          }
        }
      }
    }
  },
  methods: {
    // 跳转底部链接
    jumpOptionActive(url) {
      this.$router.push({path: url})
    }
  }
}
</script>
<style>
.slide-enter-active,
.slide-leave-active {
  transition: all .2s ease;
  position: relative;
}

.slide-enter {
  opacity: 0;
}

.slide-leave-to {
  opacity: 0;
}
</style>
<style lang='less' scoped>
@import "./index";
</style>
