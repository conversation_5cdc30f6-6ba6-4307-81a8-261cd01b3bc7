<template>
  <div style='overflow: hidden'>
    <div class='history_meeting container-box'>
      <!-- 会议筛选 -->
      <div class='meeting_search_box'>
        <!--排序方式-->
        <div
          v-for='(content) in sortOrder'
          :key='content.name'
          class='search_flex'

        >
          <p class='search_label'>{{ content.name }}</p>
          <p
            v-for='(order, orderIndex) in content.content'
            :key='orderIndex'
            :class='{themeFontColor:selectedSorting === order.id}'

            class='search_content'
            @click='sortOrderFun(order.id,order.name)'
          >
            {{ order.name }}
          </p>
        </div>
      </div>
      <!-- End -->

      <!-- 会议列表 -->
      <ul class='meetingContentBox'>
          <div class="meetingContentBoxContainer" v-for='item in meetingDataList' :key='item.id' :lg="{span:'4-8'}" :md='8' :sm='8' :xs='12'>
            <nuxt-link :to='{ path: `/meeting/detail` ,query:{id:item.id} }' target='_blank'>
              <li class='meetingContentList flex_column'>
                <div class='meetingConImage'>
                  <img v-if='item.appMainPic || item.playerCover || item.titlePic'
                       :src='item.appMainPic?$tool.compressImg(item.appMainPic,285,160):item.playerCover?$tool.compressImg(item.playerCover,285,160):item.titlePic?$tool.compressImg(item.titlePic,285,160):null'
                       class='img_cover' />
                  <img v-else class='img_cover' src='~assets/images/default16.png' />
                  <live-state :state='item.meetingLiveStatus'></live-state>
                </div>
                <p class='meetingTitle text-limit-2'>
                  {{ item.meetingName }}
                </p>
                <p class='meetingInfomation text-limit-1'>
                  <a class='flex_between'>
                    <span class='time fontSize12 flex_shrink'>{{ item.meetingDateStr }}</span>
                    <span class='userName fontSize12 text-limit-1'>{{ item.province }}-{{ item.city }}</span>
                  </a>
                </p>
              </li>
            </nuxt-link>
          </div>
      </ul>
      <Empty :loading='emptyFlag' :no-more='!emptyFlag && meetingDataList ? meetingDataList.length===0 : null' />
      <!-- End -->
      <el-pagination
        v-if='!emptyFlag'
        :current-page.sync='currentPage'
        :hide-on-single-page='$store.state.hideOnSinglePage'
        :layout='$store.state.layout'
        :page-size='20'
        :pager-count='$store.state.pager_count'
        :total='meetingTotal'
        background
        small
        style='text-align: center; margin-bottom: 10px'
        @current-change='handleCurrentChange'
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getMtgTagMeetingPage} from "../../../../api/meeting";
import LiveState from '@/components/LiveState/LiveState'
import Empty from '@/components/UI/Empty/Empty'
export default {
  name: "InternationalPage",
  components:{
    Empty,
    LiveState
  },
  async asyncData({ app, params, error, store, query, req }){
    const [meetingData] = await Promise.all([
      app.$axios.$request(getMtgTagMeetingPage({
        tagId:1,
        pageNo:1,
        pageSize: 20,
        orderType:1
      }))
    ])

    return {
      meetingDataList:meetingData.list,
      meetingTotal : meetingData.page.totalCount
    }
  },
  head() {
    return {
      title: '国际会议'
    }
  },
  data(){
    return {
      sortOrder: [
        { name: 'Ranked by: ', content: [{ id: 1, name: 'New' }, { id: 2, name: 'Hot' }] }
      ],
      selectedSorting:1,
      emptyFlag: false,
      currentPage:1,
    }
  },
  methods:{
    sortOrderFun(id,name){
      this.$analysys.btn_click(String(name), document.title)
      this.selectedSorting = id;
      this.handleCurrentChange(1,true)
    },
    handleCurrentChange(item,isTop){
      this.currentPage = item;
      this.emptyFlag = true;
      this.meetingDataList = [];
      this.meetingTotal = 0;
      isTop && this.$tool.scrollIntoTop()
      this.$axios.$request(getMtgTagMeetingPage({
        tagId:1,
        pageNo:item,
        pageSize: 20,
        orderType:this.selectedSorting
      })).then(res=>{
        if(res.code === 1){
          this.emptyFlag = false;
          this.meetingDataList = res.list
          this.meetingTotal =  res.page.totalCount
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import "./styles";
</style>
