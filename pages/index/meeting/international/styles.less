/deep/ .el-breadcrumb__item {
  i {
    margin: 0 !important;
    color: #0581ceff;
  }

  .el-breadcrumb__inner {
    color: #0581ceff;
    font-size: 14px;
    font-weight: normal;
  }
}

.history_meeting {
  margin-top: 20px;

  .meeting_search_box {
    padding: 10px 0;

    .search_flex {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 8px;
      user-select: none;
      width: 100%;
      flex-shrink: 0;

      .search_label {
        //width: 62px;
        font-size: 12px;
        color: #666666;
        line-height: 24px;
        margin-right: 10px;
      }

      .search_content_box {
        width: calc(100% - 62px);
        flex-shrink: 0;
        position: relative;
        transition: all .6s;

        .more_list {
          width: 40px;
          text-align: right;
          position: absolute;
          right: 0;
          background: white;
          line-height: 24px;
          color: #5C8FBD;
          font-size: 14px;
        }
      }

      .search_content {
        display: inline-block;
        padding: 0 13px;
        font-size: 12px;
        line-height: 24px;
        cursor: pointer;
        margin-right: 5px;
        margin-bottom: 10px;
      }

      .is_active {
        border-radius: 6px;
        background: #0581ce;
        color: #fff;
      }
    }

    .line {
      width: 200%;
      margin-left: -50%;
      border: 1px solid #8399b03f;
      margin-bottom: 16px;
    }
  }
}

// 会议列表开始
.meetingContentBox {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 0 20px;

  .meetingContentList {
    border-radius: 6px;
    overflow: hidden;
    background: #fbfbfb;
    margin-bottom: 20px;
    height: calc(100% - 20px);
    transition: all .3s;

    &:hover {
      box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
    }

    .meetingConImage {
      height: 160px;
      overflow: hidden;
      position: relative;

      img {
        width: 100%;
        min-height: 100%;
      }

      .meetingLable {
        border-radius: 6px;
        background: #5fcf7c;
        padding: 1px 8px;
        color: #fff;
        font-size: 10px;
        position: absolute;
        right: 5px;
        top: 5px;
      }
    }

    .meetingTitle {
      font-size: 16px;
      line-height: 24px;
      margin: 10px 10px 14px;
      font-weight: 500;
    }

    .meetingInfomation {
      margin-top: auto;
      line-height: 12px;

      span {
        color: #708aa2;
        margin: 0 10px;
      }

      margin-bottom: 14px;
    }
  }
}

.el-col-lg-4-8 {
  @media screen and (min-width: 1200px) {
    width: 20%;
  }
}
