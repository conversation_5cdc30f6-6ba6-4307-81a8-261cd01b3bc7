<!-- 会议详情页 -->
<template>
  <div class='meeting_details_box container-box'>
    <!--Navigation Start-->
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/meeting/home' }">会议</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/meeting/history' }">{{ meetingDetails.meetingInfo.meetingName }}
      </el-breadcrumb-item>
    </bm-breadcrumb>
    <!--End-->
    <div v-if='false' class='kalami'>
      <span>状态{{ meetingFieldsStatus }}</span>
      <span>显示{{ meetingStatus }}</span>
      <span>日程{{ defaultAgendaId }}</span>
      <p>直播状态 0=直播未开始、1=暂停中、2=直播结束、3=直播中</p>
      <p>{{ defaultAgendaIdActive }}</p>
      <p>isLive状态{{ isLive }}</p>
      <p>{{ meetingTemplate }}</p>
      <button @click='meetingFieldsStatus = 0'>0 [直播未开始]</button>
      <button @click='meetingFieldsStatus = 1'>1 [暂停]</button>
      <button @click='meetingFieldsStatus = 2'>2 [直播结束]</button>
      <button @click='meetingFieldsStatus = 3'>3 [直播中]</button>
    </div>
    <!-- 会议标题 -->
    <div class='conference_title fontSize18'>
      {{ meetingDetails.meetingInfo.meetingName }} |
      {{ meetingDetails.defaultFields.subject }}
    </div>
    <!-- End -->
    <!-- TODO 会议判断  首先 如果会场状态不是LI(直播状态)并且有录播vid的话 那么就显示录播 -->
    <!-- TODO 会议判断  其次 如果会场状态是LI(直播状态)并且isLive是isLive(直播)并且当前状态默认是直播开始0 如果直播不是暂停或者不是结束  那么就显示直播  这边判断isLive 是因为 在点击日程的时候会出现  会场是直播状态 但是 日程是录播日程 所以这边需要多加一个条件限制 -->
    <!-- TODO 会议判断  其次 如果会场状态是LI(直播状态)并且isLive没有的话 那么就代表会场是直播状态 但是日程是录播 显示录播  这边判断isLive 是因为 在点击日程的时候会出现  会场是直播状态 但是 日程是录播日程 所以这边需要多加一个条件限制 -->
    <!-- TODO 会议判断  还有一种情况是会场不是直播状态 并且会场没有录播vid  那么就只显示一张图 -->
    <!-- TODO 会议判断  还有一种情况是会场不是直播状态 并且会场没有录播vid 这里多加了一个isLive==='issoonLive'即将直播 就是说如果是即将直播也会显示默认图, 这里面对倒计时又做了判断 如果会场状态是即将直播并且开始时间大于当前时间 或者是isLive状态是即将直播状态 都会显示倒计时 -->
    <!-- TODO 会议判断  还有一种情况是 当前直播 开启了暂停或者结束 也会显示kv图. 这里如果是1暂停的情况 还需要根据当日直播去找大于当前时间的第一个直播的开始时间 然后给倒计时 -->
    <!-- TODO 会议判断  这里给到了一个直播状态,目前我所了解的业务是只有该会场是直播的情况下manualStatus状态才会有变化 也就是说 不会出现录播和直播共存的情况.  -->
    <!-- 会议内容 -->
    <div class='conference_content_box'>
      <el-row>
        <!-- 会议左侧内容 -->
        <el-col :lg='16' :md='16' :xs='24' class='page-left'>
          <!-- 会议video -->
          <div class='conference_video_box'>
            <!-- 问卷调查 Start -->
            <svg-icon
              v-if="(meetingDetails.meetingInfo.questionnaireSwitch === 'T' && $store.state.auth.token) || questionnaireStatus === 'T'"
              class-name='questionnaireicon cursor'
              icon-class='questionnaire'
              @click="editactiveNameFun('9','idPage')"
            ></svg-icon>
            <!-- 问卷调查 End -->
            <div class='occupying'>
              <!-- 播放限制mask Start -->
              <div v-if="playerMaskFlag!=='T'" class='player_limit_mask'>
                <div v-if="playerMaskFlag === 'L'" class='login_user_box'>
                  <p class='title'>{{ $t('meeting_remind_info') }}</p>
                  <div class='login_button cursor' @click='jumpSigninFun'>{{ $t('meeting_login_tv') }}</div>
                </div>
                <div v-if="playerMaskFlag=== 'V'" class='login_user_box'>
                  <p class='title'>本视频需要实名认证才能观看</p>
                  <div class='login_button cursor' @click='jumpReleaseCaseFun'>去认证</div>
                </div>
              </div>
              <!-- 播放限制mask End -->
              <!-- 会议窗口显示内容 Start -->
              <div
                :style="smallScreen?'':'width:100%;height:100%'"
                class='video_aliplayer'>
                <!--TODO 播放器封面 Start -->
                <ul v-show='smallScreen' class='zoom_btn_box'>
                  <li class='zoom_btn_top_left'></li>
                  <li class='zoom_btn_bottom_left'></li>
                  <li class='zoom_btn_top_right'></li>
                  <li class='zoom_btn_bottom_right'></li>
                </ul>
                <div
                  v-if="meetingStatus === 'COVER'"
                  class='video_Notice'>
                  <img v-if='meetingDetails.meetingInfo.playerCover'
                       :src='$tool.compressImg(meetingDetails.meetingInfo.playerCover,793,446)'
                       class='img_cover'>
                  <img v-else class='img_cover' src='~assets/images/live_z.png' />
                  <!-- 遮罩倒计时 Start -->
                  <div
                    v-if="(meetingFieldsStatus === 0 || meetingFieldsStatus === 1) && countDownFlag  && playerMaskFlag === 'T' || isLive === 'issoonLive'"
                    class='Mask flex_center flex_align_center'>
                    <div>
                      <p class='mask_first_title'>{{ $t('meeting_startTime') }}</p>
                      <p class='mask_second_time'>
                        <span class='time'>{{ mydays }}</span>
                        <span class='zhan'>{{ $t('meeting_day') }}</span>
                        <span class='time'>{{ myHours }}</span>
                        <span class='zhan'>{{ $t('meeting_hour') }}</span>
                        <span class='time'>{{ myMinutes }}</span>
                        <span class='zhan'>{{ $t('meeting_minute') }}</span>
                        <span class='time'>{{ mySeconds }}</span>
                        <span class='zhan'>{{ $t('meeting_second') }}</span>
                      </p>
                      <div
                        v-if='false'
                        id='themeButton'
                        class='mask_three_button themeFontColor fontSize16'
                        @click='addCalendartipsFun'
                      >
                        <i class='iconfont icon-tixing-tianchong'></i>
                        <span>添加到日历提醒</span>
                      </div>
                    </div>
                  </div>
                  <!-- 遮罩倒计时 End -->
                </div>
                <!--TODO 播放器封面 End -->

                <!--TODO 点播录播 Start-->
                <ali-player
                  v-if="meetingStatus === 'RECOR_DING'"
                  :autoPlayDelay='0'
                  :autoplay='true'
                  :is-live='false'
                  :playauth='defaultAgendaPlayAuth'
                  :use-h5-prism='true'
                  :vid='defaultAgendaVid'
                  control-bar-visibility='hover'
                  height='100%'
                  show-bar-time='3000'
                  width='100%'
                  @play='videoPlayerFun'
                >
                </ali-player>
                <!--TODO 点播录播 End-->

                <!--TODO 直播 Start-->
                <ali-player
                  v-if="meetingStatus === 'LIVE'"
                  :autoPlayDelay='0'
                  :autoplay='true'
                  :is-live='true'
                  :source='defaultFieldHisLiveUrl.split("http:").join("")'
                  :use-h5-prism='true'
                  control-bar-visibility='hover'
                  height='100%'
                  show-bar-time='3000'
                  width='100%'
                >
                </ali-player>
                <!--TODO 直播 End-->
              </div>
              <!-- 会议窗口显示内容 End -->
            </div>
          </div>
          <!-- End -->
          <div class='bottom_btn_box'>
            <!-- 会议底部按钮集/弹幕 -->
            <barrage-list-box :agendaId='defaultAgendaId' :borderShow='meetingAdvertisementDataList.length===0'
                              :fieldsId='defaultFieldsId'
                              :firstFieldsId='defaultFirstFieldsId' :likes-number='likesNumber'
                              :meeting-barragr='meetingDetails.defaultFields.barrageSwitch'
                              :meeting-details='meetingDetails.meetingInfo' :meeting-details-info='meetingDetails'
                              :meeting-pv='meetingPv'
                              :meetingIsLive='meetingStatus'
                              @editManualStatus='editManualStatus'
                              @editquestionnaireStatus='editquestionnaireStatus'
                              @getLikesFun='getLikesFun' @refreshComments='refreshCommentsFun'></barrage-list-box>
            <!-- End -->

            <!-- 会议广告 -->
            <div v-if='meetingAdvertisementDataList.length>0' class='advertisement'>
              <carousel :arrow="meetingAdvertisementDataList.length>1?'hover':'never'" :autoplay='true'
                        :indicator-position="meetingAdvertisementDataList.length>1?'':'none'"
                        height='100px'>
                <carousel-item v-for='item in meetingAdvertisementDataList' :key='item.id'>
                  <img :class='{cursor:item.extras}' :src='$tool.compressImg(item.image,793,100)' alt=''
                       class='img_cover advertisement_img'
                       @click='jumpCarouselFun(item.extras,item.id,item.name)'>
                </carousel-item>
              </carousel>
            </div>
          </div>
          <!-- 会场列表 讲课模板 答疑模板 系列会议模板 不显示切换会场 -->
          <recording-list
            v-if="recordingShowHide === 'T'"
            :broadcastin-current='defaultFieldsId'
            :default-images='meetingDetails.meetingInfo.playerCover'
            :fields-list='meetingFieldsList'
            :meeting-info='meetingDetails'
            :meeting-info-data='meetingDetails.meetingInfo'
            @switchVenueFun='switchVenueFun'></recording-list>
          <!-- End -->
          <!-- 会议主题开始 -->
          <conference-theme
            :active-name.sync='activeNameCurrent'
            :fieldsId='defaultFieldsId'
            :meeting-detail='meetingDetail'
            :meeting-details-info='meetingDetails'
            :meetingStatus='meetingStatus' :questionnaireStatus='questionnaireStatus'
            :refreshComments='refreshComments' @editactiveNameFun='editactiveNameFun'>
          </conference-theme>
          <!-- 结束 -->
        </el-col>

        <!-- End -->
        <!-- 会议右侧内容 -->
        <el-col :lg='8' :md='8' :xs='24' class='page-right'>
          <!-- 当前会议的今日其他会场直播 -->
          <live-broadcast
            v-if="todayOtherMeetingDataList && todayOtherMeetingDataList.length>0 && meetingTemplate !== 'twochannel'"
            :defaultFieldsId='defaultFieldsId'
            :today-other-meeting-data-list='todayOtherMeetingDataList'
            @switchVenueFun='switchVenueFun'></live-broadcast>
          <!-- End -->
          <!-- 会议日程  Start-->
          <meeting-schedule
            v-if="dynamicMeetingTemplate !== ''"
            :agenda-date-data-current='defaultAgendaDateActive'
            :agenda-list='agendaListMerge'
            :meetingTemplate='dynamicMeetingTemplate'
            :recorded-broadcast-play='defaultAgendaIdActive'
            @switchVenueSAgendaFun='switchVenueSAgendaFun'>
          </meeting-schedule>
          <!-- 会议日程 End-->
          <!-- 线下会议日程 Start -->
          <OfflineSchedule v-if="dynamicMeetingTemplate === ''" :agenda-date-data-current='defaultAgendaDateActive'
                           :agenda-date-data-list='meetingDetails.agendaDays'
                           :agenda-list='agendaListMerge'
                           :meetingTemplate='meetingTemplate'
                           :recorded-broadcast-play='defaultAgendaIdActive'
                           @editAgendaDateFun='editAgendaDateFun'
                           @switchVenueSAgendaFun='switchVenueSAgendaFun'
          ></OfflineSchedule>
          <!-- 线下会议日程 End -->
          <!-- End -->
        </el-col>
        <!-- End -->
      </el-row>
    </div>
    <!-- End -->
    <!--会议登记-->
    <meeting-register v-if='isDialogFlag' :is-dialog-flag='isDialogFlag'
                      :is-statement-dialog-flag='isStatementDialogFlag'
                      :meeting-register='meetingRegister'
                      @editDialogFlag='editDialogFlag'></meeting-register>
    <!--身份认证-->
    <identity-authentication :authenticationDataInfo='authenticationDataInfo'
                             :authenticationDialog.sync='authenticationDialog'
                             :identity-current-father='$store.state.auth.user.identity'
                             :identityFlag='!!$store.state.auth.user.identity'
                             @editFlag='authenticationDialogFun'></identity-authentication>
    <ConferenceConsultation v-if='meetingDetails.meetingInfo.conferenceConsultationSwitch === "T"' />
  </div>
</template>

<script>
import { Carousel, CarouselItem } from 'element-ui'
import NuxtAliplayer from '@/components/AliPlayer/AliPlayer'
import {
  BarrageListBox,
  ConferenceTheme,
  LiveBroadcast,
  MeetingSchedule,
  OfflineSchedule,
  RecordingList
} from '@/components/Meeting/index.js' //  会议详情组件
import {
  getAgendaListByQDate,
  getAllLiveMeetingDetail,
  getBarrageList,
  getMeetingAdList,
  getMeetingDetail,
  getMeetingPv,
  getMeetingRequireCheckinInfo,
  getTodayOtherMeetings
} from '@/api/meeting'
import LikeHeat from '@/components/Meeting/LikeHeat'
import MeetingRegister from '@/components/Meeting/MeetingRegister'
import IdentityAuthentication from '@/components/IdentityAuthentication/IdentityAuthentication' // 身份认证
import { userInfo } from '@/api/user'
import { saveBrowsingHistory } from '@/api/browsing-history'
import ConferenceConsultation from '@/components/meeting-test/layout/ConferenceConsultation/ConferenceConsultation'

export default {
  head() {
    return {
      title: this.meetingDetails.meetingInfo.meetingName,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.meetingDetails.meetingInfo.description
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.meetingDetails.meetingInfo.meetingName
        }]
    }
  },
  components: {
    ConferenceConsultation,
    BarrageListBox,
    RecordingList,
    LiveBroadcast,
    MeetingSchedule,
    ConferenceTheme,
    LikeHeat,
    Carousel,
    CarouselItem,
    MeetingRegister,
    IdentityAuthentication,
    OfflineSchedule,
    'ali-player': NuxtAliplayer
  },
  // 动态参数校验
  async asyncData({ app, params, error, store, query, req }) {
    /**
     * request1 直播页面详情
     * todayOtherMeetingDataList 获取今日直播会议
     * request3 会议会议pv
     * request4 会议详情(介绍)
     * request5 获取会议广告
     * request6 获取会议登记信息
     */
    const [request1, request3, request4, request6] = await Promise.all([
      app.$axios.$request(getAllLiveMeetingDetail({
        userId: store.state.auth.user.id,
        meetingId: query.id,
        agendaId: query.agendaId ? query.agendaId : null,
        fieldsId: query.fieldsId ? query.fieldsId : null
      })),
      app.$axios.$request(getMeetingPv({
        meetingId: query.id
      })),
      app.$axios.$request(getMeetingDetail({
        meetingId: query.id
      })),
      app.$axios.$request(getMeetingRequireCheckinInfo({
        meetingId: query.id,
        userId: store.state.auth.user.id
      }))
    ])
    const request5 = await app.$axios.$request(getMeetingAdList({
      meetingId: query.id,
      fieldsId: request1.result.defaultFields.id
    }))
    app.head.title = request1.result.meetingInfo.meetingName

    /**
     * 会议登记
     * 默认登记弹框为false
     * 如果没有登记信息 打开弹框
     * 如果不需要登记500 关闭弹框
     * 如果有登记信息 弹框
     * @type {boolean}
     */
    let isDialogFlag = false
    if (request6.code === 1 && !request6.result.info) {
      isDialogFlag = true
    } else if (request6.code === 500) {
      isDialogFlag = false
    } else if (request6.code === 1 && request6.result.info) {
      isDialogFlag = false
    }

    /**
     * 参会声明
     * 如果有参会声明 先弹参会声明 确定后弹登记信息
     * */
    let isStatementDialogFlag = ''
    if (request1.result.meetingInfo.partnerCode) {
      isStatementDialogFlag = request1.result.meetingInfo.partnerCode
    }
    /**
     * 获取当天正在直播的其他会场 需要根据默认会场的id来获取
     * @type {any}
     */
    const todayOtherMeetingDataList = await app.$axios.$request(getTodayOtherMeetings({
      meetingId: query.id
    }))
    return {
      meetingDetails: request1.result,
      todayOtherMeetingDataList: todayOtherMeetingDataList.list,
      meetingPv: request3.result.pv,
      meetingDetail: request4.result,
      meetingAdvertisementDataList: request5.list,
      meetingRegister: request6.result,
      authenticationDialog: false,// 病例身份认证弹框
      isDialogFlag,
      isStatementDialogFlag,
      defaultFirstFieldsId: request1.result.fieldsList.length > 0 ? request1.result.fieldsList[0].id : null // 第一个会场id
      , defaultAgendaFirstId: request1.result.agendaList.length > 0 ? request1.result.agendaList[0].id : null // 第一个日程ID
      , offlineAgendaList: request1.result.agendaList // 线下模板的日程
      , defaultAgendaId: request1.result.defaultAgenda.id  // 默认日程ID
      , defaultFieldsId: request1.result.defaultFields.id // 默认会场ID
      , meetingFieldsList: request1.result.fieldsList // 所有会场
      , meetingTemplate: request1.result.meetingInfo.style // 会场模板
      , defaultAgendaVid: request1.result.defaultAgenda.vid // 默认日程的VID
      , defaultAgendaPlayAuth: request1.result.defaultAgenda.playAuth // 默认日程的PLayAuth
      , defaultFieldHisLiveUrl: request1.result.defaultFields.hlsLiveUrl.split('https:').join('') // 默认会场的直播地址
      , meetingFieldsStatus: request1.result.defaultFields.manualStatus // TODO 直播状态 0=直播未开始、1=暂停中、2=直播结束、3=直播中
      , defaultFieldsCondition: request1.result.defaultFields.condition  // 默认会场的播放条件   N 无条件播放 L登录播放 V实名认证播放
      , defaultAgendaCondition: request1.result.defaultAgenda.condition  // 默认日程的播放条件   N 无条件播放 L登录播放 V实名认证播放
      , videoname: request1.result.defaultAgenda.theme
    }
  },
  data() {
    return {
      refreshComments: false,    // 刷新评论  用于发送直播弹幕时刷新评论
      authenticationDataInfo: {},// 用户身份信息
      analysysMeetingTime: null, // 会议埋点计时器
      likesNumber: 0,           // 会场点赞数
      danmukuList: [],          // 弹幕列表
      countDownFlag: false,     // 倒计时 是否显示  [如果倒计时是负数 就不显示倒计时]
      mydays: 0,          // 倒计时天数
      myHours: 0,         // 倒计时小时
      myMinutes: 0,       // 倒计时分钟
      mySeconds: 0,       // 倒计时秒钟
      mymonths: 0,
      timefun: null,      // 倒计时容器
      isLive: 'F'    // 点击日程  直播是isLive 录播是undefined 即将直播是issoonLive
      , activeName: '1'               // 切换tab默认选中
      , questionnaireStatus: null     // websocket 控制问卷调查开关
      , manualFlag: true             // 控制倒计时 如果当日有未开始的直播 就显示倒计时 否则不显示
      , isLiveFlag: true             // 控制直播显示隐藏
      , scroll: 0                 // 滚动条距离顶部位置
      , smallScreen: false         // 会议播放器 小窗播放开启
      , _beforeUnload_time: 0
      , _gap_time: 0
    }
  },
  computed: {
    /**
     * 计算哪些是显示切换会场 哪些不显示
     */
    recordingShowHide() {
      if ((this.meetingTemplate === 'teach' && this.$route.query.id !== '1973') || this.meetingTemplate === 'answer' || this.dynamicMeetingTemplate === 'series') {
        return 'F'
      } else {
        return 'T'
      }
    },
    /**
     * 直播中 选中评论tab
     * 会议状态结束 或者 会议暂停 判断当天最后一个日程结束时间 + 两小时 这个时间内选中评论tab
     */
    activeNameCurrent() {
      if (this.meetingStatus === 'LIVE' && this.activeName !== '9') {
        return '2'
      } else if (this.activeName === '9') {
        return '9'
      } else {
        let tabFlag = null // 开关  当天是否有在当前时间 小于 结束时间后两个小时的日程
        const nowTime = this.timeStamp.timestamp_13(Date.now(), 'yyyy-mm-d')                   // 当天时间
        this.meetingDetails.agendaList.forEach(item => {
          let agendaTime = this.timeStamp.timestamp_13(Date.parse(item.endTime), 'yyyy-mm-d')  // 所有日程的当天时间
          /**
           * 如果日程是当日  并且
           * 现在时间小于 当日日程的结束时间+两个小时  比如 结束日程是 3点+两小时是5点   现在的时间如果小于5点 就切换到评论列表
           */
          if (agendaTime === nowTime) {
            if (Date.now() < (Date.parse(item.endTime) + 7200000)) {
              tabFlag = true
            }
          }
        })
        if (tabFlag && (this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1)) {
          return '2'
        } else {
          this.activeName
        }
      }
    },
    /**
     * 根据定制会议id 展示固定的会议模板
     */
    dynamicMeetingTemplate() {
      if (this.$route.query.id === '1198') {
        return 'series'
      } else {
        return this.meetingTemplate
      }
    },
    /**
     *  lihaohan 2022/4/19
     *  TODO meetingFieldsStatus tips : 直播状态 0=直播未开始、1=暂停中、2=直播结束、3=直播中
     *  TODO 播放器封面 / 倒计时 / 直播播放器 / 录播播放器 判断
     *  ★ COVER       播放器封面
     *  ★ COUNT_DOWN  倒计时
     *  ★ LIVE        直播播放器
     *  ★ RECOR_DING  录播播放器
     *
     */
    meetingStatus() {
      /**
       *  1. 播放器封面的显示条件为  会场状态为 0直播未开始 或者 1暂停中 或者 2直播结束 并且要满足默认日程没有vid [因为有vid 需要显示录播] 并且播放限制要放开 T       ---- || this.isLive === 'issoonLive' 暂时不写
       *  2. 播放器倒计时显示条件为  会场状态为0直播未开始 或者1暂停中 并且倒计时要为正数 也就是要大于现在的时间  并且 通过播放限制               -------------isLive === 'issoonLive' || (manualFlag) 暂时不屑
       *  3. 录播显示条件 会场状态为直播结束 并且默认日程有vid 并且 通过播放限制
       *  4. 直播显示条件 会场状态为直播中 并且通过播放条件
       *  5. 默认是展示播放器封面
       */
      if ((this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1 || this.meetingFieldsStatus === 2) && this.defaultAgendaVid === null && this.playerMaskFlag === 'T') {
        return 'COVER'
      } else if ((this.meetingFieldsStatus === 2 && this.defaultAgendaVid && this.playerMaskFlag === 'T') || !this.isLive) {
        return 'RECOR_DING'
      } else if ((this.defaultAgendaVid && this.playerMaskFlag === 'T') && !this.isLive) {
        return 'RECOR_DING'
      } else if ((this.meetingFieldsStatus === 3 && this.playerMaskFlag === 'T') && this.isLive !== 'issoonLive' || (this.isLive === 'isLive' && this.meetingFieldsStatus === 3)) {
        return 'LIVE'
      } else {
        return 'COVER'
      }
    },
    /**
     * 默认日程选中时间
     * 如果会场状态不是直播结束 是直播的状态的话  那就选中第一个章节的时间
     * 如果是录播的话 那就取默认日程的开始时间  或者是日程列表第一个的开始时间
     */
    defaultAgendaDateActive() {
      if (this.meetingFieldsStatus !== 2 && this.meetingTemplate !== '') {
        /**
         * 如果不是直播已结束 录播的话 那么选章节的第一个时间
         * @type {*|null}
         */
        let date = this.meetingDetails.sectionList ? this.meetingDetails.sectionList.length > 0 ? this.meetingDetails.sectionList[0].startTime : null : null
        if (date) {
          date = date.split(' ')[0]
        }
        if (this.meetingDetails.sectionList) {
          return date
        } else {
          return this.meetingDetails.defaultAgenda.startTime ? this.meetingDetails.defaultAgenda.startTime.split(' ')[0] : this.meetingDetails.agendaList.length > 0 ? this.meetingDetails.agendaList[0].startTime.split(' ')[0] : null
        }
      } else if (this.meetingTemplate === '') {
        return this.meetingDetails.defaultDate
      } else {
        /**
         * 如果是录播的话 选默认日程的开始时间
         */
        return this.meetingDetails.defaultAgenda.startTime ? this.meetingDetails.defaultAgenda.startTime.split(' ')[0] : this.meetingDetails.agendaList[0].startTime.split(' ')[0]
      }
    },
    /**
     * 默认日程选中状态
     */
    defaultAgendaIdActive() {
      if (this.meetingFieldsStatus !== 2 && this.meetingTemplate !== '') {
        /**
         * 如果不是直播已结束 录播的话  那么就选今天的日程
         * @type {string}
         */
        const agendaDateFirst = []
        const liveID = []
        /**
         * 如果是系列模板的话 就显示默认日程id
         */
        if (this.dynamicMeetingTemplate === 'series') {
          // return this.defaultAgendaId ? this.defaultAgendaId : this.defaultAgendaFirstId
          // eslint-disable-next-line no-unused-expressions
          this.agendaListMerge.length > 0 ? this.agendaListMerge.forEach(itemSub => {
            itemSub.itemList.forEach(itemAgenda => {
              /**
               * 如果 当前时间大于日程开始时间 并且 小于日程结束时间  并且会场状态是直播状态 那就选中该日程
               */
              if (Date.now() > Date.parse(itemAgenda.startTime ? itemAgenda.startTime.replace(/-/g, '/') : itemAgenda.startTime) && Date.now() < Date.parse(itemAgenda.endTime) && this.meetingFieldsStatus === 3) {
                liveID.push(itemAgenda)
              } else if (Date.now() < Date.parse(itemAgenda.startTime.replace(/-/g, '/')) && (this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1)) {
                agendaDateFirst.push(itemAgenda)
              }
            })
          }) : null
        } else {
          /**
           * 遍历所有当前日期下的所有日程
           *
           */
          this.agendaListMerge.length > 0 ? this.agendaListMerge[0].subList.forEach(itemSub => {
            itemSub.itemList ? itemSub.itemList.forEach(itemAgenda => {
              /**
               * 如果 当前时间大于日程开始时间 并且 小于日程结束时间  并且会场状态是直播状态 那就选中该日程
               */
              if (Date.now() > Date.parse(itemAgenda.startTime.replace(/-/g, '/')) && Date.now() < Date.parse(itemAgenda.endTime.replace(/-/g, '/')) && this.meetingFieldsStatus === 3) {
                liveID.push(itemAgenda)
              } else if (Date.now() < Date.parse(itemAgenda.startTime.replace(/-/g, '/')) && (this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1)) {
                agendaDateFirst.push(itemAgenda)
              }
            }) : null
          }) : null
        }
        /**
         * 如果有大于现在时间的日程 那时间就等于那个日程的开始时间
         * 没有的话就等于空
         */
        if (agendaDateFirst[0]) {
          return agendaDateFirst[0].id
        }
        if (liveID[0]) {
          return liveID[0].id
        }
      } else if (this.meetingFieldsStatus !== 2 && this.meetingTemplate === '') {
        /**
         * 如果不是直播已结束 录播的话  那么就选今天的日程
         * @type {string}
         */
        const agendaDateFirst = []
        const liveID = []
        /**
         * 如果是系列模板的话 就显示默认日程id
         */
        if (this.dynamicMeetingTemplate === 'series') {
          return this.defaultAgendaId ? this.defaultAgendaId : this.defaultAgendaFirstId
        } else {
          /**
           * 遍历所有当前日期下的所有日程
           *
           */
          this.agendaListMerge.length > 0 ? this.agendaListMerge.forEach(itemSub => {
            /**
             * 如果 当前时间大于日程开始时间 并且 小于日程结束时间  并且会场状态是直播状态 那就选中该日程
             */
            if (Date.now() > Date.parse(itemSub.startTime.replace(/-/g, '/')) && Date.now() < Date.parse(itemSub.endTime.replace(/-/g, '/')) && this.meetingFieldsStatus === 3) {
              liveID.push(itemSub)
            } else if (Date.now() < Date.parse(itemSub.startTime.replace(/-/g, '/')) && (this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1)) {
              agendaDateFirst.push(itemSub)
            }
          }) : null
        }
        /**
         * 如果有大于现在时间的日程 那时间就等于那个日程的开始时间
         * 没有的话就等于空
         */
        if (agendaDateFirst[0]) {
          return agendaDateFirst[0].id
        }
        if (liveID[0]) {
          return liveID[0].id
        }
      } else {
        /**
         * 如果是录播的话 就选默认日程
         */
        return this.defaultAgendaId ? this.defaultAgendaId : this.defaultAgendaFirstId
      }
    },
    /**
     * 限制会议播放条件
     * @returns {string}
     */
    playerMaskFlag() {
      /**
       * L  代表需要登录
       * V  代表需要认证
       * T  代表正常播放
       * 1. 如果默认会场或者默认日程的播放条件为L 并且 用户没有登录 那就不让播放
       * 2. 如果默认会场或者默认日程播放条件为V实名播放  并且用户的认证状态不是已认证
       * 3. 如果说默认会场或者默认日程的播放条件为实名认证 并且 没有进行登录  那就先让去登录
       */
      if ((this.defaultFieldsCondition === 'L' || this.defaultAgendaCondition === 'L') && !this.$store.state.auth.token) {
        return 'L'
      } else if ((this.defaultFieldsCondition === 'V' || this.defaultFieldsCondition === 'V') && this.$store.state.auth.user.isAuth !== '1') {
        return 'V'
      } else if ((this.defaultFieldsCondition === 'V' || this.defaultFieldsCondition === 'V') && !this.$store.state.auth.token) {
        return 'L'
      } else {
        return 'T'
      }
    },
    /**
     * 组合日程
     * @returns {{}|[]}
     */
    agendaListMerge() {
      /**
       * 日程数据重组
       */
      if (this.dynamicMeetingTemplate === 'series') {
        /**
         * 1. 如果是系列会议模板 根据相同id 组合日程 然后按照时间排序排列
         * 2. 如果是别的模板 按照章节的时间去划分日期 然后在划分日程
         */
        const seriesDatalist = this.$tool.mergeArray(this.meetingDetails.agendaList, this.meetingDetails.sectionList, 'parentId')
        const dateDesc = seriesDatalist.sort(function(a, b) {
          // 按照时间降序
          return Date.parse(b.startTime.replace(/-/g, '/')) - Date.parse(a.startTime.replace(/-/g, '/'))
        })
        return dateDesc
      } else if (this.meetingTemplate === '') {
        /**
         * 线下模板 使用会场列表
         */
        // return this.offlineAgendaList.list
        return this.offlineAgendaList
      } else {
        /**
         * 根据日程时间 先按时间划分
         * 在划分章节
         * TODO 已取消该逻辑
         * @type {{}}
         */
        if (this.meetingDetails.sectionList) {
          return this.$tool.chapterArrayFun(this.meetingDetails.agendaList, this.meetingDetails.sectionList, 'startTime')// 在将按时间划分的数据 按章节划分
        } else {
          const parentDateList = this.$tool.mergeDateArray(this.meetingDetails.agendaList, 'startTime')// 先按时间划分
          return this.$tool.agendaDateArray(parentDateList, this.meetingDetails.sectionList, 'startTime')// 在将按时间划分的数据 按章节划分
        }
      }
    },
    /**
     * 监听是否创建了播放器
     * @returns {null|*}
     */
    isPlayer() {
      return this.$store.state.player// 需要监听的数据
    }
  },
  watch: {
    /**
     * 监听距离顶部滚动条位置
     */
    scroll: {
      handler: 'showTop'
    },
    /**
     * 监听会议详情切换状态
     * @param newVal
     * @param oldVal
     */
    meetingDetails(newVal, oldVal) {
      this.isLiveFlag = false
      const info = this.meetingDetails.meetingInfo
      if (newVal) {
        this.isLiveFlag = true
        /**
         *
         * @type {*|null}
         */
        this.defaultAgendaFirstId = newVal.agendaList.length > 0 ? newVal.agendaList[0].id : null // 第一个日程ID
        this.defaultAgendaId = newVal.defaultAgenda.id  // 默认日程ID
        this.defaultFieldsId = newVal.defaultFields.id // 默认会场ID
        this.meetingFieldsList = newVal.fieldsList // 所有会场
        this.meetingTemplate = newVal.meetingInfo.style // 会场模板
        this.defaultAgendaVid = newVal.defaultAgenda.vid // 默认日程的VID
        this.defaultAgendaPlayAuth = newVal.defaultAgenda.playAuth // 默认日程的PLayAuth
        this.meetingFieldsStatus = newVal.defaultFields.manualStatus // TODO 直播状态 0=直播未开始、1=暂停中、2=直播结束、3=直播中
        this.defaultFieldHisLiveUrl = newVal.defaultFields.hlsLiveUrl.split('https:').join('') // 默认会场的直播地址
        this.videoname = newVal.defaultAgenda.theme
        this.offlineAgendaList = newVal.agendaList
      }
      /**
       * 会议详情埋点
       */
      // this.$analysys.view_record_page(info.meetingName,"",this.meetingStatus==="LIVE"?"直播":"录播","",[],"",String(this.defaultAgendaIdActive?this.defaultAgendaIdActive:this.defaultAgendaId),0)
    },
    /**
     *  如果播放器更新了 重新获取弹幕列表
     * @param newVal
     * @param oldVal
     */
    isPlayer(newVal, oldVal) {
      this.getBarrageListFun()
    },
    /**
     * 监听 限制会议播放的变量
     * 如果会议状态是可播放的T  播放器播发
     * 否则 播放器暂停
     * 同时 如果会议登记是true的情况下 播放暂停
     * @param newVal
     */
    playerMaskFlag(newVal) {
      if (newVal) {
        this.videoPlayerFun
      }
    }
  },
  mounted() {

    /**
     * 采集会议
     */
    this.$store.state.auth.user.id ? this.acquisitionMeetingFun() : null
    /**
     * 加入滚动事件 用于监听直播或者录播 滚动到指定位置出现小播放器
     */
    window.addEventListener('scroll', this.getScroll)
    /**
     * 会议详情埋点
     */
    this.$analysys.view_record_page(this.meetingDetails.meetingInfo.meetingName, '', this.meetingStatus === 'LIVE' ? '直播' : '录播', '', [], '', String(this.defaultAgendaIdActive ? this.defaultAgendaIdActive : this.defaultAgendaId), 0)
    /**
     * 默认开启倒计时 用于直播倒计时
     * @type {number}
     */
    this.timefun = setInterval(this.testTimer, 1000) // 将以上所有函数带入'间隔性定时器（A，1000）'
    /**
     * 观看视频、60秒上传一次事件
     * @type {number}
     */
    const meetingGroupList = this.meetingDetails.meetingInfo.meetingGroupList // 会场分组
    const meetingTitle = this.meetingDetails.meetingInfo.meetingName // 视频父级名称
    var groupList = []
    let percent = null
    this.analysysMeetingTime = setInterval(() => {
      /**
       * 录播
       */
      if (this.meetingStatus === 'RECOR_DING') {
        var currentTime = document.querySelectorAll('video') ? document.querySelectorAll('video')[0].currentTime.toFixed() : 0 // 观看到哪
        var duration = document.querySelectorAll('video') ? document.querySelectorAll('video')[0].duration : 0 // 视频总时常
        percent = Number((currentTime) / Number(duration)).toFixed(2) * 100
      } else {
        /**
         * 直播
         * @type {string|number}
         */
        currentTime = 60 // 观看到哪
        duration = 60 // 视频总时常
        percent = null
      }
      for (var i in meetingGroupList) {
        groupList.push(meetingGroupList[i].name)
      }
      this.$analysys.view_video(groupList, '', '', Number(percent), String(this.defaultAgendaId), false, '', this.videoname, false, this.meetingStatus === 'LIVE' ? '会议直播' : '会议录播', 1, duration, null, '', meetingTitle, this.meetingField, this.videoname, this.$store.state.auth.unionid, String(currentTime), String(this.defaultFieldsId), String(this.$route.query.id), '', this.meetingStatus === 'LIVE' ? '' : String(this.defaultAgendaId))
    }, 60000)
  },
  destroyed() {
    /**
     * 离开时清除倒计时
     */
    clearInterval(this.timefun) //  清楚计时器
    clearInterval(this.analysysMeetingTime) // 清除计时器
  },
  directives: {
    dragzoom: {
      // 元素更新时触发  元素拖拽
      update: (el, params) => {
        // const target = el.parentElement  // 父节点
        /**
         * zoom
         */
        if (params.value.zoom) {
          // const smallBox = document.querySelector("#video_aliplayer_smallscreen")
          // const nwResizeStyle = `
          //         position: absolute;
          //         left: 0;
          //         top: 0;
          //         width:10px;
          //         height:10px;
          //         background:red;
          //         z-index:9999999;
          //         cursor:nw-resize;
          //         opacity:0;
          //     `
          // const neResizeStyle = `
          //         position: absolute;
          //         right: 0;
          //         top: 0;
          //           width:10px;
          //         height:10px;
          //         background:red;
          //         z-index:9999999;
          //         cursor:ne-resize;
          //         opacity:0;
          //     `
          // const swResizeStyle = `
          //         position: absolute;
          //         left: 0;
          //         bottom: 0;
          //            width:10px;
          //         height:10px;
          //         background:red;
          //         z-index:9999999;
          //         cursor:sw-resize;
          //         opacity:0;
          //     `
          // const seResizeStyle = `
          //         position: absolute;
          //         right: 0;
          //         bottom: 0;
          //         width:10px;
          //         height:10px;
          //         background:red;
          //         z-index:9999999;
          //         cursor:se-resize;
          //         opacity:0;
          //     `
          // var nwResize = document.createElement('div') // Upper left
          // var neResize = document.createElement('div') // Upper left
          // var swResize = document.createElement('div') // Upper left
          // var seResize = document.createElement('div') // Upper left
          // nwResize.style.cssText = nwResizeStyle
          // neResize.style.cssText = neResizeStyle
          // swResize.style.cssText = swResizeStyle
          // seResize.style.cssText = seResizeStyle
          // el.appendChild(nwResize)
          // el.appendChild(neResize)
          // el.appendChild(swResize)
          // el.appendChild(seResize)
          //
          const nwResize = document.querySelector('.zoom_btn_top_left')
          const neResize = document.querySelector('.zoom_btn_bottom_left')
          const swResize = document.querySelector('.zoom_btn_top_right')
          const seResize = document.querySelector('.zoom_btn_bottom_right')
          nwResize.addEventListener('mousedown', function(even) {
            if (even.preventDefault) {
              even.preventDefault()
            } else {
              even.returnValue = false  // 解决快速频繁拖动滞后问题
            }
            let keepLeft = 0
            let keepTop = 0
            /**
             *  first get元素宽高
             *   获取 鼠标距离元素的x轴位置
             *   获取 页面距离元素y轴的位置
             *
             * @type {boolean}
             */
            let flag = true // 每次按下去的时候 是开着状态
            let elWidth = el.clientWidth
            let elHeight = el.clientHeight
            const disX = even.pageX - el.offsetLeft //  鼠标距离元素的x位置
            const disY = even.pageY - el.offsetTop
            even.stopPropagation() // stop event bubbling
            /**
             * 移出时 获取 鼠标在页面的xx距离 减去鼠标距离元素的x轴距离 是元素最外面的距离 是元素的定位的left位置
             * 同理
             * 保留第一次移入时的位置
             * 实际位置减去首次移入的位置就是要增加的宽度
             * 高度同理
             * 这里可以设置最小宽高
             * 同时要保证 top 和 left
             * @param de
             */
            document.onmousemove = (de) => {
              let left = de.pageX - disX  // 鼠标在页面的x距离 减去 鼠标距离元素的x距离
              let top = de.pageY - disY
              if (flag) {
                /**
                 * 保留第一次移入时的位置
                 * @type {number}
                 */
                keepLeft = left
                keepTop = top
                flag = false
              }
              const nwWidth = left - keepLeft   // 实际位置  -  首次触发的位置 等于移入移出的位置
              const nwHeight = top - keepTop
              const boxWidth = elWidth - nwWidth // 盒子宽度
              const boxHeight = elHeight - nwWidth // 盒子宽度
              el.style.width = boxWidth < 400 ? 400 : boxWidth > 800 ? 800 : boxWidth + 'px' // 盒子宽度和高度都一样 等比缩放
              el.style.height = boxHeight < 225 ? 225 : boxHeight > 625 ? 625 : boxHeight + 'px'

              const boxTop = keepTop + nwWidth
              const boxLeft = keepLeft + nwWidth
              el.style.top = boxTop > keepTop ? keepTop : boxTop + 'px'  // 元素的高度等于第一次移出时的位置 加上元素增加的宽度   这里是为了盒子增加宽度后 不影响位置
              el.style.left = boxLeft > keepLeft ? keepLeft : boxLeft + 'px'  // 这里的left 加上  元素增加的宽度 因为是等比缩放 所以是一样
            }
            document.onmouseup = (de) => {
              document.onmousemove = document.onmouseup = null
            }
          }, false)

          neResize.addEventListener('mousedown', function(even) {
            if (even.preventDefault) {
              even.preventDefault()
            } else {
              even.returnValue = false  // 解决快速频繁拖动滞后问题
            }
            let keepLeft = 0
            let keepTop = 0
            /**
             *  first get元素宽高
             *   获取 鼠标距离元素的x轴位置
             *   获取 页面距离元素y轴的位置
             *
             * @type {boolean}
             */
            let flag = true // 每次按下去的时候 是开着状态
            let elWidth = el.clientWidth
            let elHeight = el.clientHeight
            const disX = even.pageX - el.offsetLeft //  鼠标距离元素的x位置
            const disY = even.pageY - el.offsetTop
            even.stopPropagation() // stop event bubbling
            /**
             * 移出时 获取 鼠标在页面的xx距离 减去鼠标距离元素的x轴距离 是元素最外面的距离 是元素的定位的left位置
             * 同理
             * 保留第一次移入时的位置
             * 实际位置减去首次移入的位置就是要增加的宽度
             * 高度同理
             * 这里可以设置最小宽高
             * 同时要保证 top 和 left
             * @param de
             */
            document.onmousemove = (de) => {
              let left = de.pageX - disX  // 鼠标在页面的x距离 减去 鼠标距离元素的x距离
              let top = de.pageY - disY
              if (flag) {
                /**
                 * 保留第一次移入时的位置
                 * @type {number}
                 */
                keepLeft = left
                keepTop = top
                flag = false
              }
              const nwWidth = left - keepLeft   // 实际位置  -  首次触发的位置 等于移入移出的位置
              const nwHeight = top - keepTop
              const boxWidth = elWidth + nwWidth // 盒子宽度
              const boxHeight = elHeight + nwWidth // 盒子宽度
              el.style.width = boxWidth < 400 ? 400 : boxWidth > 800 ? 800 : boxWidth + 'px' // 盒子宽度和高度都一样 等比缩放
              el.style.height = boxHeight < 225 ? 225 : boxHeight > 625 ? 625 : boxHeight + 'px'
              const boxTop = keepTop - nwWidth
              const boxLeft = keepLeft
              el.style.top = boxTop > keepTop ? keepTop : boxTop + 'px'  // 元素的高度等于第一次移出时的位置 加上元素增加的宽度   这里是为了盒子增加宽度后 不影响位置
              el.style.left = boxLeft + 'px'  // 这里的left 加上  元素增加的宽度 因为是等比缩放 所以是一样
            }
            document.onmouseup = (de) => {
              document.onmousemove = document.onmouseup = null
            }
          }, false)

          swResize.addEventListener('mousedown', function(even) {
            if (even.preventDefault) {
              even.preventDefault()
            } else {
              even.returnValue = false  // 解决快速频繁拖动滞后问题
            }
            let keepLeft = 0
            let keepTop = 0
            /**
             *  first get元素宽高
             *   获取 鼠标距离元素的x轴位置
             *   获取 页面距离元素y轴的位置
             *
             * @type {boolean}
             */
            let flag = true // 每次按下去的时候 是开着状态
            let elWidth = el.clientWidth
            let elHeight = el.clientHeight
            const disX = even.pageX - el.offsetLeft //  鼠标距离元素的x位置
            const disY = even.pageY - el.offsetTop
            even.stopPropagation() // stop event bubbling
            /**
             * 移出时 获取 鼠标在页面的xx距离 减去鼠标距离元素的x轴距离 是元素最外面的距离 是元素的定位的left位置
             * 同理
             * 保留第一次移入时的位置
             * 实际位置减去首次移入的位置就是要增加的宽度
             * 高度同理
             * 这里可以设置最小宽高
             * 同时要保证 top 和 left
             * @param de
             */
            document.onmousemove = (de) => {
              let left = de.pageX - disX  // 鼠标在页面的x距离 减去 鼠标距离元素的x距离
              let top = de.pageY - disY
              if (flag) {
                /**
                 * 保留第一次移入时的位置
                 * @type {number}
                 */
                keepLeft = left
                keepTop = top
                flag = false
              }
              const nwWidth = left - keepLeft   // 实际位置  -  首次触发的位置 等于移入移出的位置
              const nwHeight = top - keepTop
              const boxWidth = elWidth - nwWidth // 盒子宽度
              const boxHeight = elHeight - nwWidth // 盒子宽度
              el.style.width = boxWidth < 400 ? 400 : boxWidth > 800 ? 800 : boxWidth + 'px' // 盒子宽度和高度都一样 等比缩放
              el.style.height = boxHeight < 225 ? 225 : boxHeight > 625 ? 625 : boxHeight + 'px'
              const boxTop = keepTop
              const boxLeft = keepLeft + nwWidth
              el.style.top = boxTop + 'px'  // 元素的高度等于第一次移出时的位置 加上元素增加的宽度   这里是为了盒子增加宽度后 不影响位置
              el.style.left = boxLeft > keepLeft ? keepLeft : boxLeft + 'px'  // 这里的left 加上  元素增加的宽度 因为是等比缩放 所以是一样
            }
            document.onmouseup = (de) => {
              document.onmousemove = document.onmouseup = null
            }
          }, false)

          seResize.addEventListener('mousedown', function(even) {
            if (even.preventDefault) {
              even.preventDefault()
            } else {
              even.returnValue = false  // 解决快速频繁拖动滞后问题
            }
            let keepLeft = 0
            let keepTop = 0
            /**
             *  first get元素宽高
             *   获取 鼠标距离元素的x轴位置
             *   获取 页面距离元素y轴的位置
             *
             * @type {boolean}
             */
            let flag = true // 每次按下去的时候 是开着状态
            let elWidth = el.clientWidth
            let elHeight = el.clientHeight
            const disX = even.pageX - el.offsetLeft //  鼠标距离元素的x位置
            const disY = even.pageY - el.offsetTop
            even.stopPropagation() // stop event bubbling
            /**
             * 移出时 获取 鼠标在页面的xx距离 减去鼠标距离元素的x轴距离 是元素最外面的距离 是元素的定位的left位置
             * 同理
             * 保留第一次移入时的位置
             * 实际位置减去首次移入的位置就是要增加的宽度
             * 高度同理
             * 这里可以设置最小宽高
             * 同时要保证 top 和 left
             * @param de
             */
            document.onmousemove = (de) => {
              let left = de.pageX - disX  // 鼠标在页面的x距离 减去 鼠标距离元素的x距离
              let top = de.pageY - disY
              if (flag) {
                /**
                 * 保留第一次移入时的位置
                 * @type {number}
                 */
                keepLeft = left
                keepTop = top
                flag = false
              }
              const nwWidth = left - keepLeft   // 实际位置  -  首次触发的位置 等于移入移出的位置
              const nwHeight = top - keepTop
              const boxWidth = elWidth + nwWidth // 盒子宽度
              const boxHeight = elHeight + nwWidth // 盒子宽度
              el.style.width = boxWidth < 400 ? 400 : boxWidth > 800 ? 800 : boxWidth + 'px' // 盒子宽度和高度都一样 等比缩放
              el.style.height = boxHeight < 225 ? 225 : boxHeight > 625 ? 625 : boxHeight + 'px'
              const boxTop = keepTop
              const boxLeft = keepLeft
              el.style.top = boxTop + 'px'  // 元素的高度等于第一次移出时的位置 加上元素增加的宽度   这里是为了盒子增加宽度后 不影响位置
              el.style.left = boxLeft + 'px'  // 这里的left 加上  元素增加的宽度 因为是等比缩放 所以是一样
            }
            document.onmouseup = (de) => {
              document.onmousemove = document.onmouseup = null
            }
          }, false)
        } else {
          el.style.width = '100%'
          el.style.height = '100%'
        }


        /**
         * {
         * drag 开关元素拖拽
         * zoom 开关缩放 切记使用缩放该元素需设置相对定位
         * }
         *
         */
        if (params.value.drag) {
          el.onmousedown = (e) => {
            // e.stopPropagation(); // stop event bubbling
            const disX = e.pageX - el.offsetLeft
            const disY = e.pageY - el.offsetTop
            if (e.preventDefault) {
              e.preventDefault()
            } else {
              e.returnValue = false  // 解决快速频繁拖动滞后问题
            }

            document.onmousemove = (de) => {
              let left = de.pageX - disX
              let top = de.pageY - disY
              // 限制拖拽范围不超出可视区
              if (left <= 0) {
                left = 5    // 设置成5,离边缘不要太近
              } else if (left > document.documentElement.clientWidth - el.clientWidth) { // document.documentElement.clientWidth屏幕可视区宽度
                left = document.documentElement.clientWidth - el.clientWidth - 5
              }

              if (top <= 0) {
                top = 5
              } else if (top > document.documentElement.clientHeight - el.clientHeight) {
                top = document.documentElement.clientHeight - el.clientHeight - 5
              }

              el.style.left = left + 'px'
              el.style.top = top + 'px'
              el.style.cursor = 'move'
            }
            document.onmouseup = (de) => {
              document.onmousemove = document.onmouseup = null
            }
          }
        } else {
          /**
           * 关闭元素拖拽
           * @param e
           */
          el.onmousedown = (e) => {
            // do something
          }
        }
      }
    }
  },
  methods: {
    /**
     * 采集会议
     */
    async acquisitionMeetingFun(agendaId, fieldsId) {
      /**
       * agendaId fieldsId 是点击日程或会场才有的参数 所以优先采集这两个参数
       * 默认采集日程 如果日程没有就采集会场  然后点击日程采集日程 点击会场采集会场
       */
      if ((this.defaultAgendaIdActive && !fieldsId) || agendaId) {
        await this.$axios.$request(saveBrowsingHistory({
          loginUserId: this.$store.state.auth.user.id,
          contentSource: 6,
          contentId: agendaId ? agendaId.id : this.defaultAgendaIdActive,
          courseId: null,
          playDuration: null
        }))
      } else if ((this.defaultFieldsId && !agendaId) || fieldsId) {
        await this.$axios.$request(saveBrowsingHistory({
          loginUserId: this.$store.state.auth.user.id,
          contentSource: 5,
          contentId: fieldsId ? fieldsId : this.defaultFieldsId,
          courseId: null,
          playDuration: null
        }))
      }
    },
    /**
     * 滚动条超过播放器 就显示小播放器
     * @param newValue
     * @param oldValue
     */
    showTop(newValue, oldValue) {
      /**
       * 直播录播限制正常
       */
      if ((this.meetingStatus === 'LIVE' || this.meetingStatus === 'RECOR_DING') && this.playerMaskFlag === 'T') {
        const meeting_occupying = document.querySelector('.occupying') // 会议播放器
        const meeting_video = document.querySelector('.video_aliplayer') // 会议播放器
        const bottom_btn_box = document.querySelector('.barrage_list_box') // 会议底部按钮
        const meetingVideoTop = meeting_occupying.getBoundingClientRect().top // 距离顶部位置
        const meetingVideoHeight = meeting_occupying.clientHeight // 会议播放器高度
        const bottom_btn_boxHeight = bottom_btn_box.clientHeight
        const navHeight = 60 // 导航高度
        /**
         * 会议播放器的高度 加上具体顶部  在加底部按钮的高度 减去 导航高度
         */
        if ((meetingVideoHeight + meetingVideoTop + bottom_btn_boxHeight - navHeight) < 0) {
          meeting_video.setAttribute('id', 'video_aliplayer_smallscreen')
          // meeting_video.classList.add("drsElement")
          this.smallScreen = true
        } else {
          meeting_video.setAttribute('id', '')
          // meeting_video.classList.remove("drsElement")
          this.smallScreen = false
        }
      }
    },
    /**
     * 距离顶部位置
     */
    getScroll() {
      this.scroll = document.documentElement.scrollTop || document.body.scrollTop
    },
    /**
     * 刷新评论 用于发送直播弹幕时刷新评论
     */
    refreshCommentsFun(data) {
      this.refreshComments = data
    },
    /**
     * 埋点
     * @param name
     */
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    },
    /**
     * 线下模板切换日程时间
     * @param date
     */
    editAgendaDateFun(date) {
      this.$axios.$request(getAgendaListByQDate({
        fieldsId: this.meetingDetails.defaultFields.id,
        qDate: date
      })).then(res => {
        this.offlineAgendaList = res.list
      })
    },
    /**
     * 身份认证弹框
     * @param data
     */
    authenticationDialogFun(data) {
      this.$analysys.btn_click('去认证', document.title)
      this.authenticationDialog = data
    },
    /**
     * 身份认证按钮提示
     */
    jumpReleaseCaseFun() {
      this.$axios.$request(userInfo()).then(res => {
        if (res && res.code === 1) {
          if (res.result.isAuth === '2') {
            this.$toast.fail('身份审核中')
          } else if (res.result.isAuth === '3') {
            this.$toast.fail('身份认证失败')
          } else if (!res.result.identity || res.result.identity === 'undefined') {
            const h = this.$createElement
            this.$msgbox({
              message: h('p', null, [
                h('span', null, '部分功能需要完善信息和认证后方可使用，请在15个工作日内将真实姓名等必填信息补充完整！')
              ]),
              center: true,
              showCancelButton: true,
              cancelButtonText: '以后再说',
              confirmButtonText: '去完善信息',
              customClass: 'personaldata-messagebox',
              beforeClose: (action, instance, done) => {
                if (action === 'confirm') {
                  this.$store.commit('editAccountTypeFun', this.$store.state.auth.user.email ? this.$store.state.auth.user.email : 'tel')
                  this.$store.commit('editIdentityInformationFun', 'SelectIdentity')// 到身份选择页面
                  this.$router.push({ name: 'register' })
                  done()
                } else {
                  done()
                }
              }
            })
          } else if (res.result.isAuth === '0') {
            this.authenticationDataInfo = res.result
            this.authenticationDialog = true// 打开认证弹框
          } else {
            this.$toast.success('已认证')
            this.$store.commit('editUserDataInfoFunObject', { keyName: 'isAuth', content: res.result.isAuth })
          }
        }
      })
    },
    /**
     * 跳转登录
     */
    jumpSigninFun() {
      this.$analysys.btn_click('登录', document.title)
      this.$store.commit('editBackUrl', window.location.href)
      this.$router.push({ name: 'signin', query: { fallbackUrl: this.$route.fullPath } })
    },
    /**
     * 动态控制问卷调查开关的字段赋值
     * @param val
     */
    editquestionnaireStatus(val) {
      this.questionnaireStatus = val
    },
    /**
     * 动态控制直播开关的字段赋值
     * @param val
     */
    editManualStatus(val) {
      /**
       * 0=直播未开始、
       * 1=暂停中、
       * 2=直播结束、
       * 3=直播中
       */
      this.meetingFieldsStatus = Number(val)
    },
    /**
     * 会议登记弹框控制  用于子组件关闭弹框后父组件可以改变状态
     * @param flag
     */
    editDialogFlag(flag) {
      this.isDialogFlag = flag
    },
    /**
     * 判断会议播放限制 如果是T才可以播放
     * @param video
     */
    videoPlayerFun(video) {
      if (this.playerMaskFlag === 'T') {
        video.play()
      } else {
        video.pause()
      }
      if (this.isDialogFlag === true) {
        video.pause()
      }
    },
    /**
     * 获取会场点赞数
     * @param count
     */
    getLikesFun(count) {
      this.likesNumber = count
    },
    /**
     * 获取弹幕列表
     */
    getBarrageListFun() {
      const _this = this
      this.$axios.$request(getBarrageList({
        agendaId: this.meetingStatus === 'LIVE' ? 999 : this.defaultAgendaId, // 直播不获取弹幕
        fieldsId: this.defaultFieldsId
      })).then(res => {
        if (res.code === 1) {
          this.barrageList = res.list // 包含了当前日程直播和录播的弹幕
          // 筛选出录播弹幕
          const recordedList = this.barrageList.filter(function(item) {
            return (item.agendaId && item.videoCurrentDuration)
          })
          // 需要先重新加载 清空
          _this.$store.state.CM.load([
            {
              'mode': 8,            // mode 表示弹幕的类型，参考 弹幕类型 https://github.com/jabbany/CommentCoreLibrary/blob/master/docs/CommentTypes.md
              'text': '',       // text 表示弹幕的文字内容。注意：在创造弹幕对象后，对 text 的更改将无意义。
              'stime': 1000,        // stime 表示弹幕相对于视频位置的开始时间（ms），0即在视频开始立即出现
              'size': 20,           // 弹幕的文字大小
              'color': 0xFFFFFF    // 文字颜色
            }
          ])
          recordedList.map((item) => {
            _this.$store.state.CM.insert({
              'mode': 1,            // mode 表示弹幕的类型，参考 弹幕类型 https://github.com/jabbany/CommentCoreLibrary/blob/master/docs/CommentTypes.md
              'text': item.text,       // text 表示弹幕的文字内容。注意：在创造弹幕对象后，对 text 的更改将无意义。
              'stime': item.videoCurrentDuration * 1000 - 500,        // stime 表示弹幕相对于视频位置的开始时间（ms），0即在视频开始立即出现
              // "stime": 1000,        // stime 表示弹幕相对于视频位置的开始时间（ms），0即在视频开始立即出现
              'size': 20,           // 弹幕的文字大小
              'color': parseInt(item.color.replace(/#/g, ''), 16)    // 文字颜色
            })
          })
        }
      })
    },
    /**
     * 修改tabs到问卷调查
     * @param name
     * @param idPage
     */
    editactiveNameFun(name, idPage) {
      this.activeName = String(name)
      if (idPage) {
        document.querySelector('.barrage_list_box').scrollIntoView({
          behavior: 'smooth',  // 平滑过渡
          block: 'start'  // 上边框与视窗顶部平齐。默认值
        })
      }
    },
    /**
     * 添加到日历提醒
     * 暂时延迟
     */
    addCalendartipsFun() {
      const startDateTime = '20200403T170000'

      const endDateTime = '20200403T180000'

      const datetime = 'Friday, 3rd April 2020, 5pm'

      const location = 'XXX street'

      const icsMSG =
        'BEGIN:VCALENDAR\nPRODID:-// Microsoft Corporation// Outlook 16.0 MIMEDIR// EN\nVERSION:2.0\nMETHOD:REQUEST\nX-MS-OLK-FORCEINSPECTOROPEN:TRUE\nBEGIN:VEVENT\nDESCRIPTION:When: ' +
        datetime +
        ';Where: ' +
        location +
        '\nDTSTART;TZID="China/Beijing":' +
        startDateTime +
        '\nDTEND;TZID="China/Beijing":' +
        endDateTime +
        '\nLOCATION:' +
        location +
        '\nSUMMARY;LANGUAGE=en-sg:You have a appointment\nX-MICROSOFT-CDO-BUSYSTATUS:TENTATIVE\nX-MICROSOFT-CDO-IMPORTANCE:1\nX-MICROSOFT-CDO-INTENDEDSTATUS:BUSY\nX-MICROSOFT-DISALLOW-COUNTER:FALSE\nX-MS-OLK-CONFTYPE:0\nBEGIN:VALARM\nTRIGGER:-PT15M\nACTION:DISPLAY\nDESCRIPTION:Reminder\nEND:VALARM\nEND:VEVENT\nEND:VCALENDAR\n'

      window.open('data:text/calendar;charset=utf8,' + escape(icsMSG))
    },
    /**
     * 直播倒计时
     */
    testTimer() {
      let time = null
      if (((this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1) && this.isLive !== 'issoonLive') && this.meetingTemplate !== '' && this.dynamicMeetingTemplate !== 'series') {

        /**
         * 如果会场状态是直播未开始 或者暂停中 那就显示倒计时 默认找当前第一个日期的日程 先循环章节 在循环日程  如果该日程大于当前时间 就放进去, 如果开始时间大于当前时间 那就找第二个 如果没有就不显示倒计时
         * 直播结束和直播中的话 是不需要倒计时的 所以这边不做判断
         * 并且不等于线下模板
         */
        const agendaDateFirst = []
        // 遍历当前日期下的第一个大于现在时间的日程
        this.agendaListMerge[0].subList.forEach(itemSub => {
          itemSub.itemList ? itemSub.itemList.forEach(itemAgenda => {
            if (Date.now() < Date.parse(itemAgenda.startTime.replace(/-/g, '/'))) {
              agendaDateFirst.push(itemAgenda)
            }
          }) : null
        })
        /**
         * 如果有大于现在时间的日程 那时间就等于那个日程的开始时间
         * 没有的话就等于空
         */
        if (agendaDateFirst[0]) {
          time = agendaDateFirst[0].startTime
        } else {
          time = null
        }
      } else if (((this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1) && this.isLive !== 'issoonLive') && this.dynamicMeetingTemplate === 'series') {
        const agendaDateFirst = []
        // 遍历当前日期下的第一个大于现在时间的日程
        this.agendaListMerge.forEach(itemSub => {
          itemSub.itemList ? itemSub.itemList.forEach(itemAgenda => {
            if (Date.now() < Date.parse(itemAgenda.startTime.replace(/-/g, '/'))) {
              agendaDateFirst.push(itemAgenda)
            }
          }) : null
        })
        /**
         * 如果有大于现在时间的日程 那时间就等于那个日程的开始时间
         * 没有的话就等于空
         */
        if (agendaDateFirst[0]) {
          time = agendaDateFirst[0].startTime
        } else {
          time = null
        }
      } else if ((this.meetingFieldsStatus === 0 || this.meetingFieldsStatus === 1) && this.isLive !== 'issoonLive' && this.meetingTemplate === '') {
        const agendaDateFirst = []
        // 遍历当前日期下的第一个大于现在时间的日程
        this.agendaListMerge.forEach(itemSub => {
          if (Date.now() < Date.parse(itemSub.startTime.replace(/-/g, '/'))) {
            agendaDateFirst.push(itemSub)
          }
        })
        /**
         * 如果有大于现在时间的日程 那时间就等于那个日程的开始时间
         * 没有的话就等于空
         */
        if (agendaDateFirst[0]) {
          time = agendaDateFirst[0].startTime
        } else {
          time = null
        }
      } else {
        time = this.meetingDetails.defaultAgenda.startTime
      }
      if (!time) {
        clearInterval(this.timefun)
        return
      }
      const timeData = this.timeStamp.timestamp_13(time, 'y-m-d-h-m').split('/')
      // 计算相差多少毫秒      未来时间.需要传参.月份"month-1"     当前时间
      const minusTime = new Date(timeData[0], timeData[1] - 1, timeData[2], timeData[3], timeData[4]) - new Date()
      this.countDownFlag = minusTime > 0// 如果是负数 就代表时间已经过了 就不显示日期倒计时
      // 获取元素
      this.mymonths = parseInt(minusTime / 2592000000) // 1天 = 86  400000毫秒
      this.mydays = parseInt((minusTime % 2592000000) / 86400000) // 1天 = 86  400000毫秒
      this.myHours = parseInt((minusTime % 86400000) / 3600000) // 1小时 = 3600000毫秒
      this.myMinutes = parseInt((minusTime % 3600000) / 60000) // 1分钟 = 60000毫秒
      this.mySeconds = parseInt((minusTime % 60000) / 1000) // 1秒 = 1000毫秒
      // 空位补零.
      function checkTime(i) {
        // 将0-9的数字前面加上0\.  1变为01
        // 判断 时间必须小于0 才可以补0
        if (i > 0) {
          if (i < 10) {
            return '0' + i
          } else {
            return i
          }
        } else {
          return i
        }
      }

      if (!this.countDownFlag) {
        this.mydays = '0'
        this.myHours = '0'
        this.myMinutes = '00'
        this.mySeconds = '00'
      } else {
        this.mydays = checkTime(this.mydays)
        this.myHours = checkTime(this.myHours)
        this.myMinutes = checkTime(this.myMinutes)
        this.mySeconds = checkTime(this.mySeconds)
      }
      // 带入函数,获取新元素  补0

    },
    /**
     * 切换会场
     * @param fieldsId
     * @param callback
     * @returns {Promise<void>}
     */
    async switchVenueFun(fieldsId, callback) {
      /**
       * 采集会议
       */
      this.$store.state.auth.user.id ? this.acquisitionMeetingFun(null, fieldsId) : null
      /**
       * 获取会议页面详情
       */
      await this.$axios.$request(getAllLiveMeetingDetail({
        userId: this.$store.state.auth.user.id,
        meetingId: this.$route.query.id,
        fieldsId
      })).then(res => {
        if (res && res.code === 1) {
          this.$router.push({
            name: 'index-meeting-detail',
            query: {
              id: this.$route.query.id,
              fieldsId,
              agendaId: this.defaultAgendaIdActive,
              date: this.defaultAgendaDateActive
            }
          })
          callback(true)
          /**
           * 如果会场状态是3的话 直播状态为直播
           */
          if (this.meetingFieldsStatus === 3) {
            this.isLive = 'isLive' // 切换会场要清除状态
          } else {
            this.isLive = 'F' // 切换会场要清除状态
          }
          this.$store.state.player = null
          this.meetingDetails = res.result
        }
      })
      await this.$axios.$request(getMeetingAdList({
        meetingId: this.$route.query.id,
        fieldsId: fieldsId
      })).then(res => {
        if (res && res.code === 1) {
          this.meetingAdvertisementDataList = res.list
        }
      })
    },
    /**
     * 切换日程
     * @param agendaId
     * @param callback
     */
    switchVenueSAgendaFun(agendaId, callback) {
      /**
       * 采集会议
       */
      this.$store.state.auth.user.id ? this.acquisitionMeetingFun(agendaId, null) : null
      this.$axios.$request(getAllLiveMeetingDetail({
        userId: this.$store.state.auth.user.id,
        meetingId: this.$route.query.id,
        agendaId: agendaId.id
      })).then(res => {
        if (res && res.code === 1) {
          // eslint-disable-next-line node/no-callback-literal
          this.isLive = agendaId.type
          callback(true)
          this.meetingDetails = res.result
        }
      })
    },
    /**
     * 跳转会议广告
     * @param url
     */
    jumpCarouselFun(url, id, name) {
      this.$analysys.ad_click('会议详情页', this.$store.state.auth.user.id, String(name), '会议播放页', String(url), String(id))
      if (url) {
        window.open(url)
      }
    }
  }
}
</script>


<style lang='less' scoped>
@import "~@/pages/index/meeting/detail-out/index.less";
</style>
