/deep/ .el-tabs__item {
  user-select: none !important;
  line-height: 25px !important;
  margin-bottom: 12px !important;
  height: 25px !important;

  &:focus {
    user-select: none;
    border: none;
    box-shadow: none !important;
  }
}

/deep/ .el-tabs__nav {
  user-select: none;
}

.meeting_details_box {
  margin-top: 20px;
  //user-select: none;
  // 会议标题
  .conference_title {
    color: #333333;
    margin: 30px 0 17px;
    font-weight: bold;
  }

  // 会议内容
  .conference_content_box {
    // 视频
    .conference_video_box {
      width: 100%;
      padding-bottom: 56.25%;
      height: 0;
      position: relative;

      .questionnaireicon {
        position: absolute;
        left: -52px;
        bottom: 0;
        width: 40px;
        height: 40px;
      }

      // 占位
      .occupying {
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        position: absolute;
        border-radius: 6px 6px 0 0;
        overflow: hidden;

        .player_limit_mask {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.9);
          text-align: center;
          z-index: 1000;

          .login_user_box {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            .title {
              color: white;
              font-size: 26px;
            }

            .login_button {
              display: inline-block;
              background: red;
              color: white;
              font-size: 16px;
              line-height: 16px;
              text-align: center;
              border-radius: 30px;
              padding: 5px 18px;
              margin-top: 20px;
            }
          }

        }

        #video_aliplayer_smallscreen {
          position: fixed;
          right: 2vw;
          bottom: 35px;
          width: 400px;
          height: 225px;
          z-index: 5000;
          animation: an 0.5s;

          .prism-player {
            border-radius: 10px;
            overflow: hidden;
          }

          .resize_box {
            z-index: 999999;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: red;

            .resize_li {

            }
          }
        }

        .video_aliplayer {
          width: 100%;
          height: 100%;
          //cursor: ne-resize;
          .zoom_btn_box {
            .zoom_btn_top_left {
              position: absolute;
              left: 0;
              top: 0;
              width: 10px;
              height: 10px;
              background: red;
              z-index: 9999999;
              cursor: nw-resize;
              opacity: 0;
            }

            .zoom_btn_bottom_left {
              position: absolute;
              right: 0;
              top: 0;
              width: 10px;
              height: 10px;
              background: red;
              z-index: 9999999;
              cursor: ne-resize;
              opacity: 0;
            }

            .zoom_btn_top_right {
              position: absolute;
              left: 0;
              bottom: 0;
              width: 10px;
              height: 10px;
              background: red;
              z-index: 9999999;
              cursor: sw-resize;
              opacity: 0;
            }

            .zoom_btn_bottom_right {
              position: absolute;
              right: 0;
              bottom: 0;
              width: 10px;
              height: 10px;
              background: red;
              z-index: 9999999;
              cursor: se-resize;
              opacity: 0;
            }
          }

          .video_Notice {
            width: 100%;
            height: 100%;

            .mack_span {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              text-align: center;
              font-size: 20px;
              color: white;
              font-weight: bold;
            }

            .Mask {
              border-radius: 6px 6px 0 0;
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              background-color: rgba(0, 0, 0, 0.6);
              text-align: center;

              .mask_first_title {
                color: #ffffff;
                font-size: 26px;
                margin-bottom: 15px;
              }

              .mask_second_time {
                font-size: 48px;
                color: #ffffff;
                margin-bottom: 40px;
                line-height: 47px;

                .time {
                  margin-right: 10px;
                }

                .zhan {
                  font-size: 28px;
                  margin-right: 10px;
                }
              }

              .mask_three_button {
                display: inline-block;
                border-radius: 21px !important;
                background: #ffffff !important;
                width: auto;
                padding: 10px 17px;

                span {
                  margin-left: 3px;
                }
              }
            }
          }
        }

        img {
          display: block;
        }
      }
    }
  }
}

// 会议广告
.advertisement {
  border-radius: 0 0 6px 6px;
  overflow: hidden;

  .advertisement_img {
    width: 100%;
  }
}

.kalami {
  width: 300px;
  position: fixed;
  right: 30px;
  top: 30vh;
  z-index: 99999;

  button {
    display: block;
  }
}

.bottom_btn_box {
  margin-bottom: 20px;
}
