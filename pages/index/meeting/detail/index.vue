<template>
  <div class='meeting-detail-box container-box'>
    <!--幸运中奖弹窗 Start-->
    <luckyTips/>
    <!--幸运中奖弹窗 End-->

    <!--会议模板 Start-->
    <component :is='meetingTemplate'/>
    <!--会议模板 End-->

    <!--公共外部用户验证 start-->
    <ExternalUsers/>
    <!--公共外部用户验证 End-->

    <!--用户成长体系记录 start-->
    <GrowthSystem/>
    <!--用户成长体系记录 End-->

    <!--付费会议开票 start-->
    <MeetingInvoicing
      v-if="$store.state.auth.token && $store.state.meeting.meetingData.meetingInfo.payView==='T' && $store.state.meeting.meetingData.meetingInfo.payStatus==='T'"
    />
    <!--付费会议开票 End-->

    <!--白名单验证 start-->
    <WhitelistVerificationCommon
      v-if="$store.state.meeting.meetingData.meetingInfo.openWhiteList==='T' && $route.query.id !== '2759'"
    />
    <!--白名单验证 end-->

    <!-- 会议广告弹窗 websocket-->
    <MeetingLiveDialog :visible="meetingLiveDialogVisible"/>
    <!-- 会议文本弹窗 websocket-->

    <!--本次会议仅供专业人士观看-->
    <LimitPlayback :userInfo="userInfo"/>
  </div>
</template>

<script>
// 用户成长体系
import {acquisitionMeetingFun, meetingRecordHandler} from 'assets/helpers/meeting-collection'
import GrowthSystem from '../../../../components/optimize-components/page-components/meeting/GrowthSystem/index.vue'
// 会议埋点工具
// 2715定制会议
import Template2715 from '../../../../components/meeting-test/meeting-template/Template2715/index.vue'
// 外部用户验证
import MeetingInvoicing
  from "../../../../components/optimize-components/page-components/meeting/MeetingInvoicing/index.vue";
import {getSlotContent} from "../../../../api/banner/banner";
import WhitelistVerificationCommon
  from "../../../../components/meeting-test/layout/WhitelistVerificationCommon/index.vue";
import {getWebApiPersonalWebsite} from "../../../../api/lucky";
import ExternalUsers
  from '@/components/optimize-components/page-components/meeting/CommunalVerification/ExternalUsers/index.vue'
// 会议测试工具
import MeetingDebugger from '@/components/meeting-test/layout/MeetingDebugger/MeetingDebugger'
// 默认模板
import DefaultTemplate from '@/components/meeting-test/meeting-template/DefaultTemplate/DefaultTemplate'
// 年会模板
import AnnualTemplate from '@/components/meeting-test/meeting-template/AnnualTemplate/AnnualTemplate'
// 线上讲课模板
import TeachTemplate from '@/components/meeting-test/meeting-template/TeachTemplate/TeachTemplate'
// 线上答疑模板
import AnswerTemplate from '@/components/meeting-test/meeting-template/AnswerTemplate/AnswerTemplate'
// 原生同传模板
import TwochannelTemplate from '@/components/meeting-test/meeting-template/TwochannelTemplate/TwochannelTemplate'
// 病种视频模板
import DiseaseTemplate from '@/components/meeting-test/meeting-template/DiseaseTemplate/DiseaseTemplate'
// 系列会议模板
import SeriesTemplate from '@/components/meeting-test/meeting-template/SeriesTemplate/SeriesTemplate'
// 幸运中奖弹窗
import luckyTips from '@/components/LuckyDrawPop/luckyTips'
// 公用api
import {
  getAllLiveMeetingDetail,
  getMeetingDetail,
  getMeetingPv,
  getTodayOtherMeetings
} from '@/api/meeting'
// 会议广告弹框
import MeetingLiveDialog from "../../../../components/meeting-test/layout/MeetingLiveDialog/index.vue";
import Button from "../../../../components/Button/Button.vue";
import LimitPlayback from "../../../../components/meeting-test/layout/LimitPlayback/index.vue";

export default {
  name: 'MeetingIndexPage',
  components: {
    LimitPlayback,
    Button,
    WhitelistVerificationCommon,
    MeetingInvoicing,
    ExternalUsers,
    DefaultTemplate,
    AnnualTemplate,
    TeachTemplate,
    AnswerTemplate,
    TwochannelTemplate,
    DiseaseTemplate,
    SeriesTemplate,
    MeetingDebugger,
    luckyTips,
    Template2715,
    GrowthSystem,
    MeetingLiveDialog
  },
  async asyncData({app, params, error, store, query, req, redirect}) {
    /**
     * request1  该会议详情 (重要)
     * request2  会议会议pv
     * request3  会议介绍 Tab首页的介绍
     * request4  获取今日直播会议
     * request5  获取会议广告
     * request6  获取个人信息
     */
    const [request1, request2, request3, request4] = await Promise.all([
      app.$axios.$request(getAllLiveMeetingDetail({
        userId: store.state.auth.user.id,
        meetingId: query.id,
        agendaId: query.agendaId ? query.agendaId : null,
        fieldsId: query.fieldsId ? query.fieldsId : null
      })),
      app.$axios.$request(getMeetingPv({
        meetingId: query.id
      })),
      app.$axios.$request(getMeetingDetail({
        meetingId: query.id
      })),
      app.$axios.$request(getTodayOtherMeetings({
        meetingId: query.id
      }))
    ])

    const request5 = await app.$axios.$request(
      getSlotContent({
        loginUserId: store.state.auth.user.id,
        detailIdStr: request1.result.defaultFields.id,
        adCode: 'meeting_broadcast_room',
      })
    )

    let userInfo = null
    if (store.state.auth.token) {
      userInfo = await app.$axios.$request(getWebApiPersonalWebsite({
          userId: store.state.auth.user.id,
          profileUserId: store.state.auth.user.id
        }
      ))
    }

    // 当前会议详情
    const meetingData = request1.result
    // 当前会议PV
    const meetingPv = request2.result.pv
    // 当前会议页面介绍
    const meetingDetail = request3.result
    // 当前会议相关的别的直播
    const todayOtherMeetingDataList = request4.list
    // 当前会议的默认会场的广告
    const meetingAdList = request5.list
    // 当前会议的模板
    let meetingTemplate
    switch (request1.result.meetingInfo.style) {
      // 默认模板 (线下模板)
      case '': {
        meetingTemplate = 'DefaultTemplate'
        break
      }
      // 线上讲课
      case 'teach': {
        meetingTemplate = 'TeachTemplate'
        break
      }
      // 线上答疑
      case 'answer': {
        meetingTemplate = 'AnswerTemplate'
        break
      }
      // 原生同传
      case 'twochannel': {
        meetingTemplate = 'TwochannelTemplate'
        break
      }
      // 病种视频
      case 'disease': {
        meetingTemplate = 'DiseaseTemplate'
        break
      }
      // 年会模板
      case 'annual': {
        meetingTemplate = 'AnnualTemplate'
        break
      }
      // 系列会议模板
      case 'series': {
        meetingTemplate = 'SeriesTemplate'
        break
      }
      default: {
        meetingTemplate = 'AnnualTemplate'
      }
    }
    switch (String(query.id)) {
      case '1198': {
        meetingTemplate = 'SeriesTemplate'
        break
      }
      case '2715': {
        meetingTemplate = 'Template2715'
        break
      }
    }

    if (userInfo) {
      // 个人信息
      store.commit('meeting/setIsUserAuth', userInfo.result.isAuth)
    }

    store.commit('meeting/setPageTemplateHandler', meetingTemplate) // 会议模板 [自定义]
    store.commit('meeting/setMeetingDataHandler', meetingData) // 会议数据
    store.commit('meeting/setMeetingTemplateHandler', meetingData.meetingInfo.style)  // 会议模板
    store.commit('meeting/setMeetingActiveStateHandler', {fieldsId: meetingData.defaultFields.id}) // 会议保存默认会场ID
    store.commit('meeting/setMeetingAdListHandler', meetingAdList) // 会议广告数据
    store.commit('meeting/setMeetingDetailHandler', meetingDetail) // 会议详情数据
    store.commit('meeting/setMeetingPvHandler', meetingPv) // 会议PV数据
    store.commit('meeting/setTodayOtherMeetingDataListHandler', todayOtherMeetingDataList) // 会议今日直播数据
    store.commit('meeting/setManualStatusHandler', meetingData.defaultFields.manualStatus) // 直播当前状态
    store.commit('meeting/setIsStatementDialogFlagHandler', meetingData.meetingInfo.partnerCode) // 是否提示参会声明

    // 获取会议相似标签
    const labels = meetingData.tasteTags ? meetingData.tasteTags.map((item) => item.name) : ''

    return {
      meetingTemplate,
      labels,
      userInfo: userInfo?.result
    }
  },
  data() {
    return {
      // 会议埋点计时器
      analysysMeetingTime: null,
      // 会议ID
      meetingId: this.$route.query.id,
      meetingLiveDialogVisible: false
    }
  },
  head() {
    return {
      title: `${this.$store.state.meeting.meetingData.meetingInfo.meetingName}${this.$store.state.meeting.meetingData.defaultAgenda.theme ? ' - ' + this.$store.state.meeting.meetingData.defaultAgenda.theme : ''} - 脑医汇 - 神外资讯 - 神介资讯`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$store.state.meeting.meetingData.meetingInfo.description
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇，${this.$store.state.meeting.meetingData.defaultAgenda.authorNames}，${this.labels}，神外资讯，神内资讯，神介资讯，神经外科，神经内科`
        }]
    }
  },
  computed: {
    meetingStatus() {
      return this.$store.state.meeting.meetingStatus
    },
  },
  watch: {
    '$store.state.meeting.activeMeetingLiveDialogStatus'(newValue) {
      if (newValue === "F") {
        this.meetingLiveDialogVisible = false
        this.$store.commit('meeting/setActiveWorkBarNameHandler', "2")
      } else {
        if (Number(this.$store.state.meeting.activeMeetingLiveDialogContent.groupId) === Number(this.$route.query.fieldsId)) {
          this.meetingLiveDialogVisible = true;
          this.$analysys.meeting_modal({
            meeting_id: this.$route.query.id + '',
            field_id: this.$route.query.fieldsId + '',
            unionid: this.$store.state.auth.unionid + '',
            popup_id: this.$store.state.meeting.activeMeetingLiveDialogContent.popupId + '',
            onclick_url: '',
            source: "展示"
          })
        } else {
          this.$store.commit('meeting/setActiveMeetingLiveDialogStatusFlag', "F")
        }
      }

    },
    meetingStatus(newValue) {
      if (newValue === 'RECORD_VIDEO' || newValue === 'LIVE_VIDEO') {
        window.clearInterval(this.analysysMeetingTime)
        // window.clearInterval(this.analysysMeetingTime)
        // meetingRecordHandler(this) // 会议埋点
      } else {
        window.clearInterval(this.analysysMeetingTime)
      }
    },
    '$store.state.player'(newValue) {
      if (newValue) {
        newValue.on('play', () => {
          console.log("视频播放")
          window.clearInterval(this.analysysMeetingTime)
          meetingRecordHandler(this) // 会议埋点
        })
        newValue.on('pause', () => {
          console.log("暂停")
          window.clearInterval(this.analysysMeetingTime)
        })
        newValue.on('ended', () => {
          window.clearInterval(this.analysysMeetingTime)
        })
      }
    }
  },
  mounted() {
    acquisitionMeetingFun('', '', this) // 会议历史记录采集
  },
  destroyed() {
    window.clearInterval(this.analysysMeetingTime)
    // eslint-disable-next-line no-undef
  }
}
</script>
<style lang='less' scoped>
@import "meeting-detail";
</style>
