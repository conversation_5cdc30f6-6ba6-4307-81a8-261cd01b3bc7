<template>
  <div class='meeting_outside_box'>
    <!-- 顶部banner Start -->
    <div class='meeting_banner_box'>
      <div class='container-box container-banner'>
        <div>
          <div class='banner_box_text'>
            <img alt='' src='~assets/images/meeting/banner_text.png'/>
          </div>
        </div>
        <div>
          <div v-if='todayMeetingsDataList.length>0' class='live_today_box'>
            <div class='live_today_title'>
              <span class='live_span'>Live</span>
              <span class='today_span'>今日直播</span>
            </div>
            <ul class='live_today_meeting_list'>
              <li v-for='(item) in todayMeetingsDataList' :key='item.id' class='meeting_list cursor'>
                <nuxt-link :to='{ path: `/meeting/detail`,query:{id:item.id} }' target='_blank'>
                  <p class='list_title text-limit-1'>
                    {{ item.meetingName }}
                  </p>
                  <p class='list_time'>
                    {{ timeStamp.timestamp_13(item.startTime, 'h-m') }}
                    -
                    {{ timeStamp.timestamp_13(item.endTime, 'h-m') }}
                  </p>
                </nuxt-link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- End -->
    <div class='container-box meeting_contnet'>
      <el-row>
        <!-- 左侧内容 Start -->
        <el-col :lg='16' :md='16' :xs='24' class='meeting_content_left page-left'>
          <!-- 近期会议 -->
          <div v-show="recentMeetingTotal.totalCount > 0">
            <div class='meeting_content_title'>
              <p class='title'>
                <svg-icon class-name='meeting-icon' icon-class='recent'/>
                <span style='margin-left: 2px'>近期会议</span>
              </p>
              <div v-show='recentMeetingTotal.totalCount>=4' class='right_con'>
                <div :class="{'is_active':slideNum===1 }" class='left_page slide' @click="slideFun('left')">
                  <i class='el-icon-arrow-left fontSize12'></i>
                </div>
                <div :class="{'is_active':slideNum === recentMeetingTotal.totalPage}" class='right_page slide'
                     @click="slideFun('right')">
                  <i class='el-icon-arrow-right fontSize12'></i>
                </div>
              </div>
            </div>
            <ul class='meeting_classification'>
              <li
                v-for='(item, index) in recentSpecialtyDataList'
                :key='item.id'
                :class='{ is_active: latelyMeeting === index }'
                class='classifivation_list fontSize12 themeFontColor'

                @click='latelyMeetingFun(index,item.id,item.name)'
              >
                {{ item.name }}
              </li>
            </ul>
            <div class='recentMeeting'>
              <ul ref='sliderow' class='recent-meetingContentBox' style='flex-wrap: nowrap'>
                <li class='meetingContentList' v-for='item in recentMeetingDataList' :key='item.id'
                    @click="(e) => jumpMeetingDetailHandler(e,item.publishStatus,item.forthcoming)">
                  <nuxt-link
                    class='container_wrapper'
                    target='_blank'
                    :to='{ path: `/meeting/detail`,query: {id:item.id} }'>
                    <div class='meetingConImage'>
                      <img v-if="item.publishStatus === '0' && item.forthcoming === 'T'" class='img_cover'
                           src='~assets/images/meeting/herald.png' alt=''/>
                      <img
                        v-else-if='item.appMainPic || item.playerCover || item.titlePic'
                        :src='item.appMainPic?$tool.compressImg(item.appMainPic,252,140):item.playerCover?$tool.compressImg(item.playerCover,252,140):item.titlePic?$tool.compressImg(item.titlePic,252,140):null'
                        class='img_cover' alt=''/>
                      <img v-else class='img_cover' src='~assets/images/default16.png' alt=''/>
                      <live-state :state='item.meetingLiveStatus'></live-state>
                    </div>
                    <p class='meetingTitle text-limit-2'>
                      {{ item.meetingName }}
                    </p>
                    <p class='meetingInfomation text-limit-1'>
                    <span class='flex_between'>
                      <span class='time fontSize12 flex_shrink'>{{ item.meetingDateStr }}</span>
                      <span class='userName fontSize12 text-limit-1'>{{ item.province }}-{{ item.city }}</span>
                    </span>
                    </p>
                  </nuxt-link>
                </li>
              </ul>
            </div>
          </div>
          <!-- End -->

          <!-- 讲师大全 -->
          <div class='meeting_content_title'>
            <p class='title'>
              <svg-icon class-name='meeting-icon' icon-class='teacher'/>
              <span style='margin-left: 2px'>讲师大全</span>
            </p>
            <div class='right_con'>
              <div style='cursor: pointer'>
                <nuxt-link to='/meeting/lecturer'>
                  <span class='fontSize12 themeFontColor'
                        @click="$analysys.btn_click('讲师大全-查看更多','会议')">查看更多</span>
                  <i
                    class='el-icon-caret-right fontSize12 themeFontColor'
                    style=''
                  ></i>
                </nuxt-link>
              </div>
            </div>
          </div>
          <div class='lecturer_box'>
            <el-row>
              <el-col v-for='item in allAuthorDataList' :key='item.id' :lg='6' :md='6' :sm='12' :xs='12'>
                <nuxt-link :to='{path : `/meeting/lecturer/detail?userid=${item.id}`}' target='_blank'>
                  <div class='lecturer_list'>
                    <div class='img-box'>
                      <img v-if='!item.avatarAddress' class='img_cover' src='~assets/images/user.png'/>
                      <img v-else :src='$tool.compressImg(item.avatarAddress,84,84)' class='img_cover'/>
                    </div>
                    <p class='lecturer_userName'>{{ item.realName }}</p>
                    <p class='lecturer_department text-limit-2'>
                      {{ item.company }}
                    </p>
                  </div>
                </nuxt-link>
              </el-col>
            </el-row>
          </div>
          <!-- End -->

          <!-- 历史会议 -->
          <div class='meeting_content_title'>
            <p class='title'>
              <svg-icon class-name='meeting-icon' icon-class='history'/>
              <span style='margin-left: 2px'>历史会议</span>
            </p>
            <div class='right_con'>
              <div style='cursor: pointer'>
                <nuxt-link to='/meeting/history'>
                  <span class='fontSize12 themeFontColor'
                        @click="$analysys.btn_click('历史会议-查看更多','会议')">查看更多</span>
                  <i
                    class='el-icon-caret-right fontSize12 themeFontColor'
                    style=''
                  ></i>
                </nuxt-link>
              </div>
            </div>
          </div>
          <ul class='meeting_classification'>
            <li
              v-for='(item, index) in specialtyDataList'
              :key='item.id'
              :class='{ is_active: historyMeeting === index }'
              class='classifivation_list fontSize12 themeFontColor'
              @click='historyMeetingFun(index,item.id,item.name)'
            >
              {{ item.name }}
              <div v-if="item.code === '全部'" class='classifivation_list_sub_selection'>
                <div v-for='subList in historySubselectionList' :key='subList.code'
                     :class='{is_active:subList.code === subListCurrent}'
                     class='classifivation_list_sub_selection_list fontSize14'
                     @click.stop='switchSubselectionFun(subList.id,subList.code)'>{{ subList.name }}
                </div>
              </div>
            </li>
          </ul>
          <ul class='meetingContentBox'>
            <li v-for='item in historyMeetingDataList' :key='item.id'>
              <div class='meetingContentList cursor' @click='jumpMeetingDetailsFun(item.id)'>
                <div class='meetingConImage'>
                  <img v-if='item.appMainPic || item.playerCover || item.titlePic'
                       :src='item.appMainPic?$tool.compressImg(item.appMainPic,252,140):item.playerCover?$tool.compressImg(item.playerCover,252,140):item.titlePic?$tool.compressImg(item.titlePic,252,140):null'
                       class='img_cover'/>
                  <img v-else class='img_cover' src='~assets/images/default16.png'/>
                  <live-state :state='item.meetingLiveStatus'></live-state>
                </div>
                <p class='meetingTitle text-limit-2'>
                  {{ item.meetingName }}
                </p>
                <p class='meetingInfomation text-limit-1'>
                  <a class='flex_between'>
                    <span class='time fontSize12 flex_shrink'>{{ item.meetingDateStr }}</span>
                    <span class='userName fontSize12 text-limit-1'>{{ item.province }}-{{ item.city }}</span>
                  </a>
                </p>
              </div>
            </li>
          </ul>
        </el-col>
        <!-- End -->

        <!-- 右侧内容 Start -->
        <el-col :lg='8' :md='8' :xs='24' class='meeting_content_right page-right'>
          <!-- 会议日历 -->
          <div class='meeting_calendar'>
            <div class='calendar_title flex_start flex_align_center'>
              <svg-icon class-name='meeting-icon' icon-class='rili'/>
              <span class='title'>会议日历</span>
            </div>
            <div class='calendar_content'>
              <calendar v-model='MeetingDate' :first-day-of-week='7'>
                <template
                  slot='dateCell'
                  slot-scope='{date, data}'>
                  <p :class="data.isSelected ? 'is-selected' : ''" @click='selectMeetingListFun(data)'>
                    {{ data.day.split('-').slice(2).join('-') }}
                  </p>
                  <div>
                    <div v-for='(item,index) in calendarDateDataList' :key='index' class='meeting-calendarbox'
                         @click='selectMeetingListFun(data)'>
                      <div v-if="[(item.month)].indexOf(Number(data.day.split('-').slice(1)[0])) !== -1">
                        <div v-if="[(item.day)].indexOf(Number(data.day.split('-').slice(2).join('-'))) != -1">
                          <p :class="data.isSelected ? 'is-selected' : ''" class='meeting-calenp-p'>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                </template>
              </calendar>
            </div>
            <div class='calendar_meeting_box'>
              <ul v-if='meetingListByDateMonthDataList.length>0' class='calendar_content_list'>
                <li class="list_item_title">今日会议</li>
                <li v-for='(item) in meetingListByDateMonthDataList' :key='item.id' class='list_item cursor'
                    @click="(e) => jumpMeetingDetailHandler(e,item.publishStatus,item.forthcoming)">
                  <nuxt-link :to='{ path: `/meeting/detail`,query:{id:item.meetingId} }' target='_blank'>
                    <div
                      class='list_right flex_start flex_end'
                    >
                      <div class='list_left_info flex_start flex_align_center'>
                        <p v-for='(specialty,index) in item.specialityList' v-if='index<1'
                           :key='specialty.specialityId'
                           class='list_left themeFontColor text-limit-1'>
                          {{
                            item.publishStatus === '0' && item.forthcoming === 'T' ? '会议预告' : specialty.specialityName
                          }}
                        </p>
                      </div>
                      <span class='text-limit-2 fontWeight'>{{ item.meetingName }}</span>
                    </div>
                  </nuxt-link>
                </li>
              </ul>
              <ul class='calendar_content_list'>
                <li v-if="meetingListMonthDataList.length > 0" class="list_item_title">本月会议</li>
                <li v-for='(item) in meetingListMonthDataList' :key='item.id' class='list_item cursor'
                    @click="(e) => jumpMeetingDetailHandler(e,item.publishStatus,item.forthcoming)">
                  <nuxt-link :to='{ path: `/meeting/detail`,query:{id:item.meetingId} }' target='_blank'>
                    <div
                      class='list_last list_right flex_start flex_end'
                    >
                      <div class='list_left_info flex_start flex_align_center'>
                        <p v-for='(specialty,index) in item.specialityList' v-if='index<1'
                           :key='specialty.specialityId'
                           class='list_left themeFontColor text-limit-1'>
                          {{
                            item.publishStatus === '0' && item.forthcoming === 'T' ? '会议预告' : specialty.specialityName
                          }}
                        </p>
                      </div>
                      <span class='text-limit-2'>{{ item.meetingName }}</span>
                    </div>
                  </nuxt-link>
                </li>
              </ul>
            </div>
          </div>
          <!-- End -->

          <!-- 系列会议 -->
          <div class='meetings_series'>
            <div class='series_title flex_start flex_align_center'>
              <svg-icon class-name='meeting-icon' icon-class='xiliemeeting'/>
              <span class='title'>系列会议</span>
            </div>
            <ul class='series_list'>
              <li v-for='item in seriesMeetingDataList' :key='item.id' class='listitem'>
                <nuxt-link :to='{ path: `/meeting/detail`,query:{id:item.id} }' class='flex_start' target='_blank'>
                  <div class='image flex_shrink'>
                    <img
                      v-if='item.appMainPic || item.playerCover || item.titlePic'
                      :src='item.appMainPic?$tool.compressImg(item.appMainPic,80,45):item.playerCover?$tool.compressImg(item.playerCover,80,45):item.titlePic?$tool.compressImg(item.titlePic,80,45):null'
                      alt='' class='img_cover'>
                    <img v-else class='img_cover' src='~assets/images/default16.png'/>
                  </div>
                  <div class='flex_shrink' style='width: calc(100% - 90px)'>
                    <p class='title text-limit-2'>
                      {{ item.meetingName }}
                    </p>
                    <p class='listInfo flex_between'>
                  <span class='time'>
                    {{ timeStamp.timestamp_13(item.startTime, 'mm-d-h-m-') }}
                   -
                   {{ timeStamp.timestamp_13(item.endTime, 'mm-d-h-m-') }}
                  </span>
                      <span class='people'>
                        {{
                          Number(item.views) > 9999 ? (Number(item.views) / 10000).toFixed(1) + '万' : item.views
                        }}浏览</span>
                    </p>
                  </div>
                </nuxt-link>
              </li>
            </ul>
            <nuxt-link to='/meeting/series'>
              <div class='read_more themeFontColor' @click="$analysys.btn_click('系列会议-阅读更多','会议')">
                更多系列会议
              </div>
            </nuxt-link>
          </div>
          <!-- End -->
        </el-col>
        <!-- End -->
      </el-row>
    </div>
  </div>
</template>

<script>
import {Calendar} from 'element-ui'
import {
  getCalendarDate,
  getHistoryMeetings,
  getHotAuthorList,
  getMeetingListByDate,
  getRecentMeetingsPage,
  getRecentMeetingSpecialities,
  getSeriesMeetingPage,
  getSpecialities,
  getTodayMeetings
} from '@/api/meeting'
import LiveState from '@/components/LiveState/LiveState'

export default {
  head() {
    return {
      title: '脑医汇 - 医学会议直播录播 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  name: 'MeetingPage',
  components: {
    Calendar,
    LiveState
  },
  /**
   * request1 获取亚专业
   * request2 获取今日直播接口
   * request3 近期会议接口
   * request4 历史会议
   * request5 系列会议
   * request6 热门讲师列表
   * request7 会议日历
   * request8 会议日历列表
   * @param app
   * @param params
   * @param error
   * @param store
   * @param query
   * @param req
   * @returns {Promise<{request6: any, request5: any, request4: any, request3: any, request2: any, request1: any}>}
   */
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '脑医汇 - 医学会议直播录播 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台'
    const [request1, request2, request3, request4, request5, request6, request7, request8, request9, request10] = await Promise.all([
      app.$axios.$request(getSpecialities()),
      app.$axios.$request(getTodayMeetings()),
      app.$axios.$request(getRecentMeetingsPage({
        pageNo: 1,// 页码
        pageSize: 3// 每页显示条数
        , userId: store.state.auth.user.id
      })),
      app.$axios.$request(getHistoryMeetings({
        pageNo: 1,
        pageSize: 15,
        orderType: 1
      })),
      app.$axios.$request(getSeriesMeetingPage({
        pageNo: 1,
        pageSize: 10,
        orderType: 1
        , userId: store.state.auth.user.id
      })),
      app.$axios.$request(getHotAuthorList({
        limit: 4
      })),
      app.$axios.$request(getCalendarDate({
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      })),
      app.$axios.$request(getMeetingListByDate({
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        day: new Date().getDate()
      })),
      app.$axios.$request(getRecentMeetingSpecialities()),
      app.$axios.$request(getMeetingListByDate({
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      }))
    ])
    let meetingListMonthDataList = null
    meetingListMonthDataList = request10.list.filter(v => request8.list.findIndex(el => el.meetingId === v.meetingId) === -1) // 删除当日数据

    const specialtyDataList = request1.list
    const recentSpecialtyDataList = request9.list
    specialtyDataList.unshift(
      {
        'id': null,
        'name': '全部',
        'code': '全部',
        'reminderStatus': null
      })
    recentSpecialtyDataList.unshift(
      {
        'id': null,
        'name': '全部',
        'code': '全部',
        'reminderStatus': null
      })
    return {
      specialtyDataList,
      recentSpecialtyDataList,
      todayMeetingsDataList: request2.list,
      recentMeetingDataList: request3.list,
      recentMeetingTotal: request3.page,
      historyMeetingDataList: request4.list,
      seriesMeetingDataList: request5.list,
      allAuthorDataList: request6.list,
      calendarDateDataList: request7.list,
      meetingListByDateMonthDataList: request8.list,
      meetingListMonthDataList
    }
  },
  data() {
    return {
      page_title: '', // 页面标题
      banner_text: [
        '作为中华医学会神经外科学分会、',
        '中国医师协会神经外科医师分会官方合作伙伴，',
        '脑医汇每年直播超500场神经外科、神经介入学术会议/学习班,包括中华医学会神经外科学会年会、',
        '包括中华医学会神经外科学会年会',
        '世界华人神经外科学术大会、OCIN等，',
        '为神经科学医生带来良好的教育、交流及社交体验'
      ],
      banner_contact: '如需进行会议直播,请拨打400-888-2526',
      meeting_classification: [
        {name: '全部'},
        {name: '脑肿瘤'},
        {name: '脑血管'},
        {name: '介入'},
        {name: '功能'},
        {name: '脊髓脊柱'},
        {name: '创伤重症'},
        {name: '小儿'},
        {name: '神经科学'},
        {name: '其他'}
      ],
      historySubselectionList: [
        {
          'id': 1,
          'name': '全部',
          'code': '全部',
          'reminderStatus': null
        },
        {
          'id': 2,
          'name': '最热',
          'code': '最热',
          'reminderStatus': null
        }
      ],// 历史会议最新 最热
      subListCurrent: '全部',// 历史会议最新 最热
      MeetingDate: new Date(), //  会议日历
      latelyMeeting: 0, //  近期会议筛选
      historyMeeting: 0, //  历史会议筛选
      slideNum: 1, // 滑动状态
      slideMaxNum: 1,// 滑动下标最大
      specialityId: null,// 历史会议筛选下标
      subselecId: null,// 历史会议最新
      recentMeetingId: null// 近期会议id
    }
  },
  watch: {
    MeetingDate(newDate) {
      this.$axios.$request(getCalendarDate({
        year: this.MeetingDate.getFullYear(),
        month: this.MeetingDate.getMonth() + 1
      })).then(res => {
        this.calendarDateDataList = res.list
      })
    }
  },
  updated() {
    const nextCalendar = document.querySelectorAll('.el-calendar-table__row > .next')
    // console.log(nextCalendar)
    // for(let i = 0 ; i < nextCalendar.length; i++){
    //   nextCalendar[i].addEventListener("click", async () => {
    //     const [request1] = await Promise.all([
    //       this.$axios.$request(getCalendarDate({
    //         year: this.MeetingDate.getFullYear(),
    //         month: this.MeetingDate.getMonth()+1
    //       })),
    //     ])
    //     this.calendarDateDataList = request1.list;
    //   });
    // }
  },
  mounted() {
    /**
     * 会议日历最大高度
     * @type {string}
     */
    let left_page = document.querySelector('.meeting_content_left')
    const calendarBoxTitle = document.querySelector('.calendar_title') // 日历标题
    const calendarBox = document.querySelector('.calendar_content') // 日历
    const meetings_series = document.querySelector('.meetings_series') // 系列会议
    const num = (calendarBoxTitle ? calendarBoxTitle.offsetHeight : 0) + (meetings_series ? meetings_series.offsetHeight : 0) + (calendarBox ? calendarBox.offsetHeight : 0)
    left_page = left_page ? left_page.offsetHeight - num - 70 : 0  // 70是边距
    const right_calendar = document.querySelector('.calendar_meeting_box')
    right_calendar.style.maxHeight = `${left_page}px`
    this.page_title = document.title
    // 点击下一个月
    const nextBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(3)')
    nextBtn.addEventListener('click', async () => {
      const [request2, request3] = await Promise.all([
        this.$axios.$request(getMeetingListByDate({
          year: this.MeetingDate.getFullYear(),
          month: this.MeetingDate.getMonth() + 1,
          day: this.MeetingDate.getDate()
        })),
        this.$axios.$request(getMeetingListByDate({
          year: this.MeetingDate.getFullYear(),
          month: this.MeetingDate.getMonth() + 1
        }))
      ])
      this.meetingListByDateMonthDataList = request2.list
      this.meetingListMonthDataList = request3.list.filter(v => this.meetingListByDateMonthDataList.findIndex(el => el.meetingId === v.meetingId) === -1) // 删除当日数据
    })
    // 点击前一个月
    const prevBtn = document.querySelector('.el-calendar__button-group .el-button-group>button:nth-child(1)')
    prevBtn.addEventListener('click', async e => {
      const [request2, request3] = await Promise.all([
        this.$axios.$request(getMeetingListByDate({
          year: this.MeetingDate.getFullYear(),
          month: this.MeetingDate.getMonth() + 1,
          day: this.MeetingDate.getDate()
        })),
        this.$axios.$request(getMeetingListByDate({
          year: this.MeetingDate.getFullYear(),
          month: this.MeetingDate.getMonth() + 1
        }))
      ])
      this.meetingListByDateMonthDataList = request2.list
      this.meetingListMonthDataList = request3.list.filter(v => this.meetingListByDateMonthDataList.findIndex(el => el.meetingId === v.meetingId) === -1) // 删除当日数据
    })
  },
  methods: {
    jumpMeetingDetailHandler(e, publishStatus, forthcoming) {
      if (publishStatus === '0' && forthcoming === "T") {
        e.preventDefault()
        this.$toast('敬请期待～')
      }
    },
    // 历史会议筛选最新
    switchSubselectionFun(id, code) {
      this.specialtyDataList[0].name = code
      this.subListCurrent = code
      this.subselecId = id
      this.$axios.$request(getHistoryMeetings({
        specialityId: this.specialityId,
        pageNo: 1,
        pageSize: 15,
        orderType: this.subselecId
      })).then(res => {
        this.historyMeetingDataList = res.list
      })
    },
    //  筛选历史会议
    historyMeetingFun(index, id, name) {
      this.specialtyDataList[0].name = '全部'
      this.subListCurrent = '全部'
      this.subselecId = null
      this.$analysys.btn_click(name, document.title)
      this.historyMeeting = index
      this.specialityId = id
      this.$axios.$request(getHistoryMeetings({
        specialityId: this.specialityId,
        pageNo: 1,
        pageSize: 15,
        orderType: this.subselecId
      })).then(res => {
        this.historyMeetingDataList = res.list
      })
    },
    //  筛选近期会议
    latelyMeetingFun(index, id, name) {
      this.recentMeetingId = id
      this.$analysys.btn_click(name, document.title)
      this.slideNum = 1
      this.slideMaxNum = 1
      this.latelyMeeting = index
      this.$axios.$request(getRecentMeetingsPage({
        specialityId: id,// 亚专业
        pageNo: 1,// 页码
        pageSize: 3// 每页显示条数
        , userId: this.$store.state.auth.user.id
      })).then(res => {
        this.recentMeetingDataList = res.list
        this.recentMeetingTotal = res.page
        document.querySelector('.recent-meetingContentBox').style.transition = 'none'
        this.$refs.sliderow.style.transform = `translateX(-0px)`
      })

    },
    //  近期会议滑动
    slideFun(data) {
      const ulWidth = document.querySelector('.recent-meetingContentBox ')
      document.querySelector('.recent-meetingContentBox').style.transition = 'all .8s'
      switch (data) {
        case 'left':
          /**
           * 如果页数大于 1 页数-1 并且回退分页
           * 页数如果等于1  slideNum = 0
           */
          if (this.slideNum > 1) {
            this.slideNum -= 1
            this.$refs.sliderow.style.transform = `translateX(-${(ulWidth.clientWidth + 18) * (this.slideNum === 1 ? 0 : this.slideNum - 1)}px)`
          }
          break
        case 'right':
          /**
           * 如果页数 小于 数据的总页数 页数+1
           * 如果已经请求过数据了 就不需要再次请求了 所以这里给了个当前最大页数
           * 如果页数 大于最大页数就去请求数据并且加到数据中翻译  如果小于就代表已经请求过数据了 仅仅翻页就可以
           */
          if (this.slideNum < this.recentMeetingTotal.totalPage) {
            this.slideNum += 1
            if (this.slideNum > this.slideMaxNum) {
              this.$axios.$request(getRecentMeetingsPage({
                pageNo: this.slideNum,// 页码
                pageSize: 3// 每页显示条数
                , userId: this.$store.state.auth.user.id
                , specialityId: this.recentMeetingId
              })).then(res => {
                if (res && res.code === 1) {
                  this.slideMaxNum = this.slideNum
                  this.recentMeetingDataList = this.recentMeetingDataList.concat(res.list)
                  this.$refs.sliderow.style.transform = `translateX(-${(ulWidth.clientWidth + 18) * (this.slideNum - 1)}px)`
                }
              })
            } else {
              this.$refs.sliderow.style.transform = `translateX(-${(ulWidth.clientWidth + 18) * (this.slideNum - 1)}px)`
            }
          } else {
            this.$toast('没有喽~')
          }
          break
      }
    },
    // 根据日期筛选直播
    async selectMeetingListFun(data) {
      const dataList = data.day.split('-')
      await Promise.all([
        this.$axios.$request(getMeetingListByDate({
          year: dataList[0],
          month: dataList[1],
          day: dataList[2]
        })),
        this.$axios.$request(getMeetingListByDate({
          year: dataList[0],
          month: dataList[1]
        }))
      ]).then(res => {
        if (res[0] && res[0].code === 1) {
          this.meetingListByDateMonthDataList = res[0].list
        }
        if (res[1] && res[1].code === 1) {
          this.meetingListMonthDataList = res[1].list.filter(v => this.meetingListByDateMonthDataList.findIndex(el => el.meetingId === v.meetingId) === -1) // 删除当日数据
        }
      })
    },
    // 跳转会议详情页
    jumpMeetingDetailsFun(id) {
      const {href} = this.$router.resolve({path: `/meeting/detail`, query: {id: id}})
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang='less' scoped>
@import "./index";
</style>
