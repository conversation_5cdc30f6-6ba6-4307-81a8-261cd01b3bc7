.meeting_outside_box {
  .meeting_banner_box {
    padding: 40px 0;
    background-color: #11214a;
    background-position: center;
    background-image: url("~/assets/images/meeting/banner_bg.jpg");
    //background-size: 100% 100%;
    background-repeat: no-repeat;
    object-fit: cover;

    .container-banner {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: center;
    }

    .banner_box_text {
      img {
        max-width: 100%;
      }
    }

    .live_today_box {
      width: 525px;
      max-height: 345px;
      overflow-y: auto;
      border-radius: 14px;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 23px 20px 29px;
      color: white;
      box-sizing: border-box;
      float: right;

      &::-webkit-scrollbar {
        background: none;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.6);
      }

      &::-webkit-scrollbar-track {
        box-shadow: none;
      }

      .live_today_title {
        margin-bottom: 27px;

        .live_span {
          font-size: 12px;
          border-radius: 6px;
          background: #f2470a;
          padding: 3px 6px;
          margin-right: 4px;
        }

        .today_span {
          color: white;
          font-size: 18px;
        }
      }

      .live_today_meeting_list {
        color: #fffdef;

        .meeting_list {
          margin-bottom: 19px;

          &:hover .list_title {
            color: #13D5FF;
          }

          &:hover .list_title::before {
            background: #13D5FF;
          }

          a {
            color: #fffdef;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .list_title {
            font-size: 18px;
            margin-bottom: 9px;
            position: relative;
            padding-left: 15px;
            color: white;

            &::before {
              width: 3px;
              height: 3px;
              display: block;
              content: "";
              border-radius: 50%;
              background-color: white;
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
            }
          }

          .list_time {
            font-size: 12px;
            padding-left: 15px;
            color: white;
          }
        }
      }
    }
  }

  .meeting_contnet {
    padding-top: 40px;

    .meeting_content_left {

      .meeting_content_title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 20px;
          line-height: 22px;
          font-weight: bold;
        }

        .right_con {
          display: flex;
          justify-content: space-between;
          line-height: 12px;

          .slide {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            text-align: center;
            line-height: 18px;
            background-color: #69b3e2;
            color: white;
            margin-right: 10px;
            cursor: pointer;

            &:last-child {
              margin: 0;
            }
          }

          .is_active {
            background-color: rgba(0, 0, 0, 0.35);
            cursor: pointer;
          }
        }
      }

      .meeting_classification {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        margin: 15px 0 10px;

        .classifivation_list {
          padding: 6px 10px;
          margin-right: 12px;
          border-radius: 6px;
          border: 1px solid #d3e8f4;
          cursor: pointer;
          margin-bottom: 5px;
          position: relative;

          &:hover .classifivation_list_sub_selection {
            display: block;
          }

          .classifivation_list_sub_selection {
            display: none;
            z-index: 100;
            position: absolute;
            left: 0;
            top: 100%;
            padding-top: 4px;
            width: 100%;
            text-align: center;
            background: #FFFFFF;
            box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.15);
            border-radius: 4px 4px 4px 4px;

            .classifivation_list_sub_selection_list {
              line-height: 25px;
              color: #666666;
            }

            .is_active {
              background: #DBF1FF;
              color: #0581CE !important;
            }
          }
        }

        .is_active {
          background: #0581ce;
          color: #fff !important;
        }
      }

      .recentMeeting {
        overflow: hidden;

        .recent-meetingContentBox {
          display: flex;
          flex-wrap: nowrap;
          justify-content: flex-start;
          margin-bottom: 30px;
          transition: all .8s;
          .meetingContentList {
            margin-right: 18.5px;
            width: 252px;
            flex-shrink: 0;
            border-radius: 6px;
            overflow: hidden;
            background: #fbfbfb;
            transition: all .3s;

            .container_wrapper{
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
            }

            &:hover {
              box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
            }

            .meetingConImage {
              height: 140px;
              overflow: hidden;
              position: relative;

              img {
                width: 100%;
                min-height: 100%;
              }

              .meetingLable {
                border-radius: 6px;
                background: #5fcf7c;
                padding: 1px 8px;
                color: #fff;
                font-size: 10px;
                position: absolute;
                right: 5px;
                top: 5px;
              }
            }

            .meetingTitle {
              font-size: 18px;
              line-height: 24px;
              margin: 10px 10px 14px;
              font-weight: bold;
            }

            .meetingInfomation {
              margin-top: auto;
              line-height: 12px;

              span {
                color: #708aa2;
                margin: 0 10px;
              }

              margin-bottom: 14px;
            }
          }
        }

      }

      // 会议列表开始
      .meetingContentBox {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 18.5px 18.5px;
        margin-bottom: 30px;
        transition: all .8s;

        .jinqimeeting_flex {
          flex-wrap: nowrap !important;
        }


        //.col-slidebox {
        //  .mixin_desktop({
        //    min-width: 33.33333% !important;
        //    max-width: 33.33333% !important;
        //  });
        //}

        .meetingContentList {
          width: 100%;
          height: 100%;
          border-radius: 6px;
          overflow: hidden;
          background: #fbfbfb;
          display: flex;
          flex-direction: column;
          transition: all .3s;

          &:hover {
            box-shadow: 0px 2px 5px 1px rgba(0, 0, 0, 0.2);
          }

          .mixin_desktop({
            width: 100% !important;
          });

          .meetingConImage {
            height: 140px;
            overflow: hidden;
            position: relative;

            img {
              width: 100%;
              min-height: 100%;
            }

            .meetingLable {
              border-radius: 6px;
              background: #5fcf7c;
              padding: 1px 8px;
              color: #fff;
              font-size: 10px;
              position: absolute;
              right: 5px;
              top: 5px;
            }
          }

          .meetingTitle {
            font-size: 18px;
            line-height: 24px;
            margin: 10px 10px 14px;
            font-weight: bold;
          }

          .meetingInfomation {
            margin-top: auto;
            line-height: 12px;

            span {
              color: #708aa2;
              margin: 0 10px;
            }

            margin-bottom: 14px;
          }
        }
      }

      // 结束
      .lecturer_box {
        margin: 15px 0 50px;

        .el-row {
          margin: 0 -9px;
        }

        .el-col {
          padding: 0 9px;
        }

        .lecturer_list {
          text-align: center;
          border-radius: 6px;
          //   background: #fbfbfb;
          background: #fbfbfb;
          padding: 0 12px;
          height: 204px;
          overflow: hidden;

          .img-box {
            width: 84px;
            height: 84px;
            border-radius: 50%;
            overflow: hidden;
            margin: 18px auto 20px;
          }

          &:hover {
            cursor: pointer;
            background: #e6f5fd;

            .lecturer_userName,
            .lecturer_department {
              color: #0581ce;
            }
          }

          .lecturer_userName {
            font-size: 18px;
            color: #333333;
            font-weight: bold;
            margin-bottom: 12px;
          }

          .lecturer_department {
            font-size: 14px;
            color: #666666;
            margin-bottom: 20px;
          }
        }
      }
    }

    .meeting_content_right {
      border-radius: 6px;

      .meeting_calendar {
        background: #fbfbfb;

        .calendar_title {
          padding: 18px 14px;

          .icon {
            border-radius: 6px;
            background: #2299ee;
            padding: 3px;

            i {
              color: white;
            }
          }

          .title {
            margin-left: 10px;
            font-size: 18px;
            font-weight: bold;
          }
        }

        .calendar_content {
          padding: 0 14px;
          margin-bottom: 20px;
          // /deep/.el-calendar{
          //   width: 280px;
          // }
          /deep/ .el-calendar-table td {
            max-width: 40px;
            height: 40px;
          }

          /deep/ .el-calendar-table .el-calendar-day {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            padding: 0;
            text-align: center;
            line-height: 40px;
            margin: 0 auto;
            position: relative;
          }

          /deep/ .el-calendar-table td {
            border: 0;
          }

          /deep/ .el-calendar-table td.is-today .el-calendar-day {
            border-radius: 50%;
            background: #000000;
            color: white;

            .meeting-calendarbox {
              .meeting-calenp-p {
                z-index: -1;
              }
            }
          }

          /deep/ .el-calendar-table .el-calendar-day:hover {
            background: #0581CE !important;
            color: #fffdef;

            .meeting-calendarbox {
              .meeting-calenp-p {
                background: white !important;
              }
            }
          }

          /deep/ .el-calendar__header {
            position: relative;
            background: #fbfbfb;
            border-bottom: 0;
          }

          /deep/ .el-calendar__title {
            position: absolute;
            left: 50%;
            transform: translate(-50%);
            color: #333333;
          }

          /deep/ .el-calendar__button-group {
            width: 55%;
            margin: 0 auto;
          }

          /deep/ .el-button-group::after {
            content: "";
            display: none;
          }

          /deep/ .el-button-group::before {
            content: "";
            display: none;
          }

          /deep/ .el-button-group {
            display: flex;
            justify-content: space-between;
          }

          /deep/ .el-button-group
          > .el-button:not(:first-child):not(:last-child) {
            display: none;
          }

          /deep/ .el-button-group > .el-button:first-child:before {
            content: "\e60b";
          }

          /deep/ .el-button-group > .el-button:last-child:before {
            content: "\e60a";
          }

          /deep/ .el-button-group > .el-button:not(:last-child) span {
            display: none;
          }

          /deep/ .el-button-group > .el-button:not(:first-child) span {
            display: none;
          }

          /deep/ .el-button-group > .el-button {
            padding: 0;
            border: none;
            font-family: "iconfontNew";
            font-size: 19px;
            color: #888888;
            background: none;

            &:hover {
              background: none !important;
              color: #666666;
            }
          }

          /deep/ .el-calendar-table td.is-selected > .el-calendar-day {
            background: #0581CE !important;
            color: #fffdef;
            border-radius: 50%;

            .meeting-calendarbox {
              .meeting-calenp-p {
                background: white !important;
              }
            }
          }

          /deep/ .el-calendar__body {
            border-bottom: 1px solid #d8d8d8;
            padding: 0;
            background: #fbfbfb;
          }

          /deep/ .el-calendar__body > table > thead > th {
            color: #99a9ba;
            font-size: 16px;
          }

          /deep/ .el-calendar-table td.is-selected {
            border-radius: 8px;
            background: #fff;
          }

          .meeting-calendarbox {
            position: absolute;
            left: 0;
            top: 0;
            width: 40px;
            height: 40px;

            .meeting-calenp-p {
              position: absolute;
              bottom: 5px;
              left: 50%;
              transform: translateX(-50%);
              background: #0581CE !important;
              width: 4px;
              height: 4px;
              border-radius: 50%;
              padding: 0;
              text-align: center;
              line-height: 40px;
              margin: 0 auto;
              z-index: 1;
            }

            .is-selected {
              background: #0581CE !important;
              color: #fffdef;
              border-radius: 50%;
            }
          }
        }

        .calendar_meeting_box {
          overflow-y: auto;
        }

        .calendar_content_list {
          padding: 0 14px 20px;

          .list_item_title{
            font-size: 18px;
            color: #202020;
            line-height: 1.2;
            margin-bottom: 12px;
          }
          .list_item {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .list_left_info {
              height: 22px;
            }

            .list_left {
              display: inline-block;
              border-radius: 6px;
              background: #e8f1f6;
              font-size: 10px;
              height: 18px;
              text-align: center;
              padding: 0 8px;
              width: 68px;
              margin-right: 4px;
              line-height: 18px;
              flex-shrink: 0;
              box-sizing: border-box;
            }

            .list_right {
              font-weight: 400;
              flex-shrink: 0;
              color: #202020;

              &:hover span {
                color: #0581ce;
              }
            }

            .list_last {
              color: #4d687a;
              font-weight: normal !important;
            }
          }
        }
      }

      .meetings_series {
        background: #fbfbfb;
        margin-top: 20px;
        border-radius: 6px;
        overflow: hidden;

        .series_title {
          padding: 18px 14px;

          .icon {
            border-radius: 6px;
            background: #2299ee;
            padding: 3px;

            i {
              color: white;
            }
          }

          .title {
            margin-left: 10px;
            font-size: 18px;
            font-weight: bold;
          }
        }

        .series_list {
          padding: 0 14px;

          .listitem {
            margin-bottom: 16px;
            padding-bottom: 11px;
            border-bottom: 1px solid #f0f0f0;

            .image {
              width: 80px;
              height: 45px;
              border-radius: 6px;
              overflow: hidden;
              margin-right: 10px;
            }

            .title {
              font-size: 16px;
              margin-bottom: 9px;
              font-weight: 400;
              line-height: 22px;
              color: #333333;
            }

            .listInfo {
              font-size: 12px;
              color: #708aa2;

              .time {
                margin-right: 10px;
              }
            }
          }
        }

        .read_more {
          background: #e8f1f6;
          font-size: 14px;
          line-height: 48px;
          text-align: center;
          cursor: pointer;
          margin-top: 34px;
        }
      }
    }

    .mixin_desktop(
    {
      .meeting_content_left,
      .meeting_content_right {
        float: left;
      }
    }
    );
  }
}

.meeting-icon {
  width: 20px;
  height: 20px;
}
