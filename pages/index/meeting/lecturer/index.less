/deep/ .el-breadcrumb__item {
  i {
    margin: 0 !important;
    color: #0581ceff;
  }

  .el-breadcrumb__inner {
    color: #0581ceff;
    font-size: 14px;
    font-weight: normal;
  }
}

.LectureList_box {
  margin-top: 20px;

  .lecture_search_box {
    padding: 30px 0 16px;

    .search_flex {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 20px;
      align-items: center;
      user-select: none;
      flex-wrap: wrap;

      .search_label {
        font-size: 12px;
        color: #666666;
        line-height: 24px;
        margin-right: 10px;
        width: 72px;
      }

      .search_content {
        padding: 0 13px;
        font-size: 12px;
        line-height: 24px;
        cursor: pointer;
        margin-right: 10px;
      }

      .is_active {
        border-radius: 50px;
        background: #0581ce;
        color: #fff;
      }
    }

    .searchletter {
      .allsearch {
        width: auto !important;
        padding: 0 13px !important;
        font-size: 12px !important;
        line-height: 24px !important;
        cursor: pointer !important;
        margin-right: 10px !important;
      }

      .search_content {
        font-size: 14px;
        width: 24px;
        line-height: 24px;
        text-align: center;
        padding: 0;
        margin-right: 12px;
      }
    }

    .line {
      width: 100%;
      border: 1px solid #8399b03f;
      margin-bottom: 16px;
    }
  }

  .LectureListContnetList {
    .content_list_item {
      padding-bottom: 5px;
      display: flex;
      justify-content: flex-start;

      .letter_box {
        min-width: 72px;
        margin-right: 10px;
        font-size: 22px;
        font-weight: bold;
      }

      .LectureContent {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .content_item {
          width: 330px;
          margin-right: 30px;
          display: flex;
          justify-content: flex-start;
          margin-bottom: 43px;
          .mixin_desktop({
            width: 220px !important;
          });

          .left_image {
            width: 44px;
            height: 44px;
            overflow: hidden;
            border-radius: 4px;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .right_lecture_info {
            .lecture_title {
              margin-bottom: 10px;

              .Name {
                font-size: 16px;
                font-weight: bold;
              }

            }

            .lecture_department {
              color: #666666;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
