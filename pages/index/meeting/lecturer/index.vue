<!--  -->
<template>
  <div style='overflow: hidden'>
    <div class='LectureList_box container-box'>
      <!--Navigation Start-->
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/meeting/home' }">会议</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/meeting/history' }">讲师大全</el-breadcrumb-item>
      </bm-breadcrumb>
      <!--End-->
      <!-- 讲师筛选 -->
      <div class='lecture_search_box'>
        <div v-for='(item) in searchData' :key='item.name' class='search_flex'>
          <p class='search_label'>{{ item.name }}</p>
          <p
            v-for='(content) in item.content'
            :key='content.name'
            :class='{ is_active: searchContent[item.code] === content.id }'
            class='search_content'
            @click='searchContentFun(item.code, content.id,content.name )'
          >
            {{ content.name }}
          </p>
        </div>
        <div
          class='search_flex searchletter'
        >
          <p class='search_label'>{{ sortorder.name }}</p>
          <p
            v-for='(order, orderindex) in sortorder.content'
            :key='order.name'
            :class="{allsearch:order.name==='全部', is_active: searchContent.initial === order.id}"
            class='search_content'
            @click='sortorderFun(order.id,order.name)'
          >
            {{ order.name }}
          </p>
        </div>
        <div class='line'></div>
      </div>
      <!-- End -->
      <DataLoad :loading="loading" :no-more="allAuthorDataList.length === 0">
        <!-- 讲师列表 -->
        <ul class='LectureListContnetList'>
          <li
            class='content_list_item'
          >
            <ul class='LectureContent'>
              <nuxt-link v-for='authorList in allAuthorDataList' :key='authorList.id'
                         :to='{path : `/meeting/lecturer/detail?userid=${authorList.id}`}'
                         target='_blank'>
                <li class='content_item'>
                  <div class='left_image'>
                    <img v-if='!authorList.avatarAddress' class='img_cover' src='~assets/images/user.png' />
                    <img v-else :src='$tool.compressImg(authorList.avatarAddress,44,44)' class='img_cover' />
                  </div>
                  <div class='right_lecture_info'>
                    <div class='lecture_title'>
                      <span class='Name'>{{ authorList.realName }}</span>
                      <speaker-state :state='authorList.liveStatus'></speaker-state>
                    </div>
                    <div class='lecture_department'>{{ authorList.company }}</div>
                  </div>
                </li>
              </nuxt-link>
              <li class='content_item' style='margin-bottom: 0'></li>
            </ul>
          </li>
        </ul>
        <!-- End -->
        <el-pagination
          :current-page.sync='currentPage'
          :hide-on-single-page='$store.state.hideOnSinglePage'
          :layout='$store.state.layout'
          :page-size='$store.state.lecture_count'
          :pager-count='$store.state.pager_count'
          :total='lectureMeetingTotal'
          background
          small
          style='text-align: center; margin-bottom: 10px'
          @current-change='handleCurrentChange'
        >
        </el-pagination>
      </DataLoad>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import { getAllAuthorList, getSpecialities } from '@/api/meeting'
import SpeakerState from '@/components/SpeakerState/SpeakerState'
import DataLoad from "../../../../components/optimize-components/public/DataLoad/index.vue";

export default {
  head() {
    return {
      title: '讲师大全'
    }
  },
  name: 'LecturerintroducePage',
  // import引入的组件需要注入到对象中才能使用
  components: {
    DataLoad,
    SpeakerState
  },
  async asyncData({ app, params, error, store, query, req }) {
    app.head.title = '讲师大全'
    /**
     * request1 获取亚专业
     * request2 获取讲者列表
     */
    const [request1, request2] = await Promise.all([
      app.$axios.$request(getSpecialities()),
      app.$axios.$request(getAllAuthorList({
        pageNo: 1,
        pageSize: store.state.lecture_count,
        userId: store.state.auth.user.id
      }))
    ])
    const searchData = []
    const specialtyDataList = request1.list
    specialtyDataList.unshift(
      {
        'id': null,
        'name': '全部',
        'code': '全部',
        'reminderStatus': null
      })
    searchData.push(
      {
        name: '直录播状态: ',
        code: 'liveStatus',
        content: [
          { id: null, name: '全部' },
          { id: 'NS', name: '即将直播' },
          { id: 'LI', name: '正在直播' },
          { id: 'LE', name: '有新录播' }
        ]
      },
      {
        name: '讲师亚专业: ',
        code: 'specialtyId',
        content: specialtyDataList
      }
    )
    return {
      lectureMeetingTotal: request2.page.totalCount,
      allAuthorDataList: request2.list,
      searchData
    }
  },
  data() {
    // 这里存放数据
    return {
      loading:false,
      currentPage: 1,// 分页首页
      searchDataStatic: [
        {
          name: '直录播状态: ',
          id: 2,
          content: [
            { name: '全部' },
            { name: '即将直播' },
            { name: '正在直播' },
            { name: '有新录播' }
          ]
        },
        {
          name: '讲师亚专业: ',
          id: 3,
          content: [
            { name: '全部' },
            { name: '脑肿瘤' },
            { name: '脑血管' },
            { name: '介入' },
            { name: '功能' },
            { name: '脊髓脊柱' },
            { name: '创伤重症' },
            { name: '小儿' },
            { name: '神经科学' },
            { name: '其他' }
          ]
        }
      ],
      sortorder: {
        name: '字母排序: ',
        content: [
          { id: null, name: '全部' },
          { id: 'A', name: 'A' },
          { id: 'B', name: 'B' },
          { id: 'C', name: 'C' },
          { id: 'D', name: 'D' },
          { id: 'E', name: 'E' },
          { id: 'F', name: 'F' },
          { id: 'G', name: 'G' },
          { id: 'H', name: 'H' },
          { id: 'I', name: 'I' },
          { id: 'J', name: 'J' },
          { id: 'K', name: 'K' },
          { id: 'L', name: 'L' },
          { id: 'M', name: 'M' },
          { id: 'N', name: 'N' },
          { id: 'O', name: 'O' },
          { id: 'P', name: 'P' },
          { id: 'Q', name: 'Q' },
          { id: 'R', name: 'R' },
          { id: 'S', name: 'S' },
          { id: 'T', name: 'T' },
          { id: 'U', name: 'U' },
          { id: 'V', name: 'V' },
          { id: 'W', name: 'W' },
          { id: 'X', name: 'X' },
          { id: 'Y', name: 'Y' },
          { id: 'Z', name: 'Z' }
        ]
      },
      searchContent: {
        liveStatus: null,
        specialtyId: null,
        initial: null,
        pageNo: 1,
        pageSize: this.$store.state.lecture_count,
        userId: this.$store.state.auth.user.id
      }
    }
  },
  // 监听属性 类似于data概念
  computed: {
    // eslint-disable-next-line vue/return-in-computed-property
    sortordera() {
      // 字母排序
      const typeList = [] // 定义空数组，用于装载去重之后的数组，
      const userClass = {} // 定义空对象，用于数组转换成对象
      if (this.allAuthorDataList) { // 如果有值
        this.allAuthorDataList.forEach(item => {
          // 可以用indexOf()数组去重 如果检索的结果匹配到,则返回 1. 如果检索的结果没有匹配值,则返回 -1.
          if (!typeList.includes(item.initial)) {
            typeList.push(item.initial)
            userClass[item.initial] = [item]
          } else {
            userClass[item.initial].push(item)
          }
        })
      }
      return userClass
    }
  },
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    // 会议分页
    handleCurrentChange(item) {
      this.loading = true;
      this.$tool.scrollIntoView()
      this.searchContent.pageNo = item
      this.$axios.$request(getAllAuthorList(this.searchContent)).then(res => {
        this.loading = false;
        if (res && res.code === 1) {
          this.allAuthorDataList = res.list
          this.lectureMeetingTotal = res.page.totalCount
        }
      })
    },
    //  会议筛选
    searchContentFun(type, index, name) {
      this.loading = true;
      this.$analysys.btn_click(name, document.title)
      this.searchContent[type] = index
      this.searchContent.pageNo = 1
      this.currentPage = 1
      // eslint-disable-next-line no-unused-expressions
      this.$axios.$request(getAllAuthorList(this.searchContent)).then(res => {
        // eslint-disable-next-line no-undef
        setTimeout(()=>{
          this.loading = false;
        },1000)
        this.allAuthorDataList = res.list
        this.lectureMeetingTotal = res.page.totalCount
      })
    },
    //  排序方式
    sortorderFun(index, name) {
      this.loading = true;
      this.$analysys.btn_click(name, document.title)
      this.currentPage = 1
      this.searchContent.initial = index
      this.searchContent.pageNo = 1
      // eslint-disable-next-line no-unused-expressions
      this.$axios.$request(getAllAuthorList(this.searchContent)).then(res => {
        // eslint-disable-next-line no-undef
        setTimeout(()=>{
          this.loading = false;
        },1000)
        this.allAuthorDataList = res.list
        this.lectureMeetingTotal = res.page.totalCount
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import "~@/pages/index/meeting/lecturer/index.less";
</style>
