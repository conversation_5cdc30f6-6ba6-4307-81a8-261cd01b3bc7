/deep/ .el-breadcrumb__item {
  i {
    margin: 0 !important;
    color: #0581ceff;
  }

  .el-breadcrumb__inner {
    color: #0581ceff;
    font-size: 14px;
    font-weight: 500;
  }
}

.lecturer_introduce_box {
  margin-top: 20px;

  .doctor_introduce_box {
    border-radius: 6px;
    background: #f1f5f9;
    padding: 0 20px 23px 40px;
    margin: 77px 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    flex-wrap: wrap;

    .introduce_left {
      display: flex;
      justify-content: flex-start;

      .introduce_leftimgbox {
        width: 156px;
        height: 156px;
        border-radius: 50%;
        margin-top: -53px;
        margin-right: 22px;
        overflow: hidden;
      }

      .introduce_infomation {
        margin-top: 25px;

        .infomation_title {
          color: #333333;

          .Name {
            font-weight: bold;
            font-size: 24px;
          }

          .department {
            font-size: 18px;
            margin-left: 8px;
          }
        }

        .infomation_showme {
          font-size: 16px;
          color: #666666;
          margin-top: 10px;
        }
      }
    }

    .introduce_right {
      padding: 6px 8px;
      color: #fff;
      font-size: 16px;
    }
  }

  .recording_list_box {
    .recording_list {
      margin-bottom: 20px;
      border-radius: 6px;
      background: #fbfbfb;
      padding: 15px 0 15px 14px;

      .recording_list_left {
        min-width: 220px;
        width: 220px;
        height: 124px;
        border-radius: 6px;
        overflow: hidden;
        position: relative;
      }

      .recording_list_right {
        margin-left: 14px;
        width: 100%;

        .recording_list_right_title {
          margin-bottom: 12px;
        }

        .recording_list_right_time {
          a {
            color: #708aa2;
          }

          margin-bottom: 15px;

          .userName {
            margin-left: 10px;
          }
        }

        .live_list_box {
          .live_list {
            background: #f1f5f9;
            padding: 10px 18px 10px 20px;

            &:nth-child(even) {
              background: #fbfbfb;
            }

            &:hover {
              background: #fbfbfb;
              cursor: pointer;

              .live_left {
                .live_author,
                .live_title {
                  color: #0581ce;
                }
              }
            }

            .live_left {
              .live_title {
                color: #404040;
                margin-bottom: 5px;
                line-height: 24px;
              }

              .live_author {
                color: #708aa2;
              }
            }

            .live_right {
              display: flex;
              align-items: center;
              flex-shrink: 0;
              gap: 21px;
              margin-left: 10px;

              .play-name,
              .svg-icon {
                width: 21px;
                height: 21px;
              }
            }
          }

          .open_more {
            text-align: center;
            margin-top: 15px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .more_button_box {
    text-align: center;
    font-size: 14px;
    margin-top: 20px;
    position: relative;

    .loding_box {
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
    }
  }

  .hot_lecturer {
    width: 100%;
    overflow-y: auto;
    border-radius: 6px;
    background: #fbfbfb;
    padding: 0 14px;
    box-sizing: border-box;

    .hot_lecturer_title {
      padding: 18px 0;

      .line {
        width: 3px;
        height: 14px;
        margin-right: 10px;
      }

      .name {
        font-size: 20px;
      }
    }

    .lecturer_list_box {
      .list_item {
        padding-bottom: 16px;

        .img-box {
          min-width: 60px;
          max-width: 60px;
          height: 60px;
          margin-right: 11px;
          overflow: hidden;
        }

        .infomation {
          .name {
            font-size: 16px;
            color: #333333;
          }

          .department {
            font-size: 14px;
            margin-top: 6px;
            color: #666666;
          }
        }
      }
    }
  }
}
