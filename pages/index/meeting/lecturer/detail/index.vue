<!--  -->
<template>
  <div class='lecturer_introduce_box container-box'>
    <!--Navigation Start-->
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/meeting/home' }">会议</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/meeting/history' }"
      >{{ lecturerintroduceDataInfo.authorName }}的会议
      </el-breadcrumb-item
      >
    </bm-breadcrumb>
    <!--End-->

    <!-- doctor introduce -->
    <div class='doctor_introduce_box'>
      <div class='introduce_left'>
        <div class='introduce_leftimgbox'>
          <img v-if='lecturerintroduceDataInfo.avatar'
               :src='$tool.compressImg(lecturerintroduceDataInfo.avatar,156,156)' alt=''
               class='img_cover'/>
          <img v-else alt='' src='~assets/images/user.png'/>
        </div>
        <div class='introduce_infomation'>
          <p class='infomation_title'>
            <span class='Name'>{{ lecturerintroduceDataInfo.authorName }}</span>
            <span class='department'>{{ lecturerintroduceDataInfo.title }}</span>
          </p>
          <p class='infomation_showme'>{{ lecturerintroduceDataInfo.company }}
            {{ lecturerintroduceDataInfo.department }}</p>
        </div>
      </div>
      <div id='themeButton' class='introduce_right'>
        <nuxt-link
          :target='lecturerintroduceDataInfo.systemUserId ? "_blank" : "_self"'
          :to="lecturerintroduceDataInfo.systemUserId ? '/user-center?profileUserId='+lecturerintroduceDataInfo.systemUserId : $route.fullPath"
          style='color: white'>
          前往学术主页
        </nuxt-link>
      </div>
    </div>
    <!-- End -->
    <!-- 讲师内容列表 -->
    <el-row>
      <el-col :lg='16' :md='16' :xs='24'>
        <!-- 左 -->
        <div class='col-left'>
          <ul class='recording_list_box'>
            <li v-for='(item) in authorAgendaDataListComputed' :key='item.meetingId' class='recording_list flex_start'>
              <div class='recording_list_left cursor' @click='jumpMeetingDetailsFun(item.meetingId,item.fieldsId)'>
                <img v-if='item.meetingTitleImage' :src='$tool.compressImg(item.meetingTitleImage,220,124)'
                     class='img_cover'/>
                <img v-else class='img_cover' src='~assets/images/default16.png'/>
                <live-state :state='item.meetingLiveStatus'></live-state>
              </div>
              <div class='recording_list_right'>
                <p class='recording_list_right_title fontSize16 fontWeight'>
                  {{ item.meetingName }}
                </p>
                <p class='recording_list_right_time fontSize12'>
                  <a>
                    <span class='time fontSize12 flex_shrink'>{{ item.meetingDateStr }}</span>
                    <span class='userName fontSize12 flex_shrink'>{{ item.meetingAddress }}</span>
                  </a>
                </p>
                <ul class='live_list_box'>
                  <li
                    v-for='(itemList, index) in item.itemList'
                    :key='itemList.id'
                    v-show='expanMap[item.id] ? index<1000 : index<3'
                    class='live_list flex_between flex_align_center'
                    @click='jumpMeetingDetailsFun(item.meetingId,item.fieldsId,itemList.id)'
                  >
                    <div class='live_left'>
                      <p class='live_title fontSize16'>
                        {{ itemList.theme }}
                      </p>
                      <p class='live_author fontSize12'>{{
                          itemList.customName ? itemList.customName : '讲者'
                        }}：{{ itemList.authorNames }}</p>
                    </div>
                    <div class='live_right'>
                      <SummaryPopover
                        v-if="item.summaryShow === 1"
                        placement="right"
                        :meeting-name="item.meetingName"
                        :meeting-id="item.meetingId"
                        :agenda-name="itemList.theme"
                        :agenda-id="itemList.id"
                      >
                        <svg-icon className='summary_svg cursor' iconClass='summary'/>
                      </SummaryPopover>
                      <svg-icon icon-class='play' icon-name='play-name'></svg-icon>
                    </div>
                  </li>
                  <div v-if='item.itemList.length>3' class='open_more fontSize12 themeFontColor'
                       @click='expanMap[item.id]?collapse(item.id):expand(item.id)'>
                    {{ expanMap[item.id] ? '收回' : '展开更多' }}
                  </div>
                  <!--                  <div v-if="item.itemList.length>3" class="open_more fontSize12 themeFontColor" @click="">{{'收回'}}</div>-->
                </ul>
              </div>
            </li>
          </ul>
          <div :style="personalUserPageNo < personalUserPageTotal.totalPage?'color:#0581ce':'color:#666'"
               class='more_button_box'>
            <span v-if='!flag' style='color:#666'>加载中...</span>
            <span v-else>{{
                personalUserPageNo < personalUserPageTotal.totalPage ? '下拉展示更多' : '没有更多了'
              }}</span>
          </div>
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :xs='24'>
        <div class='col-right'>
          <!-- 右 -->
          <!-- 热门讲师 -->
          <div class='hot_lecturer'>
            <div class='hot_lecturer_title flex_start flex_align_center'>
              <div class='line themeBgColor'></div>
              <p class='name'>热门讲师</p>
            </div>
            <ul class='lecturer_list_box'>
              <nuxt-link v-for='item in HotAuthorListDataList' :key='item.id'
                         :to='{path : `/meeting/lecturer/detail?userid=${item.id}`}'
                         target='_blank'>
                <li class='list_item flex_start flex_align_center'>
                  <div class='img-box img_radius'>
                    <img v-if='!item.avatarAddress' class='img_cover' src='~assets/images/user.png'/>
                    <img v-else :src='$tool.compressImg(item.avatarAddress,60,60)' class='img_cover'/>
                  </div>
                  <div class='infomation'>
                    <div class='name'>{{ item.realName }}</div>
                    <p class='department fontSize16 text-limit-1'> {{ item.company }}</p>
                  </div>
                </li>
              </nuxt-link>
            </ul>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- End -->
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import {getAuthorDetail, getPersonalUserMeetings} from '@/api/speaker'
import {getHotAuthorList} from '@/api/meeting'
import LiveState from '@/components/LiveState/LiveState'
import SummaryPopover from "../../../../../opt-components/popover/SummaryPopover.vue";

export default {
  head() {
    return {
      title: this.lecturerintroduceDataInfo.authorName + '会议主页'
    }
  },
  // import引入的组件需要注入到对象中才能使用
  name: 'LecturerintroducePage',
  components: {
    SummaryPopover,
    LiveState
  },
  async asyncData({app, params, error, store, query, req}) {
    /**
     * request1 讲师详情
     * request2 热门讲师
     */
    const [request1, request2] = await Promise.all([
      app.$axios.$request(getAuthorDetail({
        authorId: query.userid
      })),
      app.$axios.$request(getHotAuthorList({
        limit: store.state.lecturelist_count
      }))
    ])
    app.head.title = request1.result.authorName + '会议主页'
    const authorAgendaList = await app.$axios.$request(getPersonalUserMeetings({
      userId: request1.result.systemUserId,
      pageNo: 1,
      pageSize: store.state.speaker_count
    }))
    return {
      lecturerintroduceDataInfo: request1.result,
      HotAuthorListDataList: request2.list,
      authorAgendaDataList: authorAgendaList.list,
      personalUserPageTotal: authorAgendaList.page
    }
  },
  computed: {
    // eslint-disable-next-line vue/return-in-computed-property
    authorAgendaDataListComputed() {
      return this.$tool.mergeToolArray(this.authorAgendaDataList, 'meetingId')
    }
  },
  data() {
    // 这里存放数据
    return {
      dataList: [],
      expanMap: {},
      personalUserPageNo: 1,// 日程分页
      flag: true,// 滚动底部开关
      openCount: 3,// 日程默认展示三条
      openFlag: false,// 展开收回
      openCurrent: null// 展开id
    }
  },
  mounted() {
    window.addEventListener('scroll', this.scrollEvent) // 滚动到底部，再加载的处理事件
  },
  updated() {

  },
  methods: {
    expand(data) {
      this.$set(this.expanMap, data, 1)
    },
    collapse(data) {
      this.$delete(this.expanMap, data)
    },
    // 日程展开收回
    openFlagFun(id) {
      this.openCurrent = id
      if (!this.openFlag) {
        this.openFlag = true
        this.openCount = 1000
      } else {
        this.openFlag = false
        this.openCount = 3
      }

    },
    //  分页加载讲者的 会议日程
    getPersonalUserMeetingsFun() {
      this.flag = false
      const lastMeetingId = this.authorAgendaDataListComputed[this.authorAgendaDataListComputed.length - 1].onlyId
      this.personalUserPageNo = this.personalUserPageNo += 1
      this.$axios.$request(getPersonalUserMeetings({
        userId: this.lecturerintroduceDataInfo.systemUserId,
        pageNo: this.personalUserPageNo,
        pageSize: this.$store.state.speaker_count,
        lastMeetingId
      })).then(res => {
        this.authorAgendaDataList = this.authorAgendaDataList.concat(res.list)
        this.flag = true
      })
    },
    // 跳转到会议详情
    jumpMeetingDetailsFun(meetingId, fieldsId, agendaId) {
      if (event.target.id === "summary_popover_btn") {
        return;
      }

      const {href} = this.$router.resolve({
        path: `/meeting/detail`,
        query: {id: meetingId, fieldsId, agendaId}
      })
      window.open(href, '_blank')
    },
    // 滚动条触发事件
    scrollEvent(e) {
      const footer = document.querySelector('.el-footer') ? document.querySelector('.el-footer').offsetHeight : 0
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      const clientHeight = document.documentElement.clientHeight
      const scrollHeight = document.documentElement.scrollHeight

      if (scrollTop + clientHeight + footer >= scrollHeight) {
        if (this.flag && this.personalUserPageNo < this.personalUserPageTotal.totalPage) {
          this.flag = false
          this.getPersonalUserMeetingsFun()
        }
        // 滚动到底部，逻辑代码
      }
      // if ((e.srcElement.scrollTop + e.srcElement.clientHeight) * 1.4 >= e.srcElement.scrollHeight) {
      //   if (this.flag && this.personalUserPageNo < this.personalUserPageTotal.totalPage) {
      //     this.flag = false
      //     this.getPersonalUserMeetingsFun()
      //   }
      // }
    }
  }
}
</script>
<style lang='less' scoped>
@import "./index";
</style>
