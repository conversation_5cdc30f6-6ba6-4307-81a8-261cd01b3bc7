<template>
  <div style='overflow: hidden'>
    <div class='history_meeting container-box'>
      <!--Navigation Start-->
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/meeting/home' }">会议</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/meeting/history' }">系列会议</el-breadcrumb-item>
      </bm-breadcrumb>
      <!--End-->
      <!-- 会议筛选 -->
      <div class='meeting_search_box'>
        <div
          v-for='(item) in searchData'
          :key='item.name'
          class='search_flex'

        >
          <p class='search_label'>{{ item.name }}</p>
          <ul :class="item.code === 'brandId' && !brandIdMore?'text-limit-1':''" class='search_content_box'>
            <div v-if="item.code === 'brandId'" :style="brandIdMore?'bottom:0':'top:0'" class='more_list cursor'
                 @click='brandIdMore = !brandIdMore'>
              {{ !brandIdMore ? '展开' : '收回' }}
            </div>
            <li
              v-for='(content, index2) in item.content'
              :key='index2'
              :class='{ is_active: searchContent[item.code] === content.id }'
              class='search_content'
              @click='searchContentFun(item.code, content.id,content.name)'
            >
              {{ content.name }}
            </li>
          </ul>
        </div>
        <div class='line'></div>
        <!--排序方式-->
        <template v-for='(content) in sortorder'>
          <div
            v-if="historyMeetingDataList.length>0"
            :key='content.name'
            class='search_flex'

          >
            <p class='search_label'>{{ content.name }}</p>
            <p
              v-for='(order, orderindex) in content.content'
              :key='orderindex'
              :class='{themeFontColor:searchContent.orderType === order.id}'

              class='search_content'
              @click='sortorderFun(order.id,order.name)'
            >
              {{ order.name }}
            </p>
          </div>
        </template>
      </div>
      <!-- End -->

      <!-- 会议列表 -->
      <ul class='meetingContentBox'>
        <div v-for='item in historyMeetingDataList' :key='item.id' :lg="{span:'4-8'}" :md='8' :sm='8' :xs='12'>
          <nuxt-link :to='{ path: `/meeting/detail` ,query:{id:item.id} }' target='_blank'>
            <li class='meetingContentList flex_column'>
              <div class='meetingConImage'>
                <img v-if='item.appMainPic || item.playerCover || item.titlePic'
                     :src='item.appMainPic?$tool.compressImg(item.appMainPic,285,160):item.playerCover?$tool.compressImg(item.playerCover,285,160):item.titlePic?$tool.compressImg(item.titlePic,285,160):null'
                     class='img_cover'/>
                <img v-else class='img_cover' src='~assets/images/default16.png'/>
                <live-state :state='item.meetingLiveStatus'></live-state>
              </div>
              <p class='meetingTitle text-limit-2'>
                {{ item.meetingName }}
              </p>
              <p class='meetingInfomation text-limit-1'>
                <a class='flex_between'>
                  <span class='time fontSize12 flex_shrink'>{{ item.meetingDateStr }}</span>
                  <span class='userName fontSize12 text-limit-1'>{{ item.province }}-{{ item.city }}</span>
                </a>
              </p>
            </li>
          </nuxt-link>
        </div>
      </ul>
      <Empty :loading='emptyFlag'
             :no-more='!emptyFlag && historyMeetingDataList ? historyMeetingDataList.length===0 : null'/>
      <!-- End -->
      <el-pagination
        v-if='!emptyFlag'
        :current-page.sync='currentPage'
        :hide-on-single-page='$store.state.hideOnSinglePage'
        :layout='$store.state.layout'
        :page-size='20'
        :pager-count='$store.state.pager_count'
        :total='historyMeetingTotal'
        background
        small
        style='text-align: center; margin-bottom: 10px'
        @current-change='handleCurrentChange'
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import {getBrandList, getSeriesMeetingPage, getSpecialities} from '@/api/meeting'
import LiveState from '@/components/LiveState/LiveState'
import Empty from '@/components/UI/Empty/Empty'

export default {
  head() {
    return {
      title: '系列会议'
    }
  },
  // import引入的组件需要注入到对象中才能使用
  components: {
    LiveState,
    Empty
  },
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '系列会议'
    /**
     * request1 获取压专业
     * request2 获取系列会议列表 默认是最新 25条  没有筛选条件 全部
     * request3 获取怕品牌
     */
    const [request1, request2, request3] = await Promise.all([
      app.$axios.$request(getSpecialities()),
      app.$axios.$request(getSeriesMeetingPage({
        pageNo: 1,
        pageSize: 20,
        orderType: 1,
        userId: store.state.auth.user.id
      })),
      app.$axios.$request(getBrandList())
    ])
    // 亚专业加全部
    const specialtyDataList = request1.list
    specialtyDataList.unshift(
      {
        'id': null,
        'name': '全部',
        'code': '全部',
        'reminderStatus': null
      })
    const searchData = []
    // 年份 月份
    const yearData = []
    const MonthData = []
    const nowDate = new Date()
    const nowDateYear = nowDate.getFullYear()
    const brandListDataList = request3.list
    const num = Number(String(nowDateYear).split('0')[1])
    const differ = num - 14 // 当年 到 14年
    for (let i = 0; i < differ + 1; i++) {
      yearData.push({
        id: nowDateYear - i,
        name: nowDateYear - i
      })
    }
    yearData.unshift(
      {
        id: null,
        name: '全部',
        code: '全部',
        reminderStatus: null
      })
    MonthData.push(
      {
        id: null,
        name: '全部',
        code: '全部',
        reminderStatus: null
      },
      {id: 1, name: '1月'},
      {id: 2, name: '2月'},
      {id: 3, name: '3月'},
      {id: 4, name: '4月'},
      {id: 5, name: '5月'},
      {id: 6, name: '6月'},
      {id: 7, name: '7月'},
      {id: 8, name: '8月'},
      {id: 9, name: '9月'},
      {id: 10, name: '10月'},
      {id: 11, name: '11月'},
      {id: 12, name: '12月'}
    )
    brandListDataList.unshift(
      {
        id: null,
        name: '全部',
        code: '全部'
      }
    )
    searchData.push(
      {
        id: null,
        name: '专业选择: ',
        code: 'specialityId',
        content: specialtyDataList
      },
      {
        name: '年份选择: ',
        code: 'year',
        content: yearData
      },
      {
        name: '月份选择: ',
        code: 'month',
        content: MonthData
      },
      {
        name: '品牌选择: ',
        code: 'brandId',
        content: brandListDataList
      }
    )
    return {
      searchData,
      historyMeetingDataList: request2.list,
      historyMeetingTotal: request2.page.totalCount
    }
  },
  data() {
    // 这里存放数据
    return {
      emptyFlag: false,// 加载开关
      brandIdMore: false,//品牌专区 展开更多开关
      currentPage: 1,// 分页
      searchDataStatic: [
        {
          name: '专业选择: ',
          id: 1,
          content: [
            {name: '全部'},
            {name: '脑肿瘤'},
            {name: '脑血管'},
            {name: '介入'},
            {name: '功能'},
            {name: '脊髓脊柱'},
            {name: '创伤重症'},
            {name: '小儿'},
            {name: '神经科学'},
            {name: '其他'}
          ]
        },
        {
          name: '年份选择: ',
          id: 2,
          content: [
            {name: '全部'},
            {name: '2022'},
            {name: '2021'},
            {name: '2020'},
            {name: '2019'},
            {name: '2018'},
            {name: '2017'}
          ]
        },
        {
          name: '月份选择: ',
          id: 3,
          content: [
            {name: '全部'},
            {name: '1月'},
            {name: '2月'},
            {name: '3月'},
            {name: '4月'},
            {name: '5月'},
            {name: '6月'},
            {name: '7月'},
            {name: '8月'},
            {name: '9月'},
            {name: '10月'},
            {name: '11月'},
            {name: '12月'}
          ]
        },
        {
          name: '品牌选择: ',
          id: 4,
          content: [
            {name: '全部'},
            {name: '美敦力'},
            {name: '史赛克'},
            {name: '强生'}
          ]
        }
      ],
      sortorder: [
        {name: '排序方式: ', content: [{id: 1, name: '最新'}, {id: 2, name: '最热'}]}
      ],
      searchContent: {
        specialityId: null,
        year: null,
        month: null,
        brandId: null,
        pageNo: this.currentPage,
        pageSize: 20,
        orderType: 1,
        userId: this.$store.state.auth.user.id
      }
    }
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    //  会议筛选
    searchContentFun(type, index, name) {
      if (!this.emptyFlag) {
        this.emptyFlag = true
        this.historyMeetingDataList = []
        this.$analysys.btn_click(String(name), document.title)
        this.currentPage = 1
        this.searchContent.pageNo = 1
        this.searchContent[type] = index
        this.searchContent.userId = this.$store.state.auth.user.id
        this.$axios.$request(getSeriesMeetingPage(this.searchContent)).then(res => {
          if (res && res.code === 1) {
            this.emptyFlag = false
            this.historyMeetingDataList = res.list
            this.historyMeetingTotal = res.page.totalCount
          }
        })
      }
    },
    // 会议分页
    handleCurrentChange(item) {
      this.$tool.scrollIntoView()
      this.searchContent.pageNo = item
      this.searchContent.userId = this.$store.state.auth.user.id
      this.$axios.$request(getSeriesMeetingPage(this.searchContent)).then(res => {
        if (res && res.code === 1) {
          this.historyMeetingDataList = res.list
          this.historyMeetingTotal = res.page.totalCount
        }
      })
    },
    //  排序方式
    sortorderFun(index, name) {
      this.$analysys.btn_click(String(name), document.title)
      this.currentPage = 1
      this.searchContent.pageNo = 1
      this.searchContent.orderType = index
      this.searchContent.userId = this.$store.state.auth.user.id
      this.$axios.$request(getSeriesMeetingPage(this.searchContent)).then(res => {
        if (res && res.code === 1) {
          this.historyMeetingDataList = res.list
          this.historyMeetingTotal = res.page.totalCount
        }
      })
    }
  }
}
</script>
<style lang='less' scoped>
@import "~@/pages/index/meeting/history/index.less";
</style>
