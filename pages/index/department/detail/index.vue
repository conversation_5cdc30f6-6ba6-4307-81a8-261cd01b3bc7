<template>
  <div style="background: rgba(248,248,248,1)">
    <div class='department__wrapper container-box'>
      <DepartmentHead/>
      <PageContainer top='0'>
        <section slot='page-left' style="background: white;padding-bottom: 20px" class="page_left_section">
          <Tab :tab-list='tabList' @changeTabHandler='changeTabHandler' :active-id="activeTab"/>
          <DepartmentIntroduction v-if='activeTab === 0'/>
          <CertifiedMember v-if='activeTab === 1'/>
          <DepartmentDynamics v-if='activeTab === 2'/>
          <RelatedDepartments v-if='activeTab === 3' :data-list="relatedDepartmentDataList"/>
        </section>
        <section slot='page-right' class='department__wrapper__right'>
          <NotCertifiedTips v-if='departmentData.isAuth !== "1" && $store.state.auth.token'/>
          <SettleDepartment
            v-if="departmentData.cmsDepartment && departmentData.cmsDepartment.hospitalGroup && departmentData.hospitalGroupExplain"/>
          <ErrorFeedback/>
          <Confirm
            padding='36px 34px 40px 34px'
            :close-button-flag='false'
            :button-flag='false'
            :visible='shareTips'
            @cancelFn='cancelFn'>
            <template #content>
              <div class='content___'>
                <p class='title'>科室邀请</p>
                <div class='info'>
                  <p style='margin-bottom: 6px'> Hi，您收到了一条科室邀请</p>
                  <p>您的同事{{ $route.query.shareName }}医生给您分享了{{
                      departmentData.hospitalGroupName
                    }}的{{ departmentData.cmsDepartment.name }}科室，快来一起加入吧</p>
                </div>
                <div class='join_button' @click='joinHandler'>我要加入</div>
              </div>
            </template>
          </Confirm>
        </section>
      </PageContainer>
    </div>
  </div>
</template>

<script>
import Confirm from '../../../../components/optimize-components/UI/Confirm/index.vue'
import {
  getAssociateDepartment,
  getDepartment,
  getDepartmentIntroduction,
  getExistUserForDepartment
} from '../../../../api/department'
import DepartmentHead
  from '@/components/optimize-components/page-components/department/DepartmentHead/index.vue'
import PageContainer from '@/components/optimize-components/UI/PageContainer/PageContainer.vue'
import SettleDepartment
  from '@/components/optimize-components/page-components/department/SettleDepartment/index.vue'
import ErrorFeedback
  from '@/components/optimize-components/page-components/department/ErrorFeedback/index.vue'
import NotCertifiedTips
  from '@/components/optimize-components/page-components/department/NotCertifiedTips/index.vue'
import CertifiedMember
  from '@/components/optimize-components/page-components/department/CertifiedMember/index.vue'
import DepartmentDynamics
  from '@/components/optimize-components/page-components/department/DepartmentDynamics/index.vue'
import DepartmentIntroduction
  from '@/components/optimize-components/page-components/department/DepartmentIntroduction/index.vue'
import Tab from '@/components/optimize-components/Tab/index.vue'
import RelatedDepartments
  from "../../../../components/optimize-components/page-components/department/RelatedDepartments/index.vue";

export default {
  name: 'DepartmentDetail',
  components: {
    RelatedDepartments,
    DepartmentIntroduction,
    DepartmentDynamics,
    CertifiedMember,
    NotCertifiedTips,
    ErrorFeedback,
    SettleDepartment,
    PageContainer,
    DepartmentHead,
    Tab,
    Confirm
  },
  async asyncData({app, params, error, store, query, req, redirect, route}) {
    const token = store.state.auth.token

    const [departmentData, departmentIntroduction, request3] = await Promise.all([
      app.$axios.$request(getDepartment({
        departmentId: query.departmentId,
        userId: store.state.auth.user.id
      })),
      app.$axios.$request(getDepartmentIntroduction({
        departmentId: query.departmentId
      })),
      app.$axios.$request(getAssociateDepartment({
        departmentId: query.departmentId
      }))
    ])

    const isExist = token ?
      await app.$axios.$request(getExistUserForDepartment({
        departmentId: query.departmentId
      }))
      :
      {
        result: {
          realName: '',
          isDoctor: true,
          isExist: 'false'
        }
      }

    store.commit('department/setDepartmentData', departmentData.result)
    store.commit('department/setDepartmentIntroduction', departmentIntroduction.result)
    store.commit('department/setIsExist', isExist.result.isExist === 'true')
    store.commit('department/setExistUserForDepartment', isExist.result)

    const tabList = [
      {id: 0, tabName: '科室简介', isEnable: true},
      {id: 1, tabName: '认证成员', isEnable: true},
      {id: 2, tabName: '动态', isEnable: true},
      {id: 3, tabName: '关联科室', isEnable: request3.list.length > 0}
    ]

    return {
      isExist: isExist.result.isExist,
      activeTab: request3.list.length > 0 ? 3 : 2,
      departmentData: departmentData.result,
      departmentIntroduction: departmentIntroduction.result,
      relatedDepartmentDataList: request3.list,
      tabList
    }
  },
  data() {
    return {
      shareTips: false,
    }
  },
  head() {
    return {
      title: `${this.departmentData.hospitalGroupName} - ${this.departmentData.cmsDepartment.name} - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.departmentData.cmsDepartment.introduction}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `${this.departmentData.hospitalGroupName} , ${this.departmentData.hospitalGroupExplain} , 脑医汇`
        }
      ]
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.auth.user
    }
  },
  watch: {
    '$store.state.auth.token'(newValue) {
      if (!this.$store.state.auth.token) {
        window.location.reload()
      }
    }
  },
  mounted() {
    if (this.$route.query.shareName && this.isExist === 'false' && this.departmentData.identity === 1) {
      this.shareTips = true
    } else if (this.$route.query.shareName && this.isExist === 'true') {
      this.$toast('您已加入该科室')
      this.$router.push({path: `/department/detail?departmentId=${this.$route.query.departmentId}`})
    } else if (this.$route.query.shareName && this.departmentData.identity !== 1) {
      this.$toast('医务工作者才可加入科室')
      this.$router.push({path: `/department/detail?departmentId=${this.$route.query.departmentId}`})
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-23 16:58
     * @description: 切换tab
     * ------------------------------------------------------------------------------
     */
    changeTabHandler(id, callback) {
      this.activeTab = id
      callback(true)
    },
    cancelFn(flag) {
      this.$router.push({path: `/department/detail?departmentId=${this.$route.query.departmentId}`})
      this.shareTips = flag
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-01 15:45
     * @description: 加入按钮
     * ------------------------------------------------------------------------------
     */
    joinHandler() {
      this.$store.commit('department/setCertificationFlag', true)
      this.shareTips = false
    }
  }
}
</script>

<style scoped lang='less'>
.content___ {
  .title {
    font-size: 16px;
    line-height: 22px;
    color: #333333;
    text-align: center;
    margin-bottom: 10px;
    font-weight: 600
  }

  .info {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #404040;
    margin-bottom: 30px;
  }

  .join_button {
    width: 120px;
    height: 38px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #0581CE;
    border-radius: 6px;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
    margin: 0 auto;
  }
}

.department__wrapper {
  padding: 24px 0 0px;

  .page_left_section {
    /deep/ .tabs-container {
      background: white;
    }
  }

  .department__wrapper__right {
    display: flex;
    flex-flow: column;
    grid-gap: 20px 0;
  }
}
</style>
