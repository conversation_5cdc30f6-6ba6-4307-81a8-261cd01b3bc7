<template>
  <div class='label_page_wrapper container-box'>
    <div class='exhibition__banner'>
      <div v-if='bannerList.length===0' class='banner style_'>
        {{ labelData.fullName }}
      </div>
      <div class='first style_'></div>
      <div class='second style_'></div>
      <div class='third style_'></div>
      <div class='swiper-container banner_container'>
        <div
          ref='banner_container_wrapper'
          class='swiper-wrapper banner_container_wrapper'>
          <div
            v-for='(item,index) in bannerList'
            :key='index'
            class='swiper-slide banner_container_wrapper_slide'>
            <img class='img_cover' :src='$tool.compressImg(item,1090,360)' alt=''>
          </div>

        </div>
        <div v-if='bannerList.length>0' class='swiper-button-prev hide'>
          <svg-icon icon-class='left_' class-name='icons' />
        </div><!--左箭头。如果放置在swiper外面，需要自定义样式。-->
        <div v-if='bannerList.length>0' class='swiper-button-next hide'>
          <svg-icon icon-class='right__' class-name='icons' />
        </div><!--右箭头。如果放置在swiper外面，需要自定义样式。-->
      </div>
    </div>
    <div class='label_describe'>
      <div class='tips_name'>
          <span>
            {{ labelData.fullName }}
          </span>
      </div>
      <div
        :style='seeMoreFlag ? {height:"auto"} :{maxHeight:"130px",overflow:"hidden"}'
        class='content'
        ref='content'
        v-html='labelData.detail'></div>
      <div
        v-show='seeMore'
        class='see_more'
        :class='!seeMoreFlag ? "shadow_s" : ""'
        @click='contractHandler()'>
        <span>{{ seeMoreFlag ? '收回' : '查看更多' }}</span>
        <svg-icon ref='down_1' icon-class='down_' class-name='icons' />
      </div>
    </div>
    <div v-for='item in labelData.tabList' :key='item.id' class='wrapper'>
      <div class='tips_name'>
          <span class='tips_before'>
            {{ item.title }}
          </span>
      </div>
      <div class='list_wrapper'>
        <DepartmentRosterItem
          v-for='itemChildren in item.departmentList'
          :id='itemChildren.id'
          :key='itemChildren.id'
          :company='itemChildren.hospitalGroup.name'
          :province='itemChildren.province'
          :city='itemChildren.city'
          :img='itemChildren.iconUrl'
        />
      </div>
    </div>
  </div>
</template>

<script>
import Swiper from 'swiper'

import DepartmentRosterItem
  from '../../../../components/optimize-components/page-components/department/DepartmentRosterItem/index.vue'
import { getHonorLabelById } from '../../../../api/department'

export default {
  name: 'LabelPageComponent',
  components: { DepartmentRosterItem },
  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    const [labelData] = await Promise.all([
      app.$axios.$request(getHonorLabelById({
        honorLabelId: params.id
      }))
    ])


    return {
      labelData: labelData.result,
      bannerList: labelData.result.bannerStringList
    }
  },
  data() {
    return {
      swiper: '',
      seeMore: false,
      seeMoreFlag: false

    }
  },
  head() {
    return {
      title: this.labelData.fullName,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  mounted() {
    if ((this.$refs.content ? this.$refs.content.offsetHeight : 0) >= 130) {
      this.seeMore = true
    }

    const count = this.bannerList.length
    if (count > 1) {
      this.swiper = new Swiper('.swiper-container', {
        // 可选选项，自动滑动
        autoplay: true,
        loop: true,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      })
      // 鼠标移出隐藏按钮，移入显示按钮
      this.swiper.el.onmouseover = function() {
        this.swiper.navigation.$nextEl.removeClass('hide')
        this.swiper.navigation.$prevEl.removeClass('hide')
      }
      this.swiper.el.onmouseout = function() {
        this.swiper.navigation.$nextEl.addClass('hide')
        this.swiper.navigation.$prevEl.addClass('hide')
      }
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-27 15:33
     * @description:
     * ------------------------------------------------------------------------------
     */
    contractHandler() {
      this.$refs.down_1.$el.style.transform = 'rotate(180deg)'
      this.seeMoreFlag = !this.seeMoreFlag
      if (!this.seeMoreFlag) {
        this.$refs.down_1.$el.style.transform = 'rotate(0deg)'
        this.$tool.scrollIntoTop()
      }
    }
  }
}
</script>

<style scoped lang='less'>
@import 'swiper/css/swiper.min.css';

.label_page_wrapper {
  margin-top: 20px;

  .exhibition__banner {
    overflow: hidden;
    margin-bottom: 47px;
    position: relative;

    .style_ {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    .banner {
      width: 90%;
      height: 360px;
      border-radius: 6px;
      background: rgba(5, 129, 206, 0.06);
      font-size: 46px;
      line-height: 61px;
      color: #0581CE;
      font-weight: 700;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 90px;
      background-blend-mode: soft-light;
      background: url("assets/images/department/label_bg.jpg") center center;
      z-index: 10;
    }

    .first {
      width: 94%;
      height: 324px;
      background: rgba(5, 129, 206, 0.15);
      border-radius: 6px;
      z-index: 9;
    }

    .second {
      width: 97%;
      height: 288px;
      background: rgba(5, 129, 206, 0.1);
      border-radius: 6px;
      z-index: 8;
    }

    .third {
      width: 100%;
      height: 252px;
      background: rgba(5, 129, 206, 0.05);
      border-radius: 6px;
      z-index: 7;
    }

    .banner_container {
      width: 90%;
      height: 360px;
      border-radius: 6px;
      overflow: hidden;
      z-index: 11;

      .hide {
        opacity: 0 !important;
      }

      .swiper-button-prev {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        color: #ffffff;
        transition: all .3s;
        left: 20px !important;

        &::after {
          display: none;
        }

        &:hover {
          background: #0581CE;
        }
      }

      .hide {

      }

      .swiper-button-next {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        color: #ffffff;
        transition: all .3s;
        right: 20px !important;

        &::after {
          display: none;
        }

        &:hover {
          background: #0581CE;
        }
      }

      .banner_container_wrapper {
        .banner_container_wrapper_slide {
          border-radius: 6px;
          overflow: hidden;
        }
      }
    }
  }

  .tips_name {
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    color: #202020;
    text-align: center;
    margin-bottom: 20px;

    .tips_before {
      position: relative;

      &::before {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 110%;
        height: 8px;
        background: rgba(5, 129, 206, 0.15);
        transform: translateX(-50%);
      }
    }
  }

  .label_describe {
    margin-bottom: 30px;

    .see_more {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      color: #0581CE;
      cursor: pointer;
      position: relative;
      margin-bottom: 35px;


      span {
        margin-right: 4px;
      }

      .icons {
        width: 12px;
        height: 12px;
      }
    }

    .shadow_s {
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 40px;
        box-shadow: 0px -35px 41px 27px #ffffff;
      }
    }

    .content {
      font-weight: 400;
      font-size: 18px;
      line-height: 24px;
      text-indent: 36px;
      color: #333333;
    }
  }

  .wrapper {
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 10px;
    }

    .list_wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: 20px 20px;
    }
  }
}
</style>
