<template>
  <PageContainer>
    <section slot='page-left'>
      <div style='min-height: 70vh'>
        <Tab :tab-list='tabsData' @changeTabHandler='changeTabHandler'>
          <template v-if='searchCode==="A"' #right>
            <FilterInformation @filterCitiesHandler='filterCitiesHandler'/>
          </template>
        </Tab>
        <Empty :loading='loading' :no-more='!loading && departmentDynamic.list.length===0' height='70vh'/>
        <ArticleList :list='departmentDynamic.list'/>
        <el-pagination
          v-show='!loading'
          :current-page.sync='currentPage'
          :hide-on-single-page='$store.state.hideOnSinglePage'
          :layout='$store.state.layout'
          :page-size='$store.state.article_count'
          :pager-count='$store.state.pager_count'
          :total='departmentDynamic.page.totalCount'
          background
          small
          style='text-align: center'
          @current-change='handleCurrentChange'
        >
        </el-pagination>
      </div>
    </section>


    <section slot='page-right' class='department__right'>
      <MyAttention v-if='$store.state.auth.token && isSubscribe' :key='partialRefresh' icons='follow__'/>
      <AllDepartments :list='allDepartment.list' icons='team_'/>
    </section>
  </PageContainer>
</template>

<script>
import Empty from '../../../../components/optimize-components/UI/Empty/index.vue'
import FilterInformation from '../../../../components/optimize-components/FilterInformation/index.vue'
import ArticleList from '../../../../components/optimize-components/page-components/department/ArticleList/index.vue'
import Tab from '../../../../components/optimize-components/Tab/index.vue'
import MyAttention from '../../../../components/optimize-components/page-components/department/MyAttention/index.vue'
import AllDepartments
  from '../../../../components/optimize-components/page-components/department/AllDepartments/index.vue'
import {
  getDepartmentDynamic,
  getFollowAndJoinDepartmentInfo,
  getSidebarAllDepartment
} from '../../../../api/department'
import PageContainer from '@/components/page/PageContainer/PageContainer'

export default {
  name: 'DepartmentPage',
  components: {
    FilterInformation,
    AllDepartments,
    MyAttention,
    PageContainer,
    Tab,
    ArticleList,
    Empty
  },
  async asyncData({app, params, error, store, query, req}) {
    const token = store.state.auth.token
    const followAndJoinDepartmentInfo = token ? await app.$axios.$request(getFollowAndJoinDepartmentInfo()) : {
      result: {
        isJoin: false,
        isSubscribe: false
      }
    }

    const {isJoin, isSubscribe} = followAndJoinDepartmentInfo.result
    const tabsData = [
      {id: 'F', tabName: '关注的科室', isEnable: isSubscribe},
      {id: 'M', tabName: '我的科室', isEnable: isJoin},
      {id: 'A', tabName: '全部科室', isEnable: true}
    ]


    const [departmentDynamic, allDepartment] = await Promise.all([
      app.$axios.$request(getDepartmentDynamic({
        userId: store.state.auth.user.id,
        searchCode: isSubscribe ? 'F' : isJoin ? 'M' : 'A',
        cityStr: '',
        pageNo: 1,
        pageSize: store.state.article_count
      })),
      app.$axios.$request(getSidebarAllDepartment({
        userId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 10
      }))
    ])


    return {
      isSubscribe,
      isJoin,
      followAndJoinDepartmentInfo,
      tabsData,
      departmentDynamic,
      allDepartment
    }
  },
  data() {
    return {
      searchCode: this.isSubscribe ? 'F' : this.isJoin ? 'M' : 'A',
      cityStr: '',
      currentPage: 1,
      loading: false,
      filterProvinceFlag: false
    }
  },
  head() {
    return {
      title: '脑医汇 - 科室动态 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  computed: {
    partialRefresh() {
      return this.$store.state.department.partialRefresh
    }
  },
  watch: {
    '$store.state.auth.token'(value) {
      if (!this.$store.state.auth.token) {
        this.tabsData[0].isEnable = false
        this.tabsData[1].isEnable = false
        this.currentPage = 1
        this.searchCode = 'A'
        this.loading = true
        this.departmentDynamic.list = []
        this.getDepartmentDynamicHandler({
          searchCode: 'A',
          cityStr: this.cityStr,
          pageNo: 1
        })
      }
    },
    isSubscribe(newValue) {
      this.searchCode = newValue ? 'F' : this.isJoin ? 'M' : 'A'
    }
  },
  watchQuery: ['digit'],

  mounted() {
    this.searchCode = this.isSubscribe ? 'F' : this.isJoin ? 'M' : 'A'
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-15 11:41
     * @description: 科室信息流
     * ------------------------------------------------------------------------------
     */
    getDepartmentDynamicHandler({
                                  searchCode, cityStr, pageNo, callbackFn = () => {
      }
                                }) {
      this.$axios.$request(getDepartmentDynamic({
        userId: this.$store.state.auth.user.id,
        searchCode,
        cityStr,
        pageNo,
        pageSize: this.$store.state.article_count
      })).then(response => {
        if (response && response.code === 1) {
          this.loading = false
          this.$tool.scrollIntoTop()
          this.departmentDynamic = response
          callbackFn(true)
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-15 11:40
     * @description: 切换TAB
     * ------------------------------------------------------------------------------
     */
    changeTabHandler(id, callback) {
      this.loading = true
      this.departmentDynamic.list = []
      this.searchCode = id
      this.currentPage = 1

      if (id === 'A' && this.$route.query.dyncitys) {
        // eslint-disable-next-line node/no-callback-literal
        callback(true)
        return false;
      }

      this.getDepartmentDynamicHandler({
        searchCode: id,
        cityStr: this.cityStr,
        pageNo: 1,
        callbackFn: callback
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-21 11:52
     * @description: 切换城市
     * ------------------------------------------------------------------------------
     */
    filterCitiesHandler(name) {
      this.loading = true
      this.departmentDynamic.list = []
      this.cityStr = name
      this.currentPage = 1
      this.getDepartmentDynamicHandler({
        searchCode: this.searchCode,
        cityStr: name,
        pageNo: 1
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-13 11:51
     * @description: 分页
     * ------------------------------------------------------------------------------
     */
    handleCurrentChange(current) {
      this.currentPage = current
      this.getDepartmentDynamicHandler(
        {
          searchCode: this.searchCode,
          cityStr: this.cityStr,
          pageNo: current
        }
      )
    }
  }
}
</script>

<style scoped>
.department__right {
  display: flex;
  flex-flow: column;
  grid-gap: 20px 0;
}
</style>
