<template>
  <div class='department_home_wrapper container-box'>
    <AllDepartmentTab
      :active-category='category'
      @searchGroupNameHandler='searchGroupNameHandler'
      @changeDepartmentTypeHandler='changeDepartmentTypeHandler'
      @changeDepartmentHandler='changeDepartmentHandler'
    />

    <RecommendedDepartment
      v-show='activeTab !== 3'
      v-if='recommendDepartmentList.result.data.length>0'
      :list='recommendDepartmentList.result.data'
      @changeRecommendedFollowHandler='changeRecommendedFollowHandler'
    />
    <AllDepartmentsList
      v-show='activeTab !== 3'
      :list='allDepartmentList.list'
      :page='allDepartmentList.page'
      @filterCitiesHandler='filterCitiesHandler'
      @changeAllDepartFollowHandler='changeAllDepartFollowHandler'
    />
    <SearchDepartmentsList
      v-if='activeTab === 3'
      :list='searchData.list' />
  </div>
</template>
<script>
import RecommendedDepartment
  from '../../../../components/optimize-components/page-components/department/RecommendedDepartment/index.vue'
import AllDepartmentsList
  from '../../../../components/optimize-components/page-components/department/AllDepartmentsList/index.vue'
import AllDepartmentTab
  from '../../../../components/optimize-components/page-components/department/AllDepartmentTab/index.vue'
import {
  getAllDepartment,
  getDepartmentByHospitalGroupName,
  getDepartmentList,
  getUserInterestedSubspecialtyList
} from '../../../../api/department'
import SearchDepartmentsList
  from '../../../../components/optimize-components/page-components/department/SearchDepartmentsList/index.vue'

export default {
  name: 'DepartmentHome',
  components: { SearchDepartmentsList, AllDepartmentTab, AllDepartmentsList, RecommendedDepartment },
  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const interestedSubspecialtyList = await app.$axios.$request(getUserInterestedSubspecialtyList())

    let category = 'SW'

    if (interestedSubspecialtyList.list) {

      if (interestedSubspecialtyList.list.length === 1 && interestedSubspecialtyList.list[0].name === '神经内科') {
        category = 'SN'
      }

      if (interestedSubspecialtyList.list.length === 1 && interestedSubspecialtyList.list[0].name === '神经介入') {
        category = 'SJ'
      }

      if (interestedSubspecialtyList.list.length === 2) {

        const filterResult = interestedSubspecialtyList.list.filter(item => item.name === '神经介入' || item.name === '脑血管外科')
        if (filterResult.length === 2) {
          category = 'SJ'
        }
      }
    }

    if(query.recommendFilterCode){
      category = query.recommendFilterCode.toUpperCase()
    }




    const [recommendDepartmentList, allDepartmentList] = await Promise.all([
      app.$axios.$request(getDepartmentList({
        userId: store.state.auth.user.id,
        // 科室所属类目： SW：神经外科，SN：神经内科，SJ：神经介入
        category
      })),
      app.$axios.$request(getAllDepartment({
        userId: store.state.auth.user.id,
        cityStr: '',
        // 科室所属类目： SW：神经外科，SN：神经内科，SJ：神经介入
        category,
        pageNo: 1,
        pageSize: 35
      }))
    ])

    return {
      category,
      recommendDepartmentList,
      allDepartmentList,
      interestedSubspecialtyList
    }
  },
  data() {
    return {
      cityStr: null,
      searchName: '',
      searchData: {}
    }
  },
  head() {
    return {
      title: '全部科室',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: '脑医汇，医学，医学资讯，神外资讯，神内资讯，神介资讯，神经外科，神经内科，医学会议，临床指南，在线课程，临床评分工具，招聘培训，学术年会，中华医学会，ocin，CNS'
        }
      ]
    }
  },
  computed: {
    activeTab() {
      return this.$store.state.department.departmentHome.activeTab
    }
  },
  mounted() {

  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-03 11:05
     * @description: 推荐科室关注联动全部科室
     * ------------------------------------------------------------------------------
     */
    changeRecommendedFollowHandler(result) {
      this.allDepartmentList.list.forEach((item, index) => {
        if (item.cmsDepartment.id === result.id) {
          this.$set(this.allDepartmentList.list, index, { ...item, isSubscribe: result.isSubscribe })
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-03 13:28
     * @description: 全部科室关注联动推荐科室
     * ------------------------------------------------------------------------------
     */
    changeAllDepartFollowHandler(result) {
      this.recommendDepartmentList.result.data.forEach((item, index) => {
        if (item.cmsDepartment.id === result.id) {

          this.$set(this.recommendDepartmentList.result.data, index, { ...item, isSubscribe: result.isSubscribe })
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 13:47
     * @description: 搜索
     * ------------------------------------------------------------------------------
     */
    searchGroupNameHandler(name, callback) {
      if (name) {
        this.$store.commit('department/setActiveTab', 3)
        this.searchName = name
        this.$axios.$request(getDepartmentByHospitalGroupName({
          name
        })).then(response => {
          if (response.code === 1) {
            callback(true)
            this.$analysys.search(
              response.list.length > 0,
              response.list.length,
              false,
              name,
              false,
              '科室主页综合'
            )
            this.searchData = response
            this.searchData.list.forEach(item => {
              item.hospitalGroup.name = item.hospitalGroup.name.split(name).join('<span style=\'color:red;\'>' + name + '</span>')
            })
          }
        })
      } else {
        callback(false)
        this.$store.commit('department/setActiveTab', 1)
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 11:21
     * @description: 获取推荐科室
     * ------------------------------------------------------------------------------
     */
    getDepartmentListHandler({
                               category, callbackFn = () => {
      }
                             }) {
      this.$axios.$request(getDepartmentList({
        userId: this.$store.state.auth.user.id,
        category
      })).then(response => {
        if (response.code === 1) {
          this.recommendDepartmentList = response
          callbackFn()
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 11:28
     * @description: 获取全部科室
     * ------------------------------------------------------------------------------
     */
    getAllDepartmentHandler({
                              category, cityStr, callbackFn = () => {
      }
                            }) {
      this.$axios.$request(getAllDepartment({
        userId: this.$store.state.auth.user.id,
        cityStr,
        category,
        pageNo: 1,
        pageSize: 35
      })).then(response => {
        if (response.code === 1) {
          this.allDepartmentList = response
          callbackFn()
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 11:10
     * @description: 切换选项
     * ------------------------------------------------------------------------------
     */
    changeDepartmentHandler(id, callback) {
      const callbackFn = () => {
        callback(true)
      }
      if (id === 1) {
        this.getDepartmentListHandler({ category: this.category, callbackFn })
      } else {
        this.getAllDepartmentHandler({ category: this.category, cityStr: this.cityStr, callbackFn })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 11:20
     * @description: 切换科室类型
     * ------------------------------------------------------------------------------
     */
    changeDepartmentTypeHandler(id, callback) {
      const callbackFn = () => {
        this.category = id
        callback(true)
      }
      if (this.activeTab === 1) {
        this.getDepartmentListHandler({ category: id, callbackFn })
        this.getAllDepartmentHandler({ category: id, cityStr: this.cityStr, callbackFn })
      } else if (this.activeTab === 3) {
        if (this.activeTab === 3) {
          this.$store.commit('department/setActiveTab', 1)
        }
        this.getDepartmentListHandler({ category: id, callbackFn })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-02-22 13:12
     * @description: 切换城市
     * ------------------------------------------------------------------------------
     */
    filterCitiesHandler(name) {
      this.cityStr = name
      this.getAllDepartmentHandler({ category: this.category, cityStr: name })
    }
  }
}
</script>

<style scoped lang='less'>
.department_home_wrapper {
  padding: 24px 0 32px;
}
</style>
