<template>
  <div class="home_page_wrapper">
    <Container>
      <HomeBanner v-if="bannerData.list.length>0" :banner-list="bannerData.list || []"/>
    </Container>
    <div :style="bannerData.list.length>0 ? {marginTop: '24px'} : {}">
      <ContainerPage>
        <template #left>
          <div class="row_spacing">
            <NewInfoModule :list="newInfoData.list || []"/>
            <PopularMeetings v-if="newestHotMeeting && newestHotMeeting.length>0" :list="newestHotMeeting.list || []"/>
          </div>
          <div class="row_banner">
            <MiddleBanner
              v-if="middleBannerList.length>0"
              :list="middleBannerList"/>
          </div>
          <div class="row_spacing">
            <SelectedCase :list="caseArticleData.list || []"/>
            <SelectedClass :list="recommendCourseList" :loading="loading"/>
          </div>
        </template>
        <template #right>
          <div ref="home_page_right_wrapper" class="home_page_right_wrapper">
            <TodayLive v-if="TodayMeetingData.list.length>0" :today-meeting-list="TodayMeetingData.list || []"/>
            <nuxt-link to="/askAI/char/home">
              <img style="width: 265px" :src="aiNav" alt="" @click="toaihandler">
            </nuxt-link>
            <SubMajor :list="channelData.list || []"/>
            <AcademicResources/>
            <MedicalDevices v-if="firstCategoryList.length>0" :list="firstCategoryList"/>
            <CaseCompetition v-if="competitionList.length>0" :list="competitionList"/>
            <RecommendBook v-if="merchandiseList.length>0" :list="merchandiseList"/>
          </div>
        </template>
      </ContainerPage>
    </div>
  </div>
</template>

<script>
import {
  AcademicResources,
  CaseCompetition,
  HomeBanner,
  MedicalDevices,
  MiddleBanner,
  NewInfoModule,
  PopularMeetings,
  RecommendBook,
  SelectedCase,
  SelectedClass,
  SubMajor,
  TodayLive
} from "../../opt-components/page/home";
import {Container, ContainerPage} from "../../opt-components/template";
import {getSlotContent} from "../../api/banner/banner";
import {getTodayMeetings} from "../../api/meeting";
import {getWebApiChannel} from "../../api/channel";
import {
  getCompetitionList,
  getMerchandiseList, getNewestHotMeetingList,
  getNewInfoList,
  getRecommendCourseList,
  selectedArticleList
} from "../../api/home";

export default {
  name: "HomePage",
  components: {
    ContainerPage,
    Container,
    HomeBanner,
    NewInfoModule,
    PopularMeetings,
    SelectedClass,
    SelectedCase,
    MiddleBanner,
    TodayLive,
    SubMajor,
    AcademicResources,
    MedicalDevices,
    CaseCompetition,
    RecommendBook
  },
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '脑医汇 - 神外资讯、神介资讯 - 领先的临床神经科学互联网平台'
    const [bannerData, TodayMeetingData, channelData, newInfoData, caseArticleData, newestHotMeeting] = await Promise.all([
      app.$axios.$request(
        getSlotContent({
          loginUserId: store.state.auth.user.id,
          detailIdStr: '',
          adCode: 'webAPi_banner',
        })
      ),
      app.$axios.$request(getTodayMeetings()),
      app.$axios.$request(getWebApiChannel()),
      app.$axios.$request(
        getNewInfoList({
          limit: 4
        })
      ),
      app.$axios.$request(
        selectedArticleList({
          pageNo: 1,
          pageSize: 6,
          userId: store.state.auth.user.id
        })
      ),
      app.$axios.$request(
        getNewestHotMeetingList({
          limit: 6
        })
      )
    ])
    return {
      bannerData,
      TodayMeetingData,
      channelData,
      newInfoData,
      caseArticleData,
      newestHotMeeting
    }
  },
  data() {
    return {
      competitionList: [],
      merchandiseList: [],
      middleBannerList: [],
      recommendCourseList: [],
      loading: true,
      aiNav: require('@/assets/images/ai/home_ai.png')
    }
  },
  computed: {
    firstCategoryList() {
      return this.$store.state.global.firstCategoryList
    }
  },
  mounted() {
    this.getInitDataHandler()
  },
  methods: {
    toaihandler () {
      this.$analysys.btn_click('AI问答', '脑医汇 -  神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台')
    },
    async getInitDataHandler() {
      const [competitionData, merchandiseData, middleBannerData, recommendCourseData] = await Promise.all([
        this.$axios.$request(getCompetitionList({limit: 5})),
        this.$axios.$request(getMerchandiseList({
          pageNo: 1,
          pageSize: 3,
          seqType: 2
        })),
        this.$axios.$request(
          getSlotContent({
            loginUserId: this.$store.state.auth.user.id,
            detailIdStr: '',
            adCode: 'webAPi_leftBanner',
          })
        ),
        this.$axios.$request(
          getRecommendCourseList({
            coursePageType: 'M',
            pageNo: 1,
            pageSize: 6
          })
        ),
      ])

      this.loading = false;

      if (competitionData.code === 1) {
        this.competitionList = competitionData.list.filter(item => item.id !== 25 && item.id !== 27)
      }

      if (merchandiseData.code === 1) {
        this.merchandiseList = merchandiseData.list
      }

      if (middleBannerData.code === 1) {
        this.middleBannerList = middleBannerData.list
      }

      if (recommendCourseData.code === 1) {
        this.recommendCourseList = recommendCourseData.list
      }

      // eslint-disable-next-line no-undef
      setTimeout(() => {
        const rightWrapper = this.$refs.home_page_right_wrapper
        if (rightWrapper) {
          this.rightTop = (rightWrapper.offsetHeight - window.innerHeight) + 32

          rightWrapper.style.top = "-" + this.rightTop + "px"
        }
      }, 100)

    },
    aiHandler () {

    }
  }
}
</script>

<style scoped lang="less">
.home_page_wrapper {
  background: #EEF4F6;
  padding: 24px 0 32px;

  .row_spacing {
    display: grid;
    gap: 24px;
  }

  .row_banner {
    margin: 32px 0;
  }

  .home_page_right_wrapper {
    display: grid;
    gap: 24px;

    position: sticky;
  }
}


</style>
