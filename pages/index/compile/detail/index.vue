<template>
  <InfoTemplate
    nav-name='精选编译'
    nav-path='/compile'
    :info-data='infoData'
    :related-articles='relatedArticles'
    :news-info='newsInfo'
  />
</template>

<script>

import {assocInfos, getInfo, newestInfos} from '@/api/article'
import InfoTemplate from '@/components/page/detail/InfoTemplate/InfoTemplate.vue'

export default {
  name: 'DetailIndexPage',
  components: {
    InfoTemplate
  },

  async asyncData({app, params, error, store, query, req, redirect}) {
    redirect(`/info/detail?id=${query.id}`)
    /**
     * reuqest1 获取当前文章的相关文章
     * reuqest2 获取文章详情接口
     * newsInfo 最新发布
     */
    const [request1, request2] = await Promise.all([
      app.$axios.$request(assocInfos({
        articleId: query.id,
        limit: store.state.datalist_count
      })),
      app.$axios.$request(getInfo({
        infoId: query.id,
        userId: store.state.auth.user.id
      }))
    ])
    app.head.title = request2.result.title + ' - 脑医汇 - 神外资讯 - 神介资讯'
    let idArray = ''// 作者ID集
    request2.result && request2.result.info.authorList ? request2.result.info.authorList.forEach((item) => {
      idArray += item.id + ','
    }) : idArray = ''

    const newsInfo = await app.$axios.$request(newestInfos({
      infoId: query.id,
      authorIds: idArray.slice(0, idArray.length - 1),
      pageNo: 1,
      pageSize: store.state.datalist_count
    }))

    const attrsNames = request2.result.attrs ? request2.result.attrs.map((item) => item.name + ',') : ''
    const authorNames = request2.result.authorNames && request2.result.authorNames !== '' ? request2.result.authorNames + ',' : ''
    let keywordsName = request2.result.searchKeyWords || ''
    keywordsName = keywordsName !== '' ? keywordsName + ',' : ''

    return {
      relatedArticles: request1.list,
      infoData: request2.result,
      newsInfo: newsInfo.list,
      attrsNames,
      authorNames,
      keywordsName
    }
  },
  head() {
    return {
      title: this.infoData.title + ' - 脑医汇 - 神外资讯 - 神介资讯',
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.infoData.info.metaDescription
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇, ${this.keywordsName}${this.authorNames}${this.attrsNames}神外资讯,神内资讯,神介资讯,神经外科,神经内科,医学,医学资讯`
        }]
    }
  }
}
</script>

<style lang='less' scoped>
@import "./index";
</style>
