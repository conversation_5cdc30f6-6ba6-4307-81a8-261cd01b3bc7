.consulting-box {
  padding: 24px 0 36px;

  .consulting-left {
    .NewConsulting {
      .consulting {
        .consulting-item {
          border-radius: 6px;
          overflow: hidden;
          margin-bottom: 14px;
          transition: all 0.3s;
          display: flex;
          justify-content: flex-start;
          padding: 10px 0 10px 10px;
          box-sizing: border-box;

          .item-left {
            margin-right: 10px;
            min-width: 226px;
            max-width: 226px;
            text-align: center;
            min-height: 100%;
            border-radius: 6px;
            height: 124px;
            overflow: hidden;
          }

          .item-right {
            padding-right: 20px;

            .pone {
              font-size: 18px;
              line-height: 22px;

              .poneIcon {
                min-width: 16px;
                min-height: 16px;
                max-width: 16px;
                max-height: 16px;
                vertical-align: middle;
              }
            }

            .ptwo {
              display: block;
              font-size: 12px;
              line-height: 22px;
              color: #708AA2FF;
            }

            .pthree {
              font-size: 12px;
              color: #708AA2FF;
              margin: 12px 0 16px;

              .authorList {
                margin-right: 10px;
              }

              i {
                margin-right: 2px;
              }
            }

            .lable-box {
              display: flex;
              justify-items: flex-start;
              flex-wrap: wrap;
              margin-top: 10px;

              .lable-item {
                border-radius: 6px;
                background: #F0F9FF;
                font-size: 12px;
                color: #0A83CE;
                line-height: 22px;
                padding: 0 11px;
                margin: 0 10px 10px 0;
              }
            }
          }
        }

        .consulting-item:hover {
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  .consulting-right {
    .advertisement {
      width: 100%;
      height: 132px;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 20px;
    }
  }
}

#fuwenben {
  min-height: 35vh;
  margin-bottom: 20px !important;
}

/*中屏时结束*/
#fuwenben /deep/ iframe {
  width: 100%;
  min-height: 40vh;
}

#fuwenben /deep/ section {
  max-width: 100% !important;
}

/deep/ video {
  width: 100%;
}

.pthreeIcon {
  width: 12px;
  height: 12px;
}
