<template>
  <div class='container-box consulting-box'>
    <el-row>
      <el-col :lg='16' :md='16' :sm='18' :xl='16' :xs='24' class='page-left'>
        <div class='consulting-left'>
          <NavTwo
            :channelType='channelType'
            :subSpecialtyCurrent='subSpecialtyName'
            :subSpecialtyData='subSpecialtyData'
            :type='1'
            @selectRecommendFun='selectRecommendFun'
            @selectsubSpecialtyId='selectsubSpecialtyId'
          ></NavTwo>
          <div class='NewConsulting'>
            <div class='consulting'>
              <a
                v-for='(item) in compileData'
                :key='item.id'
                :href="`/info/detail?id=${item.id}`"
                target="_blank"
                class='consulting-item cursor'
              >
                <div class='item-left'>
                  <img v-if='item.smallImageUrl' :src='$tool.compressImg(item.smallImageUrl,226,124)' alt=''
                       class='img_cover'>
                  <img v-else class='img_cover' src='~assets/images/default16.png'/>
                </div>
                <div class='item-right'>
                  <p class='pone text-limit-2 fontWeight'>
                    <svg-icon
                      v-if='item.recommend==="T"'
                      class-name='poneIcon cursor'
                      icon-class='jinghua'
                    ></svg-icon>
                    <svg-icon v-if="item.bmsAuth === 1" icon-class="auth-new"
                              style="width: 18px;height: 18px;vertical-align:middle"/>
                    <span class='vertical_align_middle'>
                        {{ item.title }}
                      </span>
                  </p>
                  <p class='pthree flex_start flex_align_center'>
                    <i class='el-icon-time' style='margin-right: 2px'></i>
                    <span style='margin-right: 22px'>
                        {{ timeStamp.timestampFormat((item.publishDate / 1000)) }}
                      </span>
                    <!--                      <i class="el-icon-video-play"></i>-->
                    <span>
                        <svg-icon
                          class-name='pthreeIcon cursor'
                          icon-class='yuanchan'
                        ></svg-icon>
                      <span v-for='itemauthorList in item.authorList' v-if='item.authorList.length>0'
                            :key='itemauthorList.id'
                            class='authorList'>{{ itemauthorList.authorName }}</span>
                      <span v-if='item.authorList.length<=0'>神外资讯原创</span>
                     </span>
                  </p>
                  <ul class='lable-box'>
                    <li v-for='item in item.attrs' :key='item.id' class='lable-item'>{{ item.name }}</li>
                  </ul>
                  <p class='ptwo text-limit-1'>{{ item.description }}</p>
                </div>
              </a>
            </div>
          </div>
          <Empty :loading='loading' :no-more='!loading && compileData.length===0' height='70vh'/>
          <el-pagination
            v-if='!loading'
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='$store.state.article_count'
            :pager-count='$store.state.pager_count'
            :total='total'
            background
            small
            style='text-align: center; margin-bottom: 10px'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </el-col>
      <el-col :lg='8' :md='8' :sm='6' :xl='8' :xs='24' class='page-right'>
        <div class='consulting-right'>
          <div class='advertisement cursor' @click='jumpReleaseCaseFun()'>
            <img alt='' class='img_cover' src='~assets/images/compile/submission.png'/>
          </div>
          <literature :literatureNewsData.sync='literatureNewsData'></literature>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Empty from '@/components/UI/Empty/Empty'
import literature from '@/components/PageComponents/Literature/Literature' // 文献速览
import NavTwo from '@/components/NavigationTwo/NavigationTwo'
import {getCompileArticleList, getCompileChannel, literatureNews} from '@/api/article'
import {getSubSpecialty} from '@/api/case' // 文献速览
export default {
  head() {
    return {
      title: '精选编译'
    }
  },
  name: 'ConsultingPage',
  components: {
    literature,
    NavTwo,
    Empty
  },
  // eslint-disable-next-line require-await
  async asyncData({app, params, error, store, query, req}) {
    app.head.title = '精选编译'
    const [request1, request2, request3, request4] = await Promise.all([
      app.$axios.$request(getCompileArticleList({
        channelId: query.subSpecialtyId ? query.subSpecialtyId : null,
        recommend: query.selectedFalg ? query.selectedFalg : null,
        pageNo: query.currentPage ? query.currentPage : Number(1),
        pageSize: store.state.article_count
      })),
      app.$axios.$request(getCompileChannel()),
      app.$axios.$request(literatureNews({pageNo: 1, pageSize: store.state.article_count})),
      app.$axios.$request(getSubSpecialty())
    ])
    let subSpecialtyData = request2.list
    // 给咨询筛选第一个加入全部
    if (typeof request2 !== 'string' && typeof subSpecialtyData !== 'string') {
      let msg = query.selectedFalg ? query.selectedFalg === 'T' ? '精华' : '全部' : '全部'
      subSpecialtyData.unshift({
        name: msg,
        id: 0,
        compileArticle: 'T'
      })
    }
    let subCurrent = subSpecialtyData.findIndex((item) => item.code === 'shenjingkexue')
    subCurrent !== -1 ? subSpecialtyData.splice(subCurrent, 1) : null // 删除神经科学
    return {
      compileData: request1.list,
      total: request1.page.totalCount,
      subSpecialtyData,
      literatureNewsData: request3.list,
      subSpecialtyChannel: request4.list,
      currentPage: query.currentPage ? Number(query.currentPage) : 1,          // 分页页码
      channelIndex: query.subSpecialtyId ? query.subSpecialtyId : null,// 亚专业ID
      subSpecialtyName: query.subSpecialtyName ? query.subSpecialtyName : '全部', // 选中亚专业名称
      channelType: query.selectedFalg ? query.selectedFalg : null       // 是否为精选
    }
  },
  data() {
    return {
      loading: false,
      currentPage: 1, // 分页页数
      channelIndex: 0,// 亚专业下标
      channelType: 'F',// 精选
      bmsAuth: null
    }
  },
  mounted() {

  },
  watch: {
    '$route'(res) {
      this.currentPage = Number(res.query.currentPage)
      this.channelIndex = res.query.channelId
      this.channelType = res.query.selectedFalg
      this.subSpecialtyName = res.query.subSpecialtyName
    }
  },
  methods: {
    // 跳转投审稿
    jumpReleaseCaseFun() {
      this.$router.push({path: '/personalfile'})
    },
    // 切换亚专业
    selectsubSpecialtyId(index) {
      if (!this.loading) {
        this.$router.push({
          name: 'index-compile', query: {
            channelId: index.channelId
          }
        })
        this.compileData = []
        this.loading = true
        this.currentPage = 1
        this.channelIndex = index.channelId
        this.channelType = null
        this.channelType === 'T' ? this.subSpecialtyData[0].name = '精华' : this.subSpecialtyData[0].name = '全部'
        this.$analysys.btn_click(index.name, document.title)
        this.$axios.$request(getCompileArticleList({
          channelId: index.channelId === 0 ? null : index.channelId,
          pageNo: 1,
          pageSize: this.$store.state.article_count
        })).then((res) => {
          this.loading = false
          if (res && res.code === 1) {
            this.compileData = res.list
            this.total = res.page.totalCount
          }
        })
      }
    },
    // 切换精华
    selectRecommendFun(type) {
      this.currentPage = 1
      this.channelType = type
      if (type === "T") {
        this.subSpecialtyData[0].name = '精华'
      } else if (type === 1) {
        this.subSpecialtyData[0].name = '品牌认证'
      } else {
        this.subSpecialtyData[0].name = '全部'
      }

      this.$axios.$request(getCompileArticleList({
        channelId: this.channelIndex === 0 ? null : this.channelIndex,
        pageNo: 1,
        pageSize: this.$store.state.article_count,
        recommend: this.channelType === 1 ? null : this.channelType,
        orderType: this.channelType === 1 ? 4 : null
      })).then((res) => {
        if (res && res.code === 1) {
          this.compileData = res.list
          this.total = res.page.totalCount
        }
      })
    },
    // 分页
    handleCurrentChange(item) {
      this.$tool.scrollIntoView()
      this.$axios.$request(getCompileArticleList({
        pageNo: item,
        pageSize: this.$store.state.article_count,
        channelId: this.channelIndex === 0 ? null : this.channelIndex,
        recommend: this.channelType === 1 ? null : this.channelType,
        orderType: this.channelType === 1 ? 4 : null
      })).then((res) => {
        this.compileData = res.list
        this.total = res.page.totalCount
      })
    },
    // 跳转文章详情
    jumpDetailsPageFun(infoid) {
      const {href} = this.$router.resolve({
        name: 'index-compile-detail',
        query: {
          id: infoid
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>

<style lang='less' scoped>
@import "~@/pages/index/compile/index.less";
</style>
