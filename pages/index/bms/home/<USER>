<template>
  <PageContainer>
    <section slot='page-left'>
      <div style='margin-bottom: 50px'>
        <PopularBrands />
      </div>
      <BrandContent
        :tab-list='tabList'
      />
    </section>
    <section slot='page-right' class='bms__wrapper__right'>
      <AdvertisementComponent
        v-for='item in adList'
        :key='item.adId'
        :ad-id="item.adId"
        :img-url='item.image'
        :name='item.name'
        :module='item.module'
        :extras='item.extras'
        :click-location="item.clickLocation"
        :code="item.code"
      />
      <BrandClassification />
      <ActivityZone :category-id='$route.params.categoryId' />
      <PopUpAdvertising
        :visible='popupAdsShow'
        :popup-ad-item="popupAdItem"
        @cancelFn='popupAdsShow = false' />
      <ShortVideoPlayback
        :video-id='$store.state.bms.bmsHomeShortVideoId'
        :visible='$store.state.bms.bmsHomeShortVisible'
        @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
      />
    </section>
  </PageContainer>
</template>

<script>
import ShortVideoPlayback from '../../../../components/optimize-components/public/ShortVideoPlayback/index.vue'
import PopUpAdvertising from '../../../../components/optimize-components/page-components/bms/PopUpAdvertising/index.vue'
import PageContainer from '../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import PopularBrands from '../../../../components/optimize-components/page-components/bms/PopularBrands/index.vue'
import BrandClassification
  from '../../../../components/optimize-components/page-components/bms/BrandClassification/index.vue'
import ActivityZone from '../../../../components/optimize-components/page-components/bms/ActivityZone/index.vue'
import BrandContent from '../../../../components/optimize-components/page-components/bms/BrandContent/index.vue'
import AdvertisementComponent
  from '../../../../components/optimize-components/page-components/bms/Advertisement/index.vue'
import {getWebApiCategoryTabs, getWebApiFirstCategoryList} from '../../../../api/bms'
import {getSlotContent} from "../../../../api/banner/banner";

export default {
  name: 'BMSPage',
  components: {
    AdvertisementComponent,
    BrandContent,
    ActivityZone,
    PopularBrands,
    PageContainer,
    BrandClassification,
    PopUpAdvertising,
    ShortVideoPlayback
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [adList, tabShowList,firstCategoryData] = await Promise.all([
      app.$axios.$request(
        getSlotContent({
          loginUserId:store.state.auth.user.id,
          detailIdStr:'',
          adCode:`webapi_bms_middle_${query.categoryCode}`,
        })
      ),
      app.$axios.$request(getWebApiCategoryTabs({
        categoryId: params.categoryId
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])



    const {
      recommendTabShow,
      caseTabShow,
      newestProductTabShow,
      shortVideoTabShow,
      hotProductTabShow,
      meetingTabShow
    } = tabShowList.result

    const tabList = [
      { id: 1, name: '推荐', tips: '随便看看', isEnable: recommendTabShow, code: 'RecommendList' },
      { id: 2, name: '病例', tips: '医生精选', isEnable: caseTabShow, code: 'CaseList' },
      { id: 3, name: '新品', tips: '最近上新', isEnable: newestProductTabShow, code: 'NewProducts' },
      { id: 4, name: '短视频', tips: '大家在看', isEnable: shortVideoTabShow, code: 'ShortVideo' },
      { id: 5, name: '热门', tips: '热门产品', isEnable: hotProductTabShow, code: 'HotProducts' },
      { id: 6, name: '直录播', tips: '大咖云集', isEnable: meetingTabShow, code: 'RecordedBroadcastList'  }
    ]

    return {
      adList: adList.list,
      tabList,
      firstCategoryList:firstCategoryData.list
    }
  },
  data() {
    return {
      popupAdsShow: false,
      popupAdItem:null
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,通路、缺血、出血、耗材、手术器械、设备、肿瘤药品、功能药品、血管药品、诊断`
        }
      ]
    }
  },
  mounted() {
    if (this.$route.query.categoryCode) {
      this.getBmsPopupAdsByCategoryHandler()
    }
    if (this.$route.query.shortVideoId) {
      this.$store.commit('bms/setBmsHomeShortVideoHandler', this.$route.query.shortVideoId)

      this.$router.replace({
        path: this.$route.path
      })
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-12 14:00
     * 广告弹窗  -- 一天弹一次
     * ------------------------------------------------------------------------------
     */
    getBmsPopupAdsByCategoryHandler() {
      this.$axios.$request(
        getSlotContent({
          loginUserId:this.$store.state.auth.user.id,
          detailIdStr:'',
          adCode:`bms_popup_${this.$route.query.categoryCode}`,
        })
      ).then(response => {
        if (response && response.code === 1) {
          const okAdArr = window.localStorage.getItem('brandAdsIdArr')
          let newArr = response.list
          if (okAdArr) {
            const adArr = okAdArr.split(',')
            newArr = newArr.filter(x => !adArr.includes(String(x.adId)))
          }


          if (newArr.length > 0) {
            if (okAdArr) {
              window.localStorage.setItem('brandAdsIdArr', okAdArr + ',' + newArr[0].adId)
            } else {
              window.localStorage.setItem('brandAdsIdArr', newArr[0].adId)
            }

            this.popupAdsShow = true
            this.popupAdItem = newArr[0]
          }
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.bms__wrapper__right {
  display: flex;
  flex-flow: column;
  grid-gap: 30px 0;
}
</style>
