<template>
  <PageContainer>
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item
        :to='{ path: `/bms/home/<USER>'>
        {{ crumbs }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        品牌列表
      </el-breadcrumb-item>
    </bm-breadcrumb>
    <div class='brand_select_wrapper'>
      <div class='title'>
        品牌选择：
      </div>
      <ul class='letters_list'>
        <li
          class='item itemAll'
          :class='active === "" ? "is_activeAll" : ""'
          @click='active = ""'
        >
          全部
        </li>
        <li
          v-for='(item,index) in brandFilter'
          :key='index'
          class='item'
          @click='active = item.initial'
          :class='active === item.initial ? "is_active" : ""'
        >
          {{ item.initial }}
        </li>
      </ul>
    </div>
    <div class='brand_list'>
      <div
        v-for='(item,index) in brandListComList'
        :key='index'
        class='image'>
        <nuxt-link
          :target='item.bmsBrandProductLineList && item.bmsBrandProductLineList.length>1 ? "_self" : "_blank"'
          :to='
            item.bmsBrandProductLineList && item.bmsBrandProductLineList.length===1 ?
            {path:`/bms/classify/${$route.params.categoryId}/product-line-details/${item.bmsBrandProductLineList[0].id}`}
            :
            item.bmsBrandProductLineList && item.bmsBrandProductLineList.length>1 ?
            {path: $route.fullPath}
            :
             {path:`/bms/classify/${$route.params.categoryId}/brand-details/${item.id}`}'>
          <img
            :title='item.name'
            class='img_cover'
            :src='item.logo ? $tool.compressImg(item.logo,146,67) : require("/assets/images/default16.png")'
            alt=''
          >
        </nuxt-link>
        <div v-if='item.bmsBrandProductLineList && item.bmsBrandProductLineList.length>1' class='product_line_wrapper'>
          <ul class='product_line_list'>
            <li
              v-for='itemC in item.bmsBrandProductLineList'
              :key='itemC.id'
              class='product_line_item text-limit-1'>
              <nuxt-link target='_blank'
                         :to='{path:`/bms/classify/${$route.params.categoryId}/product-line-details/${itemC.id}`}'>
                {{ itemC.name }}
              </nuxt-link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer2/PageContainer.vue'
import {getWebApiBrandListByCategoryId, getWebApiFirstCategoryList} from '../../../../../../api/bms'

export default {
  name: 'BrandPage',
  components: { PageContainer },
  async asyncData({ app, params, error, store, query, req }) {
    const [brandList,firstCategoryData] = await Promise.all([
      app.$axios.$request(getWebApiBrandListByCategoryId({
        categoryId: params.categoryId,
        orderType: 2
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    const typeList = [] // 定义空数组，用于装载去重之后的数组，
    const userClass = [] // 定义空对象，用于数组转换成对象
    if (brandList.list) { // 如果有值
      brandList.list.forEach(item => {
        // 可以用indexOf()数组去重 如果检索的结果匹配到,则返回 1. 如果检索的结果没有匹配值,则返回 -1.
        if (!typeList.includes(item.initial.toUpperCase())) {
          typeList.push(item.initial.toUpperCase())
          userClass.push({
            initial: item.initial.toUpperCase(),
            brandList: [item]
          })
        } else {
          userClass.forEach((itemUserClass) => {
            if (itemUserClass.initial.toUpperCase() === item.initial.toUpperCase()) {
              itemUserClass.brandList.push(item)
            }
          })
        }
      })
    }

    return {
      brandList: brandList.list,
      brandFilter: userClass,
      firstCategoryList: firstCategoryData.list
    }
  },
  head() {
    return {
      title: `脑医汇 - 品牌列表 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断`
        }
      ]
    }
  },
  data() {
    return {
      active: ''
    }
  },
  computed: {
    brandListComList() {
      const list = this.brandFilter.filter(item => item.initial === this.active)
      if (list.length > 0) {
        return list[0].brandList
      } else {
        return this.brandList
      }
    },
    crumbs() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
    },
    categoryCode() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
    }
  },
  mounted() {

  },
  methods: {
    changeInitial() {

    }
  }
}
</script>

<style scoped lang='less'>
.brand_select_wrapper {
  display: flex;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 40px;
  user-select: none;

  .title {
    flex-shrink: 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: #666666;
    margin-right: 12px;
  }

  .letters_list {
    display: flex;
    grid-gap: 12px 18px;
    flex-wrap: wrap;

    .item {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #333333;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .itemAll {
      font-weight: 400;
      font-size: 14px;
      line-height: 14px;
    }

    .is_active {
      background: #0581CE;
      color: #FFFFFF;
    }

    .is_activeAll {
      background: #0581CE;
      color: #FFFFFF;
      font-size: 12px;
    }
  }
}

.brand_list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 30px;
  margin-bottom: 50px;

  .image {
    width: 100%;
    height: 67px;
    background: #999999;
    position: relative;

    &:hover {
      .product_line_wrapper {
        display: block;
      }
    }
  }

  .product_line_wrapper {
    display: none;
    position: absolute;
    min-width: 100%;
    max-width: 300px;
    left: 50%;
    top: 100%;
    padding-top: 8px;
    transform: translateX(-50%);
    z-index: 1;

    &::after {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: calc(100% - 8px);
      content: "";
      width: 0;
      height: 0;
      border-bottom: 8px solid #ECECEC;
      border-right: 14px solid transparent;
      border-left: 14px solid transparent;
      //filter: drop-shadow(2px -2px 1px rgba(0, 0, 0, .1));
    }

    &::before {
      z-index: 1;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: calc(100% - 9px);
      content: "";
      width: 0;
      height: 0;
      border-bottom: 8px solid #f8f8f8;
      border-right: 14px solid transparent;
      border-left: 14px solid transparent;
      //filter: drop-shadow(2px -2px 1px rgba(0, 0, 0, .1));
    }

    .product_line_list {
      padding: 20px;
      box-sizing: border-box;
      background: #f8f8f8;
      border: 1px solid #ECECEC;
      border-radius: 6px;

      .product_line_item {
        margin-bottom: 20px;

        &:hover {
          a {
            color: var(--theme-color);
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
