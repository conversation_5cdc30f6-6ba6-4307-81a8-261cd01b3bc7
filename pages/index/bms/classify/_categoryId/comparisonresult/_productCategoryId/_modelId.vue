<template>
  <NewPageContainer>
    <bm-breadcrumb>
      <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
      <el-breadcrumb-item
        v-if='crumbs!==""'
        :to='{ path: `/bms/home/<USER>'>
        {{ crumbs }}
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        产品对比
      </el-breadcrumb-item>
    </bm-breadcrumb>

    <div class="title">
      <svg-icon icon-class="contrase" class-name="title_icon"/>
      <span>产品参数对比</span>
    </div>

    <ContrastTable
      :product-model-default-list="productModelDefaultList"
    />

  </NewPageContainer>
</template>

<script>
import NewPageContainer from "../../../../../../../components/optimize-components/UI/NewPageContainer/index.vue";
import ContrastTable
  from "../../../../../../../components/optimize-components/page-components/bms/ContrastTable/index.vue";
import {getProductModelListByIds, getWebApiFirstCategoryList} from "../../../../../../../api/bms";

export default {
  name: "ComparisonPage",
  components: {
    NewPageContainer,
    ContrastTable
  },
  async asyncData({app, params, error, store, query, req}) {
    if (params.modelId) {
      const [productModelData, firstCategoryData] = await Promise.all([
        app.$axios.$request(getProductModelListByIds({
          modelIdsStr: params.modelId
        })),
        app.$axios.$request(getWebApiFirstCategoryList())
      ])

      return {
        productModelDefaultList: productModelData.list,
        firstCategoryList: firstCategoryData.list,
      }
    } else {
      return {
        productModelDefaultList: [],
      }
    }


  },
  head() {
    return {
      title: '产品对比'
    }
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        if (this.firstCategoryList) {
          return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
        } else {
          return ''
        }

      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        if (this.firstCategoryList) {
          return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
        } else {
          return ''
        }

      } else {
        return ''
      }
    }
  },
  watchQuery: false
}
</script>

<style scoped lang="less">
.title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 40px 0 22px;

  .title_icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}
</style>
