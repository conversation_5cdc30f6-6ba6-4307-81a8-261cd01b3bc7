<template>
  <PageContainer>
    <div class='product_screening_wrapper'>
      <!--Navigation Start-->
      <div class='nav_top' style='margin-bottom: 35px'>
        <bm-breadcrumb>
          <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
          <el-breadcrumb-item
            :to='{ path: `/bms/home/<USER>'>
            {{ crumbs }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            活动专区
          </el-breadcrumb-item>
        </bm-breadcrumb>
      </div>
      <!--Navigation End-->
      <div class='brand_screening_wrapper'>
        <div style='margin-bottom: 30px'>
          <ProductFilterList
            filter-title='品牌选择：'
            :active-filter-id='brandActiveId'
            :filter-array='brandList'
            @changeSelectHandler='changeSelectBrandHandler'
          />
        </div>
      </div>

      <div class='product_list_wrapper'>
        <DataLoad :loading='loading' :no-more='activityData.list.length===0'>
          <ul class='product_list'>
            <ActivityItem
              v-for='item in activityData.list'
              :id='item.id'
              :key='item.id'
              img-height='126px'
              image-bottom='16px'
              :product-is-show='false'
              :title='item.name'
              :img='item.cover'
              :brand-name='item.brand.name'
              :logo='item.brandProductLine ? item.brandProductLine.shareImage : item.brand.shareImage'
              :brand-id='item.brand.id'
              :category-id='$route.params.categoryId'
              :product-line-id='item.brandProductLine ? item.brandProductLine.id : null'
            />
          </ul>
          <div style='margin-top: 80px'>
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='20'
              :pager-count='$store.state.pager_count'
              :total='activityData.page.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </DataLoad>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer2/PageContainer.vue'
import ActivityItem
  from '../../../../../../components/optimize-components/public/article-types-list/bms/ActivityItem/index.vue'
import ProductFilterList
  from '../../../../../../components/optimize-components/page-components/bms/ProductFilterList/index.vue'
import {
  getActivityAllBrandList,
  getBrandActivityListByCategoryIdAndBrandId, getWebApiFirstCategoryList
} from '../../../../../../api/bms'
import DataLoad from '../../../../../../components/optimize-components/public/DataLoad/index.vue'

export default {
  name: 'ActivityDirectory',
  components: {
    DataLoad,
    ProductFilterList,
    ActivityItem,
    PageContainer
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [brandList, activityData,firstCategoryData] = await Promise.all([
      app.$axios.$request(getActivityAllBrandList({})),
      app.$axios.$request(getBrandActivityListByCategoryIdAndBrandId({
        brandId: null,
        categoryId: params.categoryId,
        pageNo: 1,
        pageSize: 20
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    return {
      brandList: brandList.list,
      activityData,
      firstCategoryList: firstCategoryData.list
    }
  },
  head() {
    return {
      title: `脑医汇 - 活动专区列表 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断`
        }
      ]
    }
  },
  data() {
    return {
      brandActiveId: null,
      currentPage: 1,
      loading: false
    }
  },
  computed: {
    crumbs() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
    },
    categoryCode() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-13 10:34
     * 获取活动
     * ------------------------------------------------------------------------------
     */
    getBrandActivityHandler({ isTop = false, pageNo = 1, brandId = null, backFn = null }) {
      this.loading = true
      this.activityData.list = []

      if (isTop) {
        this.$tool.scrollIntoTop()
      }
      this.$axios.$request(getBrandActivityListByCategoryIdAndBrandId({
        brandId,
        categoryId: this.$route.params.categoryId,
        pageNo,
        pageSize: 20
      })).then(response => {
        this.loading = false

        if (response && response.code === 1) {
          this.activityData = response

          if (backFn) {
            backFn(true)
          }
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-09 13:08
     * @description: 筛选品牌
     * ------------------------------------------------------------------------------
     */
    changeSelectBrandHandler(code, backFn) {
      this.brandActiveId = code !== 0 ? code : null
      this.getBrandActivityHandler({ isTop: false, pageNo: this.currentPage, brandId: this.brandActiveId, backFn })
    },
    handleCurrentChange(item) {
      this.currentPage = item
      this.getBrandActivityHandler({ isTop: true, pageNo: item, brandId: this.brandActiveId })
    }
  }
}
</script>

<style scoped lang='less'>
.brand_screening_wrapper {
  margin-bottom: 30px;
}

.product_list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 20px 18px;
  margin-bottom: 5px;
}
</style>
