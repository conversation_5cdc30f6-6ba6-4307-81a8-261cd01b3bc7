<template>
  <div class='collection_wrapper container-box'>
    <!--Navigation Start-->
    <div class='nav_top' style='margin: 20px 0 0'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-if='crumbs!==""'
          :to='{ path: `/bms/home/<USER>'>
          {{ crumbs }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ collectionVideosDetails.name }}
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <!--Navigation End-->
    <PageContainer>
      <section slot='page-left'>
        <div style='margin-bottom:20px'>
          <CollectionHead
            :collection-videos-details='collectionVideosDetails'
          />
        </div>
        <Empty :loading='loading' :no-more='!loading && videosByCollection.list.length===0' />
        <ul class='video_list_wrapper'>
          <VideoItem
            v-for='item in videosByCollection.list'
            :key='item.id'
            :author-id='item.creator.id'
            :video-id='item.id'
            :video-img='item.cover'
            :video-title='item.title'
            :author-name='item.creator.realName'
            :avatar-address='item.creator.avatarAddress'
            @videoFrame='videoFrameHandler'
          />
        </ul>
        <div style='margin-top: 80px'>
          <el-pagination
            v-show='!loading'
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='12'
            :pager-count='$store.state.pager_count'
            :total='videosByCollection.page.totalCount'
            background
            small
            style='text-align: center'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </section>
      <section slot='page-right' class='bms__short_video_wrapper__right'>
        <RecommendedCollection
          :collection-by-join-time-list='collectionByJoinTime'
        />
      </section>
    </PageContainer>
    <ShortVideoPlayback
      :video-id='videoId'
      :visible='visible'
      @cancelFn='cancelHandler' />
  </div>
</template>

<script>
import Empty from '../../../../../../components/optimize-components/UI/Empty/index.vue'
import ShortVideoPlayback from '../../../../../../components/optimize-components/public/ShortVideoPlayback/index.vue'
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import CollectionHead
  from '../../../../../../components/optimize-components/page-components/bms/collection/CollectionHead/index.vue'
import VideoItem
  from '../../../../../../components/optimize-components/public/article-types-list/bms/VideoItem/index.vue'
import RecommendedCollection
  from '../../../../../../components/optimize-components/page-components/bms/collection/RecommendedCollection/index.vue'
import {
  getCollectionByJoinTime,
  getCollectionVideos,
  getVideosByCollectionId,
  getWebApiFirstCategoryList
} from '../../../../../../api/bms'

export default {
  name: 'ShortVideoCollection',
  components: { RecommendedCollection, VideoItem, CollectionHead, PageContainer, ShortVideoPlayback, Empty },
  async asyncData({ app, params, error, store, query, req }) {
    const [collectionVideos, collectionByJoinTime, videosByCollection,firstCategoryData] = await Promise.all([
      app.$axios.$request(getCollectionVideos({
        collectionId: params.id,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(getCollectionByJoinTime({
        limit: 5
      })),
      app.$axios.$request(getVideosByCollectionId({
        collectionId: params.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 12
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    return {
      collectionVideosDetails: collectionVideos.result,
      collectionByJoinTime: collectionByJoinTime.list,
      videosByCollection,
      firstCategoryList: firstCategoryData.list
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.collectionVideosDetails.name} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.collectionVideosDetails.description || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,${this.collectionVideosDetails.name}`
        }
      ]
    }
  },
  data() {
    return {
      loading: false,
      currentPage: 1,
      videoId: null,
      visible: false
    }
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
      } else {
        return ''
      }
    }
  },
  mounted() {
  },
  methods: {
    handleCurrentChange(item) {
      this.loading = true
      this.videosByCollection.list = []
      this.currentPage = item
      this.$tool.scrollIntoTop()

      this.$axios.$request(getVideosByCollectionId({
        collectionId: this.$route.params.id,
        loginUserId: this.$store.state.auth.user.id,
        pageNo: item,
        pageSize: 12
      })).then(res => {
        if (res && res.code === 1) {
          this.videosByCollection.list = res.list
        }
        this.loading = false
      })
    },
    videoFrameHandler(id) {
      this.videoId = id
      this.visible = true
    },
    cancelHandler(flag) {
      this.visible = flag
    }
  }
}
</script>

<style scoped lang='less'>
.video_list_wrapper {
  display: grid;
  grid-gap: 20px 15px;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}
</style>
