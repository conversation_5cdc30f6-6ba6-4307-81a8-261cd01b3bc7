<template>
  <PageContainer>
    <!--Navigation Start-->
    <div slot='page-top' class='pageNav'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-if='crumbs!==""'
          :to='{ path: `/bms/home/<USER>'>
          {{ crumbs }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ videoDetail.videoName }}</el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <section slot='page-left' class='left-page'>
      <div class='vide-title'>
        {{ videoDetail.videoName }}
      </div>

      <div class='player-container'>
        <div class='video-container'>
          <div class='container'>
            <ali-player
              v-if='videoDetail.vid && playAuth'
              :auto-play-delay='0'
              :autoplay='true'
              :cover='$tool.compressImg(videoDetail.cover,795,443)'
              :is-live='false'
              :playauth='playAuth'
              :use-h5-prism='true'
              :vid='videoDetail.vid'
              control-bar-visibility='hover'
              height='100%'
              show-bar-time='3000'
              width='100%'
            >
            </ali-player>
            <img v-else :src='$tool.compressImg(videoDetail.cover,795,443)' alt='视频封面' class='img_cover'>
          </div>
        </div>
        <PlayerFunctionBar
          :diggs='videoDetail.diggs'
          :is-collection='videoDetail.collectStatus === "T"'
          :pv='videoDetail.views'
          :share-data='{
              realName: videoDetail.videoName,
              title: videoDetail.videoName,
              avatarAddress: videoDetail.cover
          }'
          @collectionHandler='collectionHandler'
          @diggsHandler='diggsHandler' />
      </div>

      <Comment
        :title-show='true'
        :comment-list='commentList'
        :see-more-flag='seeMoreFlag'
        @submitCommentsHandler='submitCommentsHandler'
        @submitReplyCommentsHandler='submitReplyCommentsHandler'
        @viewMoreDataSetHandler='viewMoreDataSetHandler'
      />

    </section>

    <section slot='page-right' class='right-page'>
      <div class='right_wrapper'>
        <BmsBrandVideo
          v-if='videoDetail.brandProductLineList && videoDetail.brandProductLineList.length>0'
          :data-list='videoDetail.brandProductLineList'
          :category-id='$route.params.categoryId'
        />
        <RelatedProducts
          v-if='videoDetail.productList && videoDetail.productList.length>0'
          :data-list='videoDetail.productList'
          :category-id='$route.params.categoryId'
        />
        <RelatedRecommendations
          v-if='recommendBmsVideos.length>0'
          :data-list='recommendBmsVideos'
          :category-id='$route.params.categoryId'
        />
      </div>
    </section>
  </PageContainer>
</template>

<script>
import RelatedRecommendations
  from '../../../../../../components/optimize-components/page-components/bms/RelatedRecommendations/index.vue'
import RelatedProducts
  from '../../../../../../components/optimize-components/page-components/bms/RelatedProducts/index.vue'
import BmsBrandVideo from '../../../../../../components/optimize-components/page-components/bms/BmsBrandVideo/index.vue'
import Comment from '../../../../../../components/optimize-components/page-components/bms/Comment/index.vue'
import AliPlayer from '../../../../../../components/AliPlayer/AliPlayer.vue'
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import {
  bmsVideoDigg,
  CollectBmsVideo,
  getBmsVideoComments,
  getBmsVideoDetail,
  getRecommendBmsVideos, getWebApiFirstCategoryList, saveBmsVideoComment
} from '../../../../../../api/bms'
import { getPlayAuth } from '../../../../../../api/guidance'
import PlayerFunctionBar from '@/components/page/detail/PlayerFunctionBar/PlayerFunctionBar'

export default {
  name: 'BrandVideoDetails',
  components: {
    PageContainer,
    Comment,
    AliPlayer,
    PlayerFunctionBar,
    BmsBrandVideo,
    RelatedProducts,
    RelatedRecommendations
  },
  async asyncData({ app, params, error, store, query, req }) {
    const [videoDetail, recommendBmsVideos,firstCategoryData] = await Promise.all([
      app.$axios.$request(getBmsVideoDetail({
        videoId: params.id,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(getRecommendBmsVideos({
        videoId: params.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 5
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    const playAuth = await app.$axios.$request(getPlayAuth({
      vid: videoDetail.result.vid
    }))

    return {
      videoDetail: videoDetail.result,
      playAuth: playAuth.result.authinfo,
      recommendBmsVideos: recommendBmsVideos.list,
      firstCategoryList: firstCategoryData.list
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.videoDetail.videoName} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.videoDetail.description || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断`
        }
      ]
    }
  },
  data() {
    return {
      commentList: [],
      commentPage: {},
      seeMoreFlag: null,              // 查看更多loading开关
      pageSize: 5              // 评论数量
    }
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
      } else {
        return ''
      }

    }
  },
  mounted() {
    this.getCommentListHandler({ pageSize: this.pageSize })
  },
  methods: {
    collectionHandler() {
      this.$axios.$request(CollectBmsVideo({
        videoId: this.$route.params.id
      })).then(res => {
        if (res && res.code === 1) {
          this.videoDetail.collectStatus = res.result.followStatus
        }
      })
    },
    diggsHandler() {
      this.$axios.$request(bmsVideoDigg({
        videoId: this.$route.params.id
      })).then(res => {
        if (res && res.code === 1) {
          if (res.result.diggStatus === 'T') {
            this.videoDetail.diggs = this.videoDetail.diggs + 1
          } else {
            this.videoDetail.diggs = this.videoDetail.diggs - 1
          }

        }
      })
    },
    getCommentListHandler({ pageSize = 5, backFn = null }) {
      this.$axios.$request(getBmsVideoComments({
        videoId: this.$route.params.id,
        loginUserId: this.$store.state.auth.user.id,
        pageNo: 1,
        pageSize
      })).then(res => {
        if (res && res.code === 1) {
          this.commentList = res.list
          this.commentPage = res.page

          if (backFn) {
            backFn(true)
          }

          const count = this.commentPage.totalCount

          if (count <= this.pageSize) {
            this.seeMoreFlag = null
          } else {
            this.seeMoreFlag = false
          }
        }
      })
    },
    /**
     *  @author:Rick  @date:2022/10/20 9:43
     *  提交评论
     */
    submitCommentsHandler({ commentInfo, parentId = null }, backFn) {

      if (commentInfo === '' || !commentInfo) {
        this.$toast('请输入评论内容')
        return
      }

      this.$axios.$request(saveBmsVideoComment({
        videoId: this.$route.params.id,
        text: commentInfo,
        parentId
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getCommentListHandler({ backFn, pageSize: this.pageSize })
        }
      })

    },
    /**
     *  @author:Rick  @date:2022/10/20 13:45
     *  查看更多
     */
    viewMoreDataSetHandler() {
      const count = this.commentPage.totalCount
      this.seeMoreFlag = true
      if (this.pageSize + 5 < (count + 5)) {
        this.pageSize = this.pageSize + 5
        this.getCommentListHandler({ pageSize: this.pageSize })
      }
    },
    /**
     *  @author:Rick  @date:2022/10/20 10:02
     *  提交回复评论
     */
    submitReplyCommentsHandler({ info, parentId }, backFn) {
      this.$analysys.comment('评论留言', '', '', '', [], '', '', '', '', String(this.replyId), '', '评论留言', [], String(document.title), '')
      this.$toast.loading({ message: '提交中...', forbidClick: true })

      this.$axios.$request(saveBmsVideoComment({
        videoId: this.$route.params.id,
        text: info,
        parentId
      })).then((response) => {
        if (response && response.code === 1) {
          this.$toast(response.message)
          this.getCommentListHandler({ backFn, pageSize: this.pageSize })
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
@import "./styles";
</style>
