<template>
  <PageContainer>
    <div class='product_screening_wrapper'>
      <!--Navigation Start-->
      <div class='nav_top' style='margin-bottom: 35px'>
        <bm-breadcrumb>
          <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
          <el-breadcrumb-item
            v-if='crumbs'
            :to='{ path: `/bms/home/<USER>'>
            {{ crumbs }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>
            全部产品
          </el-breadcrumb-item>
        </bm-breadcrumb>
      </div>
      <!--Navigation End-->
      <div class='brand_screening_wrapper'>
        <div v-if="!isHide" style='margin-bottom: 30px'>
          <ProductFilterList
            filter-title='品牌选择：'
            :filter-array='brandList'
            :active-filter-id='brandId'
            @changeSelectHandler='changeSelectBrandHandler'
          />
        </div>
        <div style='margin-bottom: 30px'>
          <ProductFilterList
            filter-title='分类选择：'
            :filter-array='secondClassifyList'
            :active-filter-id='secondCategoryId'
            @changeSelectHandler='changeSelectSecondClassifyHandler'
          />
        </div>
        <ProductFilterList
          v-if="!isHide"
          filter-title='类目选择：'
          :active-filter-id='thirdCategoryId'
          :filter-array='(secondCategoryId===0 || secondCategoryId === null) ? thirdClassifyList : thirdClassifyListArr'
          @changeSelectHandler='changeSelectThirdClassifyHandler'
        />
      </div>

      <div class='product_list_wrapper'>
        <DataLoad :loading='loading' :no-more='productList.length===0'>
          <ul class='product_list'>
            <NewProductsItem
              v-for='(item) in productList'
              :id='item.id'
              :key='item.id'
              :title='item.name'
              :category-name='item.category.name'
              :logo='item.brand.shareImage'
              :brand-name='item.brand.name'
              :img='item.image'
              :category-id='$route.params.categoryId'
              :brand-id='item.brand.id'
              :bms-auth="item.bmsAuth"
              :brand-product-line-id='item.brandProductLine ? item.brandProductLine.id : null'
            />
          </ul>
          <div style='margin-top: 80px'>
            <el-pagination
              v-show='!loading'
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='20'
              :pager-count='$store.state.pager_count'
              :total='productPage.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </DataLoad>
      </div>
    </div>
  </PageContainer>
</template>

<script>
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer2/PageContainer.vue'
import NewProductsItem
  from '../../../../../../components/optimize-components/public/article-types-list/bms/NewProductsItem/index.vue'
import ProductFilterList
  from '../../../../../../components/optimize-components/page-components/bms/ProductFilterList/index.vue'
import {
  getAllSecondCategoryList,
  getCategoryListByParentId, getProductList, getProductListMapsV2,
  getWebApiBrandListByCategoryId, getWebApiFirstCategoryList
} from '../../../../../../api/bms'
import DataLoad from '../../../../../../components/optimize-components/public/DataLoad/index.vue'

export default {
  name: 'ProductScreening',
  components: {
    DataLoad,
    ProductFilterList,
    NewProductsItem,
    PageContainer
  },
  async asyncData({app, params, error, store, query, req}) {
    const [brandList, secondClassifyList, thirdClassifyList, firstCategoryData] = await Promise.all([
      app.$axios.$request(getWebApiBrandListByCategoryId({
        categoryId: params.categoryId,
        orderType: 1
      })),
      app.$axios.$request(getCategoryListByParentId({
        categoryId: params.categoryId
      })),
      app.$axios.$request(getAllSecondCategoryList()),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    let thirdClassifyListArr

    if (query.classifyId) {
      thirdClassifyListArr = await app.$axios.$request(getCategoryListByParentId({
        categoryId: query.classifyId
      }))
    } else {
      thirdClassifyListArr = {list: []}
    }

    let isHide = false
    if (query.from && query.from.includes('product-line')) {
      isHide = true
    }

    return {
      brandList: brandList.list,
      secondClassifyList: secondClassifyList.list,
      thirdClassifyList: thirdClassifyList.list,
      thirdClassifyListArr: thirdClassifyListArr.list,
      firstCategoryList: firstCategoryData.list,
      isHide
    }
  },
  head() {
    return {
      title: `脑医汇 - 产品列表 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,通路,缺血,出血,耗材,手术器械,设备,肿瘤药品,功能药品,血管药品,诊断`
        }
      ]
    }
  },
  data() {
    return {
      currentPage: 1,
      loading: true,
      brandId: Number(this.$route.query.brandId) || null,
      secondCategoryId: Number(this.$route.query.classifyId) || null,
      thirdCategoryId: null,
      productList: [],
      productPage: {}
    }
  },
  computed: {
    crumbs() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
    },
    categoryCode() {
      return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
    }
  },
  watch: {
    secondCategoryId(newValue) {
      if (newValue) {
        this.$axios.$request(getCategoryListByParentId({
          categoryId: newValue
        })).then(response => {
          if (response && response.code === 1) {
            if (response.list.length === 0) {
              this.thirdClassifyListArr = []
            } else {
              this.thirdClassifyListArr = response.list
            }

          }
        })
      } else {
        this.thirdClassifyListArr = []
      }
    }
  },
  mounted() {
    this.getProductListHandler({pageNo: 1})
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-10 17:46
     * 获取产品列表
     * ------------------------------------------------------------------------------
     */
    getProductListHandler({pageNo = 1, isTop = false}) {
      if (isTop) {
        this.$tool.scrollIntoTop()
      }

      this.productList = []
      if (this.$route.query.from && this.$route.query.from.includes('product-line')) {
        const productLineId = this.$route.query.from.split('product-line-')[1]
        this.$axios.$request(getProductListMapsV2({
          brandId: this.brandId,
          brandProductLineId: productLineId,
          categoryId: this.secondCategoryId,
          pageNo,
          pageSize: 20,
          loginUserId: this.$store.state.auth.user.id
        })).then(res => {
          if (res && res.code === 1) {
            this.productList = res.list
            this.productPage = res.page
          }
          this.loading = false
        })
      } else {
        this.$axios.$request(getProductList({
          brandId: this.brandId,
          categoryId: this.thirdCategoryId || this.secondCategoryId || this.$route.params.categoryId,
          categoryRank: this.thirdCategoryId ? 3 : this.secondCategoryId ? 2 : 1,
          pageNo,
          pageSize: 20,
          loginUserId: this.$store.state.auth.user.id
        })).then(res => {
          if (res && res.code === 1) {
            this.productList = res.list
            this.productPage = res.page
          }
          this.loading = false
        })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)
     * @date 2023-03-09 13:08
     * @description: 筛选品牌
     * ------------------------------------------------------------------------------
     */
    changeSelectBrandHandler(id, callback) {
      this.loading = true
      this.currentPage = 1
      this.brandId = id !== 0 ? id : null
      this.getProductListHandler({
        pageNo: 1
      })

      let PATH

      if (this.$route.query.classifyId) {
        PATH = `/bms/classify/${this.$route.params.categoryId}/product-directory?classifyId=${this.$route.query.classifyId}${this.brandId ? '&brandId=' + this.brandId : ''}${this.$route.query.from ? `&from=${this.$route.query.from}` : ''}`
      } else {
        PATH = `/bms/classify/${this.$route.params.categoryId}/product-directory${this.brandId ? '?brandId=' + this.brandId : ''}${this.$route.query.from ? `&from=${this.$route.query.from}` : ''}`
      }

      this.$router.push({
        path: PATH
      })

      callback(true)
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-10 18:03
     * 筛选二级分类
     * ------------------------------------------------------------------------------
     */
    changeSelectSecondClassifyHandler(id, callback) {
      this.loading = true
      this.currentPage = 1
      this.secondCategoryId = id !== 0 ? id : null
      console.log(this.secondCategoryId)
      this.thirdCategoryId = null;
      this.getProductListHandler({
        pageNo: 1
      })

      let PATH

      if (this.$route.query.brandId) {
        PATH = `/bms/classify/${this.$route.params.categoryId}/product-directory?${this.secondCategoryId ? 'classifyId=' + this.secondCategoryId + '&' : ''}brandId=${this.$route.query.brandId}${this.$route.query.from ? `&from=${this.$route.query.from}` : ''}`
      } else {
        PATH = `/bms/classify/${this.$route.params.categoryId}/product-directory${this.secondCategoryId ? '?classifyId=' + this.secondCategoryId : ''}${this.$route.query.from ? `&from=${this.$route.query.from}` : ''}`
      }

      this.$router.push({
        path: PATH
      })

      callback(true)

    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-10 18:03
     * 筛选三级级分类
     * ------------------------------------------------------------------------------
     */
    changeSelectThirdClassifyHandler(id, callback) {
      this.loading = true
      this.currentPage = 1
      this.thirdCategoryId = id !== 0 ? id : null
      this.getProductListHandler({
        pageNo: 1
      })
      callback(true)
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-10 18:28
     * 分页
     * ------------------------------------------------------------------------------
     */
    handleCurrentChange(item) {
      this.loading = true
      this.currentPage = item
      this.getProductListHandler({
        pageNo: item,
        isTop: true
      })
    }
  }
}
</script>

<style scoped lang='less'>
.brand_screening_wrapper {
  margin-bottom: 30px;
}

.product_list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 20px 18px;
  margin-bottom: 5px;
}
</style>
