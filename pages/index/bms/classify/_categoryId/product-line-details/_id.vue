<template>
  <div class='product_line_wrapper container-box'>
    <!--Navigation Start-->
    <div class='nav_top' style='margin: 20px 0 0'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-if='crumbs!==""'
          :to='{ path: `/bms/home/<USER>'>
          {{ crumbs }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ brandDetail.defaultProductLine.name }}
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <!--Navigation End-->
    <PageContainer>
      <section slot='page-left'>
        <div style='margin-bottom: 50px'>
          <LineHead
            :id='brandDetail.defaultProductLine.id'
            :is-follow='brandDetail.defaultProductLine.subscribeStatus === "T"'
            :name='brandDetail.name + " - " + brandDetail.defaultProductLine.name'
            :articles='brandDetail.defaultProductLine.articles'
            :products='brandDetail.defaultProductLine.products'
            :views='brandDetail.defaultProductLine.views'
            :logo='brandDetail.defaultProductLine.shareImage'
            :administratorTF="brandDetail.defaultProductLine.administratorTF"
            @followHandler='followHandler'
          />
        </div>
        <div v-if="productLineList && productLineList.length>0" style='margin-bottom: 50px'>
          <ProductCatalog
            :brand-id='brandDetail.id'
            :product-line-id='brandDetail.defaultProductLine.id'
            :product-line-list='productLineList'
          />
        </div>
        <ProductLineContent
          :id='brandDetail.defaultProductLine.id'
          :tabs-list='tabsList'
          :data-type='"productLine"'
          :name='brandDetail.name + " - " + brandDetail.defaultProductLine.name'
          :logo='brandDetail.defaultProductLine.shareImage'
          :is-follow='brandDetail.defaultProductLine.subscribeStatus === "T"'
          :brand-id="brandDetail.id"
          @followHandler='followHandler'
        />
      </section>
      <section slot='page-right'>
        <div class='right_wrapper'>
          <LiveToday
            v-if='brandTodayMeetingsList && brandTodayMeetingsList.length>0'
            :brand-today-meetings-list='brandTodayMeetingsList'
          />
          <div v-if='brandAdList.length>0' style='margin-bottom: 16px;display: grid;grid-gap:20px 0'>
            <Advertisement
              v-for='item in brandAdList'
              :key='item.adId'
              :img-url='item.image'
              :name='item.name'
              :module='item.module'
              :extras='item.extras'
            />
          </div>
          <InvestmentInformation
            v-if='brandInvestmentInformation && brandInvestmentInformation.length>0'
            :brand-investment-information='brandInvestmentInformation'/>
          <ActivityZone
            v-if='brandActivityList && brandActivityList.length>0'
            :activity-data-list='brandActivityList'
            :category-id='$route.params.categoryId'
          />
          <TeamMembers
            v-if='memberList && memberList.length>0 && brandDetail.defaultProductLine.memberStatus === "T"'
            :brandId="brandDetail.id"
            :administratorTF="brandDetail.defaultProductLine.administratorTF"
            :member-list='memberList'
          />
        </div>
      </section>
    </PageContainer>
    <CommunicationComponent v-if='false'/>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import Advertisement from '../../../../../../components/optimize-components/page-components/bms/Advertisement/index.vue'
import InvestmentInformation
  from '../../../../../../components/optimize-components/page-components/bms/InvestmentInformation/index.vue'
import ProductLineContent
  from '../../../../../../components/optimize-components/page-components/bms/productLineContent/index.vue'
import ActivityZone from '../../../../../../components/optimize-components/page-components/bms/ActivityZone2/index.vue'
import ProductCatalog
  from '../../../../../../components/optimize-components/page-components/bms/product-line/ProductCatalog/index.vue'
import LineHead
  from '../../../../../../components/optimize-components/page-components/bms/product-line/LineHead/index.vue'
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import LiveToday
  from '../../../../../../components/optimize-components/page-components/bms/product-line/LiveToday/index.vue'
import TeamMembers
  from '../../../../../../components/optimize-components/page-components/bms/product-line/TeamMembers/index.vue'
import CommunicationComponent from '../../../../../../components/optimize-components/Communication/index.vue'
import {
  getBrandActivityList,
  getBrandAdList,
  getBrandHomepageDetail,
  getBrandInvestmentInformationList,
  getBrandProductPage,
  getBrandTodayMeetings,
  getMemberListByBrandIdAndLineId,
  getWebApiFirstCategoryList,
  subscribeBrand
} from '../../../../../../api/bms'
import ShortVideoPlayback from '../../../../../../components/optimize-components/public/ShortVideoPlayback/index.vue'
import {saveBrowsingHistory} from "../../../../../../api/browsing-history";
import {getSlotContent} from "../../../../../../api/banner/banner.js";

export default {
  name: 'ProductLine',
  components: {
    ShortVideoPlayback,
    CommunicationComponent,
    LiveToday,
    PageContainer,
    LineHead,
    ProductCatalog,
    ActivityZone,
    TeamMembers,
    ProductLineContent,
    InvestmentInformation,
    Advertisement
  },
  async asyncData({app, params, error, store, query, req}) {
    const brandDetail = await app.$axios.$request(getBrandHomepageDetail({
      brandId: null,
      productLineId: params.id,
      loginUserId: store.state.auth.user.id,
      categoryId: params.categoryId !== '-' ? params.categoryId : null
    }))

    const [productLineList, brandInvestmentInformation, brandAdList, memberListData, brandActivity, brandTodayMeetingsList, firstCategoryData] = await Promise.all([
      app.$axios.$request(getBrandProductPage({
        brandId: brandDetail.result.id,
        productLineId: params.id,
        categoryId: null,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 3
      })),
      app.$axios.$request(getBrandInvestmentInformationList({
        brandId: brandDetail.result.id,
        productLineId: params.id
      })),
      app.$axios.$request(getSlotContent({
        loginUserId: store.state.auth.user.id,
        detailIdStr: `${brandDetail.result.id},${params.id}`,
        adCode: `bms_product_line`,
      })),
      app.$axios.$request(getMemberListByBrandIdAndLineId({
        loginUserId: store.state.auth.user.id,
        brandId: brandDetail.result.id,
        productLineId: params.id
      })),
      app.$axios.$request(getBrandActivityList({
        brandId: brandDetail.result.id,
        productLineId: params.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 10
      })),
      app.$axios.request(getBrandTodayMeetings({
        brandId: brandDetail.result.id,
        productLineId: params.id
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    return {
      brandDetail: brandDetail.result,
      productLineList: productLineList.list,
      brandInvestmentInformation: brandInvestmentInformation.list || [],
      brandAdList: brandAdList.list,
      memberList: memberListData.list,
      brandActivityList: brandActivity.list,
      brandTodayMeetingsList: brandTodayMeetingsList.data.list || [],
      firstCategoryList: firstCategoryData.list
    }
  },
  data() {
    return {
      tabsList: [
        {id: 'RecommendList', tabName: '推荐', isEnable: true},
        {id: 'CaseList', tabName: '病例', isEnable: true},
        {id: 'InfoList', tabName: '资讯', isEnable: true},
        {id: 'ShortVideo', tabName: '短视频', isEnable: true},
        {id: 'RecordedBroadcastList', tabName: '直录播', isEnable: true},
        {id: 'InteractionComment', tabName: '互动', isEnable: true}
      ]
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.brandDetail.name}${this.brandDetail.defaultProductLine.name} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.brandDetail.defaultProductLine.description || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,${this.brandDetail.defaultProductLine.name}`
        }
      ]
    }
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
      } else {
        return ''
      }
    }
  },
  mounted() {
    if (this.$store.state.auth.token) {
      this.$axios.$request(saveBrowsingHistory({
        loginUserId: this.$store.state.auth.user.id,
        contentSource: 8,
        //  浏览内容 id（浏览内容类型的id）
        contentId: this.$route.params.id,
        courseId: null,
        playDuration: null
      }))
    }
  },
  methods: {
    followHandler(id) {
      this.$axios.$request(subscribeBrand({
        brandId: this.brandDetail.id,
        productLineId: id
      })).then(res => {
        if (res && res.code === 1) {
          this.$set(this.brandDetail.defaultProductLine, 'subscribeStatus', res.result.followStatus)
          this.$analysys.add_follow(
            String(''),
            String(''),
            String(''),
            String(this.brandDetail.defaultProductLine.name),
            String(res.result.followStatus),
            '品牌-产线'
          )
        }
      })
    }
  }
}
</script>

<style scoped>
.right_wrapper {
  display: flex;
  flex-direction: column;
  grid-gap: 20px 0;
}
</style>
