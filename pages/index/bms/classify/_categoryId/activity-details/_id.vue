<template>
  <div class='product_line_wrapper container-box'>
    <!--Navigation Start-->
    <div class='nav_top' style='margin: 20px 0 0'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-if='crumbs!==""'
          :to='{ path: `/bms/home/<USER>'>
          {{ crumbs }}
        </el-breadcrumb-item>
        <el-breadcrumb-item
          :to='{ path: `/bms/classify/${$route.params.categoryId}/activity-directory`}'>
          活动专区
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ activityDetail.name }}
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <!--Navigation End-->
    <PageContainer>
      <section slot='page-left'>
        <div style='margin-bottom: 50px'>
          <ActivityHead
            :cover='activityDetail.cover'
            :title='activityDetail.name'
            :views='activityDetail.views'
            :contents='activityDetail.contents'
            :is-follow='activityDetail.subscribeStatus === "T"'
            @followHandler='followHandler'
          />
        </div>
        <TabContent
          :logo='activityDetail.shareImage || activityDetail.brand.shareImage'
          :name='activityDetail.name'
          :is-follow='activityDetail.subscribeStatus === "T"'
          @followHandler='followHandler'
        />
      </section>
      <section slot='page-right'>
        <BmsBrandComponent
          :id='activityDetail.id'
          :product-line-id='activityDetail.brandProductLine ? activityDetail.brandProductLine.id : null'
          :product-line-name='activityDetail.brandProductLine ? activityDetail.brandProductLine.name : null'
          :category-id='$route.params.categoryId'
          :cover='activityDetail.brandProductLine ? activityDetail.brandProductLine.shareImage : activityDetail.brand.shareImage'
          :brand-name='activityDetail.brand.name'
          :views='activityDetail.brandProductLine ? activityDetail.brandProductLine.views : activityDetail.brand.views'
          :contents='activityDetail.brandProductLine ? activityDetail.brandProductLine.products : activityDetail.brand.products'
        />
        <RecommendedZone
          :brand-other-activity-list='BrandOtherActivityList'
        />
      </section>
    </PageContainer>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
  </div>
</template>

<script>
import ActivityHead
  from '../../../../../../components/optimize-components/page-components/bms/activity-details/ActivityHead/index.vue'
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import BmsBrandComponent
  from '../../../../../../components/optimize-components/page-components/bms/activity-details/BmsBrand/index.vue'
import RecommendedZone
  from '../../../../../../components/optimize-components/page-components/bms/activity-details/RecommendedZone/index.vue'
import {
  getBrandActivityDetail,
  getBrandOtherActivityList,
  getWebApiFirstCategoryList,
  subscribeActivity
} from '../../../../../../api/bms'
import TabContent
  from '../../../../../../components/optimize-components/page-components/bms/activity-details/TabContent/index.vue'
import ShortVideoPlayback from '../../../../../../components/optimize-components/public/ShortVideoPlayback/index.vue'

export default {
  name: 'ActivityDetails',
  components: {
    ShortVideoPlayback,
    TabContent,
    RecommendedZone,
    BmsBrandComponent,
    PageContainer,
    ActivityHead
  },
  async asyncData({app, params, error, store, query, req}) {
    const [activityDetail, firstCategoryData] = await Promise.all([
      app.$axios.$request(getBrandActivityDetail({
        loginUserId: store.state.auth.user.id,
        activityId: params.id
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    const BrandOtherActivityList = await app.$axios.$request(getBrandOtherActivityList({
      loginUserId: store.state.auth.user.id,
      activityId: params.id,
      brandId: activityDetail.result.brand.id,
      pageNo: 1,
      pageSize: 10
    }))


    return {
      activityDetail: activityDetail.result,
      BrandOtherActivityList: BrandOtherActivityList.list,
      firstCategoryList: firstCategoryData.list
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.activityDetail.name} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.activityDetail.shareDescription || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,${this.activityDetail.brand.name},${this.activityDetail.brandProductLine.name},${this.activityDetail.name}`
        }
      ]
    }
  },
  data() {
    return {}
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
      } else {
        return ''
      }
    }
  },
  methods: {
    followHandler() {
      this.$axios.request(subscribeActivity({
        activityId: this.activityDetail.id
      })).then(res => {
        if (res && res.data.code === 1) {
          this.$set(this.activityDetail, 'subscribeStatus', res.data.result.followStatus)
          this.$analysys.add_follow(
            String(''),
            String(''),
            String(''),
            String(this.activityDetail.name),
            String(res.data.result.followStatus),
            '品牌-专区'
          )
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
