<template>
  <div class='product_line_wrapper container-box'>
    <!--Navigation Start-->
    <div class='nav_top' style='margin: 20px 0 0'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-if='crumbs!==""'
          :to='{ path: `/bms/home/<USER>'>
          {{ crumbs }}
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ productDetail.name }}
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <!--Navigation End-->
    <PageContainer>
      <section slot='page-left'>
        <div style='margin-bottom: 50px'>
          <ProductHead
            :name='productDetail.name'
            :category-name='productDetail.category.name'
            :head-image-list="productDetail.headImageList"
            :category-id="productDetail.category.id"
            :image='productDetail.image'
            :follow-status='productDetail.followStatus === "T"'
            :identity="memberInfo ? memberInfo.identity : null"
            :bmsAuth="productDetail.bmsAuth"
            @follow='followHandler'
          />
        </div>
        <ProductContent
          :name='productDetail.name'
          :logo='productDetail.image'
          :is-follow='productDetail.followStatus === "T"'
          :tabs-list='tabsList'
          :product-detail='productDetail'
          @followHandler='followHandler'
        />
      </section>
      <section slot='page-right'>
        <div class='right_wrapper'>
          <BmsBrandComponent
            :id='productDetail.brand ? productDetail.brand.id : null'
            :product-line-id='productDetail.brandProductLine ? productDetail.brandProductLine.id : null'
            :product-line-name='productDetail.brandProductLine ? productDetail.brandProductLine.name : null'
            :category-id='$route.params.categoryId'
            :brand-name='productDetail.brand ? productDetail.brand.name : null'
            :cover='productDetail.brandProductLine ? productDetail.brandProductLine.shareImage : productDetail.brand.shareImage'
            :contents='productDetail.brandProductLine ? productDetail.brandProductLine.products : productDetail.brand.products'
            :views='productDetail.brandProductLine ? productDetail.brandProductLine.views : productDetail.brand.views'
          />
          <UserManual
            v-if='productOperationManualList.length>0'
            :product-operation-manual-list='productOperationManualList'
          />
          <RelatedRecommendations
            v-if='recommendProductsList.length>0'
            :recommend-products-list='recommendProductsList'
          />
        </div>
      </section>
    </PageContainer>
    <ShortVideoPlayback
      :video-id='$store.state.bms.bmsHomeShortVideoId'
      :visible='$store.state.bms.bmsHomeShortVisible'
      @cancelFn='(flag) => $store.commit("bms/setBmsHomeShortVideoVisibleHandler",flag)'
    />
    <PopUpAdvertising
      :visible='popupAdsShow'
      :popup-ad-item="popupAdItem"
      @cancelFn='popupAdsShow = false'/>
  </div>
</template>

<script>
import PopUpAdvertising
  from "../../../../../../components/optimize-components/page-components/bms/PopUpAdvertising/index.vue";
import ProductHead
  from '../../../../../../components/optimize-components/page-components/bms/product-details/ProductHead/index.vue'
import PageContainer from '../../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import BmsBrandComponent
  from '../../../../../../components/optimize-components/page-components/bms/activity-details/BmsBrand/index.vue'
import UserManual
  from '../../../../../../components/optimize-components/page-components/bms/product-details/UserManual/index.vue'
import RelatedRecommendations
  from '../../../../../../components/optimize-components/page-components/bms/product-details/RelatedRecommendations/index.vue'
import {
  followProduct,
  getBrandProductDetail,
  getProductOperationManual,
  getRecommendProducts, getWebApiFirstCategoryList
} from '../../../../../../api/bms'
import ProductContent
  from '../../../../../../components/optimize-components/page-components/bms/ProductContent/index.vue'
import ShortVideoPlayback from '../../../../../../components/optimize-components/public/ShortVideoPlayback/index.vue'
import {saveBrowsingHistory} from "../../../../../../api/browsing-history";
import {getMemberInfo} from "../../../../../../api/user";
import {getSlotContent} from "../../../../../../api/banner/banner";

export default {
  name: 'ProductDetails',
  components: {
    ShortVideoPlayback,
    ProductContent,
    RelatedRecommendations,
    UserManual,
    BmsBrandComponent,
    PageContainer,
    ProductHead,
    PopUpAdvertising
  },
  async asyncData({app, params, error, store, query, req}) {
    const [productDetail, productOperationManual, recommendProducts, firstCategoryData] = await Promise.all([
      app.$axios.$request(getBrandProductDetail({
        productId: params.id,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(getProductOperationManual({
        productId: params.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 10
      })),
      app.$axios.$request(getRecommendProducts({
        productId: params.id,
        loginUserId: store.state.auth.user.id,
        pageNo: 1,
        pageSize: 5
      })),
      app.$axios.$request(getWebApiFirstCategoryList())
    ])

    let memberInfo = null
    if (store.state.auth.token) {
      await app.$axios.$request(getMemberInfo({
        userId: store.state.auth.user.id,
      })).then(res => {
        if (res.code === 1) {
          memberInfo = res.result
        }
      })
    }

    return {
      productDetail: productDetail.result,
      productOperationManualList: productOperationManual.list,
      recommendProductsList: recommendProducts.list,
      firstCategoryList: firstCategoryData.list,
      memberInfo
    }
  },
  head() {
    return {
      title: `脑医汇 - ${this.productDetail.name} - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.productDetail.description || '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,${this.productDetail.brand.name},${this.productDetail.brandProductLine ? this.productDetail.brandProductLine.name : null},${this.productDetail.name},${this.productDetail.category.name}`
        }
      ]
    }
  },
  data() {
    return {
      popupAdItem: null,
      popupAdsShow: false,
      tabsList: [
        {id: 'ProductInformation', tabName: '产品信息', isEnable: true},
        {id: 'ModelComponent', tabName: '型号', isEnable: true},
        {id: 'CaseList', tabName: '病例', isEnable: true},
        {id: 'ElabList', tabName: '手术复盘', isEnable: true},
        {id: 'InfoList', tabName: '资讯', isEnable: true},
        {id: 'ShortVideo', tabName: '短视频', isEnable: true},
        {id: 'InteractionComment', tabName: '互动', isEnable: true}
      ]
    }
  },
  mounted() {
    if (this.$store.state.auth.token) {
      this.$axios.$request(saveBrowsingHistory({
        loginUserId: this.$store.state.auth.user.id,
        contentSource: 9,
        //  浏览内容 id（浏览内容类型的id）
        contentId: this.$route.params.id,
        courseId: null,
        playDuration: null
      }))
    }

    this.getBmsPopupAdsByCategoryHandler()
  },
  computed: {
    crumbs() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.name
      } else {
        return ''
      }
    },
    categoryCode() {
      if (this.$route.params.categoryId !== '-') {
        return this.firstCategoryList.filter(item => item.id + '' === this.$route.params.categoryId + '')?.[0]?.code
      } else {
        return ''
      }
    }
  },
  methods: {
    followHandler() {
      this.$axios.$request(followProduct({
        productId: this.$route.params.id
      })).then(res => {
        if (res && res.code === 1) {
          this.$set(this.productDetail, 'followStatus', res.result.followStatus)
          this.$analysys.add_follow(
            String(''),
            String(''),
            String(''),
            String(this.productDetail.name),
            String(res.result.followStatus),
            '品牌-产品'
          )
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-04-12 14:00
     * 广告弹窗  -- 一天弹一次
     * ------------------------------------------------------------------------------
     */
    getBmsPopupAdsByCategoryHandler() {
      this.$axios.$request(
        getSlotContent({
          loginUserId: this.$store.state.auth.user.id,
          detailIdStr: this.$route.params.id,
          adCode: `bms_product`,
        })
      ).then(response => {
        if (response && response.code === 1) {
          const okAdArr = window.localStorage.getItem('productAdsIdArr')
          let newArr = response.list
          if (okAdArr) {
            const adArr = okAdArr.split(',')
            newArr = newArr.filter(x => !adArr.includes(String(x.adId)))
          }


          if (newArr.length > 0) {
            if (okAdArr) {
              window.localStorage.setItem('productAdsIdArr', okAdArr + ',' + newArr[0].adId)
            } else {
              window.localStorage.setItem('productAdsIdArr', newArr[0].adId)
            }

            this.popupAdsShow = true
            this.popupAdItem = newArr[0]
          }
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.right_wrapper {
  display: grid;
  grid-gap: 20px 0;
}
</style>
