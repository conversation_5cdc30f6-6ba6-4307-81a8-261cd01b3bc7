<template>
  <div id="inviteHome" v-show="isShowPage_b">
    <div class="mobileMenu">
      <span>Home > {{getName}}</span>
      <img @click="isShowMobileMenu_fn" :src="nav.icon_menu" alt="">
      <ul class="menuList" :class="nav.isShowMobileMenu?'active':''">
        <li v-for="item in nav.list_a" :key="item.id" v-html="item.name.replace('<br/>',' ')" :class="item.id === nav.active_n?'active':''" @click="handleClick(item)"></li>
      </ul>
    </div>
    <header>
      <a v-show="realTimeWindowWidth > mWidth || (realTimeWindowWidth <= mWidth && nav.active_n === 'BasicInformation')" href="https://forms.office.com/r/J4T3zVmgzk?origin=lprLink" target="_blank">
        <img :src="golink" alt="" class="golinkBtn">
        <img class="background" v-show="realTimeWindowWidth > mWidth || (realTimeWindowWidth <= mWidth && nav.active_n === 'BasicInformation')" :src="nav.banner" alt="">
      </a>
      <nav>
        <ul>
          <li v-for="item in nav.list_a" :key="item.id" v-html="item.name" :class="item.id === nav.active_n?'active':''" @click="handleClick(item)"></li>
        </ul>
      </nav>
    </header>
    <main :style="`background: ${nav.active_bg}`"><div :class="{noPadding:nav.active_n==='PhotoLive'}"><component :is="nav.active_n" :meetingData="meetingData"></component></div></main>
  </div>
</template>
<script>
  import { getAllLiveMeetingDetail } from '@/api/meeting'
  import Agenda from '@/components/optimize-components/Invitation/Agenda'
  import BasicInformation from '@/components/optimize-components/Invitation/BasicInformation'
  import ContactUs from '@/components/optimize-components/Invitation/ContactUs'
  import Committee from '@/components/optimize-components/Invitation/Committee'
  import InvitationLetter from '@/components/optimize-components/Invitation/InvitationLetter'
  import MeetingLive from '@/components/optimize-components/Invitation/MeetingLive'
  import PhotoLive from '@/components/optimize-components/Invitation/PhotoLive'
  import Traffic from '@/components/optimize-components/Invitation/Traffic'
  import History from '@/components/optimize-components/Invitation/History'

  export default {
    name: 'invitionHome',
    components: {
      Agenda, BasicInformation, ContactUs, Committee, InvitationLetter, MeetingLive, PhotoLive, Traffic, History
    },
    head() {
      return {
        title: 'Brain Connectivity Workshop-2024',
        script:[
          { src: '//api.map.baidu.com/api?v=2.0&ak=MaDY2oWcUi9MWGQwMQvB41pplaQ8eMUR', type: 'text/javascript' },
          // { src: '//res.wx.qq.com/open/js/jweixin-1.2.0.js', type: 'text/javascript' },
          // { src: 'https://webapi.amap.com/maps?v=1.4.11&key=e22196035aaa10db3b0b6eb1ab64619e', type: 'text/javascript' },
          // { src: 'http://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js', type: 'text/javascript' },
        ],
        meta:[
          { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no' }
        ]
      }
    },
    async asyncData({ app, params, error, store, query, req }) {
      const [meetingData] = await Promise.all([
        app.$axios.$request(getAllLiveMeetingDetail({
          userId: store.state.auth.user.id,
          // meetingId: params.id,
          meetingId: 3082,  // 部分人打不开页面，先写死id
        }))
      ])

      return {
        meetingData: meetingData.result,
      }
    },
    computed:{
      getName(){
        let name = '';
        this.nav.list_a.map((v)=>{
          if(v.id === this.nav.active_n){
            name = v.name;
          }
        })
        return name.replace('<br/>',' ');
      }
    },
    data() {
      return {
        golink: require('~/assets/images/invitation/2668/goLink.png'),
        nav: {
          icon_menu: require('~/assets/images/invitation/2668/icon_menu.png'),
          isShowMobileMenu: false,
          banner: require('~/assets/images/invitation/2668/banner.png'),
          list_a: [
            { id: 'BasicInformation', name: 'Basic<br/>information', background: '#F8F8F8' },
            // { id: 'InvitationLetter', name: 'Invitation<br/>letter' },
            { id: 'Committee', name: 'Committee', background: '#F8F8F8' },
            { id: 'Agenda', name: 'Agenda', background: '#FFFFFF' },
            { id: 'ContactUs', name: 'Contact us', background: '#FFFFFF'  },
            { id: 'Traffic', name: 'Traffic', background: '#F8F8F8' },
            { id: 'MeetingLive', name: 'Meeting live', background: '#FFFFFF' },
            { id: 'PhotoLive', name: 'Photo live', background: '#FFFFFF' },
            { id: 'History', name: 'History', background: '#FFFFFF' },
          ],
          active_n: 'BasicInformation',
          active_bg: '#F8F8F8',
        },
        isShowPage_b: false,
        // 页面安全宽度
        pcWidth : 1200,
        // 手机安全宽度
        mWidth: 800,
        realTimeWindowWidth: 0,
      }
    },
    mounted() {
      if(this.$route.query.tab){
        this.nav.active_n = this.nav.list_a[this.$route.query.tab].id;
      }

      if(this.meetingData){
        this.$route.query.id = this.$route.params.id
        this.$store.commit('meeting/setMeetingDataHandler', this.meetingData);
        this.$store.commit('meeting/setManualStatusHandler', this.meetingData.defaultFields.manualStatus)
      }
      this.resize_fn();

      let that = this;
      window.addEventListener('beforeunload', (event) => {
        that.isShowPage_b = false
      });
    },
    methods: {
      // 移动端菜单展示开关
      isShowMobileMenu_fn(){
        this.nav.isShowMobileMenu = !this.nav.isShowMobileMenu;
      },
      // 点击nav tab
      handleClick(item) {
        this.nav.active_n = item.id;
        this.nav.active_bg = item.background;
        this.nav.isShowMobileMenu = false;
        let indexTab = 0;
        this.nav.list_a.map((v,i)=>{
          if(this.nav.active_n === v.id){
            indexTab = i;
          }
        })
        this.$router.replace(`/invitation/${this.$route.params.id}?tab=${indexTab}`);
      },
      resize_fn(){
        let that = this;
        (function (doc, win, pcWidth, mWidth) {
          var docEl = doc.documentElement,
            resizeEvt = 'orientationchange' in win ? 'orientationchange' : 'resize';
          var recalc = function () {
            var width = win.outerWidth;
            that.realTimeWindowWidth = width;
            if (width > pcWidth) {
              width = pcWidth ;
            }
            if (width < 320) {
              width = 320 ;
            }
            var winWidth = pcWidth;
            if(width < mWidth){
              winWidth = 375;
            }
            docEl.style.fontSize = 100 * (width / winWidth) + 'px';
          };
          recalc();
          if (!doc.addEventListener) return;
          win.addEventListener(resizeEvt, recalc, false);
        })(document, window, this.pcWidth, this.mWidth);
        this.isShowPage_b = true;
      },
    }
  }
</script>
<style lang="less" scoped>
  #inviteHome{
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    main{
      flex: 1;
    }
  }
  .mobileMenu{
    background: #333;
    line-height: .44rem;
    display: none;
    justify-content: space-between;
    align-items: center;
    padding: 0 .16rem;
    position: sticky;
    top: 0;
    z-index: 100;

    span{
      color: #fff;
      font-size: .14rem;
    }
    img{
      width: .2rem;
      height: .2rem;
    }
    .menuList{
      display: flex;
      flex-direction: column;
      background: #333;
      position: absolute;
      right: 0;
      top: 100%;
      height: 0;
      overflow: hidden;
      transition: all .3s;
      &.active{
        height: 3.52rem;
        transition: all .3s;
      }
      li{
        padding: 0 .16rem;
        flex: 0 0 auto;
        color: #fff;
        &.active{
          background: #009CB2;
        }
      }
    }
  }
  header {
    position: relative;
    display: flex;
    flex-direction: column;

    .background {
      width: 100%;
    }

    // 跳转连接按钮
    .golinkBtn{
      width: 2.8rem;
      position: absolute;
      top: 68%;
      left: 27%;
      img{
        width: 100%;
      }
    }
    @navHeight: 56px;
    nav{
      position: absolute;
      bottom: 0;
      background: rgba(255, 255, 255, 0.08);
      width: 100%;
      ul{
        width: 100%;
        max-width: 1200px;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        overflow: hidden;
        li{
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-size: 16px;
          padding: 7px 10px;
          color: #fff;
          cursor: pointer;
          box-sizing: border-box;
          width: 132px;

          &.active{
            background: #fff;
            color: #000;
          }
        }
      }
    }
  }
  main {
    width: 100%;

    & > div{
      width: 100%;
      max-width: 1259px;
      margin: 0 auto;
      padding-top: 32px;
      padding-bottom: 100px;
      box-sizing: border-box;
    }
  }
  @media screen and (max-width: 1200px){
    header{
      .golinkBtn{
        width: 21%;
      }
    }
  }
  @media screen and (max-width: 800px){
    .mobileMenu{
      display: flex;
    }
    nav{
      display: none;
    }
    main > div{
      padding: .16rem;
      &.noPadding{
        padding: 0;
      }
    }
    header{
      .golinkBtn{
          width: 1.35rem;
      }
    }
  }
  /*@media (min-width: 320px){html{font-size: 42.6667px;} }*/
  /*@media (min-width: 360px){html{font-size: 48px;} }*/
  /*@media (min-width: 375px){html{font-size: 50px;} }*/
  /*@media (min-width: 384px){html{font-size: 51.2px;} }*/
  /*@media (min-width: 414px){html{font-size: 55.2px;} }*/
  /*@media (min-width: 448px){html{font-size: 59.7333px;} }*/
  /*@media (min-width: 480px){html{font-size: 48px;} }*/
  /*@media (min-width: 512px){html{font-size: 68.2667px;} }*/
  /*@media (min-width: 544px){html{font-size: 72.5333px;} }*/
  /*@media (min-width: 576px){html{font-size: 76.8px;} }*/
  /*@media (min-width: 608px){html{font-size: 81.0667px;} }*/
  /*@media (min-width: 640px){html{font-size: 85.3333px;} }*/
  /*@media (min-width: 750px){html{font-size: 100px;} }*/
</style>
