<template>
  <div class='mall_container'>
    <MallNav v-if="!$store.state.cs.TerminalType" :theme='theme'/>
    <router-view></router-view>
    <FooterNav v-if="!$store.state.cs.TerminalType"/>
    <SideBar/>
    <NewPopUp
      v-if='$store.state.mall.customerQrCode'
      :pellucidity='0.4'
      width='220px'
      height='220px'
      :img='require("/assets/images/mall/mall_customer3.png")'
      @cancelFn='(flag) => $store.commit("mall/setCustomerQrCodeHandler",flag)'
    >
      <template #tips>
        <p style='color: #ffffff;margin-bottom: 30px;font-size: 18px'>
          如需修改地址，请添加客服微信
        </p>
      </template>
    </NewPopUp>
  </div>
</template>

<script>
import SideBar from '../../components/optimize-components/page-components/mall/SideBar/index.vue'
import FooterNav from '../../components/optimize-components/public/FooterNav/index.vue'
import MallNav from '../../components/optimize-components/public/MallNav/index.vue'
import NewPopUp from '../../components/optimize-components/UI/NewPopUp/index.vue'

export default {
  name: 'MallPageComponent',
  components: {NewPopUp, FooterNav, MallNav, SideBar},
  async asyncData({app, params, error, store, query, req, route}) {
    let theme
    if (route.path === '/mall' || route.path === '/mall/') {
      theme = 'default'
    } else {
      theme = 'white'
    }

    return {
      theme,
    }
  },
  watch: {
    $route(to, from) {
      if (this.$store.state.auth.token) {
        this.$store.dispatch('mall/getTotalShoppingCartPrice')
      }
      if (to.path === '/mall' || to.path === '/mall/') {
        this.theme = 'default'
      } else {
        this.theme = 'white'
      }
    }
  },
  mounted() {
    if (this.$store.state.auth.token) {
      this.$store.dispatch('mall/getTotalShoppingCartPrice')
    }
    window.addEventListener('scroll', this.scrolling) // 导航固定
  },
  destroyed() {
    window.removeEventListener('scroll', this.scrolling) // 组件销毁时移除监听
  },
  methods: {
    scrolling() {
      // 滚动条距文档顶部的距离
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop

      this.$store.commit('global/setMallScrollHandler', scrollTop)
    }
  }
}
</script>

<style scoped lang='less'>

</style>
