<template>
  <NewPageContainer>
    <div v-if='shoppingCartList.length > 0' class='cart_details_box'>
      <OrderTitle
        msg='全部商品'
      />
      <CartDetails
        :shopping-cart-list='shoppingCartList'
        @getShoppingCartListHandler='getShoppingCartListHandler'
      />
    </div>
    <EmptyCart v-else />
  </NewPageContainer>
</template>

<script>
import OrderTitle from '../../../../components/optimize-components/page-components/mall/OrderTitle/index.vue'
import NewPageContainer from '../../../../components/optimize-components/UI/NewPageContainer/index.vue'
import CartDetails from '../../../../components/optimize-components/page-components/mall/cart/CartDetails/index.vue'
import EmptyCart from '../../../../components/optimize-components/page-components/mall/cart/EmptyCart/index.vue'
import { getShoppingCartList } from '../../../../api/mall'

export default {
  name: 'CartPage',
  components: { EmptyCart, CartDetails, NewPageContainer, OrderTitle },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [shoppingCartList] = await Promise.all([
      app.$axios.$request(getShoppingCartList({
        pageNo: 1,
        pageSize: 1000
      }))
    ])
    return {
      shoppingCartList: shoppingCartList.list
    }
  },
  head() {
    return {
      title: `购物车 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `购物车 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  methods: {
    getShoppingCartListHandler(params, backFn) {
      this.$axios.$request(getShoppingCartList({
        pageNo: 1,
        pageSize: 1000
      })).then(res => {
        if (res && res.code === 1) {
          this.shoppingCartList = res.list
          backFn(true)
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.cart_details_box {
  max-width: 1000px;
  margin: 0 auto;
}
</style>

