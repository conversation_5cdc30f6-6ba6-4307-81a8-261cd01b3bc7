<template>
  <PageContainer>
    <template #page-left>
      <div class='title_tips'>
        {{ merchandiseListData.list[0].osmPress.name }}
      </div>
      <DataLoad :loading='loading' :no-more='merchandiseListData.list[0].merchandiseDtoList.length===0'>
        <div class='books_wrapper'>
          <BooksItem
            v-for='item in merchandiseListData.list[0].merchandiseDtoList'
            :id='item.id'
            :key='item.id'
            :cover='item.cover'
            :name='item.name'
            :price='item.price'
            :discount-price='item.discountPrice'
            :introduction='item.introduction'
          />
        </div>
        <div style='margin: 40px 0'>
          <el-pagination
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='12'
            :pager-count='$store.state.pager_count'
            :total='merchandiseListData.page.totalCount'
            background
            small
            style='text-align: center'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </DataLoad>
    </template>
    <template #page-right>
      <HotBooks :list='hotMerchandiseList' />
    </template>
  </PageContainer>
</template>

<script>
import PageContainer from '../../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import BooksItem from '../../../../../components/optimize-components/public/article-types-list/BooksItem/index.vue'
import HotBooks from '../../../../../components/optimize-components/page-components/mall/HotBooks/index.vue'
import {getHotMerchandiseList, getMerchandiseListByPressId} from '../../../../../api/mall'
import DataLoad from '../../../../../components/optimize-components/public/DataLoad/index.vue'

export default {
  name: 'PressDetail',
  components: { DataLoad, HotBooks, BooksItem, PageContainer },
  async asyncData({ app, params, error, store, query, req }) {
    const [merchandiseListData, hotMerchandiseList] = await Promise.all([
      app.$axios.$request(getMerchandiseListByPressId({
        pressId: params.id,
        pageNo: 1,
        pageSize: 12
      })),
      app.$axios.$request(getHotMerchandiseList({
        limit: 10
      }))
    ])

    return {
      merchandiseListData,
      hotMerchandiseList: hotMerchandiseList.list
    }
  },
  data() {
    return {
      currentPage: 1,
      loading: false
    }
  },
  head() {
    return {
      title: `${this.merchandiseListData.list[0].osmPress.name} - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.merchandiseListData.list[0].osmPress.name} - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  methods: {
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.merchandiseListData.list[0].merchandiseDtoList = []
      this.loading = true
      this.currentPage = item

      this.$axios.$request(getMerchandiseListByPressId({
        pressId: this.$route.params.id,
        pageNo: this.currentPage,
        pageSize: 12
      })).then(response => {
        if (response && response.code === 1) {
          this.merchandiseListData = response
        }
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.loading = false
        }, 500)


      })
    }
  }
}
</script>

<style scoped>
.title_tips {
  padding: 18px 20px;
  background: #F8F8F8;
  border-radius: 6px;
  font-weight: 400;
  font-size: 18px;
  line-height: 150%;
  color: #333333;
  margin-bottom: 20px;
}

.books_wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 30px;
}
</style>
