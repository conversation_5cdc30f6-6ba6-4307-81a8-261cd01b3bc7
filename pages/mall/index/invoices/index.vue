<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的发票
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <OrderSidebar />
      </div>
      <div class='mall_container'>
        <div style='margin-bottom: 20px'>
          <MallTab
            :tab-list='tabList'
            @changeTabHandler='changeTabHandler'
          >
            <template #right>
              <div v-show='activeTab === 1' class='right_invoices_wrapper'>
                <div v-if='Object.keys(InvoiceInformation).length===0' class='add_invoices_btn'
                     @click='isInvoice = true'>
                  添加发票信息
                </div>
                <div v-else class='invoices_info'>
                  <span class='name text-limit-1'>
                    {{ InvoiceInformation.unitName }}
                  </span>
                  <span class='name text-limit-1'>
                    {{ InvoiceInformation.unitTaxId }}
                  </span>
                  <span class='edit_name' @click='isInvoice = true'>
                    修改发票信息
                  </span>
                </div>
              </div>
            </template>
          </MallTab>
        </div>
        <InvoicesTable
          v-show='activeTab === 0'
          :user-invoices-data='userInvoicesData'
        />
        <InvoicesNotAppliedTable
          v-show='activeTab === 1'
          :not-invoice-orders-data='notInvoiceOrdersData'
          @updateInvoicesHandler='updateInvoicesHandler'
        />
        <div style='margin-top: 60px'>
          <el-pagination
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='10'
            :pager-count='$store.state.pager_count'
            :total='activeTab === 0 ? userInvoicesData.page.totalCount : notInvoiceOrdersData.page.totalCount'
            background
            small
            style='text-align: center'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <InvoiceDialog
      v-if='isInvoice'
      :form-data='InvoiceInformation'
      @cancelHandler='isInvoice = false'
      @submitHandler='submitHandler'
    />
  </div>
</template>

<script>
import OrderSidebar from '../../../../components/optimize-components/page-components/mall/OrderSidebar/index.vue'
import MallTab from '../../../../components/optimize-components/page-components/mall/MallTab/index.vue'
import InvoicesTable from '../../../../components/optimize-components/page-components/mall/InvoicesTable/index.vue'
import InvoicesNotAppliedTable
  from '../../../../components/optimize-components/page-components/mall/InvoicesNotAppliedTable/index.vue'
import { getNotInvoiceOrders, getUserInvoices, getUserMyCouponList } from '../../../../api/mall'
import InvoiceDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/InvoiceDialog/index.vue'
import { email, tel } from '../../../../assets/helpers/form-validation'

export default {
  name: 'MallCouponsPage',
  components: { InvoiceDialog, InvoicesNotAppliedTable, InvoicesTable, MallTab, OrderSidebar },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [userInvoicesData, notInvoiceOrdersData] = await Promise.all([
      app.$axios.$request(getUserInvoices({
        module: 'OSM',
        pageNo: 1,
        pageSize: 10
      })),
      app.$axios.$request(getNotInvoiceOrders({
        pageNo: 1,
        pageSize: 10
      }))
    ])

    return {
      userInvoicesData,
      notInvoiceOrdersData
    }
  },
  data() {
    return {
      isInvoice: false,
      InvoiceInformation: {},
      currentPage: 1,
      tabList: [
        { id: 0, tabName: '已申请', isEnable: true },
        { id: 1, tabName: '未申请', isEnable: true }
      ],
      activeTab: 0
    }
  },
  head() {
    return {
      title: `我的发票 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `我的发票 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  mounted() {
    const InvoiceInformation = window.localStorage.getItem('Mall_InvoiceInformation')
    if (InvoiceInformation) {
      this.InvoiceInformation = JSON.parse(InvoiceInformation)
    }
  },
  methods: {
    updateInvoicesHandler() {
      this.currentPage = 1
      this.getUserInvoicesHandler({ pageNo: this.currentPage })
      this.getNotInvoiceOrders({ pageNo: this.currentPage })
    },
    getUserInvoicesHandler({ pageNo = 1 }) {
      this.$axios.$request(getUserInvoices({
        module: 'OSM',
        pageNo,
        pageSize: 10
      })).then(res => {
        if (res && res.code === 1) {
          this.userInvoicesData = res
        }
      })
    },
    getNotInvoiceOrders({ pageNo = 1 }) {
      this.$axios.$request(getNotInvoiceOrders({
        pageNo,
        pageSize: 10
      })).then(res => {
        if (res && res.code === 1) {
          this.notInvoiceOrdersData = res
        }
      })
    },

    /* ------------------------------------------------------------------------------ */



    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      if (this.activeTab === 0) {
        this.getUserInvoicesHandler({ pageNo: item })
      } else {
        this.getNotInvoiceOrders({ pageNo: item })
      }
      this.currentPage = item
    },
    changeTabHandler(id, backFn) {
      this.activeTab = id
      backFn(true)
    },
    submitHandler({ unitName, unitTaxId, contactEmail, contactPhone }) {
      const vilidate = [
        { key: unitName, msg: '单位名称不能为空', type: '' },
        { key: unitTaxId, msg: '单位税号不能为空', type: '' },
        { key: contactEmail, msg: '电子邮箱不能为空', type: 'email', errorMsg: '请输入正确邮箱' },
        { key: contactPhone, msg: '联系电话不能为空', type: 'phone', errorMsg: '请输入正确的手机号' }
      ]

      try {
        vilidate.forEach(item => {
          if (!item.key) {
            throw new Error(`${item.msg}！`)
          } else if (item.type === 'phone' && !tel.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          } else if (item.type === 'email' && !email.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          }
        })
      } catch (e) {
        this.$toast(e.message)
        return
      }

      const obj = {
        unitName,
        unitTaxId,
        contactEmail,
        contactPhone
      }
      window.localStorage.setItem('Mall_InvoiceInformation', JSON.stringify(obj))
      this.InvoiceInformation = obj
      this.isInvoice = false
    }
  }
}
</script>

<style scoped lang='less'>
.right_invoices_wrapper {
  .invoices_info {
    display: flex;
    align-items: center;

    .name {
      display: inline-block;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
      color: #333333;
      margin-left: 20px;
      max-width: 280px;
      overflow: hidden;
    }

    .edit_name {
      display: inline-block;
      font-size: 14px;
      line-height: 150%;
      color: var(--theme-color);
      margin-left: 30px;
      cursor: pointer;
    }
  }

  .add_invoices_btn {
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: var(--theme-color);
    padding: 0 20px;
  }
}

.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }

  .order_list_wrapper_container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
  }
}
</style>
