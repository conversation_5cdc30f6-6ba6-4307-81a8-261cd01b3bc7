<template>
  <div class='mall_index_container'>
    <section class='header_top'>
      <div class='container-box'>
        <img class='header_font' src='~assets/images/mall/mall_index_font.png' alt=''>
      </div>
    </section>
    <NewPageContainer bottom='0'>
      <div class='container_wrapper'>
        <PopularBooks :books-list='hotMerchandiseList'/>
        <AllBooks :books-list='AllMerchandiseList'/>
      </div>
    </NewPageContainer>
    <NotifyTip/>
  </div>
</template>

<script>
import NotifyTip from "../../../components/optimize-components/page-components/mall/NotifyTip/index.vue";
import NewPageContainer from '../../../components/optimize-components/UI/NewPageContainer/index.vue'
import AllBooks from '../../../components/optimize-components/page-components/mall/AllBooks/index.vue'
import PopularBooks from '../../../components/optimize-components/page-components/mall/PopularBooks/index.vue'
import {getMerchandiseList} from '../../../api/mall'

export default {
  name: 'MallIndex',
  components: {PopularBooks, AllBooks, NewPageContainer, NotifyTip},
  async asyncData({app, params, error, store, query, req, res}) {
    const [hotMerchandiseList, AllMerchandiseList] = await Promise.all([
      app.$axios.$request(getMerchandiseList({
        seqType: 1
      })),
      app.$axios.$request(getMerchandiseList({
        seqType: 2
      }))
    ])

    return {
      hotMerchandiseList: hotMerchandiseList.list,
      AllMerchandiseList: AllMerchandiseList.list
    }
  },
  head() {
    return {
      title: `脑医汇 -脑医藏书阁 - 神外资讯 - 神介资讯 - 领先的临床神经科学互联网平台`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: '脑医汇愿成为临床神经科学领域领先的新媒体、医生线上教育、医学传播及数字营销、患者随访管理、远程转诊会诊、临床招募及真实世界数据、技术转化孵化平台。推动中国脑疾病诊疗规范化，共建强大的临床神经科学数字生态。'
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,学术年会,中华医学会,ocin,CNS,在线书城`
        }
      ]
    }
  },
}
</script>

<style scoped lang='less'>
.mall_index_container {
  margin-top: -60px;
}

.header_top {
  width: 100%;
  height: 480px;
  background: #ffffff url("assets/images/mall/mall_index1.jpg") no-repeat;
  background-position: center;
  background-size: cover;
  position: relative;

  .header_font {
    margin: 140px 0 0 80px;
  }

  &::before {
    width: 100%;
    content: "";
    position: absolute;
    bottom: 0;
    box-shadow: 0px 13px 18px 33px #ffffff;
  }

  //background-attachment: fixed;
}

.container_wrapper {
  position: sticky;
  display: grid;
  grid-gap: 40px 0;
  border-radius: 20px 20px 0px 0px;
  background: #FFFFFF;
  margin-top: -140px;
  padding: 30px;
  box-sizing: border-box;
}
</style>
