<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的优惠券
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <OrderSidebar />
      </div>
      <div class='mall_container'>
        <div style='margin-bottom: 20px'>
          <MallTab
            :tab-list='tabList'
            @changeTabHandler='changeTabHandler'
          >
            <template #right>
              <div class='tips' style='font-size: 12px;color: #999999;padding-right: 20px'>
                <span style='cursor: pointer' @click='isIllustrate = true'>优惠券使用说明</span>
              </div>
            </template>
          </MallTab>
        </div>
        <DataLoad
          :tips='status === "F" ? "暂无优惠券" : "暂无优惠券"'
          :loading='false'
          :no-more='status === "F" ? couponListData.list.length === 0 : notCouponListData.list.length === 0'
        >
          <div v-if='status === "F"' class='order_list_wrapper_container'>
            <CouponUseItem
              v-for='item in couponListData.list'
              :key='item.id'
              :item='item'
              :coupon-id='item.id'
              :title='item.title'
              :reduction-amount='item.reductionAmount'
              :preferential-type='item.preferentialType'
              :threshold='item.threshold'
              :end-date='item.endDate'
              :specify-product-ids='item.specifyProductIds'
              @chooseCouponDialogHandler='chooseCouponDialogHandler'
            />
          </div>
          <div v-else class='order_list_wrapper_container'>
            <CouponUseItem
              v-for='item in notCouponListData.list'
              :key='item.id'
              :item='item'
              :coupon-id='item.id'
              :title='item.title'
              :reduction-amount='item.reductionAmount'
              :preferential-type='item.preferentialType'
              :threshold='item.threshold'
              :end-date='item.endDate'
              :is-use='true'
              :specify-product-ids='item.specifyProductIds'
            />
          </div>
          <div style='margin-top: 60px'>
            <el-pagination
              :current-page.sync='currentPage'
              :hide-on-single-page='$store.state.hideOnSinglePage'
              :layout='$store.state.layout'
              :page-size='10'
              :pager-count='$store.state.pager_count'
              :total='status === "F" ? couponListData.page.totalCount : notCouponListData.page.totalCount'
              background
              small
              style='text-align: center'
              @current-change='handleCurrentChange'
            >
            </el-pagination>
          </div>
        </DataLoad>
      </div>
    </div>
    <CouponIllustrateDialog
      v-if='isIllustrate'
      @cancel='isIllustrate = false'
    />
    <ChooseCouponDialog
      v-if='isChooseCouponDialog'
      :choose-coupon-list='chooseCouponList'
      :coupon-id='chooseCouponId'
      @cancelHandler='isChooseCouponDialog = false'
    />
  </div>
</template>

<script>
import OrderSidebar from '../../../../components/optimize-components/page-components/mall/OrderSidebar/index.vue'
import MallTab from '../../../../components/optimize-components/page-components/mall/MallTab/index.vue'
import CouponUseItem from '../../../../components/optimize-components/page-components/mall/CouponUseItem/index.vue'
import { getMerchandiseListByCouponId, getUserMyCouponList } from '../../../../api/mall'
import DataLoad from '../../../../components/optimize-components/public/DataLoad/index.vue'
import CouponIllustrateDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/CouponIllustrateDialog/index.vue'
import ChooseCouponDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/ChooseCouponDialog/index.vue'

export default {
  name: 'MallCouponsPage',
  components: { ChooseCouponDialog, CouponIllustrateDialog, DataLoad, CouponUseItem, MallTab, OrderSidebar },
  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [couponListData, notCouponListData] = await Promise.all([
      app.$axios.$request(getUserMyCouponList({
        status: 'F',
        pageNo: 1,
        pageSize: 10
      })),
      app.$axios.$request(getUserMyCouponList({
        status: 'T',
        pageNo: 1,
        pageSize: 10
      }))
    ])

    return {
      couponListData,
      notCouponListData
    }
  },
  data() {
    return {
      chooseCouponId: null,
      isChooseCouponDialog: false,
      chooseCouponList: [],
      isIllustrate: false,
      status: 'F',
      currentPage: 1,
      tabList: [
        { id: 'F', tabName: '可使用', isEnable: true },
        { id: 'T', tabName: '已失效', isEnable: true }
      ]
    }
  },
  head() {
    return {
      title: `我的优惠券 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `我的优惠券 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  mounted() {

  },
  methods: {
    chooseCouponDialogHandler(couponId) {
      this.$axios.$request(getMerchandiseListByCouponId({
        couponId,
        limit: 5
      })).then(res => {
        if (res.code === 1) {
          this.chooseCouponList = res.list
          this.chooseCouponId = couponId
          this.isChooseCouponDialog = true
        }
      })
    },
    getUserMyCouponListHandler({ pageNo = 1, status = 'F' }) {
      this.$axios.$request(getUserMyCouponList({
        status,
        pageNo,
        pageSize: 10
      })).then(res => {
        if (res.code === 1) {
          if (status === 'F') {
            this.couponListData = res
          } else {
            this.notCouponListData = res
          }
        }
      })
    },
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.currentPage = item
      this.getUserMyCouponListHandler({ pageNo: item, status: this.status })
    },
    changeTabHandler(id, backFn) {
      this.currentPage = 1
      this.status = id
      this.getUserMyCouponListHandler({ pageNo: 1, status: this.status })
      backFn(true)
    }
  }
}
</script>

<style scoped lang='less'>
.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }

  /deep/ .order_list_wrapper_container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
  }
}
</style>
