<template>
  <NewPageContainer>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/mall/coupons' }">
          我的优惠券
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          使用优惠券书籍
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>

    <div class='book_wrapper_content'>
      <BooksCouponItem
        v-for='item in couponList'
        :id='item.id'
        :key='item.id'
        :cover='item.cover'
        :name='item.name'
        :price='item.price'
        :discount-price='item.discountPrice'
        :introduction='item.introduction'
        :press-name='item.pressName'
      />
    </div>
  </NewPageContainer>
</template>

<script>
import NewPageContainer from '../../../../../components/optimize-components/UI/NewPageContainer/index.vue'
import {getMerchandiseListByCouponId} from '../../../../../api/mall'
import BooksCouponItem
  from '../../../../../components/optimize-components/public/article-types-list/BooksCouponItem/index.vue'

export default {
  name: 'CouponsBooks',
  components:{ BooksCouponItem, NewPageContainer},
  async asyncData({ app, params, error, store, query, req }){
    const [request1] = await Promise.all([
      app.$axios.$request(getMerchandiseListByCouponId({
        couponId:params.id
      }))
    ])

    return {
      couponList : request1.list
    }
  },
  head() {
    return {
      title: `更多书籍 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `更多书籍 - 脑医藏书阁 - 脑医汇`
        },
      ]
    }
  },
}
</script>

<style scoped lang='less'>
.book_wrapper_content{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 30px;
  padding: 0 29px;
}
</style>
