<template>
  <NewPageContainer>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/mall/orders'}">
          我的订单
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          售后详情
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <MallPageContainer>
      <AfterSalesNumber
        :order-detail='orderDetail'
      />
    </MallPageContainer>
  </NewPageContainer>
</template>

<script>
import NewPageContainer from '../../../../../components/optimize-components/UI/NewPageContainer/index.vue'
import MallPageContainer
  from '../../../../../components/optimize-components/page-components/mall/MallPageContainer/index.vue'
import AfterSalesNumber
  from '../../../../../components/optimize-components/page-components/mall/AfterSalesNumber/index.vue'
import { getAfterSalesDetail } from '../../../../../api/mall'

export default {
  name: 'ServiceDetailPage',
  components: { AfterSalesNumber, MallPageContainer, NewPageContainer },
  async asyncData({ app, params, error, store, query, req, redirect, route }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [afterSalesDetail] = await Promise.all([
      app.$axios.$request(getAfterSalesDetail({
        afterSalesId: params.id
      }))
    ])

    return {
      orderDetail: afterSalesDetail.result
    }
  },
  head() {
    return {
      title: `售后详情 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `售后详情 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  }
}
</script>

<style scoped>

</style>
