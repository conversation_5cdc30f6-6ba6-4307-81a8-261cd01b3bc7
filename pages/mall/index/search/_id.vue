<template>
  <PageContainer bottom='60px'>
    <template #page-left>
      <DataLoad :loading='loading' :no-more='merchandiseData.list.length === 0' loading-height='60vh'>
        <div class='books_wrapper'>
          <BooksSearchItem
            v-for='item in merchandiseData.list'
            :id='item.id'
            :key='item.id'
            :cover='item.cover'
            :name='item.name'
            :price='item.price'
            :discount-price='item.discountPrice'
            :introduction='item.introduction'
          />
        </div>
        <div style='margin: 40px 0'>
          <el-pagination
            :current-page.sync='currentPage'
            :hide-on-single-page='$store.state.hideOnSinglePage'
            :layout='$store.state.layout'
            :page-size='12'
            :pager-count='$store.state.pager_count'
            :total='merchandiseData.page.totalCount'
            background
            small
            style='text-align: center'
            @current-change='handleCurrentChange'
          >
          </el-pagination>
        </div>
      </DataLoad>
    </template>
    <template #page-right>
      <HotBooks :list='hotMerchandiseList' />
    </template>
  </PageContainer>
</template>

<script>
import PageContainer from '../../../../components/optimize-components/UI/PageContainer/PageContainer.vue'
import BooksSearchItem
  from '../../../../components/optimize-components/public/article-types-list/BooksSearchItem/index.vue'
import HotBooks from '../../../../components/optimize-components/page-components/mall/HotBooks/index.vue'
import { getHotMerchandiseList, getMerchandiseListByKeyWords } from '../../../../api/mall'
import DataLoad from '../../../../components/optimize-components/public/DataLoad/index.vue'

export default {
  name: 'MallSearchPage',
  components: { DataLoad, HotBooks, BooksSearchItem, PageContainer },
  async asyncData({ app, params, error, store, query, req }) {
    const [merchandiseData, hotMerchandiseList] = await Promise.all([
      app.$axios.$request(getMerchandiseListByKeyWords({
        keywords: query.keywords,
        pageNo: 1,
        pageSize: 12
      })),
      app.$axios.$request(getHotMerchandiseList({
        limit: 10
      }))
    ])

    return {
      merchandiseData,
      hotMerchandiseList: hotMerchandiseList.list
    }
  },
  watchQuery: true,
  data() {
    return {
      currentPage: 1,
      loading: false
    }
  },
  head() {
    return {
      title: `搜索 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.$route.query.keywords}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  methods: {
    handleCurrentChange(item) {
      this.$tool.scrollIntoTop()
      this.currentPage = item
      this.loading = true
      this.merchandiseData.list = []

      this.$axios.$request(getMerchandiseListByKeyWords({
        keywords: this.$route.query.keywords,
        pageNo: this.currentPage,
        pageSize: 12
      })).then(response => {
        // eslint-disable-next-line no-undef
        setTimeout(() => {
          this.loading = false
        }, 500)

        if (response && response.code === 1) {
          this.merchandiseData = response
        }
      })
    }
  }
}
</script>

<style scoped>
.books_wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 30px;
}
</style>
