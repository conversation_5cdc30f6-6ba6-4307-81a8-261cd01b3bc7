<template>
  <div class='pay_success_wrapper'>
    <div class='success_content'>
      <p class='title'>
        <svg-icon icon-class='mall_sucess' class-name='icons' />
        <span>支付成功</span>
      </p>
      <div class='pay_money'>
        <span>实付：</span>
        <MallPrice :price='price' font-size='20px' decimal-font-size='16px' />
      </div>
      <div class='btn_wrapper'>
        <nuxt-link replace :to='{path:`/mall`}' class='btn back_btn'>
          返回首页
        </nuxt-link>
        <nuxt-link replace :to='{path:`/mall/orders`}' class='btn look_btn'>
          查看详情
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
import MallPrice from '../../../../components/optimize-components/page-components/mall/MallPrice/index.vue'
import { getPaymentPriceByOrderNumber } from '../../../../api/mall'

export default {
  name: 'SuccesspayPage',
  components: { MallPrice },
  data() {
    return {
      price: 0,
      orderId: null
    }
  },
  mounted() {
    this.$axios.$request(getPaymentPriceByOrderNumber({
      orderNumber: this.$route.params.id
    })).then(res => {
      if (res.code === 1) {
        this.price = res.result.paymentPrice
        this.orderId = res.result.id
      }
    })
  }
}
</script>

<style scoped lang='less'>
.pay_success_wrapper {
  height: calc(100vh - 365px - 60px);
  position: relative;

  .success_content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    .title {
      font-weight: 400;
      font-size: 30px;
      line-height: 150%;
      color: #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      .icons {
        width: 23.4px;
        height: 23.4px;
        margin-right: 11.3px;
      }
    }

    .pay_money {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 60px;
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      color: #666666;
    }

    .btn_wrapper {
      display: flex;
      align-items: center;
      grid-gap: 0 100px;

      .btn {
        width: 120px;
        height: 48px;
        background: #0581CE;
        border-radius: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 16px;
        line-height: 150%;
        cursor: pointer;
        user-select: none;
      }

      .back_btn {
        background: #0581CE;
        color: #FFFFFF;
      }

      .look_btn {
        background: #F2F9FD;
        color: #0581CE;
      }
    }
  }
}
</style>
