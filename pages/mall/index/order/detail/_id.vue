<template>
  <NewPageContainer>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/mall/orders' }">
          我的订单
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          订单详情
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <MallPageContainer>
      <section style='margin-bottom: 20px'>
        <OrderProcess
          :order-number='orderDetail.orderNumber'
          :create-time='orderDetail.createTime'
          :status='orderDetail.status'
          :pay-type='orderDetail.payType'
        />
      </section>
      <section style='margin-bottom: 20px'>
        <OrderingInformation
          :consignee='orderDetail.consignee'
          :address='orderDetail.address'
          :district1='orderDetail.district1'
          :district2='orderDetail.district2'
          :district3='orderDetail.district3'
          :phone-no='orderDetail.phoneNo'
          :pay-type='orderDetail.payType'
          :invoice-status='orderDetail.invoiceStatus'
          :status='orderDetail.status'
          :invoice-not-status-hand='invoiceNotStatusHand'
          @addInvoices='isInvoice = true'
          @lookInvoices='lookInvoices'
        />
      </section>
      <section style='margin-bottom: 20px'>
        <LogisticsInformation
          v-if='orderDetail.osmExpressInformation'
          :osm-express-information='orderDetail.osmExpressInformation'
        />
      </section>
      <CommodityInformationEdit
        :status='orderDetail.status'
        :osm-order-item-list='orderDetail.osmOrderItemList'
        @applyHandler='applyHandler'
      />
      <OrderPrice
        :is-btn='false'
        :total-amount='totalPrice'
        :freight='orderDetail.transportationExpenses'
        :discount-amount='reductionAmount'
        :actual-payment='orderDetail.totalPrice'
      />
      <ServiceDialog
        v-if='isServiceDialog'
        :num='serviceOrderItemNum'
        :status='orderDetail.status'
        @cancelHandler='isServiceDialog = false'
        @submitHandler='submitServiceHandler'
      />
      <InvoiceDialog
        v-if='isInvoice'
        submit-msg='申请开票'
        :form-data='InvoiceInformation'
        @cancelHandler='isInvoice = false'
        @submitHandler='submitHandler'
      />
    </MallPageContainer>
  </NewPageContainer>
</template>

<script>
import MallPageContainer
  from '../../../../../components/optimize-components/page-components/mall/MallPageContainer/index.vue'
import NewPageContainer from '../../../../../components/optimize-components/UI/NewPageContainer/index.vue'
import OrderProcess
  from '../../../../../components/optimize-components/page-components/mall/order/OrderProcess/index.vue'
import OrderingInformation
  from '../../../../../components/optimize-components/page-components/mall/order/OrderingInformation/index.vue'
import LogisticsInformation
  from '../../../../../components/optimize-components/page-components/mall/order/LogisticsInformation/index.vue'
import CommodityInformationEdit
  from '../../../../../components/optimize-components/page-components/mall/order/CommodityInformationEdit/index.vue'
import OrderPrice from '../../../../../components/optimize-components/page-components/mall/order/OrderPrice/index.vue'
import ServiceDialog
  from '../../../../../components/optimize-components/page-components/mall/dialog/ServiceDialog/index.vue'
import {
  applyForAfterSales,
  getOrderDetail,
  getOrderInvoicePrice,
  getOrderItem,
  orderInvoice
} from '../../../../../api/mall'
import InvoiceDialog
  from '../../../../../components/optimize-components/page-components/mall/dialog/InvoiceDialog/index.vue'
import {email, tel} from '../../../../../assets/helpers/form-validation'

export default {
  name: 'OrderDetailPage',
  components: {
    InvoiceDialog,
    ServiceDialog,
    OrderPrice,
    CommodityInformationEdit,
    LogisticsInformation,
    OrderingInformation,
    OrderProcess,
    NewPageContainer,
    MallPageContainer
  },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    const token = store.state.auth.token
    if (!token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [orderDetail] = await Promise.all([
      app.$axios.$request(getOrderDetail({
        orderId: params.id
      }))
    ])

    let totalPrice = 0
    orderDetail.result.osmOrder.osmOrderItemList.forEach(item => {
      totalPrice += (item.originalPrice * item.num)
    })

    return {
      orderDetail: orderDetail.result.osmOrder,
      reductionAmount: orderDetail.result.reductionAmount,
      totalPrice
    }
  },
  data() {
    return {
      isServiceDialog: false,
      serviceOrderItemId: null,
      serviceOrderItemNum: 1,
      isInvoice: false,
      InvoiceInformation: {},
      invoiceNotStatusHand: false,
      servicePrice: 0,
      refundPrice: 0
    }
  },
  head() {
    return {
      title: `订单详情 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `订单详情 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  mounted() {
    const form = JSON.parse(window.localStorage.getItem('Mall_InvoiceInformation'))
    if (form) {
      this.InvoiceInformation = form
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-14 17:48
     * 查看发票
     * ------------------------------------------------------------------------------
     */
    lookInvoices() {
      if (this.orderDetail.electronicInvoice) {
        window.open(this.orderDetail.electronicInvoice, '_blank')
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-08 16:50
     * 售后
     * afterSalesStatus：售后状态，1代表售后中，2代表已申请售后，3代表售后结束，4代表正常状态
     * ------------------------------------------------------------------------------
     */
    applyHandler({ id, num, price, availableQuantity }) {
      this.$analysys.btn_click('申请售后', document.title)
      if (availableQuantity > 0) {
        this.isServiceDialog = true
        this.serviceOrderItemId = id
        this.serviceOrderItemNum = availableQuantity
        this.servicePrice = price
        this.$axios.$request(getOrderItem({
          orderItemId: id
        })).then(res => {
          if (res.code === 1) {
            this.refundPrice = (res.result.paidPrice + res.result.transportationExpenses) / res.result.num
          }
        })
      } else {
        this.$router.push({
          path: `/mall/orders?type=service&&itemId=${id}`
        })
      }
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-08 14:12
     * 开票
     * ------------------------------------------------------------------------------
     */
    submitHandler({ unitName, unitTaxId, contactEmail, contactPhone }) {
      const vilidate = [
        { key: unitName, msg: '单位名称不能为空', type: '' },
        { key: unitTaxId, msg: '单位税号不能为空', type: '' },
        { key: contactEmail, msg: '电子邮箱不能为空', type: 'email', errorMsg: '请输入正确邮箱' },
        { key: contactPhone, msg: '联系电话不能为空', type: 'phone', errorMsg: '请输入正确的手机号' }
      ]

      try {
        vilidate.forEach(item => {
          if (!item.key) {
            throw new Error(`${item.msg}！`)
          } else if (item.type === 'phone' && !tel.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          } else if (item.type === 'email' && !email.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          }
        })
      } catch (e) {
        this.$toast(e.message)
        return
      }

      const obj = {
        unitName,
        unitTaxId,
        contactEmail,
        contactPhone
      }

      this.$toast.loading({ message: '请稍候' })
      this.$axios.$request(getOrderInvoicePrice({
        orderId: this.$route.params.id
      })).then(resOrder => {
        if (resOrder.code === 1) {
          this.$axios.$request(orderInvoice({
            orderIdsStr: String(this.$route.params.id),
            totalPrice: resOrder.result.invoicePrice,
            unitName,
            unitTaxId,
            contactPhone,
            type: 'E',
            contactEmail
          })).then(res => {
            if (res.code === 1) {
              this.$toast.loading().clear()
              this.$toast('申请成功!')
              window.localStorage.setItem('Mall_InvoiceInformation', JSON.stringify(obj))
              this.InvoiceInformation = obj
              this.isInvoice = false
              this.invoiceNotStatusHand = true
            } else {
              this.$toast.loading().clear()
              this.$toast(res.message)
            }
          })
        } else {
          this.$toast(resOrder.message)
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-08 17:13
     * 售后
     * ------------------------------------------------------------------------------
     */
    submitServiceHandler(form) {
      this.$analysys.btn_click('提交售后申请', document.title)
      this.$toast.loading({ message: '请稍后' })
      const { refundType, refundInstruction, applyReason, num, images } = form

      const newImage = images.map(item => {
        return {
          image: item.image
        }
      })

      this.$axios.$request(applyForAfterSales({
        orderId: this.$route.params.id,
        orderItemId: this.serviceOrderItemId,
        applyReason,
        refundInstruction,
        refundPrice: (this.refundPrice * num).toFixed(2),
        images: JSON.stringify(newImage),
        num,
        refundType
      })).then(res => {
        if (res.code === 1) {
          this.$toast.loading().clear()
          this.isServiceDialog = false
          this.$router.push({
            path: `/mall/service/detail/${res.result.id}`
          })
        } else {
          this.$toast(res.message)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
