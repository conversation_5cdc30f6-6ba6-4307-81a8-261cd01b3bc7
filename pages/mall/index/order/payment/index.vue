<template>
  <MallPageContainer>
    <OrderTitle
      msg='订单提交成功'
    />
    <p class='pay_tips'>
      请在<span class='number'>29分05秒</span>内完成支付，否则订单将被取消。
    </p>
    <CommodityInformation :merchandise-dto-list='merchandiseDtoList' />
    <div style='margin-top: 34px;padding-bottom: 200px'>
      <PaymentOptions />
    </div>
  </MallPageContainer>
</template>

<script>
import MallPageContainer
  from '../../../../../components/optimize-components/page-components/mall/MallPageContainer/index.vue'
import OrderTitle from '../../../../../components/optimize-components/page-components/mall/OrderTitle/index.vue'
import CommodityInformation
  from '../../../../../components/optimize-components/page-components/mall/order/CommodityInformation/index.vue'
import PaymentOptions
  from '../../../../../components/optimize-components/page-components/mall/order/PaymentOptions/index.vue'

export default {
  name: 'PaymentPage',
  components: { PaymentOptions, CommodityInformation, OrderTitle, MallPageContainer },
  data() {
    return {
      merchandiseDtoList: []
    }
  },
  mounted() {
    this.merchandiseDtoList = JSON.parse(window.localStorage.getItem('ShoppingCartList')) || []
  }
}
</script>

<style scoped lang='less'>
.pay_tips {
  margin-bottom: 30px;
  font-weight: 400;
  font-size: 16px;
  line-height: 150%;
  color: #666666;

  .number {
    color: var(--theme-color);
  }
}
</style>
