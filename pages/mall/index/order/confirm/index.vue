<template>
  <MallPageContainer>
    <OrderTitle
      msg='订单确认'
    />
    <div style='margin-bottom: 30px'>
      <MallAddress
        :address-list='addressList'
        @getDeliveryInfoListHandler='getDeliveryInfoListHandler'
      />
    </div>
    <div style='margin-bottom: 30px'>
      <CommodityInformation :merchandise-dto-list='merchandiseDtoList' />
    </div>
    <div class='commodity_information_wrapper'>
      <MallCoupon :coupon-list='couponList' />
      <MallInvoice />
      <MallDelivery :merchandise-dto-list='merchandiseDtoList' />
      <MallBuyerMessage />
    </div>
    <OrderPrice
      :total-amount='totalPrice'
      :freight='$store.state.mall.transportationExpenses'
      :discount-amount='$store.state.mall.couponInfo.reductionAmount'
      @submitOrderHandler='submitOrderHandler'
    />
    <PaymentDialog
      v-if='payQr'
      :pay-type='$store.state.mall.payType'
      :qr-code='qrCode'
      :qr-html='qrHtml'
      :order-number='orderNumber'
      @cancel='paymentCancel'
      @paySuccessHandler='paySuccessHandler'
    />
  </MallPageContainer>
</template>

<script>
import qrcode from 'qrcode'
import OrderTitle from '../../../../../components/optimize-components/page-components/mall/OrderTitle/index.vue'
import MallAddress from '../../../../../components/optimize-components/page-components/mall/order/MallAddress/index.vue'
import CommodityInformation
  from '../../../../../components/optimize-components/page-components/mall/order/CommodityInformation/index.vue'
import MallCoupon from '../../../../../components/optimize-components/page-components/mall/order/MallCoupon/index.vue'
import MallInvoice from '../../../../../components/optimize-components/page-components/mall/order/MallInvoice/index.vue'
import MallDelivery
  from '../../../../../components/optimize-components/page-components/mall/order/MallDelivery/index.vue'
import MallBuyerMessage
  from '../../../../../components/optimize-components/page-components/mall/order/MallBuyerMessage/index.vue'
import MallPageContainer
  from '../../../../../components/optimize-components/page-components/mall/MallPageContainer/index.vue'
import OrderPrice from '../../../../../components/optimize-components/page-components/mall/order/OrderPrice/index.vue'
import { generateOrder, getDeliveryInfoList, getUserOsmCouponList } from '../../../../../api/mall'
import PaymentDialog
  from '../../../../../components/optimize-components/page-components/mall/dialog/PaymentDialog/index.vue'

export default {
  name: 'ConfirmPageComponent',
  components: {
    PaymentDialog,
    OrderPrice,
    MallPageContainer,
    MallBuyerMessage,
    MallDelivery,
    MallInvoice,
    MallCoupon,
    CommodityInformation,
    MallAddress,
    OrderTitle
  },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [DeliveryInfoList] = await Promise.all([
      app.$axios.$request(getDeliveryInfoList())
    ])

    DeliveryInfoList.list.forEach(item => {

      if (item.isDefault === 'T') {
        store.commit('mall/setDeliveryInfoIdHandler', item.id)
        item.isChecked = true
      }
    })

    const addressList = DeliveryInfoList.list

    return {
      addressList
    }
  },
  head() {
    return {
      title: `订单结算页 - 脑医藏书阁 -脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `订单结算页 - 脑医藏书阁 -脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },

  data() {
    return {
      orderNumber: null,
      orderID: null,
      totalPrice: 0,
      merchandiseDtoList: [],
      merchandiseJson: [],
      couponList: [],
      payQr: false,
      qrCode: '',
      qrHtml: ''
    }
  },
  mounted() {
    this.merchandiseDtoList = JSON.parse(window.localStorage.getItem('ShoppingCartList')) || []
    if (!this.merchandiseDtoList || this.merchandiseDtoList.length === 0) {
      this.$toast('商品信息有误,请重新操作!')
      window.location.replace('/mall')
      return
    }

    this.merchandiseDtoList.forEach(item => {
      this.totalPrice += (item.discountPrice * item.num) || (item.price * item.num)
    })
    this.merchandiseJson = this.merchandiseDtoList.map(item => {
      return {
        id: item.id,
        num: item.num
      }
    })

    this.getUserOsmCouponListHander()
  },
  methods: {
    paySuccessHandler(orderNumber) {
      this.payQr = false
      this.$router.replace({
        path: `/mall/successpay/${orderNumber}`
      })
    },
    paymentCancel() {
      this.payQr = false
      this.$router.replace({
        path: `/mall/order/detail/${this.orderID}`
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-06 11:29
     * 提交订单
     * ------------------------------------------------------------------------------
     */
    submitOrderHandler(data, backFn) {
      const countTotalPrice = this.totalPrice + this.$store.state.mall.transportationExpenses - (this.$store.state.mall.couponInfo.reductionAmount ? this.$store.state.mall.couponInfo.reductionAmount : 0)
      this.$analysys.btn_click('确定订单', document.title)
      this.$axios.$request(generateOrder({
        deliveryInfoId: this.$store.state.mall.deliveryInfoId,
        totalPrice: countTotalPrice.toFixed(2),
        transportationExpenses: this.$store.state.mall.transportationExpenses,
        couponId: this.$store.state.mall.couponInfo.id,
        merchandiseJson: JSON.stringify(this.merchandiseJson),
        payType: this.$store.state.mall.payType === 'weixin-payment' ? 'W' : 'A',
        buyerMessage: this.$store.state.mall.buyerMessage,
        type: 'E',
        unitName: this.$store.state.mall.InvoiceInformation.unitName,
        unitTaxId: this.$store.state.mall.InvoiceInformation.unitTaxId,
        contactEmail: this.$store.state.mall.InvoiceInformation.contactEmail,
        contactPhone: this.$store.state.mall.InvoiceInformation.contactPhone
      })).then(res => {
        if (res && res.code === 1) {
          this.qrCode = ''
          this.qrHtml = ''
          backFn(true)
          this.orderNumber = res.result.orderNumber
          this.orderID = res.result.orderId

          this.qrCode = res.result.code_url
          switch (this.$store.state.mall.payType) {
            case 'weixin-payment': {
              qrcode.toDataURL(this.qrCode).then((img) => {
                this.qrCode = img
              }).catch((err) => {
                console.log(err)
              })

              this.payQr = true
              break
            }
            case 'alipay-payment': {
              // 支付宝支付
              const actionHref = /action\=\"([\w|\W]+json)/.exec(res.result.orderInfoString)[1]
              const div = document.createElement('div')
              div.innerHTML = res.result.orderInfoString
              const dataVal = div.querySelector('input[name="biz_content"]').value
              this.qrHtml = String(actionHref + '&biz_content=' + dataVal)
              this.payQr = true
              break
            }
          }

        }

        if (res && res.code === -3) {
          this.$toast(res.message)
        }
      })
    },
    getDeliveryInfoListHandler() {
      this.$axios.$request(getDeliveryInfoList()).then(res => {
        if (res && res.code === 1) {

          if (this.$store.state.mall.deliveryInfoId) {
            res.list.forEach(item => {
              if (item.id === this.$store.state.mall.deliveryInfoId) {
                item.isChecked = true
                this.$store.commit('mall/setDeliveryInfoIdHandler', item.id)
              }
            })
          } else {
            res.list.forEach(item => {
              if (item.isDefault === 'T') {
                item.isChecked = true
                this.$store.commit('mall/setDeliveryInfoIdHandler', item.id)
              }
            })
          }

          this.$store.commit('mall/setDeliveryInfoWatchHandler', Date.now())
          this.addressList = res.list
        }
      })
    },
    getUserOsmCouponListHander() {
      let totalPrice = 0
      let merchandiseIds = ''
      this.merchandiseDtoList.forEach(item => {
        totalPrice += (item.discountPrice * item.num) || (item.price * item.num)
        merchandiseIds += `${item.id},`
      })
      this.$axios.$request(getUserOsmCouponList({
        merchandiseIds,
        totalPrice,
        pageNo: 1,
        pageSize: 1000
      })).then(response => {
        if (response && response.code === 1) {
          this.couponList = response.list.map(item => {
            return {
              ...item,
              isChecked: false
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.commodity_information_wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 30px 20px;
}
</style>
