<template>
  <div class='container-box order_page_container'>
    <div style='margin-bottom: 30px'>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁首页</el-breadcrumb-item>
        <el-breadcrumb-item>
          我的收货地址
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>
    <div class='mall_wrapper'>
      <div class='mall_sidebar'>
        <OrderSidebar />
      </div>
      <div class='mall_container'>
        <div class='add_btn_wrapper'>
          <div class='add_address_btn' @click='isAddAddress = true'>
            <svg-icon icon-class='mall_add' class-name='icons' />
            <span>新增收货地址</span>
          </div>
        </div>
        <div class='table_wrapper' style='min-height: 45vh'>
          <AddressTable
            :delivery-info-list='deliveryInfoList'
            @editAddress='editAddressHandler'
            @deleteAddress='deleteAddressHandler'
          />
          <Empty height='45vh' :no-more='deliveryInfoList.length === 0' tips='暂无收货地址' />
        </div>
      </div>
    </div>
    <AddressDialog
      v-if='isAddAddress'
      @cancelHandler='isAddAddress = false'
      @submitHandler='addAddressHandler'
    />
    <AddressDialog
      v-if='isEditAddress'
      :form-props='editForm'
      @cancelHandler='isEditAddress = false'
      submit-name='修改'
      @submitHandler='submitEditAddressHandler'
    />
  </div>
</template>

<script>
import OrderSidebar from '../../../../components/optimize-components/page-components/mall/OrderSidebar/index.vue'
import AddressTable from '../../../../components/optimize-components/page-components/mall/AddressTable/index.vue'
import { addDeliveryInfo, deleteDeliveryInfo, getDeliveryInfoList, updateDeliveryInfo } from '../../../../api/mall'
import AddressDialog
  from '../../../../components/optimize-components/page-components/mall/dialog/AddressDialog/index.vue'
import { tel } from '../../../../assets/helpers/form-validation'
import Empty from '../../../../components/optimize-components/UI/Empty/index.vue'

export default {
  name: 'MallAddressesPage',
  components: { AddressDialog, AddressTable, OrderSidebar, Empty },
  async asyncData({ app, params, error, store, query, req, route, redirect }) {
    if (!store.state.auth.token) {
      redirect(`/signin?fallbackUrl=${route.fullPath}`)
      return
    }

    const [deliveryInfoList] = await Promise.all([
      app.$axios.$request(getDeliveryInfoList())
    ])

    return {
      deliveryInfoList: deliveryInfoList.list
    }
  },
  data() {
    return {
      isAddAddress: false,
      isEditAddress: false,
      editForm: {}
    }
  },
  head() {
    return {
      title: `我的收货地址 - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `我的收货地址 - 脑医藏书阁 - 脑医汇`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,出版社名,在线书城`
        }
      ]
    }
  },
  methods: {
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-07 10:18
     * 获取收货地址
     * ------------------------------------------------------------------------------
     */
    getListHandler() {
      this.$axios.$request(getDeliveryInfoList()).then(res => {
        if (res && res.code === 1) {
          this.deliveryInfoList = res.list
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-07 10:15
     * 修改收货地址
     * ------------------------------------------------------------------------------
     */
    submitEditAddressHandler({ id, consignee, phoneNo, district1, district2, district3, address, isDefault }) {
      const vilidate = [
        { key: consignee, msg: '收货人不能为空', type: '' },
        { key: phoneNo, msg: '手机号不能为空', type: 'phone', errorMsg: '请输入正确的手机号' },
        { key: district1, msg: '请选择所在省', type: '' },
        { key: district2, msg: '请选择所在市', type: '' },
        { key: district3, msg: '请选择所在区', type: '' },
        { key: address, msg: '请输入详细地址', type: '' }
      ]

      try {
        vilidate.forEach(item => {
          if (!item.key) {
            throw new Error(`${item.msg}！`)
          } else if (item.type === 'phone' && !tel.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          }
        })
      } catch (e) {
        this.$toast(e.message)
        return
      }
      this.$axios.$request(updateDeliveryInfo({
        deliveryInfoId: id,
        consignee,
        phoneNo,
        district1,
        district2,
        district3,
        address,
        isDefault
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast('修改成功!')
          this.isEditAddress = false
          this.getListHandler()
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-07 10:15
     * 添加收货地址
     * ------------------------------------------------------------------------------
     */
    addAddressHandler({ consignee, phoneNo, district1, district2, district3, address, isDefault }) {
      const vilidate = [
        { key: consignee, msg: '收货人不能为空', type: '' },
        { key: phoneNo, msg: '手机号不能为空', type: 'phone', errorMsg: '请输入正确的手机号' },
        { key: district1, msg: '请选择所在省', type: '' },
        { key: district2, msg: '请选择所在市', type: '' },
        { key: district3, msg: '请选择所在区', type: '' },
        { key: address, msg: '请输入详细地址', type: '' }
      ]

      try {
        vilidate.forEach(item => {
          if (!item.key) {
            throw new Error(`${item.msg}！`)
          } else if (item.type === 'phone' && !tel.test(item.key)) {
            throw new Error(`${item.errorMsg}！`)
          }
        })
      } catch (e) {
        this.$toast(e.message)
        return
      }
      this.$axios.$request(addDeliveryInfo({
        consignee,
        phoneNo,
        district1,
        district2,
        district3,
        address,
        isDefault
      })).then(response => {
        if (response && response.code === 1) {
          this.$toast('保存成功!')
          this.isAddAddress = false
          this.getListHandler()
        }
      })
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-07 10:30
     * 修改
     * ------------------------------------------------------------------------------
     */
    editAddressHandler(form) {
      this.editForm = JSON.parse(JSON.stringify(form))
      this.isEditAddress = true
    },
    /**
     * ------------------------------------------------------------------------------
     * <AUTHOR> (Rick)         -- 2023-06-07 10:43
     * 删除
     * ------------------------------------------------------------------------------
     */
    deleteAddressHandler({ id }) {
      this.$toast.loading({ message: '请稍候' })
      this.$axios.$request(deleteDeliveryInfo({
        deliveryInfoId: id
      })).then(res => {
        if (res && res.code === 1) {
          this.getListHandler()
          this.$toast.loading().clear()
          this.$toast('删除成功!')
        }
      })
    }
  }
}
</script>

<style scoped lang='less'>
.order_page_container {
  padding: 20px 0 120px;
}

.mall_wrapper {
  display: flex;
  justify-content: space-between;

  &::after {
    display: none;
  }

  .mall_sidebar {
    width: 15%;
  }

  .mall_container {
    width: 83.333333%;
  }
}

.add_btn_wrapper {
  display: flex;
  justify-content: end;
  margin-bottom: 20px;
}

.add_address_btn {
  cursor: pointer;
  text-align: right;
  width: 136px;
  height: 36px;
  background: #F2F9FD;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 150%;
  color: #0581CE;

  .icons {
    width: 10px;
    height: 10px;
    margin-right: 5px;
  }
}
</style>
