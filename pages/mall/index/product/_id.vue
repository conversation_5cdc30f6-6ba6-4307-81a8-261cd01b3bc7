<template>
  <div class='page_wrapper container-box'>
    <div style='margin-bottom: 30px '>
      <bm-breadcrumb>
        <el-breadcrumb-item :to="{ path: '/' }">脑医汇首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/mall' }">脑医藏书阁</el-breadcrumb-item>
        <el-breadcrumb-item>
          <span class='text-limit-1' style='max-width:160px'>{{ merchandiseDetail.name }}</span>
        </el-breadcrumb-item>
      </bm-breadcrumb>
    </div>

    <div style='margin-bottom: 40px' ref='top_content'>
      <BooksDetails
        :name='merchandiseDetail.name'
        :merchandise-images-list='merchandiseDetail.merchandiseImagesList'
        :introduction='merchandiseDetail.introduction'
        :discount-price='merchandiseDetail.discountPrice'
        :price='merchandiseDetail.price'
        :press-name='merchandiseDetail.press.name'
        :merchandise-detail='merchandiseDetail'
      />
    </div>

    <section class='book_details'>
      <aside class='page-left' ref='left_content'>
        <slot name='page-left'>
          <CommodityDetails
            :detailed-information='merchandiseDetail.detail && merchandiseDetail.detail.detailedInformation'
            :press-name='merchandiseDetail.press.name'
            :isbn='merchandiseDetail.isbn'
            :press-id='merchandiseDetail.press.id'
          />
        </slot>
      </aside>
      <aside class='page-right'>
        <slot name='page-right'>
          <div class='right_details' ref='right_details'>
            <div ref='right_content'>
              <AuthorDetails
                v-if='authorUserList.length>0'
                :author-list='authorUserList'
              />
              <TranslatorItem
                v-if='translatorUserList.length>0'
                :translator-list='translatorUserList'
              />
              <RecommendedBooks
                v-if='recommendMerchandiseList.length>0'
                :recommend-merchandise-list='recommendMerchandiseList'/>
            </div>
          </div>
        </slot>
      </aside>
    </section>
    <NotifyTip/>
  </div>
</template>

<script>
import NotifyTip from "../../../../components/optimize-components/page-components/mall/NotifyTip/index.vue";
import BooksDetails
  from '../../../../components/optimize-components/page-components/mall/product/BooksDetails/index.vue'
import CommodityDetails
  from '../../../../components/optimize-components/page-components/mall/product/CommodityDetails/index.vue'
import AuthorDetails
  from '../../../../components/optimize-components/page-components/mall/product/AuthorDetails/index.vue'
import TranslatorItem
  from '../../../../components/optimize-components/page-components/mall/product/TranslatorDetails/index.vue'
import RecommendedBooks
  from '../../../../components/optimize-components/page-components/mall/product/RecommendedBooks/index.vue'
import {getMerchandiseDetail, getRecommendMerchandiseList} from '../../../../api/mall'

export default {
  name: 'ProductDetail',
  components: {
    RecommendedBooks,
    TranslatorItem,
    AuthorDetails,
    CommodityDetails,
    BooksDetails,
    NotifyTip
  },
  async asyncData({app, params, error, store, query, req}) {
    const [merchandiseDetail, recommendMerchandiseList] = await Promise.all([
      app.$axios.$request(getMerchandiseDetail({
        merchandiseId: params.id,
        loginUserId: store.state.auth.user.id
      })),
      app.$axios.$request(getRecommendMerchandiseList({
        merchandiseId: params.id,
        pageNo: 1,
        pageSize: 5
      }))
    ])

    return {
      merchandiseDetail: merchandiseDetail.result.merchandise,
      authorUserList: merchandiseDetail.result.authorUserList,
      translatorUserList: merchandiseDetail.result.translatorUserList,
      recommendMerchandiseList: recommendMerchandiseList.list
    }
  },
  head() {
    return {
      title: `${this.merchandiseDetail.name} - 脑医藏书阁 - 脑医汇`,
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: `${this.merchandiseDetail.introduction}`
        },
        {
          hid: 'keywords',
          name: 'keywords',
          content: `脑医汇,神外资讯,神介资讯,神内资讯,书名,作者名,出版社名,在线书城`
        }
      ]
    }
  },
  watch: {
    '$store.state.global.MallScroll'(newValue) {
      const rightBox = this.$refs.right_details
      const rightContent = this.$refs.right_content
      const leftContent = this.$refs.left_content
      const topContent = this.$refs.top_content
      if ((rightBox.clientHeight + 80) < window.innerHeight) {
        // rightBox.classList.add("is_Fixed")
        if ((newValue + 70) > rightBox.offsetTop) {
          if (newValue > (leftContent.clientHeight + topContent.clientHeight - rightBox.clientHeight)) {
            rightContent.style.top = 70 - (newValue - (leftContent.clientHeight + topContent.clientHeight - rightBox.clientHeight)) + 'px'
          } else {
            rightBox.style.height = rightContent.clientHeight + 'px'
            rightContent.classList.add('is_Fixed')
            rightContent.style.top = 70 + 'px'
            rightContent.style.width = rightBox.clientWidth + 'px'
          }
        } else {
          rightBox.style.height = 'auto'
          rightContent.classList.remove('is_Fixed')
        }
      } else if (leftContent.clientHeight > rightBox.clientHeight) {
        if ((newValue) > (rightBox.offsetTop + (rightBox.clientHeight - window.innerHeight))) {
          if ((newValue - 220) > (leftContent.clientHeight + topContent.clientHeight - rightBox.clientHeight)) {
            rightContent.style.bottom = 10 + (newValue - (leftContent.clientHeight + topContent.clientHeight - rightBox.clientHeight)) - 220 + 'px'
          } else {
            rightContent.style.bottom = 10 + 'px'
            rightBox.style.height = rightContent.clientHeight + 'px'
            rightContent.classList.add('is_Fixed_Bottom')
            rightContent.style.width = rightBox.clientWidth + 'px'
          }

        } else {
          rightBox.style.height = 'auto'
          rightContent.classList.remove('is_Fixed_Bottom')
        }
      }
    }
  },
  mounted() {
    // 暂时取消 自动弹框
    // const ShippingNotice = window.localStorage.getItem('ShippingNoticeBounce_2')
    // if (!ShippingNotice) {
    //   this.noticeDialog = true;
    //   window.localStorage.setItem('ShippingNoticeBounce_2', true)
    // }
  }
}
</script>

<style scoped lang='less'>
.page_wrapper {
  padding: 20px 0 60px;
}

.notice_wrapper {
  position: fixed;
  right: 40px;
  bottom: 100px;
  width: 46px;
  height: 46px;
  background: url("assets/images/mall/notice_2.png") no-repeat 100% 100%;
}

.book_details {
  margin-bottom: 30px;

  .right_details {
    display: flex;
    flex-direction: column;
    grid-gap: 20px 0;
    position: relative;
  }

  .is_Fixed {
    position: fixed;
    top: 70px;
  }

  .is_Fixed_Bottom {
    position: fixed;
    bottom: 10px;
  }
}
</style>
