<template>
  <div class='containerbox'>
    <img class="logoimg" src="~/static/luckydraw/luckyDrawTop.png" alt="">
    <div class="contentpart">活动时间：<span>{{ startTime }}</span>~<span>{{ endTime }}</span></div>

    <div class="machine-box">

      <div class="lottery">
        <div class="lottery-item">
          <div class="lottery-start">
            <div class="box gray" v-if="isStart===0">
              <p>活动未开始</p>
            </div>
            <div class="box" :class="disableLottery?'disabled':''" @click="startLottery" v-if="isStart===1">
              <p><b>抽奖</b></p>

            </div>
            <div class="box gray" v-if="isStart===2">
              <p>活动已过期</p>
            </div>
          </div>
          <ul>
            <li v-for="(item,i) in priceList" :class="i==index?'on':''">
              <div class="box">
                <p><img :src="item.image" alt=""></p>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <div class="share-button" @click="showluckyShare"><img src="~/static/luckydraw/luckyShareButton.png" alt=""></div>
      <div v-if="unbeginActive" class="lucky-recode" @click="showluckyGoods"><img
        src="~/static/luckydraw/luckyRecode.png" alt=""></div>
      <img class="machine" src="~/static/luckydraw/luckyMachine.png" alt="">

      <!-- 抽奖中奖信息轮播 -->
      <div id="box">
        <div style="width:330px;height: 300px;text-align:center">
          <div id="context" ref="refRoll" :class="{anim:animate==true}" @mouseenter="mEnter" @mouseleave="mLeave">
            <p v-for="item in activeMessageUserList">
              恭喜{{ item.username.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}获得{{ item.prizeName }}
            </p>
          </div>
        </div>
      </div>


      <div class="user-unlogin" v-if="!userLoginShow">
        <div class="login_tip">你好，请登录</div>
        <div class="login" @click='loginwebsite'><img class="login_img" src="~/static/luckydraw/luckyLoginFont.png"
                                                      alt="">
        </div>
      </div>
      <div class="user-login" v-else>
        <div>
          <div class="login_tip">剩余抽奖次数 <span>{{ raffleNumber }}</span>次</div>
        </div>
        <div v-if='raffleNumber!==0' @click='waitMinute' class="loginBck"><img class="login_img"
                                                                               src="~/static/luckydraw/luckyDrawNums.png"
                                                                               alt="">
        </div>
        <div v-else class="loginNonumbers"><img class="login_img" src="~/static/luckydraw/luckyAtonce.png" alt="">
        </div>
      </div>


      <div class="explains">
        <div class="explain_top">
          完成任务获抽奖机会
        </div>

        <div class="explain_background">
          <div class="task_item">
            <div class="task_item_left">
              <div class="task_item_left_img">
                <img src="~/static/luckydraw/luckyUerImg.png" alt="">
              </div>
              <div class="task_item_left_font">
                用户认证
              </div>
            </div>
            <div :class="closeTime?'hideclass':''">
              <div @click="userAuthentication" v-if="!giftChoiceForUserAuth" class="task_item_right">
                去完成
              </div>
              <div v-else class="task_item_right2">
                已完成
              </div>
            </div>
          </div>

          <div class="task_item">
            <div class="task_item_left">
              <div class="task_item_left_img">
                <img src="~/static/luckydraw/luckyDayLogin.png" alt="">
              </div>
              <div class="task_item_left_font">
                每日登录
              </div>
            </div>
            <div :class="closeTime?'hideclass':''">
              <div @click="dayLogin" v-if="!giftChoiceForLogin" class="task_item_right">
                去完成
              </div>
              <div v-else class="task_item_right2">
                已完成
              </div>
            </div>
          </div>

          <div class="task_item">
            <div class="task_item_left">
              <div class="task_item_left_img">
                <img src="~/static/luckydraw/luckyWatchYun.png" alt="">
              </div>
              <div class="task_item_left_font">
                观看云课堂5分钟
              </div>
            </div>
            <div :class="closeTime?'hideclass':''">
              <div @click="goOcs" v-if="!giftChoiceForCourse" class="task_item_right">
                去完成
              </div>
              <div v-else class="task_item_right2">
                已完成
              </div>
            </div>
          </div>

          <div class="task_item">
            <div class="task_item_left">
              <div class="task_item_left_img">
                <img src="~/static/luckydraw/luckyWatchMeeting.png" alt="">
              </div>
              <div class="task_item_left_font">
                观看会议5分钟
              </div>
            </div>
            <div :class="closeTime?'hideclass':''">
              <div @click="goMeeting" v-if="!giftChoiceForMeeting" class="task_item_right">
                去完成
              </div>
              <div v-else class="task_item_right2">
                已完成
              </div>
            </div>
          </div>

          <div class="task_item">
            <div class="task_item_left">
              <div class="task_item_left_img">
                <img src="~/static/luckydraw/luckyRead.png" alt="">
              </div>
              <div class="task_item_left_font">
                阅读文章3篇
              </div>
            </div>
            <div :class="closeTime?'hideclass':''">
              <div @click="goArticle" v-if="!giftChoiceForInfo" class="task_item_right">
                去完成
              </div>
              <div v-else class="task_item_right2">
                已完成
              </div>
            </div>
          </div>
        </div>

        <div class="commendation">
          活动规则
        </div>

        <div class="commendation_mg">

          <div style="margin-bottom:8px">
            <div class="commendation_font">
              1、每日下列任务均可获得一次抽奖机会：
            </div>
            <div class="commendation_explain">
              1)每日登录脑医汇官网；
            </div>
            <div class="commendation_explain">
              2)每日在官网连续观看任意云课堂五分钟；
            </div>
            <div class="commendation_explain">
              3)每日在官网连续观看任意会议五分钟；
            </div>
            <div class="commendation_explain">
              4)每日在官网阅读任意三篇文章，可获得一次抽奖机会；
            </div>
          </div>

          <div style="margin-bottom:8px">
            <div class="commendation_font">
              2、未认证用户完成认证可获得五次抽奖机会，已认证用户自动发放五次抽奖机会；
            </div>
          </div>

          <div style="margin-bottom:8px">
            <div class="commendation_font">
              3、奖品：
            </div>

            <div class="commendation_explain">
              1)《脑干手术书》（每人限1本），总量200本；
            </div>
            <div class="commendation_explain">
              2)《胶质瘤2021》（每人限1本），总量30本；
            </div>
            <div class="commendation_explain">
              3)10元云课堂优惠券（有效期30天）；
            </div>
            <div class="commendation_explain">
              4)50元云课堂优惠券（有效期30天）。
            </div>
          </div>


        </div>
      </div>

      <div class="explain_active">
        本活动最终解释权归脑医汇所有
      </div>

    </div>


    <!-- 中奖弹窗 -->
    <div class="mask" v-if="showToast"></div>
    <div class="lottery-alert" v-if="showToast">
      <div class="lotter-include">
        <div class="top-tips" v-if="priceList[index].Type!==5"></div>
        <p><img :src="priceList[index].image" alt=""></p>
        <h2>{{ priceList[index].name }}</h2>
        <div class="get-goods" v-if="priceList[index].Type==4">精品书籍</div>
        <div class="get-goods" v-else-if="priceList[index].Type==2">脑医汇云课堂优惠券</div>
        <div class="get-goods" v-else-if="priceList[index].Type==5"></div>

        <div class="btnsave" v-if="priceList[index].Type==4" @click="showAdressBook">开心收下</div>
        <div class="btnsave" v-if="priceList[index].Type==2" @click="showToast=false">开心收下</div>
        <div class="btnsave" v-else-if="priceList[index].Type==5" @click="showToast=false">再接再厉</div>
      </div>
    </div>

    <!-- 中奖东西部分弹窗 -->
    <div :class="showEdzindex? 'showEdzindex' : 'get_lucky_mask'" v-if="showToastGoods"></div>
    <div class="get_lucky_alert" v-if="showToastGoods">
      <div class="winning-record-top">
        <div @click="openAdress" class="winning-record-adress">
          我的地址
        </div>
        <div class="winning-record-recode">
          中奖记录
        </div>
        <div style="width: 10px;">
        </div>
      </div>

      <div class="winning-record-draw">
        <div v-show='getPriceList.length>0'>
          <div v-for="(item,i) in getPriceList" class="winning-record-has-draw" v-if="item.prizeType!==5">
            <div class="draw-coupon">
              <div class="draw-coupon-detail">
                <div class="draw-coupon-detail-img">
                  <img :src="item.lotteryPrize.image" alt="">
                </div>
                <div class="draw-coupon-detail-explain">
                  <div class="draw-coupon-detail-explain-name">
                    {{ item.prizeName }}
                  </div>
                  <div class="draw-coupon-detail-explain-date">
                    {{ item.winTime }}
                  </div>
                </div>
              </div>
              <!--
            <div class="draw-coupon-used">
            </div>
                 -->
              <div v-if="item.p1=='N'" @click="showToastCoupon" class="draw-coupon-go-used"></div>
              <div v-else-if="item.p1=='U'" class="draw-coupon-used">

              </div>
            </div>
          </div>
        </div>
        <div v-show='getPriceList.length==0' class="winning-record-empty">
          <div class="winning-record-no-draw">
            <img class="winning-record-img-empty" src="~/static/luckydraw/luckyDrawEmpty.png" alt="">
            <div
              style="font-size: 22px;display: flex;justify-content: center; margin-top: 23px;color: rgb(239, 194, 127);">
              空空如也
            </div>
            <div class="winning-record-go-draw" @click="showToastGoods=false">
              去抽奖
            </div>
          </div>
        </div>


      </div>

      <div class="close_icon" @click="showToastGoods=false"></div>

    </div>


    <!-- 中奖地址弹窗 -->
    <div class="get_lucky_mask" v-if="showToastAdress"></div>
    <div class="get_lucky_alert" v-if="showToastAdress">
      <div class="winning-record-top">
        <div @click="openAdress" class="winning-record-adress-no">
        </div>
        <div class="winning-adress-recode">
          我的地址
        </div>
        <div style="width: 10px;">
        </div>
      </div>

      <div class="winning-record-draw">
        <div class="form-details">
          <div class="form-details_name">
            <span>收件人 :</span>
            <input class="luckyname" type="text" v-model="luckyname" placeholder="请填写收件人姓名">
          </div>


          <div class="form-details_mobile">
            <span>手机号 :</span>
            <input class="luckymobile" maxlength="13" v-model="luckymobile" placeholder="请填写收件人手机号">
          </div>

          <div class="form-details_adress">
            <span>详细地址 :</span>
            <textarea maxlength="60" v-model="useradress" class="form-address"
                      placeholder="请填写收件人地址信息"> </textarea>
          </div>
        </div>

        <div class="confirm-button saveAddress" @click="confirmButton"></div>
        <div class="form-details-changeAdress">地址仅可修改一次，如需再次修改</div>
        <div class="form-details-changeAdress">请联系脑医汇助手18918666395（微信同号）</div>
      </div>

      <div class="close_icon" @click="showToastAdress=false"></div>

    </div>

    <!-- 中奖地址更改弹窗 -->
    <div class="get_lucky_mask" v-if="showAdressChange"></div>
    <div class="get_lucky_alert" v-if="showAdressChange">
      <div class="winning-record-top">
        <div @click="openAdress" class="winning-record-adress-no">
        </div>
        <div class="winning-adress-recode">
          我的地址
        </div>
        <div style="width: 10px;">
        </div>
      </div>

      <div class="winning-record-draw">
        <div class="form-details">
          <div class="form-details_name">
            <span>收件人 :</span>
            <div class="form-style">{{ consignee }}</div>
          </div>


          <div class="form-details_mobile">
            <span>手机号 :</span>
            <div class="form-style">{{ contactPhone }}</div>
          </div>

          <div class="form-details_adress">
            <span>详细地址 :</span>
            <div class="form-style">{{ address }}</div>
          </div>
        </div>

        <div @click="changeAdress" class="confirm-button changeAddress" v-show='showChangeButtom'></div>
        <div class="form-details-changeAdress">地址仅可修改一次，如需再次修改</div>
        <div class="form-details-changeAdress">请联系脑医汇助手18918666395（微信同号）</div>
      </div>

      <div class="close_icon" @click="showAdressChange=false"></div>

    </div>

    <!-- 中奖分享弹窗 -->
    <div class="get_lucky_mask" v-if="showShareToast"></div>
    <div class="wx_share" v-if="showShareToast">
      <div class="wx_share_clude">
        <div class="wx_share_clude_left">
          <div class="wx_share_tips">
            <div class="wx_share_scanning_code">
              微信扫一扫 <img src="~/static/luckydraw/luckyRightIcon.png" alt="">
            </div>
            <div class="wx_share_scanning_or">
              或者
            </div>
            <div @click="copyWebsite" ref="copytext" class="Connection_button">
            </div>

          </div>

        </div>

        <div class="wx_share_clude_right">
          <img class="wx_share_img" src="~/static/luckydraw/luckyShareImg.png" alt="">
        </div>

      </div>
      <div class="close_icon" @click="showShareToast=false"></div>

    </div>


    <!-- 优惠劵点击使用 -->
    <div class="wx_share" v-if='showCouponUse'>
      <div class="wx_share_clude">
        <div class="wx_share_clude_left">
          <div class="wx_share_tip">
            <div class="wx_share_scanning_codes">
              微信扫一扫打开云课堂
            </div>
            <img class="jiantou" src="~/static/luckydraw/luckyRightIcon.png" alt="">

          </div>

        </div>

        <div class="wx_share_clude_right">
          <img class="wx_share_img" src="~/static/luckydraw/goOcs.png" alt="">
        </div>

      </div>
      <div class="close_icon" @click='indexClose'></div>

    </div>


  </div>


  </div>
</template>

<script>
import {
  getWebApiLottery,
  getWebApiLotteryPrizes,
  webApiLottery2022,
  getWebApiActivityInfo,
  getLotteryAwardRecords,
  getWebApiLotteryWinningRecord,
  writeWebApiWinnerAddress,
  getWebApiLotteryUserInfo,
  addWebApiChoice,
  getWebApiPersonalWebsite
} from '@/api/lucky'
import Cookie from 'js-cookie'

export default {
  name: 'lucky-draw',
  components: {},
  head() {
    return {
      title: '幸运大抽奖'
    }
  },

  // async asyncData({ app, params, error, store, query, req }) {
  //   app.head.title = '浏览历史'
  //   const [request1, request2] = await Promise.all([
  //     /**
  //      * request1 用户信息
  //      * request2 获取浏览历史
  //      */
  //     app.$axios.$request(userInfo()),
  //     app.$axios.$request(getBrowsingHistory({
  //       userId: store.state.auth.user.id,
  //       contentType: 0,
  //       search: null,
  //       pageNo: 1,
  //       pageSize: store.state.history_count
  //     }))
  //   ])

  //   return {
  //     userInfomation: request1.result,
  //     historyData: request2
  //   }

  // },
  data() {
    return {
      copyContent: '',
      // 用户id
      medtion_member_id: '',
      // 活动code
      activityCode: '948afa8e733347ad962b655dbc4501b9',
      // 用户登录按钮是否显示立即抽奖
      userLoginShow: false,
      // 活动是否开启判断
      activeType: "N",
      animate: false,
      // 奖品列表部分
      priceList: [],
      // 活动id部分
      activityId: '',
      // 抽奖剩余次数
      raffleNumber: 0,
      //  中奖编号
      stime: "",
      // 轮播播放中奖人信息
      activeMessageUserList: [],
      // 每日登录判断
      giftChoiceForLogin: false,
      // 用户认证
      giftChoiceForUserAuth: false,
      // 观看云课堂5分钟
      giftChoiceForCourse: false,
      // 观看会议5分钟
      giftChoiceForMeeting: false,
      // 阅读文章3篇
      giftChoiceForInfo: false,
      // 中奖记录
      getPriceList: [],
      address: '',
      consignee: '',
      contactPhone: '',
      showEdzindex: false,
      isStart: 1,
      score: 10, //消耗积分
      index: -1, // 当前转动到哪个位置，起点位置
      count: 8, // 总共有多少个位置
      timer: 0, // 每次转动定时器
      speed: 200, // 初始转动速度
      times: 0, // 转动次数
      cycle: 50, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
      prize: -1, // 中奖位置
      click: true,
      showToast: false, //显示中奖弹窗
      // opacitysty:1,
      // activeBcColor:{
      //    background: 'rgba(0, 0, 0, 0.7)'
      // },
      showToastGoods: false, //显示中奖物品弹窗
      showToastAdress: false, //显示中奖地址弹窗
      showAdressChange: false, //显示更改中奖地址弹窗
      showShareToast: false, //显示分享弹窗
      luckyname: '', //中奖地址中奖人姓名
      luckymobile: '', //中奖地址中奖人手机号
      useradress: '', //中奖地址中奖地址
      showCouponUse: false,
      isAuth: '',
      showChangeButtom: true,
      disableLottery: false,
      startTime: '',
      endTime: '',
      closeTime: false,
      endTimeS: '',
      activeSituation: true,
      unbeginActive: true
    }
  },
  mounted() {

    //  判断是否登录显示立即抽奖或用户登录按钮
    const medtion_member_token = Cookie.get('medtion_token_only_sign')
    if (medtion_member_token) {
      // console.log(medtion_member_id,"medtion_member_id")
      this.userLoginShow = true

      // 更新用户身份状态
      this.getWebApiPersonalWebsite()

      // 获取抽奖活动信息
      this.getWebApiLottery()

    } else {
      this.disableLottery = true
      this.userLoginShow = false
    }

    this.newsgetWebApiLottery()
    // 获取抽奖奖品部分
    this.getWebApiLotteryPrizes()

    // this.getLotteryAwardRecords()
    //  每秒执行一次-滚动
    this.rollEvent = setInterval(this._rollApi, 1500);

    // console.log(this.$store.state.auth.user, 'console.log(this.$store.state.auth.user)')


  },
  methods: {

    // 获取浏览历史数据
    getHistoryDataFn(params = {
      contentType: 0,
      pageNo: 1
    }) {
      this.$axios.$request(getBrowsingHistory({
        userId: this.$store.state.auth.user.id,
        contentType: params.contentType,
        search: null,
        pageNo: params.pageNo,
        pageSize: this.$store.state.history_count
      })).then(res => {
        this.loading = false
        this.historyData = res
      })
    },


    // 轮播播放中奖人信息
    _rollApi() {
      let refRollDiv = this.$refs.refRoll;

      refRollDiv.style.marginTop = '-30px';

      this.animate = !this.animate;

      var that = this;
      if (that.activeMessageUserList) {
        setTimeout(function () {
          that.activeMessageUserList.push(that.activeMessageUserList[0]);
          that.activeMessageUserList.shift();
          refRollDiv.style.marginTop = '0px';
          that.animate = !that.animate; // 这个地方如果不把animate 取反会出现消息回滚的现象，此时把ul 元素的过渡属性取消掉就可以完美实现无缝滚动的效果了
        }, 500)
      }


    },
    mEnter() {
      clearInterval(this.rollEvent)
    },
    mLeave() {
      this.rollEvent = setInterval(this._rollApi, 1000)
    },


    startLottery() {
      if (this.closeTime) {
        this.$toast('当前不再活动时间内！')
        return
      }
      if (this.disableLottery) {
        return
      }
      if (this.raffleNumber == 0) {
        // this.$toast('请获取抽奖次数！')
        return
      }

      this.disableLottery = true
      // 如果活动类型等于N 活动停止
      if (this.activeType == 'N') {
        console.log(this.activeType, "this.activeType")
        this.$toast('当前不再活动时间内！')
        return
      }
      if (!this.click) {
        return
      }
      this.startRoll();
    },
    // 开始转动
    startRoll() {
      this.times += 1 // 转动次数
      this.oneRoll() // 转动过程调用的每一次转动方法，这里是第一次调用初始化
      // 如果当前转动次数达到要求 && 目前转到的位置是中奖位置
      if (this.times > this.cycle + 10 && this.prize === this.index) {
        clearTimeout(this.timer) // 清除转动定时器，停止转动
        this.prize = -1
        this.times = 0
        this.speed = 200
        this.click = true;
        var that = this;
        setTimeout(res => {
          that.showToast = true;
          that.disableLottery = false;
        }, 500)
      } else {
        if (this.times < this.cycle) {
          this.speed -= 10 // 加快转动速度
        } else if (this.times === this.cycle) {
          // const index = parseInt(Math.random() * 10, 0) || 0;  // 随机获得一个中奖位置

          // console.log(index, "index")
          this.webApiLottery2022().then(()=>{
            // this.prize = -3
            const index = this.stime
            this.prize = index; //中奖位置,可由后台返回
            if (this.prize > 7) {
              this.prize = 7
            }
          })
        } else if (this.times > this.cycle + 10 && ((this.prize === 0 && this.index === 7) || this.prize === this
          .index + 1)) {
          this.speed += 110
        } else {
          this.speed += 20
        }
        if (this.speed < 40) {
          this.speed = 40
        }
        this.timer = setTimeout(this.startRoll, this.speed)
      }
    },

    // 每一次转动
    oneRoll() {
      let index = this.index // 当前转动到哪个位置
      const count = this.count // 总共有多少个位置
      index += 1
      if (index > count - 1) {
        index = 0
      }
      this.index = index
    },

    //  转换时间戳为日期
    formatDate(date, fmt) {
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds()
      }
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + ''
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str))
        }
      }
      return fmt
    },
    padLeftZero(str) {
      return ('00' + str).substr(str.length)
    },

    //  显示中奖奖品部分
    showluckyGoods() {
      const medtion_member_token = Cookie.get('medtion_token_only_sign')
      if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  }else{
    this.showToastGoods = true
    // 获取用户中奖记录
    this.getWebApiLotteryWinningRecord()
  }
},


//  点击显示分享按钮部分
showluckyShare() {
  this.showShareToast = true

},

// 显示地址部分
openAdress() {
  // 判断用户是否填写地址 填写就显示填写的地址 否则就去输入
  // 发请求
  // const res =this.$axios.$request(
  //   getWebApiLotteryUserInfo({
  //      activityId: this.activityId,
  //   })
  // )
  // console.log(res,"中奖用户的收货地址")
  if (this.address && this.contactPhone && this.address) {
    // 显示已写地址
    this.showToastGoods = false
    this.showAdressChange = true
  } else {
    // 显示未写地址
    this.showToastGoods = false
    this.showToastAdress = true
  }


},

//  显示使用优惠劵部分
showToastCoupon() {
  this.showCouponUse = true
  this.showEdzindex = true
},

// 手机号码验证
isMobile: function (value) {
  return /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value);
  return /^(https?|ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i
    .test(value);
},

confirmButton() {
  // this.showToastAdress = false
  if (this.luckyname == '') {
    this.$toast('收件人不能为空')
    return
  } else if (this.luckymobile == '') {
    this.$toast('手机号不能为空')
    return
  } else if (this.useradress == '') {
    this.$toast('地址不能为空')
    return
  }

  if (!this.isMobile(this.luckymobile)) {
    this.$toast('手机号码格式错误')
    return;
  }

  this.writeWebApiWinnerAddress(this.luckyname, this.luckymobile, this.useradress)

  console.log("111", this.luckyname, this.luckymobile, this.useradress)
  this.showToastAdress = false
},
FormatDateS(strTime) {
  var date = new Date(strTime);
  return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate() + " " + date.getHours() + ":" + date.getMinutes() + ":" + date.getMinutes();
},
// 抽奖活动信息详情
async getWebApiLottery() {
  // 发请求
  const res = await this.$axios.$request(
    getWebApiLottery({
      activityCode: this.activityCode
    })
  )
  this.startTime = res.result.startTime.slice(0, 11)
  this.endTime = res.result.endTime.slice(0, 11)
  var endTimeClose = res.result.endTime
  var startTimeClose = res.result.startTime
  var today = new Date();
  var today_time = this.FormatDateS(today);
  // console.log(new Date(startTimeClose).getTime(),new Date(today_time).getTime(),today_time,endTimeClose ,startTimeClose,'this.endTime',new Date(today_time).getTime()>new Date(endTimeClose).getTime(),new Date(today_time).getTime()<new Date(startTimeClose).getTime())
  if (new Date(today_time).getTime() > new Date(endTimeClose) || new Date(today_time).getTime() < new Date(startTimeClose).getTime()) {
    this.closeTime = true
  }

  if (new Date(today_time).getTime() > new Date(endTimeClose)) {
    this.$toast('活动已结束')
  } else if (new Date(today_time).getTime() < new Date(startTimeClose).getTime()) {
    this.$toast('活动未开始')
    this.unbeginActive = false
  }

  this.activityId = res.result.id
  console.log(res, "res", res.result)
  if (res.result.status == 'O') {
    // this.$toast('活动有效')
    this.activeType = 'O'
  } else {
    // this.$toast('活动截止')
    this.closeTime = true
    this.activeType = 'N'
    this.$toast('活动已截止')
  }


  await this.getWebApiLotteryWinningRecord()

  await this.getLotteryAwardRecords()
  // 用户是否填写过地址
  await this.getWebApiLotteryUserInfo()


},


// 抽奖活动信息详情
async newsgetWebApiLottery() {
  // 发请求
  const res = await this.$axios.$request(
    getWebApiLottery({
      activityCode: this.activityCode
    })
  )
  this.activityId = res.result.id
  console.log(res, "res", res.result)
  if (res.result.status == 'O') {
    // this.$toast('活动有效')
    this.activeType = 'O'
  } else {
    // this.$toast('活动截止')
    this.activeType = 'N'
  }
  this.startTime = res.result.startTime.slice(0, 11)
  this.endTime = res.result.endTime.slice(0, 11)

  await this.getLotteryAwardRecords()
},

// 点击修改地址
changeAdress() {
  this.showAdressChange = false
  this.showToastGoods = false
  this.showToastAdress = true
  this.luckyname = this.consignee
  this.luckymobile = this.contactPhone
  this.useradress = this.address
},

//  跳转去会议部分
goMeeting() {
  const medtion_member_token = Cookie.get('medtion_token_only_sign')
  if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  } else {
    this.$router.push({
      path: '/meeting/home'
    })
  }
},

loginwebsite() {
  this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
},

goOcs() {
  const medtion_member_token = Cookie.get('medtion_token_only_sign')
  if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  } else {
    window.location.href = "/mooc/#/";
  }
},

//  跳转去文章部分
goArticle() {
  const medtion_member_token = Cookie.get('medtion_token_only_sign')
  if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  } else {
    this.$router.push({
      path: '/info'
    })
  }
},

//  跳转去用户认证部分
userAuthentication() {
  const medtion_member_token = Cookie.get('medtion_token_only_sign')
  if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  } else {
    if (this.isAuth == '0' || this.isAuth == '3') {
      this.$router.push({
        path: '/personaldata'
      })
    } else if (this.isAuth == '2') {
      this.$toast('用户正在认证中')
    }

  }
  //  console.log(this.isAuth,"this.isAuth " )
  //  return

},

dayLogin() {
  const medtion_member_token = Cookie.get('medtion_token_only_sign')
  if (!medtion_member_token) {
    this.$router.push({name: 'signin', query: {fallbackUrl: this.$route.fullPath}})
  }
},
// 抽奖奖品部分
async getWebApiLotteryPrizes() {
  // 发请求
  const res = await this.$axios.$request(
    getWebApiLotteryPrizes({
      activityCode: this.activityCode
    })
  )
  console.log(res)
  this.priceList = res.list
  console.log(this.priceList, "this.priceList")
},


// 点击抽奖
async webApiLottery2022() {
  // 发请求
  const res = await this.$axios.$request(
    webApiLottery2022({
      activityId: this.activityId
    })
  )
  const newDate = res.result.lotteryPrize;
  const newStime = newDate.prizeId
  console.log(this.priceList,'priceList')
  for (let i = 0; i < this.priceList.length; i++) {
    if (this.priceList[i].prizeId == newStime) {
      this.stime = i
      console.log(i, "i")
    }

  }
  // 获取用户抽奖相关信息，如抽奖次数、是否已认证等等
  await this.getUserActivityInfo();
  return this.stime
},
getUserActivityInfo: async function () {
  // 发请求
  const res = await this.$axios.$request(
    getWebApiActivityInfo({
      activityCode: this.activityCode
    })
  )
  // 抽奖剩余次数
  this.raffleNumber = res.result.remainLotteryChance || 0
  // 今天是否已赠送登录抽奖次数
  this.giftChoiceForLogin = res.result.giftChoiceForLogin
  // 用户认证 先取 接口 看是否用户认证了
  this.giftChoiceForUserAuth = res.result.giftChoiceForUserAuth
  //  今天是否已赠送观看云课堂抽奖次数
  this.giftChoiceForCourse = res.result.giftChoiceForCourse
  // 今天是否已赠送观看会议抽奖次数
  this.giftChoiceForMeeting = res.result.giftChoiceForMeeting
  // 今天是否已赠送阅读文章抽奖次数
  this.giftChoiceForInfo = res.result.giftChoiceForInfo
},

//  获取用户参与活动情况
async getWebApiActivityInfo() {
  // 1.获取用户抽奖相关信息，如抽奖次数、是否已认证等等
  await this.getUserActivityInfo();

  // 2.每日登录赠送抽奖次数
  if (this.giftChoiceForLogin == false) {
        const token = Cookie.get('medtion_token_only_sign')
        if (token && this.activeType === 'O') {
          await this.addWebApiChoice("login", 1)
        }
      }

      // 3.用户认证赠送抽奖次数
      if (this.giftChoiceForUserAuth == false) {
        // console.log(this.isAuth, "this.isAuth")
        if (this.isAuth == '1') {
          this.giftChoiceForUserAuth = true
          await this.addWebApiChoice("userAuth", 5)
          console.log('用户认证', this.giftChoiceForUserAuth)
        } else {
          this.giftChoiceForUserAuth = false
          console.log(this.giftChoiceForUserAuth, "this.giftChoiceForUserAuth")
        }
      }

      // 4.重新获取用户抽奖相关信息，刷新页面
      await this.getUserActivityInfo();
    },

    async getWebApiPersonalWebsite() {
      /**
       *@author:chen
       *@desc: 更新认证状态
       */
      await this.$axios.$request(getWebApiPersonalWebsite({
        userId: this.$store.state.auth.user.id,
        profileUserId: this.$store.state.auth.user.id
      })).then(res => {
        if (res && res.code === 1) {
          // 获取用户参与活动情况
          this.getWebApiActivityInfo()
          console.log(res, 'res')
          this.isAuth = res.result.isAuth
        }
      })
    },
    indexClose() {
      this.showCouponUse = false
      this.showEdzindex = false
    },

    // 转换时间戳为日期
    formatDate(date, fmt) {
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      let o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds()
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + '';
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : this.padLeftZero(str));
        }
      }
      return fmt;
    },
    padLeftZero(str) {
      return ('00' + str).substr(str.length);
    },

    //  获取用户参与活动情况轮播中奖情况
    async getLotteryAwardRecords() {
      // 发请求
      const res = await this.$axios.$request(
        getLotteryAwardRecords({
          activityId: this.activityId,
          limit: 100
        })
      )
      this.activeMessageUserList = res.list
      console.log(res, "获取用户参与活动情况轮播中奖情况")
    },

    //  获取用户中奖记录
    async getWebApiLotteryWinningRecord() {
      // 发请求
      const res = await this.$axios.$request(
        getWebApiLotteryWinningRecord({
          activityId: this.activityId,
        })
      )
      if (res.list) {
        // 转化时间戳
        res.list.forEach((item) => {
          item.winTime = this.formatDate(new Date(item.winTime), 'yyyy-MM-dd hh:mm');

        })
        this.getPriceList = res.list.filter(function (prize) {
          return prize.prizeType !== 5
        })

        // this.getPriceList.winTime = this.formatDate(this.getPriceList.winTime, 'yyyy-MM-dd hh:mm')
        console.log(res, "获取用户中奖记录", this.getPriceList)
      }
    },


    // 更新中奖用户的收货地址
    async writeWebApiWinnerAddress(consignee, contactPhone, address) {
      // 发请求
      const res = await this.$axios.$request(
        writeWebApiWinnerAddress({
          activityId: this.activityId,
          consignee: consignee,
          contactPhone: contactPhone,
          address: address
        })
      )
      if (res.code == 1) {
        this.$toast(res.result)
        console.log(res)
      }
      await this.getWebApiLotteryUserInfo()
      console.log(res, "更新中奖用户的收货地址")
    },


    // 更新中奖用户的收货地址
    async getWebApiLotteryUserInfo() {
      // 发请求
      const res = await this.$axios.$request(
        getWebApiLotteryUserInfo({
          activityId: this.activityId,
        })
      )
      this.address = res.result.address
      this.consignee = res.result.consignee
      this.contactPhone = res.result.contactPhone

      // console.log('顯示11')
      if (res.result.updateNumber == 1) {
        this.showChangeButtom = false
        //  console.log('顯示')
      }
      console.log(res, "用户的收货地址")
    },


    // 增加抽奖机会
    async addWebApiChoice(style, num) {
      // 发请求
      const res = await this.$axios.$request(
        addWebApiChoice({
          activityId: this.activityId,
          source: style,
          num: num
        })
      )
      if(num==5){
        this.$toast('认证奖励已发放')
      }
      console.log(res, "增加抽奖机会")
    },

    waitMinute() {
      if (this.activeType == 'N') {
        this.$toast('当前不再活动时间内！')
      } else {
        if (this.raffleNumber == 0) {
          if (this.closeTime) {
            this.$toast('当前不再活动时间内！')
            return
          }
          // this.$toast('请获取抽奖次数！')
        } else {
          this.$toast('抽奖中，请稍等！')
        }
      }


    },

    copyWebsite() {
      this.copyContent = window.location.href;
      ; //也可以直接写上等于你想要复制的内容
      var input = document.createElement("input"); // 直接构建input
      input.value = this.copyContent; // 设置内容
      console.log(input.value);
      document.body.appendChild(input); // 添加临时实例
      input.select(); // 选择实例内容
      document.execCommand("Copy"); // 执行复制
      document.body.removeChild(input); // 删除临时实例
      this.$toast('复制成功')
    },

    goDrawNums() {
      console.log('111')
      this.showAdressChange = true
      this.get_lucky_mask = true
    },
    showAdressBook() {
      this.showToast = false
      if (this.address && this.contactPhone && this.address) {
        // 显示已写地址
        // this.showToastGoods = false
        // this.showAdressChange = true
      } else {
        // 显示未写地址
        this.showToastGoods = false
        this.showToastAdress = true
      }
    }


  },

}

</script>

<style lang='less' scoped>
.containerbox {
  background: url('~/static/luckydraw/luckyDrawBackground.png') no-repeat;
  position: relative;
  background-size: 100% 100%;
  height: 1800px;
}

.logoimg {
  position: absolute;
  display: block;
  width: 100%;
  height: 470px;
  margin: 0 auto;
  object-fit: cover;
}

.contentpart {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 9.5%;
  font-size: 32px;
  color: #FFF0E3;
}

.machine {
  width: 752px;
  height: 658px;
}

#box {
  font-size: 16px;
  width: 326px;
  height: 30px;
  line-height: 30px;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
  color: #FFF0E2;
  position: absolute;
  top: 1.25%;
  left: 50%;
  transform: translateX(-50%);
}

.anim {
  transition: all 0.5s;
}

#con1 li {
  list-style: none;
  line-height: 30px;
  height: 30px;
}


.login_img {
  width: 132px;
  height: 36px;
  object-fit: cover;
}

.login_tip {
  position: absolute;
  bottom: 24%;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 22px;
  color: #FFF0E2;
  font-weight: 700;
}

.login {
  position: absolute;
  bottom: 12%;
  left: 50%;
  transform: translateX(-50%);
  width: 378px;
  height: 56px;
  background: url(~/static/luckydraw/luckyLogin.png) no-repeat;
  background-size: 100% 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 auto;
}

.loginBck {
  position: absolute;
  bottom: 12%;
  left: 50%;
  transform: translateX(-50%);
  width: 378px;
  height: 56px;
  background: url(~/static/luckydraw/luckyLoginBck.png) no-repeat;
  background-size: 100% 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 auto;
}

.loginNonumbers {
  position: absolute;
  bottom: 12%;
  left: 50%;
  transform: translateX(-50%);
  width: 378px;
  height: 56px;
  background: url(~/static/luckydraw/luckyNonumbers.png) no-repeat;
  background-size: 100% 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 auto;
}

.machine-box {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 14%;
}

.explains {
  margin: 0 auto;
  padding-left: 0.22rem;
  padding-right: 0.22rem;
  width: 535px;
  height: 880px;
  background: pink;
  background: url(~/static/luckydraw/luckyRw.png) no-repeat;
  background-size: 100% 100%;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 93.5%;
  z-index: 1;
}

.explain_top {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #EF5904;
  padding-top: 38px;
  font-weight: 700;
  font-size: 22px;
  margin-bottom: 23px
}

.explain_background {
  margin: 0 auto;
  width: 450px;
  height: auto;
  background: #FFE8DB;
  border-radius: 10px;
  padding: 2px 14px 14px 14px;
}

.task_item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 62px;
  background: #FFFFFF;
  border-radius: 10px;
  margin-top: 12px
}

.task_item_left_img img {
  margin-left: 14px;
  width: 36px;
  height: 36px;
}

.task_item_left_font {
  margin-left: 6px;
  font-weight: 700;
  font-size: 18px;
  color: #BF6213;
}

.task_item_left {
  display: flex;
  align-items: center;
  justify-items: center;
}

.task_item_right {
  cursor: pointer;
  width: 66px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: #CD3311;
  border-radius: 37px;
  color: #FFFFFF;
  margin-right: 14px;
}

.task_item_right2 {
  width: 66px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: #F3D497;
  border-radius: 37px;
  color: #FFFFFF;
  margin-right: 14px;
}

.commendation {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #EF5904;
  margin-top: 20px;
  margin-bottom: 16px;
  font-weight: 700;
  font-size: 22px;
}

.commendation_font {
  display: flex;
  align-items: center;
  color: #BF6213;
  font-weight: 700;
  font-size: 16px;


  color: #BF6213;
}

.commendation_mg {
  margin: 0 58px
}

.commendation_explain {
  margin-left: 13px;
  margin-top: 6px;
  font-size: 14px;
  color: #BF6213;
}

.explain_active {
  position: absolute;
  left: 50%;
  -webkit-transform: translatex(-50%);
  transform: translatex(-50%);
  bottom: -133%;
  font-size: 18px;
  color: #BF6213;
  padding-bottom: 9px;
}

.share-button {
  cursor: pointer;
  position: absolute;
  right: -9%;
  top: 9.5%;
  width: 96px;
  height: 41px;

  img {
    width: 100%;
    height: 100%
  }
}

.lucky-recode {
  cursor: pointer;
  position: absolute;
  right: -9%;
  top: 18.5%;
  width: 96px;
  height: 41px;

  img {
    width: 100%;
    height: 100%
  }
}


ul,
li {
  list-style-type: none;
}

.lottery-box {
  overflow: hidden;
}

.lottery-box .title {
  text-align: center;
  padding: 50px 0;
  font-size: 18px;
  color: #fff;
}

.lottery {
  position: absolute;
  top: 12%;
  left: 7%;
  animation: changeBg .5s ease infinite;
  /* overflow: hidden; */
  padding: 20px;
  width: 460px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.lottery .lottery-item {
  height: 340px;
  position: relative;
  margin-top: 10px;
  margin-left: 10px;
}

.lottery .lottery-item ul li {
  width: 33.33333333%;
  position: absolute;
  padding-right: 10px;
}

.lottery .lottery-item ul li:nth-child(2) {
  left: 33.33333333%;
}

.lottery .lottery-item ul li:nth-child(3) {
  left: 66.66666666%;
}

.lottery .lottery-item ul li:nth-child(4) {
  left: 99.9999999%;
  top: 0;
}

.lottery .lottery-item ul li:nth-child(5) {
  left: 99.666667%;
  top: 150px;
}

.lottery .lottery-item ul li:nth-child(6) {
  left: 66.66666%;
  top: 150px;
}

.lottery .lottery-item ul li:nth-child(7) {
  left: 33.333%;
  top: 150px;
}

.lottery .lottery-item ul li:nth-child(8) {
  left: 0;
  top: 150px;
}

.lottery .lottery-item ul li .box {
  height: 140px;
  width: 140px;
  position: relative;
  text-align: center;
  border-radius: 20%;
  overflow: hidden;

  background-size: 100% 100%;
}

.lottery .lottery-item ul li .box img {
  display: block;

  height: 140px;
  width: 140px;
  margin: 0 auto;
  margin-bottom: 5px;
}

.lottery .lottery-item ul li .box p {
  color: #708ABF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.lottery .lottery-item ul li.on .box {
  // background: url(~/static/luckydraw/luckyBg.png) no-repeat center;
  // background-size: 100% 100%;
  //  box-shadow: 0 0 19px #101010;
  outline: 3px solid #3a36367d;
}

.lottery .lottery-item ul li.on .box p {
  color: #fff;
}

.lottery .lottery-item .lottery-start {
  position: absolute;
  left: 46.333333%;
  width: 33.33333333%;
  top: 231px;
  padding-right: 10px;
}

.lottery .lottery-item .lottery-start .box {
  height: 56px;
  position: relative;
  margin-top: 123%;
  z-index: 66;
  opacity: 0;
  left: -68%;
  width: 376px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  text-align: center;
  overflow: hidden;
  background: url(~/static/luckydraw/luckyRw.png) no-repeat center;
  background-size: 100% 100%;
}

.lottery .lottery-item .lottery-start .box p b {
  font-size: 40px;
  margin-top: 16px;
  margin-bottom: 15px;
  line-height: 30px;
  display: block;
}

.lottery .lottery-item .lottery-start .box:active {
}

.lottery .lottery-item .lottery-start .box.gray {
  background: url(~/static/luckydraw/luckyRw.png) no-repeat center;
  background-size: 100% 100%;
}

.lottery .lottery-item .lottery-start .box.gray p {
  color: #708ABF;
  font-weight: bold;
}

.mask {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  overflow: hidden;
  z-index: 222;
  top: 0;
  left: 0;
}

.get_lucky_mask {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  overflow: hidden;
  z-index: 8900;
  top: 0;
  left: 0;
}

.showEdzindex {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  position: fixed;
  overflow: hidden;
  z-index: 9001;
  top: 0;
  left: 0;
}


.get_lucky_alert {
  max-width: 480px;
  height: 535px;
  text-align: center;
  z-index: 9000;
  border-radius: 10px;
  background: linear-gradient(180deg, #FBEBAC 0%, #FFFFFF 100%);
  padding: 11px;
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.jiantou {
  margin-top: 54px;
  margin-left: 2px;
}

.wx_share {
  max-width: 450px;
  height: 235px;
  text-align: center;
  z-index: 10000;
  border-radius: 10px;
  background: linear-gradient(180deg, #FBEBAC 0%, #FFFFFF 100%);
  padding: 5px;
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.lottery-alert {
  max-width: 400px;
  height: 364px;
  text-align: center;
  z-index: 10000;
  border-radius: 10px;
  /* background: #fff; */
  background: #FBEBAC;
  padding: 9px;
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.lottery-alert h1 {
  font-size: 18px;
  font-weight: bold;
  color: #D92B2F;
}

.lottery-alert img {
  height: 88px;
  width: 88px;
  margin-top: 44px;
  margin: 0 auto;
}

.lottery-alert h2 {
  font-weight: 700;
  font-size: 32px;
  line-height: 42px;
  text-align: center;
  color: #AF3A20;
  font-size: 32px;
  padding-top: 20px;
}

.lottery-alert p {
  color: #666;
  font-size: 16px;
  padding-top: 70px;
}

.lottery-alert .btnsave {
  border-radius: 60px;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  -webkit-box-shadow: none;
  box-shadow: none;
  width: 200px;
  height: 58px;
  cursor: pointer;
  line-height: 58px;
  color: #fff;
  margin-top: 12px;
  background: linear-gradient(180deg, #F35735 0%, #DB320D 100%);
  font-size: 16px;
}

.lotter-include {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFFBDD 0%, #FFFFFF 100%);
}

.get-goods {
  height: 30px;
  font-size: 18px;
  color: #BF841A;
  margin: 15px 0 15px 0;
}

.top-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  background: pink;
  width: 270px;
  height: 58px;
  line-height: 58px;
  /* text-align: center; */
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 5;
  background: url(~/static/luckydraw/luckyTipBackground.png) no-repeat;
  background-size: 100% 100%;
  font-weight: 700;
  color: #F25C3B;
}

.winning-record-adress {
  margin-left: 10px;
  width: 68px;
  height: 26px;
  line-height: 26px;
  left: 390px;
  top: 164px;
  background: #FFAD32;
  border-radius: 40px;
  font-size: 12px;
  color: white;
  text-align: center;
  cursor: pointer;
}

.winning-record-adress-no {
  width: 68px;
  height: 27px;
  margin-left: 10px;
  background-size: 100% 100%;
}

.winning-record-recode {
  font-weight: 700;
  font-size: 25px;
  /* line-height: 32px; */
  display: flex;
  align-items: center;
  text-align: center;
  color: #EF5904;
  /* border: 2px solid #FFFFFF; */
  margin-right: 62px;
  margin-top: -5px;
  /* width: 96px; */
  height: 32px;
  background-size: 100% 100%;
}

.winning-adress-recode {
  margin-right: 62px;
  margin-top: -10px;
  height: 27px;
  font-weight: 700;
  font-size: 24px;
  /* identical to box height */

  display: flex;
  align-items: center;
  text-align: center;

  color: #EF5904;
}

.winning-record-top {
  display: flex;
  padding-top: 24px;
  justify-content: space-between;
}

.winning-record-draw {
  overflow-y: scroll;
  padding: 24px 18px 0 18px;
  /* width: 100%; */
  height: 440px;
  margin-top: 16px;
  background: #FEFCEE;
  border-radius: 15px
}

.winning-record-no-draw {
  width: 100%;
  height: 100%;
}

.winning-record-go-draw {
  position: absolute;
  margin-top: 40px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 200px;
  height: 58px;
  line-height: 58px;
  background: linear-gradient(180deg, #F35735 0%, #DB320D 100%);
  background-size: 100% 100%;
  border-radius: 60px;
  color: #FFF0E2;
  font-weight: 700;
  cursor: pointer;
}

.winning-record-empty {
  position: absolute;
  left: 50%;
  top: 39%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 168px;
  height: 138px;
}

.winning-record-img-empty {
  width: 92px;
  height: 78px;
}

.winning-record-has-draw {
  padding-top: 10px;
}

.draw-coupon,
.draw-books {
  display: flex;
  justify-content: space-between;
  height: 42px;
  background: #FFFFFF;
  border-radius: 15px;
  font-size: 24px;
  padding: 13px 14px 13px 14px;
}

.draw-coupon-detail {
  display: flex;
}

.draw-coupon-detail-img {
  width: 44px;
  height: 44px;
  // background: url("~/static/luckydraw/luckyFiftyCoupon.png") no-repeat;
  background-size: 100% 100%;
}

.draw-coupon-detail-img {
  img {
    width: 44px;
    height: 44px;
  }

}

.draw-book-detail-img {
  width: 44px;
  height: 44px;
  // background: url("~/static/luckydraw/luckBook.png") no-repeat;
  background-size: 100% 100%;
}

.draw-coupon-detail-explain-name {
  margin-left: 5.5px;
  font-weight: 700;
  font-size: 18px;
  color: #BF6213;
}

.draw-coupon-detail-explain-date {
  margin-top: 2px;
  width: 130px;
  margin-left: -5px;
  font-size: 12px;
  color: #A57B57;
}

.draw-book-explain-date {
  margin-top: 2px;
  margin-left: 4px;
  font-size: 12px;
  color: #A57B57;
}

.draw-coupon-used {
  cursor: pointer;
  margin-top: 10px;
  width: 66px;
  height: 26px;
  background: url("~/static/luckydraw/luckyUsed.png") no-repeat;
  background-size: 100% 100%;
}

.draw-coupon-go-used {
  cursor: pointer;
  margin-top: 10px;
  width: 66px;
  height: 26px;
  background: url("~/static/luckydraw/luckyGoUse.png") no-repeat;
  background-size: 100% 100%;
}

.close_icon {
  position: absolute;
  left: 50%;
  bottom: -60px;
  transform: translateX(-50%);
  width: 42px;
  height: 42px;
  background: url("~/static/luckydraw/luckyCloseIcon.png") no-repeat;
  background-size: 100% 100%;
  cursor: pointer;

}

.form-details_name,
.form-details_mobile {
  height: 50px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 18px;
  background: #FFF6D1;
  border-radius: 10px;
}

.form-details_name span {
  margin-left: 14px;
  color: #BF6213;
  white-space: nowrap;
}

.form-details_mobile span {
  margin-left: 14px;
  color: #BF6213;
  white-space: nowrap;
}

.luckyname,
.luckymobile {
  font-weight: 700;
  display: block;
  padding-left: 8px;
  border: none;
  outline: none;
  font-size: 18px;
  color: #BF6213;
  background: #FFF6D1;
}

/deep/ input::-webkit-input-placeholder {
  color: #E1AE82;
  font-weight: 400;
  font-size: 18px;
}

/deep/ input::-moz-input-placeholder {
  color: #E1AE82;
  font-weight: 400;
  font-size: 18px;
}

/deep/ input::-ms-input-placeholder {
  color: #E1AE82;
  font-weight: 400;
  font-size: 18px;
}

/* 设置textarea框提示内容的样式 */
textarea::-webkit-input-placeholder {
  font-family: 'Microsoft YaHei' ! important;
  font-weight: 400;
  font-size: 18px;
  color: #E1AE82;

}

/*webkit 内核浏览器*/
textarea::-moz-placeholder {
  font-family: 'Microsoft YaHei' ! important;
  font-weight: 400;
  font-size: 18px;
  color: #E1AE82;
}

/*Mozilla Firefox 19+*/
textarea:-moz-placeholder {
  font-family: 'Microsoft YaHei' ! important;
  font-weight: 400;
  font-size: 18px;
  color: #E1AE82;
}

/*Mozilla Firefox 4 to 18*/
textarea:-ms-input-placeholder {
  font-family: 'Microsoft YaHei' ! important;
  font-weight: 400;
  font-size: 18px;
  color: #E1AE82;
}

.form-details_adress {
  padding-top: 12px;
  height: 120px;
  display: flex;
  margin-bottom: 52px;
  font-size: 24px;
  background: #FFF6D1;
}

.form-details_adress span {;
  margin-left: 14px;
  font-size: 18px;
  color: #BF6213;
  white-space: nowrap;
}


.form-details textarea {
  resize: none;
  display: block;
  width: 320px;
  height: 100px;
  border-radius: 0.02rem;
  border: none;
  outline: none;
  padding-left: 8px;
  background: #FFF6D1;
  font-size: 18px;
  color: #BF6213;
  font-weight: 700;
}

.saveAddress {
  margin: 0px auto 20px;
  background: url("~/static/luckydraw/luckySureButton.png") no-repeat;
  background-size: 100% 100%;
  width: 200px;
  height: 58px;
  cursor: pointer;
}

.changeAddress {
  margin: 0px auto 20px;
  background: url("~/static/luckydraw/luckyChangeButton.png") no-repeat;
  background-size: 100% 100%;
  width: 200px;
  height: 58px;
}

.form-details-changeAdress {
  display: flex;
  align-items: center;
  justify-content: center;
  /* font-weight: 700; */
  font-size: 14px;
  color: #BF8419;
  font-family: 'Microsoft YaHei';
  font-style: normal;
}

.form-style {
  text-align: justify;
  font-size: 18px;
  font-weight: 700;
  color: #BF6213;
  margin-left: 8px;
}

.wx_share_clude {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFFBDD 0%, #FFFFFF 100%);
}

.wx_share_clude {
  display: flex;
}

.wx_share_clude_left {
  width: 50%;
  display: flex;
  align-items: center;
}

.wx_share_clude_right {
  width: 50%;
  display: flex;
  align-items: center;
}

.wx_share_img {
  width: 140px;
  height: 140px;
  margin: auto;
}

.wx_share_tips {
  display: flex;
  flex-wrap: wrap;
  width: 140px;
  height: 140px;
  margin: auto;
}

.wx_share_tip {
  display: flex;
  width: 140px;
  height: 140px;
  margin: auto;
}

.wx_share_tip {
  img {
    width: 19px;
    height: 16px
  }
}

.wx_share_scanning_code {
  margin-top: 10px;
  font-size: 20px;
  font-weight: 700;
  color: #BF6213;

  img {
    width: 19px;
    height: 16px
  }
}


.wx_share_scanning_codes {
  margin-top: 40px;
  font-size: 20px;
  font-weight: 700;
  color: #BF6213;

  img {
    width: 19px;
    height: 16px
  }
}

.wx_share_scanning_or {
  position: relative;
  bottom: 0px;
  margin: auto;
  color: #D3945E;
}

.Connection_button {
  margin: 0px auto 0px;
  background: url("~/static/luckydraw/luckyConnectionButton.png") no-repeat;
  background-size: 100% 100%;
  width: 114px;
  height: 38px;
  font-size: 0;
  overflow: hidden;
}

.hideclass {
  display: none;
}

</style>
