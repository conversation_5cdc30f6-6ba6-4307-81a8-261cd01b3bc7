<template>
  <div class="container-box">
    <div class="header">
      <div class="header_box">
        <div class="header-title">消息中心</div>
        <div class="header-right">
          <Popconfirm
            :hide-icon="true"
            confirm-button-text="确认"
            cancel-button-text="取消"
            placement="bottom-end"
            popper-class="clean-message-confirm"
            title="确认清除所有未读信息提示吗？"
            @confirm="clearAllHandler"
          >
            <div slot="reference" class="header-setting">
              <svg-icon icon-class="message-clean" />
              清除未读
            </div>
          </Popconfirm>

          <div class="header-setting" @click="settingHandler">
            <svg-icon icon-class="message-setting" />
            消息设置
          </div>
        </div>
      </div>
    </div>
    <!-- 占位白底 -->
    <div class="write_box"></div>
    <div ref="scrollBox" class="message-container">
      <Tabs v-model="activeName" @tab-click="handleClick">
        <TabPane
          v-for="(item, index) in $store.getters['message/tabs']"
          :key="index"
          ref="pane"
          :name="item.name"
        >
          <Badge
            slot="label"
            :value="item.count"
            :hidden="item.count <= 0"
            :is-dot="item.count > 0 && item.open === 'T'"
            :max="99"
          >
            <span>{{ item.label }}</span>
          </Badge>
          <component :is="item.name" ref="com"></component>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
import { Tabs, TabPane, Badge, Popconfirm } from 'element-ui'

import { clearAllNoReadMes } from '@/api/message'
import BrandMessage from '@/components/Message/BrandMessage'
import ManuscriptMessage from '@/components/Message/ManuscriptMessage'
import BroadCasting from '@/components/Message/Broadcasting'
import Comment from '@/components/Message/Comment.vue'
import InviteAnswer from '@/components/Message/InviteAnswer.vue'
import LikeAndCollect from '@/components/Message/LikeAndCollect.vue'
import NewFans from '@/components/Message/NewFans.vue'
import WeeklyMessage from '@/components/Message/WeeklyMessage.vue'
import TodayMorningReading from '@/components/Message/TodayMorningReading.vue'
export default {
  name: 'MessageCenter',

  components: {
    Tabs,
    TabPane,
    Badge,
    Popconfirm,
    BrandMessage,
    ManuscriptMessage,
    BroadCasting,
    Comment,
    InviteAnswer,
    LikeAndCollect,
    NewFans,
    WeeklyMessage,
    TodayMorningReading,
  },
  data() {
    return {
      activeName: this.$store.state.message.activeName,
      loading: false,
    }
  },
  head() {
    return {
      title: '消息中心',
    }
  },
  // watchQuery(nv, ov) {
  //   this.$store.commit('message/setActive', nv.type)
  //   this.activeName = nv.type
  //   console.log(this.$refs.com, 'this.$refs.com')
  //   if (this.$refs.com) {
  //     const index = this.$store.state.message.activeIndex
  //     if (
  //       !this.$refs.com[index].$data.loaded &&
  //       !this.$refs.com[index].$data.loading
  //     ) {
  //       this.$refs.com[index].getList()
  //     }
  //   }
  // },
  watch: {
    '$route.query': {
      handler(nv, ov) {
        if (nv.hasOwnProperty('type')) {

          this.$store.commit('message/setActive', nv.type)
          this.activeName = nv.type
          if (this.$refs.com) {
            const index = this.$store.state.message.activeIndex
            if (
              !this.$refs.com[index].$data.loaded &&
              !this.$refs.com[index].$data.loading
            ) {
              this.$refs.com[index].getList && this.$refs.com[index].getList()
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeUpdate() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  updated() {
    window.addEventListener('scroll', this.handleScroll)
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  created() {
    if (this.$route.query.type) {
      this.$store.commit('message/setActive', this.$route.query.type)
      this.activeName = this.$route.query.type
    }

    this.$nextTick(() => {
      const index = this.$store.state.message.activeIndex
      if (this.$refs.com) {
        if (
          !this.$refs.com[index].$data.loaded &&
          !this.$refs.com[index].$data.loading
        ) {
          this.$refs.com[index].getList && this.$refs.com[index].getList()
        }
      }
    })
  },
  methods: {
    handleClick(tab, event) {
      // 埋点
      this.$analysys.btn_click(tab.name, document.title)
      // this.$store.commit('message/setActiveName', tab.name)
      // this.$store.commit(
      //   'message/setActiveIndex',
      //   this.$store.state.message.tabs.findIndex(
      //     (item) => item.name === tab.name
      //   )
      // )
      // 切换时，滚动到顶部
      // document.body.scrollTop = 0
      // document.documentElement.scrollTop = 0

      // if (tab.name === 'ManuscriptMessage') {
      //   // 更新稿件未读消息
      //   this.$axios
      //     .$request(
      //       clearAllManuscriptMes({
      //         username: this.$store.state.auth.user.username,
      //       })
      //     )
      //     .then((res) => {
      //       if (res && res.code === 1) {
      //         this.$store.commit('message/setTabsCount', {
      //           name: tab.name,
      //           count: 0,
      //         })
      //         // 更新未读消息 服务器更新
      //         this.$store.dispatch('message/getUserNoReadMessage')
      //       }
      //     })
      // }
      // 品牌消息不适用这种更新方式
      // if (tab.name !== 'BrandMessage' && tab.name !== 'ManuscriptMessage') {
      //   // 更新未读消息 本地更新
      //   this.$store.commit('message/setTabsCount', {
      //     name: tab.name,
      //     count: 0,
      //   })
      // }
// 重置滚动条
      document.querySelector('.message-content').scrollTop = 0
      this.$router.push({
        path: '/message-center',
        query: {
          type: tab.name,
        },
      })
    },
    handleScroll() {
      const windowHeight = window.innerHeight
      const scrollHeight = document.documentElement.scrollHeight
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const index = this.$store.state.message.activeIndex
      if (scrollTop + windowHeight >= scrollHeight - 50) {
        // 滚动到底部，执行加载逻辑
        if (
          this.$refs.com[index] &&
          !this.$refs.com[index].$data.loading &&
          !this.$refs.com[index].noMore
        ) {
          this.$refs.com[index].load()
        }
      }
    },
    settingHandler() {
      this.$router.push('/message-center/setting')
    },
    // 清除所有未读
    clearAllHandler() {
      // 埋点
      this.$analysys.btn_click('清除所有未读消息', document.title)
      // 调接口清除
      this.$axios
        .$request(
          clearAllNoReadMes({
            username: this.$store.state.auth.user.username,
            creationDate: this.timeStamp.changeTimeFormat(
              this.$store.state.auth.user.creationDate
            )
          })
        )
        .then((res) => {
          if (res && res.code === 1) {
            this.$message.success('清除成功')
            // 清除vuex中的数据
            this.$store.commit('message/clearAll')
            this.$store.commit('message/setClearAll', true)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.msg)
        })
    },
  },
}
</script>
<style lang="less" scoped>
.container-box {
  padding: 0 0 20px;
  min-width: 1000px;
  width: 1200px;
  background-color: #fff;
  // 头部
  .header {
    position: fixed;
    width: inherit;
    padding: 42px 20px 0px;
    box-sizing: border-box;
    z-index: 2;
    .header_box {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &::before {
      content: '';
      position: absolute;
      top: 0px;
      left: 0;
      width: inherit;
      height: 16px;
      background-color: #f8f8f8;
    }
    .header-title {
      font-size: 18px;
      line-height: 150%;
      color: #333333;
      font-weight: 600;
    }
    .header-right {
      display: flex;
      .header-setting {
        margin-left: 20px;
        font-size: 14px;
        line-height: 150%;
        display: flex;
        align-items: center;
        text-align: center;
        color: #666666;
        cursor: pointer;
        .svg-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  .write_box {
    position: fixed;
    width: inherit;
    height: 119px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    z-index: 1;
  }
  // 内容
  .message-container {
    padding: 134px 0 0;
  }
  /deep/ .el-tabs__header {
    margin-bottom: 0;
    padding: 10px 20px 0;
    position: fixed;
    top: 134px;
    z-index: 1;
  }
  /deep/ .el-tabs__item.is-active {
    color: #000;
  }
  /deep/ .el-tabs__active-bar {
    // display: none;
    height: 2px;
    background-color: #0581ce;
    border-radius: 1px;
  }

  /deep/ .el-tabs__item {
    padding-top: 8px;
    font-size: 16px;
    line-height: 150%;
    text-align: center;
    color: #808080;
    height: 30px;
  }
  /deep/ .el-tabs__item:focus.is-active.is-focus:not(:active) {
    box-shadow: none !important;
    /*去掉点击的时候带的阴影边框*/
  }

  /deep/ .el-tabs__nav {
    padding-bottom: 8px;
    padding-right: 50px;
  }
  /deep/ .el-badge__content.is-fixed {
    right: 0px;
    padding: 0 4px;
    background-color: #eb3323;
    font-size: 12px;
    line-height: 15px;
    // transform: translateX(100%) translateY(0);
    // transform: translate(0, 0) scale(1);
    border: 1px solid #ffffff;
    border-radius: 8px;
    box-sizing: border-box;
    height: auto;
    min-height: 10px;
    // min-width: 8px;
  }
  // /deep/ .el-tabs__item.is-active {
  //   position: relative;
  // }
  // /deep/ .el-tabs__item.is-active::after {
  //   position: absolute;
  //   bottom: 0;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   content: '';
  //   width: 34px;
  //   height: 2px;
  //   background-color: #000;
  //   border-radius: 2px;
  // }
  // /deep/ .el-tabs--top .el-tabs__item.is-top:nth-child(2).is-active::after {
  //   transform: translateX(-50% - 16px);
  // }
  // /deep/ .el-tabs--top .el-tabs__item.is-top:last-child.is-active::after {
  //   transform: translateX(-50% + 16px);
  // }
  /deep/ .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #eee;
  }
  /deep/ .el-tabs__content {
    padding: 0 20px;
    min-height: calc(100vh - 250px);
    box-sizing: border-box;
  }

  /deep/ .el-tabs__nav-wrap::after {
    height: 0;
  }
  /deep/ .el-tab-pane {
    min-height: calc(100vh - 250px);
    // max-height: calc(100vh - 192px);
  }
}
</style>
