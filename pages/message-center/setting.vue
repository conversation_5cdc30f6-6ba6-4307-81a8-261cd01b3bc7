<template>
  <div class="container-box">
    <div class="message_setting_container">
      <!-- 面包屑 -->
      <Breadcrumb separator-class="el-icon-arrow-right">
        <BreadcrumbItem :to="{ path: '/message-center' }"
          >消息中心</BreadcrumbItem
        >
        <BreadcrumbItem :to="{ path: '/' }">消息设置</BreadcrumbItem>
      </Breadcrumb>
      <!-- 提示文字 -->
      <div class="tip">
        <div class="tip_title">消息免打扰</div>
        <div class="tip_des">
          开启后，新通知将用圆点展示，不再显示消息未读数提醒
        </div>
      </div>
      <!-- 设置选项 -->
      <div class="context">
        <div
          v-for="(item, index) in $store.state.message.settings"
          :key="index"
          class="set_item"
        >
          <div class="item_label">{{ item.label }}</div>
          <el-switch
            v-model="item.open"
            active-value="T"
            inactive-value="F"
            :disabled="item.flag"
            @change="changeStatus(item)"
          >
          </el-switch>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { Breadcrumb, BreadcrumbItem, Switch } from 'element-ui'
import { updateMessageSetting } from '@/api/message'
export default {
  name: 'MessageSetting',
  components: {
    Breadcrumb,
    BreadcrumbItem,
    'el-switch': Switch,
  },
  data() {
    return {
      val: true,
    }
  },
  methods: {
    changeStatus(val) {
      if (val.flag) return
      // 埋点
      this.$analysys.btn_click(
        (val.open === 'T' ? '开启' : '关闭') + ' ' + val.name + ' 消息免打扰',
        '消息免打扰'
      )
      this.$store.commit('message/changeSetFlag', {
        name: val.name,
        flag: true,
      })
      this.$axios
        .$request(
          updateMessageSetting({
            loginUserId: this.$store.state.auth.user.id,
            classification: val.classification,
            switchStr: val.open,
          })
        )
        .then((res) => {
          if (res.code === 1) {
            this.$message.success('修改成功')
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.$message.error(err.msg)
          // 重置状态
          val.open = val.open === 'T' ? 'F' : 'T'
          this.$store.commit('message/setSettings', val)
        })
        .finally(() => {
          this.$store.commit('message/changeSetFlag', {
            name: val.name,
            flag: false,
          })
        })
    },
  },
}
</script>
<style lang="less" scoped>
.container-box {
  margin-top: 16px;
  padding: 0 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: calc(100vh - 96px);
}
.message_setting_container {
  padding-top: 16px;

  .tip {
    margin-top: 24px;
    padding: 16px 0;
    .tip_title {
      font-size: 18px;
      line-height: 150%;
      color: #333;
      font-weight: 600;
    }
    .tip_des {
      margin-top: 8px;
      font-size: 14px;
      line-height: 150%;
      color: #666;
    }
  }
  .context {
    margin-top: 4px;
    display: flex;
    justify-content: flex-start;
    align-content: center;
    flex-wrap: wrap;
    width: 100%;
    .set_item {
      margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      height: 78px;
      width: calc(33.33% - 20px);
      box-sizing: border-box;
      border: 1px solid #eee;
      border-radius: 6px;
      background-color: #fbfbfb;
      &:nth-of-type(3n + 1) {
        margin-left: 0;
      }
      .item_label {
        font-size: 16px;
        line-height: 150%;
        color: #000;
      }
      /deep/ .el-switch.is-checked .el-switch__core {
        width: 39px;
        height: 24px;
        background: #0581ce;
        border-radius: 999px;
      }
      /deep/ .el-switch__core {
        width: 39px;
        height: 24px;
        background: #ccc;
        border-radius: 999px;
      }
      /deep/ .el-switch.is-checked .el-switch__core::after {
        margin-left: -19px;
        left: 100%;
      }
      /deep/ .el-switch__core:after {
        width: 18px;
        height: 18px;
        top: 2px;
        left: 2px;
      }
    }
  }
}
</style>
