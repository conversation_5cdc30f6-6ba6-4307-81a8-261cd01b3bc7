<template>
  <!--  <client-only>组件在客户端渲染</client-only>-->
  <div id='app' ref='app' class='home-content'>
    <el-container>
      <!-- 导航 -->
      <Nav
        v-if="!onlyMain"
      >
      </Nav>
      <el-main>
        <!-- 内容 -->
        <!-- nuxt规定的子路由插槽  <nuxt-child></nuxt-child> -->
        <router-view :key='key'/>
        <!--          <nuxt-child></nuxt-child>-->
      </el-main>

      <el-footer
        v-show="!onlyMain"
        :style="$route.path === '/search' ||$route.path === '/advanced-search-result'||$route.path === '/advanced-search' ? {marginTop:0} : {}">
        <!-- 底部 -->
        <FooterNav></FooterNav>
      </el-footer>
    </el-container>
    <!-- 脑医汇App -->
    <div v-if="!onlyMain" class="brainmed_app">
      <p class="brainmed_content">
        <span>脑医汇</span>
        <span>
          App
          <svg-icon icon-class="nao_app" class-name="icons"/>
        </span>
      </p>
      <div class="qr_code">
        <img src="~assets/images/app_code.png" alt=""/>
      </div>
    </div>
    <transition name="el-zoom-in-center">
      <div v-show="backtopFlag" class="backtop" @click="backtopFun">
        <svg-icon class="backtopSvg" icon-class="backtop"/>
      </div>
    </transition>
    <!--身份认证弹框-->
    <identity-authentication
      :authentication-data-info="$store.state.authenticationDataInfo"
      :authentication-dialog.sync="$store.state.authenticationDialog"
      :identity-current-father="$store.state.auth.user.identity"
      :identity-flag="!!$store.state.auth.user.identity"
      :reselect-flag="false"
      @editFlag="authenticationDialogFun"
    ></identity-authentication>
    <PersonalizedPopup
      v-if="PersonalizedPopupVisible"
      @cancel="PersonalizedPopupVisible = false"
    />
  </div>
</template>

<script>
import {luckDrawAddOpportunity} from 'assets/helpers/luck-draw'
import {getWebApiFirstCategoryList} from '../api/bms' // 身份认证
import {getUserInterestedSubspecialtyList} from "../api/personalized/default";
import PersonalizedPopup from "../components/optimize-components/page-components/signin/PersonalizedPopup/index.vue";
import FooterNav from "../components/optimize-components/public/FooterNav/index.vue";
import Nav from '~/components/PageComponents/Nav/Nav' // 导航
// import Footer from '@/components/PageComponents/Footer/Footer' // 页尾
import {getDepartments, getSpecialities, getTitles} from '@/api/register'
import {getWebApiChannel} from '@/api/channel'
import IdentityAuthentication from '@/components/IdentityAuthentication/IdentityAuthentication'

export default {
  name: 'HomePage',
  components: {
    PersonalizedPopup,
    Nav,
    FooterNav,
    IdentityAuthentication,
  },
  async asyncData({app, params, error, store, query, req}) {
    const [request0, request1, request2, request3, request4] =
      await Promise.all([
        app.$axios.$request(getWebApiFirstCategoryList()),
        app.$axios.$request(getDepartments()),
        app.$axios.$request(getTitles()),
        app.$axios.$request(getSpecialities()),
        app.$axios.$request(getWebApiChannel()),
      ])
    const arrayselect = [request1.list, request2.result, request3.list]
    store.commit('editSeletIdentityFun', arrayselect)
    const channelListData = []
    // eslint-disable-next-line array-callback-return
    request4.list.map((item) => {
      channelListData.push({
        subName: item.name,
        subUrl: `/channel/home?channelId=${
          item.channelId
        }&channelTitle=${encodeURIComponent(item.name)}&mpId=${
          item.mpSubspecialityId
        }&ocsId=${item.ocsSubspecialityId}`,
        isEnable: true,
        id: item.channelId,
      })
    })
    store.commit('editChannelListState', channelListData)

    // 品牌一级分类
    const categoryList = request0.list.map((item) => {
      return {
        subName: item.name,
        subUrl: `/bms/home/<USER>
        isEnable: true,
        id: item.id   ,
        code: item.code,
      }
    })

    store.commit('bms/setFirstCategoryListHandler', categoryList)

    return {}
  },
  data() {
    return {
      PersonalizedPopupVisible: false,
      scroll: 0,
      backtopFlag: false, // 返回顶部开关
      scrolltop: null, // 距离顶部距离
      timer: null, // 超时
      timeAuth: null, // 计时器
      onlyMain: false, // 是否只显示内容
    }
  },
  computed: {
    key() {
      return this.$route.path   // + Math.random()
      /**
       * 从/page/a => /page/b，由于这两个路由的$route.path并不一样，所以组件被强制不复用，相关钩子加载顺序为beforeRouteUpdate => created => mounted

       从/page?id=a => /page?id=b，由于这两个路由的$route.path一样，所以和没设置key属性一样，会复用组件，相关钩子加载顺序为：beforeRouteUpdate
       */
    }
  },
  watch: {
    scroll: {
      handler: 'showTop'
    }
  },
  created() {
    /**
     * 隐藏 Nav 和 footer
     */
    if (this.$route.query.onlyMain || this.$route.query.source === "en-brainmed" || this.$store.state.cs.TerminalType) {
      this.onlyMain = true;
    } else {
      this.onlyMain = false;
    }
  },
  mounted() {
    const personalizedStatus = window.localStorage.getItem("personalized_popup_visible")
    if (this.$store.state.auth.token && !personalizedStatus) {
      this.$axios.$request(getUserInterestedSubspecialtyList()).then(res => {
        if (res.code === 1) {
          if (!res.list || res.list.length === 0) {
            if (this.$route.query.source !== "en-brainmed") {
              this.PersonalizedPopupVisible = true;
            }
          }
        }
      })
    }


    const _this = this
    /**
     * 抽奖机会+1
     */
    luckDrawAddOpportunity(this)
    /**
     * 账户关联
     */
    if (this.$store.state.auth.user.id && !window.sessionStorage.getItem('aliasUser')) {
      window.sessionStorage.setItem('aliasUser', 'true')
      this.$analysys.aliasUser(this.$store.state.auth.user.id)
    }

    /**
     * 有倒计时的话 代表用户是记住密码了,  失效时间  - 当前时间 = 倒计时
     * 如果 失效时间 < 6s 直接退出
     * 如果失效时间 小于0 直接退出
     * 每次进入页面 监听 3 t
     */
    if (this.$cookies.get('medtion_expirationTime_only_sign')) {
      let timeout =
        this.$cookies.get('medtion_expirationTime_only_sign') - Date.now()
      isNaN(timeout) ? (timeout = null) : null

      if (timeout < 6000) {
        this.$toast('登录失效,请重新登录')
        this.$store.commit('auth/logout', {this: this})
        return
      }
      this.timeAuth = setTimeout(() => {
        this.$toast('登录失效,请重新登录')
        // 判断timeout的值
        this.$store.commit('auth/logout', {this: this})
      }, timeout)
    }


    /**
     * 切换浏览器tab 获取登录状态
     */
    document.addEventListener('visibilitychange', () => {
      // visible-显示，hidden-隐藏
      if (document.visibilityState === 'visible') {
        luckDrawAddOpportunity(_this)
        if (this.$cookies.get('medtion_token_only_sign')) {
          this.$cookies.set('medtion_isLogged_only_sign', true, {
            path: '/',
            sameSite: 'lax',
          })
          this.$store.commit('auth/setUserFn', {
            isLogged: this.$cookies.get('medtion_isLogged_only_sign'),
            token: this.$cookies.get('medtion_token_only_sign'),
            unionid: this.$cookies.get('medtion_unionid_only_sign'),
            user: this.$cookies.get('medtion_user_only_sign'),
            weChatInfo: this.$cookies.get('medtion_weChatInfo_only_sign'),
            expirationTime: this.$cookies.get('medtion_expirationTime_only_sign')
          })
        } else {
          this.$store.commit('auth/setUserFn', {
            isLogged: false,
            token: '',
            unionid: '',
            user: {},
            weChatInfo: {},
            expirationTime: 0,
          })
        }
      }
    })
    /**
     * 判断浏览器默认语言环境
     */
    switch (navigator.language.toLowerCase().indexOf('zh')) {
      case -1:
        this.$store.commit('SET_LANG', 'en')
        this.$i18n.locale = 'en'
        break
      default:
        this.$store.commit('SET_LANG', 'zh')
        this.$i18n.locale = 'zh'
        break
    }

    if (this.$route.query.source === "en-brainmed") {
      this.$store.commit('SET_LANG', 'en')
      this.$i18n.locale = 'en'
    }


    this.$store.commit('editBackUrl', window.location.href)
    window.addEventListener('scroll', this.getScroll) // 导航固定
    if (this.$route.query.code && !this.$route.query.wechat_login) {
      this.weChatFun()
    }
    if (this.$route.query.token) {
      this.$store.dispatch('auth/insyncLogin', this)
    }
    // 监听滚动条
    window.onscroll = function () {
      /**
       * 滚动条到滑动大于页面的五分之一, 返回顶部显示
       */
      clearTimeout(_this.timer)
      _this.timer = setTimeout(() => {
        _this.scrolltop = document.documentElement.scrollTop
        if (
          _this.scrolltop >=
          Math.round(document.querySelector('.el-main').scrollHeight / 5)
        ) {
          _this.backtopFlag = true
        } else {
          _this.backtopFlag = false
        }
      }, 150)
    }
  },
  methods: {
    /**
     * 身份认证
     */
    authenticationDialogFun(data) {
      this.$store.commit('editAuthenticationDialog', data)
    },
    // 导航固定
    showTop(newValue, oldValue) {
      this.$store.commit('global/setScrollHandler', newValue)
      const topNavSelect = document.querySelector('.top_nav_select')
      const headerBox = document.querySelector('.el-header')
      const returntop = document.getElementById('returntop')
      if (newValue > 0) {
        if (headerBox) {
          headerBox.style.cssText = 'box-shadow: 0 2px 10px 0 rgba(0,0,0,0.1);'
        }

      } else {
        if (headerBox) {
          headerBox.style.cssText = 'box-shadow: none'
        }
      }
      if (this.$route.path === '/introduce') {
        if (newValue > 0) {
          if (topNavSelect) {
            topNavSelect.style.cssText = 'transform:translateY(0%)'
          }

          if (returntop) {
            returntop.style.cssText = 'transform:translateY(-100%)'
          }

        } else {
          if (topNavSelect) {
            topNavSelect.style.cssText = 'transform:translateY(-100%)'
          }

          if (returntop) {
            returntop.style.cssText = 'transform:none'
          }

        }
      } else if (/editor|\/topic-circle\/write/.test(this.$route.path)) {

        if (returntop) {
          returntop.style.cssText = 'transform:translateY(-100%);margin-top: -60px;'
        }

      } else {

        if (returntop) {
          returntop.style.cssText = 'transform:none'
        }

      }
    },
    getScroll() {
      this.scroll =
        document.documentElement.scrollTop || document.body.scrollTop
      // 窗口高度
      const windowHeight =
        document.documentElement.clientHeight || document.body.clientHeight
      // 页面高度
      const documentHeight =
        document.documentElement.scrollHeight || document.body.scrollHeight

      if (windowHeight + this.scroll === documentHeight) {
        // console.log("页面触底啦")
        this.$store.commit(
          'global/setBottomLoadingHandler',
          parseFloat(Math.random() + 1, 10)
        )
      }
    },
    // 微信登录逻辑
    async weChatFun() {
      await this.$store.dispatch('auth/wxLogin', this)
      /**
       * 有倒计时的话 代表用户是记住密码了,  失效时间  - 当前时间 = 倒计时
       * 如果 失效时间 < 6s 直接退出
       * 如果失效时间 小于0 直接退出
       * 每次进入页面 监听 3 t
       */
      if (this.$cookies.get('medtion_expirationTime_only_sign')) {
        let timeout = this.$cookies.get('medtion_expirationTime_only_sign') - Date.now()
        isNaN(timeout) ? timeout = null : null
        if (timeout < 6000) {
          this.$toast('登录失效,请重新登录')
          this.$store.commit('auth/logout', {this: this})
          return
        }
        this.timeAuth = setTimeout(() => {
          this.$toast('登录失效,请重新登录')
          // 判断timeout的值
          this.$store.commit('auth/logout', {this: this})
        }, timeout)
      }
    },
    // 返回顶部
    backtopFun() {
      this.$tool.scrollIntoTop()
      this.$analysys.btn_click('返回顶部', document.title)
    },
  },
  beforeDestroy() {
    clearTimeout(this.timeAuth)
  }
}
</script>
<style lang="less">
/* message box */
.personaldata-messagebox {
  width: 372px;
  padding: 36px 0 40px;
  text-align: center;
  border-radius: 30px;
  box-sizing: border-box;

  p {
    font-size: 16px;
    font-weight: 500;
    text-align: left;
    padding: 0 50px;
  }
}

.personaldata-messagebox > .el-message-box__content {
  padding: 0;
  text-align: center;
}

.personaldata-messagebox > .el-message-box__btns {
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  margin-top: 30px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button {
  float: left;
  width: 120px;
  height: 38px;
  line-height: 38px;
  padding: 0 !important;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--small {
  background: #efefef;
  border-radius: 12px;
  border: 0;
  color: #80a4b9;
  font-size: 14px;
}

.personaldata-messagebox > .el-message-box__btns > .el-button--primary {
  background: #0581ce;
  color: #ffffff;
}
</style>
<style lang="less" scoped>
@import './index';
</style>
