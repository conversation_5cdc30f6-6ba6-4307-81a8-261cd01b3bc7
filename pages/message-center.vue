<!--
 * @Author: 田斌 <EMAIL>
 * @Date: 2023-06-01 16:22:49
 * @LastEditors: 田斌 <EMAIL>
 * @LastEditTime: 2023-06-06 13:58:21
 * @FilePath: \medtion-nuxt\pages\message-center.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app1" ref="app1" class="home-content message-content">
    <el-container direction="vertical">
      <!-- 导航 -->
      <NavMessage></NavMessage>
      <el-main>
        <!-- 内容 -->
        <!-- nuxt规定的子路由插槽  <nuxt-child></nuxt-child> -->

        <nuxt-child></nuxt-child>
      </el-main>
    </el-container>
    <!-- 脑医汇App -->
    <!--    <div class="brainmed_app">-->
    <!--      <p class="brainmed_content">-->
    <!--        <span>脑医汇</span>-->
    <!--        <span>-->
    <!--          App-->
    <!--          <svg-icon icon-class="nao_app" class-name="icons" />-->
    <!--        </span>-->
    <!--      </p>-->
    <!--      <div class="qr_code">-->
    <!--        <img src="~assets/images/app_code.png" alt="" />-->
    <!--      </div>-->
    <!--    </div>-->
    <transition name="el-zoom-in-center">
      <div v-show="backtopFlag" class="backtop" @click="backtopFun">
        <svg-icon class="backtopSvg" icon-class="backtop" />
      </div>
    </transition>
    <!--身份认证弹框-->
    <!-- <identity-authentication
      :authenticationDataInfo='$store.state.authenticationDataInfo'
      :authenticationDialog.sync='$store.state.authenticationDialog'
      :identity-current-father='$store.state.auth.user.identity'
      :identityFlag='!!$store.state.auth.user.identity'
      :reselect-flag='false'
      @editFlag='authenticationDialogFun'></identity-authentication> -->
  </div>
</template>

<script>
import { luckDrawAddOpportunity } from 'assets/helpers/luck-draw'
import NavMessage from '@/components/PageComponents/Nav/NavMessage.vue'
export default {
  name: 'MessageCenter',
  components: {
    NavMessage,
  },
  data() {
    return {
      tab: '',
      backtopFlag: false, // 返回顶部开关
    }
  },
  // async asyncData({ store }) {
  //   // 判断是否登录
  //   if (store.state.auth.token) {
  //     await Promise.all([
  //       store.dispatch('message/getUserNoReadMessage', false),
  //       store.dispatch('message/getSettings'),
  //     ])
  //     // // 获取未读消息数
  //     // await store.dispatch('message/getUserNoReadMessage', false)
  //     // // 获取消息设置
  //     // await store.dispatch('message/getSettings')
  //   }
  //   return {}
  // },
  mounted() {
    const _this = this
    /**
     * 抽奖机会+1
     */
    luckDrawAddOpportunity(this)
    /**
     * 账户关联
     */
    if (
      this.$store.state.auth.user.id &&
      !sessionStorage.getItem('aliasUser')
    ) {
      sessionStorage.setItem('aliasUser', 'true')
      this.$analysys.aliasUser(this.$store.state.auth.user.id,this.$store.state.auth.user.identity)
    }

    /**
     * 有倒计时的话 代表用户是记住密码了,  失效时间  - 当前时间 = 倒计时
     * 如果 失效时间 < 6s 直接退出
     * 如果失效时间 小于0 直接退出
     * 每次进入页面 监听 3 t
     */
    if (this.$cookies.get('medtion_expirationTime_only_sign')) {
      let timeout =
        this.$cookies.get('medtion_expirationTime_only_sign') - Date.now()
      isNaN(timeout) ? (timeout = null) : null

      if (timeout < 6000) {
        this.$toast('登录失效,请重新登录')
        this.$store.commit('auth/logout', { this: this })
        return
      }
      this.timeAuth = setTimeout(() => {
        this.$toast('登录失效,请重新登录')
        // 判断timeout的值
        this.$store.commit('auth/logout', { this: this })
      }, timeout)
    }

    /**
     * 切换浏览器tab 获取登录状态
     */
    document.addEventListener('visibilitychange', () => {
      // visible-显示，hidden-隐藏
      if (document.visibilityState === 'visible') {
        luckDrawAddOpportunity(_this)
        if (this.$cookies.get('medtion_token_only_sign')) {
          this.$cookies.set('medtion_isLogged_only_sign', true, {
            path: '/',
            sameSite: 'lax',
          })
          this.$store.commit('auth/setUserFn', {
            isLogged: this.$cookies.get('medtion_isLogged_only_sign'),
            token: this.$cookies.get('medtion_token_only_sign'),
            unionid: this.$cookies.get('medtion_unionid_only_sign'),
            user: this.$cookies.get('medtion_user_only_sign'),
            weChatInfo: this.$cookies.get('medtion_weChatInfo_only_sign'),
            expirationTime: this.$cookies.get(
              'medtion_expirationTime_only_sign'
            ),
          })
        } else {
          this.$store.commit('auth/setUserFn', {
            isLogged: false,
            token: '',
            unionid: '',
            user: {},
            weChatInfo: {},
            expirationTime: 0,
          })
        }
      }
    })
    /**
     * 判断浏览器默认语言环境
     */
    switch (navigator.language.toLowerCase().indexOf('zh')) {
      case -1:
        this.$store.commit('SET_LANG', 'en')
        this.$i18n.locale = 'en'
        break
      default:
        this.$store.commit('SET_LANG', 'zh')
        this.$i18n.locale = 'zh'
        break
    }

    this.$store.commit('editBackUrl', window.location.href)
    window.addEventListener('scroll', this.getScroll) // 导航固定
    if (this.$route.query.code && !this.$route.query.wechat_login) {
      this.weChatFun()
    }
    if (this.$route.query.token) {
      this.$store.dispatch('auth/insyncLogin', this)
    }
    // 监听滚动条
    window.onscroll = function () {
      /**
       * 滚动条到滑动大于页面的五分之一, 返回顶部显示
       */
      clearTimeout(_this.timer)
      _this.timer = setTimeout(() => {
        _this.scrolltop = document.documentElement.scrollTop
        if (
          _this.scrolltop >=
          Math.round(document.querySelector('.el-main').scrollHeight / 5)
        ) {
          _this.backtopFlag = true
        } else {
          _this.backtopFlag = false
        }
      }, 150)
    }
  },
  beforeDestroy() {
    clearTimeout(this.timeAuth)
  },
  methods: {
    /**
     * 身份认证
     */
    authenticationDialogFun(data) {
      this.$store.commit('editAuthenticationDialog', data)
    },
    // 导航固定
    showTop(newValue, oldValue) {
      this.$store.commit('global/setScrollHandler', newValue)
      const personalHomepageNav = document.querySelector(
        '.personal-homepage-box'
      )
      const headerLeft = document.querySelector('.header-bigbox-left')
      const headerSearch = document.querySelector('.home-search')
      const releaseButton = document.querySelector('.release-button')
      const top_nav_select = document.querySelector('.top_nav_select')
      const headerBox = document.querySelector('.el-header')
      const returntop = document.getElementById('returntop1')
      if (newValue > 0) {
        headerBox.style.cssText = 'box-shadow: 0 2px 10px 0 rgba(0,0,0,0.1);'
      } else {
        headerBox.style.cssText = 'box-shadow: none'
      }
      if (this.$route.path === '/introduce') {
        if (newValue > 0) {
          top_nav_select
            ? (top_nav_select.style.cssText = 'transform:translateY(0%)')
            : null
          returntop.style.cssText = 'transform:translateY(-100%)'
        } else {
          top_nav_select
            ? (top_nav_select.style.cssText = 'transform:translateY(-100%)')
            : null
          returntop.style.cssText = 'transform:none'
        }
      } else {
        returntop.style.cssText = 'transform:none'
      }
      if (this.$route.name === 'index-user-center') {
        const columnBox = document.getElementById('column-box')
        const columnTop = columnBox ? columnBox.offsetTop : 10
        if (newValue > columnTop - 5) {
          releaseButton.style.cssText =
            'transform:translateY(-100%);opacity: 0;'
          headerSearch.style.cssText = 'transform:translateY(-100%)'
          headerLeft.style.cssText = 'transform:translateY(-100%)'
          personalHomepageNav.style.transform = 'translateY(0%)'
        } else {
          releaseButton.style.cssText = 'transform:translateY(0%)'
          headerLeft.style.cssText = 'transform:translateY(0%)'
          headerSearch.style.cssText = 'transform:none'
          personalHomepageNav.style.transform = 'translateY(-100%)'
        }
      } else {
        releaseButton.style.cssText = 'transform:none'
        headerLeft.style.cssText = 'transform:none'
        headerSearch.style.cssText = 'transform:none'
        personalHomepageNav.style.transform = 'translateY(-100%)'
      }
    },
    getScroll() {
      this.scroll =
        document.documentElement.scrollTop || document.body.scrollTop
      // 窗口高度
      const windowHeight =
        document.documentElement.clientHeight || document.body.clientHeight
      // 页面高度
      const documentHeight =
        document.documentElement.scrollHeight || document.body.scrollHeight

      if (windowHeight + this.scroll === documentHeight) {
        // console.log("页面触底啦")
        this.$store.commit(
          'global/setBottomLoadingHandler',
          parseFloat(Math.random() + 1, 10)
        )
      }
    },
    // 微信登录逻辑
    async weChatFun() {
      await this.$store.dispatch('auth/wxLogin', this)
      /**
       * 有倒计时的话 代表用户是记住密码了,  失效时间  - 当前时间 = 倒计时
       * 如果 失效时间 < 6s 直接退出
       * 如果失效时间 小于0 直接退出
       * 每次进入页面 监听 3 t
       */
      if (this.$cookies.get('medtion_expirationTime_only_sign')) {
        let timeout =
          this.$cookies.get('medtion_expirationTime_only_sign') - Date.now()
        isNaN(timeout) ? (timeout = null) : null
        if (timeout < 6000) {
          this.$toast('登录失效,请重新登录')
          this.$store.commit('auth/logout', { this: this })
          return
        }
        this.timeAuth = setTimeout(() => {
          this.$toast('登录失效,请重新登录')
          // 判断timeout的值
          this.$store.commit('auth/logout', { this: this })
        }, timeout)
      }
    },
    // 返回顶部
    backtopFun() {
      this.$tool.scrollIntoTop()
      this.$analysys.btn_click('返回顶部', document.title)
    },
  },
}
</script>

<style lang="less" scoped>
@import url('./index');
</style>
<style lang="less" scoped>
.message-content {
  //height: 100vh;
  //overflow-y: scroll;
  /deep/ .el-container {
    // height: 100vh;
    // overflow: hidden;
    background-color: #F8F8F8;
  }
  /deep/ .el-main {
    margin-bottom: 36px;
    padding: 0;
    display: flex;

  }
}
</style>
<style>

</style>
<style lang="less">

.clean-message-confirm {
  padding: 0;
  z-index: 2000;
  margin-top: 13px !important;
  //width: 232px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  & > div {
    padding: 20px 18px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    align-items: center !important;
    & > p {
      z-index: 2;
      background-color: #fff;
      font-size: 14px !important;
      line-height: 150% !important;
      color: #333333 !important;
    }
    & > div {
      margin-top: 16px;
      width: 100%;
      //display: flex;
      //justify-content: center;
      //align-items: center;
      //& > button {
      //  border: none;
      //  margin: 0 5px;
      //  padding: 0;
      //  width: 60px;
      //  height: 24px;
      //  border-radius: 18px;
      //  font-size: 12px;
      //  line-height: 24px;
      //  text-align: center;
      //  background: #0581ce;
      //  color: #fff;
      //}
      //& > button:first-child {
      //  background: #eeeeee;
      //  color: #666666;
      //}
    }
  }
}
</style>
