<!--
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-03-14 09:39:19
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-03-14 10:10:41
 * @FilePath: /medtion-nuxt/pages/favorite/favorite.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <component :is="header"/>
    <!--    <Header v-if="!onlyMain"></Header>-->
    <!--    <SearchNav/>-->
    <main>
      <nuxt-child></nuxt-child>
    </main>

  </div>
</template>

<script>
import {Header, SearchNav} from "../opt-components/template";
import {init} from "../assets/helpers/index-file-mounted";

export default {
  name: "MainPage",
  components: {
    Header,
    SearchNav,
  },
  data() {
    return {
      timeAuth: null,
      PersonalizedPopupVisible: false,
    }
  },
  computed: {
    header() {

        return 'Header'

    }
  },
  mounted() {
    // 入口文件 初始化
    init(this)

    // 监听滚动条变化 (待优化)
    window.addEventListener('scroll', this.getScroll)
  },
  beforeDestroy() {
    // eslint-disable-next-line no-undef
    clearTimeout(this.timeAuth)
    window.removeEventListener('scroll', this.getScroll)
  },
  methods: {
    getScroll() {
      const scroll = document.documentElement.scrollTop || document.body.scrollTop
      this.$store.commit('global/setScrollHandler', scroll)

      // 窗口高度
      const windowHeight = document.documentElement.clientHeight || document.body.clientHeight
      // 页面高度
      const documentHeight = document.documentElement.scrollHeight || document.body.scrollHeight

      if (windowHeight + scroll >= documentHeight) {
        // 页面触底
        this.$store.commit('global/setBottomLoadingHandler', parseFloat(Math.random() + 1, 10))
      }
    },
  }
}
</script>

<style scoped lang="less">
.home-content {
  //overflow-x: hidden;

  .el-main {
    padding: 0;
    overflow: unset;
  }

  .el-footer {
    height: auto !important;
    background: #F6F6F6;
    margin-top: 50px;
  }
}

html,
body,
#app {
  height: auto;
}

/*返回顶部*/
.backtop {
  z-index: 999;
  width: 48px;
  height: 48px;
  position: fixed;
  right: 2vw;
  text-align: center;
  list-style: none;
  line-height: 48px;
  border-radius: 6px !important;
  bottom: calc(225px + 20px + 15px) !important;
  background: #d0d7dc !important;

  .backtopSvg {
    color: #789db4;
    width: 18px;
    height: 18px;
  }

  &:hover {
    background: #3a8ee6 !important;
    cursor: pointer;

    .backtopSvg {
      color: #789db4 !important;
    }
  }
}

/deep/ .el-footer {
  padding: 0 !important;
}

</style>
