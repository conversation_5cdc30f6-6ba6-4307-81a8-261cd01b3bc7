<!--
 * @Author: tb0912 <EMAIL>
 * @Date: 2025-03-14 09:40:55
 * @LastEditors: tb0912 <EMAIL>
 * @LastEditTime: 2025-07-18 11:13:58
 * @FilePath: /medtion-nuxt/pages/favorite/detail/_id.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="favorite-detail">
    <!-- 资料库不存在的缺省状态 -->
    <div v-if="!favoriteExists" class="no_content">
      <img class="no_contentimg" :src="require('@/assets/images/null_content.png')" alt="">
      <p>内容已失效</p>
    </div>

    <!-- 正常的资料库内容 -->
    <div v-else class="favorite-content">
      <!-- 头部资料库信息 -->
      <div class="library-info">
        <div class="cover">
          <img :src="favoriteDetail.cover || require('@/assets/images/h5_img_thumbnail_16_9.png')" alt="" />
        </div>
        <div class="info">
          <h1 class="title">{{ favoriteDetail.name || '资料库名称' }}</h1>
          <p class="desc">{{ favoriteDetail.description }}</p>
          <div class="meta">
            <span class="creator">创建者：<span class="creator-name" @click="goToCreatorProfile">{{ favoriteDetail.creatorName || '未知' }}</span></span>
            <div class="right">
              <span class="count">{{ favoriteDetail.contentCount }}个内容</span>
              <span class="status">· {{ favoriteDetail.isPublic ? '公开' : '私有' }}</span>
            </div>
          </div>
        </div>
        <div v-if="favoriteDetail.isOwner" class="edit" @click="toggleMenu" :class="{ active: showMenu }">
          <span>编辑信息</span>
          <div class="menu" v-if="showMenu">
            <div class="menu-item" @click="showEditDialog">编辑信息</div>
            <div class="menu-item" @click="showAddContentDialog">添加内容</div>
            <div class="menu-item" @click="showBatchDeleteDialog">批量管理</div>
            <div class="menu-item delete" @click="showDeleteDialog">删除资料库</div>
          </div>
        </div>
      </div>



      <!-- 内容列表 -->
      <div class="content-list" v-loading="contentLoading">
        <div v-if="contentList.length === 0 && !contentLoading" class="no_content" style="padding-top: 1.6rem">
          <img class="no_contentimg" :src="require('@/assets/images/null_content.png')" alt="">
          <p>该资料库还没加入任何内容</p>
        </div>
        <div v-else>
          <template v-for="item in contentList">
             <InfoArticleItem
          v-if="item.contentType === 'info' || item.contentType === 'pInfo'"
          :id="item.content.infoId"
          :key="item.userCollectId"
          :img="item.content.infoCover"
          :title="item.content.infoTitle"
          :publish-time="timeStamp.timestampFormat(item.collectTime / 1000)"
          :author-names="item.content.AuthorNames"
          :meta-description="item.content.metaDescription"
          :show-type="item.showType"
          :bms-auth="item.content.bmsAuth"
        />
        <MeetingArticleInstitutionItem
          v-else-if="item.contentType === 'meeting'"
          :id="item.content.meetingId"
          :key="item.userCollectId"
          :meeting-name="item.content.meetingName"
          :meeting-address="item.content.meetingAddress"
          :app-main-pic="item.content.meetingCover"
          :meeting-date-str="item.content.meetingDateStr"
          :meeting-id="item.content.meetingId"
          :meeting-live-status="item.content.meetingLiveStatus || 'NL'"
          :description="item.content.description"
        />
        <PgcArticleItem
          v-else-if="item.contentType === 'mpArticle' || item.contentType === 'pMpArticle'"
          :id="item.content.mpArticleId"
          :key="item.userCollectId"
          :title="item.content.mpArticleTitle"
          :img="item.content.mpArticleCover"
          :subspecialtys="item.content.subspecialtys"
          :product-list="item.content.productList"
          :author-name="item.content.creator.realName"
          :article-date='item.content.creationTime'
          :article-read='item.content.showViews'
          :article-comment='item.content.showComments'
          :article-fabulous='item.content.showDiggs'
          :paragraph-type="item.content.paragraphType"
          :type="item.content.type"
          :is-edit="false"
          :bms-auth="item.content.bmsAuth"
        />
        <ClassroomArticleItem
          v-else-if="item.contentType === 'courseVideo'"
          :id="item.content.courseId"
          :key="item.userCollectId"
          :cover="item.content.courseCover"
          :name="item.content.name"
          :speaker-names="item.content.speakerNames"
          :score="item.content.score"
          :show-views="item.content.showViews"
          :money="item.content.money"
          :subspecialties="item.content.subspecialties"
          :introduction="item.content.introduction"
          :is-label-show="true"
          :show-type="item.showType"
        />
        <BmsVideoItemInformation
          v-else-if="item.contentType === 'bmsVideo'"
          :id='item.content.id'
          :key="item.userCollectId"
          :img='item.content.cover'
          :title='item.content.videoName'
          :category-id='"-"'
          :product-list='item.content.productList'
          :create-time='timeStamp.timestamp_13(item.collectTime, "y-m-d")'
          :show-type="item.showType"
        />
        <VideoItemInformation
          v-else-if="item.contentType === 'shortVideo'"
          :id='item.content.id'
          :key="item.userCollectId"
          :img='item.content.cover'
          :title='item.content.title'
          :article-date='timeStamp.timestampFormat(item.collectTime / 1000)'
          :author-list='[{
            id:item.content.creator.id,
            userAvatar:item.content.creator.avatarAddress,
            authorName:item.content.creator.realName
          }]'
          :category-id='"-"'
          :article-comment='item.content.comments'
          :article-fabulous='item.content.diggs'
          :article-read='item.content.views'
          :product-list='item.content.productList'
        />
        <QADefaultArticleItem
          v-else-if="item.contentType === 'communityQA' && item.content.images.length>0"
          :key="item.userCollectId"
          :q-a-id="item.content.id"
          :title="item.content.title"
          :real-name="item.content.creator.realName"
          :publish-time="item.collectTime"
          :regex-text="item.content.regexText"
          :images="item.content.images[0]"
          :qa-detail-url="item.content.qaDetailUrl"
        />
        <QAArticleItem
          v-else-if="item.contentType === 'communityQA' && item.content.images.length===0"
          :key="item.userCollectId"
          :q-a-id="item.content.id"
          :title="item.content.title"
          :real-name="item.content.creator.realName"
          :publish-time="item.collectTime"
          :regex-text="item.content.regexText"
          :qa-detail-url="item.content.qaDetailUrl"
        />
        <MeetingArticleScheduleItem
          v-else-if="item.contentType === 'agenda'"
          :id="item.content.meetingId"
          :key="item.userCollectId"
          :meeting-name="item.content.theme"
          :app-main-pic="item.content.headImage"
          :meeting-date-str="timeStamp.timestampFormat(item.collectTime / 1000)"
          :meeting-id="item.content.meetingId"
          :description="item.content.fieldsSubject"
          :fields-id="item.content.fieldsId"
          :agenda-id="item.content.id"
        />
        <ElabItem
          v-if='item.contentType === "elab"'
            :id='item.content.id'
            :key='item.userCollectId'
            :case-name='item.content.caseName'
            :elab-surgical-classification='item.content.elabSurgicalClassification'
            :hospital='item.content.hospital'
            :author-list='item.content.authorList'
            :classification-list='item.content.classificationList'
            :image='item.content.image'
            :share-desc="item.content.shareDesc"
          />
        <AIQA
          v-if="item.contentType === 'aiQaDialogue'"
          :ids="item.content.chatId"
          :publish-time="item.collectTime"
          :question="item.content.question"
          :regex-text="item.content.answer"
        />
          </template>

          <!-- 加载更多组件 -->
          <LoadingOpt v-if="contentLoading && contentList.length > 0" />
        </div>
      </div>
    </div>
            <!-- 编辑弹窗 -->
    <el-dialog
          title="编辑资料库"
          :visible.sync="dialogVisible"
          width="375px"
          custom-class="resource-dialog"
          :show-close="false"
        >
          <div class="dialog-content">
            <div class="form-item">
              <div class="label">名称<span class="required">*</span></div>
              <el-input
                v-model="form.name"
                placeholder="为你的资料库添加一个名称"
                maxlength="12"
              ></el-input>
            </div>
            <div class="form-item">
              <div class="label">简介</div>
              <el-input
                type="textarea"
                v-model="form.description"
                :rows="4"
                placeholder="请输入简介"
                maxlength="30"
              ></el-input>
            </div>
            <div class="form-item upload-item">
              <div class="label">封面</div>
              <div class="upload-cover" @click="handleUpload">
                <template v-if="!form.cover">
                  <div class="upload-btn">
                    <img src="@/assets/images/user-center/new_add.png" alt="" />
                    <span>添加封面</span>
                  </div>
                </template>
                <img v-else :src="form.cover" class="preview-img" />
              </div>
            </div>
            <div class="form-item switch-item">
              <div class="switch-header">
                <span class="label">设置为公开</span>
                <el-switch v-model="form.isPublic"></el-switch>
              </div>
              <div class="tip">
                公开后将展示在个人主页，有利会被推荐给其他人
              </div>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button class="cancel-btn" @click="dialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              class="confirm-btn"
              :class="{ 'disabled': !isFormValid }"
              :loading="loading"
              :disabled="!isFormValid"
              @click="handleSubmit"
            >确认</el-button>
          </div>
    </el-dialog>
        <!-- 删除确认弹窗 -->
    <el-dialog
      :visible.sync="deleteDialogVisible"
      width="375px"
      height="185px"
      custom-class="delete-dialog"
      :show-close="false"
    >
      <div class="delete-content">
        <p>是否确认删除该资料库？</p>
        <p class="tip">删除后，资料库下的内容仍将保留在 [我的收藏] 中</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="primary" class="confirm-btn" :loading="deleteLoading" @click="handleDelete">删除</el-button>
      </div>
    </el-dialog>
    <!-- 添加内容弹窗 -->
    <el-dialog
      :visible.sync="addContentVisible"
      width="1000px"
      custom-class="add-content-dialog"
      :show-close="false"
    >
      <div class="dialog-content">
        <div class="header">
          <div class="title">
            <span>我的收藏</span>
            <span class="tip">此处仅展示未添加到资料库的内容</span>
          </div>
          <div class="right">
            <el-button class="cancel-btn" @click="addContentVisible = false">取消</el-button>
            <el-button
              type="primary"
              :disabled="!selectedItems.length"
              :class="{'add_ok': true, 'disabled': !selectedItems.length }"
              @click="handleAddContent"
            >添加</el-button>
          </div>
        </div>

        <div class="content-list">
          <el-checkbox-group v-model="selectedItems">
            <div v-for="item in contentList" :key="item.id" class="content-item">
              <el-checkbox :label="item.id"></el-checkbox>
              <div class="item-cover">
                <img :src="item.cover" alt="">
              </div>
              <div class="item-info">
                <div class="item-title">{{item.title}}</div>
                <div class="item-meta">
                  <span>{{item.type}}</span>
                  <span>{{item.date}}</span>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </el-dialog>
    <!-- 批量删除弹窗 -->
    <el-dialog
      :visible.sync="batchDeleteVisible"
      width="1000px"
      custom-class="add-content-dialog"
      :show-close="false"
    >
      <div class="dialog-content">
        <div class="header">
          <div class="title">
            <span>批量管理</span>
          </div>
          <div class="right">
            <el-button class="cancel-btn" @click="batchDeleteVisible = false">取消</el-button>
            <el-button
              type="primary"
              :disabled="!selectedItems.length"
              :class="{'add_ok': true, 'disabled': !selectedItems.length }"
              @click="handleBatchDelete"
            >移除</el-button>
          </div>
        </div>

        <div class="content-list">
          <el-checkbox-group v-model="selectedItems">
            <div v-for="item in contentList" :key="item.id" class="content-item">
              <el-checkbox :label="item.id"></el-checkbox>
              <div class="item-cover">
                <img :src="item.cover" alt="">
              </div>
              <div class="item-info">
                <div class="item-title">{{item.title}}</div>
                <div class="item-meta">
                  <span>{{item.type}}</span>
                  <span>{{item.date}}</span>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </el-dialog>

    <!-- 测试 -->
    <!-- <favorite-dialog-vue :visible.sync="test"></favorite-dialog-vue> -->
  </div>
</template>

<script>
import { Switch, Checkbox, CheckboxGroup } from 'element-ui'
import FavoriteDialogVue from '../../../components/FavoriteDialog.vue'
import { getFavoriteDetail, getUserCollectContentByFavoriteId, updateFavorite, deleteFavorite, addFavoriteContent, deleteFavoriteContent } from '../../../api/favorite'
import InfoArticleItem from "../../../components/optimize-components/public/article-types-list/user-center-page/InfoArticleItem/index.vue";
import ClassroomArticleItem
  from "../../../components/optimize-components/public/article-types-list/user-center-page/ClassroomArticleItem/index.vue";
import MeetingArticleInstitutionItem
  from "../../../components/optimize-components/public/article-types-list/user-center-page/MeetingArticleInstitutionItem/index.vue";
import PgcArticleItem from "../../../components/optimize-components/public/article-types-list/user-center-page/PgcArticleItem/index.vue";
import QADefaultArticleItem
  from "../../../components/optimize-components/public/article-types-list/user-center-page/QADefaultArticleItem/index.vue";
import QAArticleItem from "../../../components/optimize-components/public/article-types-list/user-center-page/QAArticleItem/index.vue";
import VideoItemInformation
  from "../../../components/optimize-components/public/article-types-list/user-center-page/VideoItemInformation/index.vue";
import BmsVideoItemInformation
  from "../../../components/optimize-components/public/article-types-list/user-center-page/BmsVideoItemInformation/index.vue";
import MeetingArticleScheduleItem
  from "../../../components/optimize-components/public/article-types-list/user-center-page/MeetingArticleScheduleItem/index.vue";

  import ElabItem
  from "../../../components/optimize-components/public/article-types-list/bms/ElabItem/index.vue";
import AIQA from '../../../components/optimize-components/public/article-types-list/user-center-page/AIQA/index.vue'
import LoadingOpt from '../../../components/optimize-components/public/Loading/index.vue'
export default {
  name: 'FavoriteDetail',
  components: {
    'el-switch': Switch,
    'el-checkbox': Checkbox,
    'el-checkbox-group': CheckboxGroup,
    FavoriteDialogVue,
    AIQA,
    VideoItemInformation,
    InfoArticleItem,
    ClassroomArticleItem,
    MeetingArticleInstitutionItem,
    PgcArticleItem,
    QADefaultArticleItem,
    QAArticleItem,
    BmsVideoItemInformation,
    MeetingArticleScheduleItem,
    ElabItem,
    LoadingOpt
  },
  data() {
    return {
      showMenu: false,
      dialogVisible: false,
      loading: false,
      // 资料库详情信息
      favoriteDetail: {
        id: null,
        name: '',
        description: '',
        cover: '',
        isPublic: true,
        creatorName: '',
        creatorId: null,
        contentCount: 0,
        isOwner: false
      },
      form: {
        name: '',
        description: '',
        cover: '',
        isPublic: true,
      },
      deleteDialogVisible: false,
      deleteLoading: false,
      addContentVisible: false,
      selectAll: false,
      selectedItems: [],
      // 资料库内容列表
      contentList: [],
      // 分页信息
      pagination: {
        pageNo: 1,
        pageSize: 10,
        totalCount: 0,
        totalPage: 0
      },
      // 加载状态
      contentLoading: false,
      batchDeleteVisible: false,
      // 上拉加载相关
      scrollFlag: true, // 滚动加载开关
      // 资料库是否存在
      favoriteExists: true
    }
  },
  computed: {
    isFormValid() {
      const { name, description, cover } = this.form;
      return !!(name || description || cover);
    },
  },
  mounted() {
    // 检查用户是否已登录
    this.checkUserLogin()
  },
  beforeDestroy() {
    // 移除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    // 获取资料库详情
    async loadFavoriteDetail() {
      try {
        const response = await this.$axios.$request(getFavoriteDetail({
          favoriteId: this.$route.params.id,
          loginId: this.$store.state.auth.user.id
        }))

        if (response.code === 1 && response.result) {
          this.favoriteExists = true

          // 判断是否为资料库创建者
          const currentUserId = this.$store.state.auth.user.id
          const creatorUserId = response.result.creator?.id || response.result.creatorId
          const isOwner = currentUserId === creatorUserId

          this.favoriteDetail = {
            id: response.result.id,
            name: response.result.name || '',
            description: response.result.introduction || '',
            cover: response.result.cover || '',
            isPublic: response.result.permission === 'A' || false,
            creatorName: response.result.creator.realName || '',
            creatorId: response.result.creator?.id || response.result.creatorId,
            contentCount: response.result.favoriteContents || 0,
            isOwner: isOwner
          }

          // 初始化编辑表单
          this.form = {
            name: this.favoriteDetail.name,
            description: this.favoriteDetail.description,
            cover: this.favoriteDetail.cover,
            isPublic: this.favoriteDetail.isPublic
          }
        } else {
          // 资料库不存在或获取失败
          this.favoriteExists = false
        }
      } catch (error) {
        console.error('获取资料库详情失败:', error)
        // this.$message.error('获取资料库详情失败')
        // 请求失败时也认为资料库不存在
        this.favoriteExists = false
      } finally {
      }
    },

    // 获取资料库内容列表
    async loadContentList(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.contentLoading = true
          this.pagination.pageNo = 1
        } else {
          // 上拉加载时也要显示loading
          this.contentLoading = true
        }

        const response = await this.$axios.$request(getUserCollectContentByFavoriteId({
          favoriteId: this.$route.params.id,
          userId: this.$store.state.auth.user.id,
          pageNo: this.pagination.pageNo,
          pageSize: this.pagination.pageSize
        }))

        if (response.code === 1) {
          const newList = response.list || []

          if (isLoadMore) {
            // 上拉加载，追加数据
            this.contentList = [...this.contentList, ...newList]
          } else {
            // 初始加载或刷新，替换数据
            this.contentList = newList
          }

          if (response.page) {
            this.pagination = {
              ...this.pagination,
              totalCount: response.page.totalCount || 0,
              totalPage: response.page.totalPage || 0
            }
          }

          // 重新开启滚动加载
          this.scrollFlag = true
        }
      } catch (error) {
        console.error('获取内容列表失败:', error)
        this.$message.error('获取内容列表失败')
      } finally {
        this.contentLoading = false
      }
    },

    toggleMenu() {
      this.showMenu = !this.showMenu
    },
    showEditDialog() {
      // 重新初始化表单数据
      this.form = {
        name: this.favoriteDetail.name,
        description: this.favoriteDetail.description,
        cover: this.favoriteDetail.cover,
        isPublic: this.favoriteDetail.isPublic
      }
      this.dialogVisible = true
      this.showMenu = false
    },
    handleUpload() {
      // TODO: 实现上传封面功能
    },

    // 跳转到创建者个人主页
    goToCreatorProfile() {
      if (this.favoriteDetail.creatorId) {
        this.$router.push(`/user-center?profileUserId=${this.favoriteDetail.creatorId}`)
      }
    },

    // 上拉加载更多
    async loadMore() {
      if (!this.scrollFlag || this.contentLoading) return

      // 检查是否还有更多数据
      if (this.pagination.pageNo >= this.pagination.totalPage) {
        return
      }

      this.scrollFlag = false
      this.pagination.pageNo++
      await this.loadContentList(true)
    },

    // 滚动事件处理
    handleScroll() {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight || document.body.scrollHeight

      // 距离底部50px时触发加载
      if (scrollTop + windowHeight >= documentHeight - 50) {
        this.loadMore()
      }
    },

    async handleSubmit() {
      if (!this.form.name) {
        this.$message.warning('请输入资料库名称');
        return;
      }
      this.loading = true;
      try {
        const response = await this.$axios.$request(updateFavorite({
          id: this.favoriteDetail.id,
          name: this.form.name,
          description: this.form.description,
          cover: this.form.cover,
          isPublic: this.form.isPublic
        }))

        if (response.code === 1) {
          this.$message.success('更新成功')
          this.dialogVisible = false
          // 重新加载详情
          this.loadFavoriteDetail()
        } else {
          this.$message.error(response.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新资料库失败:', error)
        this.$message.error('更新失败')
      } finally {
        this.loading = false;
      }
    },
    showDeleteDialog() {
      this.deleteDialogVisible = true
      this.showMenu = false
    },
    async handleDelete() {
      this.deleteLoading = true
      try {
        const response = await this.$axios.$request(deleteFavorite({
          id: this.favoriteDetail.id
        }))

        if (response.code === 1) {
          this.$message.success('删除成功')
          this.deleteDialogVisible = false
          // 跳转回资料库列表页
          this.$router.push('/favorite')
        } else {
          this.$message.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除资料库失败:', error)
        this.$message.error('删除失败')
      } finally {
        this.deleteLoading = false
      }
    },
    handleSelectAll(val) {
      if (val) {
        this.selectedItems = this.contentList.map(item => item.id)
      } else {
        this.selectedItems = []
      }
    },
    async handleAddContent() {
      this.addContentVisible = true
      this.showMenu = false
    },
    showAddContentDialog() {
      this.addContentVisible = true
      this.showMenu = false
    },
    showBatchDeleteDialog() {
    this.batchDeleteVisible = true
    this.showMenu = false
  },
  async handleBatchDelete() {
    if (!this.selectedItems.length) return

    try {
      const response = await this.$axios.$request(deleteFavoriteContent({
        favoriteId: this.favoriteDetail.id,
        contentIds: this.selectedItems
      }))

      if (response.code === 1) {
        this.$message.success('移除成功')
        this.batchDeleteVisible = false
        this.selectedItems = []
        // 重新加载内容列表和详情
        this.loadContentList()
        this.loadFavoriteDetail()
      } else {
        this.$message.error(response.msg || '移除失败')
      }
    } catch (error) {
      console.error('批量移除失败:', error)
      this.$message.error('移除失败')
    }
  }
  }
}
</script>

<style scoped lang="less">
.favorite-detail {
  background: #F8F8F8;
  min-height: 100vh;
  padding: 20px 0;

  // 缺省状态样式
  .no_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;

    .no_contentimg {
      width: 200px;
      height: auto;
      margin-bottom: 20px;
    }

    p {
      color: #999999;
      font-size: 16px;
      margin: 0;
    }
  }

  .favorite-content {
    width: 1000px;
    margin: 0 auto;
    background: #FFFFFF;
    border-radius: 12px;
    min-height: calc(100vh - 20px);

    .library-info {
      display: flex;
      align-items: flex-start;
      padding: 33px 20px 23px;
      position: relative;

      .cover {
        margin-right: 16px;
        flex-shrink: 0;
        width: 220px;
        height: 124px;
        border-radius: 4px;
        position: relative;
        z-index: 3;

        &::before,
        &::after {
          content: '';
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background: #FFFFFF;
        }

        &::before {
          top: -5px;
          width: 204px;
          height: 5px;
          z-index: 1;
          border-radius: 6px 6px 0px 0px;
          background: rgba(164, 196, 216, 0.40);
        }

        &::after {
          top: -10px;
          width: 187px;
          height: 10px;
          z-index: 0;
          border-radius: 6px 6px 0px 0px;
          background: rgba(164, 196, 216, 0.20);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          position: relative;
          z-index: 2;
          border-radius: 4px;
        }
      }

      .info {
        margin-top: 1px;
        flex: 1;
        padding-top: 4px;
        display: flex;
        flex-direction: column;
        height: 124px;

        .title {
          font-size: 20px;
          color: #0581ce;
          margin-bottom: 13px;
          font-weight: 500;
          flex-shrink: 0;
        }

        .desc {
          flex: 1;
          font-size: 15px;
          color: #666666;
          line-height: normal;
          margin-bottom: 10px;
          max-width: 526px;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .meta {
          flex-shrink: 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #95999F;
          .creator{
            color: #708AA2;
            font-size: 15px;
          }
          .creator-name {
            color: #0581CE;
            cursor: pointer;
            

            &:hover {
              text-decoration: underline;
            }
          }

          .right {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .edit {
        position: absolute;
        top: 24px;
        right: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 96px;
        height: 31px;
        border-radius: 20px;
        border: 1px solid #EEE;
        background: #FFF;
        color: #666;
        font-family: "Source Han Sans CN";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%; /* 21px */
        cursor: pointer;

        &.active {
          color: #0581CE;
        }

        .menu {
          position: absolute;
          top: 100%;
          right: 0;
          margin-top: 8px;
          width: 120px;
          border-radius: 6px;
          border: 1px solid #F1F1F1;
          background: #FFF;
          box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);

          .menu-item {
            width: 101px;
            height: 40px;
            line-height: 40px;
            color: #333333;
            font-size: 15px;
            padding-left: 13px;

            &:hover {
              background: #F4F4F4;
            }
          }
        }
      }
    }

    .content-list {
      margin-top: 24px;
      border-top: 1px solid #EEEEEE;
      // padding: 20px;

      // // LoadingOpt 组件样式调整
      // /deep/ .content {
      //   margin: 30px auto;
      //   width: 150px;
      //   text-align: center;
      // }

      .no-more-tips {
        text-align: center;
        padding: 30px 0;
        color: #999999;
        font-size: 14px;

        span {
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60px;
            height: 1px;
            background-color: #E5E5E5;
          }

          &::before {
            right: 100%;
            margin-right: 16px;
          }

          &::after {
            left: 100%;
            margin-left: 16px;
          }
        }
      }
    }
  }
}
.content-list {
  /deep/ li {
    padding: 10px 16px;
  }
}
/deep/ .resource-dialog {
  border-radius: 16px;
  overflow: hidden;
  .el-dialog__header {
    padding: 17px 0 14px;
    border-bottom: 1px solid #eaeaea;
    margin-right: 0;
    display: flex;
    justify-content: center;

    .el-dialog__title {
      font-size: 16px;
      color: #666;
      font-weight: normal;
      width: 100%;
      text-align: center;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .dialog-content {
    .form-item {
      margin-bottom: 20px;
      .el-input__inner,
      .el-textarea__inner {
        background-color: #fafafa;
        border: none;
        resize: none;
        color: #333333;
        font-size: 15px;
        &::placeholder {
          color: #A0A0A0;
          font-size: 14px;
        }
      }

      .label {
        margin-bottom: 8px;
        color: #333;
        font-size: 14px;

        .required {
          color: #FF4D4F;
          margin-left: 4px;
        }
      }

      .upload-cover {
        width: 220px;
        height: 124px;
        background: #fafafa;
        border: none;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        overflow: hidden;

        .upload-btn span {
          color: #708aa2;
          font-size: 14px;
        }

        .preview-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .switch-item {
      .switch-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .tip {
        margin-top: 8px;
        color: #999;
        font-size: 12px;
        line-height: 1.5;
      }
    }
  }

  .el-dialog__footer {
    padding: 12px 0;
    text-align: center;
    border-top: 1px solid #eaeaea;

    .dialog-footer {
      .el-button {
        width: 88px;
        height: 32px;
        border-radius: 16px;
        font-size: 14px;
        padding: 0;
        font-weight: 400;

        & + .el-button {
          margin-left: 14px;
        }
      }

      .cancel-btn {
        background: #F4F4F4;
        border: 1px solid #CCC;
        color: #333333;
      }

      .confirm-btn {
        background: #0581CE;
        border: none;
        color: #FFFFFF;
        &.disabled {
          background: #F4F4F4;
          border: none;
          color: #999;
        }
      }
    }
  }
}
/deep/ .delete-dialog {
  border-radius: 16px;
  overflow: hidden;

  .el-dialog__header {
    display: none;  // 隐藏标题区域
  }

  .el-dialog__body {
    padding-top: 40px;  // 增加顶部内边距
    padding-bottom: 32px;
    .delete-content {
      text-align: center;

      p {
        font-size: 18px;
        color: #333;
        margin-bottom: 5px;
      }

      .tip {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .el-dialog__footer {
    padding: 12px 0;
    text-align: center;
    border-top: 1px solid #eee;

    .dialog-footer {
      .el-button {
        width: 88px;
        height: 32px;
        border-radius: 16px;
        font-size: 14px;
        padding: 0;
        font-weight: 400;

        & + .el-button {
          margin-left: 14px;
        }
      }

      .cancel-btn {
        background: #F4F4F4;
        border: 1px solid #CCC;
        color: #333333;
      }

      .confirm-btn {
        background: #0581ce;
        border: none;
        color: #FFFFFF;
      }
    }
  }
}

/deep/ .add-content-dialog {
  border-radius: 16px;
  overflow: hidden;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .dialog-content {
    .header {
      height: 55px;
      padding: 0px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #EEEEEE;

      .title {
        font-size: 16px;
        color: #666;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;

        span:first-child {
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }

        .tip {
          font-size: 10px;
          color: #999999;
        }
      }

      .right {
        display: flex;
        align-items: center;
        gap: 14px;

        .el-button {
          width: 88px;
          height: 32px;
          border-radius: 16px;
          padding: 0;
          font-weight: 400;
          &.add_ok {
            background: #0581CE;
            border: none;
            color: #FFFFFF;
            &.disabled {
            background: #F4F4F4;
            border: none;
            color: #999;
          }
          }


        }
      }
    }


  }

  .el-dialog__footer {
    display: none;
  }
}


</style>
