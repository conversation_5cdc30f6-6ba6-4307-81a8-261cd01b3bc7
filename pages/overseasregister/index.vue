<template>
  <div class='signin-bg-box'>
    <div class='container-box'>
      <div class='login-header-logo'>
        <nuxt-link to='/'>
          <img alt='' class='img_render' src='~assets/images/login-logo.png'>
        </nuxt-link>
      </div>
    </div>
    <!-- 登录输入框 -->
    <div class='signin-center-box'>
      <div class='china-user-box'>
        <!-- 登录切换 -->
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            Email Register
          </li>
        </ul>
        <!-- 登录表单 -->
        <el-form ref='loginForm' :model='loginForm' :rules='loginRules'>
          <!-- 账号密码登录 -->
          <el-form-item v-if='loginType==="account"' key='email' prop='email'>
            <el-input v-model='loginForm.email' autocomplete='on'>
              <p slot='prepend' class='input-p'>Your Email：</p>
            </el-input>
          </el-form-item>
          <el-form-item prop='captcha'>
            <el-input v-model='loginForm.captcha' autocomplete='off' type='number'>
              <p slot='prepend' class='input-p'>Verification Code:</p>
              <template slot='append'>
                <get-verification :account-number='loginForm.email' :name="'Send code'"
                                  @getValidationFun='getValidationFun'></get-verification>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType==="account"' prop='password'>
            <el-input v-model='loginForm.password' :type="passwordflag?'text':'password'" autocomplete='on'>
              <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                        class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
              <p slot='prepend' class='input-p'>Your Password：</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType !== "weixin"'>
            <el-button id='themeButton' class='button_item' type='primary' @click='registerFun("loginForm")'>Register
            </el-button>
          </el-form-item>
        </el-form>
        <!-- tips buttom -->
        <div class='tip-buttom'>
          <p class='tip-register'>
            <nuxt-link :to='{name:"overseaslogin"}'>
              <span class='register fontSize16 themeFontColor'>Have an account</span>
            </nuxt-link>
          </p>
        </div>
        <!-- 微信登录/邮箱登录 -->
        <!-- close -->
        <nuxt-link to='/'>
          <div class='close img_radius'>
            <i class='el-icon-close'></i>
          </div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
import { Checkbox, Radio } from 'element-ui'
import { getOverseasEmailCaptcha, overseasEmailRegister } from '@/api/login'
import GetVerification from '@/components/GetVerification/GetVerification'
import validate from '@/validate/form-validate'

export default {
  head() {
    return {
      title: 'register'
    }
  },
  components: {
    Checkbox,
    Radio,
    GetVerification
  },
  mounted() {
  },
  name: 'index',
  data() {
    return {
      loginType: 'account',
      passwordflag: false,
      loginRules: {
        email: [
          { validator: validate.overseasloginformValidate().Email, trigger: 'change' }
        ],
        password: [
          { validator: validate.overseasloginformValidate().RegisterPassword, trigger: 'change' }
        ],
        captcha: [
          { validator: validate.overseasloginformValidate().Captcha, trigger: 'change' }
        ]
      },// 表单验证
      loginForm: {},// 登录表单
      errorMessage: '' // 验证错误
    }
  },
  methods: {
    // 获取验证码
    getValidationFun(item, callback) {
      this.$analysys.btn_click('Send Code', document.title)
      this.$axios.$request(getOverseasEmailCaptcha({
        email: this.loginForm.email
      })).then(res => {
        if (res && res.code === 1) {
          return callback(true)
        }
      })
    },
    // 登录
    registerFun(formName) {
      this.$analysys.btn_click('register', document.title)
      // 为了按顺序验证
      let storage = []
      let flag = true
      this.$refs[formName].validateField(['email', 'password', 'captcha'], (valid) => {
        storage.push(valid)
      })
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false
          this.$toast(storage[i].tip)
          return
        }
      }
      if (flag) {
        // 注册
        this.$axios.$request(overseasEmailRegister({
          email: this.loginForm.email,
          captcha: this.loginForm.captcha,
          password: this.loginForm.password
        })).then(res => {
          if (res && res.code === 1) {
            this.$store.commit('auth/overseaslogin', {
              token: res.result.token,
              user: res.result.user,
              this: this
            })
          }
        })
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "~@/pages/overseasregister/index.less";
</style>
