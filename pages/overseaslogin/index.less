body, #__nuxt, #__layout, #__layout {
  height: 100%;
}

::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

::v-deep input[type='number'] {
  -moz-appearance: textfield !important;
}

/deep/ .el-form-item {
  margin-bottom: 15px;
}

/deep/ .el-input__inner {
  width: 100%;
  height: 48px;
  background: #F5FBFE;
  border: none;
}

/deep/ .el-input__suffix-inner {
  line-height: 54px;
}

/deep/ .el-form-item__content {
  line-height: 0;
}

/deep/ .el-checkbox__inner {
  border-radius: 4px;
}

/deep/ .el-radio__input.is-checked + .el-radio__label {
  color: #666666;
}

/deep/ .el-form-item__error {
  display: none;
}

.lable_tong {
  margin-bottom: 25px;
  text-align: center;
}

.button_item {
  width: 100%;
  border-radius: 12px !important;
  height: 47px;
  text-align: center;
  line-height: 47px;
  padding: 0;
}

.remember_box {
  margin-bottom: 25px;

  .closepassword {
    font-size: 14px;
    color: #FF922D;
  }
}

.passwordflag {
  width: 20px;
  height: 20px;
  margin-right: 19px;
}

/deep/ .el-input-group__prepend {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 0 0 24px;
  color: #759BB2;
  font-size: 16px;
  line-height: 48px;
}

/deep/ .el-input-group__append {
  border: none;
  background: none;
  background: #F5FBFE;
  padding: 0 24px;

  .verificationcode {
    font-size: 16px;
    color: #0581CE;
    line-height: 19px;

    span {
      color: #9FCFED;
      margin-right: 3px;
    }
  }
}

.signin-bg-box {
  height: 100vh;
  background-image: url("~assets/images/bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  overflow: hidden;
  //background-size: 100% 100%;
  background-position: center;

  .login-header-logo {
    margin-top: 10px;
    width: 302px;

    img {
      width: 100%;
    }
  }

  .signin-center-box {
    position: absolute;
    left: 50%;
    top: calc(50% - 34px);
    width: 488px;
    height: 501px;
    transform: translate(-50%, -50%);
    background: #FFFFFF;
    border-radius: 30px;
    padding: 36px 47px;
    box-sizing: border-box;
    box-shadow: 0px 10px 15px 1px rgba(0, 0, 0, 0.1);
    .close {
      width: 22px;
      height: 22px;
      text-align: center;
      line-height: 20px;
      background: #EBEBEB;
      position: absolute;
      top: 30px;
      right: 30px;

      &:hover i {
        color: #B1B1B1;
      }

      i {
        font-size: 13px;
        color: #949494;
      }
    }

    .weixin-login-box {
      margin-bottom: 37px;
      text-align: center;

      .weixin-box {
        width: 208px;
        height: 208px;
        border: 1px solid #F2F2F2;
        box-sizing: border-box;
        margin: 0 auto 29px;

        .weixin-tip {
          color: #666666;
        }

        #weixinbox {
          height: 100%;

          .title {
            display: none;
          }
        }
      }
    }

    .login-switching-list {
      margin-bottom: 40px;
      align-items: end;
      position: relative;

      .verificationcode-tip {
        position: absolute;
        left: 0;
        top: calc(100% + 6px);
        color: #666666;
      }

      .login-switching-item {
        margin-right: 15px;
        font-size: 20px;
        font-weight: 550;
        color: #888888;

        &:last-child {
          margin-right: 0;
        }
      }

      .is_active {
        font-size: 24px;
        color: #000000;
      }
    }

    .login-button_list {
      margin-bottom: 30px;

      & .button_item:nth-child(1) {
        background: #EEF8FF;
        color: #0581CE;
        margin-bottom: 15px;

        &:hover {
          background: #0581CE;
          color: #EEF8FF;
        }
      }

      & .button_item:nth-child(2) {
        background: #EDFFEF;
        color: #00B432;
        margin-bottom: 15px;

        &:hover {
          background: #00B432;
          color: #EDFFEF;
        }
      }

      & .button_item:nth-child(3) {
        background: #FFF9ED;
        color: #F86E0A;

        &:hover {
          background: #F86E0A;
          color: #FFF9ED;
        }
      }
    }

    .tip-buttom {
      width: 100%;
      box-sizing: border-box;
      padding: 0 47px;
      line-height: 16px;
      text-align: center;
      position: absolute;
      bottom: 36px;
      left: 50%;
      transform: translate(-50%);

      .tip-register {
        font-size: 14px;
        color: #92A3AE;

        .register {

        }
      }

      .tip-Overseas {

      }
    }
  }
}
