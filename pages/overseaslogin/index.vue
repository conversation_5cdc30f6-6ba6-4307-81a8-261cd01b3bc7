<template>
  <div class='signin-bg-box'>
    <div class='container-box'>
      <div class='login-header-logo'>
        <nuxt-link to='/'>
          <img alt='' class='img_render' src='~assets/images/login-logo.png'>
        </nuxt-link>
      </div>
    </div>
    <!-- 登录输入框 -->
    <div class='signin-center-box'>
      <div class='china-user-box'>
        <!-- 登录切换 -->
        <ul class='login-switching-list flex_start'>
          <li class='login-switching-item cursor is_active'>
            Login
          </li>
        </ul>
        <!-- 登录表单 -->
        <el-form ref='loginForm' :model='loginForm' :rules='loginRules'>
          <!-- 账号密码登录 -->
          <el-form-item v-if='loginType==="account"' prop='username'>
            <el-input v-model='loginForm.username' autocomplete='on'>
              <p slot='prepend' class='input-p'>Your Email：</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType==="account"' prop='password'>
            <el-input v-model='loginForm.password' :type="passwordflag?'text':'password'" autocomplete='on'>
              <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                        class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
              <p slot='prepend' class='input-p'>Your Password：</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType !== "forgetpassword"' prop='remember'>
            <div class='remember_box flex_between flex_align_center'>
              <checkbox v-model='loginForm.remember' class='themeFontColor'>Remember my account</checkbox>
              <span v-if='loginType==="account"' class='closepassword cursor'
                    @click='loginType = "forgetpassword",analysysFun("Forget Password")'>Forget Password?</span>
            </div>
          </el-form-item>
          <el-form-item v-if='loginType !== "weixin" && loginType !== "forgetpassword"'>
            <el-button id='themeButton' class='button_item' type='primary' @click='signinFun("loginForm")'>Login
            </el-button>
          </el-form-item>
          <!-- 忘记密码登录Start -->
          <el-form-item v-if='loginType==="forgetpassword"' :key='"forgetUsername"' prop='username'>
            <el-input v-model='loginForm.username' autocomplete='on'>
              <p slot='prepend' class='input-p'>Your Email：</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType==="forgetpassword"' :key='"abc"' prop='captcha'>
            <el-input v-model='loginForm.captcha' autocomplete='on'>
              <p slot='prepend' class='input-p'>Verification Code:</p>
              <template slot='append'>
                <get-verification :account-number='loginForm.username' :name="'Send code'"
                                  @getValidationFun='getValidationFun'></get-verification>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType==="forgetpassword"' :key='"forgetpassword"' prop='forgetpassword'>
            <el-input v-model='loginForm.forgetpassword' :type="passwordflag?'text':'password'" autocomplete='on'>
              <svg-icon slot='suffix' :icon-class="!passwordflag?'hidepassword':'showpassword'"
                        class-name='passwordflag cursor' @click='passwordflag=!passwordflag'></svg-icon>
              <p slot='prepend' class='input-p'>Your Password：</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType==="forgetpassword"' :key='"confirmpassword"' prop='confirmpassword'>
            <el-input v-model='loginForm.confirmpassword' :type="newpasswordflag?'text':'password'" autocomplete='on'>
              <svg-icon slot='suffix' :icon-class="!newpasswordflag?'hidepassword':'showpassword'"
                        class-name='passwordflag cursor' @click='newpasswordflag=!newpasswordflag'></svg-icon>
              <p slot='prepend' class='input-p'>Again Password:</p>
            </el-input>
          </el-form-item>
          <el-form-item v-if='loginType === "forgetpassword"'>
            <el-button id='themeButton' class='button_item' type='primary' @click='signinFun("loginForm")'>Change
              Password
            </el-button>
          </el-form-item>
          <!-- 忘记密码登录End -->
        </el-form>
        <!-- tips buttom -->
        <div v-if='loginType !== "forgetpassword"' class='tip-buttom'>
          <p class='tip-register flex_between'>
            <nuxt-link :to='{name:"overseasregister"}'>
              <span class='register fontSize16 themeFontColor'
                    @click="analysysFun('Email Register')">Email Register</span>
            </nuxt-link>
            <nuxt-link :to='{name:"signin"}'>
              <span class='register fontSize14'>Domestic user login></span>
            </nuxt-link>
          </p>
        </div>
        <div v-if='loginType === "forgetpassword"' class='tip-buttom'>
          <p class='tip-register flex_center cursor'>
            <span class='register fontSize14 fontWeight'
                  @click='loginType = "account",analysysFun("Gologin")'>Go login~</span>
          </p>
        </div>
        <!-- 微信登录/邮箱登录 -->
        <!-- close -->
        <nuxt-link to='/'>
          <div class='close img_radius'>
            <i class='el-icon-close'></i>
          </div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
import { Checkbox, Radio } from 'element-ui'
import { getOverseasForgetPasswordCaptcha, login, overseasForgetEmailPassword } from '@/api/login'
import validate from '@/validate/form-validate'
import GetVerification from '@/components/GetVerification/GetVerification'

export default {
  head() {
    return {
      title: 'login'
    }
  },
  components: {
    Checkbox,
    Radio,
    GetVerification
  },
  mounted() {
  },
  name: 'index',
  data() {
    var confirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback({ tip: 'Please enter the password again', isEnable: false })
      } else if (value !== this.loginForm.forgetpassword) {
        callback({ tip: 'Inconsistent passwords', isEnable: false })
      } else {
        callback({ isEnable: true })
      }
    }
    return {
      loginType: 'account',// 账户类型
      passwordflag: false,// 密码显隐
      newpasswordflag: false,// 密码显隐
      loginRules: {
        username: [
          { validator: validate.overseasloginformValidate().Email, trigger: 'change' }
        ],
        password: [
          { validator: validate.overseasloginformValidate().Password, trigger: 'change' }
        ],
        captcha: [
          { validator: validate.overseasloginformValidate().Captcha, trigger: 'change' }
        ],
        forgetpassword: [
          { validator: validate.overseasloginformValidate().RegisterPassword, trigger: 'change' }
        ],
        confirmpassword: [
          { validator: confirmPassword, trigger: 'change' }
        ]
      },// 表单验证
      loginForm: {
        captchaa: ''
      },// 登录表单
      errorMessage: '' // 验证错误
    }
  },
  methods: {
    // 埋点
    analysysFun(name) {
      this.$analysys.btn_click(name, document.title)
    },
    // 获取验证码
    getValidationFun(item, callback) {
      this.$analysys.btn_click('Send Code', document.title)
      this.$axios.$request(getOverseasForgetPasswordCaptcha({
        email: this.loginForm.username
      })).then(res => {
        if (res && res.code === 1) {
          return callback(true)
        }
      })
    },
    // 登录
    signinFun(formName) {
      this.$analysys.btn_click('login', document.title)
      // 为了按顺序验证
      let storage = []
      let flag = true
      this.$refs[formName].validateField(['username', 'password', 'captcha', 'forgetpassword', 'confirmpassword'], (valid) => {
        storage.push(valid)
      })
      // 按顺序验证表单
      for (let i = 0; i < storage.length; i++) {
        if (!storage[i].isEnable) {
          flag = false
          this.$toast(storage[i].tip)
          return
        }
      }
      if (flag) {
        switch (this.loginType) {
          case 'account': {
            // 账号密码登录
            this.$axios.$request(login({
              username: this.loginForm.username,
              password: this.loginForm.password,
              loginType: 'F'
            })).then(res => {
              if (res && res.code === 1) {
                this.$store.commit('auth/overseaslogin', {
                  token: res.result.token,
                  user: res.result.user,
                  remember: this.loginForm.remember,
                  this: this
                })
              }
            })
            break
          }
          case 'forgetpassword': {
            this.$analysys.btn_click('ChangePassword', document.title)
            // 忘记密码
            this.$axios.$request(overseasForgetEmailPassword({
              email: this.loginForm.username,
              captcha: this.loginForm.captcha,
              newPassword: this.loginForm.forgetpassword
            })).then(res => {
              if (res && res.code === 1) {
                this.$toast({
                  message: '密码修改成功',
                  type: 'success',
                  duration: 1000,
                  onClose: () => {
                    this.loginType = 'account'
                  }
                })
              }
            })
            break
          }
        }
      }
    }
  }
}
</script>

<style lang='less' scoped>
@import "./index";
</style>
